package com.facishare.crm.loyalty.controller;

import com.facishare.crm.loyalty.utils.LoyaltyUtils;
import com.facishare.crm.sfa.predefine.controller.SFAWebDetailController;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;

import java.util.List;
import java.util.Optional;

public class LoyaltyMemberChangeRecordsWebDetailController extends SFAWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (LoyaltyUtils.removeFallbackButton(result.getData().toObjectData())) {
            LayoutDocument layoutDocument = result.getLayout();
            Optional<IComponent> optional = LayoutExt.of(layoutDocument).getHeadInfoComponent();
            if (optional.isPresent()) {
                IComponent headInfoComponent = optional.get();
                List<IButton> buttons = headInfoComponent.getButtons();
                buttons.removeIf(button -> ObjectAction.FALLBACK.getButtonApiName().equals(button.getName()));
                headInfoComponent.setButtons(buttons);
            }
        }
        return result;
    }
}
