package com.facishare.crm.loyalty.controller;

import com.facishare.crm.loyalty.utils.ControllerUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Sets;

import java.util.Set;

public class LoyaltyMemberWebDetailController extends StandardWebDetailController {

    Set<String> removeButtonSets = Sets.newHashSet(ObjectAction.MEMBER_CHANGE_POINTS.getButtonApiName()
            , ObjectAction.MEMBER_SET_LEVEL.getButtonApiName()
            , ObjectAction.MEMBER_SET_LEVEL.getButtonApiName()
            , ObjectAction.MEMBER_TRANSFER_POINTS.getButtonApiName()
    );

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (RequestUtil.isMobileRequest() || RequestUtil.isH5MobileRequest()) {
            ControllerUtils.removeButtonByWebDetailController(layout, removeButtonSets);
        }
        return layout;
    }
}