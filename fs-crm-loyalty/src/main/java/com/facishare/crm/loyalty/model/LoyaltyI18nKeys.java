package com.facishare.crm.loyalty.model;

public interface LoyaltyI18nKeys {

    /**
     * 会员等级图标转换失败
     */
    String SFA_LOYALTY_TIER_ICON_ERR = "sfa.loyalty.tier.icon.error";

    /**
     * 登陆已过期.请重新登陆
     */
    String SFA_LOYALTY_MEMBER_LOGIN = "sfa.loyalty.member.login";

    /**
     * 结果值长度不能超过10位
     */
    String SFA_LOYALTY_POINT_FIXED_VALUE_LENGTH_ERROR = "sfa.loyalty.point.fixed.value.length.error";

    /**
     * 当前使用积分{0}超出订单金额{1}
     */
    String SFA_LOYALTY_PLUGIN_AMOUNT_EXCESS = "sfa.loyalty.plugin.amount.excess";

    /**
     * 积分金额与当前配置不一致
     */
    String SFA_LOYALTY_PLUGIN_AMOUNT_CONTRADICTION = "sfa.loyalty.plugin.amount.contradiction";

    /**
     * 积分兑换比例异常
     */
    String SFA_LOYALTY_PLUGIN_POINTS_EXCHANGE_RATE_EXCEPTION = "sfa.loyalty.plugin.points.exchange_rate.exception";

    /**
     * 暂不支持修改积分
     */
    String SFA_LOYALTY_PLUGIN_PROHIBIT_EDITING_POINTS = "sfa.loyalty.plugin.prohibit.editing.points";

    /**
     * 会员开启插件失败
     */
    String SFA_LOYALTY_PLUGIN_OPENING_FAILED = "sfa.loyalty.plugin.opening.failed";

    /**
     * 忠诚度开关未开启
     */
    String SFA_LOYALTY_NOT_ENABLED_LOYALTY_SWITCH = "sfa.loyalty.not_enabled.loyalty_switch";

    /**
     * {0}未开启,请先开启{1}
     */
    String SFA_LOYALTY_SWITCH_NOT_ENABLED = "sfa.loyalty.switch.not_enabled";

    String SFA_LOYALTY_PLUGIN_MEMBER_MISMATCH = "sfa.loyalty.plugin.member.mismatch";

    /**
     * 会员合并时间不能超过一年
     */
    String SFA_MEMBER_MERGE_TIME_TOO_LONG = "sfa.loyalty.member.merge.time_too_long";

    /**
     * 积分必须大于0
     */
    String SFA_MEMBER_OPERATE_VALUE_POSITIVE_INTEGER = "sfa.loyalty.member.operate.value_positive_integer";
}
