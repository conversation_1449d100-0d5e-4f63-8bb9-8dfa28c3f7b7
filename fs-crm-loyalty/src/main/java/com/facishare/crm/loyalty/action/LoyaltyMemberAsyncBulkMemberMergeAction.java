package com.facishare.crm.loyalty.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.loyalty.model.LoyaltyI18nKeys;
import com.facishare.crm.loyalty.service.LoyaltyMqService;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.i18n.LoyaltyI18nException;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberService;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.predefine.action.AbstractSimpleStandardAction;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.redisson.api.RLock;

import java.util.*;

public class LoyaltyMemberAsyncBulkMemberMergeAction extends AbstractSimpleStandardAction {

    private final LoyaltyMemberService loyaltyMemberService = SpringUtil.getContext().getBean(LoyaltyMemberService.class);
    private final LoyaltyMqService loyaltyMqService = SpringUtil.getContext().getBean(LoyaltyMqService.class);

    List<IObjectData> sourceObjectDataList = new ArrayList<>();

    @Override
    protected ObjectAction getButton() {
        return ObjectAction.MEMBER_MERGE;
    }

    @Override
    protected void init() {
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        JSONObject param = arg.getArgs();
        Long startTime = param.getLong("startTime");
        Long endTime = param.getLong("endTime");
        if (startTime == null) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.MONTH, Calendar.JANUARY); // 设置月份为1月
            calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置日期为1号
            calendar.set(Calendar.HOUR_OF_DAY, 0); // 时设为0
            calendar.set(Calendar.MINUTE, 0); // 分设为0
            calendar.set(Calendar.SECOND, 0); // 秒设为0
            calendar.set(Calendar.MILLISECOND, 0); // 毫秒设为0
            startTime = calendar.getTimeInMillis();
        }
        if (endTime == null) {
            endTime = System.currentTimeMillis();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startTime);
        int startYear = calendar.get(Calendar.YEAR);
        calendar.setTimeInMillis(endTime);
        int endYear = calendar.get(Calendar.YEAR);
        if (startYear != endYear) {
            throw new ValidateException(I18N.text(LoyaltyI18nKeys.SFA_MEMBER_MERGE_TIME_TOO_LONG));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        String tenantId = actionContext.getTenantId();
        JSONObject param = arg.getArgs();
        Long startTime = param.getLong("startTime");
        Long endTime = param.getLong("endTime");
        List<String> sourceDataIdList = param.getJSONArray("sourceDataIds").toJavaList(String.class);
        boolean needMergeRelationObjects = param.getBooleanValue("needMergeRelationObjects");
        List<String> dataIdList = new ArrayList<>(sourceDataIdList);
        dataIdList.add(arg.getObjectDataId());
        Set<String> lockedMemberIds = new HashSet<>(dataIdList);
        RLock rLock = loyaltyMemberService.tryLockMultipleMember(tenantId, lockedMemberIds);
        try {
            initData(dataIdList);
            IObjectData targetData = removeNoSupportField(arg.getObjectData());
            Map<String, Object> updateFields = new HashMap<>();
            if (needMergeRelationObjects) {
                updateFields.put(LoyaltyConstants.LoyaltyMember.MEMBER_STATUS, "merging");
            } else {
                updateFields.put(LoyaltyConstants.LoyaltyMember.MEMBER_STATUS, "merged");
                targetData.set(LoyaltyConstants.LoyaltyMember.MEMBER_STATUS, "normal");
            }
            serviceFacade.updateObjectData(actionContext.getUser(), targetData);
            serviceFacade.batchUpdateWithMap(actionContext.getUser(), sourceObjectDataList, updateFields);
            if (needMergeRelationObjects) {
                Loyalty.MemberMerge memberMerge = new Loyalty.MemberMerge();
                memberMerge.setTenantId(tenantId);
                memberMerge.setOperator(actionContext.getUser().getUserId());
                memberMerge.setSouceIdList(sourceDataIdList);
                memberMerge.setTargetId(arg.getObjectDataId());
                memberMerge.setStartTime(startTime);
                memberMerge.setEndTime(endTime);
                memberMerge.setTraceId(TraceContext.get().getTraceId());
                loyaltyMqService.sendMqForMergeMember(memberMerge);
            }
        } finally {
            loyaltyMemberService.unLockMultipleMember(rLock);
        }
        return new Result();
    }

    public void initData(List<String> dataIdList) {
        String tenantId = actionContext.getTenantId();
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, dataIdList, LoyaltyConstants.LoyaltyMember.API_NAME);
        checkMemberStatus(dataList);
        for (IObjectData data : dataList) {
            if (arg.getObjectDataId().equals(data.getId())) {
                objectData = data;
            } else {
                sourceObjectDataList.add(ObjectDataExt.of(data).copy());
            }
        }

    }

    public IObjectData removeNoSupportField(ObjectDataDocument objectDataDocument) {
        objectDataDocument.remove(LoyaltyConstants.LoyaltyMember.GRADING_POINTS);
        objectDataDocument.remove(LoyaltyConstants.LoyaltyMember.CONSUMER_POINTS);
        objectDataDocument.remove(LoyaltyConstants.LoyaltyMember.FROZEN_POINTS);
        objectDataDocument.remove(LoyaltyConstants.LoyaltyMember.FROZEN_GRADING_POINTS);
        objectDataDocument.remove(LoyaltyConstants.LoyaltyMember.MEMBER_STATUS);

        objectDataDocument.put(LoyaltyConstants.LoyaltyMember.MEMBER_STATUS, "merging");
        objectDataDocument.put(IObjectData.DESCRIBE_API_NAME, LoyaltyConstants.LoyaltyMember.API_NAME);
        objectDataDocument.put(IObjectData.TENANT_ID, actionContext.getTenantId());
        objectDataDocument.put(IObjectData.RECORD_TYPE, IObjectData.RECORD_TYPE_DEFAULT);
        return objectDataDocument.toObjectData();
    }

    public void checkMemberStatus(List<IObjectData> dataList) {
        for (IObjectData member : dataList) {
            String memberStatus = member.get(LoyaltyConstants.LoyaltyMember.MEMBER_STATUS, String.class);
            if ("merging".equals(memberStatus)) {
                String i18nKey = GetI18nKeyUtil.getOptionNameKey(LoyaltyConstants.LoyaltyMember.API_NAME, LoyaltyConstants.LoyaltyMember.MEMBER_STATUS, memberStatus);
                LoyaltyI18nException.I18nParam param = new LoyaltyI18nException.I18nParam(i18nKey, LoyaltyI18nException.ParamType.variable);
                throw LoyaltyI18nException.build(LoyaltyI18nKey.PROHIBIT_OPERATION_FOR_MEMBER_STATUS, Lists.newArrayList(param));
            }
        }
    }
}
