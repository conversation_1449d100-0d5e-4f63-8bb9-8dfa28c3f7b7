package com.facishare.crm.loyalty.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface Loyalty {

    @Data
    class PointsOperationParam {
        private String tenantId;
        /**
         * 唯一id，同一个id的操作不会重复执行，用于幂等
         */
        private String uniqueId;
        /**
         * 会员id
         */
        private String memberId;
        /**
         * 用户行为，由用户自己填写的描述
         */
        private String desc;
        /**
         * 操作类型
         * CONSUMER_POINTS_TO_MEMBER 为给员工加积分
         * CONSUMER_POINTS_TO_POOL 为给员工扣积分
         */
        private Type type;
        /**
         * 积分变动值。消费积分增减都必须填
         */
        private Long value;
        /**
         * 积分池id。消费积分增减都必须填
         */
        private String pointPoolId;
        /**
         * 积分分类，定级积分变动时，需要指定积分分类
         */
        private String pointTypeId;
        /**
         * 直接修改等级时必填
         */
        private String tierId;

        public enum Type {
            CONSUMER_POINTS_TO_MEMBER, CONSUMER_POINTS_TO_POOL, LEVEL_POINTS, SET_LEVEL
        }
    }

    @Data
    @NoArgsConstructor
    class Result {
        private boolean success;
        private String message;
        private Object data;

        public Result(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public Result(Object data) {
            this.success = true;
            this.data = data;
        }
    }

    @Data
    class ParaCount {
        private Integer maxToB;
        private Integer usingToB;
        private Integer maxToC;
        private Integer usingToC;
    }


    @Data
    class Statistics {
        private Long startTime;
        private Long endTime;

        private List<ParaInfo> paraInfoList;
    }

    @Data
    @NoArgsConstructor
    class ParaInfo {
        private String key;
        private Integer maxCount;
        private Integer usingCount;

        public ParaInfo(String key, Integer maxCount, Integer usingCount) {
            this.key = key;
            this.maxCount = maxCount;
            this.usingCount = usingCount;
        }
    }

}