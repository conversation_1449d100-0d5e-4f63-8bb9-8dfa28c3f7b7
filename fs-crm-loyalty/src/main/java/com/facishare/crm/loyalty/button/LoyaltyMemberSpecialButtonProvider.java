package com.facishare.crm.loyalty.button;

import com.facishare.crm.loyalty.LoyaltyPredefineObject;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class LoyaltyMemberSpecialButtonProvider implements SpecialButtonProvider {
    @Override
    public String getApiName() {
        return LoyaltyPredefineObject.LoyaltyMember.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = Lists.newArrayList();

        buttons.add(ButtonUtils.buildButton(ObjectAction.MEMBER_CHANGE_POINTS));
        buttons.add(ButtonUtils.buildButton(ObjectAction.MEMBER_SET_LEVEL));
        buttons.add(ButtonUtils.buildButton(ObjectAction.MEMBER_TRANSFER_POINTS));
        return buttons;
    }
}
