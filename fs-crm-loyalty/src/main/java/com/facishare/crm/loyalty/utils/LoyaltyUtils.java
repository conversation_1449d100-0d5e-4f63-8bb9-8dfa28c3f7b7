package com.facishare.crm.loyalty.utils;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Sets;

import java.util.Set;

public class LoyaltyUtils {

    static Set<String> recordFallbackTypes = Sets.newHashSet(
            Loyalty.PointsOperationParam.Type.CONSUMER_POINTS_TO_MEMBER.toString()
            , Loyalty.PointsOperationParam.Type.CONSUMER_POINTS_TO_POOL.toString()
            , Loyalty.PointsOperationParam.Type.LEVEL_POINTS.toString()
            , Loyalty.PointsOperationParam.Type.TRANSFER_CONSUMER_POINTS.toString()
            , Loyalty.PointsOperationParam.Type.TRANSFER_LEVEL_POINTS.toString()
            , Loyalty.PointsOperationParam.Type.TRANSFER_FREEZE_CONSUMER_POINTS.toString()
            , Loyalty.PointsOperationParam.Type.TRANSFER_FREEZE_LEVEL_POINTS.toString()
    );

    public static boolean removeFallbackButton(IObjectData data) {
        String changeType = data.get(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, String.class);
        if (!recordFallbackTypes.contains(changeType)) {
            return true;
        }
        String fallback = data.get(LoyaltyConstants.LoyaltyMemberChangeRecords.IS_FALLBACK, String.class);
        return "true".equals(fallback);
    }

    public static Set<String> memberImportTemplateRemoveFields() {
        return Sets.newHashSet("tree_path");
    }
}
