package com.facishare.crm.loyalty.bizvalidator.event;

import com.facishare.crm.constants.TransactionEventConstants;
import com.facishare.crm.loyalty.LoyaltyPredefineObject;
import com.facishare.crm.loyalty.utils.IncentivePolicyRuleUtils;
import com.facishare.crm.sfa.predefine.bizvalidator.Validator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.BigDecimalUtils;
import com.facishare.crm.sfa.utilities.util.i18n.LoyaltyKeyUtil;
import com.facishare.crm.util.DescribeI18NUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
public class TransactionEventCommonValidator implements Validator {
    private final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);

    @Override
    public void validate(ValidatorContext context) {
        StopWatch stopWatch = StopWatch.create("TransactionEventCommonValidator");
        memberCheck(context);
        stopWatch.lap("memberCheck");
        storeCheck(context);
        stopWatch.lap("storeCheck");
        exclusiveCheck(context);
        stopWatch.lap("exclusiveCheck");
        customerCheck(context);
        stopWatch.lap("customerCheck");
        gradingCheck(context);
        stopWatch.lap("gradingCheck");
        stopWatch.logSlow(100);
    }

    private void storeCheck(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        String storeId = objectData.get(TransactionEventConstants.TRANSACTION_STORE_ID, String.class);
        List<IObjectData> objectDataList = IncentivePolicyRuleUtils.validateReferenceIds(context.getUser().getTenantId(), "PartnerObj", Sets.newHashSet(storeId));
        IObjectData parentData = objectDataList.get(0);
        doStoreCheck(objectData, parentData);

    }

    public void doStoreCheck(IObjectData eventData, IObjectData parentData) {
        String recordType = parentData.getRecordType();
        if (!"record_store__c".equals(recordType)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_LOYALTY_TRANSACTION_EVENT_STORE_INVALID));
        }
        //比较忠诚度计划是否一致
        String loyaltyPlanId = eventData.get(TransactionEventConstants.PROGRAM_ID, String.class);
        String partnerProgram = parentData.get(TransactionEventConstants.PROGRAM_ID, String.class);
        if (!StringUtils.equals(loyaltyPlanId, partnerProgram)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_PROGRAM_NOT_EQUAL));
        }
    }

    private void gradingCheck(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        doGradingCheck(objectData);
    }

    public void doGradingCheck(IObjectData objectData) {
        String gradingPointsTypeId = objectData.get(TransactionEventConstants.GRADING_POINTS_TYPE_ID, String.class);
        String gradingPointsQuantity = objectData.get(TransactionEventConstants.GRADING_POINTS_QUANTITY, String.class);
        String gradingPointsType = objectData.get(TransactionEventConstants.GRADING_POINTS_TYPE, String.class);
        if (StringUtils.isAllBlank(gradingPointsTypeId, gradingPointsQuantity, gradingPointsType)) {
            return;
        }
        if (StringUtils.isAnyBlank(gradingPointsTypeId, gradingPointsQuantity, gradingPointsType)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_LOYALTY_TRANSACTION_EVENT_GRADING_POINTS_EMPTY));
        }

        BigDecimal quantityBig = objectData.get(TransactionEventConstants.GRADING_POINTS_QUANTITY, BigDecimal.class);

        if (BigDecimalUtils.compare(quantityBig, Operator.LT, BigDecimal.ONE)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_POLICY_NUMBER_ERROR, I18N.text(DescribeI18NUtils.getFieldNameKey(LoyaltyPredefineObject.TransactionEvent.getApiName(), TransactionEventConstants.GRADING_POINTS_QUANTITY))));
        }
    }

    /**
     * 消费积分校验
     *
     * @param context 上下文
     */
    private void customerCheck(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        doCustomerCheck(objectData);
    }

    public void doCustomerCheck(IObjectData objectData) {
        String customerOrgId = objectData.get(TransactionEventConstants.CONSUMER_ORG_ID, String.class);
        String consumerPointsTypeId = objectData.get(TransactionEventConstants.CONSUMER_POINTS_TYPE_ID, String.class);
        String quantity = objectData.get(TransactionEventConstants.CONSUMER_POINTS_QUANTITY, String.class);
        String consumerPointsType = objectData.get(TransactionEventConstants.CONSUMER_POINTS_TYPE, String.class);
        if (StringUtils.isAllBlank(customerOrgId, consumerPointsTypeId, quantity, consumerPointsType)) {
            return;
        }
        if (StringUtils.isAnyBlank(customerOrgId, consumerPointsTypeId, quantity, consumerPointsType)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_LOYALTY_TRANSACTION_EVENT_CUSTOMER_POINTS_EMPTY));
        }
        BigDecimal quantityBig = objectData.get(TransactionEventConstants.CONSUMER_POINTS_QUANTITY, BigDecimal.class);

        if (BigDecimalUtils.compare(quantityBig, Operator.LT, BigDecimal.ONE)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_POLICY_NUMBER_ERROR, I18N.text(DescribeI18NUtils.getFieldNameKey(LoyaltyPredefineObject.TransactionEvent.getApiName(), TransactionEventConstants.CONSUMER_POINTS_QUANTITY))));
        }
    }

    private void exclusiveCheck(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        doExclusiveCheck(objectData);
    }

    public void doExclusiveCheck(IObjectData objectData) {
        String memberLevelId = objectData.get(TransactionEventConstants.CHANGE_MEMBER_LEVEL_ID, String.class);
        String levelType = objectData.get(TransactionEventConstants.CHANGE_MEMBER_LEVEL_TYPE, String.class);
        String quantity = objectData.get(TransactionEventConstants.CHANGE_MEMBER_LEVEL_QUANTITY, String.class);
        log.info("log doExclusiveCheck memberLevelId:{},levelType:{},quantity:{}", memberLevelId, levelType, quantity);
        log.info("log doExclusiveCheck empty memberLevelId:{},levelType:{},quantity:{}", StringUtils.isBlank(memberLevelId), StringUtils.isBlank(levelType), StringUtils.isBlank(quantity));
        if (StringUtils.isAllBlank(memberLevelId, levelType, quantity)) {
            log.info("log doExclusiveCheck empty memberLevelId:{},levelType:{},quantity:{}", memberLevelId, levelType, quantity);
            return;
        }
        if (StringUtils.isNotBlank(memberLevelId) && (StringUtils.isNotBlank(levelType) || StringUtils.isNotBlank(quantity))) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_LOYALTY_TRANSACTION_EVENT_CHANGE_MEMBER_LEVEL_EXCLUSIVE));
        }
        if (StringUtils.isNotBlank(memberLevelId)) {
            return;
        }
        if (StringUtils.isAnyBlank(levelType, quantity)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_TRANSACTION_EVENT_CHANGE_MEMBER_LEVEL_EMPTY));
        }
        BigDecimal quantityBig = objectData.get(TransactionEventConstants.CHANGE_MEMBER_LEVEL_QUANTITY, BigDecimal.class);
        if (BigDecimalUtils.compare(quantityBig, Operator.LT, BigDecimal.ONE)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_POLICY_NUMBER_ERROR, I18N.text(DescribeI18NUtils.getFieldNameKey(LoyaltyPredefineObject.TransactionEvent.getApiName(), TransactionEventConstants.CHANGE_MEMBER_LEVEL_QUANTITY))));
        }
    }

    private void memberCheck(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        String memberId = objectData.get(TransactionEventConstants.MEMBER_ID, String.class);
        if (StringUtils.isBlank(memberId)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_MEMBER_EMPTY));
        }
        Optional.ofNullable(serviceFacade.findObjectDataByIds(context.getUser().getTenantId(), Collections.singletonList(memberId), LoyaltyPredefineObject.LoyaltyMember.getApiName()))
                .filter(CollectionUtils::notEmpty)
                .map(x -> x.get(0))
                .ifPresent(data ->
                        equalsPhone(objectData, data)
                );
    }

    public void equalsPhone(IObjectData eventData, IObjectData memberData) {
        String memberPhone = memberData.get("phone", String.class);
        String eventPhone = eventData.get(TransactionEventConstants.PHONE, String.class);
        if (!StringUtils.equals(memberPhone, eventPhone)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_MEMBER_PHONE_NOT_EQUAL));
        }
    }
}
