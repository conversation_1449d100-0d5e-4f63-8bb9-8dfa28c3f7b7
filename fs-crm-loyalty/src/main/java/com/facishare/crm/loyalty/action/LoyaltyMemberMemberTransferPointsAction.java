package com.facishare.crm.loyalty.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.loyalty.model.LoyaltyI18nKeys;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyPointsService;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.predefine.action.AbstractSimpleStandardAction;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.util.StringUtils;

public class LoyaltyMemberMemberTransferPointsAction extends AbstractSimpleStandardAction {

    private static final LoyaltyPointsService loyaltyPointsService = SpringUtil.getContext().getBean(LoyaltyPointsService.class);

    @Override
    protected ObjectAction getButton() {
        return ObjectAction.MEMBER_TRANSFER_POINTS;
    }

    @Override
    protected Result doAct(Arg arg) {
        JSONObject param = arg.getArgs();
        String tenantId = actionContext.getTenantId();
        String transferOutMemberId = arg.getObjectDataId();
        String transferIntoMemberId = param.getString("form_transfer_into_member_id");
        String type = param.getString("form_type");
        checkType(type);
        Loyalty.PointsOperationParam.Type operationType = Loyalty.PointsOperationParam.Type.valueOf(type);
        Long value = param.getLong("form_points");
        if (StringUtils.isEmpty(transferOutMemberId)) {
            String I18n = I18N.text(GetI18nKeyUtil.getFieldLabelKey(LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME, "transfer_out_member"));
            throw new ValidateException(I18N.text(LoyaltyI18nKey.MISSING_PARAMETERS, I18n));
        }
        if (StringUtils.isEmpty(transferIntoMemberId)) {
            String I18n = I18N.text(GetI18nKeyUtil.getFieldLabelKey(LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME, "transfer_into_member"));
            throw new ValidateException(I18N.text(LoyaltyI18nKey.MISSING_PARAMETERS, I18n));
        }
        if (value == null || value <= 0) {
            throw new ValidateException(I18N.text(LoyaltyI18nKeys.SFA_MEMBER_OPERATE_VALUE_POSITIVE_INTEGER));
        }
        Loyalty.PointsOperationParam operationParam = new Loyalty.PointsOperationParam();
        operationParam.setTenantId(tenantId);
        operationParam.setUniqueId(serviceFacade.generateId());
        operationParam.setMemberId(transferOutMemberId);
        operationParam.setTransferMemberId(transferIntoMemberId);
        operationParam.setDesc(param.getString("form_desc"));
        operationParam.setValue(Math.abs(value));
        operationParam.setType(operationType);
        operationParam.setOperator(actionContext.getUser().getUserId());
        loyaltyPointsService.operate(operationParam);
        return new AbstractSimpleStandardAction.Result();
    }

    public void checkType(String type) {
        String i18nKey = GetI18nKeyUtil.getFieldLabelKey(LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME
                , LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE);
        if (StringUtils.isEmpty(type)) {
            throw new ValidateException(I18N.text(LoyaltyI18nKey.MISSING_PARAMETERS, I18N.text(i18nKey)));
        }
        if (!Loyalty.PointsOperationParam.Type.TRANSFER_CONSUMER_POINTS.name().equals(type)
                && !Loyalty.PointsOperationParam.Type.TRANSFER_LEVEL_POINTS.name().equals(type)
                && !Loyalty.PointsOperationParam.Type.TRANSFER_FREEZE_CONSUMER_POINTS.name().equals(type)
                && !Loyalty.PointsOperationParam.Type.TRANSFER_FREEZE_LEVEL_POINTS.name().equals(type)) {
            throw new ValidateException(I18N.text(LoyaltyI18nKey.MISSING_PARAMETERS, I18N.text(i18nKey)));
        }
    }
}
