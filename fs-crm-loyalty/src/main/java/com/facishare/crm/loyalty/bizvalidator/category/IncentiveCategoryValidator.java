package com.facishare.crm.loyalty.bizvalidator.category;

import com.facishare.crm.constants.IncentiveCategoryConstants;
import com.facishare.crm.loyalty.LoyaltyPredefineObject;
import com.facishare.crm.sfa.predefine.bizvalidator.Validator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.service.treepath.impl.TreePathService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.i18n.LoyaltyKeyUtil;
import com.facishare.crm.sfa.utilities.validator.RebatePolicyValidator;
import com.facishare.crm.util.SearchTemplateQueryPlus;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class IncentiveCategoryValidator implements Validator {
    public static final int MAX_DEPTH = 3;
    private final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private final TreePathService treePathService = SpringUtil.getContext().getBean(TreePathService.class);

    @Override
    public void validate(ValidatorContext context) {
        validateSameProgram(context);
        validateDepth(context);
        validateRepeatName(context);
    }

    private void validateSameProgram(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        RebatePolicyValidator.validateEmptyField(objectData, LoyaltyPredefineObject.IncentiveCategory.getApiName()
                , Lists.newArrayList(IncentiveCategoryConstants.PROGRAM_ID));
        String programId = objectData.get(IncentiveCategoryConstants.PROGRAM_ID, String.class);
        String parentId = objectData.get(IncentiveCategoryConstants.PARENT_ID, String.class);
        if(StringUtils.isBlank(parentId)){
            return;
        }
        List<IObjectData> parentDataList = serviceFacade.findObjectDataByIdsIgnoreAll(context.getUser().getTenantId(), Lists.newArrayList(parentId), LoyaltyPredefineObject.IncentiveCategory.getApiName());
        if(CollectionUtils.isEmpty(parentDataList)){
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_CATEGORY_PARENT_NOT_EXIST));
        }
        IObjectData parentData = parentDataList.get(0);
        String parentProgramId = parentData.get(IncentiveCategoryConstants.PROGRAM_ID, String.class);
        if (!programId.equals(parentProgramId)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_CATEGORY_PROGRAMID_NOT_SAME));
        }
    }

    private void validateRepeatName(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        String name = objectData.getName();
        String id = objectData.getId();
        User user = context.getUser();
        String programId = objectData.get(IncentiveCategoryConstants.PROGRAM_ID, String.class);
        //判断id是否为空
        if (ObjectAction.CREATE.equals(context.getAction())) {
            checkNameUniqueInSameLevel(user, name, programId, null);
        } else {
            checkNameUniqueInSameLevel(user, name, programId, id);
        }
    }

    public void checkNameUniqueInSameLevel(User user, String categoryName, String programId, String excludeId) {
        if (StringUtils.isBlank(categoryName)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_CATEGORY_NAME_EMPTY));
        }
        SearchTemplateQueryPlus searchQuery = new SearchTemplateQueryPlus();
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterEq(filters, IObjectData.NAME, categoryName);
        SearchUtil.fillFilterEq(filters, IncentiveCategoryConstants.PROGRAM_ID, programId);

        if (StringUtils.isNotBlank(excludeId)) {
            SearchUtil.fillFilterNotEq(filters, DBRecord.ID, excludeId);
        }
        searchQuery.setLimit(1);
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, LoyaltyPredefineObject.IncentiveCategory.getApiName(), searchQuery).getData();
        if (CollectionUtils.isNotEmpty(dataList)) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_CATEGORY_NAME_EXIST));
        }
    }

    private void validateDepth(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        User user = context.getUser();
        String pid = objectData.get(IncentiveCategoryConstants.PARENT_ID, String.class);
        if (StringUtils.isBlank(pid)) {
            return;
        }
        int depth = treePathService.currentTreeDepth(user, pid, LoyaltyPredefineObject.IncentiveCategory.getApiName(), IncentiveCategoryConstants.PATH);
        if (depth + 1 >= MAX_DEPTH) {
            throw new ValidateException(I18N.text(LoyaltyKeyUtil.SFA_CATEGORY_DEPTH_LIMIT, MAX_DEPTH));
        }
    }

}
