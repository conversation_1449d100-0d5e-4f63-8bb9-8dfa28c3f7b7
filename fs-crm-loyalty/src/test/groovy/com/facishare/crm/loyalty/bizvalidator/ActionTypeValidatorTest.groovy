package com.facishare.crm.loyalty.bizvalidator

import com.facishare.crm.constants.ExtendedAttributeConstants
import com.facishare.crm.constants.IncentiveMetricConstants
import com.facishare.crm.constants.IncentivePolicyRuleConstants
import com.facishare.crm.loyalty.LoyaltyPredefineObject
import com.facishare.crm.loyalty.bizvalidator.policy.ActionTypeValidator
import com.facishare.crm.loyalty.model.IncentiveActionContext
import com.facishare.crm.loyalty.model.RuleActionModel
import com.facishare.crm.loyalty.utils.IncentivePolicyRuleUtils
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.RebatePolicyCalculateProUtil
import com.facishare.domain.sfa.RemoveUseless
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.util.SpringUtil
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import org.springframework.web.context.support.XmlWebApplicationContext
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.eq

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([
        IncentivePolicyRuleUtils.class,
        SpringUtil.class,
        I18N.class,
        RebatePolicyCalculateProUtil.class
])
@PowerMockIgnore(["javax.management.*"])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.paas.metadata.util.SpringUtil",
        "com.facishare.crm.loyalty.utils.IncentivePolicyRuleUtils",
        "com.facishare.crm.sfa.predefine.service.rebatecoupon.util.RebatePolicyCalculateProUtil"
])
class ActionTypeValidatorTest extends RemoveUseless {
    @Shared
    ServiceFacade serviceFacade
    @Shared
    BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService
    def setup() {
        PowerMockito.mockStatic(I18N)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.mockStatic(IncentivePolicyRuleUtils)
        PowerMockito.mockStatic(RebatePolicyCalculateProUtil)


        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        
        // 创建正确的mock对象
        serviceFacade = PowerMockito.mock(ServiceFacade.class)
        bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService.class)
        
        // 配置Spring容器返回这些mock对象
        PowerMockito.when(mockXmlWebApplicationContext.getBean(ServiceFacade.class)).thenReturn(serviceFacade)
        PowerMockito.when(mockXmlWebApplicationContext.getBean(BizConfigThreadLocalCacheService.class)).thenReturn(bizConfigThreadLocalCacheService)
        
        // Mock常用的I18N文本
        PowerMockito.when(I18N.text(any())).thenReturn("测试错误信息")
        PowerMockito.when(I18N.text(any(), any())).thenReturn("测试错误信息")
    }

    def "test checkValueLength with valid value"() {
        when:
        ActionTypeValidator.checkValueLength("12345")

        then:
        notThrown(ValidateException)
    }

    def "test checkValueLength with invalid length"() {
        when:
        ActionTypeValidator.checkValueLength("12345678901")

        then:
        thrown(ValidateException)
    }

    def "test checkValueLength with null value"() {
        when:
        ActionTypeValidator.checkValueLength(null)

        then:
        notThrown(ValidateException)
    }

    def "test checkValueLength with blank value"() {
        when:
        ActionTypeValidator.checkValueLength("")

        then:
        notThrown(ValidateException)
    }

    def "test consumer point action - valid scenario"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        ruleAction.setPointCategoryId("point_category_id")
        ruleAction.setValueType("fixed")
        ruleAction.setValue("100")
        context.setRuleActionModel(ruleAction)

        def pointCategoryData = new ObjectData()
        pointCategoryData.set("is_qualifying", false)
        pointCategoryData.set("program_id", "program_id")
        pointCategoryData.setName("积分类型")

        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any()))
                .thenReturn([pointCategoryData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType()).accept(context)

        then:
        notThrown(ValidateException)
        ruleAction.getPointCategoryName() == "积分类型"
    }

    def "test consumer point action - flow event scenario"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType(IncentivePolicyRuleConstants.FLOW_EVENT)
        context.setRuleActionModel(ruleAction)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType()).accept(context)

        then:
        notThrown(ValidateException)
    }

    def "test consumer point action - missing change type"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        context.setRuleActionModel(ruleAction)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test consumer point action - missing required fields"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        context.setRuleActionModel(ruleAction)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test consumer point action - qualifying point category"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        ruleAction.setPointCategoryId("point_category_id")
        ruleAction.setValueType("fixed")
        ruleAction.setValue("100")
        context.setRuleActionModel(ruleAction)
        
        def pointCategoryData = new ObjectData()
        pointCategoryData.set("is_qualifying", true)
        pointCategoryData.setName("积分类型")
        
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any()))
                .thenReturn([pointCategoryData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test consumer point action - with pool"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        ruleAction.setPointCategoryId("point_category_id")
        ruleAction.setValueType("fixed")
        ruleAction.setValue("100")
        ruleAction.setPoolId("pool_id")
        context.setRuleActionModel(ruleAction)
        
        def pointCategoryData = new ObjectData()
        pointCategoryData.set("is_qualifying", false)
        pointCategoryData.set("program_id", "program_id")
        pointCategoryData.setName("积分类型")
        
        def poolData = new ObjectData()
        poolData.set("program_id", "program_id")
        poolData.setName("积分池")
        
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any()))
                .thenReturn([pointCategoryData])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_POOL), any()))
                .thenReturn([poolData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType()).accept(context)

        then:
        notThrown(ValidateException)
        ruleAction.getPoolName() == "积分池"
    }

    def "test consumer point action - invalid fixed value"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        ruleAction.setPointCategoryId("point_category_id")
        ruleAction.setValueType("fixed")
        ruleAction.setValue("-10")
        context.setRuleActionModel(ruleAction)
        
        def pointCategoryData = new ObjectData()
        pointCategoryData.set("is_qualifying", false)
        pointCategoryData.set("program_id", "program_id")
        pointCategoryData.setName("积分类型")
        
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any()))
                .thenReturn([pointCategoryData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test tiered point action - valid scenario"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        ruleAction.setPointCategoryId("point_category_id")
        ruleAction.setValueType("fixed")
        ruleAction.setValue("100")
        context.setRuleActionModel(ruleAction)
        
        def pointCategoryData = new ObjectData()
        pointCategoryData.set("is_qualifying", true)
        pointCategoryData.set("program_id", "program_id")
        pointCategoryData.setName("积分类型")
        
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any()))
                .thenReturn([pointCategoryData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.TIERED_POINTS.getActionType()).accept(context)

        then:
        notThrown(ValidateException)
        ruleAction.getPointCategoryName() == "积分类型"
    }

    def "test tiered point action - not qualifying"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        ruleAction.setPointCategoryId("point_category_id")
        ruleAction.setValueType("fixed")
        ruleAction.setValue("100")
        context.setRuleActionModel(ruleAction)
        
        def pointCategoryData = new ObjectData()
        pointCategoryData.set("is_qualifying", false)
        pointCategoryData.setName("积分类型")
        
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any()))
                .thenReturn([pointCategoryData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.TIERED_POINTS.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test level action - valid scenario"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        ruleAction.setValueType("fixed")
        ruleAction.setValue("100")
        context.setRuleActionModel(ruleAction)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.LEVEL.getActionType()).accept(context)

        then:
        notThrown(ValidateException)
    }

    def "test level action - missing required fields"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        context.setRuleActionModel(ruleAction)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.LEVEL.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test level name action - valid scenario"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        ruleAction.setMemberLevelId("level_id")
        context.setRuleActionModel(ruleAction)
        
        def levelData = new ObjectData()
        levelData.set("program_id", "program_id")
        levelData.setName("VIP等级")
        
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.LOYALTY_TIER), any()))
                .thenReturn([levelData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.LEVEL_NAME.getActionType()).accept(context)

        then:
        notThrown(ValidateException)
        ruleAction.getMemberLevelName() == "VIP等级"
    }

    def "test level name action - missing member level id"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setChangeType("add")
        context.setRuleActionModel(ruleAction)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.LEVEL_NAME.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }


    def "test APL action - missing apl info"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        context.setRuleActionModel(ruleAction)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.APL.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test APL action - missing required fields"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        def aplInfo = new RuleActionModel.AplInfoBean()
        aplInfo.setApiName("test_function")
        ruleAction.setAplInfo(aplInfo)
        context.setRuleActionModel(ruleAction)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.APL.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test issued coupon action - coupon not open"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        context.setRuleActionModel(ruleAction)

        // 重新配置Spring容器返回mock对象，确保静态字段能获取到正确的mock
        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        PowerMockito.when(mockXmlWebApplicationContext.getBean(BizConfigThreadLocalCacheService.class)).thenReturn(bizConfigThreadLocalCacheService)
        PowerMockito.when(bizConfigThreadLocalCacheService.isOpenCoupon(any())).thenReturn(false)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.ISSUED_COUPON.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test issued coupon action - invalid quantity"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setCouponPlanId("coupon_plan_id")
        ruleAction.setQuantity(quantity)
        context.setRuleActionModel(ruleAction)

        // 重新配置Spring容器返回mock对象，确保静态字段能获取到正确的mock
        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        PowerMockito.when(mockXmlWebApplicationContext.getBean(BizConfigThreadLocalCacheService.class)).thenReturn(bizConfigThreadLocalCacheService)
        PowerMockito.when(bizConfigThreadLocalCacheService.isOpenCoupon(any())).thenReturn(true)

        def couponPlanData = new ObjectData()
        couponPlanData.set("program_id", "program_id")
        couponPlanData.setName("优惠券计划")

        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.COUPON_PLAN), any()))
                .thenReturn([couponPlanData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.ISSUED_COUPON.getActionType()).accept(context)

        then:
        thrown(ValidateException)

        where:
        quantity << [0, -1, 11]
    }



    def "test issued coupon action - missing coupon plan id"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setQuantity(5)
        context.setRuleActionModel(ruleAction)

        // 重新配置Spring容器返回mock对象，确保静态字段能获取到正确的mock
        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        PowerMockito.when(mockXmlWebApplicationContext.getBean(BizConfigThreadLocalCacheService.class)).thenReturn(bizConfigThreadLocalCacheService)
        PowerMockito.when(bizConfigThreadLocalCacheService.isOpenCoupon(any())).thenReturn(true)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.ISSUED_COUPON.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test issued coupon action - null quantity"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setCouponPlanId("coupon_plan_id")
        ruleAction.setQuantity(null)
        context.setRuleActionModel(ruleAction)

        // 重新配置Spring容器返回mock对象，确保静态字段能获取到正确的mock
        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        PowerMockito.when(mockXmlWebApplicationContext.getBean(BizConfigThreadLocalCacheService.class)).thenReturn(bizConfigThreadLocalCacheService)
        PowerMockito.when(bizConfigThreadLocalCacheService.isOpenCoupon(any())).thenReturn(true)

        def couponPlanData = new ObjectData()
        couponPlanData.set("program_id", "program_id")
        couponPlanData.setName("优惠券计划")

        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.COUPON_PLAN), any()))
                .thenReturn([couponPlanData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.ISSUED_COUPON.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }



    def "test issued coupon action - coupon plan not match coupon"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setCouponPlanId("coupon_plan_id")
        ruleAction.setQuantity(3)
        ruleAction.setCouponId("coupon_id")
        context.setRuleActionModel(ruleAction)

        // 重新配置Spring容器返回mock对象，确保静态字段能获取到正确的mock
        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        PowerMockito.when(mockXmlWebApplicationContext.getBean(BizConfigThreadLocalCacheService.class)).thenReturn(bizConfigThreadLocalCacheService)
        PowerMockito.when(bizConfigThreadLocalCacheService.isOpenCoupon(any())).thenReturn(true)

        def couponPlanData = new ObjectData()
        couponPlanData.set("program_id", "program_id")
        couponPlanData.setName("优惠券计划")

        def couponData = new ObjectData()
        couponData.set("coupon_plan_id", "different_coupon_plan_id")
        couponData.setName("优惠券")

        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.COUPON_PLAN), any()))
                .thenReturn([couponPlanData])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.COUPON), any()))
                .thenReturn([couponData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.ISSUED_COUPON.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test issued coupon action - coupon plan not match program id"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        ruleAction.setCouponPlanId("coupon_plan_id")
        ruleAction.setQuantity(5)
        context.setRuleActionModel(ruleAction)

        // 重新配置Spring容器返回mock对象，确保静态字段能获取到正确的mock
        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        PowerMockito.when(mockXmlWebApplicationContext.getBean(BizConfigThreadLocalCacheService.class)).thenReturn(bizConfigThreadLocalCacheService)
        PowerMockito.when(bizConfigThreadLocalCacheService.isOpenCoupon(any())).thenReturn(true)

        def couponPlanData = new ObjectData()
        couponPlanData.set("program_id", "different_program_id")
        couponPlanData.setName("优惠券计划")

        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(IncentivePolicyRuleConstants.COUPON_PLAN), any()))
                .thenReturn([couponPlanData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.ISSUED_COUPON.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test metric action - valid scenario"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        def metricInfo = new RuleActionModel.MetricInfoBean()
        metricInfo.setMetricId("metric_id")
        ruleAction.setMetricInfo(metricInfo)
        ruleAction.setValue("100")
        ruleAction.setValueType("fixed")
        context.setRuleActionModel(ruleAction)
        
        def metricData = new ObjectData()
        metricData.setId("metric_id")
        metricData.setName("测试指标")
        metricData.set(IncentiveMetricConstants.METRIC_INFO, '{"attributeId":"attr_id"}')
        
        def attributeData = new ObjectData()
        attributeData.setId("attr_id")
        attributeData.set(ExtendedAttributeConstants.DATA_TYPE, "number")
        attributeData.set(ExtendedAttributeConstants.ATTR_API_NAME, "test_attr__c")
        attributeData.set(ExtendedAttributeConstants.ATTR_TYPE, "member")
        attributeData.set(ExtendedAttributeConstants.DEFAULT_VALUE, "0")
        
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(LoyaltyPredefineObject.IncentiveMetric.getApiName()), any()))
                .thenReturn([metricData])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(LoyaltyPredefineObject.ExtendedAttribute.getApiName()), any()))
                .thenReturn([attributeData])

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.METRIC.getActionType()).accept(context)

        then:
        notThrown(ValidateException)
        metricInfo.getMetricName() == "测试指标"
    }

    def "test metric action - missing metric info"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        context.setRuleActionModel(ruleAction)

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.METRIC.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def "test metric action - expression with non-number type"() {
        given:
        def context = createBasicContext()
        def ruleAction = new RuleActionModel()
        def metricInfo = new RuleActionModel.MetricInfoBean()
        metricInfo.setMetricId("metric_id")
        ruleAction.setMetricInfo(metricInfo)
        ruleAction.setValue("\$EXT#METRIC#attr_id\$")
        ruleAction.setValueType(IncentivePolicyRuleConstants.ActionValueType.EXPRESSION.getActionValueType())
        context.setRuleActionModel(ruleAction)
        
        def metricData = new ObjectData()
        metricData.setId("metric_id")
        metricData.setName("测试指标")
        metricData.set(IncentiveMetricConstants.METRIC_INFO, '{"attributeId":"attr_id"}')
        metricData.set(IncentiveMetricConstants.METRIC_TYPE, IncentiveMetricConstants.MetricType.ATTRIBUTE.getMetricType())
        
        def attributeData = new ObjectData()
        attributeData.setId("attr_id")
        attributeData.set(ExtendedAttributeConstants.DATA_TYPE, "text")
        attributeData.set(ExtendedAttributeConstants.ATTR_API_NAME, "test_attr__c")
        attributeData.set(ExtendedAttributeConstants.ATTR_TYPE, "member")
        
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(LoyaltyPredefineObject.IncentiveMetric.getApiName()), any()))
                .thenReturn([metricData])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(any(), eq(LoyaltyPredefineObject.ExtendedAttribute.getApiName()), any()))
                .thenReturn([attributeData])
        
        // Mock RebatePolicyCalculateProUtil.splitMetricAndField
        PowerMockito.doAnswer({ invocation ->
            String value = invocation.getArgument(0)
            Set<String> metricIds = invocation.getArgument(1)
            Set<String> fields = invocation.getArgument(2)
            metricIds.add("metric_id")
            return null
        }).when(RebatePolicyCalculateProUtil.class, "splitMetricAndField", any(String.class), any(Set.class), any(Set.class))

        when:
        ActionTypeValidator.TYPE_MAP.get(IncentivePolicyRuleConstants.ActionType.METRIC.getActionType()).accept(context)

        then:
        thrown(ValidateException)
    }

    def createBasicContext() {
        def context = new IncentiveActionContext()
        context.setTenantId("tenant_id")
        context.setProgramId("program_id")

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        def mockFieldDescribe = PowerMockito.mock(IFieldDescribe)
        PowerMockito.when(mockFieldDescribe.getApiName()).thenReturn("test_field")
        PowerMockito.when(mockFieldDescribe.getLabel()).thenReturn("测试字段")
        PowerMockito.when(mockObjectDescribe.getFieldDescribes()).thenReturn([mockFieldDescribe])
        context.setUsedObjectDescribe(mockObjectDescribe)

        return context
    }
}