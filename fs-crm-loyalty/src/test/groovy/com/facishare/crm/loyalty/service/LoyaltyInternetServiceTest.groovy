package com.facishare.crm.loyalty.service

import com.facishare.crm.loyalty.model.LoyaltyInternet
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.reflect.Whitebox
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

@PrepareForTest([])
@SuppressStaticInitializationFor([])
class LoyaltyInternetServiceTest extends BaseDependencyFactory {

    @Shared
    LoyaltyInternetService loyaltyInternetService
    @Shared
    ServiceFacade serviceFacade
    @Shared
    ConfigService configService

    def setupSpec() {
        initConfigFactory()

        loyaltyInternetService = PowerMockito.spy(new LoyaltyInternetService())

        serviceFacade = PowerMockito.mock(ServiceFacade.class)
        IObjectData program = new ObjectData()
        program.set("internetAccount", "internetAccount")
        Mockito.doReturn(program).when(serviceFacade).findObjectDataIgnoreAll(any() as User, any() as String, any() as String)
        Whitebox.setInternalState(loyaltyInternetService, "serviceFacade", serviceFacade)

        configService = PowerMockito.mock(ConfigService.class)
        Mockito.doReturn("{\n" +
                "  \"secretKey\": \"secretKey\",\n" +
                "  \"loginType\": \"loginType\",\n" +
                "  \"internetAccount\": \"internetAccount\"\n" +
                "}").when(configService).findTenantConfig(any() as User, anyString())
        Whitebox.setInternalState(loyaltyInternetService, "configService", configService)
    }

    def "setConfig"() {
        given:

        when:
        loyaltyInternetService.setConfig("tenantId", new LoyaltyInternet.InternetInfo())

        then:
        noExceptionThrown()
    }

    def "getAccessUrl"() {
        given:

        when:
        loyaltyInternetService.getAccessUrl("89707", "mock_program_id", "mock_mobile", "mock_url?upstreamEa=upstreamEa&appId=appId")

        then:
        noExceptionThrown()
    }
}
