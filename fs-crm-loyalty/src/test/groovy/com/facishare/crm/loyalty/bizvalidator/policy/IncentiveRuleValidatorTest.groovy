package com.facishare.crm.loyalty.bizvalidator.policy

import com.alibaba.fastjson.JSON
import com.facishare.crm.constants.IncentiveMetricConstants
import com.facishare.crm.constants.IncentivePolicyConstants
import com.facishare.crm.constants.IncentivePolicyRuleConstants
import com.facishare.crm.loyalty.LoyaltyPredefineObject
import com.facishare.crm.loyalty.model.RuleActionModel
import com.facishare.crm.loyalty.service.incentive.rule.TransferRule
import com.facishare.crm.loyalty.utils.IncentivePolicyRuleUtils

import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RuleWhere
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil
import com.facishare.crm.sfa.utilities.util.i18n.LoyaltyKeyUtil
import com.facishare.crm.sfa.utilities.validator.PricePolicyValidator
import com.facishare.crm.util.DescribeI18NUtils
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import org.springframework.web.context.support.XmlWebApplicationContext
import spock.lang.Shared
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.eq

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([SpringUtil.class, IncentivePolicyRuleUtils.class, I18N.class, PricePolicyValidator.class, DescribeI18NUtils.class])
@PowerMockIgnore(["javax.management.*"])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.paas.metadata.util.SpringUtil",
        "com.facishare.crm.loyalty.utils.IncentivePolicyRuleUtils",
        "com.facishare.crm.sfa.utilities.validator.PricePolicyValidator"
])
class IncentiveRuleValidatorTest extends Specification {
    @Shared
    ServiceFacade serviceFacade
    @Shared
    TransferRule transferRule

    def setup() {
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.mockStatic(I18N)
        PowerMockito.mockStatic(IncentivePolicyRuleUtils)
        PowerMockito.mockStatic(PricePolicyValidator)
        PowerMockito.mockStatic(DescribeI18NUtils)

        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_RULE_NOT_EMPTY)).thenReturn("激励规则不能为空")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_RULE_ACTION_SIZE_LIMIT_ERROR, 10)).thenReturn("规则动作数量不能超过10个")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_ACTION_NAME_REPEAT)).thenReturn("激励动作名称重复：%s")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_METRIC_PARAMETER_ERROR_PLEASE_CHECK)).thenReturn("参数错误，请检查")
        PowerMockito.when(I18N.text(SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL)).thenReturn("%s 不能为空")
        PowerMockito.when(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR)).thenReturn("参数错误")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_RULE_NOT_EFFECTIVE)).thenReturn("激励规则无效")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_RULE_CONDITION_ONLY_ONE_POLICY_ATTRIBUTE)).thenReturn("规则条件只能包含一个政策属性")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_METRIC_AGGREGATE_RULE_TYPE_ERROR)).thenReturn("聚合规则类型错误")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_RULE_APL_BINDOBJECTAPINAME_EMPTY)).thenReturn("APL绑定对象API名称为空")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_INCENTIVE_RULE_APL_FUNCTION_NOT_EXIST)).thenReturn("APL函数不存在")
        PowerMockito.when(I18N.text(LoyaltyKeyUtil.SFA_METRIC_NOT_ACTIVE)).thenReturn("指标未激活")

        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)

        serviceFacade = PowerMockito.mock(ServiceFacade)
        transferRule = PowerMockito.mock(TransferRule)
        PowerMockito.when(mockXmlWebApplicationContext.getBean(ServiceFacade.class)).thenReturn(serviceFacade)
        PowerMockito.when(mockXmlWebApplicationContext.getBean(TransferRule.class)).thenReturn(transferRule)
        PowerMockito.when(DescribeI18NUtils.getFieldNameKey(any(),any())).thenReturn("IncentivePolicyRuleObj.field.action.label")
        PowerMockito.when(I18N.text("IncentivePolicyRuleObj.field.action.label")).thenReturn("激励动作")
    }

    def "测试 validate - 规则列表为空时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [])
        context.setDetailObjectData(detailObjectData)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "激励规则不能为空"
    }

    def "测试 validate - 规则列表为null时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), null)
        context.setDetailObjectData(detailObjectData)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "激励规则不能为空"
    }

    def "测试 validate - action为空时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()
        def ruleData = createMockObjectData([
            "id": "rule1",
            (IncentivePolicyRuleConstants.ACTION): null
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "激励动作 不能为空"
    }

    def "测试 validate - action为空字符串时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()
        def ruleData = createMockObjectData([
            "id": "rule1",
            (IncentivePolicyRuleConstants.ACTION): ""
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "激励动作 不能为空"
    }

    def "测试 validate - 无效action类型时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()
        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType("INVALID_TYPE")
        action.setActiveStatus("active")
        
        def ruleData = createMockObjectData([
            "id": "rule1",
            (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "参数错误，请检查"
    }

    def "测试 validate - action数量超过限制时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def actionList = []
        for (int i = 0; i < 11; i++) {
            def action = new RuleActionModel()
            action.setName("action_${i}")
            action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
            action.setActiveStatus("active")
            action.setChangeType("fixed")
            action.setPointCategoryId("point_category_1")
            action.setValueType("fixed")
            action.setValue("100")
            actionList.add(action)
        }
        def ruleData = createMockObjectData([
            "id": "rule1",
            (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString(actionList)
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def pointCategoryData = createMockObjectData([
                "id": "point_category_1",
                "name": "消费积分",
                "is_qualifying": false,
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any())).thenReturn([pointCategoryData])

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "规则动作数量不能超过10个"
    }

    def "测试 validate - action名称重复时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action1 = new RuleActionModel()
        action1.setName("duplicate_name")
        action1.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action1.setActiveStatus("active")
        action1.setChangeType("fixed")
        action1.setPointCategoryId("point_category_1")
        action1.setValueType("fixed")
        action1.setValue("100")
        def action2 = new RuleActionModel()
        action2.setName("duplicate_name")
        action2.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action2.setActiveStatus("active")
        action2.setChangeType("fixed")
        action2.setPointCategoryId("point_category_1")
        action2.setValueType("fixed")
        action2.setValue("100")
        def actionList = [action1, action2]
        def ruleData = createMockObjectData([
            "id": "rule1",
            (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString(actionList)
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def pointCategoryData = createMockObjectData([
                "id": "point_category_1",
                "name": "消费积分",
                "is_qualifying": false,
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any())).thenReturn([pointCategoryData])

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "激励动作名称重复：duplicate_name"
    }

    def "测试 validate - 成功验证有效的激励规则"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): null
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def pointCategoryData = createMockObjectData([
                "id": "point_category_1",
                "name": "消费积分",
                "is_qualifying": false,
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any())).thenReturn([pointCategoryData])

        when:
        incentiveRuleValidator.validate(context)

        then:
        noExceptionThrown()
    }

    def "测试 validate - action为空数组时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()
        def ruleData = createMockObjectData([
            "id": "rule1",
            (IncentivePolicyRuleConstants.ACTION): "[]"
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "激励动作参数错误"
    }

    def "测试 validate - action中name为空时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName(null)
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")

        def ruleData = createMockObjectData([
            "id": "rule1",
            (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "参数错误，请检查"
    }

    def "测试 validate - action中actionType为空时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(null)
        action.setActiveStatus("active")

        def ruleData = createMockObjectData([
            "id": "rule1",
            (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "参数错误，请检查"
    }

    def "测试 validate - action中activeStatus为空时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus(null)

        def ruleData = createMockObjectData([
            "id": "rule1",
            (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "参数错误，请检查"
    }

    def "测试 validate - 有效的条件验证通过"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def filter = new RuleWhere.FiltersBean()
        filter.setFieldNameType("field")
        filter.setFieldName("amount")
        filter.setOperator("gt")
        filter.setFieldValues(["100"])

        def ruleWhere = new RuleWhere()
        ruleWhere.setFilters([filter])

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): JSON.toJSONString([ruleWhere])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def pointCategoryData = createMockObjectData([
                "id": "point_category_1",
                "name": "消费积分",
                "is_qualifying": false,
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any())).thenReturn([pointCategoryData])
        PowerMockito.when(transferRule.transferStandRuleWhere(eq("tenant1"), any())).thenReturn([ruleWhere])

        when:
        incentiveRuleValidator.validate(context)

        then:
        noExceptionThrown()
    }

    def "测试 validate - 条件中filter为空时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def ruleWhere = new RuleWhere()
        ruleWhere.setFilters([])

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): JSON.toJSONString([ruleWhere])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "激励规则无效"
    }

    def "测试 validate - 条件中filter字段不完整时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def filter = new RuleWhere.FiltersBean()
        filter.setFieldNameType("field")
        filter.setFieldName(null) // 缺少字段名
        filter.setOperator("gt")
        filter.setFieldValues(["100"])

        def ruleWhere = new RuleWhere()
        ruleWhere.setFilters([filter])

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): JSON.toJSONString([ruleWhere])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "激励规则无效"
    }

    def "测试 validate - TIERED_POINTS action类型验证成功"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("tiered_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.TIERED_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): null
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def pointCategoryData = createMockObjectData([
                "id": "point_category_1",
                "name": "定级积分",
                "is_qualifying": true,
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any())).thenReturn([pointCategoryData])

        when:
        incentiveRuleValidator.validate(context)

        then:
        noExceptionThrown()
    }

    def "测试 validate - LEVEL action类型验证成功"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("level_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.LEVEL.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setValueType("fixed")
        action.setValue("1")

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): null
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        when:
        incentiveRuleValidator.validate(context)

        then:
        noExceptionThrown()
    }

    def "测试 validate - LEVEL_NAME action类型验证成功"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("level_name_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.LEVEL_NAME.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setMemberLevelId("level_1")

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): null
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def levelData = createMockObjectData([
                "id": "level_1",
                "name": "黄金会员",
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_TIER), any())).thenReturn([levelData])

        when:
        incentiveRuleValidator.validate(context)

        then:
        noExceptionThrown()
    }

    def "测试 validate - 跟随事件类型的action验证通过"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("flow_event_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType(IncentivePolicyRuleConstants.FLOW_EVENT)

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): null
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        when:
        incentiveRuleValidator.validate(context)

        then:
        noExceptionThrown()
    }

    def "测试 validate - 条件中包含指标验证成功"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def filter = new RuleWhere.FiltersBean()
        filter.setFieldNameType(IncentivePolicyRuleConstants.FieldNameType.METRIC.getType())
        filter.setFieldName("metric_1")
        filter.setOperator("gt")
        filter.setFieldValues(["100"])

        def ruleWhere = new RuleWhere()
        ruleWhere.setFilters([filter])

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): JSON.toJSONString([ruleWhere])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def pointCategoryData = createMockObjectData([
                "id": "point_category_1",
                "name": "消费积分",
                "is_qualifying": false,
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any())).thenReturn([pointCategoryData])

        def metricData = createMockObjectData([
                "id": "metric_1",
                "name": "销售额指标",
                (IncentiveMetricConstants.ACTIVE_STATUS): IncentiveMetricConstants.ActiveStatusType.ENABLE.getActiveStatusType()
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(LoyaltyPredefineObject.IncentiveMetric.getApiName()), any())).thenReturn([metricData])
        PowerMockito.when(IncentivePolicyRuleUtils.getIdsByType(any(), eq(IncentivePolicyRuleConstants.FieldNameType.METRIC.getType()))).thenReturn(Sets.newHashSet("metric_1"))
        PowerMockito.when(transferRule.transferStandRuleWhere(eq("tenant1"), any())).thenReturn([ruleWhere])

        when:
        incentiveRuleValidator.validate(context)

        then:
        noExceptionThrown()
    }

    def "测试 validate - 条件中指标未激活时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def filter = new RuleWhere.FiltersBean()
        filter.setFieldNameType(IncentivePolicyRuleConstants.FieldNameType.METRIC.getType())
        filter.setFieldName("metric_1")
        filter.setOperator("gt")
        filter.setFieldValues(["100"])

        def ruleWhere = new RuleWhere()
        ruleWhere.setFilters([filter])

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): JSON.toJSONString([ruleWhere])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def metricData = createMockObjectData([
                "id": "metric_1",
                "name": "销售额指标",
                (IncentiveMetricConstants.ACTIVE_STATUS): IncentiveMetricConstants.ActiveStatusType.DISABLE.getActiveStatusType()
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(LoyaltyPredefineObject.IncentiveMetric.getApiName()), any())).thenReturn([metricData])
        PowerMockito.when(IncentivePolicyRuleUtils.getIdsByType(any(), eq(IncentivePolicyRuleConstants.FieldNameType.METRIC.getType()))).thenReturn(Sets.newHashSet("metric_1"))

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "指标未激活"
    }

    def "测试 validate - 条件中APL函数bindObjectApiName为空时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def filter = new RuleWhere.FiltersBean()
        filter.setFieldNameType(IncentivePolicyRuleConstants.FieldNameType.APL.getType())
        filter.setFieldName("apl_function_1")
        filter.setOperator("gt")
        filter.setFieldValues(["100"])
        filter.setBindObjectApiName(null) // APL函数缺少绑定对象

        def ruleWhere = new RuleWhere()
        ruleWhere.setFilters([filter])

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): JSON.toJSONString([ruleWhere])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def pointCategoryData = createMockObjectData([
                "id": "point_category_1",
                "name": "消费积分",
                "is_qualifying": false,
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any())).thenReturn([pointCategoryData])
        PowerMockito.when(transferRule.transferStandRuleWhere(eq("tenant1"), any())).thenReturn([ruleWhere])

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "APL绑定对象API名称为空"
    }

    /*def "测试 validate - 条件中包含多个政策属性时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def filter1 = new RuleWhere.FiltersBean()
        filter1.setFieldNameType(IncentivePolicyRuleConstants.FieldNameType.ATTRIBUTE.getType())
        filter1.setFieldName("attr_1")
        filter1.setOperator("eq")
        filter1.setFieldValues(["value1"])
        filter1.setBindObjectApiName(LoyaltyPredefineObject.ExtendedAttributePolicy.getApiName())

        def filter2 = new RuleWhere.FiltersBean()
        filter2.setFieldNameType("field")
        filter2.setFieldName("amount")
        filter2.setOperator("gt")
        filter2.setFieldValues(["100"])

        def ruleWhere1 = new RuleWhere()
        ruleWhere1.setFilters([filter1])

        def ruleWhere2 = new RuleWhere()
        ruleWhere2.setFilters([filter2])

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): JSON.toJSONString([ruleWhere1, ruleWhere2])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def pointCategoryData = createMockObjectData([
                "id": "point_category_1",
                "name": "消费积分",
                "is_qualifying": false,
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any())).thenReturn([pointCategoryData])
        PowerMockito.when(transferRule.transferStandRuleWhere(eq("tenant1"), any())).thenReturn([ruleWhere1, ruleWhere2])

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "规则条件只能包含一个政策属性"
    }*/

    def "测试 validate - 条件中聚合规则类型错误时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def filter = new RuleWhere.FiltersBean()
        filter.setFieldNameType(IncentivePolicyRuleConstants.FieldNameType.AGGREGATE.getType())
        filter.setFieldName("aggregate_1")
        filter.setOperator("gt")
        filter.setFieldValues(["100"])

        def ruleWhere = new RuleWhere()
        ruleWhere.setFilters([filter])

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): JSON.toJSONString([ruleWhere])
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def pointCategoryData = createMockObjectData([
                "id": "point_category_1",
                "name": "消费积分",
                "is_qualifying": false,
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any())).thenReturn([pointCategoryData])

        def aggregateData = createMockObjectData([
                "id": "aggregate_1",
                "name": "聚合规则",
                (AggregateRuleConstants.Field.RULE_TYPE): "PRICE" // 错误的规则类型
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq("AggregateRuleObj"), any())).thenReturn([aggregateData])
        PowerMockito.when(IncentivePolicyRuleUtils.getIdsByType(any(), eq(IncentivePolicyRuleConstants.FieldNameType.AGGREGATE.getType()))).thenReturn(Sets.newHashSet("aggregate_1"))

        // 创建转换后的RuleWhere，包含聚合类型的filter
        def transferredFilter = new RuleWhere.FiltersBean()
        transferredFilter.setFieldNameType(IncentivePolicyRuleConstants.FieldNameType.AGGREGATE.getType())
        transferredFilter.setFieldName("aggregate_1")
        transferredFilter.setOperator("gt")
        transferredFilter.setFieldValues(["100"])
        transferredFilter.setFromMetric(false)

        def transferredRuleWhere = new RuleWhere()
        transferredRuleWhere.setFilters([transferredFilter])

        PowerMockito.when(transferRule.transferStandRuleWhere(eq("tenant1"), any())).thenReturn([transferredRuleWhere])
        PowerMockito.doNothing().when(PricePolicyValidator.class, "validateObjectFields", any(), any())

        when:
        incentiveRuleValidator.validate(context)

        then:
        def e = thrown(ValidateException)
        e.message == "聚合规则类型错误"
    }

    def "测试 validate - JSON解析异常时抛出异常"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def ruleData = createMockObjectData([
            "id": "rule1",
            (IncentivePolicyRuleConstants.ACTION): "invalid_json"
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        when:
        incentiveRuleValidator.validate(context)

        then:
        thrown(Exception)
    }

    def "测试 validate - 条件为空字符串时验证通过"() {
        given:
        def context = createValidatorContext("tenant1")
        def incentiveRuleValidator = new IncentiveRuleValidator()

        def action = new RuleActionModel()
        action.setName("test_action")
        action.setActionType(IncentivePolicyRuleConstants.ActionType.CONSUMER_POINTS.getActionType())
        action.setActiveStatus("active")
        action.setChangeType("fixed")
        action.setPointCategoryId("point_category_1")
        action.setValueType("fixed")
        action.setValue("100")

        def ruleData = createMockObjectData([
                "id": "rule1",
                (IncentivePolicyRuleConstants.ACTION): JSON.toJSONString([action]),
                (IncentivePolicyRuleConstants.CONDITION_CONTENT): ""
        ])
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put(LoyaltyPredefineObject.IncentivePolicyRule.getApiName(), [ruleData])
        context.setDetailObjectData(detailObjectData)

        def mockObjectDescribe = PowerMockito.mock(IObjectDescribe)
        PowerMockito.when(serviceFacade.findObject(eq("tenant1"), any())).thenReturn(mockObjectDescribe)

        def pointCategoryData = createMockObjectData([
                "id": "point_category_1",
                "name": "消费积分",
                "is_qualifying": false,
                "program_id": "program1"
        ])
        PowerMockito.when(IncentivePolicyRuleUtils.validateReferenceIds(eq("tenant1"), eq(IncentivePolicyRuleConstants.LOYALTY_POINT_TYPE), any())).thenReturn([pointCategoryData])

        when:
        incentiveRuleValidator.validate(context)

        then:
        noExceptionThrown()
    }

    private ValidatorContext createValidatorContext(String tenantId) {
        def user = User.systemUser(tenantId)
        ValidatorContext validatorContext = new ValidatorContext()
        validatorContext.setUser(user)
        def objectData = createMockObjectData([
                "id": "policy1",
                (IncentivePolicyConstants.USED_OBJECT_API_NAME): "TestObject",
                (IncentivePolicyConstants.PROGRAM_ID): "program1"
        ])
        validatorContext.setObjectData(objectData)
        return validatorContext
    }

    private IObjectData createMockObjectData(Map<String, Object> data) {
        def objectData = new ObjectData()
        objectData.setId(data.get("id", "default_id") as String)
        if (data.containsKey("name")) {
            objectData.setName(data.get("name") as String)
        }
        data.each { key, value ->
            if (key != "id" && key != "name") {
                objectData.set(key, value)
            }
        }
        return objectData
    }
}
