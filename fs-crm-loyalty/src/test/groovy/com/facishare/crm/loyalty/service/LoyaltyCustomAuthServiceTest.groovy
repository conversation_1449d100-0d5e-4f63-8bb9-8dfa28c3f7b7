package com.facishare.crm.loyalty.service

import com.facishare.crm.loyalty.LoyaltyPredefineObject
import com.facishare.crm.loyalty.model.LoyaltyCustomAuth
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyPointsService
import com.facishare.crm.sfa.predefine.service.rebatecoupon.CouponMemberService
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.google.common.collect.Lists
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.reflect.Whitebox
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

@PrepareForTest([])
@SuppressStaticInitializationFor([
])
class LoyaltyCustomAuthServiceTest extends BaseDependencyFactory {

    @Shared
    LoyaltyCustomAuthService loyaltyCustomAuthService
    @Shared
    LoyaltyMemberService loyaltyMemberService
    @Shared
    LoyaltyPointsService loyaltyPointsService
    @Shared
    ServiceFacade serviceFacade
    @Shared
    ServiceContext serviceContext
    @Shared
    LoyaltyPointsOperationService loyaltyPointsOperationService
    @Shared
    CouponMemberService couponMemberService

    def setup() {
        initI18N()
    }

    def setupSpec() {
        serviceContext = Mockito.mock(ServiceContext)
        Mockito.doReturn("89707").when(serviceContext).getTenantId()

        loyaltyCustomAuthService = PowerMockito.spy(new LoyaltyCustomAuthService())

        PowerMockito.doReturn("member_mock_id").when(loyaltyCustomAuthService).getMemberId(any() as LoyaltyCustomAuth.TokenInfo, anyString())
        PowerMockito.doAnswer {
            ServiceContext serviceContext = it.getArgument(0)
            LoyaltyCustomAuth.CommonParam param = it.getArgument(1)
            param.setTenantId(serviceContext != null ? "89707" : "86085")
            param.setRelatedField("phone")
            param.setRelatedValue("18741111111")
            IObjectData marketingMember = new ObjectData()
            marketingMember.setId("marketingMember_mock_id")
            param.setMarketingMember(marketingMember)
            return param
        }.when(loyaltyCustomAuthService).preProcessing(any() as ServiceContext, any() as LoyaltyCustomAuth.CommonParam)

        loyaltyMemberService = PowerMockito.mock(LoyaltyMemberService.class)
        PowerMockito.doAnswer {
            Object[] args = it.getArguments()
            String id = args[1]
            if ("absent".equals(id)) {
                return null
            }
            IObjectData defaultTier = new ObjectData()
            defaultTier.setId("tier_mock_id")
            return defaultTier
        }.when(loyaltyMemberService).getDefaultTier(anyString(), anyString())
        Whitebox.setInternalState(loyaltyCustomAuthService, "loyaltyMemberService", loyaltyMemberService)

        initServiceFacade()
        Whitebox.setInternalState(loyaltyCustomAuthService, "serviceFacade", serviceFacade)

        initRedissonService()
        Whitebox.setInternalState(loyaltyCustomAuthService, "redissonService", redissonService)

        loyaltyPointsService = Mockito.mock(LoyaltyPointsService)
        Whitebox.setInternalState(loyaltyCustomAuthService, "loyaltyPointsService", loyaltyPointsService)

        loyaltyPointsOperationService = Mockito.mock(LoyaltyPointsOperationService)
        PowerMockito.doAnswer {
            String programId = it.getArgument(1)
            if (programId == null) {
                return null
            } else {
                return new ObjectData()
            }
        }.when(loyaltyPointsOperationService).findDefaultPointPool(anyString(), anyString())
        Whitebox.setInternalState(loyaltyCustomAuthService, "loyaltyPointsOperationService", loyaltyPointsOperationService)

        couponMemberService = Mockito.mock(CouponMemberService)
        Whitebox.setInternalState(loyaltyCustomAuthService, "couponMemberService", couponMemberService)
    }

    void initServiceFacade() {
        ObjectDataDocument indifferentData = new ObjectDataDocument()
        serviceFacade = PowerMockito.mock(ServiceFacade.class)

        BaseObjectSaveAction.Result result = new BaseObjectSaveAction.Result()
        result.setObjectData(indifferentData)

        PowerMockito.doReturn(result).when(serviceFacade).triggerAction(any() as ActionContext, any() as BaseObjectSaveAction.Arg, any() as Class)

        PowerMockito.doAnswer {
            Object[] args = it.getArguments()
            User user = (User) args[0]
            String tenantId = user.getTenantId()
            String descApiName = (String) args[1]
            if (LoyaltyPredefineObject.LoyaltyMemberChangeRecords.getApiName() == descApiName) {
                QueryResult<IObjectData> queryResult = new QueryResult<>()
                IObjectData data = new ObjectData()
                data.set("member_points_detail_id", "mock_member_points_detail_id")
                queryResult.setData(Lists.newArrayList(data))
                return queryResult
            } else if (LoyaltyPredefineObject.LoyaltyMember.getApiName().equals(descApiName) && "89707".equals(tenantId)) {
                QueryResult<IObjectData> queryResult = new QueryResult<>()
                IObjectData data = new ObjectData()
                data.setId("mock_member_id")
                queryResult.setData(Lists.newArrayList(data))
                return queryResult
            } else if (LoyaltyPredefineObject.LoyaltyPointPool.getApiName().equals(descApiName)) {
                QueryResult<IObjectData> queryResult = new QueryResult<>()
                IObjectData data = new ObjectData()
                data.setId("mock_point_pool_id")
                queryResult.setData(Lists.newArrayList(data))
                return queryResult
            } else if (LoyaltyPredefineObject.LoyaltyProgram.getApiName().equals(descApiName)) {
                QueryResult<IObjectData> queryResult = new QueryResult<>()
                IObjectData data = new ObjectData()
                data.setId("mock_program_id")
                queryResult.setData(Lists.newArrayList(data))
                return queryResult
            }
            return null
        }.when(serviceFacade).findBySearchQueryIgnoreAll(any() as User, anyString(), any() as SearchTemplateQuery)

        PowerMockito.doAnswer {
            Object[] args = it.getArguments()
            String id = args[1]
            String descApiName = args[2]
            if (LoyaltyPredefineObject.LoyaltyProgram.getApiName().equals(descApiName)) {
                if ("absent".equals(id)) {
                    return null
                }
                IObjectData data = new ObjectData()
                data.set("join_type", id)
                return data
            }
            return null
        }.when(serviceFacade).findObjectDataIgnoreAll(any() as User, anyString(), anyString())
    }

    def "memberList"() {
        given:
        LoyaltyCustomAuth.CommonParam param = new LoyaltyCustomAuth.CommonParam()
        param.setLimit(20)
        param.setOffset(0)

        when:
        loyaltyCustomAuthService.memberList(null, param)
        then:
        noExceptionThrown()
    }

    def "member"() {
        given:
        LoyaltyCustomAuth.MemberParam param = new LoyaltyCustomAuth.MemberParam()
        param.setProgramId(programId)

        when:
        loyaltyCustomAuthService.member(sc, param)

        then:
        noExceptionThrown()
        where:
        sc             | programId || _
        serviceContext | "system"  || _
        null           | "system"  || _
        null           | "apply"   || _
    }

    def "member ValidateException"() {
        given:
        LoyaltyCustomAuth.MemberParam param = new LoyaltyCustomAuth.MemberParam()
        param.setProgramId(programId)

        when:
        loyaltyCustomAuthService.member(null, param)

        then:
        thrown(e)
        where:
        programId || e
        "absent"  || ValidateException
        null      || ValidateException
    }

    def "memberAdd"() {
        given:
        LoyaltyCustomAuth.MemberParam param = new LoyaltyCustomAuth.MemberParam()
        param.setProgramId("programId")

        when:
        loyaltyCustomAuthService.memberAdd(null, param)
        then:
        noExceptionThrown()
    }

    def "memberAdd ValidateException"() {
        given:
        LoyaltyCustomAuth.MemberParam param = new LoyaltyCustomAuth.MemberParam()
        param.setProgramId(programId)

        when:
        loyaltyCustomAuthService.memberAdd(sc, param)
        then:
        thrown(e)

        where:
        sc             | programId || e
        serviceContext | null      || ValidateException
        null           | "absent"  || ValidateException
    }

    def "queryPointsRecords"() {
        given:
        LoyaltyCustomAuth.PointsRecordsQueryParam param = new LoyaltyCustomAuth.PointsRecordsQueryParam()
        param.setProgramId("programId")

        QueryResult<IObjectData> pointsDetailsQueryResult = new QueryResult<>()
        IObjectData data = new ObjectData()
        data.setId("mock_member_points_detail_id")
        pointsDetailsQueryResult.setData(Lists.newArrayList(data))
        PowerMockito.doReturn(pointsDetailsQueryResult).when(serviceFacade)
                .findBySearchQueryWithFieldsIgnoreAll(any() as User, anyString(), any() as SearchTemplateQuery, any() as List)
        when:
        loyaltyCustomAuthService.queryPointsRecords(serviceContext, param)
        then:
        noExceptionThrown()
    }

    def "queryProgramList"() {
        given:
        LoyaltyCustomAuth.ProgramQueryParam param = new LoyaltyCustomAuth.ProgramQueryParam()
        param.setProgramId("programId")

        when:
        loyaltyCustomAuthService.queryProgramList(null, param)
        then:
        noExceptionThrown()
    }

    def "queryStoreList"() {
        given:
        LoyaltyCustomAuth.StoreQueryParam param = new LoyaltyCustomAuth.StoreQueryParam()
        param.setProgramId("programId")
        param.setStoreId("storeId")
        param.setName("name")
        param.setGis(Lists.newArrayList("(0,0),5000"))

        when:
        loyaltyCustomAuthService.queryStoreList(null, param)
        then:
        noExceptionThrown()
    }

    def "createOperationNumber"() {
        given:

        when:
        loyaltyCustomAuthService.createOperationNumber(null)

        then:
        noExceptionThrown()
    }

    def "findOperation"() {
        given:
        LoyaltyCustomAuth.QueryOperationParam param = new LoyaltyCustomAuth.QueryOperationParam()
        param.setOperateId("operateId")

        when:
        loyaltyCustomAuthService.findOperation(serviceContext, param)

        then:
        noExceptionThrown()
    }

    def "operate"() {
        given:
        LoyaltyCustomAuth.OperateParam param = new LoyaltyCustomAuth.OperateParam()
        param.setOperateId("operateId")
        param.setProgramId("programId")
        param.setType("CONSUMER_POINTS_TO_MEMBER")
        param.setValue(10L)

        when:
        loyaltyCustomAuthService.operate(serviceContext, param)

        then:
        noExceptionThrown()
    }

    def "operate ValidateException"() {
        given:
        LoyaltyCustomAuth.OperateParam param = new LoyaltyCustomAuth.OperateParam()
        param.setOperateId(operateId)
        param.setProgramId(programId)
        param.setType(type)
        param.setValue(value)

        when:
        loyaltyCustomAuthService.operate(serviceContext, param)

        then:
        thrown(ValidateException)

        where:
        operateId   | programId   | type                        | value || _
        null        | null        | null                        | null  || _
        "operateId" | null        | null                        | null  || _
        "operateId" | "programId" | null                        | null  || _
        "operateId" | "programId" | "CONSUMER_POINTS_TO_MEMBER" | null  || _
    }

    def "queryByMember"() {
        given:
        LoyaltyCustomAuth.CouponMemberQueryParam param = new LoyaltyCustomAuth.CouponMemberQueryParam()
        param.setProgramId("programId")
        param.setLimit(10)
        param.setOffset(0)

        when:
        loyaltyCustomAuthService.queryByMember(serviceContext, param)

        then:
        noExceptionThrown()
    }

}
