package com.facishare.crm.loyalty.bizvalidator

import com.facishare.crm.loyalty.bizvalidator.category.IncentiveCategoryEditValidator
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext
import com.facishare.crm.sfa.predefine.service.treepath.impl.TreePathService
import com.facishare.domain.sfa.RemoveUseless
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.util.SpringUtil
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import org.springframework.web.context.support.XmlWebApplicationContext

import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_DATA_LOOP
import static org.mockito.ArgumentMatchers.*

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([])
@PowerMockIgnore(["javax.management.*"])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.paas.metadata.util.SpringUtil"
])
class IncentiveCategoryEditValidatorTest extends RemoveUseless {
    
    def setup() {
        PowerMockito.mockStatic(I18N)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.when(I18N.text(SO_DATA_LOOP)).thenReturn("所选上级已为其子级，不可让数据成环，请重新选择上级数据")
        mockSpringUtilsByClass([TreePathService.class])
    }

    def mockSpringUtilsByClass(List<Class> typeArray) {
        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        typeArray.forEach({ type ->
            def mockByType = PowerMockito.mock(type)
            PowerMockito.when(mockXmlWebApplicationContext.getBean(type)).thenReturn(mockByType)
        })
    }

    def "incentive category edit validator - no loop"() {
        given:
        def objectData = new ObjectData()
        objectData.set("id", "category1")
        objectData.set("parent_id", "parentCategory1")
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().objectData(objectData).user(user).build()
        def validator = new IncentiveCategoryEditValidator()
        def treePathService = PowerMockito.mock(TreePathService)
        Whitebox.setInternalState(validator, "treePathService", treePathService)
        
        // Mock不形成循环的情况
        PowerMockito.when(treePathService.isLoop(any(), any(), any(), any(), any())).thenReturn(false)

        when:
        validator.validate(validatorContext)

        then:
        noExceptionThrown()
    }

    def "incentive category edit validator - has loop"() {
        given:
        def objectData = new ObjectData()
        objectData.set("id", "category1")
        objectData.set("parent_id", "childCategory1")
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().objectData(objectData).user(user).build()
        def validator = new IncentiveCategoryEditValidator()
        def treePathService = PowerMockito.mock(TreePathService)
        Whitebox.setInternalState(validator, "treePathService", treePathService)
        
        // Mock形成循环的情况
        PowerMockito.when(treePathService.isLoop(any(), any(), any(), any(), any())).thenReturn(true)

        when:
        validator.validate(validatorContext)

        then:
        def exception = thrown(ValidateException)
        exception.getMessage() == "所选上级已为其子级，不可让数据成环，请重新选择上级数据"
    }

    def "incentive category edit validator - null parent id"() {
        given:
        def objectData = new ObjectData()
        objectData.set("id", "category1")
        objectData.set("parent_id", null)
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().objectData(objectData).user(user).build()
        def validator = new IncentiveCategoryEditValidator()
        def treePathService = PowerMockito.mock(TreePathService)
        Whitebox.setInternalState(validator, "treePathService", treePathService)
        
        // Mock不形成循环的情况（null parent_id 通常不会形成循环）
        PowerMockito.when(treePathService.isLoop(any(), any(), any(), isNull(), any())).thenReturn(false)

        when:
        validator.validate(validatorContext)

        then:
        noExceptionThrown()
    }

    def "incentive category edit validator - empty parent id"() {
        given:
        def objectData = new ObjectData()
        objectData.set("id", "category1")
        objectData.set("parent_id", "")
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().objectData(objectData).user(user).build()
        def validator = new IncentiveCategoryEditValidator()
        def treePathService = PowerMockito.mock(TreePathService)
        Whitebox.setInternalState(validator, "treePathService", treePathService)
        
        // Mock不形成循环的情况
        PowerMockito.when(treePathService.isLoop(any(), any(), any(), eq(""), any())).thenReturn(false)

        when:
        validator.validate(validatorContext)

        then:
        noExceptionThrown()
    }
} 