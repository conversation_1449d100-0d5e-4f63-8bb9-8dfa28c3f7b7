package com.facishare.crm.loyalty.bizvalidator


import com.facishare.crm.loyalty.bizvalidator.category.IncentiveCategoryEditValidator
import com.facishare.crm.loyalty.bizvalidator.category.IncentiveCategoryInvalidValidator
import com.facishare.crm.loyalty.bizvalidator.category.IncentiveCategoryValidator
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext
import com.facishare.crm.sfa.predefine.service.treepath.impl.TreePathService
import com.facishare.domain.sfa.RemoveUseless
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import org.springframework.web.context.support.XmlWebApplicationContext

import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_DATA_LOOP
import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([])
@PowerMockIgnore(["javax.management.*"])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.paas.metadata.util.SpringUtil"
])
class CategoryAddTest extends RemoveUseless {
    def setup() {
        PowerMockito.mockStatic(I18N)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.when(I18N.text(SO_DATA_LOOP)).thenReturn("所选上级已为其子级，不可让数据成环，请重新选择上级数据")
        mockSpringUtilsByClass([TreePathService.class])
    }

    def mockSpringUtilsByClass(List<Class> typeArray) {
        def mockXmlWebApplicationContext = PowerMockito.mock(XmlWebApplicationContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(mockXmlWebApplicationContext)
        typeArray.forEach({ type ->
            def mockByType = PowerMockito.mock(type)
            PowerMockito.when(mockXmlWebApplicationContext.getBean(type)).thenReturn(mockByType)
        })
    }

    def "incentive category edit validator"() {
        given:
        def objectData = new ObjectData()
        objectData.set("id", "id")
        objectData.set("parent_id", "parent_id")
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().objectData(objectData).user(user).build()
        def validator = new IncentiveCategoryEditValidator()
        def treePathService = PowerMockito.mock(TreePathService)
        Whitebox.setInternalState(validator, "treePathService", treePathService)
        PowerMockito.when(treePathService.isLoop(any(), any(), any(), any(), any())).thenReturn(true)

        when:
        validator.validate(validatorContext)

        then:
        def exception = thrown(ValidateException)
        exception.getMessage() == "所选上级已为其子级，不可让数据成环，请重新选择上级数据"

    }


    def "incentive category invalid validator"() {
        given:
        def user = User.systemUser("82681")
        def categoryDataList = [new ObjectData(["_id": "1"])]
        def validator = new IncentiveCategoryInvalidValidator()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(validator, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(null).when(serviceFacade, "findBySearchQuery", any(User) as User, any(String), any())

        when:
        validator.ValidatorUsed(user, categoryDataList)

        then:
        noExceptionThrown()
    }
    def "incentive category invalid validator with existing data"() {
        given:
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.when(I18N.text(anyString())).thenReturn("分类正在被激励政策使用，无法删除")
        def user = User.systemUser("82681")
        def categoryDataList = [new ObjectData(["_id": "1"])]
        def validator = new IncentiveCategoryInvalidValidator()
        def serviceFacade = PowerMockito.mock(ServiceFacade.class) // Ensure .class is used
        Whitebox.setInternalState(validator, "serviceFacade", serviceFacade)

        // Mock返回非空数据来触发验证异常
        def queryResult = new QueryResult()
        queryResult.setData([new ObjectData(["_id": "policy1"])])
        // **** MODIFIED LINE ****
        PowerMockito.doReturn(queryResult).when(serviceFacade).findBySearchQuery(any(User) as User, anyString(), any(SearchTemplateQuery) as SearchTemplateQuery)

        when:
        validator.ValidatorUsed(user, categoryDataList)

        then:
        def exception = thrown(ValidateException)
        exception.getMessage() == "分类正在被激励政策使用，无法删除"
    }

    def "incentive category invalid validator with empty data"() {
        given:
        def user = User.systemUser("82681")
        def categoryDataList = []
        def validator = new IncentiveCategoryInvalidValidator()

        when:
        validator.ValidatorUsed(user, categoryDataList)

        then:
        noExceptionThrown()
    }

    def "incentive category invalid validator with null data"() {
        given:
        def user = User.systemUser("82681")
        def categoryDataList = null
        def validator = new IncentiveCategoryInvalidValidator()

        when:
        validator.ValidatorUsed(user, categoryDataList)

        then:
        noExceptionThrown()
    }

    def "incentive category validator"() {
        given:
        def objectData = new ObjectData()
        objectData.set("_id", "id")
        objectData.set("program_id", "program_id")
        objectData.set("parent_id", "parent_id")
        objectData.set("name", "name")
        def user = User.systemUser("82681")
        def validatorContext = ValidatorContext.builder().objectData(objectData).user(user).build()
        def validator = new IncentiveCategoryValidator()
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        def treePathService = PowerMockito.mock(TreePathService)
        Whitebox.setInternalState(validator, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(validator, "treePathService", treePathService)
        PowerMockito.doReturn([new ObjectData("program_id": "program_id")]).when(serviceFacade, "findObjectDataByIdsIgnoreAll", any(String), any(), any())
        def result = new QueryResult()
        result.setData(Lists.newArrayList())
        PowerMockito.doReturn(result).when(serviceFacade).findBySearchQuery(any(User) as User, anyString(), any(SearchTemplateQuery) as SearchTemplateQuery)
        PowerMockito.when(treePathService.currentTreeDepth(any(), any(), any(), any())).thenReturn(1)

        when:
        validator.validate(validatorContext)

        then:
        noExceptionThrown()

        when:
        objectData.remove("parent_id")
        validator.validate(validatorContext)

        then:
        noExceptionThrown()

    }
}
