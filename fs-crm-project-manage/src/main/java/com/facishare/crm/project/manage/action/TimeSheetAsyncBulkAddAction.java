package com.facishare.crm.project.manage.action;

import com.facishare.crm.project.manage.ProjectManagePredefineObject;
import com.facishare.crm.project.manage.utils.CommonUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkCreateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_ADDITIONAL_REQUIRED_FIELDS;

@Slf4j
public class TimeSheetAsyncBulkAddAction extends AbstractStandardAsyncBulkAction<StandardBulkCreateAction.Arg, BaseObjectSaveAction.Arg> {

    private static final List<String> exclusionFields = Lists.newArrayList("name", "start_date", "end_date", "time_period", "task_id", "project_id", "billing_type", "owner", "lock_status", "record_type", "life_status", "owner_department", "out_owner", "last_modified_time", "create_time", "last_modified_by", "created_by", "data_own_department", "object_describe_api_name", "tenant_id" );



    @Override
    protected void before(StandardBulkCreateAction.Arg arg) {
        validateRequiredFields();
        CommonUtils.validateSize(arg.getDataList(), 50);
        CommonUtils.batchFillDataId(arg.getDataList());
        super.before(arg);
    }

    private void validateRequiredFields() {
        IObjectDescribe describe = serviceFacade.findObject(actionContext.getTenantId(), ProjectManagePredefineObject.TimeSheet.getApiName());
        Optional<IFieldDescribe> requiredField = describe.getFieldDescribes().stream().filter(f -> f.isRequired() && !exclusionFields.contains(f.getApiName())).findFirst();
        if (requiredField.isPresent()) {
            log.warn("TimeSheetAsyncBulkAddAction validateRequiredFields requiredField:{}", requiredField.get().getApiName());
            throw new ValidateException(I18N.text(SFA_ADDITIONAL_REQUIRED_FIELDS));
        }
    }

    @Override
    protected String getDataIdByParam(BaseObjectSaveAction.Arg param) {
        return param.getObjectData().getId();
    }

    @Override
    protected List<BaseObjectSaveAction.Arg> getButtonParams() {
        return arg.getDataList().stream()
                .map(data -> {
                    BaseObjectSaveAction.Arg addArg = new BaseObjectSaveAction.Arg();
                    addArg.setObjectData(data);
                    return addArg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CREATE.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.CREATE.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.CREATE.getActionCode());
    }






}