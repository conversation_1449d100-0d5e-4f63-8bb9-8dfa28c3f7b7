package com.facishare.crm.management.service.config;

import com.facishare.crm.management.service.ProfileAdviceProducer;
import com.facishare.crm.management.service.model.ProfileAdviceMqModel;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 配置 是否开启非标属性
 *
 * <AUTHOR>
 * @date 2019/3/25
 */
@Component
@Slf4j
public class OpenCustomerProfileAgentBizConfigProvider extends DefaultBizConfigProvider {
    @Autowired
    ProfileAdviceProducer profileAdviceProducer;

    private static final String BIZ = "feature-init-data";

    @Override
    public String getConfigKey() {
        return BizConfigKey.CUSTOMER_PROFILE_AGENT.getId();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        ProfileAdviceMqModel.Message profileAdviceMqModel = ProfileAdviceMqModel.Message.builder()
                .tenantId(user.getTenantId())
                .tags(BIZ)
                .receiverId(user.getUserId())
                .build();
        profileAdviceProducer.sendMessage(profileAdviceMqModel);
        super.setConfigValue(user, value, oldValue, key);
    }
}
