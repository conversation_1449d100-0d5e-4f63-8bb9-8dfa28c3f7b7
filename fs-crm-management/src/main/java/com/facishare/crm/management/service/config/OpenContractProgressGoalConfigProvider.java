package com.facishare.crm.management.service.config;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.utilities.constant.ContractProgressConstants;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_SALE_CONTRACT_NOT_OPEN_CONTRACT_MODE;

/**
 * 销售合同履约进度管理配置提供者
 */
@Slf4j
@Component
public class OpenContractProgressGoalConfigProvider extends DefaultBizConfigProvider {

    @Autowired
    private LicenseService licenseService;

    @Autowired
    EnterpriseInitService enterpriseInitService;



    private static final List<String> initObjectDescribeApiNames = Lists.newArrayList(ContractProgressConstants.Rule.DESC_API_NAME,
            ContractProgressConstants.RuleGoal.DESC_API_NAME,
            ContractProgressConstants.RuleGoalCheck.DESC_API_NAME,
            ContractProgressConstants.RuleGoalSnapshot.DESC_API_NAME);

    @Override
    public String getConfigKey() {
        return ConfigType.IS_OPEN_CONTRACT_PROGRESS.getKey();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        //不重复开启，直接返回
        if (Objects.equals("1", oldValue) && Objects.equals("1", value)) {
            return;
        }

        //已开启则不允许关闭
        if ("1".equals(oldValue) && "0".equals(value)) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.OPENED_NOT_ALLOW_CLOSE));
        }

        Map<String, IObjectDescribe> describeMap = getDescribeWithSimplifiedChineseByApiNames(User.systemUser(user.getTenantId()), initObjectDescribeApiNames);
        initObjectDescribeApiNames.stream().forEach(describeApiName -> {
            IObjectDescribe describe = describeMap.get(describeApiName);
            if (describe == null) {
                enterpriseInitService.initDescribeForTenant(user.getTenantId(), describeApiName);
                //销售合同对象刷 "目标设置" "查看进度" 两个按钮
                serviceFacade.batchCreateFunc(user, Utils.SALE_CONTRACT_API_NAME, Lists.newArrayList("SetGoal", "ViewGoal"));
                serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, Utils.SALE_CONTRACT_API_NAME, Lists.newArrayList("SetGoal", "ViewGoal"), Lists.newArrayList());
            }
        });
        super.setConfigValue(user, value, oldValue, key);
    }

    @Override
    public void validateSetConfig(User user, String key, String new_value) {
        //License判断
        if(!Objects.equals("1", getConfigValue(user, ConfigType.MODULE_SALE_CONTRACT.getKey()))) {
            throw  new ValidateException(I18N.text(SFA_SALE_CONTRACT_NOT_OPEN_CONTRACT_MODE));
        }
        Set<String> module = licenseService.getModule(user.getTenantId());
        if (CollectionUtils.empty(module) || !module.contains("contract_progress_management_app")) {
            throw new ValidateException(I18N.text("sfa.config.license.not.open.contract_progress_management_app"));
        }
    }
}