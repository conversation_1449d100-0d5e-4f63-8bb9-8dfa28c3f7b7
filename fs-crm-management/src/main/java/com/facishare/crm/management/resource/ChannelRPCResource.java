package com.facishare.crm.management.resource;

import com.facishare.crm.enums.BizScopeEnums;
import com.facishare.crm.enums.ReminderTrigger;
import com.facishare.crm.management.service.ChannelServiceProvider;
import com.facishare.crm.model.ChannelManagementDTO;
import com.facishare.crm.model.ChannelRpcModel;
import com.facishare.crm.model.PartnerChannelManage;
import com.facishare.crm.sfa.model.ChannelServiceModel;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024-06-29 14:50
 * @Description caller：fs-crm-task-sfa
 */
@Slf4j
@Component
@ServiceModule("channel_rpc")
public class ChannelRPCResource {
    @Resource
    private ChannelServiceProvider channelServiceProvider;

    @ServiceMethod("query_match_enterprise_setting")
    public PartnerChannelManage.EnterpriseActivationSetting queryMatchEnterpriseSetting(ServiceContext serviceContext, ChannelRpcModel.SettingArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getDataId())) {
            log.warn("PartnerChannelRPCResource#queryMatchEnterpriseSetting arg is null or empty, tenant:{}", serviceContext.getTenantId());
            return PartnerChannelManage.EnterpriseActivationSetting.builder().effective(false).build();
        }
        User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        return channelServiceProvider.queryMatchEnterpriseSetting(systemUser, arg.getObjectApiName(), arg.getDataId());
    }

    @ServiceMethod("query_approval_notice")
    public List<ChannelRpcModel.ApprovalNoticeAggregator> queryApprovalNotice(ServiceContext serviceContext, ChannelRpcModel.ApprovalNoticeArg arg) {
        BizScopeEnums bizScope = BizScopeEnums.fromString(arg.getBizScope());
        try {
            User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            return channelServiceProvider.queryEnableApprovalNotice(systemUser, bizScope);
        } catch (Exception e) {
            log.error("PartnerChannelRPCResource#queryApprovalNotice error, tenant:{}", serviceContext.getTenantId(), e);
        }
        return Lists.newArrayList();
    }

    @ServiceMethod("query_activation_setting")
    public PartnerChannelManage.EnterpriseActivationSetting queryActivationSetting(ServiceContext serviceContext, ChannelRpcModel.SettingArg arg) {
        try {
            User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            return channelServiceProvider.queryActivationSetting(systemUser, arg.getDataId());
        } catch (Exception e) {
            log.error("PartnerChannelRPCResource#queryApprovalNotice error, tenant:{}", serviceContext.getTenantId(), e);
        }
        return PartnerChannelManage.EnterpriseActivationSetting.builder().effective(false).build();
    }

    @ServiceMethod("definition_channel_work_flow")
    public Boolean definitionChannelWorkFlow(ServiceContext serviceContext) {
        try {
            User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            return channelServiceProvider.definitionChannelWorkFlow(systemUser);
        } catch (Exception e) {
            log.warn("PartnerChannelRPCResource#definitionChannelWorkFlow error, tenant:{}", serviceContext.getTenantId(), e);
        }
        return false;
    }

    @ServiceMethod("match_sign_scheme")
    public ChannelRpcModel.ApprovalNoticeAggregatorResult matchSignScheme(ServiceContext serviceContext, ChannelRpcModel.SettingArg arg) {
        try {
            User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            return channelServiceProvider.matchSignSchemeWithAllInfo(systemUser, arg.getDataId());
        } catch (Exception e) {
            log.error("PartnerChannelRPCResource#match_sign_scheme error, tenant:{}", serviceContext.getTenantId(), e);
        }
        return ChannelRpcModel.ApprovalNoticeAggregatorResult.builder().effective(false).build();
    }

    @ServiceMethod("query_sign_reminder_info")
    public ChannelRpcModel.SignReminderInfoResult querySignReminderInfo(ServiceContext serviceContext, ChannelRpcModel.SettingArg arg) {
        try {
            User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            ReminderTrigger reminderTrigger = ReminderTrigger.from(arg.getReminderTrigger());
            return channelServiceProvider.querySignReminderInfo(systemUser, arg.getDataId(), reminderTrigger);
        } catch (Exception e) {
            log.error("PartnerChannelRPCResource#querySignReminderInfo error, tenant:{}", serviceContext.getTenantId(), e);
        }
        return ChannelRpcModel.SignReminderInfoResult.builder().effective(false).build();
    }

    @ServiceMethod("query_sign_email_sender")
    public String querySignEmailSender(ServiceContext serviceContext) {
        try {
            User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            return channelServiceProvider.querySignEmailSender(systemUser);
        } catch (Exception e) {
            log.error("PartnerChannelRPCResource#queryEmailSender error, tenant:{}", serviceContext.getTenantId(), e);
        }
        throw new ValidateException("No available email");
    }

    @ServiceMethod("prm_alter_reminder_trigger")
    public Boolean prmAlterReminderTrigger(ServiceContext serviceContext, ChannelRpcModel.ReminderCacheArg reminderCacheArg) {
        try {
            User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            return channelServiceProvider.prmAlterReminderTrigger(systemUser, reminderCacheArg.getReminderCacheList());
        } catch (Exception e) {
            log.error("PartnerChannelRPCResource#prmAlterReminderTrigger error, tenant:{}", serviceContext.getTenantId(), e);
        }
        return Boolean.FALSE;
    }

    @ServiceMethod("clear_alter_reminder_trigger")
    public Boolean clearAlterReminderTrigger(ServiceContext serviceContext, ChannelManagementDTO.ReminderCache reminderCache) {
        try {
            User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            return channelServiceProvider.clearAlterReminderTrigger(systemUser, reminderCache.getOutTenantId());
        } catch (Exception e) {
            log.error("PartnerChannelRPCResource#prmAlterReminderTrigger error, tenant:{}", serviceContext.getTenantId(), e);
        }
        return Boolean.FALSE;
    }

    @ServiceMethod("match_simple_sign_scheme")
    public ChannelServiceModel.MatchScheme matchSimpleSignScheme(ServiceContext serviceContext, ChannelRpcModel.SettingArg arg) {
        try {
            User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            return channelServiceProvider.matchSimpleSignScheme(systemUser, arg.getDataId());
        } catch (Exception e) {
            log.error("PartnerChannelRPCResource#prmAlterReminderTrigger error, tenant:{}", serviceContext.getTenantId(), e);
        }
        return ChannelServiceModel.MatchScheme.builder().build();
    }

    @ServiceMethod("match_sign_scheme_info")
    public ChannelServiceModel.MatchScheme matchSignSchemeInfo(ServiceContext serviceContext, ChannelRpcModel.SettingArg arg) {
        try {
            User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            return channelServiceProvider.matchSignSchemeInfo(systemUser, arg.getDataId());
        } catch (Exception e) {
            log.error("PartnerChannelRPCResource#match_sign_scheme_info error, tenant:{}", serviceContext.getTenantId(), e);
        }
        return ChannelServiceModel.MatchScheme.builder().build();
    }

    @ServiceMethod("create_link_contact")
    public PartnerChannelManage.CreateDataResult createLinkContact(ServiceContext context, ChannelRpcModel.CreateLinkContactArg arg) {
        PartnerChannelManage.CreateDataResult rst = new PartnerChannelManage.CreateDataResult();
        if (StringUtils.isBlank(arg.getDataId())) {
            rst.setError("data id is null");
            return rst;
        }
        try {
            BaseObjectSaveAction.Result contactData = channelServiceProvider.createContactByPartner(context.getUser(), arg.getObjectApiName(), arg.getDataId());
            if (contactData != null && contactData.getObjectData() != null) {
                rst.setDataId(contactData.getObjectData().getId());
            }
        } catch (AppBusinessException appEx) {
            log.warn("createLinkContact ValidateException, tenant:{}", context.getTenantId(), appEx);
            rst.setError(appEx.getMessage());
        } catch (Exception ex) {
            rst.setError("system error");
            log.warn("createLinkContact Error, tenant:{}", context.getTenantId(), ex);
        }
        return rst;
    }
}
