package com.facishare.crm.management.service;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.enums.*;
import com.facishare.crm.management.service.access.*;
import com.facishare.crm.model.*;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.rest.ApprovalInitProxy;
import com.facishare.crm.rest.dto.ApprovalInitModel;
import com.facishare.crm.sfa.model.ChannelFlowInitVO;
import com.facishare.crm.sfa.model.ChannelServiceModel;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.ChannelCacheService;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.channel.ChannelAdmissionService;
import com.facishare.crm.sfa.prm.api.dto.AdmissionConfigDTO;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.crm.util.RestUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.license.LicenseServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.metadata.api.*;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.facishare.crm.model.PartnerChannelConfigModel.*;

/**
 * <AUTHOR>
 * @time 2024-06-29 11:56
 * @Description
 */
@Service
@Slf4j
public class ChannelServiceProvider implements ChannelService {
    @Resource
    private PaasRuleEngineService paasRuleEngineService;
    @Resource
    private PartnerChannelService partnerChannelService;
    @Resource
    private ChannelFunctionExecutor channelFunctionExecutor;
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private PrmSmsAccess prmSmsAccess;
    @Resource
    private PrmEmailAccess prmEmailAccess;
    @Resource
    private ProvisionSchemeAccess provisionSchemeAccess;
    @Resource
    private SignSchemeAccess signSchemeAccess;
    @Resource
    private ApprovalInitProxy approvalInitProxy;
    @Resource
    private LicenseServiceImpl licenseService;
    @Resource
    private Converter converter;
    @Resource
    private ChannelCacheService channelCacheService;
    @Resource
    private PartnerChannelConfigAccess channelConfigAccess;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ObjectMappingService objectMappingService;
    @Resource
    private ChannelAdmissionService channelAdmissionService;

    public static final Set<String> PARTNER_CHANNEL_LICENSE_SET = ImmutableSet.of("channel_partner_recruitment_app", "channel_partner_recruitment_industry");


    @Override
    public ChannelServiceModel.@NotNull MatchScheme matchPriorityLayoutApiName(User user, String admissionApiName, IObjectData admissionData) {
        ChannelServiceModel.MatchScheme matchScheme = ChannelServiceModel.MatchScheme.builder().build();
        AtomicReference<ChannelServiceModel.MatchScheme> matchAtomic = new AtomicReference<>(matchScheme);
        List<PartnerChannelManage.QualificationScheme> qualificationSchemes = partnerChannelService.queryQualificationScheme(user);
        if (CollectionUtils.isEmpty(qualificationSchemes)) {
            return matchAtomic.get();
        }
        List<String> qualificationSchemeIds = qualificationSchemes.stream()
                .filter(q -> q.getConditionTypeEnum() == ConditionType.CONDITION)
                .map(PartnerChannelManage.QualificationScheme::getQualificationSchemeId)
                .collect(Collectors.toList());
        List<String> matchRuleCodes = paasRuleEngineService.findDataMatchRuleCodes(user, RuleEngineSceneEnums.CHANNEL_LAYOUT,
                admissionApiName, admissionData, qualificationSchemeIds);
        if (CollectionUtils.isEmpty(matchRuleCodes)) {
            qualificationSchemes.stream().filter(x -> x.getConditionTypeEnum() == ConditionType.ALL)
                    .min(Comparator.comparingInt(PartnerChannelManage.QualificationScheme::getPriority)).ifPresent(qualificationScheme -> matchAtomic.set(buildMatchScheme(qualificationScheme)));
            return matchAtomic.get();
        }
        qualificationSchemes.stream()
                .filter(q -> matchRuleCodes.contains(q.getQualificationSchemeId()))
                .min(Comparator.comparingInt(PartnerChannelManage.QualificationScheme::getPriority))
                .ifPresent(qualificationScheme -> fetchHighestPriority(user, admissionApiName, admissionData.getId(), qualificationScheme, matchAtomic, qualificationSchemes));
        return matchAtomic.get();
    }

    public @Nullable PartnerChannelManage.EnterpriseActivationSetting queryMatchEnterpriseSetting(User user, String objectApiName, String dataId) {
        PartnerChannelManage.RegistrationApprovalResult result = partnerChannelService.queryActivationSettings(user);
        List<PartnerChannelManage.EnterpriseActivationSetting> enterpriseActivationSettings = result.getEnterpriseActivationSettings();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(enterpriseActivationSettings)) {
            return PartnerChannelManage.EnterpriseActivationSetting.builder().effective(false).build();
        }
        ConditionType conditionType = ConditionType.fromString(result.getConditionType());
        if (conditionType == ConditionType.ALL) {
            return enterpriseActivationSettings.get(0);
        }
        List<String> settingIds = enterpriseActivationSettings.stream().map(PartnerChannelManage.EnterpriseActivationSetting::getEnterpriseActivationSettingId).collect(Collectors.toList());
        IObjectData data = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, dataId, objectApiName);
        if (data == null) {
            return PartnerChannelManage.EnterpriseActivationSetting.builder().effective(false).build();
        }
        AtomicReference<PartnerChannelManage.EnterpriseActivationSetting> rst = new AtomicReference<>(PartnerChannelManage.EnterpriseActivationSetting.builder().effective(false).build());
        List<String> dataMatchRuleCodes = paasRuleEngineService.findDataMatchRuleCodes(user, RuleEngineSceneEnums.CHANNEL_APPROVAL, objectApiName, data, settingIds);
        enterpriseActivationSettings
                .stream()
                .filter(d -> dataMatchRuleCodes.contains(d.getEnterpriseActivationSettingId()))
                .min(Comparator.comparingInt(PartnerChannelManage.EnterpriseActivationSetting::getPriority))
                .ifPresent(rst::set);
        return rst.get();
    }

    public List<ChannelRpcModel.ApprovalNoticeAggregator> queryEnableApprovalNotice(User user, BizScopeEnums bizScope) {
        List<PartnerChannelManage.ApprovalNotice> approvalNotices = partnerChannelService.queryNoticeByBizScope(user, bizScope);
        return buildApprovalNoticeAggregators(user, approvalNotices);
    }

    public List<ChannelRpcModel.ApprovalNoticeAggregator> buildApprovalNoticeAggregators(User user, List<PartnerChannelManage.ApprovalNotice> approvalNotices) {
        if (CollectionUtils.isEmpty(approvalNotices)) {
            return Lists.newArrayList();
        }
        List<ChannelRpcModel.ApprovalNoticeAggregator> approvalNoticeAggregators = Lists.newArrayList();
        approvalNotices.stream().filter(a -> Boolean.TRUE.equals(a.getEnabled())).forEach(approvalNotice -> {
            ChannelRpcModel.ApprovalNoticeAggregator aggregator = buildNoticeAggregator(user, approvalNotice);
            approvalNoticeAggregators.add(aggregator);
        });
        return approvalNoticeAggregators;
    }

    public ChannelRpcModel.ApprovalNoticeAggregator buildNoticeAggregator(User user, PartnerChannelManage.ApprovalNotice approvalNotice) {
        NotifyViaEnums thisNotifyVia = NotifyViaEnums.of(approvalNotice.getNotifyVia());
        ChannelRpcModel.ApprovalNoticeAggregator aggregator = new ChannelRpcModel.ApprovalNoticeAggregator();
        aggregator.setApprovalNoticeId(approvalNotice.getApprovalNoticeId());
        aggregator.setSender(approvalNotice.getSender());
        aggregator.setReceiver(approvalNotice.getReceiver());
        aggregator.setNotifyVia(approvalNotice.getNotifyVia());
        aggregator.setAplApiName(approvalNotice.getAplApiName());
        if (StringUtils.isNotBlank(approvalNotice.getAplApiName())) {
            return aggregator;
        }
        // 非 APL 提醒
        List<String> noticeIds = approvalNotice.getNoticeIds();
        if (NotifyViaEnums.EMAIL == thisNotifyVia) {
            List<IObjectData> emailDataList = prmEmailAccess.queryEmailByIds(user, noticeIds);
            for (IObjectData emailData : emailDataList) {
                PartnerChannelManage.PrmEmail emailMessage = partnerChannelService.buildEmailMessage(emailData);
                String category = emailMessage.getCategory();
                NoticeInstanceCategoryEnums instanceCategory = NoticeInstanceCategoryEnums.fromString(category);
                if (instanceCategory == NoticeInstanceCategoryEnums.PASS) {
                    aggregator.setPassEmail(emailMessage);
                } else {
                    aggregator.setNonPassEmail(emailMessage);
                }
            }
        } else if (NotifyViaEnums.SMS == thisNotifyVia) {
            List<IObjectData> SmsDataList = prmSmsAccess.querySmsByIds(user, noticeIds);
            for (IObjectData smsData : SmsDataList) {
                PrmManagementModel.ShortMessage shortMessage = partnerChannelService.buildShortMessage(smsData);
                String category = shortMessage.getCategory();
                NoticeInstanceCategoryEnums instanceCategory = NoticeInstanceCategoryEnums.fromString(category);
                if (instanceCategory == NoticeInstanceCategoryEnums.PASS) {
                    aggregator.setPassSms(shortMessage);
                } else {
                    aggregator.setNonPassSms(shortMessage);
                }
            }
        }
        return aggregator;
    }

    public List<String> getOccupiedPartnerProvisionIds(User user) {
        List<IObjectData> allProvisionData = provisionSchemeAccess.queryAllProvisionScheme(user);
        if (CollectionUtils.isEmpty(allProvisionData)) {
            return Lists.newArrayList();
        }
        return allProvisionData.stream()
                .map(data -> data.get(ProvisionSchemeModel.PROVISION_IDS, List.class, Lists.newArrayList()))
                .filter(Objects::nonNull)
                .flatMap(obj -> ((List<?>) obj).stream())
                .filter(item -> item instanceof String)
                .map(item -> (String) item)
                .collect(Collectors.toList());
    }

    public List<String> getOccupiedPartnerAgreementIds(User user) {
        List<IObjectData> allSignData = signSchemeAccess.queryAllSignScheme(user);
        if (CollectionUtils.isEmpty(allSignData)) {
            return Lists.newArrayList();
        }
        return allSignData.stream()
                .map(data -> data.get(SignSchemeModel.AGREEMENT_ID, String.class, ""))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    public ChannelServiceModel.MatchScheme matchSignScheme(User user, String admissionObject, IObjectData admissionData) {
        List<PartnerChannelManage.SignScheme> signSchemes;
        signSchemes = partnerChannelService.queryPushedSignSchemeList(user);
        return matchSignScheme(user, admissionObject, admissionData, signSchemes);
    }

    @Override
    public PartnerChannelManage.SignScheme matchSignSchemeDate(User user, String admissionObject, IObjectData admissionData) {
        List<PartnerChannelManage.SignScheme> signSchemes;
        signSchemes = partnerChannelService.queryPushedSignSchemeList(user);
        ChannelServiceModel.MatchScheme matchScheme = matchSignScheme(user, admissionObject, admissionData, signSchemes);
        if (StringUtils.isBlank(matchScheme.getSchemeId())) {
            return null;
        }
        AtomicReference<PartnerChannelManage.SignScheme> dataAtomic = new AtomicReference<>(null);
        signSchemes.stream().filter(x -> matchScheme.getSchemeId().equals(x.getSignSchemeId())).findFirst().ifPresent(dataAtomic::set);
        return dataAtomic.get();
    }

    private ChannelServiceModel.MatchScheme matchSignScheme(User user, String admissionObject, IObjectData admissionData, List<PartnerChannelManage.SignScheme> signSchemes) {
        ChannelServiceModel.MatchScheme matchScheme = ChannelServiceModel.MatchScheme.builder().build();
        AtomicReference<ChannelServiceModel.MatchScheme> matchAtomic = new AtomicReference<>(matchScheme);
        if (CollectionUtils.isEmpty(signSchemes)) {
            return matchAtomic.get();
        }
        List<String> signIds = signSchemes.stream().map(PartnerChannelManage.SignScheme::getSignSchemeId).collect(Collectors.toList());
        List<String> matchRuleCodes = paasRuleEngineService.findDataMatchRuleCodes(user, RuleEngineSceneEnums.CHANNEL_AGREEMENT, admissionObject, admissionData, signIds);
        if (CollectionUtils.isEmpty(matchRuleCodes)) {
            signSchemes.stream().filter(x -> x.getConditionTypeEnum() == ConditionType.ALL)
                    .max(Comparator.comparingInt(PartnerChannelManage.SignScheme::getPriority)).ifPresent(signScheme -> matchAtomic.set(buildMatchScheme(signScheme)));
            return matchAtomic.get();
        }
        signSchemes.stream()
                .filter(scheme -> matchRuleCodes.contains(scheme.getSignSchemeId()))
                .max(Comparator.comparingInt(PartnerChannelManage.SignScheme::getPriority))
                .ifPresent(scheme -> fetchHighestPriorityForSign(user, admissionObject, admissionData.getId(), scheme, matchAtomic, signSchemes));
        return matchAtomic.get();
    }

    private void fetchHighestPriority(User user, String admissionApiName, String admissionDataId, MatchRulePriority priorityScheme, AtomicReference<ChannelServiceModel.MatchScheme> matchAtomic, List<? extends MatchRulePriority> schemes) {
        MatchRulePriority matchRuleData = getMatchRuleData(user, admissionApiName, admissionDataId, priorityScheme, schemes);
        ChannelServiceModel.MatchScheme matchScheme = buildMatchScheme(matchRuleData);
        matchAtomic.set(matchScheme);
    }

    private void fetchHighestPriorityForSign(User user, String admissionObject, String admissionDataId, MatchRulePriority priorityScheme, AtomicReference<ChannelServiceModel.MatchScheme> matchAtomic, List<? extends MatchRulePriority> schemes) {
        MatchRulePriority matchRuleData = getMatchRuleDataForSign(user, admissionObject, admissionDataId, priorityScheme, schemes);
        ChannelServiceModel.MatchScheme matchScheme = buildMatchScheme(matchRuleData);
        matchAtomic.set(matchScheme);
    }

    private MatchRulePriority getMatchRuleDataForSign(User user, String admissionObject, String admissionDataId, MatchRulePriority priorityScheme, List<? extends MatchRulePriority> allRuleDataList) {
        for (MatchRulePriority ruleData : allRuleDataList) {
            if (ruleData.getMatchSchemeId().equals(priorityScheme.getMatchSchemeId())) {
                return priorityScheme;
            }
            if (ruleData.getConditionTypeEnum() == ConditionType.ALL && ruleData.getPriority() > priorityScheme.getPriority()) {
                return ruleData;
            }
            if (ruleData.getConditionTypeEnum() == ConditionType.APL && ruleData.getPriority() > priorityScheme.getPriority() &&
                    StringUtils.isBlank(ruleData.getAplApiName())) {
                List<String> matchPartnerIds = channelFunctionExecutor.executeLayoutFunction(user, ruleData.getAplApiName(), admissionObject, admissionDataId);
                if (matchPartnerIds.contains(admissionDataId)) {
                    return ruleData;
                }
            }
        }
        return priorityScheme;
    }

    private ChannelServiceModel.MatchScheme buildMatchScheme(MatchRulePriority matchRuleData) {
        return ChannelServiceModel.MatchScheme.builder()
                .schemeId(matchRuleData.getMatchSchemeId())
                .match(matchRuleData.getMatch())
                .matchList(matchRuleData.getMatchList())
                .build();
    }

    private MatchRulePriority getMatchRuleData(User user, String admissionApiName, String admissionDataId, MatchRulePriority priorityScheme, List<? extends MatchRulePriority> allRuleDataList) {
        for (MatchRulePriority ruleData : allRuleDataList) {
            if (ruleData.getMatchSchemeId().equals(priorityScheme.getMatchSchemeId())) {
                return priorityScheme;
            }
            if (ruleData.getConditionTypeEnum() == ConditionType.ALL && ruleData.getPriority() <= priorityScheme.getPriority()) {
                return ruleData;
            }
            if (ruleData.getConditionTypeEnum() == ConditionType.APL && ruleData.getPriority() <= priorityScheme.getPriority() &&
                    StringUtils.isBlank(ruleData.getAplApiName())) {
                List<String> matchPartnerIds = channelFunctionExecutor.executeLayoutFunction(user, ruleData.getAplApiName(), admissionApiName, admissionDataId);
                if (matchPartnerIds.contains(admissionDataId)) {
                    return ruleData;
                }
            }
        }
        return priorityScheme;
    }

    @Override
    public @NotNull ChannelServiceModel.MatchScheme matchProvisionScheme(User user, IObjectData admissionData, String admissionObject) {
        ChannelServiceModel.MatchScheme matchScheme = ChannelServiceModel.MatchScheme.builder().build();
        AtomicReference<ChannelServiceModel.MatchScheme> matchAtomic = new AtomicReference<>(matchScheme);
        List<PartnerChannelManage.ProvisionScheme> provisionSchemes = partnerChannelService.queryProvisionScheme(user);
        if (CollectionUtils.isEmpty(provisionSchemes)) {
            return matchAtomic.get();
        }
        List<String> schemeIds = provisionSchemes.stream().map(PartnerChannelManage.ProvisionScheme::getProvisionSchemeId).collect(Collectors.toList());
        List<String> matchRuleCodes = paasRuleEngineService.findDataMatchRuleCodes(user, RuleEngineSceneEnums.CHANNEL_PROVISION, admissionObject, admissionData, schemeIds);
        if (CollectionUtils.isEmpty(matchRuleCodes)) {
            provisionSchemes.stream().filter(x -> x.getConditionTypeEnum() == ConditionType.ALL)
                    .min(Comparator.comparingInt(PartnerChannelManage.ProvisionScheme::getPriority)).ifPresent(provisionScheme -> matchAtomic.set(buildMatchScheme(provisionScheme)));
            return matchAtomic.get();
        }
        provisionSchemes.stream()
                .filter(scheme -> matchRuleCodes.contains(scheme.getProvisionSchemeId()))
                .min(Comparator.comparingInt(PartnerChannelManage.ProvisionScheme::getPriority))
                .ifPresent(sign -> fetchHighestPriority(user, admissionObject, admissionData.getId(), sign, matchAtomic, provisionSchemes));
        return matchAtomic.get();
    }

    @Override
    public String queryAgreementTemplateById(User user, String partnerAgreementId) {
        IObjectData data = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, partnerAgreementId, SFAPreDefineObject.PartnerAgreement.getApiName());
        if (data == null) {
            return null;
        }
        return ObjectDataUtils.getValueOrDefault(data, "print_template_id", "");
    }

    @Override
    public PartnerChannelManage.SignScheme querySignSchemeById(User user, String signSchemeId) {
        return partnerChannelService.querySignSchemeById(user, signSchemeId);
    }

    @Override
    public IObjectData queryAgreementDataById(User user, String partnerAgreementId) {
        return metaDataFindServiceExt.findObjectByIdIgnoreAll(user, partnerAgreementId, SFAPreDefineObject.PartnerAgreement.getApiName());
    }

    @Override
    public void executeLayoutFunction(User user, String aplApiName, String admissionObject, String admissionDataId) {
        if (StringUtils.isAnyBlank(aplApiName, admissionDataId)) {
            return;
        }
        channelFunctionExecutor.executeLayoutFunction(user, aplApiName, admissionObject, admissionDataId);
    }

    @Override
    public boolean notExistsModule(User user) {
        Map<String, Boolean> booleanMapping = licenseService.existModule(user.getTenantId(), PARTNER_CHANNEL_LICENSE_SET);
        if (booleanMapping == null || booleanMapping.isEmpty()) {
            return true;
        }
        for (String module : PARTNER_CHANNEL_LICENSE_SET) {
            if (Boolean.TRUE.equals(booleanMapping.get(module))) {
                return false;
            }
        }
        return true;
    }

    @Override
    public ChannelManagementDTO.MatchSignSchemeWithPrmReminder matchSignSchemeWithReminderInfo(User user, IObjectData admissionData, String admissionObject) {
        ChannelServiceModel.MatchScheme matchScheme = matchSignScheme(user, admissionObject, admissionData);
        if (!matchScheme.matchSchemeSuccess()) {
            return null;
        }
        String signSchemeId = matchScheme.getSchemeId();
        PartnerChannelManage.SignScheme signScheme = queryAssembledSignSchemeById(user, signSchemeId);
        if (signScheme == null) {
            return null;
        }
        ChannelManagementDTO.MatchSignSchemeWithPrmReminder matchSignSchemeWithPrmReminder = new ChannelManagementDTO.MatchSignSchemeWithPrmReminder();
        matchSignSchemeWithPrmReminder.setSignSchemeId(signSchemeId);
        ReminderTrigger reminderTrigger = ReminderTrigger.find(signScheme.getReminderTrigger(), null);
        if (reminderTrigger == null) {
            return null;
        }
        PartnerChannelManage.ExpireReminderType expireReminderType;
        if (ReminderTrigger.AUTO == reminderTrigger) {
            expireReminderType = signScheme.getExpireReminderTypeView().getAutoExpireReminderType();
        } else {
            expireReminderType = signScheme.getExpireReminderTypeView().getManualExpireReminderType();
        }
        matchSignSchemeWithPrmReminder.setReminderTrigger(reminderTrigger);
        ChannelManagementDTO.ReminderType reminderType = converter.convertDTO(expireReminderType.getPrmAlertWindowReminder(), ChannelManagementDTO.ReminderType.class);
        matchSignSchemeWithPrmReminder.setPrmAlertWindowReminder(reminderType);
        List<ChannelManagementDTO.ReminderPerson> externalPersonList = Lists.newArrayList();
        for (PartnerChannelManage.ReminderPerson externalPerson : signScheme.getExpireReminderPersonView().getExternalPerson()) {
            ChannelManagementDTO.ReminderPerson person = converter.convertDTO(externalPerson, ChannelManagementDTO.ReminderPerson.class);
            externalPersonList.add(person);

        }
        matchSignSchemeWithPrmReminder.setExternalPerson(externalPersonList);
        return matchSignSchemeWithPrmReminder;
    }

    @Override
    public String querySignEmailSender(User user) {
        List<PartnerChannelManage.ApprovalNotice> approvalNotices = partnerChannelService.queryNoticeByBizScope(user, BizScopeEnums.REGISTER);
        if (CollectionUtils.isEmpty(approvalNotices)) {
            throw new ValidateException("No available email");
        }
        AtomicReference<String> sender = new AtomicReference<>();
        approvalNotices.stream().filter(d -> Boolean.TRUE.equals(d.getEnabled())
                        && NotifyViaEnums.EMAIL == NotifyViaEnums.of(d.getNotifyVia()))
                .findAny()
                .ifPresent(d -> sender.set(d.getSender()));
        if (StringUtils.isBlank(sender.get())) {
            throw new ValidateException("No available email");
        }
        return sender.get();
    }

    @Override
    public Boolean clearAlterReminderTrigger(User user, String outTenantId) {
        List<String> outUserIds = channelCacheService.queryRecordPrmAlterOutUserIds(user, outTenantId);
        channelCacheService.deleteAlterReminderCache(user, outTenantId, outUserIds);
        channelCacheService.deleteRecordPrmAlterOutUserIds(user, outTenantId);
        return Boolean.TRUE;
    }

    @Override
    public boolean allowInitiateRenewal(User user, String admissionObject, String belongDataId) {
        IObjectData signSchemeData = matchSignSchemeByDataId(user, admissionObject, belongDataId);
        return isManualRemindTrigger(signSchemeData);
    }

    private boolean isManualRemindTrigger(IObjectData signSchemeData) {
        return ReminderTrigger.MANUAL == getReminderTrigger(signSchemeData);
    }

    private ReminderTrigger getReminderTrigger(IObjectData signSchemeData) {
        String reminderTriggerStr = ObjectDataUtils.getValue(signSchemeData, SignSchemeModel.REMINDER_TRIGGER, String.class, null);
        if (StringUtils.isBlank(reminderTriggerStr)) {
            return null;
        }
        return ReminderTrigger.find(reminderTriggerStr, null);
    }

    private IObjectData matchSignSchemeByDataId(User user, String admissionObject, String belongDataId) {
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, belongDataId, admissionObject);
        if (admissionData == null) {
            log.warn("PrmChannelService#matchProvisionData admissionData is null, user:{}, admissionObject:{}", user.getTenantId(), admissionObject);
            return null;
        }
        ChannelServiceModel.MatchScheme matchScheme = matchSignScheme(user, admissionObject, admissionData);
        String schemeId = matchScheme.getSchemeId();
        if (StringUtils.isBlank(schemeId)) {
            return null;
        }
        return signSchemeAccess.querySignSchemeById(user, schemeId);
    }

    @Override
    public Set<String> allowInitiateRenewal(User user, Set<String> admissionDataIds, String admissionObject) {
        if (CollectionUtils.isEmpty(admissionDataIds)) {
            return Sets.newHashSet();
        }
        List<IObjectData> admissionDataList = metaDataFindServiceExt.findObjectByIdsIgnoreAll(user, Lists.newArrayList(admissionDataIds), admissionObject);
        if (CollectionUtils.isEmpty(admissionDataList)) {
            return Sets.newHashSet();
        }
        List<PartnerChannelManage.SignScheme> signSchemes = partnerChannelService.queryPushedSignSchemeList(user);
        if (CollectionUtils.isEmpty(signSchemes)) {
            return Sets.newHashSet();
        }
        Set<String> manualSignIds = signSchemes.stream()
                .filter(d -> ReminderTrigger.MANUAL == ReminderTrigger.find(d.getReminderTrigger(), null))
                .map(PartnerChannelManage.SignScheme::getSignSchemeId)
                .collect(Collectors.toSet());
        return admissionDataList.stream()
                .filter(data -> manualSignIds.contains(matchSignScheme(user, admissionObject, data, signSchemes).getSchemeId()))
                .map(DBRecord::getId)
                .collect(Collectors.toSet());
    }

    @Override
    public PartnerChannelManage.CustomText fetchCustomText(User user, String key) {
        return channelConfigAccess.fetchCustomText(user, key);
    }

    @Override
    public String fetchChannelAdmissionObject(User user) {
        PartnerChannelManage.AdmissionConfig admissionConfig = channelConfigAccess.fetchChannelAdmissionConfig(user);
        return Optional.ofNullable(admissionConfig).map(PartnerChannelManage.AdmissionConfig::getRelatedObjectApiName).orElse(null);
    }

    @Override
    public String getAdmissionObjectDataId(IObjectData objectData, String admissionObject) {
        if (SFAPreDefine.Partner.getApiName().equals(admissionObject)) {
            return ObjectDataUtils.getValue(objectData, PartnerAgreementDetailModel.BELONG_PARTNER_ID, String.class, null);
        } else if (SFAPreDefine.Account.getApiName().equals(admissionObject)) {
            return ObjectDataUtils.getValue(objectData, PartnerAgreementDetailModel.BELONG_ACCOUNT_ID, String.class, null);
        } else {
            return null;
        }
    }

    @Override
    public String fetchAdmissionDataId(User user, IObjectData data) {
        String admissionObject = fetchChannelAdmissionObject(user);
        return getAdmissionObjectDataId(data, admissionObject);
    }

    private PartnerChannelManage.SignScheme queryAssembledSignSchemeById(User user, String signSchemeId) {
        IObjectData signSchemeData = signSchemeAccess.querySignSchemeById(user, signSchemeId);
        if (signSchemeData == null) {
            return null;
        }
        List<PartnerChannelManage.SignScheme> signSchemes = partnerChannelService.assembleSignSchemeList(user, Lists.newArrayList(signSchemeData));
        return signSchemes.get(0);
    }

    public PartnerChannelManage.EnterpriseActivationSetting queryActivationSetting(User user, String dataId) {
        return partnerChannelService.queryActivationSettingById(user, dataId);
    }

    public boolean definitionChannelWorkFlow(User user) {
        AdmissionConfigDTO admissionConfigDTO = channelAdmissionService.fetchChannelAdmissionConfig(user);
        if (admissionConfigDTO == null) {
            log.warn("initMapping: admission config found, tenant:{}", user.getTenantId());
            return false;
        }
        Set<String> needInitWorkFlowIds = ChannelFlowInitVO.getNeedInitWorkFlowIds(admissionConfigDTO.getRelatedObjectApiName());
        for (String workFlowId : needInitWorkFlowIds) {
            ApprovalInitModel.DefinitionArg definitionArg = ApprovalInitModel.DefinitionArg.builder()
                    .sourceWorkflowIds(Lists.newArrayList(workFlowId))
                    .build();
            ApprovalInitModel.Result result = approvalInitProxy.definitionBySourceWorkflowId(RestUtils.getApprovalInitHeaders(user), definitionArg);
            if (result == null || !result.isSuccess()) {
                log.warn("PartnerChannelServiceProvider#definitionChannelWorkFlow, tenantId:{}, result:{}, workFlowId:{}", user.getTenantId(), result, workFlowId);
            }
        }
        return true;
    }

    public ChannelRpcModel.ApprovalNoticeAggregatorResult matchSignSchemeWithAllInfo(User user, String dataId) {
        String admissionObject = fetchChannelAdmissionObject(user);
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, dataId, admissionObject);
        if (admissionData == null) {
            log.warn("admissionData is null, dataId:{}", dataId);
            return ChannelRpcModel.ApprovalNoticeAggregatorResult.builder().effective(false).build();
        }
        ChannelServiceModel.MatchScheme matchScheme = matchSignScheme(user, admissionObject, admissionData);
        String schemeId = matchScheme.getSchemeId();
        if (StringUtils.isBlank(schemeId)) {
            return ChannelRpcModel.ApprovalNoticeAggregatorResult.builder().effective(false).build();
        }
        IObjectData data = signSchemeAccess.querySignSchemeById(user, schemeId);
        List<PartnerChannelManage.SignScheme> signSchemes = partnerChannelService.assembleSignSchemeList(user, Lists.newArrayList(data));
        if (CollectionUtils.isEmpty(signSchemes)) {
            return ChannelRpcModel.ApprovalNoticeAggregatorResult.builder().effective(false).build();
        }
        List<PartnerChannelManage.ApprovalNotice> approvalNoticeList = signSchemes.get(0).getApprovalNoticeList();
        List<ChannelRpcModel.ApprovalNoticeAggregator> approvalNoticeAggregators = buildApprovalNoticeAggregators(user, approvalNoticeList);
        return ChannelRpcModel.ApprovalNoticeAggregatorResult.builder()
                .signSchemeId(schemeId)
                .approvalNoticeAggregators(approvalNoticeAggregators)
                .activateRoles(signSchemes.get(0).getActivateRoles())
                .build();
    }

    @Override
    public ChannelRpcModel.SignReminderInfoResult querySignReminderInfo(User user, String signSchemeId, ReminderTrigger reminderTrigger) {
        PartnerChannelManage.SignScheme signScheme = queryAssembledSignSchemeById(user, signSchemeId);
        if (signScheme == null) {
            return ChannelRpcModel.SignReminderInfoResult.builder().effective(false).build();
        }
        if (!Boolean.TRUE.equals(signScheme.getPushed())) {
            return ChannelRpcModel.SignReminderInfoResult.builder().effective(false).build();
        }
        List<PartnerChannelManage.ReminderType> reminderTypes = assembleExpireReminderTypeList(signScheme.getExpireReminderTypeView().getAutoExpireReminderType());
        if (reminderTrigger == ReminderTrigger.MANUAL) {
            reminderTypes = assembleExpireReminderTypeList(signScheme.getExpireReminderTypeView().getManualExpireReminderType());
        }
        return ChannelRpcModel.SignReminderInfoResult.builder()
                .startDate(signScheme.getStartDate())
                .renewalCycleTime(signScheme.getRenewalCycleTime())
                .renewalCycleUnit(signScheme.getRenewalCycleUnit())
                .reminderTypes(reminderTypes)
                .reminderTrigger(signScheme.getReminderTrigger())
                .scheduleType(signScheme.getScheduleType())
                .expireReminderPersonView(signScheme.getExpireReminderPersonView())
                .planDuration(signScheme.getPlanDuration())
                .planDurationUnit(signScheme.getPlanDurationUnit())
                .build();
    }

    private List<PartnerChannelManage.ReminderType> assembleExpireReminderTypeList(PartnerChannelManage.ExpireReminderType expireReminderType) {
        List<PartnerChannelManage.ReminderType> ExpireReminderTypeList = Lists.newArrayList();
        PartnerChannelManage.ReminderType crmReminder = expireReminderType.getCrmReminder();
        PartnerChannelManage.ReminderType prmCrmReminder = expireReminderType.getPrmCrmReminder();
        PartnerChannelManage.ReminderType emailReminder = expireReminderType.getEmailReminder();
        PartnerChannelManage.ReminderType smsReminder = expireReminderType.getSmsReminder();
        PartnerChannelManage.ReminderType prmAlertWindowReminder = expireReminderType.getPrmAlertWindowReminder();
        ExpireReminderTypeList.add(crmReminder);
        ExpireReminderTypeList.add(prmCrmReminder);
        ExpireReminderTypeList.add(emailReminder);
        ExpireReminderTypeList.add(smsReminder);
        ExpireReminderTypeList.add(prmAlertWindowReminder);
        return ExpireReminderTypeList;
    }

    public boolean prmAlterReminderTrigger(User user, List<ChannelManagementDTO.ReminderCache> reminderCacheList) {
        if (CollectionUtils.isEmpty(reminderCacheList)) {
            return false;
        }
        Map<String, List<String>> listMap = reminderCacheList.stream()
                .collect(Collectors.groupingBy(
                        ChannelManagementDTO.ReminderCache::getOutTenantId,    // key: outTenantId
                        Collectors.mapping(ChannelManagementDTO.ReminderCache::getOutUserId, Collectors.toList()) // value: outUserId list
                ));
        listMap.forEach((outTenantId, outUSerIds) -> channelCacheService.recordPrmAlterOutUserIds(user, outTenantId, outUSerIds));
        channelCacheService.prmAlterReminderTrigger(user, reminderCacheList);
        return true;
    }

    public ChannelServiceModel.MatchScheme matchSimpleSignScheme(User user, String admissionDataId) {
        String admissionObject = fetchChannelAdmissionObject(user);
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, admissionDataId, admissionObject);
        return matchSignScheme(user, admissionObject, admissionData);
    }

    public ChannelServiceModel.MatchScheme matchSignSchemeInfo(User user, String dataId) {
        String admissionObject = fetchChannelAdmissionObject(user);
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, dataId, admissionObject);
        if (admissionData == null) {
            log.warn("根据id 未找到admissionData数据, tenantId:{}, dataId:{}", user.getTenantId(), dataId);
            return ChannelServiceModel.MatchScheme.builder().build();
        }

        return matchSignScheme(user, admissionObject, admissionData);
    }

    public BaseObjectSaveAction.Result createContactByPartner(User user, String objectApiName, String dataId) {
        IObjectData data = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, dataId, objectApiName);
        if (data == null) {
            log.warn("createContactByPartner but data is empty, tenant:{}, objectApiName:{}, dataId:{}", user.getTenantId(), objectApiName, dataId);
            return null;
        }
        ActionContext actionContext = new ActionContext(RequestContextManager.getContext(), SFAPreDefine.Contact.getApiName(), SystemConstants.ActionCode.Add.getActionCode());
        BaseObjectSaveAction.Arg arg = buildContactAddArg(user, objectApiName, data);
        return serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
    }

    private BaseObjectSaveAction.Arg buildContactAddArg(User user, String objectApiName, IObjectData data) {
        Map<String, Object> dataMap = buildContactDataMap(user, objectApiName, data);
        ObjectDataDocument dataDocument = ObjectDataDocument.of(dataMap);
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(dataDocument);
        return arg;
    }

    private Map<String, Object> buildContactDataMap(User user, String objectApiName, IObjectData data) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(ObjectDataExt.TENANT_ID, user.getTenantId());
        if (SFAPreDefineObject.Account.equals(objectApiName)) {
            dataMap.put(ObjectDataExt.RECORD_TYPE, DEFAULT__C);
        } else {
            dataMap.put(ObjectDataExt.RECORD_TYPE, CONTACT_PARTNER_TYPE);
        }
        dataMap.put(ObjectDataExt.DESCRIBE_API_NAME, SFAPreDefineObject.Contact.getApiName());
        List<IObjectMappingRuleDetailInfo> mappingFields = getMappingFields(user, objectApiName);
        Optional.ofNullable(mappingFields).orElse(Lists.newArrayList())
                .forEach(mapping -> fillMappingFields(mapping, data, dataMap));
        return dataMap;
    }

    private void fillMappingFields(IObjectMappingRuleDetailInfo mapping, IObjectData data, Map<String, Object> dataMap) {
        String sourceField = mapping.getSourceFieldName();
        String targetField = mapping.getTargetFieldName();
        Object sourceValue = data.get(sourceField);
        Object targetValue = null;
        List<IObjectMappingRuleEnumInfo> optionMappingInfo = mapping.getOptionMapping();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(optionMappingInfo)) {
            targetValue = sourceValue;
        } else {
            Map<String, String> optionMapping = optionMappingInfo.stream().collect(Collectors.toMap(IObjectMappingRuleEnumInfo::getSourceEnumCode, IObjectMappingRuleEnumInfo::getTargetEnumCode, (k1, k2) -> k1));
            if (sourceValue != null) {
                targetValue = optionMapping.get(sourceValue.toString());
            }
        }
        dataMap.put(targetField, targetValue);
    }

    private List<IObjectMappingRuleDetailInfo> getMappingFields(User user, String objectApiName) {
        String ruleApiName;
        if (SFAPreDefineObject.Partner.getApiName().equals(objectApiName)) {
            ruleApiName = PARTNER_2_PARTNER_CONTACT;
        } else if (SFAPreDefineObject.Account.getApiName().equals(objectApiName)) {
            ruleApiName = ACCOUNT_2_ACCOUNT_CONTACT;
        } else {
            log.warn("getMappingFields but objectApiName is not support, objectApiName:{}", objectApiName);
            return Lists.newArrayList();
        }
        List<IObjectMappingRuleInfo> rules = objectMappingService.findByApiName(user, ruleApiName);
        IObjectMappingRuleInfo mappingRule = rules.get(0);
        return mappingRule.getFieldMapping();
    }
}
