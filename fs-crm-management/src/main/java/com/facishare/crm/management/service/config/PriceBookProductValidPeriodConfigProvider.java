package com.facishare.crm.management.service.config;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/28
 */
@Component
@Slf4j
public class PriceBookProductValidPeriodConfigProvider extends DefaultBizConfigProvider {

    private static final String START_DATE = "{\"is_index\": true,\"is_active\": true,\"is_required\": false,\"api_name\": \"start_date\",\"is_unique\": false,\"define_type\": \"package\",\"label\": \"有效开始时间\",\"help_text\": \"\",\"type\": \"date_time\",\"time_zone\": \"GMT+8\",\"date_format\": \"yyyy-MM-dd HH:mm:ss\"}";// ignoreI18n
    private static final String END_DATE = "{\"is_index\": true,\"is_active\": true,\"is_required\": false,\"api_name\": \"end_date\",\"is_unique\": false,\"define_type\": \"package\",\"label\": \"有效结束时间\",\"help_text\": \"\",\"type\": \"date_time\",\"time_zone\": \"GMT+8\",\"date_format\": \"yyyy-MM-dd HH:mm:ss\"}";// ignoreI18n

    @Autowired
    private EnterpriseInitService enterpriseInitService;

    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    @Override
    public String getConfigKey() {
        return BizConfigKey.PRICE_BOOK_PRODUCT_VALID_PERIOD.getId();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        if (!"1".equals(value)) {
            throw new ValidateException(I18N.text("sfa.config.not_allowed_to_close"));
        }
        if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(user.getTenantId())) {
            throw new ValidateException(I18N.text("sfa.config.not.open.valid.period.warn"));
        }
        IObjectDescribe priceBookProductDescribe = getDescribeWithSimplifiedChinese(user, Utils.PRICE_BOOK_PRODUCT_API_NAME);
        if (priceBookProductDescribe == null) {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), Utils.PRICE_BOOK_PRODUCT_API_NAME);
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
        refreshPriceBookProductDescribe(priceBookProductDescribe);
        refreshPriceBookProductLayout(user, priceBookProductDescribe);
        super.setConfigValue(user, value, oldValue, key);
    }

    private void refreshPriceBookProductDescribe(IObjectDescribe priceBookProductDescribe) {
        if (priceBookProductDescribe.getFieldDescribe(PriceBookConstants.ProductField.START_DATE.getApiName()) == null) {
            IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(START_DATE);
            priceBookProductDescribe.addFieldDescribe(fieldDescribe);
        }
        if (priceBookProductDescribe.getFieldDescribe(PriceBookConstants.ProductField.END_DATE.getApiName()) == null) {
            IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(END_DATE);
            priceBookProductDescribe.addFieldDescribe(fieldDescribe);
        }
        try {
            objectDescribeService.update(priceBookProductDescribe);
        } catch (MetadataServiceException e) {
            log.error("Init PriceBookProductValidPeriod failed:", e);
        }
    }

    private void refreshPriceBookProductLayout(User user, IObjectDescribe priceBookProductDescribe) {
        List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayouts = Lists.newArrayList();
        List<String> addedDateFields = Lists.newArrayList(PriceBookConstants.ProductField.START_DATE.getApiName(), PriceBookConstants.ProductField.END_DATE.getApiName());
        for (String fieldName : addedDateFields) {
            FieldLayoutPojo fieldLayout = enterpriseInitService.getFieldLayoutPojo(SystemConstants.RenderType.DateTime.renderType, false, false);
            fieldLayouts.add(Tuple.of(priceBookProductDescribe.getFieldDescribe(fieldName), fieldLayout));
        }
        List<ILayout> layoutList = serviceFacade.getLayoutLogicService().getDetailLayouts(user.getTenantId(), priceBookProductDescribe);
        layoutList.forEach(m -> {
            fieldLayouts.forEach(tuple -> LayoutExt.of(m).addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.getLayoutLogicService().updateLayout(user, m);
        });
    }
}
