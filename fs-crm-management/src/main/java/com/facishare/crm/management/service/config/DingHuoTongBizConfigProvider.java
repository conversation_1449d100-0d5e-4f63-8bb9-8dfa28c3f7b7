package com.facishare.crm.management.service.config;

import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 配置 是否启用订货通(0-不启用 1-启用) class
 *
 * <AUTHOR>
 * @date 2019/3/25
 */
@Component
@Slf4j
public class DingHuoTongBizConfigProvider extends DefaultBizConfigProvider {
    @Autowired
    private BizConfigProviderManager bizConfigProviderManager;
    @Autowired
    private ModuleCtrlConfigService moduleCtrlConfigService;

    @Override
    public String getConfigKey() {
        return BizConfigKey.IS_DINGHUOTONG_ENABLED.getId();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        if (!"1".equals(value) || !"0".equals(oldValue)) {
            return;
        }
        //region更改销售订单/退货规则为规则一
//        String oldConfigValue = super.getConfigValue(user, BizConfigKey.CUSTOMER_ORDER_RULE.getId());
//        String newConfigValue = "1,0,0";
//        CustomerOrderRuleBizConfigProvider customerOrderRuleBizConfigProvider = new CustomerOrderRuleBizConfigProvider();
//        customerOrderRuleBizConfigProvider.serviceFacade = serviceFacade;
//        customerOrderRuleBizConfigProvider.setConfigValue(user, newConfigValue, oldConfigValue, key);

        BizConfigProvider provider = bizConfigProviderManager.getProvider(BizConfigKey.CUSTOMER_ORDER_RULE.getId());
        provider.validateSetConfig(user, BizConfigKey.CUSTOMER_ORDER_RULE.getId(), "1,0,0");
        provider.setConfigValue(user, "1,0,0", "", BizConfigKey.CUSTOMER_ORDER_RULE.getId());
        //endregion

        List<IFieldDescribe> updateFieldDescribes = Lists.newArrayList();
        Map<String, IObjectDescribe> describiMap = serviceFacade.findObjects(user.getTenantId(), Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.CUSTOMER_PAYMENT_API_NAME, Utils.INVOICE_APPLICATION_API_NAME));
        if (describiMap != null) {
            //region将销售订单商机设置为非必填,且不允許修改
            IObjectDescribe salesOrderDescribe = describiMap.get(Utils.SALES_ORDER_API_NAME);
            if (salesOrderDescribe != null) {
                IFieldDescribe opportunityIdDescribe = salesOrderDescribe.getFieldDescribe(SalesOrderConstants.SalesOrderField.OPPORTUNITY_ID.getApiName());
                if (opportunityIdDescribe != null) {
                    opportunityIdDescribe.setRequired(false);
                    Map<String, Object> configMap = opportunityIdDescribe.getConfig();
                    if (configMap == null) {
                        configMap = Maps.newHashMap();
                    }
                    Map<String, Object> attrsConfig = (Map) configMap.get("attrs");
                    if (attrsConfig == null) {
                        attrsConfig = Maps.newHashMap();
                    }
                    attrsConfig.put("is_required", 0);
                    configMap.put("attrs", attrsConfig);
                    opportunityIdDescribe.setConfig(configMap);
                    updateFieldDescribes.add(opportunityIdDescribe);

                } else {
                    log.warn("setConfigValue>获取字段描述失败={},{},{}", user.getTenantId(), Utils.SALES_ORDER_API_NAME,
                            SalesOrderConstants.SalesOrderField.OPPORTUNITY_ID.getApiName());
                }
            } else {
                log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), Utils.SALES_ORDER_API_NAME);
            }
            //endregion

            //region订单、回款、开票等改为自动编号规则
            if (salesOrderDescribe != null) {
                changeNameField(user, salesOrderDescribe, updateFieldDescribes);
            }
            IObjectDescribe customerPaymentDescribe = describiMap.get(Utils.CUSTOMER_PAYMENT_API_NAME);
            if (customerPaymentDescribe != null) {
                updateFieldDescribes.clear();
                changeNameField(user, customerPaymentDescribe, updateFieldDescribes);
            } else {
                log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), Utils.CUSTOMER_PAYMENT_API_NAME);
            }
            IObjectDescribe invoiceApplicationDescribe = describiMap.get(Utils.INVOICE_APPLICATION_API_NAME);
            if (invoiceApplicationDescribe != null) {
                updateFieldDescribes.clear();
                changeNameField(user, invoiceApplicationDescribe, updateFieldDescribes);
            } else {
                log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), Utils.INVOICE_APPLICATION_API_NAME);
            }
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), Lists.newArrayList(Utils.CUSTOMER_PAYMENT_API_NAME, Utils.INVOICE_APPLICATION_API_NAME));
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
        //endregion

        //开启虚拟字段
        enableVirtualExtension(user.getTenantId());

        super.setConfigValue(user, value, oldValue, key);
    }

    private void enableVirtualExtension(String tenantId) {
        ConfigCtrlModule.Arg moduleArg = new ConfigCtrlModule.Arg();
        moduleArg.setModuleCode(IModuleInitService.VIRTUAL_EXTENSION);
        moduleArg.setTenantId(tenantId);
        moduleArg.setOpenStatus(1);
        ServiceContext serviceContext = new ServiceContext(RequestContextManager.getContext(), null, null);
        moduleCtrlConfigService.saveModuleStatus(moduleArg, serviceContext);
    }

    private void changeNameField(User user, IObjectDescribe objectDescribe, List<IFieldDescribe> updateFieldDescribes) {
        IFieldDescribe nameDescribe = objectDescribe.getFieldDescribe("name");
        String type = nameDescribe.getType();
        if(Objects.equals(type, "auto_number")){
            return;
        }
        nameDescribe.set("type", "auto_number");
        nameDescribe.set("serial_number", "6");
        nameDescribe.set("start_number", "1");
        nameDescribe.set("prefix", "{yyyy}{mm}{dd}-");
        nameDescribe.set("postfix", "");
        updateFieldDescribes.add(nameDescribe);
        if (updateFieldDescribes.size() > 0) {
            for (IFieldDescribe fieldDescribe : updateFieldDescribes) {
                objectDescribe.updateFieldDescribe(fieldDescribe);
            }
            serviceFacade.update(objectDescribe);
        }

        List<ILayout> layoutList = serviceFacade.getLayoutLogicService().getDetailLayouts(user.getTenantId(), objectDescribe);
        HashMap<String, Map<String, Object>> changeFields = new HashMap<>();
        HashMap<String, Object> innerMap = new HashMap<>();
        innerMap.put("render_type", "auto_number");
        changeFields.put("name",innerMap);

        if (objectDescribe.getApiName().equals(Utils.SALES_ORDER_API_NAME)) {
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("is_required", false);
            changeFields.put(SalesOrderConstants.SalesOrderField.OPPORTUNITY_ID.getApiName(), stringObjectHashMap);
        }
        layoutList.forEach(m -> serviceFacade.getLayoutLogicService().updateLayout(user, descUtil.updateFieldRenderType(m, changeFields)));
    }
}
