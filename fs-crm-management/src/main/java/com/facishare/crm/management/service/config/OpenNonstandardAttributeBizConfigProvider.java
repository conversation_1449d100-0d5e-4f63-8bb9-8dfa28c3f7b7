package com.facishare.crm.management.service.config;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.management.service.AttributeGroupInitService;
import com.facishare.crm.management.service.config.model.InsertOrUpdataResult;
import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.modulectrl.SFABizObjMappingRuleWrapperService;
import com.facishare.crm.sfa.utilities.constant.AttributePriceBookConstants;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ComponentDefineType;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配置 是否开启非标属性
 *
 * <AUTHOR>
 * @date 2019/3/25
 */
@Component
@Slf4j
public class OpenNonstandardAttributeBizConfigProvider extends DefaultBizConfigProvider {

    @Autowired
    EnterpriseInitService enterpriseInitService;
    @Autowired
    SFABizObjMappingRuleWrapperService mappingRuleWrapperService;
    @Autowired
    private AttributeGroupInitService attributeGroupInitService;

    private final static String NON_ATTRIBUTE_VALUES_JSON = "{\"api_name\":\"non_attribute_values\",\"is_index\":false,\"is_active\":true,\"description\":\"非标属性默认值\",\"is_unique\":false,\"label\":\"非标属性默认值\",\"type\":\"use_range\",\"expression_type\":\"json\",\"is_abstract\":false,\"field_num\":null,\"is_required\":false,\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"max_length\":5000,\"status\":\"new\"}"; // ignoreI18n

    @Override
    public String getConfigKey() {
        return BizConfigKey.IS_OPEN_NONSTANDARD_ATTRIBUTE.getId();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {

        List<String> describeList = Lists.newArrayList(Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME, Utils.PRODUCT_API_NAME,
                Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.QUOTE_LINES_API_NAME, Utils.SALE_CONTRACT_LINE_API_NAME);
        Map<String, IObjectDescribe> describeMap = getDescribeWithSimplifiedChineseByApiNames(user, describeList);
        for (String describeName : describeList) {
            //刷描述
            if (describeName.equals(SFAPreDefineObject.NonstandardAttribute.getApiName())) {
                IObjectDescribe describe = describeMap.get(describeName);
                if (describe == null) {
                    enterpriseInitService.initDescribeForTenant(user.getTenantId(), describeName);
                }
                //先刷功能权限
                enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(describeName), user, null, null, null);
                //刷布局
                List<ILayout> layouts = serviceFacade.getLayoutLogicService().findLayoutByObjectApiName(user.getTenantId(), describeName);
                if (layouts == null || layouts.isEmpty()) {
                    enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(describeName), user.getTenantId());
                }
            }
        }
        //属性值所有员工角色绑定默认业务类型
        //addRoleRecordForAttributeValue(user);

        //region 订单产品，报价单明细加字段
        // TODO: 2020/10/14 configmap
        addAttributeField(user, describeMap, Utils.SALES_ORDER_PRODUCT_API_NAME,"nonstandard_attribute","非标属性",SystemConstants.RenderType.Text.renderType,null,null,null); // ignoreI18n
        addAttributeField(user, describeMap, Utils.QUOTE_LINES_API_NAME,"nonstandard_attribute","非标属性",SystemConstants.RenderType.Text.renderType,null,null,null); // ignoreI18n
        addAttributeJsonField(user, describeMap, Utils.SALES_ORDER_PRODUCT_API_NAME);
        addAttributeJsonField(user, describeMap, Utils.QUOTE_LINES_API_NAME);
        if (SFAConfigUtil.isSaleContractOpen(user.getTenantId())) {
            addAttributeField(user, describeMap, Utils.SALE_CONTRACT_LINE_API_NAME,"nonstandard_attribute","非标属性",SystemConstants.RenderType.Text.renderType,null,null,null); // ignoreI18n
            addAttributeJsonField(user, describeMap, Utils.SALE_CONTRACT_LINE_API_NAME);
        }

        addProductField(user, describeMap, Utils.PRODUCT_API_NAME, "nonstandard_attribute_ids", "关联非标属性"); // ignoreI18n
        //endregion

        //处理映射规则
        addRuleMapping(user.getTenantId());

        addNonAttributeDefaultValueFieldForProduct(describeMap.get(Utils.PRODUCT_API_NAME));

        //处理产品布局
        try {
            addProductComponent(user, describeMap.get(Utils.PRODUCT_API_NAME));
        } catch (Exception e) {
            log.warn("处理产品布局 error,{}", e.toString()); // ignoreI18n
        }
        //功能权限和按钮
        serviceFacade.batchCreateFunc(user, Utils.PRODUCT_API_NAME, Lists.newArrayList(ObjectAction.AssociateNonstandardAttribute.getActionCode()));
        serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, Utils.PRODUCT_API_NAME, Lists.newArrayList(ObjectAction.AssociateNonstandardAttribute.getActionCode()), Lists.newArrayList());
        attributeGroupInitService.handle(user,Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME);
        super.setConfigValue(user, value, oldValue, key);
    }

    /**
     * 开通非标实现时，给产品刷非标属性默认值字段
     * @param describe
     * @throws MetadataServiceException
     */
    private void addNonAttributeDefaultValueFieldForProduct(IObjectDescribe describe) {
        if(describe != null && describe.getFieldDescribe(ProductConstants.NON_ATTRIBUTE_VALUES) == null) {
            IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(NON_ATTRIBUTE_VALUES_JSON);
            fieldDescribe.setDescribeApiName(describe.getApiName());
            describe.addFieldDescribe(fieldDescribe);
            try {
                objectDescribeService.update(describe);
            }catch (MetadataServiceException e) {
                log.warn("更新产品非标属性默认值字段 error,{}", e); // ignoreI18n
            }
        }
    }

    private void addAttributeField(User user, Map<String, IObjectDescribe> describiMap, String apiName,String fieldName,
        String fieldLabel,String renderType,String targetApiName, String targetRelatedListLabel,String targetRelatedListName ) {
        Map configMap = new HashMap<String, Object>();
        IObjectDescribe attributeDescribe = describiMap.get(apiName);
        if (attributeDescribe != null) {
            InsertOrUpdataResult fieldResult = null;
            if(renderType.equals(SystemConstants.RenderType.ObjectReference.renderType)){
                fieldResult = descUtil.getUpdateOrInsertFieldDescribe(attributeDescribe,fieldName,fieldLabel,targetApiName,targetRelatedListLabel,targetRelatedListName,configMap,false);
            } else if(renderType.equals(SystemConstants.RenderType.Text.renderType)){
                fieldResult = descUtil.getUpdateOrInsertTextFieldDescribe(attributeDescribe, fieldName, fieldLabel, false);
            }
            if(fieldResult == null){
                return;
            }
            if (fieldResult.getIsInsert()) {
                descUtil.addFieldDescribe(attributeDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            } else {
                descUtil.updateFieldDescribe(attributeDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            }
            // 把新增的字段放入到指定的layout中的FormComponent中的基本信息FieldSection的里面
            FieldLayoutPojo fieldLayoutPojo = descUtil.getFieldLayoutPojo(renderType, true, false);
            descUtil.insertFieldToLayout(user, attributeDescribe, fieldResult.getFieldDescribe(), fieldLayoutPojo);
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), apiName); // ignoreI18n
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
    }

    private void addProductField(User user, Map<String, IObjectDescribe> describiMap, String apiName, String fieldName, String lable) {
        IObjectDescribe productDescribe = describiMap.get(apiName);
        if (productDescribe != null) {
            InsertOrUpdataResult fieldResult = descUtil.getUpdateOrInsertArrayFieldDescribe(productDescribe
                , fieldName, lable);
            if (fieldResult.getIsInsert()) {
                descUtil.addFieldDescribe(productDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            } else {
                descUtil.updateFieldDescribe(productDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            }
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), apiName); // ignoreI18n
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
    }

    private void addAttributeJsonField(User user, Map<String, IObjectDescribe> describiMap, String apiName) {
        IObjectDescribe attributeDescribe = describiMap.get(apiName);
        if (attributeDescribe != null) {
            InsertOrUpdataResult fieldResult = descUtil.getUpdateOrInsertLongTextFieldDescribe(attributeDescribe
                , "nonstandard_attribute_json", "非标属性值", true); // ignoreI18n
            if (fieldResult.getIsInsert()) {
                descUtil.addFieldDescribe(attributeDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            } else {
                descUtil.updateFieldDescribe(attributeDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            }
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), apiName); // ignoreI18n
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
    }

    private void addProductComponent(User user, IObjectDescribe describe) {
        IComponent component = buildProductAttributeComponent();

        descUtil.insertComponentToLayout(user, describe, component, 0);
    }
    public static CommonComponent buildProductAttributeComponent() {
        CommonComponent component = new CommonComponent();
        component.setType("product_attribute");
        component.setName("product_attribute_component");
        component.setHeader(I18N.text(I18NKey.PRODUCT_ATTRIBUTE));
        component.set("field_api_name", "product_attribute");
        component.setDefineType(ComponentDefineType.BUSINESS.getType());
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    private void addRuleMapping(String tenantId)
    {
        Map<String,String> fieldMapping = Maps.newHashMap();
        fieldMapping.put("nonstandard_attribute","nonstandard_attribute");
        fieldMapping.put("nonstandard_attribute_json","nonstandard_attribute_json");

        try {
            mappingRuleWrapperService.addFieldMapping(tenantId,"rule_quotelinesobj2salesorderproduct__c", fieldMapping);
            if (SFAConfigUtil.isSaleContractOpen(tenantId)) {
                mappingRuleWrapperService.addFieldMapping(tenantId,"rule_quotelinesobj2salecontractlineobj__c", fieldMapping);
                mappingRuleWrapperService.addFieldMapping(tenantId,"rule_salecontractlineobj2salesorderproductobj__c", fieldMapping);
            }
            mappingRuleWrapperService.addFieldMapping(tenantId,"rule_salesorderprodobj2quotelinesobj__c", fieldMapping);
        } catch (MetadataServiceException e) {
            log.error("MetadataServiceException:{}",e);
        }
    }
}
