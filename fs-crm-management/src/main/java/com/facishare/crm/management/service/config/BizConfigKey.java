package com.facishare.crm.management.service.config;


/**
 * 业务配置枚举
 *
 * <AUTHOR>
 */

public enum BizConfigKey {
    //订单规则 ，默认勾选3。其中12互斥
    //1 订单金额=产品合计*折扣，产品是必填项
    //2 订单金额、产品合计、折扣互相独立，产品是选填项
    //3 订单金额、产品合计、折扣互相独立，产品是必填项
    CUSTOMER_ORDER_RULE("customer_order_rule", "16"),
    //是否启用订货通(0-不启用 1-启用)
    IS_DINGHUOTONG_ENABLED("is_dinghuotong_enabled", "23"),
    //是否启用价目表（0-不起用 1-启用）
    IS_PRICE_BOOK_ENABLED("is_price_book_enabled", "28"),
    //客户账户是否启用（0-不启用,1-启用)
    IS_CUSTOMER_ACCOUNT_ENABLED("is_customer_account_enabled", "29"),
    //库存是否启用
    IS_INVENTORY_ENABLED("is_inventory_enabled", "30"),
    //促销是否启用
    IS_PROMOTION_ENABLED("is_promotion_enabled", "31"),
    //发货单是否启用
    IS_DELIVERY_NOTE_ENABLED("is_delivery_note_enabled", "33"),
    //是否开启设备管理
    IS_DEVICE_MANAGEMENT_ENABLED("is_device_management_enabled", "35"),
    //是否开启合作伙伴
    IS_OPEN_PARTNER("is_open_partner", "37"),
    //老企业是否开启商机2.0
    IS_OPEN_NEW_OPPORTUNITY("is_open_new_opportunity", "42"),
    //行业价目表  与价目表互斥
    IS_INDUSTRY_PRICE_BOOK_ENABLED("is_industry_price_book_enabled", "44"),
    SPU_OPEN("spu", "spu"),
    IS_BATCH_SN_ENABLED("is_batch_sn_enabled", "47"),
    IS_KX_PECULIARITY_ENABLED("is_kx_peculiarity_enabled", "is_kx_peculiarity_enabled"),
    IS_KX_OPEN_SCAN_CODE_ENABLED("is_kx_open_scan_code_enabled", "is_kx_open_scan_code_enabled"),
    //是否开启可售范围
    AVAILABLE_RANGE("available_range", "available_range"),
    //强制执行优先级取价
    ENFORCE_PRIORITY("enforce_priority", "enforce_priority"),
    //强制执行优先级取价
    IS_OPEN_ATTRIBUTE("is_open_attribute", "is_open_attribute"),
    IS_OPEN_NONSTANDARD_ATTRIBUTE("is_open_nonstandard_attribute", "is_open_nonstandard_attribute"),
    //是否打开可售范围查重
    AVAILABLE_RANGE_DUPLICATED_CHECK("available_range_duplicated_check", "available_range_duplicated_check"),
    // 报价单转订单，收货人、收货人电话、收货人地址、仓库、默认值不覆盖映射值-灰度功能
    ORDER_TO_QUOTE_DEFAULT_VALUE_COVER("order_to_quote_default_value_cover", "order_to_quote_default_value_cover"),
    /*产品关键字检索模式
    空-单关键词搜索
    and-多关键词且模式
    or-多关键词或模式*/
    PRODUCT_KEYWORD_SEARCH_MODE("product_keyword_search_mode", "product_keyword_search_mode"),
    /**
     * 订单编辑权限扩大范围
     */
    ORDER_ENLARGE_EDIT_PRIVILEGE("order_enlarge_edit_privilege", "order_enlarge_edit_privilege"),
    /*
    客户地图颜色配置
     */
    MAP_MODE_SETTING_TYPE("map_mode_setting_type", "map_mode_setting_type"),
    IGNORE_PRICE_BOOK_VALID_PERIOD("ignore_price_book_valid_period", "ignore_price_book_valid_period"),
    MATCH_PRICE_BOOK_VALID_FIELD("match_price_book_valid_field", "match_price_book_valid_field"),
    DHT_SALES_ORDER_RECORD_TYPE("dht_sales_order_record_type", "dht_sales_order_record_type"),
    WHETHER_FILTER_PRICE_BOOK_SELECT_PRODUCT("whether_filter_price_book_select_product", "whether_filter_price_book_select_product"),
    PRICE_BOOK_PRODUCT_VALID_PERIOD("price_book_product_valid_period", "price_book_product_valid_period"),
    PRICE_BOOK_PRODUCT_TIERED_PRICE("price_book_product_tiered_price", "price_book_product_tiered_price"),
    // 840 开启合作伙伴地址
    PARTNER_ADDRESS("partner_address", "partner_address"),
    // 开启合作伙伴：分子公司
    PARTNER_SUBSIDIARY("partner_subsidiary", "partner_subsidiary"),
    //开启属性报价器
    IS_OPEN_QUOTER("is_open_quoter", "is_open_quoter"),
    //BOM查重校验
    BOM_DUPLICATE_CHECK("bom_duplicate_check", "bom_duplicate_check"),
    //分组单选时自动收起分组
    BOM_SINGLE_LEAF_NODE_CLOSED("bom_single_leaf_node_closed", "bom_single_leaf_node_closed"),
    //详情页是否收起BOM结构
    BOM_DETAIL_LAYOUT_CLOSED("bom_detail_layout_closed", "bom_detail_layout_closed"),

    //CPQ下单选配页模式
    CPQ_UI_MODE("cpq_ui_mode", "cpq_ui_mode"),
    //期初日期校验
    OPENING_BALANCE_DATE("opening_balance_date", "opening_balance_date"),
    //期初日期强制校验
    OPENING_BALANCE_FORCE_CHECK("opening_balance_force_check", "opening_balance_force_check"),
    //订单行关闭
    ORDER_CLOSE("order_close", "order_close"),

    BIZ_FUNCTION("biz_function", "biz_function"),
    //开票允许负数和零
    INVOICE_SUPPORT_NEGATIVE_AND_ZERO("invoice_support_negative_and_zero", "invoice_support_negative_and_zero"),
    //开启到款银行流水
    IS_OPEN_RECEIVED_PAYMENT("is_open_received_payment", "is_open_received_payment"),
    //开启回款明细必填
    ORDER_PAYMENT_REQUIRED("order_payment_required", "order_payment_required"),
    //是否开启回款明细多来源
    IS_OPEN_ORDER_PAYMENT_MULTI_SOURCE("is_open_order_payment_multi_source", "is_open_order_payment_multi_source"),
    //回款明细字段映射
    ORDER_PAYMENT_MAPPING_RULE("order_payment_mapping_rule", "order_payment_mapping_rule"),
    //结算单明细字段映射
    SETTLEMENT_DETAIL_MAPPING_RULE("settlement_detail_mapping_rule", "order_payment_mapping_rule"),
    //创建应收单，增加选配界面辅助完成创建
    IS_OPEN_AR_QUICK_ADD("is_open_ar_quick_add", "is_open_ar_quick_add"),
    //创建应收单，由哪个对象辅助选配（SaleContractObj,SalesOrderObj）
    CREATE_AR_BY_OBJECTS("create_ar_by_objects", "create_ar_by_objects"),
    //是否开启自动核销
    IS_OPEN_AUTO_MATCH("is_open_auto_match", "create_ar_by_objects"),
    //自动核销匹配规则
    AUTO_MATCH_RULE("auto_match_rule", "create_ar_by_objects"),
    //是否开启属性增量定价
    IS_OPEN_INCREMENTAL_PRICING("is_open_incremental_pricing", "is_open_incremental_pricing"),
    CLOSE_OLD_CATEGORY("close_old_category", "close_old_category"),
    ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY("accounts_receivable_auto_match_button_switch_key", "accounts_receivable_auto_match_button_switch_key"),
    IS_OPEN_PERIODIC_ACCOUNTS_RECEIVABLE("is_open_periodic_accounts_receivable", "is_open_periodic_accounts_receivable"),
    PERIODIC_ACCOUNTS_RECEIVABLE_TYPE("periodic_accounts_receivable_type", "periodic_accounts_receivable_type"),
    CONTRACT_CONNECTOR("contract_connector", "contract_connector"),
    OUTER_CONTRACT_MAPPING("outer_contract_mapping", "outer_contract_mapping"),
    PERIODIC_PRODUCT("periodic_product", "periodic_product"),
    ONE_CLICK_ORDER_TRANSFER("one_click_order_transfer", "one_click_order_transfer"),
    INVOICE_LINES_MULTI_SOURCE("invoice_lines_multi_source", "invoice_lines_multi_source"),
    INVOICE_LINES_REQUIRED("invoice_lines_required", "invoice_lines_required"),
    INVOICE_ORDER_BINDING_STATUS("invoice_order_binding_status","invoice_order_binding_status"),
    //是否启用合同分层结构（0-不启用 1-启用）
    IS_OPEN_ADDITIONAL_CONTRACT("is_open_additional_contract", "is_open_additional_contract"),
    SALE_CONTRACT_RECORD_TYPE_MAPPING("sale_contract_record_type_mapping", "sale_contract_record_type_mapping"),

    STRATIFIED_PRICING("stratified_pricing", "stratified_pricing"),
    OPEN_CHANNEL_ACCESS("open_channel_access", "open_channel_access"),
    // 销售合同履约进度管理
    IS_OPEN_CONTRACT_PROGRESS("is_open_contract_progress", "is_open_contract_progress"),
    BOM_FIRST_COLUMN_WITH_LAYOUT("bom_first_column_with_layout", "bom_first_column_with_layout"),
    //是否开启多来源字段
    IS_OPEN_PAYMENT_MULTI_SOURCE_FIELD("is_open_payment_multi_source_field", "is_open_payment_multi_source_field"),
    //销售Agent
    CUSTOMER_PROFILE_AGENT("customer_profile_agent", "customer_profile_agent")
    ;

    private final String key;
    private final String id;

    BizConfigKey(String key, String id) {
        this.key = key;
        this.id = id;
    }

    public String getKey() {
        return key;
    }

    public String getId() {
        return id;
    }
}
