package com.facishare.crm.management.service.config;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PARTNER_CHANNEL_MODULE_EXPIRED;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PARTNER_NOT_OPEN_WHEN_OPEN_CHANNEL_ACCESS;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-02-20
 * ============================================================
 */
@Slf4j
@Component
public class OpenChannelAccessConfigProvider extends DefaultBizConfigProvider {
    @Resource(name = "asyncTaskProducerSFA")
    private AsyncTaskProducer asyncTaskProducer;
    @Resource
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Resource(name = "channelServiceProvider")
    private ChannelService channelService;


    @Override
    public void validateSetConfig(User user, String key, String new_value) {
        super.validateSetConfig(user, key, new_value);
        if (!bizConfigThreadLocalCacheService.isPartnerEnabled(user.getTenantId())) {
            throw new ValidateException(I18N.text(SFA_PARTNER_NOT_OPEN_WHEN_OPEN_CHANNEL_ACCESS));
        }
        if (channelService.notExistsModule(user)) {
            throw new ValidateException(I18N.text(SFA_PARTNER_CHANNEL_MODULE_EXPIRED));
        }
    }

    @Override
    public String getConfigKey() {
        return BizConfigKey.OPEN_CHANNEL_ACCESS.getId();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        if ("open".equals(bizConfigThreadLocalCacheService.queryChannelAccess(user.getTenantId()))) {
            return;
        }
        super.setConfigValue(user, "open", oldValue, key);
    }
}
