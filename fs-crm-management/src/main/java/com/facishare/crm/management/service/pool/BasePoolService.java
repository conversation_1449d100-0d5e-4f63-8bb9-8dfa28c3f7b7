package com.facishare.crm.management.service.pool;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.management.SFAManagementPredefineObject;
import com.facishare.crm.management.constant.*;
import com.facishare.crm.management.enums.HighSeasEnums;
import com.facishare.crm.management.enums.LeadsPoolEnums;
import com.facishare.crm.management.service.CrmLogService;
import com.facishare.crm.management.service.MgtOpenApiMqService;
import com.facishare.crm.management.service.RecyclingRuleService;
import com.facishare.crm.management.service.model.AllocateRuleInfoModel;
import com.facishare.crm.management.service.model.AllocateRuleMemberModel;
import com.facishare.crm.management.service.model.CalculateRuleMemberWheresTaskArg;
import com.facishare.crm.management.service.model.pool.*;
import com.facishare.crm.management.task.CommonTaskService;
import com.facishare.crm.management.utils.PoolUtilServiceImpl;
import com.facishare.crm.management.utils.PrmUtils;
import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.rest.SettingLogProxy;
import com.facishare.crm.rest.dto.SettingLogModel;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.crm.sfa.predefine.service.*;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.predefine.service.model.ServiceResult;
import com.facishare.crm.sfa.predefine.service.task.RecalculateTaskService;
import com.facishare.crm.sfa.task.RecalculateProducer;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.proxy.MergeJobProxy;
import com.facishare.crm.sfa.utilities.proxy.model.MergeJobModel;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crm.sfa.utilities.util.SFABizLogUtil;
import com.facishare.crm.sfa.utilities.util.SFARestHeaderUtil;
import com.facishare.crm.sfa.utilities.util.i18n.I18NUtil;
import com.facishare.crm.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfoExt;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.config.ObjectConfig;
import com.facishare.paas.appframework.metadata.config.ObjectConfigService;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.facishare.crm.management.constant.HighSeasConstants.Field.ONLY_ALLOW_MEMBER_MOVE;
import static com.facishare.crm.management.constant.HighSeasConstants.Field.ONLY_ALLOW_MEMBER_RETURN;
import static com.facishare.crm.management.utils.RecyclingRuleModelUtils.isRecyclingRuleChange;
import static com.facishare.crm.management.utils.RecyclingRuleModelUtils.recyclingRuleExtendChange;
import static com.facishare.crm.management.utils.SFAManagamentI18NKeys.SFA_ACCOUNT_GETDESCRIPTIONFAILED;
import static com.facishare.crm.management.utils.SFAManagamentI18NKeys.SFA_CONFIG_PARAMETER_ERROR;
import static com.facishare.crm.management.utils.SFAManagamentI18NKeys.SFA_CONFIG_POOLTOPOOL;
import static com.facishare.crm.management.utils.SFAManagamentI18NKeys.SFA_TO_BE_POOL_ADMIN_NEW_RECORD;
import static com.facishare.crm.management.utils.SFAManagamentI18NKeys.SFA_TO_BE_POOL_MEMBER_NEW_RECORD;
import static com.facishare.crm.management.utils.SFAManagamentI18NKeys.SFA_TO_CANCEL_POOL_ADMIN_NEW_RECORD;
import static com.facishare.crm.management.utils.SFAManagamentI18NKeys.SFA_TO_CANCEL_POOL_MEMBER_NEW_RECORD;
import static com.facishare.crm.management.utils.SFAManagamentI18NKeys.*;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;
import static com.facishare.crm.util.CommonSqlUtils.convert2ActionContext;

@Component
@Slf4j
public class BasePoolService implements PoolService {
    @Autowired
    protected  ServiceFacade serviceFacade;
    @Autowired
    protected  ICommonSqlService commonSqlService;
    @Autowired
    protected RecyclingRuleService recyclingRuleService;
    @Autowired
    protected ObjectPoolService objectPoolService;
    @Autowired
    protected ObjectPoolServiceManager objectPoolServiceManager;
    @Autowired
    protected OrgService orgService;
    @Autowired
    protected EIEAConverter eieaConverter;
    @Autowired
    protected SettingLogProxy settingLogProxy;
    @Autowired
    private RecalculateTaskService recalculateTaskService;
    @Autowired
    protected ObjectConfigService objectConfigService;
    @Autowired
    protected MgtOpenApiMqService mgtOpenApiMqService;
    @Autowired
    protected CrmLogService crmLogService;
    @Autowired
    protected ObjectDataProxy dataProxy;
    @Autowired
    private MergeJobProxy mergeJobProxy;
    @Autowired
    private CommonTaskService taskService;
    @Autowired
    private SFARedisService distributionLock;
    @Autowired
    private PoolUtilServiceImpl poolUtilService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private CRMNotificationServiceImpl crmNotificationService;

    @Autowired
    protected ObjectDataServiceImpl objectDataService;

    @Autowired
    private RecalculateProducer recalculateProducer;
    protected static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    private static final String OBJECT_LIMIT_OVER_RULE_TABLE = "object_limit_over_rule";
    private static final String OBJECT_POOL_ID_FIELD = "object_pool_id";
    private static final String DATA_IN_OPERATION_ERROR = "sfa.data.in.operation.error";
    private static final String PARTNER_ID = "partner_id";

    @Override
    public String getApiName() {
        return null;
    }

    @Override
    public String getPoolDataApiName() {
        return null;
    }

    public String getPoolApiName(){
        return null;
    }

    @Override
    public String getPoolDataPoolIdApiName() {
        return null;
    }

    @Override
    public String getPoolUnAllocatedStatusValue() {
        return "unallocated";
    }

    protected boolean needSendNotifyWhenChangePermission() {
        return false;
    }

    @Override
    public boolean delete(ServiceContext context, PoolServiceModel.DeletePoolArg arg) {
        if (!Lists.newArrayList(SFAManagementPredefineObject.HighSeas.getApiName(),
                SFAManagementPredefineObject.LeadsPool.getApiName()).contains(arg.getApiName())) {
            throw new ValidateException(I18N.text(API_NAME_NOT_POOL_OBJ));
        }
        IObjectData data = serviceFacade.findObjectData(context.getUser(), arg.getId(), arg.getApiName());
        if (data == null) {
            throw new ValidateException(I18N.text(Data_Is_Null));
        }
        deleteBefore(context, arg, data);
        serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(Lists.newArrayList(data), context.getUser());
        //删除池成员
        deletePoolPermission(context, data.getId(), arg.getApiName());
        //删除可见字段
        deletePoolPermissionField(context, data.getId(), arg.getApiName());
        //删除分配规则
        deleteAllocateRule(context, data.getId(), arg.getApiName());
        //删除提新规则
        deleteRemindRule(context, data.getId(), HighSeasEnums.RemindRuleDataType.HighSeasRemind.getValue());
        deleteRecyclingRule(context, data.getId(), arg.getApiName());

        //删除负责人规则
        deletePoolOwnerRule(context, data.getId(), arg.getApiName());
        deleteAfter(context, arg, true, data);
        return true;
    }

    @Override
    public PoolServiceModel.PoolDataResult add(ServiceContext context, PoolServiceModel.PoolDataArg arg) {
        addBefore(context, arg);
        validatePoolDataArg(arg);
        IObjectData argData = arg.getData().toObjectData();
        initObjectData(argData, context);
        arg.setData(ObjectDataDocument.of(argData));
        IObjectData data = serviceFacade.saveObjectData(context.getUser(), argData);
        if (needSendNotifyWhenChangePermission()) {
            sendChangePermissionCRMNotify(context, arg.getAdminPoolPermissions(), arg.getMemberPoolPermissions(), arg.getApiName(),
                    data.getId(), data.get("name").toString(), Lists.newArrayList());
        }

        PoolServiceModel.PoolDataResult result = PoolServiceModel.PoolDataResult.builder().data(ObjectDataDocument.of(data)).build();
        //保存可见字段
        addPoolPermissionField(context, arg.getPoolPermissionTemplates(), data.getId(), arg.getApiName());
        //保存成员
        addPoolPermission(context, arg.getAdminPoolPermissions(),arg.getCollaboratorPoolPermissions(), arg.getMemberPoolPermissions(), data.getId(), arg.getApiName());
        //保存提醒规则
        addRemindRule(context, arg.getRemindRuleList(), data.getId(), arg.getApiName());
        //保存收回规则
        addRecyclingRule(context, arg.getRecyclingRuleList(), data.getId(), arg.getApiName());
        //保存分配规则
        addAllocateRule(context, arg.getAllocateRuleList(), data.getId(), arg.getApiName());
        //保存负责人规则
        addPoolOwnerRule(context, arg.getPoolOwnerRuleWheres(), data.getId(), arg.getApiName());
        addAfter(context, arg, result, data);
        return result;
    }

    @Override
    public ServiceResult bulkAddValidate(ServiceContext context, List<PoolServiceModel.PoolDataArg> args) {
        StringBuilder sb = new StringBuilder();
        for (PoolServiceModel.PoolDataArg arg : args) {
            try {
                addBefore(context, arg);
                validatePoolDataArg(arg);
            } catch (ValidateException e) {
                sb.append(arg.getData().get("name").toString() + "," + e.getMessage()+";\n");
            }
        }
        if (sb.length() > 0){
            throw new ValidateException(sb.toString());
        }
        return ServiceResult.ofSuccess();
    }

    @Override
    public void bulkAdd(ServiceContext context, BulkAddPoolModel model) {
        List<PoolServiceModel.PoolDataArg> args = model.getPoolDataArgs();
        int success = args.size(),failed = 0;
         String title = String.format(I18N.text("sfa.pool.bulkAdd.title"),getPoolApiName());
        String content = String.format(I18N.text("sfa.pool.bulkAdd.content"),getPoolApiName());
        String code = "0";
        StringBuilder sb = new StringBuilder();
        for (PoolServiceModel.PoolDataArg arg : args) {
            try {
                add(context,arg);
            } catch (ValidateException e) {
                failed++;
                success--;
                title = I18N.text("sfa.pool.bulkAdd.catch.title");
                sb.append(arg.getData().get("name").toString()).append(",").append(e.getMessage()).append(";");
            }catch (Exception e){
                failed++;
                success--;
                sb.append(arg.getData().get("name").toString()).append(",").append(e.getMessage()).append(";");
                log.error("bulkAdd exception:",e);
            }
        }
        if (failed > 0){
            content = String.format(I18N.text("sfa.pool.bulkAdd.count.title"),getPoolApiName(),success,failed);
            if (sb.length() > 0){
                content += sb.toString();
            }
        }

        mergeJobMessage(context,code,title,model.getJobId());

        Set<Integer> receiverIds = Sets.newHashSet(Integer.valueOf(context.getUser().getUserId()));

        String titleKey = SFA_POOL_BULKADDPOOL;
        String contentKey = SFA_POOL_BULKADDPOOLDATADONEANDFAILURE;
        if ("enterprise".equals(model.getDataFrom())){
            titleKey = SFA_POOL_BULKADDPOOL_ENTERPRISE_DESC;
            contentKey = SFA_POOL_BULKADDPOOLDATADONEANDFAILURE_ENTERPRISE;
        }


        CRMRemindRecordUtil.sendNewCRMRecord(crmNotificationService,
                context.getUser(),
                92,
                Lists.newArrayList(receiverIds),
                "",
                title,
                content,
                titleKey,
                Lists.newArrayList(CRMRemindRecordUtil.getI18ParameterKey(GetI18nKeyUtil.getDescribeDisplayNameKey(getApiName()))),
                contentKey,
                Lists.newArrayList(CRMRemindRecordUtil.getI18ParameterKey(GetI18nKeyUtil.getDescribeDisplayNameKey(getApiName())),String.valueOf(success),String.valueOf(failed),sb.toString()),
                null,
                context.getAppId()
                );
    }

    @Override
    public ServiceResult bulkUpdateValidate(ServiceContext context, List<PoolServiceModel.PoolDataArg> args) {
        StringBuilder sb = new StringBuilder();
        for (PoolServiceModel.PoolDataArg arg : args) {
            try {
                List<RecyclingRuleInfoModel> oldRecyclingRules= getRecyclingRuleListByDataId(arg.getApiName(), arg.getData().getId(), context);
                List<RemindRuleModel> oldRemindRules = getRemindRulesByDataId(arg.getData().getId(), arg.getApiName(), context);
                List<PoolPermissionModel> oldPoolPermissions = getPoolPermission(context, arg.getData().getId(), arg.getApiName());

                List<PoolPermissionModel> oldAdminList = Lists.newArrayList();
                List<PoolPermissionModel> oldMemberList = Lists.newArrayList();
                List<PoolPermissionModel> oldCollaboratorList = Lists.newArrayList();
                for (PoolPermissionModel p : oldPoolPermissions) {
                    if (PoolPermissionConstants.PermissionType.COLLABORATOR.getValue().equals(p.getPermissionType())) {
                        oldCollaboratorList.add(p);
                    } else if (p.getIsAdmin()) {
                        oldAdminList.add(p);
                    } else {
                        oldMemberList.add(p);
                    }
                }
                //参数中，除data参数外，其余参数，如果有null，则取库中旧值
                if (arg.getAdminPoolPermissions() == null) {
                    //为null表示不改动，为空数组表示置空
                    arg.setAdminPoolPermissions(oldAdminList);
                }
                if (arg.getCollaboratorPoolPermissions() == null) {
                    arg.setCollaboratorPoolPermissions(oldCollaboratorList);
                }
                if (arg.getMemberPoolPermissions() == null) {
                    arg.setMemberPoolPermissions(oldMemberList);
                }
                if (arg.getRecyclingRuleList() == null) {
                    arg.setRecyclingRuleList(oldRecyclingRules);
                }
                if (arg.getRemindRuleList() == null) {
                    arg.setRemindRuleList(oldRemindRules);
                }
                if (arg.getAllocateRuleList() == null) {
                    arg.setAllocateRuleList(getAllocateRuleByDataId(arg.getApiName(), arg.getData().getId(), context));
                }
                if (arg.getPoolPermissionTemplates()==null) {
                    arg.setPoolPermissionTemplates(getPoolPermissionTemplate(context, arg.getData().getId(), arg.getApiName()));
                }
                if (arg.getPoolOwnerRuleWheres() == null) {
                    //为null表示不改动，为空字符串表示置空
                    arg.setPoolOwnerRuleWheres(getPoolOwnerRule(context, arg.getData().getId(), arg.getApiName()));
                }

                IObjectData beforeData = serviceFacade.findObjectData(context.getUser(), arg.getData().getId(), arg.getApiName());
                if (beforeData == null) {
                    throw new ValidateException(I18N.text(Data_Is_Null));
                }
                //参数中的data，只会含有需要更新的列及值
                ObjectDataDocument newData = arg.getData();
                ObjectDataDocument oldData = ObjectDataDocument.of(beforeData);

                for (Map.Entry<String, Object> oldEntry : oldData.entrySet()) {
                    if (!newData.containsKey(oldEntry.getKey())) {
                        newData.put(oldEntry.getKey(), oldData.get(oldEntry.getKey()));
                    }
                }

                updateBefore(context, arg, oldRecyclingRules, oldRemindRules, oldPoolPermissions);
                validatePoolDataArg(arg);
            } catch (ValidateException e) {
                sb.append(arg.getData().get("name").toString()).append(",").append(e.getMessage()).append(";\n");
            }
        }
        if (sb.length() > 0) {
            throw new ValidateException(sb.toString());
        }
        return ServiceResult.ofSuccess();
    }

    @Override
    public void bulkUpdate(ServiceContext context, BulkAddPoolModel model) {
        List<PoolServiceModel.PoolDataArg> args = model.getPoolDataArgs();
        int success = args.size(), failed = 0;
        String title = String.format(I18N.text("sfa.pool.bulkUpdate.title"), getPoolApiName());
        String content = String.format(I18N.text("sfa.pool.bulkUpdate.content"), getPoolApiName());
        String code = "0";
        HashMultimap<String, String> multiMap = HashMultimap.create();
        StringBuilder sb = new StringBuilder();
        for (PoolServiceModel.PoolDataArg arg : args) {
            try {
                doUpdate(context, arg);
            } catch (ValidateException e) {
                failed++;
                success--;
                //sb.append(arg.getData().get("name").toString()).append(",").append(e.getMessage()).append(";");
                multiMap.put(e.getMessage(), arg.getData().get("name").toString());
            } catch (Exception e) {
                failed++;
                success--;
                //sb.append(arg.getData().get("name").toString()).append(",").append(e.getMessage()).append(";");
                multiMap.put(e.getMessage(), arg.getData().get("name").toString());
                log.error("bulkUpdate exception:", e);
            }
        }
        String msgPre = getNameByApiName(model.getPoolDataArgs().get(0).getApiName());
        Set<String> keySet = multiMap.keySet();
        for(String key: keySet) {
            Set<String> strings = multiMap.get(key);
            String str = Joiner.on("、").join(strings);
            sb.append(msgPre).append(str).append(I18N.text("sfa.pool.bulkUpdate.failed.reason")).append(key).append("；");
        }
        if (failed > 0) {
            content = String.format(I18N.text("sfa.pool.bulkUpdate.count.title"), getPoolApiName(), success, failed);
            if (sb.length() > 0) {
                content += sb.toString();
            }
        }
        String failLeadsPoolOrHighSeaName = multiMap.values().toString();
        String failLeadsPoolOrHighSeaReason = keySet.toString();

        mergeJobMessage(context, code, title, model.getJobId());

        Set<Integer> receiverIds = Sets.newHashSet(Integer.valueOf(context.getUser().getUserId()));

        String i18Str;
        List<String> i18ParamList;
        if (failed > 0) {
            if ("LeadsPoolObj".equals(model.getPoolDataArgs().get(0).getApiName())) {
                i18Str = SFA_POOL_BULKUPDATEPOOLDATADONEANDFAILURELEADSPOOL;
                i18ParamList = Lists.newArrayList(getPoolApiName(), String.valueOf(success), String.valueOf(failed), failLeadsPoolOrHighSeaName, failLeadsPoolOrHighSeaReason);
            } else {
                i18Str = SFA_POOL_BULKUPDATEPOOLDATADONEANDFAILUREHIGHSEA;
                i18ParamList = Lists.newArrayList(getPoolApiName(), String.valueOf(success), String.valueOf(failed), failLeadsPoolOrHighSeaName, failLeadsPoolOrHighSeaReason);
            }
        } else {
            i18Str = SFA_POOL_BULKUPDATEPOOLDATADONEANDFAILURE;
            i18ParamList = Lists.newArrayList(getPoolApiName(), String.valueOf(success), String.valueOf(failed), sb.toString());
        }
        CRMRemindRecordUtil.sendNewCRMRecord(crmNotificationService,
                context.getUser(),
                92,
                Lists.newArrayList(receiverIds),
                "",
                title,
                content,
                SFA_POOL_BULKUPDATEPOOL,
                Lists.newArrayList(getPoolApiName()),
                i18Str,
                i18ParamList,
                null,
                context.getAppId()
        );
    }

    @Override
    public List<String> getPoolMembers(ServiceContext context, String apiName, String poolId, String memberType) {
        List<PoolPermissionModel> poolPermissions = getPoolPermission(context, poolId, apiName);
        List<PoolPermissionModel> filterPoolPermissions = Lists.newArrayList();
        switch (memberType) {
            case "Admin":
                filterPoolPermissions.addAll(poolPermissions.stream().filter(PoolPermissionModel::getIsAdmin).collect(Collectors.toList()));
                break;
            case "Member":
                filterPoolPermissions.addAll(poolPermissions.stream().filter(x -> !x.getIsAdmin()).collect(Collectors.toList()));
                break;
            case "ALL":
                filterPoolPermissions.addAll(poolPermissions);
                break;
        }
        Set<String> memberIds = getMemberIds(filterPoolPermissions, context);
        if(context.getUser().isOutUser()) {
            List<PoolPermissionModel> outerEmployeePermissions = filterPoolPermissions.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.OuterEmployee.getValue())).collect(Collectors.toList());
            memberIds.addAll(outerEmployeePermissions.stream().map(PoolPermissionModel::getDataId).collect(Collectors.toList()));
            if(filterPoolPermissions.stream().anyMatch(x -> x.getType().equals(HighSeasEnums.PermissionType.OuterEnterprise.getValue())
                    && x.getDataId().equals(context.getUser().getOutTenantId()))) {
                memberIds.add(context.getUser().getOutUserId());
            }
        }
        return Lists.newArrayList(memberIds);
    }

    private String getNameByApiName(String apiName){
        if("LeadsPoolObj".equals(apiName)){
            return I18N.text("sfa.LeadsPoolObj.fail.title");
        } else {
            return I18N.text("sfa.HighSeasObj.fail.title");
        }
    }

    private void mergeJobMessage(ServiceContext context,String code, String message, String jobId) {
        Map<String, String> header = SFARestHeaderUtil.getCrmHeader(context.getTenantId(), context.getUser().getUpstreamOwnerIdOrUserId());
        MergeJobModel.Status status = mergeJobProxy.mergeComplete(
                MergeJobModel.MergeComplete.builder().
                        code(code).
                        message(message).
                        jobId(jobId).
                        build(),
                header
        );
        log.info("mergeJobMessage:{},{},{}",jobId,status,message);
    }

    @Override
    public PoolServiceModel.PoolDataResult update(ServiceContext context, PoolServiceModel.PoolDataArg arg) {
        validatePoolDataArg(arg);
        IObjectData beforeData = serviceFacade.findObjectData(context.getUser(), arg.getData().getId(), arg.getApiName());
        List<RecyclingRuleInfoModel> oldRecyclingRules = Lists.newArrayList();
        List<RemindRuleModel> oldRemindRules = Lists.newArrayList();
        List<PoolPermissionModel> oldPoolPermissions = Lists.newArrayList();
        oldRecyclingRules = getRecyclingRuleListByDataId(arg.getApiName(), arg.getData().getId(), context);
        oldPoolPermissions = getPoolPermission(context, arg.getData().getId(), arg.getApiName());
        oldRemindRules = getRemindRulesByDataId(arg.getData().getId(), arg.getApiName(), context);

        fillRemindRule(arg.getRemindRuleList(),arg.getData());
        fillRecyclingRule(arg.getRecyclingRuleList(),arg.getData());

        updateBefore(context, arg, oldRecyclingRules, oldRemindRules, oldPoolPermissions);
        arg.getData().put(DBRecord.LAST_MODIFIED_TIME, System.currentTimeMillis());
        arg.getData().put(DBRecord.LAST_MODIFIED_BY, Lists.newArrayList(context.getUser().getUpstreamOwnerIdOrUserId()));
        IObjectData data = serviceFacade.updateObjectData(context.getUser(), arg.getData().toObjectData());
        PoolServiceModel.PoolDataResult result = PoolServiceModel.PoolDataResult.builder().data(ObjectDataDocument.of(data)).build();
        if (needSendNotifyWhenChangePermission()) {
            sendChangePermissionCRMNotify(context, arg.getAdminPoolPermissions(), arg.getMemberPoolPermissions(), arg.getApiName(),
                    data.getId(), data.get("name").toString(), oldPoolPermissions);
        }
        //删除池成员
        deletePoolPermission(context, data.getId(), arg.getApiName());
        //删除可见字段
        deletePoolPermissionField(context, data.getId(), arg.getApiName());
        //保存可见字段
        addPoolPermissionField(context, arg.getPoolPermissionTemplates(), data.getId(), arg.getApiName());
        //保存成员
        addPoolPermission(context, arg.getAdminPoolPermissions(),arg.getCollaboratorPoolPermissions(), arg.getMemberPoolPermissions(), data.getId(), arg.getApiName());

        if (remindRuleChanged(arg.getRemindRuleList(), arg.getApiName(), oldRemindRules)) {
            //删除提醒规则
            deleteRemindRule(context, data.getId(), HighSeasEnums.RemindRuleDataType.HighSeasRemind.getValue());
            //保存提醒规则
            addRemindRule(context, arg.getRemindRuleList(), data.getId(), arg.getApiName());
            recalculateTaskService.sendChangeRuleMsg(context.getTenantId(), data.getId(), arg.getApiName(), ActionCodeEnum.CHANGE_REMIND);
        }

        updateAllocateRule(context, arg.getAllocateRuleList(), data.getId(), arg.getApiName());
        //保存负责人规则
        addPoolOwnerRule(context, arg.getPoolOwnerRuleWheres(), data.getId(), arg.getApiName());

        updateAfter(context, arg, result, beforeData, oldRecyclingRules, oldRemindRules, oldPoolPermissions);

        //删除回收规则再新建，发送重算任务
        UpdateRecyclingRule(arg.getApiName(), arg.getRecyclingRuleList(),
                result.getData().getId(), HighSeasEnums.RecyclingRuleDataType.HighSeas, context, oldRecyclingRules);
        return result;
    }

    /**
     * It fills the skip_holidays field in the RemindRuleModel with the value of the skip_holidays field in the
     * ObjectDataDocument.
     *
     * @param remindRuleList The list of RemindRuleModel objects that will be filled with the data from the object.
     * @param data The data object that is being processed.
     */
    private void fillRemindRule(List<RemindRuleModel> remindRuleList, ObjectDataDocument data) {
        if (data.get(RecyclingRuleConstans.Field.SKIP_HOLIDAYS) == null || CollectionUtils.empty(remindRuleList)) {
            return;
        }
        remindRuleList.forEach(remindRule -> {
            remindRule.setSkipHolidays(data.get(RecyclingRuleConstans.Field.SKIP_HOLIDAYS) != null && (boolean) data.get(RecyclingRuleConstans.Field.SKIP_HOLIDAYS));
        });
    }

    /**
     * It fills the skip_holidays field in the recyclingRuleLists.
     *
     * @param recyclingRuleLists The list of recycling rules that will be filled.
     * @param data The object data document.
     */
    private void fillRecyclingRule(List<RecyclingRuleInfoModel> recyclingRuleLists, ObjectDataDocument data) {
        if (data.get(RecyclingRuleConstans.Field.SKIP_HOLIDAYS)  == null || CollectionUtils.empty(recyclingRuleLists)) {
            return;
        }
        recyclingRuleLists.forEach(recyclingRuleInfoModel -> {
            recyclingRuleInfoModel.setSkipHolidays(data.get(RecyclingRuleConstans.Field.SKIP_HOLIDAYS) != null && (boolean)data.get(RecyclingRuleConstans.Field.SKIP_HOLIDAYS));
        });
    }

    private PoolServiceModel.PoolDataResult doUpdate(ServiceContext context, PoolServiceModel.PoolDataArg arg) {
        List<RecyclingRuleInfoModel> oldRecyclingRules = getRecyclingRuleListByDataId(arg.getApiName(), arg.getData().getId(), context);
        List<RemindRuleModel> oldRemindRules = getRemindRulesByDataId(arg.getData().getId(), arg.getApiName(), context);
        List<PoolPermissionModel> oldPoolPermissions = getPoolPermission(context, arg.getData().getId(), arg.getApiName());
        List<AllocateRuleInfoModel> oldAllocateRuleByDataId = getAllocateRuleByDataId(arg.getApiName(), arg.getData().getId(), context);

        List<PoolPermissionModel> oldAdminList = Lists.newArrayList();
        List<PoolPermissionModel> oldMemberList = Lists.newArrayList();
        List<PoolPermissionModel> oldCollaboratorList = Lists.newArrayList();

        for (PoolPermissionModel p : oldPoolPermissions) {
            if (PoolPermissionConstants.PermissionType.COLLABORATOR.getValue().equals(p.getPermissionType())) {
                oldCollaboratorList.add(p);
            } else if (p.getIsAdmin()) {
                oldAdminList.add(p);
            } else {
                oldMemberList.add(p);
            }
        }
        //参数中，除data参数外，其余参数，如果有null，则取库中旧值
        if (arg.getAdminPoolPermissions() == null) {
            //为null表示不改动，为空数组表示置空
            arg.setAdminPoolPermissions(oldAdminList);
        }
        if (arg.getCollaboratorPoolPermissions() == null) {
            arg.setCollaboratorPoolPermissions(oldCollaboratorList);
        }
        if (arg.getMemberPoolPermissions() == null) {
            arg.setMemberPoolPermissions(oldMemberList);
        }
        if (arg.getRecyclingRuleList() == null) {
            arg.setRecyclingRuleList(oldRecyclingRules);
        }
        if (arg.getAllocateRuleList() == null) {
            arg.setAllocateRuleList(oldAllocateRuleByDataId);
        }
        if (arg.getPoolOwnerRuleWheres() == null) {
            //为null表示不改动，为空字符串表示置空
            arg.setPoolOwnerRuleWheres(getPoolOwnerRule(context, arg.getData().getId(), arg.getApiName()));
        }

        arg.getData().put(DBRecord.LAST_MODIFIED_TIME, System.currentTimeMillis());
        arg.getData().put(DBRecord.LAST_MODIFIED_BY, Lists.newArrayList(context.getUser().getUpstreamOwnerIdOrUserId()));
        IObjectData beforeData = serviceFacade.findObjectData(context.getUser(), arg.getData().getId(), arg.getApiName());
        if (beforeData == null) {
            throw new ValidateException(I18N.text(Data_Is_Null));
        }
        //参数中的data，只会含有需要更新的列及值
        ObjectDataDocument newData = arg.getData();
        ObjectDataDocument oldData = ObjectDataDocument.of(beforeData);

        for (Map.Entry<String, Object> oldEntry : oldData.entrySet()) {
            if (!newData.containsKey(oldEntry.getKey())) {
                newData.put(oldEntry.getKey(), oldData.get(oldEntry.getKey()));
            }
        }

        updateBefore(context, arg, oldRecyclingRules, oldRemindRules, oldPoolPermissions);
        validatePoolDataArg(arg);

        IObjectData data = serviceFacade.updateObjectData(context.getUser(), newData.toObjectData());//更新池数据
        PoolServiceModel.PoolDataResult result = PoolServiceModel.PoolDataResult.builder().data(ObjectDataDocument.of(data)).build();
        if (needSendNotifyWhenChangePermission()) {
            sendChangePermissionCRMNotify(context, arg.getAdminPoolPermissions(), arg.getMemberPoolPermissions(), arg.getApiName(),
                    data.getId(), data.get("name").toString(), oldPoolPermissions);
        }

        //删除池成员
        deletePoolPermission(context, data.getId(), arg.getApiName());
        //保存成员
        addPoolPermission(context, arg.getAdminPoolPermissions(), arg.getCollaboratorPoolPermissions(), arg.getMemberPoolPermissions(), data.getId(), arg.getApiName());

        if (arg.getPoolPermissionTemplates() != null) {
            //删除可见字段
            deletePoolPermissionField(context, data.getId(), arg.getApiName());
            //保存可见字段
            addPoolPermissionField(context, arg.getPoolPermissionTemplates(), data.getId(), arg.getApiName());
        }

        if (remindRuleChanged(arg.getRemindRuleList(), arg.getApiName(), oldRemindRules)) {
            //删除提醒规则
            deleteRemindRule(context, data.getId(), HighSeasEnums.RemindRuleDataType.HighSeasRemind.getValue());
            //保存提醒规则
            addRemindRule(context, arg.getRemindRuleList(), data.getId(), arg.getApiName());
            recalculateTaskService.sendChangeRuleMsg(context.getTenantId(), data.getId(), arg.getApiName(), ActionCodeEnum.CHANGE_REMIND);
        }

        updateAllocateRule(context, arg.getAllocateRuleList(), data.getId(), arg.getApiName());

        //保存负责人规则
        addPoolOwnerRule(context, arg.getPoolOwnerRuleWheres(), data.getId(), arg.getApiName());

        updateAfter(context, arg, result, beforeData, oldRecyclingRules, oldRemindRules, oldPoolPermissions);

        //删除回收规则再新建，发送重算任务
        UpdateRecyclingRule(arg.getApiName(), arg.getRecyclingRuleList(),
                result.getData().getId(), HighSeasEnums.RecyclingRuleDataType.HighSeas, context, oldRecyclingRules);
        return result;
    }

    private boolean remindRuleChanged(List<RemindRuleModel> remindRuleList, String apiName, List<RemindRuleModel> oldRemindRules) {

        if (CollectionUtils.empty(oldRemindRules) && CollectionUtils.empty(remindRuleList)) {
            return false;
        } else if (CollectionUtils.empty(oldRemindRules) && CollectionUtils.notEmpty(remindRuleList)) {
            return true;
        } else if (CollectionUtils.empty(remindRuleList) && CollectionUtils.notEmpty(oldRemindRules)) {
            return true;
        } else if (remindRuleList.size() != oldRemindRules.size()) {
            return true;
        } else {
            for (RemindRuleModel newRule : remindRuleList) {
                if ("".equals(newRule.getId())) {
                    return true;
                }
                Optional<RemindRuleModel> anyFind = oldRemindRules.stream().filter(x -> x.getDealDays().equals(newRule.getDealDays()) && x.getFollowUpDays().equals(newRule.getFollowUpDays())).findAny();
                if (!anyFind.isPresent()) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public PoolServiceModel.PoolDataResult detail(ServiceContext context, PoolServiceModel.PoolIdArg arg) {
        if (!Lists.newArrayList(SFAManagementPredefineObject.HighSeas.getApiName(),
                SFAManagementPredefineObject.LeadsPool.getApiName()).contains(arg.getApiName())) {
            throw new ValidateException(I18N.text(API_NAME_NOT_POOL_OBJ));
        }
        IObjectData data = serviceFacade.findObjectData(context.getUser(), arg.getId(), arg.getApiName());
        if (data == null) {
            throw new ValidateException(I18N.text(Data_Is_Null));
        }

        if (data.get(LeadsPoolConstants.Field.Leads_Count) == null) {
            data.set(LeadsPoolConstants.Field.Leads_Count, 0);
        }
//        Map<String, Integer> poolTotalCount = getPoolTotalCount(context.getTenantId(), Lists.newArrayList(data.getId()));
//        if(SFAManagementPredefineObject.HighSeas.getApiName().equals(arg.getApiName())) {
//            if (poolTotalCount.containsKey(data.getId())) {
//                data.set("accounts_count", poolTotalCount.get(data.getId()));
//            } else {
//                data.set("accounts_count", "0");
//            }
//        } else {
//            if (poolTotalCount.containsKey(data.getId())) {
//                data.set(LeadsPoolConstants.Field.Leads_Count, poolTotalCount.get(data.getId()));
//            } else {
//                data.set(LeadsPoolConstants.Field.Leads_Count, "0");
//            }
//        }
        if (data.get(ONLY_ALLOW_MEMBER_MOVE) == null) {
            data.set(ONLY_ALLOW_MEMBER_MOVE, false);
        } else if (data.get(ONLY_ALLOW_MEMBER_RETURN) == null) {
            data.set(ONLY_ALLOW_MEMBER_RETURN, false);
        }
        context.setAttribute(I18NUtil.IS_REPLACE_I18N,true);
        PoolServiceModel.PoolDataResult result = PoolServiceModel.PoolDataResult.builder().build();
        result.setPoolPermissionTemplates(getPoolPermissionTemplate(context, arg.getId(), arg.getApiName()));
        List<PoolPermissionModel> permissionModels = getPoolPermission(context, arg.getId(), arg.getApiName());
        result.setAdminPoolPermissions(permissionModels.stream().filter(x -> x.getIsAdmin() && !PoolPermissionConstants.PermissionType.COLLABORATOR.getValue().equals(x.getPermissionType())).collect(Collectors.toList()));
        result.setMemberPoolPermissions(permissionModels.stream().filter(x -> !x.getIsAdmin()).collect(Collectors.toList()));
        result.setCollaboratorPoolPermissions(permissionModels.stream().filter(x -> PoolPermissionConstants.PermissionType.COLLABORATOR.getValue().equals(x.getPermissionType())).collect(Collectors.toList()));
        List<RecyclingRuleInfoModel> recyclingRuleListByDataId = getRecyclingRuleListByDataId(arg.getApiName(), data.getId(), context);
        List<RemindRuleModel> remindRulesByDataId = getRemindRulesByDataId(data.getId(), arg.getApiName(), context);
        List<AllocateRuleInfoModel> allocateRuleByDataId = getAllocateRuleByDataId(arg.getApiName(), arg.getId(), context);
        result.setRecyclingRuleList(recyclingRuleListByDataId);
        result.setRemindRuleList(remindRulesByDataId);
        result.setAllocateRuleList(allocateRuleByDataId);
        data.set("role_pool", 0);
        if (!context.getUser().isOutUser() && result.getAdminPoolPermissions().stream().anyMatch(x -> x.getDataId().equals(context.getUser().getUpstreamOwnerIdOrUserId()) && x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue()))) {
            data.set("role_pool", 1);
        }
        if (context.getUser().isOutUser() && result.getAdminPoolPermissions().stream().anyMatch(x -> x.getType().equals(HighSeasEnums.PermissionType.OuterEmployee.getValue()) &&
                x.getDataId().equals(context.getUser().getOutUserId()))) {
            data.set("role_pool", 1);
        }
        if (!context.getUser().isOutUser() && result.getAdminPoolPermissions().stream().anyMatch(x -> x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue()))) {
            List<String> adminDeptMember = orgService.getMembersByDeptIds(context.getUser(), result.getAdminPoolPermissions().stream()
                    .filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())).map(c -> c.getDataId()).collect(Collectors.toList()));
            if (adminDeptMember.contains(context.getUser().getUpstreamOwnerIdOrUserId())) {
                data.set("role_pool", 1);
            }
        }
        if (!context.getUser().isOutUser() && result.getAdminPoolPermissions().stream().anyMatch(x -> x.getType().equals(HighSeasEnums.PermissionType.UserGroup.getValue()))) {
            List<String> userGroupIds = result.getAdminPoolPermissions().stream()
                    .filter(x -> x.getType().equals(HighSeasEnums.PermissionType.UserGroup.getValue())).map(c -> c.getDataId()).collect(Collectors.toList());
            userGroupIds = CommonBizOrgUtils.filterStopUserGroup(context.getTenantId(), userGroupIds);
            List<String> adminMember = CommonBizOrgUtils.batchGetMembersByUserGroupIds(context.getTenantId(), userGroupIds);
            if (adminMember.contains(context.getUser().getUpstreamOwnerIdOrUserId())) {
                data.set("role_pool", 1);
            }
        }
        if (!context.getUser().isOutUser() && result.getAdminPoolPermissions().stream().anyMatch(x -> x.getType().equals(HighSeasEnums.PermissionType.UserRole.getValue()))) {
            Set<String> adminMember = CommonBizOrgUtils.batchGetRoleUsersByRoleIds(context.getTenantId(), result.getAdminPoolPermissions().stream()
                    .filter(x -> x.getType().equals(HighSeasEnums.PermissionType.UserRole.getValue())).map(c -> c.getDataId()).collect(Collectors.toList()));
            if (adminMember.contains(context.getUser().getUpstreamOwnerIdOrUserId())) {
                data.set("role_pool", 1);
            }
        }
        // 在协作人员里面
        if (CollectionUtils.notEmpty(result.getCollaboratorPoolPermissions())) {
            data.set("is_collaborator", true);
        }else {
            data.set("is_collaborator", false);
        }
        result.setData(ObjectDataDocument.of(data));
        String poolOwnerRule = getPoolOwnerRule(context, data.getId(), arg.getApiName());
        result.setPoolOwnerRuleWheres(poolOwnerRule);
        IObjectData objectData = result.getData().toObjectData();
        PoolEmptyRule emptyRule = new PoolEmptyRule.Builder().poolData(Lists.newArrayList(objectData)).build();
        emptyRule.fillEmptyRule();

        List<PoolServiceModel.OuterInfo> employeeInfoList = Lists.newArrayList();
        List<PoolServiceModel.OuterInfo> enterpriseInfoList = Lists.newArrayList();
        handleOuterInfo(context.getUser(), data, permissionModels, result.getAllocateRuleList(), employeeInfoList, enterpriseInfoList);
        result.setOuterEmployee(employeeInfoList);
        result.setOuterEnterprise(enterpriseInfoList);
        result.setAreaList(getAreaList(context,recyclingRuleListByDataId,allocateRuleByDataId,poolOwnerRule));
        return result;
    }

    private List<Map> getAreaList(ServiceContext context,List<RecyclingRuleInfoModel> recyclingRuleInfoModels,List<AllocateRuleInfoModel> allocateRuleInfoModels,
                                  String poolOwnerRuleWheres){
        List<Map> areaList = new ArrayList<>();
        if (CollectionUtils.notEmpty(recyclingRuleInfoModels)) {
            for (RecyclingRuleInfoModel recyclingRuleInfoModel : recyclingRuleInfoModels) {
                if (recyclingRuleInfoModel.getWheres() != null) {
                    areaList.addAll(poolUtilService.getAreaList(context,recyclingRuleInfoModel.getWheres()));
                }
            }
        }
        if (CollectionUtils.notEmpty(allocateRuleInfoModels)) {
            for (AllocateRuleInfoModel allocateRuleInfoModel : allocateRuleInfoModels) {
                if (allocateRuleInfoModel.getWheres() != null) {
                    areaList.addAll(poolUtilService.getAreaList(context,allocateRuleInfoModel.getWheres()));
                }
            }
        }
        areaList.addAll(poolUtilService.getAreaList(context,poolOwnerRuleWheres));
        return areaList;
    }

    @Override
    public List<IObjectData> getPoolIdsToUser(ServiceContext context, String apiName, Boolean ignoreCount) {
        boolean outUser = context.getUser().isOutUser();
        List<IObjectData> allPoolData = getObjectPoolListByUser(context, apiName);


        List<IObjectData> result = Lists.newArrayList();

        List<String> allPoolIds = allPoolData.stream().map(x -> x.getId()).collect(Collectors.toList());
        Map<String, Integer> tempUnAllocatedCountMap = Maps.newHashMap();
        Map<String, Integer> tempPoolCountMap = Maps.newHashMap();
        if (ignoreCount == null || Boolean.FALSE.equals(ignoreCount)) {
            tempUnAllocatedCountMap = getPoolUnAllocatedCount(context.getTenantId(), allPoolIds);
            tempPoolCountMap = getPoolTotalCount(context.getTenantId(), allPoolIds);
        }
        Map<String, Integer> unAllocatedCountMap = tempUnAllocatedCountMap;
        Map<String, Integer> poolCountMap = tempPoolCountMap;
        allPoolData.forEach(a -> {
            if (outUser) {
                a.set("allow_member_view_log", false);
            }
            if (a.get("permission_type") != null &&  PoolPermissionConstants.PermissionType.COLLABORATOR.getValue().equals(a.get("permission_type", String.class))) {
                a.set("role_pool", HighSeasEnums.RoleType.ADMIN.getValue());
                a.set("allow_member_view_log", true);
                a.set("is_collaborator", true);
            } else if (a.get("is_pool_admin", Boolean.class)) {
                a.set("role_pool", HighSeasEnums.RoleType.ADMIN.getValue());
                a.set("allow_member_view_log", true);
                a.set("is_collaborator", false);
            } else {
                a.set("role_pool", HighSeasEnums.RoleType.MEMBER.getValue());
                a.set("is_collaborator", false);
            }
            if (unAllocatedCountMap.containsKey(a.getId())) {
                a.set("un_allocated_accounts_count", unAllocatedCountMap.get(a.getId()));
            } else {
                a.set("un_allocated_accounts_count", 0);
            }
            if (poolCountMap.containsKey(a.getId())) {
                if (SFAManagementPredefineObject.LeadsPool.getApiName().equals(apiName)) {
                    a.set(LeadsPoolConstants.Field.Leads_Count, poolCountMap.get(a.getId()));
                } else {
                    a.set(HighSeasConstants.Field.ACCOUNTS_COUNT, poolCountMap.get(a.getId()));
                }
            }
            result.add(a);
        });
        List<IObjectData> newResult = handleFunction(context.getUser(),result,apiName);
        return newResult;
    }

    protected List<IObjectData> getObjectPoolListByUser(ServiceContext context, String apiName) {
        List<IObjectData> result = Lists.newArrayList();
        boolean outUser = context.getUser().isOutUser();
        String tenantId = context.getTenantId();
        String outTenantId = "";
        if (outUser) {
            outTenantId = context.getUser().getOutTenantId();
        }
        String userId = "";
        if (context.getUser().isOutUser()) {
            userId = context.getUser().getOutUserId();
        } else {
            userId = context.getUser().getUpstreamOwnerIdOrUserId();
        }

        try {
            String queryString = String.format("SELECT * FROM biz_pool_permission WHERE tenant_id='%s' " +
                    "AND object_api_name='%s' AND is_deleted=0 ", tenantId, apiName);

            List<String> userFilters = Lists.newArrayList();
            userFilters.add(String.format("(data_id='%s' AND type IN ('%S', '%s'))", userId,
                    ObjectPoolPermission.ObjectPoolMemberType.EMPLOYEE.getValue(), ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue()));

            if (StringUtils.isNotEmpty(outTenantId)) {
                userFilters.add(String.format("(data_id='%s' AND type='%s')", outTenantId,
                        ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue()));
            }

            List<String> deptIds = CommonBizOrgUtils.getAllSuperDepartmentByUserId(tenantId, userId);
            if (CollectionUtils.notEmpty(deptIds)) {
                String idString = LeadsUtils.getListQuerySql(deptIds);
                userFilters.add(String.format("(data_id IN (%s) AND type='%s')", idString,
                        ObjectPoolPermission.ObjectPoolMemberType.CIRCLE.getValue()));
            }

            List<String> userGroupIds = CommonBizOrgUtils.getUserGroupIdsByMemberId(tenantId, userId);
            if (CollectionUtils.notEmpty(userGroupIds)) {
                String idString = LeadsUtils.getListQuerySql(userGroupIds);
                userFilters.add(String.format("(data_id IN (%s) AND type='%s')", idString,
                        ObjectPoolPermission.ObjectPoolMemberType.USERGROUP.getValue()));
            }

            List<String> userRoleIds = CommonBizOrgUtils.getUserRoleIdsByMemberId(tenantId, userId);
            if (CollectionUtils.notEmpty(userRoleIds)) {
                String idString = LeadsUtils.getListQuerySql(userRoleIds);
                userFilters.add(String.format("(data_id IN (%s) AND type='%s')", idString,
                        ObjectPoolPermission.ObjectPoolMemberType.USERROLE.getValue()));
            }

            String userFilter = String.join(" OR ", userFilters);

            if (StringUtils.isNotBlank(userFilter)) {
                queryString += String.format(" AND (%s)", userFilter);
            }
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);

            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            Set<String> poolIds = queryResult.stream().map(this::getObjectPoolPermissionPoolId).collect(Collectors.toSet());
            if (CollectionUtils.empty(poolIds)) {
                return result;
            }
            List<IObjectData> poolList = serviceFacade.findObjectDataByIds(convert2ActionContext(context), Lists.newArrayList(poolIds), apiName);
            if (CollectionUtils.empty(poolList)) {
                return result;
            }
            List<Map> admins = queryResult.stream().filter(x -> getObjectPoolPermissionIsAdmin(x)).collect(Collectors.toList());
            List<Map> collaborators = queryResult.stream().filter(x -> getObjectPoolPermissionIsCollaborator(x)).collect(Collectors.toList());

            Set<String> adminPoolIds = admins.stream().map(this::getObjectPoolPermissionPoolId).collect(Collectors.toSet());
            Set<String> collaboratorPoolIds = collaborators.stream().map(this::getObjectPoolPermissionPoolId).collect(Collectors.toSet());
            poolList.forEach(p -> {
                if (collaboratorPoolIds.contains(p.getId())){
                    p.set("permission_type",PoolPermissionConstants.PermissionType.COLLABORATOR.getValue());
                    p.set("is_pool_admin", true);
                    result.add(p);
                }else if (adminPoolIds.contains(p.getId())) {
                    p.set("permission_type",PoolPermissionConstants.PermissionType.ADMIN.getValue());
                    p.set("is_pool_admin", true);
                    result.add(p);
                } else if (p.get("is_visible_to_member", Boolean.class)) {
                    p.set("permission_type",PoolPermissionConstants.PermissionType.MEMBER.getValue());
                    p.set("is_pool_admin", false);
                    result.add(p);
                }
            });
        } catch (Exception e) {
            log.error("getObjectPoolListByUser error", e);
        }

        return result;
    }

    private boolean getObjectPoolPermissionIsAdmin(Map x) {
        return ObjectUtil.getBooleanValue(x, "is_admin", false);
    }

    private boolean getObjectPoolPermissionIsCollaborator(Map x) {
        return PoolPermissionConstants.PermissionType.COLLABORATOR.getValue().equals(x.get("permission_type"));
    }

    private String getObjectPoolPermissionPoolId(Map x) {
        return ObjectUtil.getStringValue(x, "pool_id", "");
    }

    protected void addBefore(ServiceContext context, PoolServiceModel.PoolDataArg arg) {
        if(CollectionUtils.notEmpty(arg.getRecyclingRuleList())){
            arg.getRecyclingRuleList().forEach(x->{
                x.setSkipHolidays(arg.getData().get(RecyclingRuleConstans.Field.SKIP_HOLIDAYS) != null && (Boolean) arg.getData().get(RecyclingRuleConstans.Field.SKIP_HOLIDAYS));
            });
        }
    }

    private IObjectDescribe validatePoolItemDescribeAndField(ServiceContext context) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), getPoolDataApiName());
        if (objectDescribe == null) {
            throw new ValidateException("describe is null");
        }
        return objectDescribe;
    }

    protected void addAfter(ServiceContext context, PoolServiceModel.PoolDataArg arg, PoolServiceModel.PoolDataResult rst, IObjectData data) {
        String describeDisplayNameKey = GetI18nKeyUtil.getDescribeDisplayNameKey(arg.getApiName());
        String defMsg = String.format("新增%s:%s", I18N.text(describeDisplayNameKey), data.getName()); // ignoreI18n
        InternationalItem internationalItem = InternationalItem.builder()
                .defaultInternationalValue(defMsg)
                .internationalKey("sfa.audit.log.leads.pool.add.msg")
                .internationalParameters(Lists.newArrayList(describeDisplayNameKey, data.getName()))
                .build();
        serviceFacade.logInternational(context.getUser(), EventType.ADD, ActionType.Add, arg.getApiName(), arg.getApiName(), defMsg, internationalItem);
    }

    protected void updateBefore(ServiceContext context, PoolServiceModel.PoolDataArg arg, List<RecyclingRuleInfoModel> oldRecyclingRules,
                                List<RemindRuleModel> oldRemindRules, List<PoolPermissionModel> oldPoolPermissions) {
//        poolItemDescribe = validatePoolItemDescribeAndField(context, arg);
        validateUpdateRecyclingRule(arg, oldRecyclingRules);

        IObjectData data = serviceFacade.findObjectData(context.getUser(), arg.getData().getId(), arg.getData().toObjectData().getDescribeApiName());

        if (data == null) {
            throw new ValidateException(I18N.text(Data_Is_Null));
        }
    }

    protected void updateAfter(ServiceContext context, PoolServiceModel.PoolDataArg arg, PoolServiceModel.PoolDataResult rst,
                               IObjectData oldData, List<RecyclingRuleInfoModel> oldRecyclingRules,
                               List<RemindRuleModel> oldRemindRules, List<PoolPermissionModel> oldPoolPermissions) {
        IObjectData data = serviceFacade.findObjectData(context.getUser(), arg.getData().getId(), arg.getData().toObjectData().getDescribeApiName());
        List<IObjectData> dataList = ObjectDataDocument.fillOldData(Lists.newArrayList(data), Lists.newArrayList(oldData));
        addLog(context, EventType.MODIFY, ActionType.MODIFY, dataList, "");
    }

    protected void deleteBefore(ServiceContext context, PoolServiceModel.DeletePoolArg arg, IObjectData data) {

        if (isBindRecyclingRule(arg.getId(), arg.getApiName(), context)) {
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.Associated_With_Recycling_Rules));
        }
        if (isBindLimitOverRule(arg.getId(), getPoolDataApiName(), context)) {
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.Associated_With_Inventory_Rule));
        }
    }

    // 审计日志
    protected void deleteAfter(ServiceContext context, PoolServiceModel.DeletePoolArg arg, Boolean rst, IObjectData data) {
        String describeDisplayNameKey = GetI18nKeyUtil.getDescribeDisplayNameKey(arg.getApiName());
        String defMsg = String.format("删除%s:%s", I18N.text(describeDisplayNameKey), data.getName()); // ignoreI18n
        InternationalItem internationalItem = InternationalItem.builder()
                .defaultInternationalValue(defMsg)
                .internationalKey("sfa.audit.log.leads.pool.delete.msg")
                .internationalParameters(Lists.newArrayList(describeDisplayNameKey, data.getName()))
                .build();
        serviceFacade.logInternational(context.getUser(), EventType.DELETE, ActionType.Delete, arg.getApiName(), arg.getApiName(), defMsg, internationalItem);
    }

    protected void addLog(ServiceContext context, EventType eventType, ActionType actionType, List<IObjectData> dataList, String message) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), getApiName());
        serviceFacade.logWithCustomMessage(context.getUser(), eventType, actionType, objectDescribe, dataList, message);
    }

    /**
     * 保存可见字段
     *
     * @param context
     * @param templates
     * @param poolId
     * @param apiName
     */
    protected void addPoolPermissionField(ServiceContext context, List<PoolPermissionTemplateModel> templates, String poolId, String apiName) {
        if (CollectionUtils.empty(templates)){
            return;
        }
        List<Map<String, Object>> toInsertData = Lists.newArrayList();
        List<String> fieldNames = templates.stream().map(x -> x.getFieldApiName()).collect(Collectors.toList());
        String finalApiName = apiName.equals(SFAManagementPredefineObject.HighSeas.getApiName()) ? "AccountObj" : "LeadsObj";
        List<String> blackList = SFAManagementPredefineObject.HighSeas.getApiName().equals(apiName) ? PoolPublicFieldBlackList.HighSeasList : PoolPublicFieldBlackList.LeadsPoolList;
        IObjectDescribe poolItemDescribe = validatePoolItemDescribeAndField(context);
        poolItemDescribe.getFieldDescribes().forEach(field -> {
            if (fieldNames.contains(field.getApiName()) && !blackList.contains(field.getApiName())) {
                Map<String, Object> item = Maps.newHashMap();
                String itemId = serviceFacade.generateId();
                item.put(Tenantable.TENANT_ID, context.getTenantId());
                item.put(PoolPermissionTemplateConstants.Field.Field_Name, field.getApiName());
                item.put(PoolPermissionTemplateConstants.Field.Is_Visible, false);
                item.put(PoolPermissionTemplateConstants.Field.Pool_Id, poolId);
                item.put(PoolPermissionTemplateConstants.Field.Object_Api_Name, finalApiName);
                item.put(PoolPermissionTemplateConstants.Field.Id, itemId);
                item.put(PoolPermissionTemplateConstants.Field.Created_By, context.getUser().getUpstreamOwnerIdOrUserId());
                item.put(PoolPermissionTemplateConstants.Field.Create_Time, System.currentTimeMillis());
                item.put(PoolPermissionTemplateConstants.Field.Last_Modified_By, context.getUser().getUpstreamOwnerIdOrUserId());
                item.put(PoolPermissionTemplateConstants.Field.Last_Modified_Time, System.currentTimeMillis());
                item.put(PoolPermissionTemplateConstants.Field.Is_Delete, 0);
                toInsertData.add(item);
            }
        });
        if (CollectionUtils.notEmpty(toInsertData)) {
            try {
                commonSqlService.insert(HighSeasConstants.TableName.POOL_PERMISSION_FIELD, toInsertData, convert2ActionContext(context));
            } catch (MetadataServiceException e) {
                log.error(e.getMessage(),e);
            }
        }
    }

    /**
     * 删除可见字段
     *
     * @param context
     * @param id
     * @param apiName
     */
    protected void deletePoolPermissionField(ServiceContext context, String id, String apiName) {
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
        String fieldApiName = apiName.equals(SFAManagementPredefineObject.HighSeas.getApiName()) ? "AccountObj" : "LeadsObj";
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(fieldApiName));
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
        Map<String, Object> updateValue = Maps.newHashMap();
        updateValue.put(PoolPermissionTemplateConstants.Field.Is_Delete, 1);
        try {
            commonSqlService.update(HighSeasConstants.TableName.POOL_PERMISSION_FIELD, updateValue, wheres, convert2ActionContext(context));
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
    }

    /**
     * 获取可见字段
     *
     * @param context
     * @param id
     * @param apiName
     * @return
     */
    protected List<PoolPermissionTemplateModel> getPoolPermissionTemplate(ServiceContext context, String id, String apiName) {
        List<PoolPermissionTemplateModel> poolPermissionTemplateModels = Lists.newArrayList();
        List<WhereParam> wheres = Lists.newArrayList();
        IObjectDescribe objectDescribe = null;
        if (apiName.equals(SFAManagementPredefineObject.HighSeas.getApiName())) {
            objectDescribe = serviceFacade.findObject(context.getTenantId(), "AccountObj");
        } else if (apiName.equals(SFAManagementPredefineObject.LeadsPool.getApiName())) {
            objectDescribe = serviceFacade.findObject(context.getTenantId(), "LeadsObj");
        }
        if (objectDescribe == null) {
            throw new ValidateException(I18N.text(SFA_ACCOUNT_GETDESCRIPTIONFAILED));
        }
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(objectDescribe.getApiName()));
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
        CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Is_Visible, CommonSqlOperator.EQ, Lists.newArrayList(false));
        try {
            List<Map> maps = commonSqlService.select(HighSeasConstants.TableName.POOL_PERMISSION_FIELD,
                    wheres, convert2ActionContext(context));
            IObjectDescribe finalObjectDescribe = objectDescribe;
            List<IFieldDescribe> fieldDescribes = finalObjectDescribe.getFieldDescribes();
            List<String> blackList = SFAManagementPredefineObject.HighSeas.getApiName().equals(apiName) ? PoolPublicFieldBlackList.HighSeasList : PoolPublicFieldBlackList.LeadsPoolList;
            fieldDescribes.removeIf(x -> blackList.contains(x.getApiName()));
            Map<String, ObjectConfig> objectConfigMap = objectConfigService.getObjectConfig(context.getUser(), Sets.newHashSet(getPoolDataApiName()));
            ObjectConfig objectConfig = objectConfigMap.containsKey(getPoolDataApiName()) ? objectConfigMap.get(getPoolDataApiName()) : null;
            Map<String, Map<String, Object>> fields = objectConfig == null ? Maps.newHashMap() : objectConfig.getFields();
            fieldDescribes.forEach(x -> {
                if (!maps.stream().anyMatch(p -> p.get(PoolPermissionTemplateConstants.Field.Field_Name).toString().equals(x.getApiName()))) {
                    boolean isVisible = true;
                    if (fields.containsKey(x.getApiName())) {
                        Map<String, Object> fieldConfigMap = fields.get(x.getApiName());
                        if (fieldConfigMap != null && fieldConfigMap.containsKey("display") && !fieldConfigMap.get("display").equals(1)) {
                            return;
                        }
                    }
                    PoolPermissionTemplateModel item = new PoolPermissionTemplateModel();
                    item.setFieldApiName(x.getApiName());
                    item.setIsVisible(isVisible);
                    item.setFieldCaption(x.getLabel());
                    poolPermissionTemplateModels.add(item);
                } else if (maps.stream().anyMatch(p -> p.get(PoolPermissionTemplateConstants.Field.Field_Name).toString().equals(x.getApiName()))) {
                    PoolPermissionTemplateModel item = new PoolPermissionTemplateModel();
                    item.setFieldApiName(x.getApiName());
                    item.setIsVisible(false);
                    item.setFieldCaption(x.getLabel());
                    poolPermissionTemplateModels.add(item);
                }
            });
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
        return poolPermissionTemplateModels;
    }

    /**
     * 保存成员管理员
     *
     * @param context
     * @param adminPoolPermissions
     * @param memberPoolPermissions
     * @param id
     * @param apiName
     */
    protected void addPoolPermission(ServiceContext context, List<PoolPermissionModel> adminPoolPermissions, List<PoolPermissionModel>  collaboratorPoolPermissions,
                                     List<PoolPermissionModel> memberPoolPermissions, String id, String apiName) {
        //准备数据
        List<Map<String, Object>> toInsertData = Lists.newArrayList();
        adminPoolPermissions.forEach(a -> {
            Map<String, Object> dataItem = Maps.newHashMap();
            String itemId = serviceFacade.generateId();
            dataItem.put(Tenantable.TENANT_ID, context.getTenantId());
            dataItem.put(PoolPermissionConstants.Field.Id, itemId);
            dataItem.put(PoolPermissionConstants.Field.Pool_Id, id);
            dataItem.put(PoolPermissionConstants.Field.Data_Id, a.getDataId());
            dataItem.put(PoolPermissionConstants.Field.Is_Admin, true);
            dataItem.put(PoolPermissionConstants.Field.Type, a.getType());
            dataItem.put(PoolPermissionConstants.Field.Is_Delete, 0);
            dataItem.put(PoolPermissionConstants.Field.Last_Modified_By, context.getUser().getUpstreamOwnerIdOrUserId());
            dataItem.put(PoolPermissionConstants.Field.Last_Modified_Time, System.currentTimeMillis());
            dataItem.put(PoolPermissionConstants.Field.Create_Time, System.currentTimeMillis());
            dataItem.put(PoolPermissionConstants.Field.Created_By, context.getUser().getUpstreamOwnerIdOrUserId());
            dataItem.put(PoolPermissionConstants.Field.Object_Api_Name, apiName);
            dataItem.put(PoolPermissionConstants.Field.PERMISSION_TYPE, PoolPermissionConstants.PermissionType.ADMIN.getValue());
            toInsertData.add(dataItem);
        });
        memberPoolPermissions.forEach(p -> {
            Map<String, Object> dataItem = Maps.newHashMap();
            String itemId = serviceFacade.generateId();
            dataItem.put(Tenantable.TENANT_ID, context.getTenantId());
            dataItem.put(PoolPermissionConstants.Field.Id, itemId);
            dataItem.put(PoolPermissionConstants.Field.Pool_Id, id);
            dataItem.put(PoolPermissionConstants.Field.Data_Id, p.getDataId());
            dataItem.put(PoolPermissionConstants.Field.Is_Admin, false);
            dataItem.put(PoolPermissionConstants.Field.Type, p.getType());
            dataItem.put(PoolPermissionConstants.Field.Is_Delete, 0);
            dataItem.put(PoolPermissionConstants.Field.Last_Modified_By, context.getUser().getUpstreamOwnerIdOrUserId());
            dataItem.put(PoolPermissionConstants.Field.Last_Modified_Time, System.currentTimeMillis());
            dataItem.put(PoolPermissionConstants.Field.Create_Time, System.currentTimeMillis());
            dataItem.put(PoolPermissionConstants.Field.Created_By, context.getUser().getUpstreamOwnerIdOrUserId());
            dataItem.put(PoolPermissionConstants.Field.Object_Api_Name, apiName);
            dataItem.put(PoolPermissionConstants.Field.PERMISSION_TYPE, PoolPermissionConstants.PermissionType.MEMBER.getValue());
            toInsertData.add(dataItem);
        });
        collaboratorPoolPermissions = CollectionUtils.empty(collaboratorPoolPermissions) ? Lists.newArrayList() : collaboratorPoolPermissions;
        // 协作人员 is_admin 是true
        collaboratorPoolPermissions.forEach(p -> {
            Map<String, Object> dataItem = Maps.newHashMap();
            String itemId = serviceFacade.generateId();
            dataItem.put(Tenantable.TENANT_ID, context.getTenantId());
            dataItem.put(PoolPermissionConstants.Field.Id, itemId);
            dataItem.put(PoolPermissionConstants.Field.Pool_Id, id);
            dataItem.put(PoolPermissionConstants.Field.Data_Id, p.getDataId());
            dataItem.put(PoolPermissionConstants.Field.Is_Admin, true);
            dataItem.put(PoolPermissionConstants.Field.Type, p.getType());
            dataItem.put(PoolPermissionConstants.Field.Is_Delete, 0);
            dataItem.put(PoolPermissionConstants.Field.Last_Modified_By, context.getUser().getUpstreamOwnerIdOrUserId());
            dataItem.put(PoolPermissionConstants.Field.Last_Modified_Time, System.currentTimeMillis());
            dataItem.put(PoolPermissionConstants.Field.Create_Time, System.currentTimeMillis());
            dataItem.put(PoolPermissionConstants.Field.Created_By, context.getUser().getUpstreamOwnerIdOrUserId());
            dataItem.put(PoolPermissionConstants.Field.Object_Api_Name, apiName);
            dataItem.put(PoolPermissionConstants.Field.PERMISSION_TYPE, PoolPermissionConstants.PermissionType.COLLABORATOR.getValue());
            toInsertData.add(dataItem);
        });
        try {
            commonSqlService.insert(HighSeasConstants.TableName.POOL_PERMISSION, toInsertData, convert2ActionContext(context));
        } catch (MetadataServiceException e) {
            log.error("新建线索池成员出错");
        }
    }

    /**
     * 删除线索池成员表(软删除)
     *
     * @param context
     * @param id
     * @param apiName
     */
    protected void deletePoolPermission(ServiceContext context, String id, String apiName) {
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
        CommonSqlUtils.addWhereParam(wheres, PoolPermissionConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
        CommonSqlUtils.addWhereParam(wheres, RemindRuleConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
        Map<String, Object> updateValue = Maps.newHashMap();
        updateValue.put(RemindRuleConstants.Field.Is_Delete, 1);
        try {
            commonSqlService.update(HighSeasConstants.TableName.POOL_PERMISSION, updateValue, wheres, convert2ActionContext(context));
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
    }

    /**
     * 获取线索池成员
     *
     * @param context
     * @param id
     * @param apiName
     */
    protected List<PoolPermissionModel> getPoolPermission(ServiceContext context, String id, String apiName) {
        List<PoolPermissionModel> poolPermissionModels = Lists.newArrayList();
        List<WhereParam> whereParams = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(whereParams, PoolPermissionConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
        CommonSqlUtils.addWhereParam(whereParams, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
        CommonSqlUtils.addWhereParam(whereParams, PoolPermissionConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtils.addWhereParam(whereParams, PoolPermissionConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
        try {
            List<Map> maps = commonSqlService.select(HighSeasConstants.TableName.POOL_PERMISSION, whereParams, convert2ActionContext(context));
            maps.forEach(m -> {
                PoolPermissionModel item = new PoolPermissionModel();
                item.setDataId(m.get(PoolPermissionConstants.Field.Data_Id).toString());
                item.setIsAdmin((Boolean) m.get(PoolPermissionConstants.Field.Is_Admin));
                item.setType((Integer) m.get(PoolPermissionConstants.Field.Type));
                item.setPermissionType(Optional.ofNullable(m.get(PoolPermissionConstants.Field.PERMISSION_TYPE))
                        .map(Object::toString)
                        .orElse(""));
                poolPermissionModels.add(item);
            });
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
        return poolPermissionModels;
    }

    /**
     * 获取负责人规则
     *
     * @param context
     * @param id
     * @param apiName
     */
    protected String getPoolOwnerRule(ServiceContext context, String id, String apiName) {
        String result = "[]";
        try {
            List<WhereParam> whereParams = Lists.newArrayList();
            CommonSqlUtils.addWhereParam(whereParams, AllocateRuleConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
            CommonSqlUtils.addWhereParam(whereParams, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
            CommonSqlUtils.addWhereParam(whereParams, AllocateRuleConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
            CommonSqlUtils.addWhereParam(whereParams, AllocateRuleConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
            List<Map> maps = commonSqlService.select(HighSeasConstants.TableName.POOL_OWNER_RULE, whereParams, convert2ActionContext(context));
            if (CollectionUtils.notEmpty(maps)) {
                result = maps.get(0).get(AllocateRuleConstants.Field.Wheres).toString();
            }
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
        return result;
    }

    /**
     * 保存提醒规则
     *
     * @param context
     * @param remindRuleList
     * @param id
     * @param apiName
     */
    protected void addRemindRule(ServiceContext context, List<RemindRuleModel> remindRuleList, String id, String apiName) {
        if (CollectionUtils.notEmpty(remindRuleList)) {
            String groupId = serviceFacade.generateId();
            List<Map<String, Object>> toInsertData = Lists.newArrayList();
            remindRuleList.forEach(r -> {
                String ruleId = serviceFacade.generateId();
                Map<String, Object> item = Maps.newHashMap();
                item.put(RemindRuleConstants.Field.Create_Time, System.currentTimeMillis());
                item.put(RemindRuleConstants.Field.Last_Modified_Time, System.currentTimeMillis());
                item.put(RemindRuleConstants.Field.Created_By, context.getUser().getUpstreamOwnerIdOrUserId());
                item.put(RemindRuleConstants.Field.Last_Modified_By, context.getUser().getUpstreamOwnerIdOrUserId());
                item.put(RemindRuleConstants.Field.Data_Id, id);
                item.put(RemindRuleConstants.Field.Data_Type, HighSeasEnums.RemindRuleDataType.HighSeasRemind.getValue());
                item.put(RemindRuleConstants.Field.Deal_Days, r.getDealDays());
                item.put(RemindRuleConstants.Field.Follow_Up_Days, r.getFollowUpDays());
                item.put(RemindRuleConstants.Field.Id, ruleId);
                item.put(RemindRuleConstants.Field.Rule_Type, r.getRuleType());
                item.put(RemindRuleConstants.Field.Group_Id, groupId);
                item.put(Tenantable.TENANT_ID, context.getTenantId());
                item.put(RemindRuleConstants.Field.Object_Api_Name, apiName);
                item.put(RemindRuleConstants.Field.Is_Delete, 0);
                toInsertData.add(item);
            });
            try {
                commonSqlService.insert(HighSeasConstants.TableName.REMIND_RULE, toInsertData, convert2ActionContext(context));
            } catch (MetadataServiceException e) {
                log.error("公海新增提醒规则出错!");
            }
        }
    }

    private List<RemindRuleModel> getRemindRulesByDataId(String id, String apiName, ServiceContext serviceContext) {
        List<RemindRuleModel> remindRuleModels = Lists.newArrayList();
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, RemindRuleConstants.Field.Data_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
        CommonSqlUtils.addWhereParam(wheres, RemindRuleConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
        CommonSqlUtils.addWhereParam(wheres, RemindRuleConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(serviceContext.getTenantId()));
        try {
            List<Map> maps = commonSqlService.select(HighSeasConstants.TableName.REMIND_RULE, wheres, convert2ActionContext(serviceContext));
            maps.forEach(m -> {
                RemindRuleModel item = new RemindRuleModel();
                item.setDataId(m.get(RemindRuleConstants.Field.Data_Id).toString());
                item.setDataType((Integer) m.get(RemindRuleConstants.Field.Data_Type));
                item.setDealDays((Integer) m.get(RemindRuleConstants.Field.Deal_Days));
                item.setFollowUpDays((Integer) m.get(RemindRuleConstants.Field.Follow_Up_Days));
                item.setId(m.get(RemindRuleConstants.Field.Id).toString());
                item.setRuleType((Integer) m.get(RemindRuleConstants.Field.Rule_Type));
                item.setSkipHolidays(m.get(RemindRuleConstants.Field.SKIP_HOLIDAYS) != null && (boolean)m.get(RemindRuleConstants.Field.SKIP_HOLIDAYS));
                remindRuleModels.add(item);
            });
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
        return remindRuleModels;
    }

    /**
     * 删除提醒规则(软删除)
     *
     * @param context
     * @param id
     * @param dataType
     */
    protected void deleteRemindRule(ServiceContext context, String id, Integer dataType) {
        recyclingRuleService.deleteRemindRule(context, id, dataType);
    }

    /**
     * 保存收回规则
     *
     * @param context
     * @param recyclingRuleList
     * @param id
     * @param apiName
     */
    protected void addRecyclingRule(ServiceContext context, List<RecyclingRuleInfoModel> recyclingRuleList, String id, String apiName) {
        recyclingRuleService.addRecyclingRule(context, recyclingRuleList, id, apiName);
    }

    /**
     * 删除收回规则
     *
     * @param context
     * @param id
     * @param apiName
     */
    protected void deleteRecyclingRule(ServiceContext context, String id, String apiName) {
        recyclingRuleService.deleteRecyclingRule(context, id, apiName);
    }

    private void UpdateRecyclingRule(String apiName, List<RecyclingRuleInfoModel> recyclingRuleInfoModels, String dataID,
                                     HighSeasEnums.RecyclingRuleDataType type, ServiceContext context, List<RecyclingRuleInfoModel> oldRecyclingRules) {
        boolean changed = isRecyclingRuleChange(oldRecyclingRules, recyclingRuleInfoModels);
        if (changed) {
            recyclingRuleService.deleteAddRecyclingRule(context, apiName, recyclingRuleInfoModels, dataID, type);
            // 修改了收回规则字段 发送重算任务
            recalculateTaskService.sendChangeRuleMsg(context.getTenantId(), dataID, apiName, ActionCodeEnum.CHANGE_RULE);
        } else if (recyclingRuleExtendChange(oldRecyclingRules, recyclingRuleInfoModels)) {
            recyclingRuleService.updateRecyclingRule(context, apiName, recyclingRuleInfoModels, dataID, null);
        }
    }

    @Override
    public List<RecyclingRuleInfoModel> getRecyclingRuleListByDataId(String apiName, String dataId, ServiceContext context) {
        List<RecyclingRuleInfoModel> result = recyclingRuleService.getRecyclingRuleListByDataId(context, apiName, dataId);
        List<String> targetPoolList = result.stream().map(RecyclingRuleInfoModel::getTargetPoolId).collect(Collectors.toList());
        addTargetPoolNameById(result, apiName, targetPoolList, context);
        return result;
    }

    @Override
    public boolean movePoolData2OtherPool(ServiceContext context, PoolServiceModel.DeletePoolArg arg) {
        if (StringUtils.isEmpty(arg.getId()) || StringUtils.isEmpty(arg.getTargetPoolId()) || StringUtils.isEmpty(arg.getApiName())) {
            throw new ValidateException(I18N.text(SFA_CONFIG_PARAMETER_ERROR));
        }
        IObjectPoolService objectPoolService = objectPoolServiceManager.getObjectPoolService(getPoolDataApiName());
        boolean result = false;
        if (objectPoolService != null) {
            result = objectPoolService.movePoolObjects(context.getUser(), arg.getId(), arg.getTargetPoolId());
            List<IObjectData> poolDataList = objectPoolService.getObjectPoolByIds(context.getTenantId(), Lists.newArrayList(arg.getId(), arg.getTargetPoolId()));
            Optional<IObjectData> poolData = poolDataList.stream().filter(x -> arg.getId().equals(x.getId())).findFirst();
            Optional<IObjectData> targetPoolData = poolDataList.stream().filter(x -> arg.getTargetPoolId().equals(x.getId())).findFirst();

            String oldPoolName = poolData.isPresent() ? poolData.get().getName() : "";
            String targetPoolName = targetPoolData.isPresent() ? targetPoolData.get().getName() : "";
            String settingLogMsg = I18N.text(SFA_CONFIG_POOLTOPOOL,
                    I18N.text(arg.getApiName() + ".attribute.self.display_name"), oldPoolName,
                    I18N.text(arg.getApiName() + ".attribute.self.display_name"), targetPoolName);
            InternationalItem internationalItem = InternationalItem.builder()
                    .defaultInternationalValue(settingLogMsg)
                    .internationalKey("sfa.audit.log.move.msg")
                    .internationalParameters(Lists.newArrayList(oldPoolName, targetPoolName))
                    .build();
            serviceFacade.logInternational(context.getUser(), EventType.MODIFY, ActionType.MOVE, arg.getApiName(), arg.getApiName(), settingLogMsg, internationalItem);
        }
        return result;
    }

    @Override
    public void deleteAllPoolData(ServiceContext context, PoolServiceModel.DeletePoolArg arg) {
        if (StringUtils.isEmpty(arg.getId()) || StringUtils.isEmpty(arg.getApiName())) {
            throw new ValidateException(I18N.text(SFA_CONFIG_PARAMETER_ERROR));
        }
        try {
            final String poolId = arg.getId();
            final String tenantId = context.getTenantId();
            final User user = context.getUser();
            final String poolDataPoolIdApiName = getPoolDataPoolIdApiName();
            final String poolDataApiName = getPoolDataApiName();
            final String apiName = arg.getApiName();

            /**
             * 两个变量反着了，
             * poolDataApiName   代表的是对象apiName
             * apiName           代表的是池子的ApiName
             */
            String redisRequestId = distributionLock.tryLockExpireSecondTime(tenantId, apiName, ObjectAction.UPDATE.getActionCode(), poolId, 300);
            if (StringUtils.isEmpty(redisRequestId)) {
                throw new ValidateException(I18N.text(DATA_IN_OPERATION_ERROR));
            }
            recalculateProducer.senddeleteAllPoolDataMQ(user.getTenantId(), user.getUpstreamOwnerIdOrUserId(), poolId, poolDataPoolIdApiName, redisRequestId
                    ,apiName,poolDataApiName);
//            ParallelUtils.ParallelTask backgroundTaskB = ParallelUtils.createBackgroundTask();
//            backgroundTaskB.submit(() -> {
//                for(int executeCount = 0, maxExecuteCount = 10000 ; executeCount < maxExecuteCount ; executeCount++){
//                    try {
//                        if (executeCount >= maxExecuteCount - 1) {
//                            log.warn("deleteAllPoolData#deleteAllPoolData reaches loop limit, limit:{}", executeCount); //日志打印
//                            //上报audit_log
//                            SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
//                                .action("sfa_loop_limit")
//                                .objectApiNames(poolDataApiName)
//                                .message("BasePoolService.deleteAllPoolData").build(), user);
//                            return;
//                        }
//
//                        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
//                        searchTemplateQuery.setLimit(500);
//                        searchTemplateQuery.setPermissionType(0);
//                        searchTemplateQuery.setNeedReturnCountNum(false);
//                        List filters = Lists.newLinkedList();
//                        SearchUtil.fillFilterEq(filters, poolDataPoolIdApiName, poolId);
//                        SearchUtil.fillFilterGTE(filters, HighSeasConstants.Field.IS_DELETED, 0);
//                        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
//                        searchTemplateQuery.setFilters(filters);
//                        QueryResult<IObjectData> dataList = serviceFacade.findBySearchQuery(user, poolDataApiName, searchTemplateQuery);
//                        if (CollectionUtils.empty(dataList.getData())) {
//                            return;
//                        }
//                        serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(dataList.getData(), user);
//
//                        try {
//                            //删除从对象
//                            IObjectDescribe objectDescribe = this.serviceFacade.findObject(user.getTenantId(), poolDataApiName);
//                            AccountUtil.dealWithDetail(dataList.getData(), user, objectDescribe);
//                        } catch (Exception e) {
//                            log.error("dealWithDetail error:", e);
//                        }
//
//                    } catch (Exception e) {
//                        log.error("deleteAllPoolData error", e);
//                        throw e;
//                    } finally {
//                        if (!StringUtils.isEmpty(redisRequestId)) {
//                            distributionLock.releaseLock(tenantId, apiName, ObjectAction.UPDATE.getActionCode(), poolId, redisRequestId);
//                        }
//                    }
//                }
//            });
//            backgroundTaskB.run();
        } catch (Exception e) {
            log.error("deleteAllPoolData error", e);
        }
    }

    private void addTargetPoolNameById(List<RecyclingRuleInfoModel> result, String apiName, List<String> targetPoolList, ServiceContext context) {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(context.getTenantId(), targetPoolList, apiName);
        Map<String, String> stringMap = objectDataList.stream().collect(Collectors.toMap(IObjectData::getId, objectData ->I18NUtil.getValueByLang(objectData,IObjectData.NAME,context)));
        for (RecyclingRuleInfoModel item : result) {
            if (stringMap.containsKey(item.getTargetPoolId())) {
                item.setTargetPoolName(stringMap.get(item.getTargetPoolId()));
            }
        }
    }

    /**
     * 保存分配规则
     *
     * @param context
     * @param allocateRuleList
     * @param id
     * @param apiName
     */
    protected void addAllocateRule(ServiceContext context, List<AllocateRuleInfoModel> allocateRuleList, String id, String apiName) {
        List<Runnable> taskList = Lists.newArrayList();
        List<Map<String, Object>> weightMemberList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(allocateRuleList)) {
            boolean isGrayAllocateResetSequence = isGrayAllocateResetSequence(context.getTenantId());
            List<Map<String, Object>> toInsertRuleData = Lists.newArrayList();
            List<Map<String, Object>> toUpdateRuleData = Lists.newArrayList();
            List<Map<String, Object>> toInsertMemberData = Lists.newArrayList();

            allocateRuleList.forEach(a -> {
                if("".equals(a.getMemberWheres())){
                    a.setMemberWheres("[]");
                }
                String calculateId = serviceFacade.generateId();
                Map<String, Object> allocateRuleItem = Maps.newHashMap();
                String allocateRuleId = serviceFacade.generateId();
                allocateRuleItem.put(AllocateRuleConstants.Field.Id, allocateRuleId);
                allocateRuleItem.put(AllocateRuleConstants.Field.Pool_Id, id);
                allocateRuleItem.put(AllocateRuleConstants.Field.Priority, a.getPriority());
                allocateRuleItem.put(AllocateRuleConstants.Field.Allocate_Pattern, a.getAllocatePattern());
                allocateRuleItem.put(AllocateRuleConstants.Field.Is_All_Sales_Clue, a.getIsAllPool());
                allocateRuleItem.put(AllocateRuleConstants.Field.Last_Modified_By, context.getUser().getUpstreamOwnerIdOrUserId());
                allocateRuleItem.put(AllocateRuleConstants.Field.Created_By, context.getUser().getUpstreamOwnerIdOrUserId());
                allocateRuleItem.put(AllocateRuleConstants.Field.Last_Modified_Time, System.currentTimeMillis());
                allocateRuleItem.put(AllocateRuleConstants.Field.Create_Time, System.currentTimeMillis());
                allocateRuleItem.put(AllocateRuleConstants.Field.Wheres, a.getWheres());
                allocateRuleItem.put(Tenantable.TENANT_ID, context.getTenantId());
                allocateRuleItem.put(AllocateRuleConstants.Field.Object_Api_Name, apiName);
                allocateRuleItem.put(AllocateRuleConstants.Field.Is_Delete, 0);
                allocateRuleItem.put(AllocateRuleConstants.Field.Is_Reset_Allocate_Sequence, a.getIsResetAllocateSequence());
                allocateRuleItem.put(AllocateRuleConstants.Field.MEMBER_WHERES, a.getMemberWheres());
                allocateRuleItem.put(AllocateRuleConstants.Field.Calculate_Id, calculateId);
                if (isGrayAllocateResetSequence && a.getIsResetAllocateSequence() != null && !a.getIsResetAllocateSequence() && StringUtils.isNotBlank(a.getId())) {
                    allocateRuleId = a.getId();
                    allocateRuleItem.put(AllocateRuleConstants.Field.Id, allocateRuleId);
                    toUpdateRuleData.add(allocateRuleItem);
                } else {
                    toInsertRuleData.add(allocateRuleItem);
                }

                String finalAllocateRuleId = allocateRuleId;
                List<AllocateRuleMemberModel> memberList = a.getMemberList();
                AtomicLong currentTimeMillis = new AtomicLong(System.currentTimeMillis());
                if (CollectionUtils.notEmpty(memberList)) {
                    List<String> haveWheresMemberIdList = Lists.newArrayList();
                    memberList.forEach(m -> {
                        currentTimeMillis.set(currentTimeMillis.get() + 10L);
                        Map<String, Object> memberItem = Maps.newHashMap();
                        String allocateRuleMemberId = serviceFacade.generateId();
                        memberItem.put(AllocateRuleMemberConstants.Field.Id, allocateRuleMemberId);
                        memberItem.put(AllocateRuleMemberConstants.Field.Allocate_Rule_Id, finalAllocateRuleId);
                        memberItem.put(AllocateRuleMemberConstants.Field.Member_Id, m.getMemberId());
                        memberItem.put(AllocateRuleMemberConstants.Field.Member_Type, m.getMemberType());
                        memberItem.put(AllocateRuleMemberConstants.Field.Last_Modified_By, context.getUser().getUpstreamOwnerIdOrUserId());
                        memberItem.put(AllocateRuleMemberConstants.Field.Created_By, context.getUser().getUpstreamOwnerIdOrUserId());
                        memberItem.put(AllocateRuleMemberConstants.Field.Last_Modified_Time, currentTimeMillis.get());
                        memberItem.put(AllocateRuleMemberConstants.Field.Create_Time, currentTimeMillis.get());
                        memberItem.put(Tenantable.TENANT_ID, context.getTenantId());
                        memberItem.put(AllocateRuleMemberConstants.Field.Is_Delete, 0);
                        memberItem.put(AllocateRuleMemberConstants.Field.Pool_Id, id);
                        memberItem.put(AllocateRuleConstants.Field.Object_Api_Name, apiName);
                        memberItem.put(AllocateRuleMemberConstants.Field.Priority, m.getPriority());
                        memberItem.put(AllocateRuleMemberConstants.Field.Weight, m.getWeight());
                        memberItem.put(AllocateRuleMemberConstants.Field.Member_Wheres, m.getMemberWheres());
                        toInsertMemberData.add(memberItem);

                        if (StringUtils.isNotEmpty(m.getMemberWheres())) {
                            haveWheresMemberIdList.add(allocateRuleMemberId);
                        } else {
                            // wheres为空，且是权重分配，将member直接插入weightMember表
                            if (Objects.equals(a.getAllocatePattern(), LeadsPoolEnums.LeadsPoolAllocatePatternType.WeightAllocate.getValue())) {
                                weightMemberList.add(buildWeightMemberByMember(calculateId, memberItem));
                            }
                        }
                    });
                    // 待插入后发送计算weightMember任务
                    if (Objects.equals(a.getAllocatePattern(), LeadsPoolEnums.LeadsPoolAllocatePatternType.WeightAllocate.getValue())
                            && CollectionUtils.notEmpty(haveWheresMemberIdList)) {
                        taskList.add(() -> {
                            CalculateRuleMemberWheresTaskArg taskArg = CalculateRuleMemberWheresTaskArg.builder()
                                    .ruleId(finalAllocateRuleId).tenantId(context.getTenantId()).poolId(id)
                                    .memberIdList(haveWheresMemberIdList).objectApiName(apiName).isUpdate(false).build();
                            taskService.send(AllocateRuleMemberConstants.MEMBER_WHERES_CALCULATE_TASK, context.getTenantId(), finalAllocateRuleId, taskArg);
                        });
                    }
                }
            });

            if (CollectionUtils.notEmpty(toInsertRuleData)) {
                try {
                    commonSqlService.insert(LeadsPoolConstants.TableName.Pool_Allocate_Rule, toInsertRuleData, convert2ActionContext(context));
                } catch (MetadataServiceException e) {
                    log.error("保存 Sales_Clue_Pool_Allocate_Rule 错误");
                }
            }
            if (isGrayAllocateResetSequence && CollectionUtils.notEmpty(toUpdateRuleData)) {
                try {
                    List<WhereParam> wheres = Lists.newArrayList();
                    List<String> idList = toUpdateRuleData.stream().map(x -> x.get(AllocateRuleConstants.Field.Id).toString()).collect(Collectors.toList());
                    CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
                    CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Id, CommonSqlOperator.IN, Lists.newArrayList(idList));
                    CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
                    CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
                    commonSqlService.delete(LeadsPoolConstants.TableName.Pool_Allocate_Rule, wheres, convert2ActionContext(context));
                    commonSqlService.insert(LeadsPoolConstants.TableName.Pool_Allocate_Rule, toUpdateRuleData, convert2ActionContext(context));
                } catch (MetadataServiceException e) {
                    log.error("保存 Sales_Clue_Pool_Allocate_Rule 错误");
                }
            }

            if (CollectionUtils.notEmpty(toInsertMemberData)) {
                try {
                    commonSqlService.insert(LeadsPoolConstants.TableName.Pool_Allocate_Rule_Member, toInsertMemberData, convert2ActionContext(context));
                } catch (MetadataServiceException e) {
                    log.error("保存 Sales_Clue_Pool_Allocate_Rule_Member 错误");
                }
                if (CollectionUtils.notEmpty(weightMemberList)) {
                    try {
                        commonSqlService.insert(LeadsPoolConstants.TableName.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT, weightMemberList, convert2ActionContext(context));
                    } catch (MetadataServiceException e) {
                        log.error("保存 biz_pool_allocate_rule_member_weight 错误");
                    }
                }
                taskList.forEach(Runnable::run);
            }
        }
    }

    @NotNull
    private Map<String, Object> buildWeightMemberByMember(String calculateId, Map<String, Object> memberItem) {
        Map<String, Object> weightMember = Maps.newHashMap();
        weightMember.put(AllocateRuleMemberWeightConstant.Field.id, serviceFacade.generateId());
        weightMember.put(Tenantable.TENANT_ID, memberItem.get(Tenantable.TENANT_ID));
        weightMember.put(AllocateRuleMemberWeightConstant.Field.objectApiName, memberItem.get(AllocateRuleConstants.Field.Object_Api_Name));
        weightMember.put(AllocateRuleMemberWeightConstant.Field.poolId, memberItem.get(AllocateRuleMemberConstants.Field.Pool_Id));
        weightMember.put(AllocateRuleMemberWeightConstant.Field.allocateRuleId, memberItem.get(AllocateRuleMemberConstants.Field.Allocate_Rule_Id));
        weightMember.put(AllocateRuleMemberWeightConstant.Field.memberWeightRuleId, memberItem.get(AllocateRuleMemberConstants.Field.Id));
        weightMember.put(AllocateRuleMemberWeightConstant.Field.weight, memberItem.get(AllocateRuleMemberConstants.Field.Weight));
        weightMember.put(AllocateRuleMemberWeightConstant.Field.isDeleted, 0);
        weightMember.put(AllocateRuleMemberWeightConstant.Field.createBy, memberItem.get(AllocateRuleMemberConstants.Field.Created_By));
        weightMember.put(AllocateRuleMemberWeightConstant.Field.createTime, System.currentTimeMillis());
        weightMember.put(AllocateRuleMemberWeightConstant.Field.memberId, memberItem.get(AllocateRuleMemberConstants.Field.Member_Id));
        weightMember.put(AllocateRuleMemberWeightConstant.Field.calculateId, calculateId);
        return weightMember;
    }

    /**
     * 新建或者更新分配规则
     *
     * @param context
     * @param allocateRuleList
     * @param id
     * @param apiName
     */
    protected void updateAllocateRule(ServiceContext context, List<AllocateRuleInfoModel> allocateRuleList, String id, String apiName) {

        List<Map<String, Object>> toInsertRuleData = Lists.newArrayList();
        List<Map<String, Object>> toInsertMemberData = Lists.newArrayList();
        List<String> haveWheresMemberIdList = Lists.newArrayList();
        List<Map<String, Object>> weightMemberList = Lists.newArrayList();
        List<Runnable> taskList = Lists.newArrayList();
        List<AllocateRuleInfoModel> ruleInfoModelList = getAllocateRuleByDataId(apiName, id, context);

        List<String> oldRuleIds = ruleInfoModelList.stream().map(AllocateRuleInfoModel::getId).collect(Collectors.toList());
        // 找到需要删除的旧规则ID,过滤掉更新的id
        if (CollectionUtils.notEmpty(allocateRuleList)) {
            List<String> newRuleIds = allocateRuleList.stream().filter(x -> StringUtils.isNotBlank(x.getId())).map(AllocateRuleInfoModel::getId).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(newRuleIds)) {
                oldRuleIds.removeIf(x -> newRuleIds.contains(x));
            }
        }
        // 删除旧规则
        if (CollectionUtils.notEmpty(oldRuleIds)) {
            try {
                List<WhereParam> wheres = Lists.newArrayList();
                CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
                CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Id, CommonSqlOperator.IN, Lists.newArrayList(oldRuleIds));
                CommonSqlUtils.addWhereParam(wheres, PoolPermissionTemplateConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
                Map<String, Object> updateValue = Maps.newHashMap();
                updateValue.put(PoolPermissionTemplateConstants.Field.Is_Delete, 1);
                commonSqlService.update(LeadsPoolConstants.TableName.Pool_Allocate_Rule, updateValue, wheres, convert2ActionContext(context));

                List<WhereParam> ruleMemberWheres = Lists.newArrayList();
                CommonSqlUtils.addWhereParam(ruleMemberWheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
                CommonSqlUtils.addWhereParam(ruleMemberWheres, AllocateRuleMemberConstants.Field.Allocate_Rule_Id, CommonSqlOperator.IN, Lists.newArrayList(oldRuleIds));
                CommonSqlUtils.addWhereParam(ruleMemberWheres, PoolPermissionTemplateConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
                commonSqlService.update(HighSeasConstants.TableName.ALLOCATE_RULE_MEMBER, updateValue, ruleMemberWheres, convert2ActionContext(context));
                commonSqlService.update(LeadsPoolConstants.TableName.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT, updateValue, ruleMemberWheres, convert2ActionContext(context));

            } catch (MetadataServiceException e) {
                log.error("delete biz_pool_allocate_rule poolId:{}", id, e);
            }
        }

        if (CollectionUtils.notEmpty(allocateRuleList)) {
            allocateRuleList.forEach(a -> {
                if("".equals(a.getMemberWheres())){
                    a.setMemberWheres("[]");
                }
                String calculateId = serviceFacade.generateId();
                long currentTime = System.currentTimeMillis();
                Map<String, Object> allocateRuleItem = Maps.newHashMap();
                allocateRuleItem.put(AllocateRuleConstants.Field.Pool_Id, id);
                allocateRuleItem.put(AllocateRuleConstants.Field.Priority, a.getPriority());
                allocateRuleItem.put(AllocateRuleConstants.Field.Allocate_Pattern, a.getAllocatePattern());
                allocateRuleItem.put(AllocateRuleConstants.Field.Is_All_Sales_Clue, a.getIsAllPool());
                allocateRuleItem.put(AllocateRuleConstants.Field.Last_Modified_By, context.getUser().getUserId());
                allocateRuleItem.put(AllocateRuleConstants.Field.Created_By, context.getUser().getUserId());
                allocateRuleItem.put(AllocateRuleConstants.Field.Last_Modified_Time, currentTime);
                allocateRuleItem.put(AllocateRuleConstants.Field.Wheres, a.getWheres());
                allocateRuleItem.put(Tenantable.TENANT_ID, context.getTenantId());
                allocateRuleItem.put(AllocateRuleConstants.Field.Object_Api_Name, apiName);
                allocateRuleItem.put(AllocateRuleConstants.Field.Is_Delete, 0);
                allocateRuleItem.put(AllocateRuleConstants.Field.Is_Reset_Allocate_Sequence, a.getIsResetAllocateSequence());
                allocateRuleItem.put(AllocateRuleConstants.Field.MEMBER_WHERES, a.getMemberWheres());
                allocateRuleItem.put(AllocateRuleConstants.Field.Calculate_Id, calculateId);
                String allocateRuleId;
                if (StringUtils.isBlank(a.getId())) {
                    allocateRuleId = serviceFacade.generateId();
                    allocateRuleItem.put(AllocateRuleConstants.Field.Id, allocateRuleId);
                    allocateRuleItem.put(AllocateRuleConstants.Field.Create_Time, currentTime);
                    toInsertRuleData.add(allocateRuleItem);
                } else {
                    allocateRuleId = a.getId();
                    List<WhereParam> wheres = Lists.newArrayList();
                    CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
                    CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Id, CommonSqlOperator.EQ, Lists.newArrayList(allocateRuleId));
                    CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
                    CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
                    try {
                        commonSqlService.update(LeadsPoolConstants.TableName.Pool_Allocate_Rule, allocateRuleItem, wheres, convert2ActionContext(context));
                    } catch (MetadataServiceException e) {
                        log.error(" insert  biz_pool_allocate_rule error poolId:{}", id, e);
                    }
                }

                String finalAllocateRuleId = allocateRuleId;
                List<AllocateRuleMemberModel> memberList = a.getMemberList();
                AtomicLong atomicCurrentTime = new AtomicLong(currentTime);
                if (CollectionUtils.notEmpty(memberList)) {
                    memberList.forEach(m -> {
                        atomicCurrentTime.set(atomicCurrentTime.get() + 10L);
                        Map<String, Object> memberItem = Maps.newHashMap();
                        String allocateRuleMemberId = serviceFacade.generateId();
                        memberItem.put(AllocateRuleMemberConstants.Field.Id, allocateRuleMemberId);
                        memberItem.put(AllocateRuleMemberConstants.Field.Allocate_Rule_Id, finalAllocateRuleId);
                        memberItem.put(AllocateRuleMemberConstants.Field.Member_Id, m.getMemberId());
                        memberItem.put(AllocateRuleMemberConstants.Field.Member_Type, m.getMemberType());
                        memberItem.put(AllocateRuleMemberConstants.Field.Last_Modified_By, context.getUser().getUserId());
                        memberItem.put(AllocateRuleMemberConstants.Field.Created_By, context.getUser().getUserId());
                        memberItem.put(AllocateRuleMemberConstants.Field.Last_Modified_Time, atomicCurrentTime.get());
                        memberItem.put(AllocateRuleMemberConstants.Field.Create_Time, atomicCurrentTime.get());
                        memberItem.put(Tenantable.TENANT_ID, context.getTenantId());
                        memberItem.put(AllocateRuleMemberConstants.Field.Is_Delete, 0);
                        memberItem.put(AllocateRuleMemberConstants.Field.Pool_Id, id);
                        memberItem.put(AllocateRuleConstants.Field.Object_Api_Name, apiName);
                        memberItem.put(AllocateRuleMemberConstants.Field.Priority, m.getPriority());
                        memberItem.put(AllocateRuleMemberConstants.Field.Weight, m.getWeight());
                        memberItem.put(AllocateRuleMemberConstants.Field.Member_Wheres, m.getMemberWheres());
                        toInsertMemberData.add(memberItem);
                        if (StringUtils.isNotEmpty(m.getMemberWheres())) {
                            haveWheresMemberIdList.add(allocateRuleMemberId);
                        } else {
                            // wheres为空，且是权重分配，将member直接插入weightMember表
                            if (Objects.equals(a.getAllocatePattern(), LeadsPoolEnums.LeadsPoolAllocatePatternType.WeightAllocate.getValue())) {
                                weightMemberList.add(buildWeightMemberByMember(calculateId, memberItem));
                            }
                        }
                    });
                }
                // 待插入后发送计算weightMember任务
                if (Objects.equals(a.getAllocatePattern(), LeadsPoolEnums.LeadsPoolAllocatePatternType.WeightAllocate.getValue())
                        && CollectionUtils.notEmpty(haveWheresMemberIdList)) {
                    taskList.add(() -> {
                        CalculateRuleMemberWheresTaskArg taskArg = CalculateRuleMemberWheresTaskArg.builder()
                                .ruleId(finalAllocateRuleId).tenantId(context.getTenantId()).poolId(id)
                                .memberIdList(haveWheresMemberIdList).objectApiName(apiName).isUpdate(true).build();
                        taskService.send(AllocateRuleMemberConstants.MEMBER_WHERES_CALCULATE_TASK, context.getTenantId(), finalAllocateRuleId, taskArg);
                    });
                }
                // 按人员分配的，在这里将本次不匹配的已分配归零
                if (Safes.isNotEmpty(weightMemberList)) {
                    List<String> memberIdList = weightMemberList.stream().map(m -> m.get("member_id").toString()).collect(Collectors.toList());
                    this.makeZeroByMismatched(context.getTenantId(), allocateRuleId, apiName, memberIdList);
                }
            });

            // 新规则
            if (CollectionUtils.notEmpty(toInsertRuleData)) {
                try {
                    commonSqlService.insert(LeadsPoolConstants.TableName.Pool_Allocate_Rule, toInsertRuleData, convert2ActionContext(context));
                } catch (MetadataServiceException e) {
                    log.error("保存 Sales_Clue_Pool_Allocate_Rule 错误");
                }
            }

            deleteAllocateRuleMember(context, id, apiName);
            if (CollectionUtils.notEmpty(toInsertMemberData)) {
                try {
                    commonSqlService.insert(LeadsPoolConstants.TableName.Pool_Allocate_Rule_Member, toInsertMemberData, convert2ActionContext(context));
                } catch (MetadataServiceException e) {
                    log.error("保存 Sales_Clue_Pool_Allocate_Rule_Member 错误");
                }
                if (CollectionUtils.notEmpty(weightMemberList)) {
                    try {
                        commonSqlService.insert(LeadsPoolConstants.TableName.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT, weightMemberList, convert2ActionContext(context));
                    } catch (MetadataServiceException e) {
                        log.error("保存 biz_pool_allocate_rule_member_weight 错误");
                    }
                }
                if (CollectionUtils.notEmpty(taskList)) {
                    taskList.forEach(Runnable::run);
                }
            }
        }
    }

    /**
     * 将之前有，修改后没有的memberId的record表已分配归零
     */
    private void makeZeroByMismatched(String tenantId, String ruleId, String objectApiName, List<String> matchedIdList) {
        List<WhereParam> whereParams = CommonSqlUtils.getCommonWhereParams(tenantId, objectApiName);
        CommonSqlUtils.addWhereParam(whereParams, "allocate_rule_id", CommonSqlOperator.EQ, Lists.newArrayList(ruleId));
        @SuppressWarnings("rawtypes") List<Map> oldWeightMemberList;
        try {
            oldWeightMemberList = CommonSqlUtils.queryData(tenantId, LeadsPoolConstants.TableName.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT,
                    whereParams);
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
        List<Object> oldIdList = oldWeightMemberList.stream().map(m -> m.get("member_id")).collect(Collectors.toList());
        //noinspection SuspiciousMethodCalls
        oldIdList.removeIf(matchedIdList::contains);

        List<WhereParam> updateWhereList = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(updateWhereList, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtils.addWhereParam(updateWhereList, "allocate_rule_id", CommonSqlOperator.EQ, Lists.newArrayList(ruleId));
        CommonSqlUtils.addWhereParam(updateWhereList, "employee_id", CommonSqlOperator.IN, oldIdList);
        Map<String, Object> updateField = new HashMap<>();
        updateField.put("allocated_number", 0);
        try {
            if (CollectionUtils.notEmpty(oldIdList)) {
                CommonSqlUtils.updateData(tenantId, LeadsPoolConstants.TableName.BIZ_POOL_ALLOCATE_RULE_MEMBER_RECORD, updateField, updateWhereList);
            }
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
    }

    protected boolean isGrayAllocateResetSequence(String tenantId) {
        return gray.isAllow("pool_allocate_reset_sequence", tenantId);
    }

    /**
     * 保存负责人规则
     *
     * @param context
     * @param poolOwnerRuleWheres
     * @param id
     * @param apiName
     */
    protected void addPoolOwnerRule(ServiceContext context, String poolOwnerRuleWheres, String id, String apiName) {
        try {
            if (StringUtils.isBlank(poolOwnerRuleWheres)) {
                poolOwnerRuleWheres = "[]";
            }
            List<Map<String, Object>> toInsertRuleDataList = Lists.newArrayList();
            Map<String, Object> ruleData = Maps.newHashMap();
            ruleData.put(AllocateRuleConstants.Field.Id, serviceFacade.generateId());
            ruleData.put(AllocateRuleConstants.Field.Pool_Id, id);
            ruleData.put(AllocateRuleConstants.Field.Last_Modified_By, context.getUser().getUpstreamOwnerIdOrUserId());
            ruleData.put(AllocateRuleConstants.Field.Created_By, context.getUser().getUpstreamOwnerIdOrUserId());
            ruleData.put(AllocateRuleConstants.Field.Last_Modified_Time, System.currentTimeMillis());
            ruleData.put(AllocateRuleConstants.Field.Create_Time, System.currentTimeMillis());
            ruleData.put(AllocateRuleConstants.Field.Wheres, poolOwnerRuleWheres);
            ruleData.put(Tenantable.TENANT_ID, context.getTenantId());
            ruleData.put(AllocateRuleConstants.Field.Object_Api_Name, apiName);
            ruleData.put(AllocateRuleConstants.Field.Is_Delete, 0);
            toInsertRuleDataList.add(ruleData);

            //先删除负责人规则(设置为1)
            deletePoolOwnerRule(context, id, apiName);
            CommonSqlUtils.insertData(context.getTenantId(), HighSeasConstants.TableName.POOL_OWNER_RULE, toInsertRuleDataList);
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
    }

    protected void deleteAllocateRule(ServiceContext context, String id, String apiName) {
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
        CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
        Map<String, Object> updateValue = Maps.newHashMap();
        updateValue.put(RecyclingRuleConstans.Field.Is_Delete, 1);
        try {
            commonSqlService.update(HighSeasConstants.TableName.BIZ_POOL_ALLOCATE_RULE_MEMBER_RULE, updateValue, wheres, convert2ActionContext(context));
            CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
            commonSqlService.update(HighSeasConstants.TableName.ALLOCATE_RULE, updateValue, wheres, convert2ActionContext(context));
            commonSqlService.update(HighSeasConstants.TableName.ALLOCATE_RULE_MEMBER, updateValue, wheres, convert2ActionContext(context));
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
    }

    /**
     * 逻辑删除biz_pool_allocate_rule_member 分配候选人
     *
     * @param context
     * @param id
     * @param apiName
     */
    protected void deleteAllocateRuleMember(ServiceContext context, String id, String apiName) {
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
        CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
        CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
        Map<String, Object> updateValue = Maps.newHashMap();
        updateValue.put(RecyclingRuleConstans.Field.Is_Delete, 1);
        try {
            commonSqlService.update(HighSeasConstants.TableName.ALLOCATE_RULE_MEMBER, updateValue, wheres, convert2ActionContext(context));
            commonSqlService.update(LeadsPoolConstants.TableName.BIZ_POOL_ALLOCATE_RULE_MEMBER_WEIGHT, updateValue, wheres, convert2ActionContext(context));
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
    }

    protected void deletePoolOwnerRule(ServiceContext context, String id, String apiName) {
        try {
            List<WhereParam> wheres = Lists.newArrayList();
            CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
            CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
            CommonSqlUtils.addWhereParam(wheres, AllocateRuleConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(id));
            CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
            Map<String, Object> updateValue = Maps.newHashMap();
            updateValue.put(RecyclingRuleConstans.Field.Is_Delete, 1);
            CommonSqlUtils.updateData(context.getTenantId(), HighSeasConstants.TableName.POOL_OWNER_RULE, updateValue, wheres);
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
    }

    private void handleOuterInfo(User user, IObjectData objectData, List<PoolPermissionModel> permissionModels,
                                 List<AllocateRuleInfoModel> allocateRuleInfoModelList,
                                 List<PoolServiceModel.OuterInfo> employeeInfoList, List<PoolServiceModel.OuterInfo> enterpriseInfoList) {
        Set<String> employeeIds = Sets.newHashSet();
        Set<String> enterpriseIds = Sets.newHashSet();
        handleOuterIds(objectData, allocateRuleInfoModelList, permissionModels, enterpriseIds, employeeIds);
        employeeInfoList.addAll(PrmUtils.getOuterEmployeeInfo(user, employeeIds));
        enterpriseInfoList.addAll(PrmUtils.getOuterEnterpriseInfo(user, enterpriseIds));
    }

    private void handleOuterIds(IObjectData objectData, List<AllocateRuleInfoModel> allocateRuleInfoModelList
                                ,List<PoolPermissionModel> permissionModels, Set<String> enterpriseIds,
                                Set<String> employeeIds) {
        String partnerId = objectData.get(PARTNER_ID, String.class);
        if (!Strings.isNullOrEmpty(partnerId)) {
            enterpriseIds.add(partnerId);
        }
        for (PoolPermissionModel permissionModel : permissionModels) {
            if (HighSeasEnums.PermissionType.OuterEmployee.getValue().equals(permissionModel.getType())) {
                employeeIds.add(permissionModel.getDataId());
            } else if (HighSeasEnums.PermissionType.OuterEnterprise.getValue().equals(permissionModel.getType())) {
                enterpriseIds.add(permissionModel.getDataId());
            }
        }
        for (AllocateRuleInfoModel allocateRuleInfoModel : allocateRuleInfoModelList) {
            List<AllocateRuleMemberModel> ruleMemberModelList = allocateRuleInfoModel.getMemberList();
            if (CollectionUtils.notEmpty(ruleMemberModelList)) {
                for (AllocateRuleMemberModel allocateRuleMemberModel : ruleMemberModelList) {
                    if (HighSeasEnums.PermissionType.OuterEmployee.getValue().equals(allocateRuleMemberModel.getMemberType())) {
                        employeeIds.add(allocateRuleMemberModel.getMemberId());
                    } else if (HighSeasEnums.PermissionType.OuterEnterprise.getValue().equals(allocateRuleMemberModel.getMemberType())) {
                        enterpriseIds.add(allocateRuleMemberModel.getMemberId());
                    }
                }
            }
        }
    }

    private void validatePoolDataArg(PoolServiceModel.PoolDataArg arg) {
        if (arg.getData() == null) {
            throw new ValidateException(I18N.text(Data_Is_Null));
        }
        if (arg.getData().toObjectData().getDescribeApiName() == null) {
            throw new ValidateException(I18N.text("paas.metadata.globalVariable.api_name_null"));
        }
        if (!Lists.newArrayList(SFAManagementPredefineObject.HighSeas.getApiName(),
                SFAManagementPredefineObject.LeadsPool.getApiName()).contains(arg.getData().toObjectData().getDescribeApiName())) {
            throw new ValidateException(I18N.text(API_NAME_NOT_POOL_OBJ));
        }
        if(CollectionUtils.notEmpty(arg.getRecyclingRuleList())){
            RecyclingRuleInfoModel recyclingRuleInfoModel = arg.getRecyclingRuleList().get(0);

            if(AccountUtil.getBooleanValue(arg.getData(),RecyclingRuleConstans.Field.SKIP_HOLIDAYS,false)
                    && ObjectUtils.isNotEmpty(recyclingRuleInfoModel.getIsAllowExtend()) && recyclingRuleInfoModel.getIsAllowExtend()){
                    throw new ValidateException(I18N.text(SFA_NOT_ALLOW_SAME_TIME_SELECT_EXTEND_AND_SKIP_HOLIDAYS));
            }
        }
    }

    private void initObjectData(IObjectData data, ServiceContext context) {
        String id = serviceFacade.generateId();
        data.setId(id);
        data.setTenantId(context.getTenantId());
        if (data.getDescribeApiName().equals(SFAManagementPredefineObject.HighSeas.getApiName())) {
            data.set(HighSeasConstants.Field.ACCOUNTS_COUNT, 0);
        }
    }

    private void sendChangePermissionCRMNotify(ServiceContext context, List<PoolPermissionModel> newAdminPermissionList, List<PoolPermissionModel> newMemberPermissionList,
                                               String apiName, String poolId, String name, List<PoolPermissionModel> oldPoolPermissions) {
        newAdminPermissionList.forEach(x -> x.setIsAdmin(true));
        newMemberPermissionList.forEach(x -> x.setIsAdmin(false));
        List<PoolPermissionModel> newPermissionList = Lists.newArrayList();
        newPermissionList.addAll(newAdminPermissionList);
        newPermissionList.addAll(newMemberPermissionList);
        List<String> newAdminUserIds = Lists.newArrayList();
        List<String> newMemberUserIds = Lists.newArrayList();
        List<String> cancelAdminUserIds = Lists.newArrayList();
        List<String> cancelMemberUserIds = Lists.newArrayList();
        // 是否成员变更
        oldPoolPermissions.removeIf(x -> x.getType().equals(HighSeasEnums.PermissionType.OuterEmployee.getValue()) ||
                x.getType().equals(HighSeasEnums.PermissionType.OuterEnterprise.getValue()));
        newPermissionList.removeIf(x -> x.getType().equals(HighSeasEnums.PermissionType.OuterEmployee.getValue()) ||
                x.getType().equals(HighSeasEnums.PermissionType.OuterEnterprise.getValue()));
        List<PoolPermissionModel> oldAdminPoolPermissionModel = oldPoolPermissions.stream().filter(x -> x.getIsAdmin()).collect(Collectors.toList());
        List<PoolPermissionModel> oldEmployeePoolPermissionModel = oldPoolPermissions.stream().filter(x -> !x.getIsAdmin()).collect(Collectors.toList());

        Set<String> newAdminMemberIds = getMemberIds(newAdminPermissionList, context);
        Set<String> newEmployeeMemberIds = getMemberIds(newMemberPermissionList, context);

        Set<String> oldAdminMemberIds = getMemberIds(oldAdminPoolPermissionModel, context);
        Set<String> oldEmployeeMemberIds = getMemberIds(oldEmployeePoolPermissionModel, context);

        newAdminUserIds.addAll(newAdminMemberIds);
        newAdminUserIds.removeAll(oldAdminMemberIds);

        newMemberUserIds.addAll(newEmployeeMemberIds);
        newMemberUserIds.removeAll(oldEmployeeMemberIds);

        cancelAdminUserIds.addAll(oldAdminMemberIds);
        cancelAdminUserIds.removeAll(newAdminMemberIds);

        cancelMemberUserIds.addAll(oldEmployeeMemberIds);
        cancelMemberUserIds.removeAll(newEmployeeMemberIds);

        String objectType = null;
        String poolApiName = null;
        if (apiName.equals(SFAManagementPredefineObject.HighSeas.getApiName())) {
            objectType = I18N.text("sfa.HighSeasObj.name");
            poolApiName = SFAManagementPredefineObject.HighSeas.getApiName();
        } else {
            objectType = I18N.text("sfa.LeadsPoolObj.name");
            poolApiName = SFAManagementPredefineObject.LeadsPool.getApiName();
        }
        if (CollectionUtils.notEmpty(newAdminUserIds)) {
            sendCRMNotification(context.getUser(), String.format(I18N.text("sfa.authorized.as.administrator"), objectType, name), 11, "", poolId, context.getUser().getUpstreamOwnerIdOrUserId(), newAdminUserIds);


            sendNewCRMNotification(context.getUser(), 11, "", String.format(I18N.text("sfa.authorized.as.administrator"), objectType, name), null, null,
                    SFA_TO_BE_POOL_ADMIN_NEW_RECORD,
                    Lists.newArrayList(CRMRemindRecordUtil.getI18ParameterKey(GetI18nKeyUtil.getDescribeDisplayNameKey(poolApiName)), name, CRMRemindRecordUtil.getUserNameById(serviceFacade, context.getUser())),
                    newAdminUserIds, poolApiName, poolId, CRMRemindRecordUtil.getUserNameById(serviceFacade, context.getUser()), context.getAppId());
        }
        if (CollectionUtils.notEmpty(newMemberUserIds)) {
            sendCRMNotification(context.getUser(), String.format(I18N.text("sfa.authorized.as.member"), objectType, name), 11, "", poolId, context.getUser().getUpstreamOwnerIdOrUserId(), newMemberUserIds);

            sendNewCRMNotification(
                    context.getUser(),
                    11,
                    "",
                    String.format(I18N.text("sfa.authorized.as.member"), objectType, name),
                    null,
                    null,
                    SFA_TO_BE_POOL_MEMBER_NEW_RECORD,
                    Lists.newArrayList(CRMRemindRecordUtil.getI18ParameterKey(GetI18nKeyUtil.getDescribeDisplayNameKey(poolApiName)), name, CRMRemindRecordUtil.getUserNameById(serviceFacade, context.getUser())),
                    newMemberUserIds,
                    poolApiName,
                    poolId,
                    CRMRemindRecordUtil.getUserNameById(serviceFacade, context.getUser()), context.getAppId());
        }

        if (CollectionUtils.notEmpty(cancelAdminUserIds)) {
            sendCRMNotification(context.getUser(), String.format(I18N.text("sfa.permissions.of.admin.have.been.revoked"), objectType, name), 11, "", poolId, context.getUser().getUpstreamOwnerIdOrUserId(), cancelAdminUserIds);

            sendNewCRMNotification(context.getUser(), 11, "", String.format(I18N.text("sfa.permissions.of.admin.have.been.revoked"), objectType, name), null, null,
                    SFA_TO_CANCEL_POOL_ADMIN_NEW_RECORD,
                    Lists.newArrayList(CRMRemindRecordUtil.getI18ParameterKey(GetI18nKeyUtil.getDescribeDisplayNameKey(poolApiName)), name, CRMRemindRecordUtil.getUserNameById(serviceFacade, context.getUser())),
                    cancelAdminUserIds, poolApiName, poolId, CRMRemindRecordUtil.getUserNameById(serviceFacade, context.getUser()), context.getAppId());

        }
        if (CollectionUtils.notEmpty(cancelMemberUserIds)) {
            sendCRMNotification(context.getUser(), String.format(I18N.text("sfa.permissions.of.member.have.been.revoked"), objectType, name), 11, "", poolId, context.getUser().getUpstreamOwnerIdOrUserId(), cancelMemberUserIds);

            sendNewCRMNotification(context.getUser(), 11, "", String.format(I18N.text("sfa.permissions.of.member.have.been.revoked"), objectType, name), null, null,
                    SFA_TO_CANCEL_POOL_MEMBER_NEW_RECORD,
                    Lists.newArrayList(CRMRemindRecordUtil.getI18ParameterKey(GetI18nKeyUtil.getDescribeDisplayNameKey(poolApiName)), name, CRMRemindRecordUtil.getUserNameById(serviceFacade, context.getUser())),
                    cancelMemberUserIds, poolApiName, poolId, CRMRemindRecordUtil.getUserNameById(serviceFacade, context.getUser()), context.getAppId());
        }
    }

    public Set<String> getMemberIds(List<PoolPermissionModel> poolPermissions,ServiceContext context){
        Set<String> members = new HashSet<>();
        ArrayList<String> employeeList = Lists.newArrayList();
        ArrayList<String> depIdList = Lists.newArrayList();
        ArrayList<String> userGroupList = Lists.newArrayList();
        ArrayList<String> userRoleList = Lists.newArrayList();
        poolPermissions.forEach(x -> {
            if (x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue())) {
                employeeList.add(x.getDataId());
            } else if (x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())){
                depIdList.add(x.getDataId());
            }else if (x.getType().equals(HighSeasEnums.PermissionType.UserGroup.getValue())){
                userGroupList.add(x.getDataId());
            }else if (x.getType().equals(HighSeasEnums.PermissionType.UserRole.getValue())){
                userRoleList.add(x.getDataId());
            }
        });

        if (CollectionUtils.notEmpty(employeeList)){
            members.addAll(employeeList);
        }
        if (CollectionUtils.notEmpty(depIdList)) {
            List<String> deptMember = orgService.getMembersByDeptIds(context.getUser(), depIdList);
            if (CollectionUtils.notEmpty(deptMember)){
                members.addAll(deptMember);
            }
        }
        if (CollectionUtils.notEmpty(userGroupList)) {
            List<String> userGroupMember = CommonBizOrgUtils.batchGetMembersByUserGroupIds(context.getTenantId(), userGroupList);
            if (CollectionUtils.notEmpty(userGroupMember)){
                members.addAll(userGroupMember);
            }
        }
        if (CollectionUtils.notEmpty(userRoleList)) {
            Set<String> userRoleMember = CommonBizOrgUtils.batchGetRoleUsersByRoleIds(context.getTenantId(), userRoleList);
            if (CollectionUtils.notEmpty(userRoleMember)){
                members.addAll(userRoleMember);
            }
        }
        return members;
    }

    private void validateUpdateRecyclingRule(PoolServiceModel.PoolDataArg arg, List<RecyclingRuleInfoModel> oldRecyclingRules) {
        if (!isRecyclingRuleChange(oldRecyclingRules, arg.getRecyclingRuleList())
                && !recyclingRuleExtendChange(oldRecyclingRules, arg.getRecyclingRuleList())) {
            return;
        }
        Optional<RecyclingRuleInfoModel> ruleInfoModel = oldRecyclingRules.stream().findFirst();
        if (ruleInfoModel.isPresent() && CollectionUtils.notEmpty(oldRecyclingRules) && (System.currentTimeMillis() - ruleInfoModel.get().getUpdateTime() < 1800000)) {
                throw new ValidateException(I18N.text(Recycling_Rule_Update_Too_Many_Times));
        }
    }

    private List<AllocateRuleInfoModel> getAllocateRuleByDataId(String apiName, String poolId, ServiceContext context) {
        List<AllocateRuleInfoModel> rst = Lists.newArrayList();
        if (SFAManagementPredefineObject.LeadsPool.getApiName().equals(apiName)) {
            List<WhereParam> allocateRuleWheres = Lists.newArrayList();
            CommonSqlUtils.addWhereParam(allocateRuleWheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
            CommonSqlUtils.addWhereParam(allocateRuleWheres, AllocateRuleConstants.Field.Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(poolId));
            CommonSqlUtils.addWhereParam(allocateRuleWheres, AllocateRuleConstants.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
            CommonSqlUtils.addWhereParam(allocateRuleWheres, AllocateRuleConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
            List<Object> ruleIdList = Lists.newArrayList();
            try {
                List<Map> allocateRuleRst = commonSqlService.select(LeadsPoolConstants.TableName.Pool_Allocate_Rule, allocateRuleWheres, convert2ActionContext(context));
                if (CollectionUtils.notEmpty(allocateRuleRst)) {
                    allocateRuleRst.forEach(r -> {
                        AllocateRuleInfoModel item = new AllocateRuleInfoModel();
                        item.setId(r.get(AllocateRuleConstants.Field.Id).toString());
                        item.setPoolId(r.get(AllocateRuleConstants.Field.Pool_Id).toString());
                        item.setPriority((Integer) r.get(AllocateRuleConstants.Field.Priority));
                        item.setAllocatePattern((Integer) r.get(AllocateRuleConstants.Field.Allocate_Pattern));
                        item.setIsAllPool((Boolean) r.get(AllocateRuleConstants.Field.Is_All_Sales_Clue));
                        item.setWheres(r.get(AllocateRuleConstants.Field.Wheres).toString());
                        item.setMemberWheres(r.get(AllocateRuleConstants.Field.MEMBER_WHERES) == null ? "[]" : r.get(AllocateRuleConstants.Field.MEMBER_WHERES).toString());
                        Object objValue = r.get(AllocateRuleConstants.Field.Is_Reset_Allocate_Sequence);
                        if (objValue != null) {
                            item.setIsResetAllocateSequence((Boolean) r.get(AllocateRuleConstants.Field.Is_Reset_Allocate_Sequence));
                        } else {
                            item.setIsResetAllocateSequence(true);
                        }
                        ruleIdList.add(r.get(AllocateRuleConstants.Field.Id).toString());
                        rst.add(item);
                    });
                }
            } catch (MetadataServiceException e) {
                log.error(e.getMessage(),e);
            }
            if (CollectionUtils.empty(ruleIdList)) {
                return rst;
            }
            Map<String, List<AllocateRuleMemberModel>> allocateRuleMembers = new HashMap<>();
            List<WhereParam> memberWheres = Lists.newArrayList();
            CommonSqlUtils.addWhereParam(memberWheres, AllocateRuleMemberConstants.Field.Allocate_Rule_Id, CommonSqlOperator.IN, ruleIdList);
            CommonSqlUtils.addWhereParam(memberWheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(context.getTenantId()));
            CommonSqlUtils.addWhereParam(memberWheres, AllocateRuleMemberConstants.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));

            CommonSqlSearchTemplate sqlSearchTemplate = new CommonSqlSearchTemplate();
            sqlSearchTemplate.setTableName(LeadsPoolConstants.TableName.Pool_Allocate_Rule_Member);
            sqlSearchTemplate.setWhereParamList(memberWheres);
            sqlSearchTemplate.setLimit(1000);
            OrderBy orderBy = new OrderBy();
            orderBy.setIsAsc(true);
            orderBy.setFieldName(AllocateRuleConstants.Field.Last_Modified_Time);
            List<OrderBy> orderByList = Lists.newArrayList(orderBy);
            sqlSearchTemplate.setOrderByList(orderByList);
            try {
                List<Map> members = commonSqlService.select(sqlSearchTemplate, convert2ActionContext(context));
                if (CollectionUtils.notEmpty(members)) {
                    members.forEach(m -> {
                        AllocateRuleMemberModel member = new AllocateRuleMemberModel();
                        if(m.get(AllocateRuleMemberConstants.Field.Member_Type) != null) {
                            member.setMemberType((Integer)m.get(AllocateRuleMemberConstants.Field.Member_Type));
                        }
                        if(m.get(AllocateRuleMemberConstants.Field.Member_Id) != null) {
                            member.setMemberId(m.get(AllocateRuleMemberConstants.Field.Member_Id).toString());
                        }
                        if(m.get(AllocateRuleMemberConstants.Field.Priority) != null) {
                            member.setPriority((Integer) m.get(AllocateRuleMemberConstants.Field.Priority));
                        }
                        if(m.get(AllocateRuleMemberConstants.Field.Weight) != null) {
                            member.setWeight((Integer) m.get(AllocateRuleMemberConstants.Field.Weight));
                        }
                        if(m.get(AllocateRuleMemberConstants.Field.Member_Wheres) != null) {
                            member.setMemberWheres(m.get(AllocateRuleMemberConstants.Field.Member_Wheres).toString());
                        }
                        String key = m.get(AllocateRuleMemberConstants.Field.Allocate_Rule_Id).toString();
                        if (allocateRuleMembers.containsKey(key)) {
                            allocateRuleMembers.get(key).add(member);
                        } else {
                            allocateRuleMembers.put(key, Lists.newArrayList(member));
                        }
                    });
                }
            } catch (MetadataServiceException e) {
                log.error(e.getMessage(),e);
            }
            for (AllocateRuleInfoModel allocateRule : rst) {
                String key = allocateRule.getId();
                if (allocateRuleMembers.containsKey(key)) {
                    List<AllocateRuleMemberModel> ruleMemberModels = allocateRuleMembers.get(key);
                    allocateRule.setMemberList(ruleMemberModels);

                    if (Objects.equals(allocateRule.getAllocatePattern()
                            , LeadsPoolEnums.LeadsPoolAllocatePatternType.WeightAllocate.getValue())) {
                        ruleMemberModels.forEach(m -> {
                            if(m.getMemberWheres() != null) {
                                m.setMemberRuleType(AllocateRuleMemberConstants.MemberRuleType.BY_WHERES);
                            } else {
                                m.setMemberRuleType(AllocateRuleMemberConstants.MemberRuleType.BY_MEMBER);
                            }
                        });
                    }
                }
            }
        }
        return rst;
    }

    private boolean isBindRecyclingRule(String poolId, String apiName, ServiceContext serviceContext) {
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(serviceContext.getTenantId()));
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Target_Pool_Id, CommonSqlOperator.EQ, Lists.newArrayList(poolId));
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Data_Id, CommonSqlOperator.NEQ, Lists.newArrayList(poolId));

        boolean rst = true;
        try {
            List<Map> qRst = commonSqlService.select(HighSeasConstants.TableName.RECYCLING_RULE,
                    wheres, convert2ActionContext(serviceContext));
            if (CollectionUtils.empty(qRst)) {
                rst = false;
            }
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
        return rst;
    }

    private boolean isBindLimitOverRule(String poolId, String apiName, ServiceContext serviceContext) {
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(serviceContext.getTenantId()));
        CommonSqlUtils.addWhereParam(wheres, OBJECT_POOL_ID_FIELD, CommonSqlOperator.EQ, Lists.newArrayList(poolId));
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Is_Delete, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtils.addWhereParam(wheres, RecyclingRuleConstans.Field.Object_Api_Name, CommonSqlOperator.EQ, Lists.newArrayList(apiName));
        boolean rst = true;
        try {
            List<Map> qRst = commonSqlService.select(OBJECT_LIMIT_OVER_RULE_TABLE,
                    wheres, convert2ActionContext(serviceContext));
            if (CollectionUtils.empty(qRst)) {
                rst = false;
            }
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
        return rst;
    }

    protected void addNewPoolLog(ServiceContext context, String apiName, String name) {
        String message = String.format("%s", name);
        String objectApiNameArg = SFAManagementPredefineObject.HighSeas.getApiName().equals(apiName) ? "18" : "17";
        addSettingLog(context, message, apiName, objectApiNameArg, "1");
    }

    protected String getPoolPermissionContent(ServiceContext serviceContext, PoolServiceModel.PoolDataArg arg, List<PoolPermissionModel> oldPoolPermissions) {
        StringBuilder content = new StringBuilder();
        Set<String> employeeIds = Sets.newHashSet();
        Set<String> circleIds = Sets.newHashSet();
        employeeIds.addAll(oldPoolPermissions.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        circleIds.addAll(oldPoolPermissions.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));

        employeeIds.addAll(arg.getAdminPoolPermissions().stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        circleIds.addAll(arg.getAdminPoolPermissions().stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));

        employeeIds.addAll(arg.getMemberPoolPermissions().stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        circleIds.addAll(arg.getMemberPoolPermissions().stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));

        List<UserInfoExt> userInfoExts = orgService.getUserExtByIds(serviceContext.getTenantId(), serviceContext.getUser().getUpstreamOwnerIdOrUserId(), Lists.newArrayList(employeeIds));
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = orgService.getDeptInfoNameByIds(serviceContext.getTenantId(), serviceContext.getUser().getUpstreamOwnerIdOrUserId(), Lists.newArrayList(circleIds));
        Set<String> oldAdminEmployeeIds = oldPoolPermissions.stream().filter(x -> Boolean.TRUE.equals(x.getIsAdmin()) && x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue())).map(x -> x.getDataId()).collect(Collectors.toSet());
        Set<String> oldAdminCircleIds = oldPoolPermissions.stream().filter(x -> Boolean.TRUE.equals(x.getIsAdmin()) && x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())).map(x -> x.getDataId()).collect(Collectors.toSet());
        List<String> oldEmployeeNames = userInfoExts.stream().filter(x -> oldAdminEmployeeIds.contains(x.getId())).map(x -> x.getName()).collect(Collectors.toList());
        List<String> oldCircleNames = deptInfos.stream().filter(x -> oldAdminCircleIds.contains(x.getDeptId())).map(x -> x.getDeptName()).collect(Collectors.toList());

        Set<String> newAdminEmployeeIds = arg.getAdminPoolPermissions().stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue())).map(x -> x.getDataId()).collect(Collectors.toSet());
        Set<String> newAdminCircleIds = arg.getAdminPoolPermissions().stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())).map(x -> x.getDataId()).collect(Collectors.toSet());
        List<String> newEmployeeNames = userInfoExts.stream().filter(x -> newAdminEmployeeIds.contains(x.getId())).map(x -> x.getName()).collect(Collectors.toList());
        List<String> newCircleNames = deptInfos.stream().filter(x -> newAdminCircleIds.contains(x.getDeptId())).map(x -> x.getDeptName()).collect(Collectors.toList());

        content.append(String.format("%s: %s%s %s: %s%s ",
                I18N.text(SFAManagamentI18NKeys.SFA_POOL_OLDADMIN),
                CollectionUtils.notEmpty(oldEmployeeNames) ? String.join(",", oldEmployeeNames) : "",
                CollectionUtils.notEmpty(oldCircleNames) ? String.join(",", oldCircleNames) : "",
                I18N.text(SFAManagamentI18NKeys.SFA_POOL_NEWADMIN),
                CollectionUtils.notEmpty(newEmployeeNames) ? String.join(",", newEmployeeNames) : "",
                CollectionUtils.notEmpty(newCircleNames) ? String.join(",", newCircleNames) : ""
        ));

        Set<String> oldMemberEmployeeIds = oldPoolPermissions.stream().filter(x -> Boolean.FALSE.equals(x.getIsAdmin()) && x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue())).map(x -> x.getDataId()).collect(Collectors.toSet());
        Set<String> oldMemberCircleIds = oldPoolPermissions.stream().filter(x -> Boolean.FALSE.equals(x.getIsAdmin()) && x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())).map(x -> x.getDataId()).collect(Collectors.toSet());
        oldEmployeeNames = userInfoExts.stream().filter(x -> oldMemberEmployeeIds.contains(x.getId())).map(x -> x.getName()).collect(Collectors.toList());
        oldCircleNames = deptInfos.stream().filter(x -> oldMemberCircleIds.contains(x.getDeptId())).map(x -> x.getDeptName()).collect(Collectors.toList());

        Set<String> newMemberEmployeeIds = arg.getMemberPoolPermissions().stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue())).map(x -> x.getDataId()).collect(Collectors.toSet());
        Set<String> newMemberCircleIds = arg.getMemberPoolPermissions().stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())).map(x -> x.getDataId()).collect(Collectors.toSet());
        newEmployeeNames = userInfoExts.stream().filter(x -> newMemberEmployeeIds.contains(x.getId())).map(x -> x.getName()).collect(Collectors.toList());
        newCircleNames = deptInfos.stream().filter(x -> newMemberCircleIds.contains(x.getDeptId())).map(x -> x.getDeptName()).collect(Collectors.toList());

        content.append(String.format("%s: %s%s %s: %s%s ",
                I18N.text(SFAManagamentI18NKeys.SFA_POOL_OLDMEMBER),
                CollectionUtils.notEmpty(oldEmployeeNames) ? String.join(",", oldEmployeeNames) : "",
                CollectionUtils.notEmpty(oldCircleNames) ? String.join(",", oldCircleNames) : "",
                I18N.text(SFAManagamentI18NKeys.SFA_POOL_NEWMEMBER),
                CollectionUtils.notEmpty(newEmployeeNames) ? String.join(",", newEmployeeNames) : "",
                CollectionUtils.notEmpty(newCircleNames) ? String.join(",", newCircleNames) : ""
        ));

        return content.toString();
    }

    protected void addSettingLog(ServiceContext context, String message, String objectName, String module, String bizOperationName) {
        String userName = context.getUser().getUserName();
        if (StringUtils.isEmpty(userName)) {
            User userInfo = orgService.getUser(context.getTenantId(), context.getUser().getUpstreamOwnerIdOrUserId());
            if (userInfo != null) {
                userName = userInfo.getUserName();
            }
        }
        SettingLogModel.SetttingLogInfo settingLog = SettingLogModel.SetttingLogInfo.builder()
                .appId("CRM")
                .bizOperationName(bizOperationName)
                .corpId(context.getTenantId())
                .module(module)
                .userId(context.getUser().getUpstreamOwnerIdOrUserId())
                .userName(userName)
                .textMessage(message)
                .objectName(objectName)
                .operationTime(System.currentTimeMillis())
                .jsonMessage(SettingLogModel.JsonText.builder().
                        textMsg(SettingLogModel.TexMsg.builder().dataID("").objectType(0).type(0).text(message).build()).build())
                .build();
        SettingLogModel.Arg settingArg = SettingLogModel.Arg.builder()
                .log(Lists.newArrayList(settingLog))
                .build();
        settingLogProxy.recordLog(FSRestHeaderUtil.getCrmHeader(context.getTenantId(), context.getUser().getUpstreamOwnerIdOrUserId()),
                settingArg);
    }

    protected Map<String, Integer> getPoolUnAllocatedCount(String tenantId, List<String> poolIds) {
        if(CollectionUtils.empty(poolIds)) {
            return Maps.newHashMap();
        }
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        SearchUtil.fillFilterEq(filters, "biz_status", getPoolUnAllocatedStatusValue());
        
        SearchUtil.fillFilterIn(filters, getPoolDataPoolIdApiName(), poolIds);
        Map<String, Integer> result = CommonSqlUtils.getGroupByResult(tenantId, getPoolDataApiName(), getPoolDataPoolIdApiName(), filters, true);
        return result;

//        if(poolIds.size() > 1) {
//            SearchUtil.fillFilterIn(filters, getPoolDataPoolIdApiName(), poolIds);
//            Map<String, Integer> result = CommonSqlUtils.getGroupByResult(tenantId, getPoolDataApiName(), getPoolDataPoolIdApiName(), filters, true);
//            return result;
//        } else {
//            String poolId = poolIds.get(0);
//            SearchTemplateQuery query = new SearchTemplateQuery();
//            SearchUtil.fillFilterEq(filters, getPoolDataPoolIdApiName(), poolId);
//            query.setFilters(filters);
//            query.setSearchSource("es");
//            Count countFieldDescribe = getCountField();
//            int totalCount = 0;
//            Object objResult = serviceFacade.getCountValue(tenantId, countFieldDescribe, query);
//            if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
//                totalCount = Integer.parseInt(objResult.toString());
//            }
//            Map<String, Integer> result = Maps.newHashMap();
//            result.put(poolId, totalCount);
//            return result;
//        }
    }

    private Count getCountField() {
        Count countFieldDescribe = new CountFieldDescribe();
        countFieldDescribe.setApiName(getPoolDataApiName());
        countFieldDescribe.setFieldApiName("totalcount");
        countFieldDescribe.setSubObjectDescribeApiName(getPoolDataApiName());
        countFieldDescribe.setCountFieldApiName("id");
        countFieldDescribe.setCountType(Count.TYPE_COUNT);
        countFieldDescribe.setReturnType("number");
        countFieldDescribe.setDecimalPlaces(0);
        return countFieldDescribe;
    }

    @Override
    public Map<String, Integer> getPoolTotalCount(String tenantId, List<String> poolIds) {
        if(CollectionUtils.empty(poolIds)) {
            return Maps.newHashMap();
        }
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);

        SearchUtil.fillFilterIn(filters, getPoolDataPoolIdApiName(), poolIds);
        Map<String, Integer> result = CommonSqlUtils.getGroupByResult(tenantId, getPoolDataApiName(), getPoolDataPoolIdApiName(), filters, true);
        return result;

//        if(poolIds.size() > 1) {
//            SearchUtil.fillFilterIn(filters, getPoolDataPoolIdApiName(), poolIds);
//            Map<String, Integer> result = CommonSqlUtils.getGroupByResult(tenantId, getPoolDataApiName(), getPoolDataPoolIdApiName(), filters, true);
//            return result;
//        } else {
//            String poolId = poolIds.get(0);
//            SearchTemplateQuery query = new SearchTemplateQuery();
//            SearchUtil.fillFilterEq(filters, getPoolDataPoolIdApiName(), poolId);
//            query.setFilters(filters);
//            query.setSearchSource("es");
//            Count countFieldDescribe = getCountField();
//            int totalCount = 0;
//            Object objResult = serviceFacade.getCountValue(tenantId, countFieldDescribe, query);
//            if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
//                totalCount = Integer.parseInt(objResult.toString());
//            }
//            Map<String, Integer> result = Maps.newHashMap();
//            result.put(poolId, totalCount);
//            return result;
//        }
    }

    protected boolean isExistPoolName(String name, String tenantId, String dataId) {
        ISearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.addFilter(Operator.EQ, IObjectData.DESCRIBE_API_NAME, getApiName());
        queryExt.addFilter(Operator.EQ, Tenantable.TENANT_ID, tenantId);
        queryExt.addFilter(Operator.EQ, IObjectData.NAME, name);
        // 更新时，id不为空，用id去除自身的重复
        if (StringUtils.isNotEmpty(dataId)) {
            queryExt.addFilter(Operator.NEQ, DBRecord.ID, dataId);
        }

        IActionContext actionContext = ActionContextExt.of(User.systemUser(tenantId)).allowUpdateInvalid(false)
                .setSkipRelevantTeam(true).getContext();
        List<IObjectData> dataList = serviceFacade
                .findBySearchTemplateQueryWithFields(actionContext, getApiName(), queryExt.toSearchTemplateQuery(),
                        Lists.newArrayList(DBRecord.ID, IObjectData.NAME)).getData();

        return !CollectionUtils.empty(dataList);
    }

    protected int getPoolCount(String tenantId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        query.setFilters(filters);
        CountFieldDescribe count = new CountFieldDescribe();
        count.setApiName(getApiName());
        count.setFieldApiName("count0");
        count.setCountFieldApiName("id");
        count.setSubObjectDescribeApiName(getApiName());
        count.setCountType(Count.TYPE_COUNT);
        count.setReturnType("number");
        count.setDecimalPlaces(0);
        int result = 0;
        try {
            Object calculateValue = dataProxy.getCountResult(tenantId, count.getSubObjectDescribeApiName(), count, query);
            if (!Objects.isNull(calculateValue)) {
                result = Integer.parseInt(calculateValue.toString());
            }
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
        return result;
    }

    protected void sendCRMNotification(User user, String remindContent, Integer remindRecordType, String title
            , String dataId, String content2Id, List<String> receiverIds) {
        receiverIds.removeIf(x -> user.getUpstreamOwnerIdOrUserId().equals(x));
        receiverIds = receiverIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(receiverIds)) {
            return;
        }

        List<Integer> realReceiverIds = Lists.newArrayList();
        for (String receiverId : receiverIds) {
            if (StringUtils.isBlank(receiverId)) {
                continue;
            }
            try {
                Integer realId = Integer.valueOf(receiverId);
                realReceiverIds.add(realId);
            } catch (Exception e) {
                log.error("accountutil sendCRMNotification error", e);
            }
        }
        if (StringUtils.isEmpty(content2Id)) {
            content2Id = user.getUpstreamOwnerIdOrUserId();
        } else {
            try {
                Integer realId = Integer.valueOf(content2Id);
            } catch (Exception e) {
                content2Id = user.getUpstreamOwnerIdOrUserId();
            }
        }
        CRMNotification crmNotification = CRMNotification.builder().sender(user.getUpstreamOwnerIdOrUserId())
                .remindRecordType(remindRecordType).title(title).content(remindContent).dataId(dataId)
                .content2Id(content2Id)
                .receiverIds(realReceiverIds.stream().collect(Collectors.toSet()))
                .build();
        crmNotificationService.sendCRMNotification(user, crmNotification);
    }

    protected void sendNewCRMNotification(User user, Integer remindType, String title, String content,
                                          String titleKey, List<String> titleParameters, String contentKey,
                                          List<String> contentParameters, List<String> receiverIds, String describeApiName, String dataId, String opUserName, String appId) {

        CRMRemindRecordUtil.sendNewCRMRecord(crmNotificationService, user, remindType, CRMRemindRecordUtil.getReceivedIds(receiverIds), "",
                title, String.format("%s "+I18N.text("sfa.operator.name")+": %s", content, opUserName), titleKey, titleParameters, contentKey, contentParameters,
                CRMRemindRecordUtil.getUrlParameter(describeApiName, dataId), appId);
    }

    protected OpenApiModel.PoolMQContent getPoolMQContent(ServiceContext context, List<PoolPermissionModel> adminPermission,
                                                        List<PoolPermissionModel> memberPermission, IObjectData data) {
        Set<String> adminUserIds = Sets.newHashSet();
        Set<String> adminCircleIds = Sets.newHashSet();
        Set<String> adminUserGroupIds = Sets.newHashSet();
        Set<String> adminUserRoleIds = Sets.newHashSet();
        adminUserIds.addAll(adminPermission.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        adminCircleIds.addAll(adminPermission.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        adminUserGroupIds.addAll(adminPermission.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.UserGroup.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        adminUserRoleIds.addAll(adminPermission.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.UserRole.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        adminUserIds.addAll(orgService.getMembersByDeptIds(context.getUser(), Lists.newArrayList(adminCircleIds)));
        adminUserIds.addAll(CommonBizOrgUtils.batchGetMembersByUserGroupIds(context.getTenantId(), Lists.newArrayList(adminUserGroupIds)));
        adminUserIds.addAll(CommonBizOrgUtils.batchGetRoleUsersByRoleIds(context.getTenantId(), Lists.newArrayList(adminUserRoleIds)));
        List<UserInfoExt> userInfoExts = orgService.getUserExtByIds(context.getTenantId(), context.getUser().getUpstreamOwnerIdOrUserId(), Lists.newArrayList(adminUserIds));
        userInfoExts.removeIf(x -> !x.isActive());
        Set<Integer> adminIds = Sets.newHashSet();
        adminIds.addAll(userInfoExts.stream().map(x -> Integer.valueOf(x.getId())).collect(Collectors.toList()));

        Set<String> employeeIds = Sets.newHashSet();
        Set<String> circleIds = Sets.newHashSet();
        Set<String> userGroupIds = Sets.newHashSet();
        Set<String> userRoleIds = Sets.newHashSet();
        circleIds.addAll(memberPermission.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Circle.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        userGroupIds.addAll(memberPermission.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.UserGroup.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        userRoleIds.addAll(memberPermission.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.UserRole.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        employeeIds.addAll(memberPermission.stream().filter(x -> x.getType().equals(HighSeasEnums.PermissionType.Employee.getValue())).map(x -> x.getDataId()).collect(Collectors.toList()));
        employeeIds.addAll(orgService.getMembersByDeptIds(context.getUser(), Lists.newArrayList(circleIds)));
        employeeIds.addAll(CommonBizOrgUtils.batchGetMembersByUserGroupIds(context.getTenantId(), Lists.newArrayList(userGroupIds)));
        employeeIds.addAll(CommonBizOrgUtils.batchGetRoleUsersByRoleIds(context.getTenantId(), Lists.newArrayList(userRoleIds)));
        userInfoExts = orgService.getUserExtByIds(context.getTenantId(), context.getUser().getUpstreamOwnerIdOrUserId(), Lists.newArrayList(employeeIds));
        userInfoExts.removeIf(x -> !x.isActive());
        Set<Integer> memberIds = Sets.newHashSet();
        memberIds.addAll(userInfoExts.stream().map(x -> Integer.valueOf(x.getId())).collect(Collectors.toList()));
        Set<Integer> allCircleIds = Sets.newHashSet();
        allCircleIds.addAll(adminCircleIds.stream().map(Integer::valueOf).collect(Collectors.toList()));
        allCircleIds.addAll(circleIds.stream().map(Integer::valueOf).collect(Collectors.toList()));

        Set<String> allUserGroupIds = Sets.newHashSet();
        allUserGroupIds.addAll(adminUserGroupIds);
        allUserGroupIds.addAll(userGroupIds);

        Set<String> allUserRoleIds = Sets.newHashSet();
        allUserRoleIds.addAll(adminUserRoleIds);
        allUserRoleIds.addAll(userRoleIds);

        OpenApiModel.PoolMQContent poolMQContent = OpenApiModel.PoolMQContent.builder()
                .ea(eieaConverter.enterpriseIdToAccount(Integer.parseInt(context.getTenantId())))
                .apiName(getApiName())
                .objectID(data.getId())
                .adminIDs(Lists.newArrayList(adminIds))
                .employeeIds(Lists.newArrayList(memberIds))
                .name(data.getName())
                .circleIds(Lists.newArrayList(allCircleIds))
                .userGroupList(Lists.newArrayList(allUserGroupIds))
                .roleList(Lists.newArrayList(allUserRoleIds))
                .build();
        return poolMQContent;
    }

    protected void sendPoolDeletedOpenApiMQ(ServiceContext context, IObjectData data) {
        OpenApiModel.DeleteHighSeasContent content = OpenApiModel.DeleteHighSeasContent.builder()
                .name(data.getName())
                .build();
        mgtOpenApiMqService.sendOpenApiMqWithFlag(context.getUser(), 1,
                ObjectAction.DELETE.getActionCode(),
                getApiName(), data.getId(), content);
    }

    protected void sendPoolVisibleMemberChangedOpenApiMQ(ServiceContext context, Integer value, IObjectData data) {
        OpenApiModel.PoolMemberVisibleChang content = OpenApiModel.PoolMemberVisibleChang.builder()
                .name(data.getName())
                .value(value)
                .build();
        mgtOpenApiMqService.sendOpenApiMqWithFlag(context.getUser(), 1,
                "PoolMemberVisibleChang",
                getApiName(), data.getId(), content);
    }

    protected void sendPoolMemberChangedOpenApiMQ(ServiceContext context, OpenApiModel.PoolMQContent poolMQContent, List<Integer> addMembers,
                                                     List<Integer> delMembers, IObjectData data) {
        OpenApiModel.PoolMemberChangContent content = OpenApiModel.PoolMemberChangContent.builder()
                .poolMQContent(poolMQContent)
                .addMemberIDs(addMembers)
                .deleteMembers(delMembers)
                .build();
        mgtOpenApiMqService.sendOpenApiMqWithFlag(context.getUser(), 1,
                "PoolMemberChang",
                getApiName(), data.getId(), content);
    }

    protected void sendPoolAdminChangedOpenApiMQ(ServiceContext context, OpenApiModel.PoolMQContent poolMQContent,
                                                    List<Integer> addAdmins, List<Integer> delAdmins, IObjectData data) {
        OpenApiModel.PoolAdminChangContent content = OpenApiModel.PoolAdminChangContent.builder()
                .poolMQContent(poolMQContent)
                .addAdminIDs(addAdmins)
                .deleteAdminIds(delAdmins)
                .build();
        mgtOpenApiMqService.sendOpenApiMqWithFlag(context.getUser(), 1,
                "PoolAdminChang",
                getApiName(), data.getId(), content);
    }

    protected void sendPoolNameChangedOpenApiMQ(ServiceContext context, OpenApiModel.PoolMQContent poolMQContent, IObjectData data) {
        OpenApiModel.EditHighSeasNameContent content = OpenApiModel.EditHighSeasNameContent.builder()
                .name(data.getName())
                .poolMQContent(poolMQContent)
                .build();
        mgtOpenApiMqService.sendOpenApiMqWithFlag(context.getUser(), 1,
                "EditObjectName",
                getApiName(), data.getId(), content);
    }

    protected void sendPoolRuleChangedOpenApiMQ(ServiceContext context, IObjectData data) {
        OpenApiModel.PoolRuleChangContent content = OpenApiModel.PoolRuleChangContent.builder()
                .name(data.getName())
                .build();
        mgtOpenApiMqService.sendOpenApiMqWithFlag(context.getUser(), 1,
                "PoolRuleChang",
                getApiName(), data.getId(), content);
    }

    protected void sendAddPoolOpenApiMQ(ServiceContext context, List<PoolPermissionModel> adminPermission,
                                           List<PoolPermissionModel> memberPermission, IObjectData data) {
        OpenApiModel.PoolMQContent poolMQContent = getPoolMQContent(context, adminPermission, memberPermission, data);
        OpenApiModel.AddHighSeasContent content = OpenApiModel.AddHighSeasContent.builder()
                .poolMQContent(poolMQContent)
                .addAdminIDs(poolMQContent.getAdminIDs())
                .deleteAdminIds(Lists.newArrayList())
                .build();
        mgtOpenApiMqService.sendOpenApiMqWithFlag(context.getUser(), 1,
                ActionCodeEnum.ADD.getActionCode(),
                getApiName(), data.getId(), content);
    }

    protected Integer getMaxRecyclingRuleNumber(User user, String configKey) {
        int maxRecyclingRuleNumber = 30;
        String queryRst = configService.findTenantConfig(user, configKey);
        if (StringUtils.isNotBlank(queryRst)) {
            maxRecyclingRuleNumber = Integer.parseInt(queryRst);
        }
        return maxRecyclingRuleNumber;
    }

    public List<IObjectData>  handleFunction(User user,List<IObjectData> resultData,String apiName){
        try {
            if(CollectionUtils.empty(resultData)){
                return resultData;
            }
            String value = configService.findTenantConfig(user,"sfa_filter_pool_list_function_name");
            if(ObjectUtils.isEmpty(value)){
                return resultData;
            }
            log.warn("handleFunction value :{}",value);
            IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user, value, "AccountObj");
            if (function == null) {
                log.warn("handleFunction function is null");
                return resultData;
            }
            List<String> ids = resultData.stream().map(IObjectData::getId).collect(Collectors.toList());
            Map<String, Object> parameters = Maps.newHashMap();
            parameters.put("userId", user.getUpstreamOwnerIdOrUserId());
            parameters.put("poolApiName", apiName);
            parameters.put("poolIds", ids);
            RunResult result =  serviceFacade.getFunctionLogicService().executeUDefFunction(user, function, parameters, null, null);
            if(ObjectUtils.isEmpty(result) || !result.isSuccess()){
                log.warn("handleFunction result error :{}",JSONObject.toJSONString(result));
                return resultData;
            }
            List<String> lastIds  = (List<String>)result.getFunctionResult();
            if(CollectionUtils.empty(lastIds)){
                log.warn("handleFunction lastIds is null");
                return Lists.newArrayList();
            }
            log.warn("handleFunction lastIds :{}",JSONObject.toJSONString(lastIds));
            return  resultData.stream().filter(x->lastIds.contains(x.getId())).collect(Collectors.toList());
        }catch (Exception e){
            log.error("handleFunction error :",e);
        }
        return resultData;
    }
}
