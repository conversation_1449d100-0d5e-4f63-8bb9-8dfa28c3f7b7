package com.facishare.crm.management.service.config;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.LayoutConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.privilege.service.impl.FunctionPrivilegeOperateServiceImpl;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.BizCommonConfigService;
import com.facishare.crm.sfa.predefine.service.CrmMenuInitService;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils;
import com.facishare.crm.sfa.utilities.util.VirtualFieldUtils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.crm.util.CRMRemindRecordUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.FuncCodePrivilege;
import com.facishare.paas.appframework.privilege.dto.FuncPermiss;
import com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService.IS_OPEN_ATTRIBUTE;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Component
@Slf4j
public class SPUBizConfigProvider extends DefaultBizConfigProvider {
    @Autowired
    EnterpriseInitService enterpriseInitService;
    @Autowired
    CRMNotificationServiceImpl crmNotificationService;
    @Autowired
    private RecordTypeLogicServiceImpl recordTypeLogicService;
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;
    @Autowired
    private FunctionPrivilegeOperateServiceImpl functionPrivilegeOperateService;
    @Autowired
    private ILayoutService layoutService;
    @Resource(name = "crmMenuInitServiceSFA")
    private CrmMenuInitService crmMenuInitService;
    @Autowired
    private BizCommonConfigService bizCommonConfigService;
    @Autowired
    LicenseService licenseService;
    @Autowired
    BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Resource
    private ProductCategoryUtils productCategoryUtils;

    @Override
    public String getConfigKey() {
        return BizConfigKey.SPU_OPEN.getId();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        /*
        开启更改ProductObj描述，初始化SPUObj、SpecificationObj、SpecificationValueObj等对象;
        关闭更改ProductObj描述，删除SPUObj、SpecificationObj、SpecificationValueObj等对象，迁移商品字段;
         */
        if (Objects.equals(oldValue, value)) {
            return;
        }
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() -> openOrCloseSpu(user, value, oldValue, key));
            task.run();
        } catch (Exception e) {
            log.warn("spu setting task error,tenantId {} {}", user.getTenantId(), e);
        }
    }

    @Override
    public void validateSetConfig(User user, String key, String new_value) {
        /*
        老企业只允许关闭，新企业只允许开启;
        开启时校验是否有产品数据;
        关闭时校验是否存在多规格商品、商品是否被lookup，选产品设置是否关闭态;
         */
        String changedValue = configService.findTenantConfig(user, "spu_setting_changed");
        if (!Strings.isNullOrEmpty(changedValue)) {
            throw new ValidateException(I18N.text("sfa.spu.config.operation.warn"));
        }
        if (!"0".equals(new_value) && !"1".equals(new_value)) {
            throw new ValidateException(I18N.text("paas.sf.param_error"));
        }
        if ("1".equals(new_value)) {
            validate2Open(user);
        }
        if ("0".equals(new_value)) {
            validate2Close(user);
        }
    }

    private void validate2Open(User user) {
        validateProductData(user);
        validateAttribute(user);
    }

    private void validateAttribute(User user) {
        //与属性互斥
        if (bizCommonConfigService.isOpen(IS_OPEN_ATTRIBUTE, user)) {
            throw new ValidateException(I18N.text("sfa.spu.config.attribute.warn"));
        }
    }

    private void validateProductData(User user) {
        if (GrayUtil.skipCheckProduct(user.getTenantId())) {
            return;
        }
        //已有产品数据，不能开启
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldValues(Lists.newArrayList("0", "1"));
        filter.setOperator(Operator.IN);
        filter.setFieldName("is_deleted");
        filters.add(filter);
        query.setFilters(filters);
        query.setOffset(0);
        query.setLimit(1);
        query.setPermissionType(0);
        query.setNeedReturnCountNum(Boolean.FALSE);
        query.setNeedReturnQuote(Boolean.FALSE);
        QueryResult<IObjectData> productResult = serviceFacade.findBySearchQuery(user,
                SFAPreDefineObject.Product.getApiName(), query);
        if (productResult != null && CollectionUtils.notEmpty(productResult.getData())) {
            throw new ValidateException(I18N.text("sfa.spu.config.product.data.warn"));
        }
    }

    private void validate2Close(User user) {
        validateSpuSelector(user);
        validateSpuRelation(user);
        validateSpuData(user);
    }

    private void validateSpuData(User user) {
        //存在多规格商品，不能关闭
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName("is_spec");
        filter.setFieldValues(Lists.newArrayList("true"));
        filter.setOperator(Operator.EQ);
        filters.add(filter);
        query.setFilters(filters);
        query.setOffset(0);
        query.setLimit(1);
        query.setPermissionType(0);
        query.setNeedReturnCountNum(Boolean.FALSE);
        query.setNeedReturnQuote(Boolean.FALSE);
        QueryResult<IObjectData> productResult = serviceFacade.findBySearchQuery(user,
                SFAPreDefineObject.SPU.getApiName(), query);
        if (productResult != null && CollectionUtils.notEmpty(productResult.getData())) {
            throw new ValidateException(I18N.text("sfa.spu.config.multi.spec.warn"));
        }
    }

    private void validateSpuRelation(User user) {
        //商品被lookup，不能关闭
        List<IObjectDescribe> describeList = serviceFacade.findLookupDescribes(user.getTenantId(),
                SFAPreDefineObject.SPU.getApiName(), false);
        Optional<IObjectDescribe> productDescribe = CollectionUtils.nullToEmpty(describeList).stream()
                .filter(objectDescribe -> SFAPreDefineObject.Product.getApiName().equals(objectDescribe.getApiName()))
                .findFirst();
        if (productDescribe.isPresent()) {
            List<IFieldDescribe> fields = productDescribe.get().getFieldDescribes();
            if (CollectionUtils.notEmpty(fields) && fields.stream()
                    .anyMatch(field -> String.valueOf(field.getDefaultValue()).contains("spu_id__r") ||
                            String.valueOf(field.getExpression()).contains("spu_id__r") ||
                            String.valueOf(field.getQuoteField()).contains("spu_id__r"))) {
                throw new ValidateException(I18N.text("sfa.spu.config.field.quote.warn"));
            }
        }
        List<IRule> ruleList = infraServiceFacade.findRuleList(user.getTenantId(), SFAPreDefineObject.Product.getApiName());
        if (CollectionUtils.notEmpty(ruleList)) {
            if (ruleList.stream().anyMatch(rule -> String.valueOf(rule.getCondition()).contains("spu_id__r"))) {
                throw new ValidateException(I18N.text("sfa.spu.config.field.validation.warn"));
            }
        }
        describeList.removeIf(describe -> SFAPreDefineObject.Product.getApiName().equals(describe.getApiName()));
        if (CollectionUtils.notEmpty(describeList)) {
            List<String> apiNames = describeList.stream().map(describe -> describe.getDisplayName()).collect(Collectors.toList());
            throw new ValidateException(String.format(I18N.text("sfa.spu.config.field.lookup.warn"), String.join("、", apiNames)));
        }
    }

    private void validateSpuSelector(User user) {
        //打开基于商品选择产品的开关，不能关闭
        String moduleConfig = configService.findTenantConfig(user, IModuleInitService.MODULE_SPU);
        if (Boolean.valueOf(moduleConfig)) {
            throw new ValidateException(I18N.text("sfa.spu.config.selector.warn"));
        }
    }

    private void openOrCloseSpu(User user, String value, String oldValue, String key) {
        List<String> describeNames = Lists.newArrayList(SFAPreDefineObject.SPU.getApiName(),
                SFAPreDefineObject.Specification.getApiName(),
                SFAPreDefineObject.SpecificationValue.getApiName());
        boolean isOpen = false;
        boolean success = true;
        IObjectDescribe objectDescribe = getDescribeWithSimplifiedChinese(user, SFAPreDefineObject.Product.getApiName());
        if (objectDescribe == null) {
            log.error("ProductObj describe not found");
            throw new ValidateException("describe not found");
        }
        List<IFieldDescribe> fieldsAdd2SpuLayout = Lists.newArrayList();
        List<IFieldDescribe> fieldsAdd2SkuLayout = Lists.newArrayList();
        String errorMsg = null;
        try {
            if ("1".equals(value)) {
                for (String apiName : describeNames) {
                    enterpriseInitService.initDescribeForTenant(user.getTenantId(), apiName);
                }
                enterpriseInitService.initPrivilegeRelate(describeNames, user, null,
                        DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PUBLIC_READONLY.getValue(), null);
                enterpriseInitService.initMultiLayoutForOneTenant(describeNames, user.getTenantId());
                crmMenuInitService.createMenuItem(user, describeNames, "");
                if (productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
                    descUtil.addRefField(user.getTenantId(), "product_category_id", "产品分类", "SPUObj", "ProductCategoryObj", false, true);// ignoreI18n
                    descUtil.setRequired(user, Lists.newArrayList("SPUObj"), false, "category");
                }
                isOpen = true;
            }
            processDescribe(objectDescribe, isOpen, fieldsAdd2SpuLayout, fieldsAdd2SkuLayout);
            updateProductDetailLayout(user.getTenantId(), objectDescribe, isOpen, fieldsAdd2SkuLayout);
            updateSPUDetailLayout(user.getTenantId(), fieldsAdd2SpuLayout);
            if (!isOpen) {
                updateProduct(objectDescribe);
            }
            deleteFields(objectDescribe, isOpen);
            if ("0".equals(value)) {
                for (String apiName : describeNames) {
                    if (!SFAPreDefineObject.SPU.getApiName().equals(apiName)) {
                        serviceFacade.deleteDescribeDirect(user, apiName);
                    }
                    //删除对应功能权限
                    serviceFacade.deleteFunctionPrivilege(user, apiName);
                }
            }
            if (isOpen) {
                syncRecordTypeRole(user.getTenantId());
                Set<String> roleSet = syncFunctionRight(user.getTenantId());
                syncFieldRight(user.getTenantId(), roleSet);
                addVirtualFields(user.getTenantId());
            }
            dealSaleOrder(user, isOpen);
            super.setConfigValue(user, value, oldValue, key);
            //增加一个配置，用于校验是否开启/关闭过
            configService.createTenantConfig(user, "spu_setting_changed", "1", ConfigValueType.STRING);
            sendMqAfterInitModuleSuccess(user.getTenantId());
        } catch (ValidateException ex) {
            success = false;
            errorMsg = ex.getMessage();
            log.error("spu setting error,tenantId {}", user.getTenantId(), ex);
        } catch (Exception e) {
            success = false;
            log.error("spu setting error,tenantId {}", user.getTenantId(), e);
        }
        sendNotification(user, success, isOpen, errorMsg);
    }

    private void addVirtualFields(String tenantId) {
        boolean virtualExtensionEnabled = bizConfigThreadLocalCacheService.isVirtualExtensionEnabled(tenantId);
        if (!virtualExtensionEnabled) {
            return;
        }
        List<IFieldDescribe> virtualFields = Lists.newArrayList();
        virtualFields.addAll(VirtualFieldUtils.getProductVirtualFields());
        boolean priceBookEnabled = bizConfigThreadLocalCacheService.isPriceBookEnabled(tenantId);
        if (priceBookEnabled) {
            boolean currencyEnabled = bizConfigThreadLocalCacheService.isCurrencyEnabled(tenantId);
            virtualFields.addAll(VirtualFieldUtils.getPriceBookVirtualFields(currencyEnabled));
        }
        boolean stockEnabled = bizConfigThreadLocalCacheService.isStockEnabled(tenantId);
        if (stockEnabled) {
            virtualFields.addAll(VirtualFieldUtils.getStockVirtualFields());
        }
        if (CollectionUtils.empty(virtualFields)) {
            return;
        }
        IObjectDescribe spuDescribe = getDescribeWithSimplifiedChinese(User.systemUser(tenantId), SFAPreDefineObject.SPU.getApiName());
        if (null == spuDescribe) {
            return;
        }
        List<IFieldDescribe> toAddFields = Lists.newArrayListWithExpectedSize(virtualFields.size());
        for(IFieldDescribe field : virtualFields) {
            IFieldDescribe fieldDescribe = spuDescribe.getFieldDescribe(field.getApiName());
            if (null == fieldDescribe){
                toAddFields.add(field);
            }
        }
        if (CollectionUtils.empty(toAddFields)) {
            return;
        }
        try {
            objectDescribeService.addCustomFieldDescribe(spuDescribe, toAddFields);
        } catch (MetadataServiceException e) {
            log.error("open spu add virtual fields error,tenantId {} ", tenantId, e);
        }
    }

    private void updateProductDetailLayout(String tenantId, IObjectDescribe objectDescribe, boolean isOpen,
                                           List<IFieldDescribe> fieldsAdd2SkuLayout) throws MetadataServiceException {
        List<ILayout> detailLayouts = serviceFacade.getLayoutLogicService().getDetailLayouts(tenantId, objectDescribe);
        if (CollectionUtils.empty(detailLayouts)) {
            return;
        }
        Map<String, Object> propertyNameValueMap = Maps.newHashMap();
        propertyNameValueMap.put("is_readonly", isOpen);
        List<String> toModifyFields = Lists.newArrayList("name", "product_spec", "unit",
                "category", "product_line", ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, ProductCategoryModel.Filed.SHOP_CATEGORY_ID);
        Map<String, Map<String, Object>> productNeedModifyFieldInfo = Maps.newHashMap();
        toModifyFields.forEach(f -> productNeedModifyFieldInfo.put(f, propertyNameValueMap));
        for (ILayout layout : detailLayouts) {
            LayoutExt layoutExt = LayoutExt.of(layout);
            Optional<FormComponentExt> formComponentExtOptional = layoutExt.getFormComponent();
            formComponentExtOptional.ifPresent(formComponentExt -> {
                Optional<IFieldSection> baseFieldSection = formComponentExt.getBaseFieldSection();
                baseFieldSection.ifPresent(o -> {
                    List<IFormField> formFields = o.getFields();
                    formFields.forEach(f -> {
                        if (toModifyFields.contains(f.getFieldName())) {
                            productNeedModifyFieldInfo.get(f.getFieldName()).forEach(f::set);
                        }
                    });
                    if (isOpen) {
                        if (formFields.stream().noneMatch(field -> "spu_id".equals(field.getFieldName()))) {
                            IFormField formField = new FormField();
                            formField.setFieldName("spu_id");
                            formField.setRenderType("object_reference");
                            formField.setRequired(true);
                            formField.setReadOnly(false);
                            formFields.add(formField);
                        }
                        formFields.removeIf(f -> ObjectDataExt.OWNER.equals(f.getFieldName()));
                    } else {
                        formFields.removeIf(f -> "spu_id".equals(f.getFieldName()));
                        Optional<IFormField> ownerField = formFields.stream()
                                .filter(f -> ObjectDataExt.OWNER.equals(f.getFieldName()))
                                .findFirst();
                        if (ownerField.isPresent()) {
                            ownerField.get().setRequired(true);
                        } else {
                            IFormField formField = new FormField();
                            formField.setFieldName(ObjectDataExt.OWNER);
                            formField.setRenderType("employee");
                            formField.setRequired(true);
                            formField.setReadOnly(false);
                            formFields.add(formField);
                        }
                        if (CollectionUtils.notEmpty(fieldsAdd2SkuLayout)) {
                            for (IFieldDescribe fieldDescribe : fieldsAdd2SkuLayout) {
                                addFormFields(formFields, fieldDescribe);
                            }
                        }
                    }
                    o.setFields(formFields);
                });
            });
            layoutService.replace(layout);
        }
    }

    private void addFormFields(List<IFormField> formFields, IFieldDescribe fieldDescribe) {
        formFields.removeIf(f -> fieldDescribe.getApiName().equals(f.getFieldName()));
        IFormField formField = new FormField();
        formField.setFieldName(fieldDescribe.getApiName());
        formField.setRenderType(fieldDescribe.getType());
        formField.setRequired(fieldDescribe.isRequired());
        formField.setReadOnly(false);
        formFields.add(formField);
    }

    private void updateSPUDetailLayout(String tenantId, List<IFieldDescribe> fieldsAdd2SpuLayout) throws MetadataServiceException {
        if (CollectionUtils.empty(fieldsAdd2SpuLayout)) {
            return;
        }
        List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(SFAPreDefineObject.SPU.getApiName(), tenantId);
        if (CollectionUtils.empty(layouts)) {
            return;
        }
        layouts = layouts.stream()
                .filter(x -> ILayout.DETAIL_LAYOUT_TYPE.equals(x.getLayoutType()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(layouts)) {
            return;
        }
        for (ILayout layout : layouts) {
            LayoutExt layoutExt = LayoutExt.of(layout);
            Optional<FormComponentExt> formComponentExtOptional = layoutExt.getFormComponent();
            formComponentExtOptional.ifPresent(formComponentExt -> {
                Optional<IFieldSection> baseFieldSection = formComponentExt.getBaseFieldSection();
                baseFieldSection.ifPresent(o -> {
                    List<IFormField> formFields = o.getFields();
                    for (IFieldDescribe fieldDescribe : fieldsAdd2SpuLayout) {
                        addFormFields(formFields, fieldDescribe);
                    }
                    o.setFields(formFields);
                });
            });
            layoutService.replace(layout);
        }
    }

    private void syncRecordTypeRole(String tenantId) {
        RecordTypeResult skuRoleAndRecordType = recordTypeLogicService.findRoleAndRecordType(tenantId,
                SFAPreDefineObject.Product.getApiName(), new User(tenantId, "-10000"));
        List<Map> roleList = skuRoleAndRecordType.getRole_list();
        if (CollectionUtils.notEmpty(roleList)) {
            List<RecordTypeResult.RoleInfo> roleInfoList = Lists.newArrayList();
            for (Map map : roleList) {
                RecordTypeResult.RoleInfo roleInfo = new RecordTypeResult.RoleInfo();
                roleInfo.setDefaultRecord("default__c");
                roleInfo.setRecords((List) map.getOrDefault("records", Lists.newArrayList()));
                roleInfo.setRoleCode(String.valueOf(map.get("roleCode")));
                roleInfoList.add(roleInfo);
            }
            recordTypeLogicService.assignRecord(tenantId, SFAPreDefineObject.SPU.getApiName(),
                    JSON.toJSONString(roleInfoList), new User(tenantId, "-10000"));
        }
    }

    private Set<String> syncFunctionRight(String tenantId) {
        Map<String, String> codeMap = getDefaultFuncMap();
        FuncCodePrivilege.Arg argFunc = new FuncCodePrivilege.Arg(AuthContext.builder()
                .appId(PrivilegeConstants.APP_ID)
                .tenantId(tenantId)
                .userId(User.SUPPER_ADMIN_USER_ID)
                .build(), Lists.newArrayList(codeMap.keySet()));
        FuncCodePrivilege.Result result = functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(argFunc, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(tenantId));
        Map<String, List<String>> roleFuncMap = Maps.newHashMap();
        if (result.isSuccess()) {
            result.getResult().forEach((k, v) -> {
                v.forEach(role -> {
                    if (roleFuncMap.containsKey(role)) {
                        roleFuncMap.get(role).add(codeMap.get(k));
                    } else {
                        roleFuncMap.put(role, Lists.newArrayList(codeMap.get(k)));
                    }
                });
            });
        }
        if (CollectionUtils.notEmpty(roleFuncMap)) {
            roleFuncMap.forEach((k, v) -> {
                List<String> spuFuncCodes = Lists.newArrayList(codeMap.values());
                spuFuncCodes.removeAll(v);
                UpdateRoleModifiedFuncPrivilege.Arg roleArg = new UpdateRoleModifiedFuncPrivilege.Arg(AuthContext.builder()
                        .appId(PrivilegeConstants.APP_ID)
                        .tenantId(tenantId)
                        .userId(User.SUPPER_ADMIN_USER_ID)
                        .build(),
                        k, v, spuFuncCodes,null);
                functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(roleArg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(tenantId));
            });
        }
        return roleFuncMap.keySet();
    }

    private void syncFieldRight(String tenantId, Set<String> roleSet) {
        if (CollectionUtils.empty(roleSet)) {
            return;
        }
        //移除管理员
        roleSet.remove("00000000000000000000000000000006");
        //同步商品字段权限的产品字段
        //去掉单位、分类、批次序列号，必填字段，无需同步
        List<String> syncFields = Lists.newArrayList("product_line", "commodity_label");
        roleSet.forEach(role -> {
            FuncPermiss.RolesEntitysFieldPermissArg rolePermissionArg = new FuncPermiss.RolesEntitysFieldPermissArg(AuthContext.builder()
                    .appId(PrivilegeConstants.APP_ID)
                    .tenantId(tenantId)
                    .userId(User.SUPPER_ADMIN_USER_ID)
                    .build(),
                    Lists.newArrayList(role),
                    Lists.newArrayList(SFAPreDefineObject.Product.getApiName()));
            FuncPermiss.UserEntitysFieldPermissResponse response = functionPrivilegeProxy.batchQueryRolesEntitysFieldPermiss(rolePermissionArg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(tenantId));
            if (response != null && CollectionUtils.notEmpty(response.getResult())) {
                Map<String, Integer> fieldPermissionMap = response.getResult().getOrDefault(SFAPreDefineObject.Product.getApiName(), Maps.newHashMap());
                Map<String, Integer> fieldMap = Maps.newHashMap();
                fieldPermissionMap.forEach((k, v) -> {
                    if (v != 2 && syncFields.contains(k)) {
                        fieldMap.put(k, v);
                    }
                });
                if (CollectionUtils.notEmpty(fieldMap)) {
                    try {
                        functionPrivilegeOperateService.updateRoleObjectFieldsPrivilege(tenantId, role,
                                SFAPreDefineObject.SPU.getApiName(), fieldMap);
                    } catch (Exception e) {
                        log.error("spu setting sync field permission error,tenantId {}", tenantId, e);
                    }
                }
            }
        });
    }

    private Map<String, String> getDefaultFuncMap() {
        Map<String, String> funcMap = new HashMap<>(32);
        funcMap.put("ProductObj", "SPUObj");
        funcMap.put("ProductObj||View", "SPUObj||View");
        funcMap.put("ProductObj||Add", "SPUObj||Add");
        funcMap.put("ProductObj||Edit", "SPUObj||Edit");
        funcMap.put("ProductObj||Abolish", "SPUObj||Abolish");
        funcMap.put("ProductObj||Recover", "SPUObj||Recover");
        funcMap.put("ProductObj||Delete", "SPUObj||Delete");
        funcMap.put("ProductObj||Import", "SPUObj||Import");
        funcMap.put("ProductObj||Export", "SPUObj||Export");
        funcMap.put("ProductObj||ChangeOwner", "SPUObj||ChangeOwner");
        funcMap.put("ProductObj||EditTeamMember", "SPUObj||EditTeamMember");
        funcMap.put("ProductObj||Print", "SPUObj||Print");
        funcMap.put("ProductObj||Lock", "SPUObj||Lock");
        funcMap.put("ProductObj||Unlock", "SPUObj||Unlock");
        return funcMap;
    }

    private void sendNotification(User user, boolean success, boolean isOpen, String message) {
        String content;
        String title;
        String titleKey = null;
        String contentKey = null;
        if (success) {
            if (isOpen) {
                content = "您好，商品已经成功开启！";// ignoreI18n
                title = "商品开启成功";// ignoreI18n
                titleKey = SFA_OPEN_SPU_SUCCESS_TITLE_NEW_RECORD;
                contentKey = SFA_OPEN_SPU_SUCCESS_CONTENT_NEW_RECORD;
            } else {
                content = "您好，商品已经成功关闭！";// ignoreI18n
                title = "商品关闭成功";// ignoreI18n

                titleKey = SFA_CLOSE_SPU_SUCCESS_TITLE_NEW_RECORD;
                contentKey = SFA_CLOSE_SPU_SUCCESS_CONTENT_NEW_RECORD;
            }
        } else {
            if (isOpen) {
                content = "您好，商品开启失败，请重新操作开启！";// ignoreI18n
                title = "商品开启失败";// ignoreI18n

                titleKey = SFA_OPEN_SPU_FAILURE_TITLE_NEW_RECORD;
                contentKey = SFA_OPEN_SPU_FAILURE_CONTENT_NEW_RECORD;
            } else {
                content = "您好，商品关闭失败，请重新操作关闭！";// ignoreI18n
                title = "商品关闭失败";// ignoreI18n
                titleKey = SFA_CLOSE_SPU_FAILURE_TITLE_NEW_RECORD;
                contentKey = SFA_CLOSE_SPU_FAILURE_CONTENT_NEW_RECORD;
            }
            if (!Strings.isNullOrEmpty(message)) {
                content = message;
                contentKey = message;
            }
        }
        Set<Integer> receiverIds = Sets.newHashSet();
        if (NumberUtils.isNumber(user.getUserId())) {
            receiverIds.add(Integer.parseInt(user.getUserId()));
        }
        if (CollectionUtils.empty(receiverIds)) {
            return;
        }
        CRMNotification crmNotification = CRMNotification.builder()
                .sender(user.getUserId())
                .remindRecordType(92)
                .content(content)
                .title(title)
                .dataId("")
                .content2Id("0")
                .receiverIds(receiverIds)
                .build();
        crmNotificationService.sendCRMNotification(user, crmNotification);

        CRMRemindRecordUtil.sendNewCRMRecord(crmNotificationService, user, 92, Lists.newArrayList(receiverIds), "", title, content, titleKey, Lists.newArrayList(),
                contentKey, Lists.newArrayList(), null, null);
    }

    private void processDescribe(IObjectDescribe objectDescribe, boolean isOpen,
                                 List<IFieldDescribe> fieldsAdd2SpuLayout,
                                 List<IFieldDescribe> fieldsAdd2SkuLayout) throws MetadataServiceException {
        updateDescribe(objectDescribe, isOpen, fieldsAdd2SpuLayout, fieldsAdd2SkuLayout);
    }

    private void updateDescribe(IObjectDescribe objectDescribe, boolean isOpen,
                                List<IFieldDescribe> fieldsAdd2SpuLayout,
                                List<IFieldDescribe> fieldsAdd2SkuLayout) throws MetadataServiceException {
        IObjectDescribe spuDescribe = getDescribeWithSimplifiedChinese(User.systemUser(objectDescribe.getTenantId()), SFAPreDefineObject.SPU.getApiName());
        if (spuDescribe == null) {
            log.error("SPUObj describe not found");
            throw new ValidateException("describe not found");
        }
        if (productCategoryUtils.isCloseOldProductCategory(objectDescribe.getTenantId())) {
            IFieldDescribe categoryFieldDescribe = spuDescribe.getFieldDescribe(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID);
            if (categoryFieldDescribe != null) {
                fieldsAdd2SpuLayout.add(categoryFieldDescribe);
            }
            IFieldDescribe shopCategoryFieldDescribe = spuDescribe.getFieldDescribe(ProductCategoryModel.Filed.SHOP_CATEGORY_ID);
            if (shopCategoryFieldDescribe != null) {
                fieldsAdd2SpuLayout.add(shopCategoryFieldDescribe);
            }
        }
        //重置负责人必填属性
        IFieldDescribe ownerField = objectDescribe.getFieldDescribe(ObjectDataExt.OWNER);
        if (isOpen) {
            ownerField.setRequired(Boolean.FALSE);
            IFieldDescribe spuIdField = new ObjectReferenceFieldDescribe();
            spuIdField.fromJsonString(enterpriseInitService.getProductJsonFromFieldName("spuid"));
            spuIdField.setDescribeApiName(SFAPreDefineObject.Product.getApiName());
            objectDescribe.addFieldDescribe(spuIdField);
            //如果深研开启了标签，则在商品中添加该字段
            IFieldDescribe labelField = objectDescribe.getFieldDescribe("commodity_label");
            if (labelField != null && Boolean.TRUE.equals(labelField.isActive())) {
                spuDescribe.addFieldDescribe(getLabelFieldDescribe(labelField,
                        SFAPreDefineObject.SPU.getApiName(), I18N.text("SPUObj.field.commodity_label.label")));
                fieldsAdd2SpuLayout.add(labelField);
            }
            //如果深研开启了批次管理，则在商品中添加该字段
            IFieldDescribe batchField = objectDescribe.getFieldDescribe("batch_sn");
            if (batchField != null && Boolean.TRUE.equals(batchField.isActive())) {
                IFieldDescribe spuBatchField = spuDescribe.getFieldDescribe("batch_sn");
                if (spuBatchField != null) {
                    spuBatchField.setActive(Boolean.TRUE);
                    spuBatchField.setRequired(batchField.isRequired());
                    spuBatchField.set("options", batchField.get("options"));
                    spuBatchField.setConfig(batchField.getConfig());
                    spuBatchField.setLabel(batchField.getLabel());
                    spuBatchField.setDescription(batchField.getDescription());
                } else {
                    batchField.setId(null);
                    spuDescribe.addFieldDescribe(batchField);
                }
                fieldsAdd2SpuLayout.add(batchField);
            }
            //如果开启了多单位，则在商品中添加该字段
            IFieldDescribe multiUnitField = objectDescribe.getFieldDescribe("is_multiple_unit");
            if (multiUnitField != null && Boolean.TRUE.equals(multiUnitField.isActive())) {
                multiUnitField.setId(null);
                spuDescribe.addFieldDescribe(multiUnitField);
                fieldsAdd2SpuLayout.add(multiUnitField);
            }
            //反向同步产品字段选项至商品
            List<String> syncFieldNames = Lists.newArrayList(ObjectDescribe.RECORD_TYPE, "category", "unit",
                    "product_line");
            for (String fieldName : syncFieldNames) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
                if (fieldDescribe != null && Boolean.TRUE.equals(fieldDescribe.isActive())) {
                    IFieldDescribe spuFieldDescribe = spuDescribe.getFieldDescribe(fieldName);
                    if (spuFieldDescribe != null) {
                        List<Map> options = fieldDescribe.get("options", List.class);
                        if (CollectionUtils.notEmpty(options)) {
                            options.forEach(option -> option.put("child_options", null));
                        }
                        spuFieldDescribe.set("options", options);
                    }
                }
            }
        } else {
            ownerField.setRequired(Boolean.TRUE);
            //如果深研开启了标签，则在产品中添加该字段
            IFieldDescribe labelField = spuDescribe.getFieldDescribe("commodity_label");
            if (labelField != null && Boolean.TRUE.equals(labelField.isActive())) {
                objectDescribe.addFieldDescribe(getLabelFieldDescribe(labelField,
                        SFAPreDefineObject.Product.getApiName(), I18N.text("ProductObj.field.commodity_label.label")));
                fieldsAdd2SkuLayout.add(labelField);
            }
        }
        boolean isMultiUnitOpen = false;
        IFieldDescribe multiUnitField = objectDescribe.getFieldDescribe("is_multiple_unit");
        if (multiUnitField != null && Boolean.TRUE.equals(multiUnitField.isActive())) {
            isMultiUnitOpen = true;
        }

        objectDescribeService.update(objectDescribe);
        if (isOpen) {
            objectDescribeService.update(spuDescribe);
        }
        if (isMultiUnitOpen) {
            IObjectDescribe multiUnitDescribe = getDescribeWithSimplifiedChinese(User.systemUser(objectDescribe.getTenantId()), SFAPreDefineObject.MultiUnitRelated.getApiName());
            if (multiUnitDescribe != null) {
                IFieldDescribe fieldDescribe = multiUnitDescribe.getFieldDescribe("spu_id");
                if (fieldDescribe != null) {
                    fieldDescribe.setRequired(isOpen);
                    objectDescribeService.update(multiUnitDescribe);
                }
            }
        }
    }

    private IFieldDescribe getLabelFieldDescribe(IFieldDescribe originalFieldDescribe, String objectApiName, String label) {
        IFieldDescribe fieldDescribe = new SelectManyFieldDescribe();
        fieldDescribe.setDescribeApiName(objectApiName);
        fieldDescribe.setApiName(originalFieldDescribe.getApiName());
        fieldDescribe.setIndex(originalFieldDescribe.isIndex());
        fieldDescribe.setActive(originalFieldDescribe.isActive());
        fieldDescribe.setRequired(originalFieldDescribe.isRequired());
        fieldDescribe.set("options", originalFieldDescribe.get("options"));
        fieldDescribe.setConfig(originalFieldDescribe.getConfig());
        fieldDescribe.setDefineType(originalFieldDescribe.getDefineType());
        fieldDescribe.setLabel(label);
        fieldDescribe.setDescription(label);
        return fieldDescribe;
    }

    private void deleteFields(IObjectDescribe objectDescribe, boolean isOpen) throws MetadataServiceException {
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        List<String> toDeleteFields = Lists.newArrayList();
        if (isOpen) {
            toDeleteFields.add("commodity_label");
        } else {
            toDeleteFields.add("spu_id");
        }
        for (String field : toDeleteFields) {
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(field);
            if (fieldDescribe != null) {
                fieldDescribeList.add(fieldDescribe);
            }
        }
        if (CollectionUtils.notEmpty(fieldDescribeList)) {
            objectDescribeService.deleteCustomFieldDescribe(objectDescribe, fieldDescribeList);
        }
    }

    private void updateProduct(IObjectDescribe objectDescribe) {
        boolean hasLabelField = false;
        IFieldDescribe labelField = objectDescribe.getFieldDescribe("commodity_label");
        if (labelField != null && Boolean.TRUE.equals(labelField.isActive())) {
            hasLabelField = true;
        }
        if (!hasLabelField) {
            return;
        }
        User user = new User(objectDescribe.getTenantId(), "-10000");
        int offset = 0;
        int loopCnt = 0;
        SearchTemplateQuery query = getTemplateQuery();
        while (loopCnt < 300) {
            query.setOffset(offset);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user,
                    SFAPreDefineObject.Product.getApiName(), query);
            if (queryResult != null && CollectionUtils.notEmpty(queryResult.getData())) {
                updateLabel(user, queryResult.getData(), hasLabelField, false);
            }
            if (queryResult == null || CollectionUtils.empty(queryResult.getData()) || queryResult.getData().size() < 1000) {
                break;
            }
            offset += 1000;
            loopCnt++;
        }
    }

    private void updateLabel(User user, List<IObjectData> objectDataList, boolean hasLabelField, boolean hasBatchField) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        Set<String> spuIds = objectDataList.stream()
                .filter(m -> !Strings.isNullOrEmpty(m.get("spu_id", String.class)))
                .map(it -> it.get("spu_id", String.class))
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(spuIds)) {
            return;
        }
        List<IObjectData> spuDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(spuIds),
                SFAPreDefineObject.SPU.getApiName());
        List<IObjectData> toUpdateList = Lists.newArrayList();
        for (IObjectData objectData : objectDataList) {
            Optional<IObjectData> dataOptional = spuDataList.stream()
                    .filter(item -> item.getId().equals(objectData.get("spu_id", String.class)))
                    .findFirst();
            if (dataOptional.isPresent()) {
                if (hasLabelField) {
                    objectData.set("commodity_label", dataOptional.get().get("commodity_label"));
                }
                if (hasBatchField) {
                    objectData.set("batch_sn", dataOptional.get().get("batch_sn"));
                }
                toUpdateList.add(objectData);
            }
        }
        if (CollectionUtils.notEmpty(toUpdateList)) {
            List<String> updateField = Lists.newArrayList();
            if (hasLabelField) {
                updateField.add("commodity_label");
            }
            if (hasBatchField) {
                updateField.add("batch_sn");
            }
            serviceFacade.batchUpdateByFields(user, toUpdateList, updateField);
        }
    }

    private SearchTemplateQuery getTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(Lists.newArrayList());
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(Boolean.FALSE);
        searchTemplateQuery.setNeedReturnQuote(Boolean.FALSE);
        return searchTemplateQuery;
    }

    private void dealSaleOrder(User user, boolean isOpen) {
        IObjectDescribe objectDescribe = getDescribeWithSimplifiedChinese(user, Utils.SALES_ORDER_PRODUCT_API_NAME);
        if (Objects.isNull(objectDescribe)) {
            return;
        }
        if (isOpen) {
            Set<String> module = licenseService.getModule(user.getTenantId());
            if (!module.contains("kx_peculiarity")) {
                return;
            }
            IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(spuNameField);
            IFieldDescribe spuName = Optional.of(objectDescribe).map(x -> x.getFieldDescribe("spu_id")).orElse(null);
            if (Objects.nonNull(spuName)) {
                return;
            }
            objectDescribe.addFieldDescribe(fieldDescribe);
            try {
                objectDescribeService.update(objectDescribe);
                IFormField formField = new FormField();
                formField.setFieldName(fieldDescribe.getApiName());
                formField.setRequired(false);
                formField.setReadOnly(true);
                formField.setRenderType(fieldDescribe.getType());
                addFieldsToDetailLayoutFormComponent(user, Lists.newArrayList(formField), Utils.SALES_ORDER_PRODUCT_API_NAME);
            } catch (MetadataServiceException e) {
                log.error(e.getMessage(), e);
            }

        } else {
            IFieldDescribe spu_name = objectDescribe.getFieldDescribe("spu_id");
            if (Objects.isNull(spu_name)) {
                return;
            }
            objectDescribe.removeFieldDescribe("spu_id");
            try {
                objectDescribeService.update(objectDescribe);
            } catch (MetadataServiceException e) {
                log.error(e.getMessage(), e);
            }
        }

    }

    public void addFieldsToDetailLayoutFormComponent(User user, List<IFormField> iFormFields, String describeApiName) throws MetadataServiceException {
        List<ILayout> layouts;
        try {
            List<Layout> layoutList = layoutService.findByTypes(user.getTenantId(), Lists.newArrayList(ILayout.DETAIL_LAYOUT_TYPE), describeApiName);
            layouts = layoutList.stream().collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
        if (CollectionUtils.empty(layouts)) {
            return;
        }
        for (ILayout layout : layouts) {
            layout.getComponents().forEach(iComponent -> {
                if (Objects.equals(iComponent.getName(), LayoutConstants.FORM_COMPONENT_API_NAME)) {
                    FormComponent fc = (FormComponent) iComponent;
                    Optional<IFieldSection> fieldSection = fc.getFieldSections().stream()
                            .filter(it -> it.getName().equals("base_field_section__c"))
                            .findFirst();
                    if (fieldSection.isPresent()) {
                        List<IFormField> fieldsToRemove = Lists.newArrayList();
                        for (IFormField iFormField : iFormFields) {
                            if (fieldSection.get().getFields().contains(iFormField)) {
                                fieldsToRemove.add(iFormField);
                            }
                        }
                        iFormFields.removeAll(fieldsToRemove);
                        fieldSection.get().addFields(iFormFields);
                    }
                }
            });
            layoutService.replace(layout);
        }

    }

    private static final String spuNameField = " {          \"default_is_expression\": true,          \"is_unique\": false,          \"where_type\": \"field\",          \"type\": \"object_reference\",          \"is_required\": false,          \"wheres\": [],          \"define_type\": \"package\",          \"input_mode\": \"\",          \"is_single\": false,          \"is_index\": true,          \"is_active\": true,          \"is_encrypted\": false,          \"default_value\": \"$product_id__r.spu_id$\",          \"label\": \"商品名称\",          \"target_api_name\": \"SPUObj\",          \"target_related_list_name\": \"target_related_list_t1ap5\",          \"field_num\": null,          \"target_related_list_label\": \"订单产品\",          \"action_on_target_delete\": \"set_null\",          \"api_name\": \"spu_id\",          \"is_index_field\": true,          \"help_text\": \"\",          \"status\": \"released\"        }";// ignoreI18n
}
