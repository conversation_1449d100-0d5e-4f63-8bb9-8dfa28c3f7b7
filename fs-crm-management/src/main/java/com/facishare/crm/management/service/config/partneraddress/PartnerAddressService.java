package com.facishare.crm.management.service.config.partneraddress;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.sfa.utilities.constant.PartnerConstants.*;
import static com.facishare.paas.appframework.privilege.model.role.Role.CHANNEL_MANAGER;

@Slf4j
@Service
public class PartnerAddressService {
    @Autowired
    private InitObjectService initObjectService;
    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private FunctionPrivilegeServiceExt functionPrivilegeServiceExt;
    @Resource(name = "crmMenuInitServiceSFA")
    CrmMenuInitService crmMenuInitService;

    public void initPartnerAddressObject(User user) throws MetadataServiceException {
        initObjectService.initObject(user, SFAPreDefineObject.PartnerAddr.getApiName());
        // 渠道经理增加合作伙伴地址所有权限
        functionPrivilegeServiceExt.updateRoleFuncAccessOfAllFunc(user, SFAPreDefineObject.PartnerAddr.getApiName(), Lists.newArrayList(CHANNEL_MANAGER.getRoleCode()));
        crmMenuInitService.createMenuItem(user, Lists.newArrayList(SFAPreDefineObject.PartnerAddr.getApiName()), "");
    }

    public void initPartnerAddressData(User user) {
        RequestContext context = RequestContextManager.getContext();
        ActionContext actionContext = new ActionContext(context, SFAPreDefine.PartnerAddr.getApiName(), SystemConstants.ActionCode.Add.getActionCode());
        // 查出所有合作伙伴数据
        metaDataFindServiceExt.dealDataByTemplate(user, SFAPreDefine.Partner.getApiName(), new SearchTemplateQuery(), dataList -> {
            // 合作伙伴数据
            List<IObjectData> successPartnerDataList = Lists.newArrayList();
            for (IObjectData partnerData : dataList) {
                boolean success = createPartnerAddDataByPartner(actionContext, partnerData);
                if (!success) {
                    continue;
                }
                successPartnerDataList.add(partnerData);
            }
            clearPartnerAreaLocation(user, successPartnerDataList);
        });
    }

    private void clearPartnerAreaLocation(User user, List<IObjectData> successPartnerDataList) {
        if (CollectionUtils.empty(successPartnerDataList)) {
            return;
        }
        for (IObjectData partnerData : successPartnerDataList) {
            partnerData.set(ADDRESS, null);
            partnerData.set(COUNTRY, null);
            partnerData.set(PROVINCE, null);
            partnerData.set(CITY, null);
            partnerData.set(DISTRICT, null);
            partnerData.set(LOCATION, null);
        }
        try {
            metaDataFindServiceExt.bulkUpdateByFields(user, successPartnerDataList, Lists.newArrayList("address", "country", "province", "city", "district", "location"));
        } catch (Exception e) {
            log.warn("clearPartnerAreaLocation#bulkUpdateByFields failed, tenant:{}", user.getTenantId(), e);
        }
    }

    private boolean createPartnerAddDataByPartner(ActionContext actionContext, IObjectData partnerData) {
        // 把合作伙伴数据上的地址信息解析出来
        String partnerAddress = ObjectDataUtils.getValueOrDefault(partnerData, ADDRESS, "");
        String partnerCountry = ObjectDataUtils.getValueOrDefault(partnerData, COUNTRY, "");
        String partnerProvince = ObjectDataUtils.getValueOrDefault(partnerData, PROVINCE, "");
        String partnerCity = ObjectDataUtils.getValueOrDefault(partnerData, CITY, "");
        String partnerDistrict = ObjectDataUtils.getValueOrDefault(partnerData, DISTRICT, "");
        String partnerLocation = ObjectDataUtils.getValueOrDefault(partnerData, LOCATION, "");
        if (StringUtils.isAllBlank(partnerAddress, partnerCountry, partnerProvince, partnerCity, partnerDistrict, partnerLocation)) {
            return false;
        }
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(ObjectDataExt.OWNER, Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId()));
        dataMap.put(ObjectDataExt.DESCRIBE_API_NAME, SFAPreDefineObject.PartnerAddr.getApiName());
        dataMap.put("add_type", "1");
        dataMap.put("partner", partnerData.getId());
        dataMap.put(ADDRESS, partnerAddress);
        dataMap.put(COUNTRY, partnerCountry);
        dataMap.put(PROVINCE, partnerProvince);
        dataMap.put(CITY, partnerCity);
        dataMap.put(DISTRICT, partnerDistrict);
        dataMap.put(LOCATION, partnerLocation);
        ObjectDataDocument dataDocument = ObjectDataDocument.of(dataMap);
        BaseObjectSaveAction.Arg arg = buildAddActionArg(dataDocument);
        try {
            // 根据解析的地址信息创建合作伙伴地址数据
            serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
        } catch (Exception e) {
            log.warn("根据合作伙伴创建合作伙伴地址失败，tenant:{}, partner_id:{}", actionContext.getTenantId(), partnerData.getId(), e);
            return false;
        }
        return true;
    }

    private BaseObjectSaveAction.Arg buildAddActionArg(ObjectDataDocument dataDocument) {
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(dataDocument);
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setIsDuplicateSearch(true);
        optionInfo.setUseValidationRule(true);
        optionInfo.setSkipFuncValidate(true);
        arg.setOptionInfo(optionInfo);
        return arg;
    }
}
