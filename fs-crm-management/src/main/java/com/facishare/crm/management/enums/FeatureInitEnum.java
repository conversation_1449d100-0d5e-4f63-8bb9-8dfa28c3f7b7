package com.facishare.crm.management.enums;

import java.util.Optional;

/**
 * 应收开关状态
 */
public enum FeatureInitEnum {
    NOT_OPEN(0, "未开启"),
    OPENING(1, "开启中"),
    OPENED(2, "开启成功"),
    OPEN_FAIL(3, "开启失败");

    private int status;
    private String message;

    FeatureInitEnum(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public static Optional<FeatureInitEnum> get(int status) {
        for (FeatureInitEnum switchEnum : FeatureInitEnum.values()) {
            if (switchEnum.status == status) {
                return Optional.of(switchEnum);
            }
        }
        return Optional.empty();
    }

    public int getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }
}
