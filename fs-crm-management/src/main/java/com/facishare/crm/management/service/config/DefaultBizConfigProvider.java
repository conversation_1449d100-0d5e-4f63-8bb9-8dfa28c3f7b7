package com.facishare.crm.management.service.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.management.enums.FeatureInitEnum;
import com.facishare.crm.management.service.config.model.GetSetConfigValue;
import com.facishare.crm.rest.SettingLogProxy;
import com.facishare.crm.rest.dto.SettingLogModel;
import com.facishare.crm.sfa.cache.RedisDataAccessor;
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.CustomerAccountConfig;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.SalesOrderI18NKeyUtil;
import com.facishare.crm.util.RestUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.RocketMQMessageSender;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;
import static com.facishare.crm.sfa.utilities.util.i18n.SalesOrderI18NKeyUtil.SFA_CONFIG_CANT_DO_MUST_OPEN_PROMOTION;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DefaultBizConfigProvider implements BizConfigProvider {

    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected InfraServiceFacade infraServiceFacade;
    @Autowired
    protected IObjectDescribeService objectDescribeService;
    @Autowired
    protected ConfigService configService;
    @Autowired
    protected DescUtil descUtil;

    @Autowired
    private SettingLogProxy logServiceProxy;
    @Autowired
    private OrgService orgService;
    @Autowired
    private RedisDataAccessor redisDataAccessor;
    @Resource(name = "initModuleCtrlMQSender")
    private RocketMQMessageSender sender;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private DescribeWithSimplifiedChineseService describeWithSimplifiedChineseService;

    @Override
    public String getConfigKey() {
        return null;
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        String queryRst = configService.findTenantConfig(user, key);
        if (queryRst == null) {
            configService.createTenantConfig(user, key, value, ConfigValueType.STRING);
        } else {
            configService.updateTenantConfig(user, key, value, ConfigValueType.STRING);
        }
        sendSettingRecord(user,key,value,oldValue);
    }

    @Override
    public void setConfigValues(User user, Map<String, String> values){
        values.forEach((key,value)->{
            validateSetConfig(user,key,value);
            setConfigValue(user,value,"", key);
        });
    }

    @Override
    public String getConfigValue(User user, String key){
        ConfigType keyType = ConfigType.getConfigType(key);
        if(keyType.equals(ConfigType.NONE)){
            log.warn("DefaultBizConfigProvider error key:{}",key);
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_KEY_ERROR));
        }
        String queryRst = configService.findTenantConfig(user,key);
        if(Strings.isNullOrEmpty(queryRst)){
            return getDefaultConfigValue(key);
        }else{
            return keyType.getValue(queryRst);
        }
    }

    @Override
    public String getDefaultConfigValue(String key) {
        if (ConfigType.getConfigType(key).equals(ConfigType.NONE)) {
            return "";
        } else {
            return ConfigType.getConfigType(key).getDefaultValue();
        }
    }

    @Override
    public Map<String, String> getConfigValues(User user, List<String> keys) {
        return getConfigValuesByKeys(user, keys);
    }
    @Override
    public Map<String, String> getAllConfigValues(User user) {
        List<String> allKeys = ConfigType.getAllKeys();
        return getConfigValuesByKeys(user, allKeys);
    }

    private Map<String, String> getConfigValuesByKeys(User user, List<String> keys) {
        if(CollectionUtils.size(keys) > 0) {
            Map<String, String> queryRst = configService.queryTenantConfigs(user, keys);
            keys.forEach(x -> {
                if (!queryRst.containsKey(x) || Strings.isNullOrEmpty(queryRst.get(x))) {
                    queryRst.put(x, ConfigType.getConfigType(x).getDefaultValue());
                }
                else{
                    queryRst.put(x, ConfigType.getConfigType(x).getValue(queryRst.get(x)));
                }
            });
            return queryRst;
        }
        return Maps.newHashMap();
    }

    @Override
    public void validateSetConfig(User user, String key, String new_value){
        String value = getConfigValue(user,key);
        if(key.equals(ConfigType.IS_CUSTOMER_ACCOUNT_ENABLED.getKey()) && value.equals("1")){
            //不允许禁用客户账户!
            throw new ValidateException(I18N.text(SFA_CONFIG_NOTALLOWFORBIDDENCUSTOMERACCOUNT));
        }
        if(key.equals(ConfigType.IsDeviceManagementEnabled.getKey()) && value.equals("1")){
            //不允许禁用设备管理!
            throw new ValidateException(I18N.text(SFA_CONFIG_NOT_ALL_CLOSE_DEVICE_MANAGEMENT));
        }
        if(key.equals(ConfigType.IsDeliveryNoteEnabled.getKey()) && value.equals("1")){
            //不允许禁用发货单!
            throw new ValidateException(I18N.text(SFA_CONFIG_NOTALLOWFORBIDDENDELIVERYNOTE));
        }
        if(key.equals(ConfigType.IsOpenPartner.getKey()) && value.equals("1")){
            //不允许禁用合作伙伴!
            throw new ValidateException(I18N.text(SFA_CONFIG_CANT_CLOSE_PARTNER));
        }
        if(key.equals(ConfigType.IsOpenNewOpportunity.getKey()) && value.equals("1")){
            //不允许禁用商机2.0!
            throw new ValidateException(I18N.text(SFA_CONFIG_CANT_CLOSE_NEW_OPP));
        }
        if(key.equals(ConfigType.IsPromotionEnabled.getKey()) && value.equals("1")){
            throw new ValidateException(I18N.text(SFA_CONFIG_NOTALLOWFORBIDDENPROMOTION));
        }
        if(key.equals(ConfigType.IS_OPEN_ATTRIBUTE.getKey())){
            Set<String> module = licenseService.getModule(user.getTenantId());
            if (CollectionUtils.empty(module) || !module.contains("cpq_attribute_app")) {
                throw new ValidateException(I18N.text("sfa.config.license.not.open.attribute"));
            }
            String isPromotionEnabled = getConfigValue(user, ConfigType.IsPromotionEnabled.getKey());
            if(isPromotionEnabled.equals("1")){
                throw new ValidateException(I18N.text(SFA_CONFIG_NOTOPENATTRIBUTEFORPROMOTION));
            }
            String IsPriceBookEnabled = getConfigValue(user, ConfigType.IS_PRICE_BOOK_ENABLED.getKey());
            if(!IsPriceBookEnabled.equals("1")){
                throw new ValidateException(I18N.text(SFA_CONFIG_NOTOPENATTRIBUTEFORPRICEBOOK));
            }
            String IsMultiEnabled = getConfigValue(user, ConfigType.MULTIPLE_UNIT.getKey());
            if(IsMultiEnabled.equals("1")){
                throw new ValidateException(I18N.text(SFA_CONFIG_NOTOPENATTRIBUTEFORMULTIUNIT));
            }
            String IsSPUEnabled = getConfigValue(user, ConfigType.SPU.getKey());
            if(IsSPUEnabled.equals("1")){
                throw new ValidateException(I18N.text(SFA_CONFIG_NOTOPENATTRIBUTEFORSPU));
            }
            String IsTradeProductRepeatable = getConfigValue(user, ConfigType.IsTradeProductRepeatable.getKey());
            if(!IsTradeProductRepeatable.equals("1")){
                throw new ValidateException(I18N.text(SFA_CONFIG_NOTOPENATTRIBUTEFORPRODUCTCOPY));
            }
        }
        if(key.equals(ConfigType.IS_OPEN_NONSTANDARD_ATTRIBUTE.getKey())){
            Set<String> module = licenseService.getModule(user.getTenantId());
            if (CollectionUtils.empty(module) || !module.contains("cpq_attribute_app")) {
                throw new ValidateException(I18N.text("sfa.config.license.not.open.attribute"));
            }
        }
        if(key.equals(ConfigType.IS_PRICE_BOOK_ENABLED.getKey())){
            if(value.equals("1")){
                //不允许禁用价目表!
                throw new ValidateException(I18N.text(SFA_CONFIG_NOTALLOWFORBIDDENPRICEBOOK));
            }
            String industryValue = getConfigValue(user, ConfigType.IsIndustryPriceBookEnabled.getKey());
            if(industryValue.equals("1")){
                //已启用快消行业包的行业价目表，无法启用标准价目表!
                throw new ValidateException(I18N.text(SFA_CONFIG_NOTALLOWENABLEPRICEBOOK));
            }
        }
        if(key.equals(ConfigType.IsBatchNumberOrSerialNumberEnabled.getKey())){
            if(value.equals("1")){
                //不允许禁用批次与序列号管理
                throw new ValidateException(I18N.text(SFA_CONFIG_CANT_DISABLE_SN_MANAGEMENT));
            }
            String stockValue =  getConfigValue(user,ConfigType.IsInventoryEnabled.getKey());
            if(!stockValue.equals("1")){
                //开启库存后才可以开启批次与序列号管理!
                throw new ValidateException(I18N.text(SFA_CONFIG_MUST_OPEN_STOCK_BEFORE_OPEN_SN));
            }
        }
        if(key.equals(ConfigType.IsIndustryPriceBookEnabled.getKey())){
            if(value.equals("1")){
                //不允许禁用行业价目表!
                throw new ValidateException(I18N.text(SFA_CONFIG_CANT_DISABLE_BU_PRICE_BOOK));
            }
            String priceBookValue = getConfigValue(user, ConfigType.IS_PRICE_BOOK_ENABLED.getKey());
            if(priceBookValue.equals("1")){
                //已启用标准价目表，无法启用快消行业包的行业价目表!
                throw new ValidateException(I18N.text(SFA_CONFIG_NOTALLOWENABLEPRICEBOOK));
            }
        }
        if(key.equals(ConfigType.IsTradeProductRepeatable.getKey())){
            if(new_value.equals("0")){
                String openAttribute = getConfigValue(user, ConfigType.IS_OPEN_ATTRIBUTE.getKey());
                if(Objects.equals(openAttribute,"1")){
                    throw new ValidateException(I18N.text(SalesOrderI18NKeyUtil.SFA_CONFIG_OPEN_ATTRIBUTE_NOT_ALLOW_CLOSING));
                }
            }
        }

        if(key.equals(ConfigType.CanEditOrderWhenPromotionEnabled.getKey())){
            String isPromotionEnabled = getConfigValue(user, ConfigType.IsPromotionEnabled.getKey());
            if(new_value.equals("1") && isPromotionEnabled.equals("0")){
                //促销应用未开启，请开启促销后再操作!
                throw new ValidateException(I18N.text(SFA_CONFIG_CANT_DO_MUST_OPEN_PROMOTION));
            }
        }
/*
        if (ConfigType.GET_PRICE_WHEN_COPY.getKey().equals(key)) {

            if ("0".equals(new_value) && isOpenMarketing(user)) {
                //todo jimzh 修改多语提示，目前提示已开启高级定价，不允许修改
                throw new ValidateException(I18N.text("sfa.config.not_allowed_to_close"));
            }
        }*/

        if (ConfigType.CUSTOMER_ORDER_RULE.getKey().equals(key)) {
            //todo jimzh 修改多语提示，目前提示已开启高级定价，不允许修改
            if (!"1,0,0".equals(new_value) && isOpenMarketing(user)) {
                throw new ValidateException(I18N.text("sfa.config.not_allowed_to_modify"));
            }
        }
        if (ConfigType.MULTI_UNIT_MODE_CONFIG.getKey().equals(key)||
                ConfigType.MULTI_UNIT_SHOW_TYPE.getKey().equals(key)||
                ConfigType.MULTI_UNIT_LINKAGE.getKey().equals(key)){
            String isMultiEnabled = getConfigValue(user, ConfigType.MULTIPLE_UNIT.getKey());
            if(!"1".equals(isMultiEnabled)){
                throw new ValidateException("sfa.config.no.open.multiunit");
            }
        }
        if (ConfigType.IS_OPEN_RECEIVED_PAYMENT.getKey().equals(key)) {
            if (CustomerAccountConfig.oldPaymentList.contains(user.getTenantId())) {
                throw new ValidateException(I18N.text("sfa.config.not_allowed_to_open_received_payment_for_payment"));
            }
        }
        if (ConfigType.IS_OPEN_ORDER_PAYMENT_MULTI_SOURCE.getKey().equals(key)) {
            if (CustomerAccountConfig.oldPaymentList.contains(user.getTenantId())) {
                throw new ValidateException(I18N.text("sfa.config.not_allowed_to_payment_multi_source"));
            }
            if (bizConfigThreadLocalCacheService.isOpenAccountsReceivable(user.getTenantId())) {
                throw new ValidateException(I18N.text("sfa.config.open_ar_not_allowed_to_payment_multi_source"));
            }
        }
        if (ConfigType.IS_OPEN_AUTO_MATCH.getKey().equals(key)) {
            if (bizConfigThreadLocalCacheService.isOpenKxAutoMatch(user.getTenantId())) {
                throw new ValidateException(I18N.text("sfa.config.open_auto_match_not_kx"));
            }
        }
        if(key.equals(ConfigType.CUSTOMER_PROFILE_AGENT.getKey())){
            Set<String> module = licenseService.getModule(user.getTenantId());
            if (CollectionUtils.empty(module)
                    || !module.contains("sales_portrait_insight_agent_app")
                    || !module.contains("knowledgemanagement_app")
                    || !module.contains("cpq_ai_interactive_assistant")
                    || !module.contains("ai_product_app")
                    || !module.contains("ai_product_limit")) {
                throw new ValidateException(I18N.text("sfa.config.license.not.open.profile.agent"));
            }
        }
    }

    /**
     * 是否开启营销政策
     *
     * @return boolean
     */
    public boolean isOpenMarketing(User user) {
        String pricePolicyConfigValue = getConfigValue(user, ConfigType.PRICE_POLICY_SALES_ORDER_OBJ.getKey());
        String couponConfigValue = getConfigValue(user, ConfigType.COUPON.getKey());
        String rebateConfigValue = getConfigValue(user, ConfigType.REBATE.getKey());
        String dynamicAllowAmortizeValue = getConfigValue(user, ConfigType.DYNAMIC_ALLOW_AMORTIZE.getKey());
            return "1".equals(pricePolicyConfigValue) || "1".equals(couponConfigValue) || "1".equals(rebateConfigValue) || "1".equals(dynamicAllowAmortizeValue);
    }

    private void sendSettingRecord(User user, String key,String value,String oldValue){
        String message = "";
        if(key.equals(ConfigType.IS_ALLOW_NO_RIGHT_SEE_COMPLETE_CUSTOMER_NAME.getKey())){
            if (value.equals("1")) {
                message = "基础设置：允许无查看数据权限的员工在工作流中看到完整线索、客户、商机、拜访、产品名称开关 ";//ignoreI18n
            }else{
                message = "基础设置：禁止无查看数据权限的员工在工作流中看到完整线索、客户、商机、拜访、产品名称开关 ";//ignoreI18n
            }
        }
        if(key.equals(ConfigType.IS_ALLOWED_TO_EDIT_CUSTOMER_NAME.getKey())){
            if (value.equals("1")) {
                message = "客户规则设置，允许修改客户名称";//ignoreI18n
            }else{
                message = "客户规则设置，不允许修改客户名称";//ignoreI18n
            }
        }
        if(key.equals(ConfigType.IsAllowedToEditPartnerName.getKey())){
            message = "基础设置：合作伙伴名称修改开关 ";//ignoreI18n
            if (value.equals("1")) {
                message += "开";//ignoreI18n
            }else{
                message += "关";//ignoreI18n
            }
        }
        if(key.equals(ConfigType.LEADER_VIEW_SCOPE.getKey())){
            message = "基础设置：上级可见数据范围 ";//ignoreI18n
            if (value.equals("1")) {
                message += "所有下级数据";//ignoreI18n
            }else{
                message += "直属下级数据";//ignoreI18n
            }
        }

        if(key.equals(ConfigType.IS_OPEN_FILING.getKey())){
            message = "客户报备： ";//ignoreI18n
            if (value.equals("1")) {
                message += "开";//ignoreI18n
            }else{
                message += "关";//ignoreI18n
            }
        }

        if(key.equals(ConfigType.SERVICE_EMPLOYEE_FIND_CATEGORY.getKey())){
            message = "服务人员查询类型： ";//ignoreI18n
            if (value.equals("1")) {
                message += "模糊查询";//ignoreI18n
            }else{
                message += "精确查询";//ignoreI18n
            }
        }

        if(key.equals(ConfigType.CUSTOMER_ORDER_RULE.getKey())){
            message = "销售订单／退货单规则： ";//ignoreI18n
            List<String> temp = Lists.newArrayList(value.split(","));
            Integer index = temp.indexOf("1");
            if(index == 0){
                message += "销售订单金额／退货单金额 ＝ 产品合计 * 整单折扣，产品必填";//ignoreI18n
            }
            else if(index == 1){
                message += "销售订单金额／退货单金额、产品合计、整单折扣相互独立，产品选填";//ignoreI18n
            }
            else{
                message += "销售订单金额／退货单金额、产品合计、整单折扣相互独立，产品必填";//ignoreI18n
            }
        }

        if(key.equals(ConfigType.OPPORTUNITY_DUPLICATE_SEARCH.getKey())){
            message = "查重设置：禁止在同一客户下创建重复商机 ";//ignoreI18n
            if (value.equals("1")) {
                message += "开";//ignoreI18n
            }else{
                message += "关";//ignoreI18n
            }
        }

        if(key.equals(ConfigType.CUSTOMER_FOLLOW_DEAL_SETTING.getKey())){
            message = "基础设置：编辑跟进成交行为 ";//ignoreI18n
        }


        if(key.equals(ConfigType.IsEnableTransExistCustomer.getKey())){
            message = "线索转换关联已有客户设置:  ";//ignoreI18n
            if (value.equals("1")) {
                message += "允许";//ignoreI18n
            }else{
                message += "不允许";//ignoreI18n
            }
        }

        if(key.equals(ConfigType.LeadsTransferSetting.getKey())){
            message = "销售线索必须转换的对象:  ";//ignoreI18n
            String[] valueArray = value.split(",");
            List<String> rstMsg = Lists.newArrayList();
            if(valueArray.length != 4){
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONFIG_PARAMETER_ERROR));
            }
            if (valueArray[0].equals("1")){
                rstMsg.add("客户");//ignoreI18n
            }
            if (valueArray[1].equals("1")){
                rstMsg.add("联系人");//ignoreI18n
            }
            if (valueArray[2].equals("1")){
                rstMsg.add("商机");//ignoreI18n
            }
            if (valueArray[3].equals("1")){
                rstMsg.add("商机2.0");//ignoreI18n
            }
            message += String.join(",",rstMsg);
        }

        if(key.equals(ConfigType.IsTradeProductRepeatable.getKey())){
            message = "订单选产品设置: 同一业务类型下订单产品是否允许重复  ";//ignoreI18n
            if (value.equals("1")) {
                message += "允许";//ignoreI18n
            }else{
                message += "不允许";//ignoreI18n
            }
        }

        if(key.equals(ConfigType.CanEditOrderWhenPromotionEnabled.getKey())){
            message = "促销订单支持编辑设置: 参加促销后，可编辑订单  ";//ignoreI18n
            if (value.equals("1")) {
                message += "允许";//ignoreI18n
            }else{
                message += "不允许";//ignoreI18n
            }
        }

        if(key.equals(ConfigType.IsBatchNumberOrSerialNumberEnabled.getKey())){
            message = "基础设置：开启批次与序列号管理: ";//ignoreI18n
        }

        if(key.equals(ConfigType.ConfigChangeCloseDateIfWin.getKey())){
            message = "商机赢单时修改结单日期（预计成交日期）为赢单日期: ";//ignoreI18n
            if (value.equals("1")) {
                message += "开";//ignoreI18n
            }else{
                message += "关";//ignoreI18n
            }
        }

        if(key.equals(ConfigType.ACCOUNT_LEADS_TO_ENTERPRISE.getKey())){
            try {
                JSONObject jsonObject = JSONObject.parseObject(value);
                String use_default_rule = jsonObject.getString("use_default_rule");
                String func_name = jsonObject.getString("func_name");
                String func_api_name = jsonObject.getString("func_api_name");
                StringBuilder sbMessage = new StringBuilder();
                if ("1".equals(use_default_rule)) {
                    sbMessage.append("使用默认规则（线索/客户的公司名称是工商注册）， ");//ignoreI18n
                } else {
                    sbMessage.append("使用自定义规则， ");//ignoreI18n
                }
                sbMessage.append(String.format("func_name: %s , ", StringUtils.isBlank(func_name) ? "无" : func_name));//ignoreI18n
                sbMessage.append(String.format("func_api_name: %s , ", StringUtils.isBlank(func_api_name) ? "无" : func_api_name));//ignoreI18n

                message = "线索/客户同步企业库规则: " + sbMessage.toString();//ignoreI18n
            }catch (Exception e) {
                log.error("set account_leads_to_enterprise error", e);
            }
        }

        if(key.equals(ConfigType.LEADS_TRANSFER_RIGHT_SETTING.getKey())){
            message = "线索是否允许转换为无查看权限客户: ";//ignoreI18n
            if (value.equals("1")) {
                message += "允许";//ignoreI18n
            }else{
                message += "不允许";//ignoreI18n
            }
        }

        Map<String,String> header = RestUtils.getDDSHeaders(user.getTenantId(),user.getUserId());
        User userInfo = orgService.getUser(user.getTenantId(),user.getUserId());
        if(userInfo == null){
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_USER_ID_ERROR));
        }

		if(Strings.isNullOrEmpty(message)) {
			String customRecordLogMessage = getCustomRecordLogMessage(user, key, value, oldValue);
			if (StringUtils.isEmpty(customRecordLogMessage)) {
				return;
			} else {
				message = customRecordLogMessage;
			}
        }

        SettingLogModel.SetttingLogInfo settingLog = SettingLogModel.SetttingLogInfo.builder()
                .appId("CRM")
                .bizOperationName("2")
                .corpId(user.getTenantId())
                .module("35")
                .userId(user.getUserId())
                .userName(user.getUserName())
                .textMessage(message)
                .objectName("35")
                .operationTime(System.currentTimeMillis())
                .jsonMessage(SettingLogModel.JsonText.builder().
                        textMsg(SettingLogModel.TexMsg.builder().dataID("").objectType(0).type(0).text(message).build()).build())
                .build();
        SettingLogModel.Arg arg =SettingLogModel.Arg.builder()
                .log(Lists.newArrayList(settingLog))
                .build();
        logServiceProxy.recordLog(header,arg);
    }

	protected String getCustomRecordLogMessage(User user, String key,String value,String oldValue) {
		return StringUtils.EMPTY;
	}

    @Override
    public GetSetConfigValue.Result getSetUserConfig(User user, String key, String value) {
        String redisKey = user.getTenantId().concat(user.getUserIdOrOutUserIdIfOutUser()).concat(key);
        ConfigType keyType = ConfigType.getConfigType(key);
        if (Objects.equals(keyType, ConfigType.NONE)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_KEY_ERROR));
        }
        String queryRst = redisDataAccessor.get(redisKey);
        if (StringUtils.isBlank(queryRst)) {
            queryRst = configService.findUserConfig(user, key);
            if (StringUtils.isNotEmpty(valueHandler(queryRst))) {
                redisDataAccessor.set(redisKey,valueHandler(queryRst),24*60*60);
            }
        }
        if (StringUtils.isBlank(value)||StringUtils.equals(value,queryRst)) {
            String resp = valueHandler(queryRst);
            return GetSetConfigValue.Result.builder().success(Boolean.TRUE).value(resp).build();
        }
        if (StringUtils.isBlank(configService.findUserConfig(user, key))) {
            configService.createUserConfig(user,key,value, ConfigValueType.STRING);
        } else {
            configService.updateUserConfig(user,key,value, ConfigValueType.STRING);
        }
        redisDataAccessor.set(redisKey,value,24*60*60);
        return GetSetConfigValue.Result.builder().success(Boolean.TRUE).value(value).build();
    }
    @Override
    public GetSetConfigValue.Result getUserConfig(User user, String key){
        String redisKey = user.getTenantId().concat(user.getUserIdOrOutUserIdIfOutUser()).concat(key);
        ConfigType keyType = ConfigType.getConfigType(key);
        if (Objects.equals(keyType, ConfigType.NONE)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_KEY_ERROR));
        }
        String queryRst = redisDataAccessor.get(redisKey);
        if (StringUtils.isBlank(queryRst)) {
            queryRst = configService.findUserConfig(user, key);
            redisDataAccessor.set(redisKey,StringUtils.equals("1", queryRst) ? "1" : "0",24*60*60);
        }
        String resp = StringUtils.equals("1", queryRst) ? "1" : "0";
        return GetSetConfigValue.Result.builder().success(Boolean.TRUE).value(resp).build();
    }
    @Override
    public GetSetConfigValue.Result setUserConfig(User user, String key, String value){
        String redisKey = user.getTenantId().concat(user.getUserIdOrOutUserIdIfOutUser()).concat(key);
        ConfigType keyType = ConfigType.getConfigType(key);
        if (Objects.equals(keyType, ConfigType.NONE)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_KEY_ERROR));
        }
        if (StringUtils.isBlank(value)) {
            return GetSetConfigValue.Result.builder().success(Boolean.TRUE).value(value).build();
        }
        String userConfig = configService.findUserConfig(user, key);
        if (StringUtils.isBlank(userConfig)) {
            configService.createUserConfig(user,key,value, ConfigValueType.STRING);
        } else {
            if(!Objects.equals(userConfig,value)){
                configService.updateUserConfig(user,key,value, ConfigValueType.STRING);
            }
        }
        redisDataAccessor.set(redisKey,value,24*60*60);
        return GetSetConfigValue.Result.builder().success(Boolean.TRUE).value(value).build();
    }

    @Override
    public void deleteTenantConfig(User user, String key){
        configService.deleteTenantConfig(user, key);
    }

    protected void sendMqAfterInitModuleSuccess(String tenantId) {
        Map<String, String> initMsgMap = Maps.newHashMap();
        initMsgMap.put("tenantId", tenantId);
        initMsgMap.put("moduleCode", getConfigKey());
        byte[] bytes = JSON.toJSONString(initMsgMap).getBytes(StandardCharsets.UTF_8);
        sender.sendMessage(bytes);
    }

    public String valueHandler(String queryRst){
        return StringUtils.equals("1", queryRst) ? "1" : "0";
    }

    public void sendAuditLog(User user,String message,String bizOperationName){
        Map<String,String> header = RestUtils.getDDSHeaders(user.getTenantId(),user.getUserId());
        User userInfo = orgService.getUser(user.getTenantId(),user.getUserId());
        SettingLogModel.SetttingLogInfo settingLog = SettingLogModel.SetttingLogInfo.builder()
                .appId("CRM")
                .bizOperationName(bizOperationName)
                .corpId(user.getTenantId())
                .module("35")
                .userId(user.getUserId())
                .userName(userInfo.getUserName())
                .textMessage(message)
                .objectName("35")
                .operationTime(System.currentTimeMillis())
                .jsonMessage(SettingLogModel.JsonText.builder().
                        textMsg(SettingLogModel.TexMsg.builder().dataID("").objectType(0).type(0).text(message).build()).build())
                .build();
        SettingLogModel.Arg arg =SettingLogModel.Arg.builder()
                .log(Lists.newArrayList(settingLog))
                .build();
        logServiceProxy.recordLog(header,arg);
    }

    public IObjectDescribe getDescribeWithSimplifiedChinese(User user, String describeApiName) {
       return describeWithSimplifiedChineseService.findByDescribeApiName(user, describeApiName);
    }

    public Map<String, IObjectDescribe> getDescribeWithSimplifiedChineseByApiNames(User user, List<String> describeApiNames) {
        return describeWithSimplifiedChineseService.findByDescribeApiNameList(user, describeApiNames);
    }
}

