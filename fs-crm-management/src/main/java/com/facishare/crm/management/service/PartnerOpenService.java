package com.facishare.crm.management.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.describebuilder.*;
import com.facishare.crm.management.resource.PrmServiceResource;
import com.facishare.crm.management.service.config.DescUtil;
import com.facishare.crm.management.utils.PrmUtils;
import com.facishare.crm.model.BatchAddObjectToPRMModel;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.rest.ApprovalInitProxy;
import com.facishare.crm.rest.dto.ApprovalInitModel;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.*;
import com.facishare.crm.sfa.prm.api.channel.ChannelAdmissionService;
import com.facishare.crm.sfa.prm.api.dto.AdmissionConfigDTO;
import com.facishare.crm.util.CRMRemindRecordUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypeRoleViewPojo;
import com.facishare.paas.appframework.privilege.RoleService;
import com.facishare.paas.appframework.privilege.model.role.ChannelManagerRoleProvider;
import com.facishare.paas.appframework.privilege.model.role.Role;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.*;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.management.utils.SFAManagamentI18NKeys.*;
import static com.facishare.crm.sfa.utilities.constant.PartnerConstants.*;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Service
@Slf4j
public class PartnerOpenService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private RoleService roleService;
    @Autowired
    private ChannelManagerRoleProvider channelManagerRoleProvider;
    @Autowired
    private RecordTypeLogicServiceImpl recordTypeLogicService;
    @Autowired
    private CRMNotificationService crmNotificationService;
    @Autowired
    private FunctionPrivilegeServiceExt functionPrivilegeServiceExt;
    @Autowired
    private LayoutServiceExt layoutServiceExt;
    @Autowired
    private PrmServiceResource prmServiceResource;
    @Autowired
    private ApprovalInitProxy approvalInitProxy;
    @Autowired
    private EnterpriseInitService enterpriseInitService;
    @Resource(name = "crmMenuInitServiceSFA")
    private CrmMenuInitService crmMenuInitService;
    @Autowired
    private DescUtil descUtil;
    @Autowired
    private InitObjectService initObjectService;
    @Autowired
    private LicenseService licenseService;
    @Resource
    private ChannelAdmissionService channelAdmissionService;

    private static final List<Tuple<String, String>> layoutListMap = Lists.newArrayList();

    static {
        layoutListMap.add(Tuple.of(SFAPreDefine.Partner.getApiName(), "detailprmzzc"));
        layoutListMap.add(Tuple.of(SFAPreDefine.Contact.getApiName(), "partnerdetail"));
        layoutListMap.add(Tuple.of(SFAPreDefine.Contact.getApiName(), "partnerdetailprmzzc"));
    }

    public void initPartnerObject(User user) throws MetadataServiceException {
        initObjectService.initObject(user, SFAPreDefine.Partner.getApiName());
        // 创建合作伙伴对象菜单
        boolean createMenuSuccess = crmMenuInitService.createMenuItem(user, Lists.newArrayList(Utils.PARTNER_API_NAME), "");
        if (!createMenuSuccess) {
            log.error("initPartnerObject createMenuItem error, tenant:{}, apiName:{}", user.getTenantId(), SFAPreDefine.Partner.getApiName());
        }
    }

    /**
     * 添加渠道经理角色
     *
     * @param user
     * @return
     */
    public void addPrmRole(User user) {
        //新增渠道经理角色
        try {
            if (Boolean.TRUE.equals(roleService.checkRoleIsExist(user.getTenantId(), "CRM", Role.CHANNEL_MANAGER.getRoleCode()))) {
                return;
            }
            roleService.addPredefinedRole(user.getTenantId(), Role.CHANNEL_MANAGER);
            List<String> havePerFuncCodes = Lists.newArrayList();
            for (String menuApiName : PrmConstant.menuApiNames) {
                for (String funcCode : channelManagerRoleProvider.getHavePermissFuncCodes()) {
                    if (funcCode.startsWith(menuApiName)) {
                        havePerFuncCodes.add(funcCode);
                    }
                }
            }
            serviceFacade.updatePreDefinedFuncAccess(user, Role.CHANNEL_MANAGER.getRoleCode(), havePerFuncCodes, null);
        } catch (AppBusinessException bizEx) {
            throw bizEx;
        } catch (Exception e) {
            log.warn("PartnerOpenService# 初始化渠道经理角色失败, tenant:{}", user.getTenantId(), e);
            throw new ValidateException(I18N.text(SFA_PRM_INIT_CHANNEL_ROLE_FAILED));
        }
    }

    public void createPrmContactRecordType(User user) {
        List<String> jsonList = Lists.newArrayList();
        //初始化合作伙伴联系人业务类型
        jsonList.add("{\"label\":\"合作伙伴联系人\",\"api_name\":\"default_contact_partner__c\",\"description\":\"合作伙伴联系人1\",\"config\":{\"remove\":0},\"is_active\":true,\"roles\":[{\"role_code\":\"00000000000000000000000000000025\",\"is_default\":true,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetail_layout_by_UDObjectServer__c\"},{\"role_code\":\"00000000000000000000000000000006\",\"is_default\":false,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetail_layout_by_UDObjectServer__c\"}]}");//ignoreI18n
        // 初始化合作伙伴自注册业务类型
        jsonList.add("{\"label\":\"合作伙伴自注册联系人\",\"api_name\":\"default_contact_partner_zzc__c\",\"description\":\"合作伙伴联系人\",\"config\":{\"remove\":0},\"is_active\":true,\"roles\":[{\"role_code\":\"00000000000000000000000000000025\",\"is_default\":true,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetailprmzzc_layout_by_UDObjectServer__c\"},{\"role_code\":\"00000000000000000000000000000006\",\"is_default\":false,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetailprmzzc_layout_by_UDObjectServer__c\"}]}");//ignoreI18n
        for (String json : jsonList) {
            RecordTypeRoleViewPojo pojo = JSON.parseObject(json, RecordTypeRoleViewPojo.class);
            RecordTypeResult recordTypeResult = recordTypeLogicService.findRecordInfo(user, SFAPreDefineObject.Contact.getApiName(), pojo.getApi_name());
            if (CollectionUtils.notEmpty(recordTypeResult.getRecordTypeOption())) {
                continue;
            }
            RecordTypeResult result = recordTypeLogicService.createRecordType(user.getTenantId(), Utils.CONTACT_API_NAME, pojo, user);
            if (!result.isSuccess()) {
                throw new ValidateException(I18N.text(SFA_PRM_INIT_CONTACT_RECORD_FAILED));
            }
        }
    }

    public void sendMessage(User user, boolean success, String errMsg) {
        String content;
        String title;
        String titleKey;
        String contentKey;
        String extendContent = "";
        if (success) {
            content = "您好，合作伙伴对象已经成功开启，并且相关配置逻辑也已完成，请放心使用！";//ignoreI18n
            title = "合作伙伴开启成功";//ignoreI18n
            titleKey = SFA_OPEN_PARTNER_SUCCESS_TITLE_NEW_RECORD;
            contentKey = SFA_OPEN_PARTNER_SUCCESS_CONTENT_NEW_RECORD;
        } else {
            extendContent = errMsg + "，trace: " + TraceContext.get().getTraceId();
            content = "您好，合作伙伴对象开启失败，请重新操作开启！";//ignoreI18n
            title = "合作伙伴开启失败";//ignoreI18n
            titleKey = SFA_OPEN_PARTNER_FAILURE_TITLE_NEW_RECORD;
            contentKey = SFA_OPEN_PARTNER_FAILURE_CONTENT_NEW_RECORD_V2;
        }
        Set<Integer> receiverIds = Sets.newHashSet();
        if (NumberUtils.isNumber(user.getUserId())) {
            receiverIds.add(Integer.parseInt(user.getUserId()));
        }
        if (CollectionUtils.empty(receiverIds)) {
            return;
        }
        CRMNotification crmNotification = CRMNotification.builder()
                .sender(user.getUserId())
                .remindRecordType(92)
                .content(I18N.text(SFA_OPEN_PARTNER_FAILURE_CONTENT_NEW_RECORD_V2, extendContent))
                .title(I18N.text(titleKey))
                .dataId("")
                .content2Id("0")
                .receiverIds(receiverIds)
                .build();
        log.info("partner_init sendCRMNotification,tenantId {} {}", user.getTenantId(), crmNotification);
        CRMRemindRecordUtil.sendNewCRMRecord(crmNotificationService,
                user,
                92,
                Lists.newArrayList(receiverIds),
                "",
                title,
                content,
                titleKey,
                Lists.newArrayList(),
                contentKey,
                Lists.newArrayList(extendContent),
                null,
                null);
    }

    public void createPartnerSpecialFunc(User user, Set<String> objectApiNames) {
        if (CollectionUtils.empty(objectApiNames)) {
            return;
        }
        //预置对象刷功能权限，更换合作伙伴、移除合作伙伴
        objectApiNames.remove("ActiveRecordObj");
        List<String> actionCodeList = Lists.newArrayList(ObjectAction.CHANGE_PARTNER.getActionCode(), ObjectAction.DELETE_PARTNER.getActionCode());
        for (String needInitApiName : objectApiNames) {
            functionPrivilegeServiceExt.createFunctionPrivilege(user, needInitApiName, actionCodeList);
        }
        //  7.2.0 预置线索对象功能权限：转换合作伙伴、转换新建
        functionPrivilegeServiceExt.createFunctionPrivilege(user, SFAPreDefineObject.Leads.getApiName(), Lists.newArrayList(ObjectAction.TRANSFER_PARTNER.getActionCode()));
        functionPrivilegeServiceExt.createFunctionPrivilege(user, SFAPreDefineObject.Partner.getApiName(), Lists.newArrayList(ObjectAction.TRANSFER_ADD.getActionCode()));
        // 8.7.0 预置线索对象功能权限：合并
//        functionPrivilegeServiceExt.createFunctionPrivilege(user, SFAPreDefineObject.Partner.getApiName(), Lists.newArrayList(ObjectAction.MERGE.getActionCode()));
        functionPrivilegeServiceExt.createSubmitAndAddContact(user);
    }

    public void initObjectLayout(User user, Map<String, IObjectDescribe> describeMap) {
        layoutListMap.forEach(tuple -> {
            String objectApiName = tuple.getKey();
            String type = tuple.getValue();
            layoutServiceExt.createLayoutByLocalURI(user, objectApiName, type);
        });
        // 把外部来源、合作伙伴、所属合作伙伴 字段加到布局中
        addPartnerField2Layout(user, describeMap);
        // 联系人上布局，account_id 都变成为必填
        descUtil.changeContactSpecialDescribeAndLayout(user, describeMap.get(SFAPreDefine.Contact.getApiName()));
    }

    private void addPartnerField2Layout(User user, Map<String, IObjectDescribe> describeMap) {
        List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList = Lists.newArrayList();
        describeMap.forEach((objectApiName, describe) -> {
            IFieldDescribe partnerIdField = describe.getFieldDescribe(PARTNER_ID);
            IFieldDescribe outResourcesField = describe.getFieldDescribe(OUT_RESOURCES);
            if (partnerIdField != null) {
                FieldLayoutPojo reference = getFieldLayoutPojo(SystemConstants.RenderType.ObjectReference.renderType, false, false);
                addFieldTupleList.add(Tuple.of(partnerIdField, reference));
            }
            if (outResourcesField != null) {
                FieldLayoutPojo selectOne = getFieldLayoutPojo(SystemConstants.RenderType.SelectOne.renderType, false, false);
                addFieldTupleList.add(Tuple.of(outResourcesField, selectOne));
            }
            if (SFAPreDefine.Contact.getApiName().equals(objectApiName)) {
                IFieldDescribe ownedPartnerIdField = describe.getFieldDescribe(OWNED_PARTNER_ID);
                if (ownedPartnerIdField != null) {
                    FieldLayoutPojo reference = getFieldLayoutPojo(SystemConstants.RenderType.ObjectReference.renderType, false, false);
                    addFieldTupleList.add(Tuple.of(ownedPartnerIdField, reference));
                }
            }
            layoutServiceExt.insertFieldsToLayout(user, describe, addFieldTupleList);
        });
    }

    private FieldLayoutPojo getFieldLayoutPojo(String renderType, boolean readOnly, boolean required) {
        FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
        fieldLayoutPojo.setReadonly(readOnly);
        fieldLayoutPojo.setRequired(required);
        fieldLayoutPojo.setRenderType(renderType);
        return fieldLayoutPojo;
    }

    public void addObject2Prm(User user) {
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(user.getTenantId(), PrmUtils.prmAppObjects);
        List<String> apiNames = PrmUtils.prmAppObjects.stream().filter(o -> describeMap.get(o) != null).collect(Collectors.toList());
        if (CollectionUtils.empty(apiNames)) {
            return;
        }
        BatchAddObjectToPRMModel.Arg arg = new BatchAddObjectToPRMModel.Arg();
        arg.setAllowRemove(false);
        arg.setApiNames(apiNames);
        prmServiceResource.addObjectRest(user.getTenantId(), arg);
    }

    public void initApproval(User user) {
        List<String> apiNameList = Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.SALES_ORDER_API_NAME);
        Map<String, String> headerMap = getApprovalInitHeaders(user.getTenantId(), user.getUpstreamOwnerIdOrUserId());
        for (String apiName : apiNameList) {
            ApprovalInitModel.Arg arg = new ApprovalInitModel.Arg();
            arg.setEntityId(apiName);
            ApprovalInitModel.Result result = approvalInitProxy.init(arg, headerMap);
            if (!result.isSuccess()) {
                throw new ValidateException(I18N.text(SFA_PRM_INIT_APPROVAL_FLOW_FAILED));
            }
        }
    }

    private Map<String, String> getApprovalInitHeaders(String tenantId, String fsUserId) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-user-id", fsUserId);
        headers.put("x-tenant-id", tenantId);
        return headers;
    }

    public void initMapping(User user) {
        RequestContext requestContext = RequestContextManager.getContext();
        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
        enterpriseInitService.initObjectMappingRule(serviceContext, "rule_bizqueryobj2partnerobj__c");
        enterpriseInitService.initObjectMappingRule(serviceContext, "rule_leadsobj2partnerobj__c");
        enterpriseInitService.initObjectMappingRule(serviceContext, "rule_leadsobj2partnercontact__c");
    }

    public void initPartnerChannelMapping() {
        RequestContext requestContext = RequestContextManager.getContext();
        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
        AdmissionConfigDTO admissionConfigDTO = channelAdmissionService.fetchChannelAdmissionConfig(requestContext.getUser());
        if (admissionConfigDTO == null) {
            log.warn("initMapping: admission config found, tenant:{}", requestContext.getTenantId());
            return;
        }
        if (SFAPreDefineObject.Account.getApiName().equals(admissionConfigDTO.getRelatedObjectApiName())) {
            enterpriseInitService.initObjectMappingRule(serviceContext, "rule_accountobj2contact__c");
            enterpriseInitService.initObjectMappingRule(serviceContext, "rule_accountobj2partneragreementdetail__c");
        } else if (SFAPreDefineObject.Partner.getApiName().equals(admissionConfigDTO.getRelatedObjectApiName())) {
            enterpriseInitService.initObjectMappingRule(serviceContext, "rule_partnerobj2partnercontact__c");
            enterpriseInitService.initObjectMappingRule(serviceContext, "rule_partnerobj2partneragreementdetail__c");
        } else {
            log.warn("initPartnerChannelMapping but relatedObjectApiName unSupport, relatedObjectApiName:{}", admissionConfigDTO.getRelatedObjectApiName());
        }
    }

    public void addPartnerChannelFields(User user) throws MetadataServiceException {
        Map<String, Boolean> partnerChannel = licenseService.existModule(user.getUserId(), Sets.newHashSet("partner_channel"));
        Boolean exists = partnerChannel.get("partner_channel");
        if (Boolean.TRUE.equals(exists)) {
            List<IFieldDescribe> partnerFields = getPartnerFields();
            descUtil.addCustomField(user.getTenantId(), SFAPreDefineObject.Partner.getApiName(), partnerFields);
        }
    }

    private List<IFieldDescribe> getPartnerFields() {
        List<IFieldDescribe> fields = Lists.newArrayList();

        TextFieldDescribe applicantName = TextFieldDescribeBuilder.builder()
                .maxLength(200)
                .apiName("applicant_name")
                .required(false)
                .unique(false)
                .label("姓名")//ignoreI18n
                .build();
        fields.add(applicantName);

        LongTextFieldDescribe businessScope = LongTextFieldDescribeBuilder.builder()
                .maxLength(5000)
                .apiName("business_scope")
                .required(false)
                .label("经营范围")//ignoreI18n
                .build();
        fields.add(businessScope);

        ImageFieldDescribe businessLicense = ImageFieldDescribeBuilder.builder()
                .label("工商营业执照")//ignoreI18n
                .apiName("business_license")
                .required(false)
                .fileAmountLimit(1)
                .build();
        fields.add(businessLicense);

        ImageFieldDescribe identityCards = ImageFieldDescribeBuilder.builder()
                .label("法人身份证")//ignoreI18n
                .apiName("identity_cards")
                .required(false)
                .fileAmountLimit(2)
                .build();
        fields.add(identityCards);

        LongTextFieldDescribe coDescribe = LongTextFieldDescribeBuilder.builder()
                .maxLength(5000)
                .apiName("co_description")
                .required(false)
                .label("合作描述")//ignoreI18n
                .build();
        fields.add(coDescribe);

        CountryFieldDescribe country = CountryFieldDescribeBuilder.builder()
                .apiName("意向-国家")//ignoreI18n
                .apiName("interest_country")
                .build();
        fields.add(country);


        ProvinceFieldDescribe province = ProvinceFieldDescribeBuilder.builder()
                .apiName("意向-省")//ignoreI18n
                .apiName("interest_province")
                .cascadeParentApiName("interest_country")
                .build();
        fields.add(province);

        CityFiledDescribe city = CityFieldDescribeBuilder.builder()
                .apiName("意向-市")//ignoreI18n
                .apiName("interest_city")
                .cascadeParentApiName("interest_province")
                .build();
        fields.add(city);

        DistrictFieldDescribe district = DistrictFieldDescribeBuilder.builder()
                .apiName("意向-区")//ignoreI18n
                .apiName("interest_district")
                .cascadeParentApiName("interest_city")
                .build();
        fields.add(district);

        LocationFieldDescribe location = LocationFieldDescribeBuilder.builder()
                .apiName("意向-定位")//ignoreI18n
                .apiName("interest_location")
                .build();
        fields.add(location);

        TextFieldDescribe address = TextFieldDescribeBuilder.builder()
                .apiName("意向-详细地址")//ignoreI18n
                .apiName("interest_address")
                .required(false)
                .unique(false)
                .maxLength(500)
                .usedIn("component")
                .build();
        fields.add(address);


        AreaFieldDescribe area = AreaFieldDescribeBuilder.builder()
                .apiName("interest_areas")
                .label("意向代理区域/城市")//ignoreI18n
                .areaCountry("interest_country")
                .areaLocation("interest_location")
                .areaDetailAddress("interest_address")
                .areaCity("interest_city")
                .areaProvince("interest_province")
                .areaDistrict("interest_district")
                .build();
        fields.add(area);

        return fields;
    }
}
