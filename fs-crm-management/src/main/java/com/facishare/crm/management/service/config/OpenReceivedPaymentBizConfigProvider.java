package com.facishare.crm.management.service.config;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.management.service.config.model.InsertOrUpdataResult;
import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.modulectrl.SFABizObjMappingRuleWrapperService;
import com.facishare.crm.sfa.utilities.constant.PaymentObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.crm.sfa.utilities.util.SFABizLogUtil.sendAuditLog;

/**
 * 配置 是否开启非标属性
 *
 * <AUTHOR>
 * @date 2019/3/25
 */
@Component
@Slf4j
public class OpenReceivedPaymentBizConfigProvider extends DefaultBizConfigProvider {

    @Autowired
    EnterpriseInitService enterpriseInitService;
    @Autowired
    SFABizObjMappingRuleWrapperService mappingRuleWrapperService;
    @Autowired
    RecordTypeLogicService recordTypeLogicService;
    @Autowired
    ObjectConvertRuleService objectConvertRuleService;

    @Override
    public String getConfigKey() {
        return BizConfigKey.IS_OPEN_RECEIVED_PAYMENT.getId();
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        List<String> describeList = Lists.newArrayList(SFAPreDefineObject.BankStatement.getApiName(), SFAPreDefineObject.VirtualAccount.getApiName(),
                SFAPreDefineObject.ReceivedPayment.getApiName(), Utils.CUSTOMER_PAYMENT_API_NAME);
        Map<String, IObjectDescribe> describeMap = getDescribeWithSimplifiedChineseByApiNames(user, describeList);
        for (String describeName : describeList) {
            if (Objects.equals(describeName, Utils.CUSTOMER_PAYMENT_API_NAME)) {
                continue;
            }
            //刷描述
            IObjectDescribe describe = describeMap.get(describeName);
            if (describe == null) {
                enterpriseInitService.initDescribeForTenant(user.getTenantId(), describeName);
            }
            recordTypeLogicService.recordTypeInit(user, describeName + "_default_layout__c", user.getTenantId(), describeName);
            //刷布局
            List<ILayout> layouts = serviceFacade.getLayoutLogicService().findLayoutByObjectApiName(user.getTenantId(), describeName);
            if (layouts == null || layouts.isEmpty()) {
                enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(describeName), user.getTenantId());
            }
        }
        addField(user, describeMap
                , SystemConstants.RenderType.ObjectReference.renderType, SFAPreDefineObject.ReceivedPayment.getApiName()
                , "回款", "payment_received_payment_list");// ignoreI18n
        //处理映射规则
        initObjectConvertRule();

        super.setConfigValue(user, value, oldValue, key);
    }

    private void addField(User user, Map<String, IObjectDescribe> describiMap,String renderType,String targetApiName, String targetRelatedListLabel,String targetRelatedListName ) {
        Map<String, Object> configMap = new HashMap<>();
        IObjectDescribe attributeDescribe = describiMap.get(Utils.CUSTOMER_PAYMENT_API_NAME);
        if (attributeDescribe != null) {
            InsertOrUpdataResult fieldResult = null;
            if(renderType.equals(SystemConstants.RenderType.ObjectReference.renderType)){
                fieldResult = descUtil.getUpdateOrInsertFieldDescribe(attributeDescribe, PaymentObjConstants.Field.ReceivedPaymentId.apiName,PaymentObjConstants.Field.ReceivedPaymentId.label,targetApiName,targetRelatedListLabel,targetRelatedListName,configMap,true);
            } else if(renderType.equals(SystemConstants.RenderType.Text.renderType)){
                fieldResult = descUtil.getUpdateOrInsertTextFieldDescribe(attributeDescribe, PaymentObjConstants.Field.ReceivedPaymentId.apiName, PaymentObjConstants.Field.ReceivedPaymentId.label, false);
            }
            if(fieldResult == null){
                return;
            }
            if (Boolean.TRUE.equals(fieldResult.getIsInsert())) {
                descUtil.addFieldDescribe(attributeDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            } else {
                descUtil.updateFieldDescribe(attributeDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            }
            // 把新增的字段放入到指定的layout中的FormComponent中的基本信息FieldSection的里面
            FieldLayoutPojo fieldLayoutPojo = descUtil.getFieldLayoutPojo(renderType, true, false);
            descUtil.insertFieldToLayout(user, attributeDescribe, fieldResult.getFieldDescribe(), fieldLayoutPojo);
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), Utils.CUSTOMER_PAYMENT_API_NAME);
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
    }

    private void initObjectConvertRule() {
        ServiceContext serviceContext = new ServiceContext(RequestContextManager.getContext(), null, null);
        try {
            if(objectConvertRuleService.supportConvertRule(serviceContext.getTenantId())) {
                enterpriseInitService.initObjectConvertRule(serviceContext, "rule_convert_receivedpayment_to_payment");
            }
        } catch (Exception e) {
            log.error("initObjectConvertRule error :{}",e.getMessage(),e);
        }
    }
}
