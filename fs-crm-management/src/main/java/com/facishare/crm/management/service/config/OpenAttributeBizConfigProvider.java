package com.facishare.crm.management.service.config;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.describebuilder.ArrayFieldDescribeBuilder;
import com.facishare.crm.management.service.AttributeGroupInitService;
import com.facishare.crm.management.service.config.model.InsertOrUpdataResult;
import com.facishare.crm.management.utils.SFAManagamentI18NKeys;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.modulectrl.SFABizObjMappingRuleWrapperService;
import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.crm.sfa.utilities.util.DataOrganizationUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.auth.AddRoleRecordTypeModel;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypePojo;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;

/**
 * 配置 是否开启属性属性值
 *
 * <AUTHOR>
 * @date 2019/3/25
 */
@Component
@Slf4j
public class OpenAttributeBizConfigProvider extends DefaultBizConfigProvider {

    @Autowired
    EnterpriseInitService enterpriseInitService;
    @Autowired
    private AttributeGroupInitService attributeGroupInitService;

    @Override
    public String getConfigKey() {
        return BizConfigKey.IS_OPEN_ATTRIBUTE.getId();
    }

    @Autowired
    private RecordTypeAuthProxy recordTypeAuthProxy;// 分配业务类型和默认布局
    @Autowired
    SFABizObjMappingRuleWrapperService mappingRuleWrapperService;
    @Autowired
    RecordTypeLogicService recordTypeLogicService;

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        List<String> describeList = Lists.newArrayList(Utils.ATTRIBUTE_OBJ_API_NAME, Utils.ATTRIBUTE_VALUE_OBJ_API_NAME,
                Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.QUOTE_LINES_API_NAME, Utils.PRODUCT_API_NAME,
                Utils.ATTRIBUTE_PRICE_BOOK_OBJ_API_NAME, Utils.ATTRIBUTE_APPLICABLE_PRICE_BOOK_OBJ_API_NAME,
                Utils.ATTRIBUTE_PRICE_BOOK_LINES_OBJ_API_NAME,
                Utils.SALE_CONTRACT_LINE_API_NAME);
        Map<String, IObjectDescribe> describiMap = getDescribeWithSimplifiedChineseByApiNames(user, describeList);
        log.warn("OpenAttributeBizConfigProvider find objects");
        Integer limit = AttributeUtils.getAttributeLimit(user.getTenantId());
        for (String describeName : describeList) {
            //刷描述
            if (describeName.equals(SFAPreDefineObject.Attribute.getApiName())
                || describeName.equals(SFAPreDefineObject.AttributeValue.getApiName())
                || describeName.equals(SFAPreDefineObject.AttributePriceBook.getApiName())
                || describeName.equals(SFAPreDefineObject.AttributePriceBookLines.getApiName())
                || describeName.equals(SFAPreDefineObject.AttributeApplicablePriceBook.getApiName())) {
                IObjectDescribe describe = describiMap.get(describeName);
                if (describe == null) {
                    enterpriseInitService.initDescribeForTenant(user.getTenantId(), describeName);
                }
                //先刷功能权限(950提到模板库，优化性能)
                recordTypeLogicService.recordTypeInit(user, describeName + "_default_layout__c", user.getTenantId(), describeName);
                //enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(describeName), user, null, null, null);
                //刷布局
                List<ILayout> layouts = serviceFacade.getLayoutLogicService().findLayoutByObjectApiName(user.getTenantId(), describeName);
                if (layouts == null || layouts.isEmpty()) {
                    enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(describeName), user.getTenantId());
                }
                if(Utils.ATTRIBUTE_PRICE_BOOK_LINES_OBJ_API_NAME.equals(describeName) && limit > 30) {
                    beyondAttributeBuild(limit, describe);
                }
            }
        }
        log.warn("OpenAttributeBizConfigProvider init describe and layout");
        //属性值所有员工角色绑定默认业务类型
        //addRoleRecordForAttributeValue(user);

        //region 订单产品，报价单明细加字段
        // TODO: 2020/10/14 configmap
        addAttributeField(user, describiMap, Utils.SALES_ORDER_PRODUCT_API_NAME,"attribute","属性",SystemConstants.RenderType.Text.renderType,null,null,null); // ignoreI18n
        addAttributeField(user, describiMap, Utils.QUOTE_LINES_API_NAME,"attribute","属性",SystemConstants.RenderType.Text.renderType,null,null,null); // ignoreI18n
        addAttributeField(user, describiMap, Utils.SALES_ORDER_PRODUCT_API_NAME,"attribute_price_book_id","产品属性价目表", SystemConstants.RenderType.ObjectReference.renderType,SFAPreDefineObject.AttributePriceBook.getApiName(),"订单产品","attribute_price_book_sales_order_list"); // ignoreI18n
        addAttributeField(user, describiMap, Utils.QUOTE_LINES_API_NAME,"attribute_price_book_id","产品属性价目表", SystemConstants.RenderType.ObjectReference.renderType,SFAPreDefineObject.AttributePriceBook.getApiName(),"报价单明细","attribute_price_book_quote_lines_list"); // ignoreI18n
        log.warn("OpenAttributeBizConfigProvider add attribute field");
        addAttributeJsonField(user, describiMap, Utils.SALES_ORDER_PRODUCT_API_NAME);
        addAttributeJsonField(user, describiMap, Utils.QUOTE_LINES_API_NAME);
        log.warn("OpenAttributeBizConfigProvider add attribute json field");
        if (SFAConfigUtil.isSaleContractOpen(user.getTenantId())) {
            addAttributeField(user, describiMap, Utils.SALE_CONTRACT_LINE_API_NAME,"attribute","属性",SystemConstants.RenderType.Text.renderType,null,null,null); // ignoreI18n
            addAttributeField(user, describiMap, Utils.SALE_CONTRACT_LINE_API_NAME,"attribute_price_book_id","产品属性价目表", SystemConstants.RenderType.ObjectReference.renderType,SFAPreDefineObject.AttributePriceBook.getApiName(),"合同产品","attribute_price_book_sale_contract_line_list"); // ignoreI18n
            addAttributeJsonField(user, describiMap, Utils.SALE_CONTRACT_LINE_API_NAME);
            log.warn("OpenAttributeBizConfigProvider add contract field");
        }
        productAttributeBuild(limit, describiMap);
        log.warn("OpenAttributeBizConfigProvider product attribute build");
        /*for (Integer i = 1; i <= limit; i++) {
            addProductField(user, describiMap, Utils.PRODUCT_API_NAME, "attribute" + i.toString(), "属性" + i.toString()); // ignoreI18n
        }*/

        addProductField(user, describiMap, Utils.PRODUCT_API_NAME, "attribute_ids", "关联属性"); // ignoreI18n
        addProductField(user, describiMap, Utils.PRODUCT_API_NAME, "pricing_attribute_ids", "是否定价"); // ignoreI18n
        //endregion
        log.warn("OpenAttributeBizConfigProvider add product field");
        //多组织
        DataOrganizationUtils.addDataOrganizationField(user.getTenantId(), Lists.newArrayList(SFAPreDefineObject.AttributePriceBook.getApiName(),
                SFAPreDefineObject.AttributePriceBookLines.getApiName(),
                SFAPreDefineObject.AttributeApplicablePriceBook.getApiName()));
        log.warn("OpenAttributeBizConfigProvider add data organization field");
        //处理映射规则
        addRuleMapping(user.getTenantId());
        log.warn("OpenAttributeBizConfigProvider add rule mapping");
        //处理产品布局
        try {
            addProductComponent(user, describiMap.get(Utils.PRODUCT_API_NAME));
        } catch (Exception e) {
            log.warn("处理产品布局 error,{}", e.toString()); // ignoreI18n
        }
        log.warn("OpenAttributeBizConfigProvider add product component");
        //功能权限和按钮
        serviceFacade.batchCreateFunc(user, Utils.PRODUCT_API_NAME, Lists.newArrayList(ObjectAction.AssociateAttribute.getActionCode()));
        serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, Utils.PRODUCT_API_NAME, Lists.newArrayList(ObjectAction.AssociateAttribute.getActionCode()), Lists.newArrayList());

        //serviceFacade.batchCreateFunc(user, Utils.ATTRIBUTE_OBJ_API_NAME, Lists.newArrayList(ObjectAction.AttributeEnable.getActionCode()));
        //serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, Utils.ATTRIBUTE_OBJ_API_NAME, Lists.newArrayList(ObjectAction.AttributeEnable.getActionCode()), Lists.newArrayList());
        log.warn("OpenAttributeBizConfigProvider add func");
        attributeGroupInitService.handle(user,Utils.ATTRIBUTE_OBJ_API_NAME);
        log.warn("OpenAttributeBizConfigProvider handle attribute group");
        super.setConfigValue(user, value, oldValue, key);
    }

    private void beyondAttributeBuild(int limit, IObjectDescribe describe) {
        List<IFieldDescribe> updatedFields = Lists.newArrayList();
        if (null != describe) {
            for (int i = 31; i <= limit; i++) {
                String attributeApiName = "attribute".concat(String.valueOf(i));
                String attributeLabel = "属性".concat(String.valueOf(i)); // ignoreI18n
                updatedFields.add(ArrayFieldDescribeBuilder.builder()
                        .apiName(attributeApiName)
                        .label(attributeLabel)
                        .unique(false)
                        .required(false)
                        .build());
            }
            if (CollectionUtils.size(updatedFields) > 0) {
                try {
                    objectDescribeService.addCustomFieldDescribe(describe, updatedFields);
                } catch (MetadataServiceException e) {
                    log.error("属性槽位超过30动态扩展失败异常信息", e); // ignoreI18n
                }
            }
        }
    }

    private void addAttributeField(User user, Map<String, IObjectDescribe> describiMap, String apiName,String fieldName,
        String fieldLabel,String renderType,String targetApiName, String targetRelatedListLabel,String targetRelatedListName ) {
        Map configMap = new HashMap<String, Object>();
        IObjectDescribe attributeDescribe = describiMap.get(apiName);
        if (attributeDescribe != null) {
            InsertOrUpdataResult fieldResult = null;
            if(renderType.equals(SystemConstants.RenderType.ObjectReference.renderType)){
                fieldResult = descUtil.getUpdateOrInsertFieldDescribe(attributeDescribe,fieldName,fieldLabel,targetApiName,targetRelatedListLabel,targetRelatedListName,configMap,false);
            } else if(renderType.equals(SystemConstants.RenderType.Text.renderType)){
                fieldResult = descUtil.getUpdateOrInsertTextFieldDescribe(attributeDescribe, fieldName, fieldLabel, false);
            }
            if(fieldResult == null){
                return;
            }
            if (fieldResult.getIsInsert()) {
                descUtil.addFieldDescribe(attributeDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            } else {
                descUtil.updateFieldDescribe(attributeDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            }
            // 把新增的字段放入到指定的layout中的FormComponent中的基本信息FieldSection的里面
            FieldLayoutPojo fieldLayoutPojo = descUtil.getFieldLayoutPojo(renderType, true, false);
            descUtil.insertFieldToLayout(user, attributeDescribe, fieldResult.getFieldDescribe(), fieldLayoutPojo);
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), apiName); // ignoreI18n
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
    }

    private void addProductField(User user, Map<String, IObjectDescribe> describiMap, String apiName, String fieldName, String lable) {
        IObjectDescribe productDescribe = describiMap.get(apiName);
        if (productDescribe != null) {
            InsertOrUpdataResult fieldResult = descUtil.getUpdateOrInsertArrayFieldDescribe(productDescribe
                , fieldName, lable);
            if (fieldResult.getIsInsert()) {
                descUtil.addFieldDescribe(productDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            } else {
                descUtil.updateFieldDescribe(productDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            }
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), apiName); // ignoreI18n
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
    }

    private void productAttributeBuild(int limit,Map<String, IObjectDescribe> describiMap) {
        List<IFieldDescribe> updatedFields = Lists.newArrayList();
        IObjectDescribe productDescribe = describiMap.get(Utils.PRODUCT_API_NAME);
        if(null != productDescribe) {
            for (int i = 1; i <= limit; i++) {
                String attributeApiName = "attribute".concat(String.valueOf(i));
                String attributeLabel = "属性".concat(String.valueOf(i)); // ignoreI18n
                updatedFields.add(ArrayFieldDescribeBuilder.builder()
                        .apiName(attributeApiName)
                        .label(attributeLabel)
                        .unique(false)
                        .required(false)
                        .build());
            }
            if(CollectionUtils.size(updatedFields) > 0) {
                try {
                    objectDescribeService.addCustomFieldDescribe(productDescribe, updatedFields);
                } catch (MetadataServiceException e) {
                    log.error("产品属性创建失败，异常信息",e); // ignoreI18n
                }
            }
        }
    }

    private void addAttributeJsonField(User user, Map<String, IObjectDescribe> describiMap, String apiName) {
        Map configMap = new HashMap<String, Object>();
        IObjectDescribe attributeDescribe = describiMap.get(apiName);
        if (attributeDescribe != null) {
            InsertOrUpdataResult fieldResult = descUtil.getUpdateOrInsertLongTextFieldDescribe(attributeDescribe
                , "attribute_json", "属性值", true); // ignoreI18n
            if (fieldResult.getIsInsert()) {
                descUtil.addFieldDescribe(attributeDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            } else {
                descUtil.updateFieldDescribe(attributeDescribe, Lists.newArrayList(fieldResult.getFieldDescribe()));
            }
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), apiName); // ignoreI18n
            throw new ValidateException(I18N.text(SFAManagamentI18NKeys.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
    }

    private void addRoleRecordForAttributeValue(User user) {
        final AddRoleRecordTypeModel.Arg arg = new AddRoleRecordTypeModel.Arg();
        final RecordTypePojo recordTypePojo = new RecordTypePojo();
        recordTypePojo.setTenantId(user.getTenantId());
        recordTypePojo.setAppId("CRM");
        recordTypePojo.setRoleCode("personnelrole");
        recordTypePojo.setEntityId(Utils.ATTRIBUTE_VALUE_OBJ_API_NAME);
        final String recordTypeId = "default__c";
        recordTypePojo.setRecordTypeId(recordTypeId);
        recordTypePojo.setDefaultType(Objects.equals(recordTypeId, "default__c"));
        arg.setRecordTypePojos(Lists.newArrayList(recordTypePojo));
        arg.setEntityId(Utils.ATTRIBUTE_VALUE_OBJ_API_NAME);
        arg.setRecordTypeId(recordTypeId);
        arg.setAuthContext(user);
        recordTypeAuthProxy.addRoleRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
    }
    private void addProductComponent(User user, IObjectDescribe describe) {
        IComponent component = buildProductAttributeComponent();

        descUtil.insertComponentToLayout(user, describe, component, 0);
    }
    public static CommonComponent buildProductAttributeComponent() {
        CommonComponent component = new CommonComponent();
        component.setType("product_attribute");
        component.setName("product_attribute_component");
        component.setHeader(I18N.text(I18NKey.PRODUCT_ATTRIBUTE));
        component.set("field_api_name", "product_attribute");
        component.setDefineType(ComponentDefineType.BUSINESS.getType());
        ComponentExt.of(component).setLimit(1);
        return component;
    }

    private void addRuleMapping(String tenantId)
    {
        Map<String,String> fieldMapping = Maps.newHashMap();
        fieldMapping.put("attribute","attribute");
        fieldMapping.put("attribute_json","attribute_json");
        fieldMapping.put("attribute_price_book_id","attribute_price_book_id");
        try {
            mappingRuleWrapperService.addFieldMapping(tenantId,"rule_quotelinesobj2salesorderproduct__c", fieldMapping);
            if (SFAConfigUtil.isSaleContractOpen(tenantId)) {
                mappingRuleWrapperService.addFieldMapping(tenantId,"rule_quotelinesobj2salecontractlineobj__c", fieldMapping);
                mappingRuleWrapperService.addFieldMapping(tenantId,"rule_salecontractlineobj2salesorderproductobj__c", fieldMapping);
            }
            mappingRuleWrapperService.addFieldMapping(tenantId,"rule_salesorderprodobj2quotelinesobj__c", fieldMapping);
        } catch (MetadataServiceException e) {
            log.error("MetadataServiceException:{}",e);
        }
    }
}
