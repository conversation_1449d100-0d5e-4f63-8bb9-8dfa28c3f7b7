package com.facishare.crm.management.service.config

import com.facishare.crm.enums.ConfigType
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.license.LicenseService
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18N])
@SuppressStaticInitializationFor(["com.facishare.paas.I18N"])
class OpenContractProgressGoalConfigProviderTest extends Specification {

    @Shared
    private OpenContractProgressGoalConfigProvider configProvider

    @Mock
    @Shared
    private ServiceFacade serviceFacade

    @Mock
    @Shared
    private LicenseService licenseService

    @Mock
    @Shared
    private EnterpriseInitService enterpriseInitService

    def setupSpec() {
        MockitoAnnotations.initMocks(this)
        configProvider = new OpenContractProgressGoalConfigProvider()
        Whitebox.setInternalState(configProvider, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(configProvider, "licenseService", licenseService)
        Whitebox.setInternalState(configProvider, "enterpriseInitService", enterpriseInitService)
    }

    def setup() {
        PowerMockito.mockStatic(I18N)
        PowerMockito.when(I18N.text(anyString())).thenReturn("mock text")
    }

    def "test getConfigKey"() {
        expect:
        configProvider.getConfigKey() == ConfigType.IS_OPEN_CONTRACT_PROGRESS.getKey()
    }

    def "test setConfigValue when already enabled"() {
        given:
        def user = new User("89242", "1000")
        def value = "1"
        def oldValue = "1"
        def key = ConfigType.IS_OPEN_CONTRACT_PROGRESS.getKey()

        when:
        configProvider.setConfigValue(user, value, oldValue, key)

        then:
        noExceptionThrown()
    }

    def "test setConfigValue when trying to disable"() {
        given:
        def user = new User("89242", "1000")
        def value = "0"
        def oldValue = "1"
        def key = ConfigType.IS_OPEN_CONTRACT_PROGRESS.getKey()

        when:
        configProvider.setConfigValue(user, value, oldValue, key)

        then:
        ValidateException exception = thrown(ValidateException)
        exception.getMessage() == "mock text"
    }

//    def "test setConfigValue when enabling with missing describes"() {
//        given:
//        def user = new User("89242", "1000")
//        def value = "1"
//        def oldValue = "0"
//        def key = ConfigType.IS_OPEN_CONTRACT_PROGRESS.getKey()
//        def describeMap = ["A": new ObjectDescribe()]
//        def spyProvider = PowerMockito.spy(configProvider)
//
//        PowerMockito.suppress(MemberMatcher.methods(DefaultBizConfigProvider, "setConfigValue"));
//        when:
//        PowerMockito.doReturn(describeMap).when(spyProvider, "getDescribeWithSimplifiedChineseByApiNames", any(User.class), any(List.class))
//        spyProvider.setConfigValue(user, value, oldValue, key)
//
//        then:
//        noExceptionThrown()
//    }

    def "test validateSetConfig with invalid license"() {
        given:
        def user = new User("89242", "1000")
        def key = ConfigType.IS_OPEN_CONTRACT_PROGRESS.getKey()
        def newValue = "1"
        def spyProvider = PowerMockito.spy(configProvider)

        when:
        PowerMockito.doReturn("1").when(spyProvider, "getConfigValue", any(User.class), anyString())
        PowerMockito.doReturn([] as Set).when(licenseService, "getModule", anyString())
        spyProvider.validateSetConfig(user, key, newValue)

        then:
        ValidateException exception = thrown(ValidateException)
        exception.getMessage() == "mock text"
    }

    def "test validateSetConfig with contract module not enabled"() {
        given:
        def user = new User("89242", "1000")
        def key = ConfigType.IS_OPEN_CONTRACT_PROGRESS.getKey()
        def newValue = "1"
        def spyProvider = PowerMockito.spy(configProvider)

        when:
        PowerMockito.doReturn("0").when(spyProvider, "getConfigValue", any(User.class), anyString())
        spyProvider.validateSetConfig(user, key, newValue)

        then:
        ValidateException exception = thrown(ValidateException)
        exception.getMessage() == "mock text"
    }

    def "test validateSetConfig with valid license"() {
        given:
        def user = new User("89242", "1000")
        def key = ConfigType.IS_OPEN_CONTRACT_PROGRESS.getKey()
        def newValue = "1"
        def modules = ["contract_progress_management_app"] as Set
        def spyProvider = PowerMockito.spy(configProvider)

        when:
        PowerMockito.doReturn("1").when(spyProvider, "getConfigValue", any(User.class), anyString())
        PowerMockito.doReturn(modules).when(licenseService, "getModule", anyString())
        spyProvider.validateSetConfig(user, key, newValue)

        then:
        noExceptionThrown()
    }
} 