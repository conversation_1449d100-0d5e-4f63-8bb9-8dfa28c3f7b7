package com.facishare.crm.sfa.utilities.util

import com.facishare.crm.openapi.Utils
import com.facishare.crm.rest.MultiShoppingMallProxy
import com.facishare.crm.rest.dto.MultiShoppingMallModel
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.model.ProductRangeCheckModel
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt
import com.facishare.crm.sfa.predefine.service.PriceBookCommonService
import com.facishare.crm.sfa.predefine.service.UnitCoreService
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.model.GetAvailableRangeByPartnerModel
import com.facishare.crm.sfa.predefine.service.model.GetPriceBookListByProductIdsModel
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil
import com.facishare.crm.sfa.utilities.constant.AvailableConstants
import com.facishare.crm.util.DoubleParamFunction
import com.facishare.crm.util.MtCurrentUtil
import com.facishare.crm.util.ThreeParamFunction
import com.facishare.paas.appframework.common.service.dto.QueryAllSuperDeptsByDeptIds
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds
import com.facishare.paas.appframework.common.util.AppIdMapping
import com.facishare.paas.appframework.common.util.StopWatch
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController
import com.facishare.paas.appframework.core.util.Lang
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.facishare.paas.metadata.impl.search.Filter
import com.facishare.paas.metadata.impl.search.Operator
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.facishare.paas.metadata.impl.search.Where
import com.facishare.paas.metadata.impl.ui.layout.Button
import com.facishare.paas.metadata.impl.ui.layout.Layout
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import com.facishare.paas.metadata.ui.layout.IButton
import com.facishare.paas.metadata.ui.layout.ILayout
import com.facishare.paas.metadata.util.SpringContextUtil
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.LoggerFactory
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.*

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([SoCommonUtils.class, GrayUtil.class, AppIdMapping.class, ValidDateUtils.class, MtCurrentUtil.class, UdobjGrayConfig.class, RequestContextManager.class, DhtUtil.class])
class AvailableRangeUtilsTest extends RemoveUseless {
    @Shared
    def logger = LoggerFactory.getLogger(AvailableRangeUtils.class)

    @Shared
    private String tenantId = "71568"

    @Shared
    protected AvailableRangeUtils availableRangeUtils

    @Shared
    protected BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService

    @Shared
    protected PriceBookCommonService priceBookCommonService

    @Shared
    protected MetaDataFindServiceExt metaDataFindServiceExt

    @Shared
    protected ServiceFacade serviceFacade

    @Shared
    protected ObjectDataServiceImpl objectDataService

    @Shared
    protected FunctionUtils functionUtils

    @Shared
    protected UnitCoreService unitCoreService

    @Shared
    protected MultiUnitService multiUnitService

    @Shared
    protected RequestContext requestContext

    @Shared
    protected InfraServiceFacade infraServiceFacade

    @Shared
    protected MultiShoppingMallProxy multiShoppingMallProxy

    @Shared
    private User user;

    protected static final List<String> RELATED_FIELD_API_NAME =
            Lists.newArrayList("salesorderproduct_product_list"
                    , "pricebookproduct_salesorderproduct_list"
                    , "price_book_product_quote_lines_list"
                    , "price_book_sales_order_list"
                    , "price_book_quote_list"
                    , "product_quote_lines_list"
                    , "price_book_product_new_opportunity_lines_list"
                    , "product_new_opportunity_lines_list"
                    , "price_book_new_opportunity_list"
                    , "price_book_sale_contract_list"
                    , "product_sale_contract_line_list"
                    , "spu_sku_list"
                    , "SPUObj_salesorderproduct_list");

    def setupSpec() {
        removeI18N()
        removeConfigFactory()
        initSpringContext()
        requestContext = RequestContext.builder()
                .user(user)
                .tenantId(tenantId)
                .lang(Lang.zh_CN)
                .requestSource(RequestContext.RequestSource.CEP)
                .peerName("dht")
                .appId("123")
                .build()
        Whitebox.setInternalState(AvailableRangeUtils, "log", logger)
        Whitebox.setInternalState(AvailableRangeUtils, "RELATED_FIELD_API_NAME", RELATED_FIELD_API_NAME)
        availableRangeUtils = new AvailableRangeUtils()
        bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        priceBookCommonService = PowerMockito.mock(PriceBookCommonService)
        metaDataFindServiceExt = PowerMockito.mock(MetaDataFindServiceExt)
        serviceFacade = PowerMockito.mock(ServiceFacade)
        objectDataService = PowerMockito.mock(ObjectDataServiceImpl.class)
        functionUtils = PowerMockito.mock(FunctionUtils)
        unitCoreService = PowerMockito.mock(UnitCoreService)
        multiUnitService = PowerMockito.mock(MultiUnitService)
        user = PowerMockito.spy(new User("71568", "-10000"))
        infraServiceFacade = PowerMockito.mock(InfraServiceFacade.class)
        multiShoppingMallProxy = PowerMockito.mock(MultiShoppingMallProxy.class)
    }

    def "getAvailablePriceBookList"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(availableRangeUtils, "objectDataService", objectDataService)
        PowerMockito.doReturn(isAvailableRangeEnabled).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        PowerMockito.when(GrayUtil.isPriceBookReform(any())).thenReturn(isPriceBookReform)
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(searchTemplateQuery)
        PowerMockito.when(DhtUtil.isDhtRequest(any(), any())).thenReturn(true)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        //PowerMockito.doReturn(searchTemplateQuery).when(availableRangeUtils, "buildPriceBookRangeSearchQuery", any(), any(), any(), any(), anyBoolean())
        PowerMockito.doReturn(Lists.newArrayList()).when(priceBookCommonService, "getPriceBookList", any(), any(), any(), any(), any(), any())
        //PowerMockito.doReturn(Lists.newArrayList()).when(availableRangeUtils, "getAllPriceBookList", any(), any(), any(), any())
        //PowerMockito.doReturn(Lists.newArrayList()).when(availableRangeUtils, "getAvailableRangeIdList", any(), any(), any(), any(), any(), any(), any(), any(), any())
        //PowerMockito.doReturn(priceBookList).when(availableRangeUtils, "getPriceBookListByRangeIds", any(), any(), any(), any(), any(), anyBoolean(), any(), any(), any(), any(), anyBoolean(), any())
        PowerMockito.doReturn(rangeInfoQueryResult).when(metaDataFindServiceExt, "findBySearchQueryWithFields", any(), any(), any(), any(), anyBoolean())
        PowerMockito.doReturn(rangeInfoQueryResult).when(metaDataFindServiceExt, "findBySearchQuery", any(), any(), any())
        when:
        availableRangeUtils.getAvailablePriceBookList(user, accountId, partnerId, null, null, businessDate, backFillRangeFieldApiName, null, null, null, isActCurrentDetailRow, null)
        then:
        1 == 1
        where:
        accountId | partnerId | isAvailableRangeEnabled | businessDate  | isPriceBookReform | isActCurrentDetailRow | backFillRangeFieldApiName  | searchTemplateQuery       | rangeInfoQueryResult | priceBookList
        "xxx"     | "xxx"     | true                    | ************* | true              | true                  | Lists.newArrayList("name") | new SearchTemplateQuery() | new QueryResult()    | getObjectDataList(["_id"], 1)
        null      | "xxx"     | true                    | ************* | true              | true                  | Lists.newArrayList("name") | new SearchTemplateQuery() | new QueryResult()    | getObjectDataList(["_id"], 1)
        "xxx"     | "xxx"     | false                   | ************* | true              | true                  | Lists.newArrayList("name") | new SearchTemplateQuery() | new QueryResult()    | getObjectDataList(["_id"], 1)
        "xxx"     | "xxx"     | false                   | ************* | false             | true                  | Lists.newArrayList("name") | new SearchTemplateQuery() | new QueryResult()    | getObjectDataList(["_id"], 1)
    }

    def "buildRangeSearchQueryForProduct"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        StopWatch stopWatch = StopWatch.create("xxx")
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isAvailableRangeEnabled).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        PowerMockito.when(GrayUtil.supportScene4RelatedList(any())).thenReturn(false)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenContractConstraintMode", any())
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(getObjectData("_id", "sale_contract_id", "object_describe_api_name", "constraint_mode", "record_type", "partner_id")).when(serviceFacade, "findObjectDataIgnoreAll", any(), any(), any())
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = Maps.newHashMap()
        QueryDeptInfoByUserIds.MainDeptInfo mainDeptInfo = new QueryDeptInfoByUserIds.MainDeptInfo()
        mainDeptInfo.setUserId("1000")
        mainDeptInfo.setDeptId("99999")
        mainDeptInfoMap.put("1000", mainDeptInfo)
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        PowerMockito.doReturn(mainDeptInfoMap).when(serviceFacade, "getMainDeptInfo", any(), any(), any())
        Map<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> allSuperDeptIds = Maps.newHashMap()
        allSuperDeptIds.put("99999", Lists.newArrayList())
        PowerMockito.doReturn(allSuperDeptIds).when(serviceFacade, "getAllSuperDeptsByDeptIds", any(), any(), any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isPartnerEnabled", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenContractConstraintPricebookAvailableRange", any())
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id", "available_range_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryWithFields", any(), any(), any(), any(), anyBoolean())
        PowerMockito.when(AppIdMapping.isPRM(any())).thenReturn(false);
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isDhtMultiLevelOrder", any())
        Whitebox.setInternalState(availableRangeUtils, "objectDataService", objectDataService)
        PowerMockito.doReturn(getObjectData("_id")).when(objectDataService, "findById", any(), any(), any(), any())
        Map map = new HashMap()
        map.put("available_range_id", "xxx")
        map.put("_id", "xxx")
        PowerMockito.doReturn(Lists.newArrayList(map)).when(objectDataService, "findBySql", any(), any())
        Whitebox.setInternalState(availableRangeUtils, "functionUtils", functionUtils)
        PowerMockito.doReturn(new SearchTemplateQuery()).when(functionUtils, "handleFiltersByFunction", any(), any(), any(), any(), any())
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQueryWithFieldsIgnoreAll", any(), any(), any(), any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenAvailableRangePriority", any())
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        PowerMockito.when(DhtUtil.isDhtOrAccessoriesMallRequest(any(), any())).thenReturn(true)
        when:
        availableRangeUtils.buildRangeSearchQueryForProduct(user, new SearchTemplateQuery(), relatedListName, accountId, partnerId, "xxx", "id", stopWatch, getObjectData("_id", "sale_contract_id", "object_describe_api_name", "constraint_mode", "record_type", "partner_id"))
        then:
        1 == 1
        where:
        relatedListName                  | accountId | partnerId | isAvailableRangeEnabled
        "salesorderproduct_product_list" | "xxx"     | "xxx"     | true
        "salesorderproduct_product_list" | "xxx"     | null      | true
        "salesorderproduct_product_list" | "xxx"     | "xxx"     | false
        "salesorderproduct_product_list" | null      | null      | true
        "xxx"                            | "xxx"     | "xxx"     | true
    }

    def "getEffectivePriceBookProductList"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        PowerMockito.mockStatic(GrayUtil.class)
        List<IObjectData> baseProductList = getObjectDataList(["_id", "product_id", "start_count", "end_count"], 1)
        def objectData = baseProductList.get(0)
        objectData.set("available_range_id", Lists.newArrayList("xxx"))
        baseProductList.add(objectData)
        List<IObjectData> availableProductList = getObjectDataList(["_id", "available_range_id", "product_id"], 1)
        List<IObjectData> standardPriceBookProductList = getObjectDataList(["_id", "product_id"], 1)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenPriceBookProductTieredPrice", any())
        when:
        List<IObjectData> list = availableRangeUtils.getEffectivePriceBookProductList(availableRangeFlag, priceBookFlag, productId, baseProductList, availableProductList, standardPriceBookProductList, needUseStandardPriceBook, user, amount)
        then:
        list != null
        where:
        availableRangeFlag | priceBookFlag | productId | needUseStandardPriceBook | amount
        true               | true          | "xxx"     | true                     | new BigDecimal("1")
        false              | true          | "xxx"     | false                    | new BigDecimal("20")
        false              | true          | "xxx"     | true                     | new BigDecimal("20")
    }

    def "getProductDataListOutOfScope"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isAvailableRangeEnabled).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isPriceBookEnabled", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenPriceBookProductValidPeriod", any())
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(serviceFacade, "findObjectDataByIdsIgnoreFormula", any(), any(), any())
        //PowerMockito.doReturn(getObjectData("_id")).when(availableRangeUtils, "findSaleContractInfo", any(), any())
        //PowerMockito.doReturn(Lists.newArrayList("xxx")).when(availableRangeUtils, "getAvailableRangeIdList", any(), any(), any(), any(), any(), any(), any())
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id", "product_id"], 1))
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryWithFields", any(), any(), any(), any(), anyBoolean())
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQuery", any(), any(), any())
        PowerMockito.when(GrayUtil.isPriceBookReform(any())).thenReturn(true)
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(priceBookCommonService, "getPriceBookList", any(), any(), any(), any(), any(), anyBoolean())
        PowerMockito.doReturn(true).when(priceBookCommonService, "isPriceBookUseful", any())
        PowerMockito.doReturn(getObjectDataList(["_id", "product_id"], 1)).when(priceBookCommonService, "getPriceBookProducts", any(), any(), any())
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        PowerMockito.when(DhtUtil.isDhtOrAccessoriesMallRequest(any(), any())).thenReturn(true)
        when:
        List<IObjectData> list = availableRangeUtils.getProductDataListOutOfScope(user, accountId, partnerId, priceBookId, productIdList)
        then:
        list != null
        where:
        accountId | partnerId | priceBookId | productIdList             | isAvailableRangeEnabled
        "xxx"     | "xxx"     | "xxx"       | Lists.newArrayList("xxx") | true
        "xxx"     | "xxx"     | "xxx"       | null                      | true
        "xxx"     | "xxx"     | "xxx"       | Lists.newArrayList("xxx") | false
    }

    def "buildRangeSearchQueryForPriceBook"() {
        given:
        PowerMockito.mockStatic(GrayUtil.class)
        //PowerMockito.doReturn(getObjectDataList(["_id", "price_book_id"], 1)).when(availableRangeUtils, "getAvailablePriceBookList", any() as User, anyString(), anyString(), anyLong(), any())
        when:
        availableRangeUtils.buildRangeSearchQueryForPriceBook(user, new SearchTemplateQuery(), relatedList, accountId, partnerId, 14342423424L, true, getObjectData("_id"))
        then:
        1 == 1
        where:
        accountId | partnerId | relatedList
        "xxx"     | "xxx"     | "salesorderproduct_product_list"
        "xxx"     | "xxx"     | null
        null      | "xxx"     | "salesorderproduct_product_list"
    }

    def "getPriceBookListByProductIds"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        PowerMockito.when(GrayUtil.isPriceBookReform(any())).thenReturn(isPriceBookReform)
        List<GetPriceBookListByProductIdsModel.ProductInfo> productInfos = Lists.newArrayList()
        GetPriceBookListByProductIdsModel.ProductInfo productInfo = new GetPriceBookListByProductIdsModel.ProductInfo()
        productInfo.setProductId("xxx")
        productInfo.setAmount(BigDecimal.ONE)
        productInfos.add(productInfo)
        List<IObjectData> priceBookList = getObjectDataList(["_id", "priority"], 1)
        def objectData = priceBookList.get(0)
        objectData.set("available_range_id", Lists.newArrayList("xxx"))
        def saleContractObjectData = getObjectData("_id")
        saleContractObjectData.set("constraint_mode", "xxx")
        //PowerMockito.doReturn(saleContractObjectData).when(availableRangeUtils, "findSaleContractInfo", any(), any())
        //PowerMockito.doReturn(priceBookList).when(availableRangeUtils, "getAvailablePriceBookList", any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), anyBoolean(), any())
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenContractConstraintPricebookAvailableRange", any())
        PowerMockito.doReturn(isOpenTieredPrice).when(bizConfigThreadLocalCacheService, "isOpenPriceBookProductTieredPrice", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenPriceBookProductValidPeriod", any())
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id", "product_id", "pricebook_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQuery", any(), any(), any())
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(new ObjectDescribe()).when(serviceFacade, "findObject", any(), any())
        PowerMockito.doNothing().when(serviceFacade, "fillObjectDataWithRefObject", any(), any(), any())
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        PowerMockito.doReturn(priceBookList.get(0)).when(priceBookCommonService, "getStandardPriceBook", any())
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        when:
        List<IObjectData> list = availableRangeUtils.getPriceBookListByProductIds(user, productIdList, productInfos, accountId, partnerId, businessDate, getObjectData("_id"), Maps.newHashMap(), "xxx")
        then:
        list != null
        where:
        accountId | partnerId | businessDate                      | isOpenTieredPrice | isPriceBookReform | productIdList
        "xxx"     | "xxx"     | System.currentTimeMillis() + 1000 | true              | true              | Lists.newArrayList("xxx")
        "xxx"     | "xxx"     | System.currentTimeMillis() + 1000 | false             | true              | Lists.newArrayList("xxx")
        "xxx"     | "xxx"     | System.currentTimeMillis() + 1000 | false             | false             | null
    }

    def "getPriceBookProductListByMap"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Map<String, List<String>> priceBookId2productIds = Maps.newHashMap()
        priceBookId2productIds.put("xxx", Lists.newArrayList("xxx"))
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenMultiUnitPriceBook", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenPriceBookProductTieredPrice", any())
        PowerMockito.doReturn(isAvailableRangeEnabled).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenContractConstraintMode", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenAvailablePriceBook", any())
        PowerMockito.when(GrayUtil.isPriceBookReform(any())).thenReturn(true)
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(priceBookCommonService, "getPriceBookList", any(), any(), any(), any(), any(), any())
        PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(priceBookCommonService, "getPriceBookList", any(), any(), any(), any(), any(), any(), anyBoolean(), any(), any())
        def saleContractObjectData = getObjectData("_id")
        saleContractObjectData.set("constraint_mode", "detail")
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id", "product_id", "price_book_id", "available_range_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryWithFields", any(), any(), any(), any(), anyBoolean())
        //PowerMockito.doReturn(Lists.newArrayList("xxx", "xxxx")).when(availableRangeUtils, "getAvailableRangeIdList", any(), any(), any(), any(), any(), any(), any())
        //PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(availableRangeUtils, "getAvailableProductDataList", any(), any(), any())
        PowerMockito.doReturn(true).when(priceBookCommonService, "isPriceBookUseful", any(), any())
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        PowerMockito.when(DhtUtil.isDhtOrAccessoriesMallRequest(any(), any())).thenReturn(true)
        when:
        ProductRangeCheckModel productRangeCheckModel = availableRangeUtils.getPriceBookProductListByMap(user, accountId, partnerId, priceBookId2productIds, availablePriceBookDataList, businessDate, masterData, saleContractObjectData)
        then:
        productRangeCheckModel != null
        where:
        accountId | partnerId | businessDate | isAvailableRangeEnabled | availablePriceBookDataList    | masterData
        "xxx"     | "xxx"     | 15423544535L | true                    | getObjectDataList(["_id"], 1) | getObjectData("_id", "sale_contract_id", "object_describe_api_name")
        "xxx"     | "xxx"     | 15423544535L | false                   | getObjectDataList(["_id"], 1) | getObjectData("_id", "sale_contract_id", "object_describe_api_name")
    }

    def "processDataRange"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        def masterData = getObjectData("_id")
        List<IObjectData> detailData = getObjectDataList(["_id"], 1)
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        IObjectDescribe objectDescribe = new ObjectDescribe()
        IFieldDescribe fieldDescribe = new ObjectReferenceFieldDescribe()
        fieldDescribe.setApiName("price_book_id")
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        List<LinkedHashMap> wheres = Lists.newArrayList();
        map.put("connector", Where.CONN.OR);
        List<Map> filters = Lists.newArrayList();
        Map<String, Object> filter = Maps.newHashMap();
        filter.put("field_name", "is_deleted");
        filter.put("operator", Operator.EQ.name());
        filter.put("field_values", Lists.newArrayList("0"));
        filters.add(filter);
        map.put("filters", filters);
        wheres.add(map)
        fieldDescribe.setWheres(wheres)
        objectDescribe.addFieldDescribe(fieldDescribe)
        objectDescribe.setApiName("SalesOrderProductObj")
        PowerMockito.doReturn(objectDescribe).when(serviceFacade, "findObject", any(), any())
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        Whitebox.setInternalState(availableRangeUtils, "functionUtils", functionUtils)
        PowerMockito.doNothing().when(functionUtils, "fillFunctionFilter", any(), any(), any(), any(), any())
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())
        when:
        List<String> list = availableRangeUtils.processDataRange(user, masterData, "SalesOrderProductObj", detailData, Lists.newArrayList("xxx"), Lists.newArrayList("xxx"))
        then:
        list != null
    }

    def "findPriceBook"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        def objectData = getObjectData("_id", "record_type")
        def saleContractObjectData = getObjectData("_id")
        Map<String, List<IObjectData>> availablePriceBookListMap = Maps.newHashMap()
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isDhtMultiLevelOrder", any())
        //PowerMockito.doReturn(Lists.newArrayList("xxx")).when(availableRangeUtils, "handleRealPriceConstraintQuery", any(), any(), any(), any())
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        when:
        Whitebox.invokeMethod(availableRangeUtils, "findPriceBook", user, objectData, saleContractObjectData, availablePriceBookListMap, null)
        then:
        1 == 1
    }

    def "getHighestPriorityPriceBook"() {
        given:
        List<IObjectData> priceBookList = getObjectDataList(["_id", "last_modified_time"], 2)
        when:
        IObjectData objectData = availableRangeUtils.getHighestPriorityPriceBook(priceBookList)
        then:
        objectData != null
    }

    def "getAllPriceBookList"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        //PowerMockito.doReturn(Lists.newArrayList("xxx")).when(availableRangeUtils, "handleRealPriceConstraintQuery", any(), any(), any(), any())
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQuery", any(), any(), any())
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        PowerMockito.doReturn(true).when(priceBookCommonService, "isPriceBookUseful", any(), any())
        when:
        List<IObjectData> list = availableRangeUtils.getAllPriceBookList(user, 135353535L, new SearchTemplateQuery())
        then:
        list != null
    }

    def "getAvailableRangeByIds"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        //PowerMockito.doReturn(Lists.newArrayList("xxx")).when(availableRangeUtils, "getRangeOrgList", any(), any())
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isDhtMultiLevelOrder", any())
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQuery", any(), any(), any())
        when:
        List<IObjectData> list = availableRangeUtils.getAvailableRangeByIds(user, Lists.newArrayList("xxx"), "xxx")
        then:
        list != null
    }

    def "getAvailableRangeIdList"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        //PowerMockito.doReturn(Lists.newArrayList("xxx")).when(availableRangeUtils, "getRangeOrgList", any(), any())
        Whitebox.setInternalState(availableRangeUtils, "objectDataService", objectDataService)
        Map map = Maps.newHashMap()
        map.put("_id", "xxx")
        PowerMockito.doReturn(Lists.newArrayList(map)).when(objectDataService, "findBySql", any(), any())
        //PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(availableRangeUtils, "handleAvailableRangePriority", any(), any())
        when:
        List<String> list = availableRangeUtils.getAvailableRangeIdList(user)
        then:
        list != null
    }

    def "isAvailablePriceBook"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        //PowerMockito.doReturn(getObjectDataList(["_id", "price_book_id"], 1)).when(availableRangeUtils, "getAvailableRangePriceBookList", any(), any())
        PowerMockito.when(GrayUtil.isPriceBookReform(any())).thenReturn(true)
        //PowerMockito.doReturn(null).when(availableRangeUtils, "convertIObjectDataToDocument", any())
        //PowerMockito.doReturn(new SearchTemplateQuery()).when(availableRangeUtils, "buildPriceBookRangeSearchQuery", any(), any(), any(), any())
        //PowerMockito.doReturn(true).when(availableRangeUtils, "isAllPriceBook", any(), any(), any(), any(), any())
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(priceBookCommonService, "getPriceBookList", any(), any(), any(), any(), any(), anyBoolean(), any())
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        when:
        availableRangeUtils.isAvailablePriceBook(user, "xxx", "xxx", Lists.newArrayList("xxx"), "xxx", getObjectData("xxx"), Maps.newHashMap())
        then:
        1 == 1
    }

    def "getAvailableProductList4Spu"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isAvailableRangeEnabled).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        //PowerMockito.doReturn(getObjectData("xxx")).when(availableRangeUtils, "findSaleContractInfo", any(), any())
        //PowerMockito.doReturn(availableRangeIdList).when(availableRangeUtils, "getAvailableRangeIdList", any(), any(), any(), any(), any(), any(), any())
        //PowerMockito.doReturn(isAllProductAvailable).when(availableRangeUtils, "isAllProductAvailable", any(), any())
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        PowerMockito.when(DhtUtil.isDhtOrAccessoriesMallRequest(any(), any())).thenReturn(true)
        when:
        List<String> list = availableRangeUtils.getAvailableProductList4Spu(user, "xxx", "xxx", getObjectData("xxx"))
        then:
        list != null
        where:
        isAvailableRangeEnabled | availableRangeIdList      | isAllProductAvailable
        false                   | null                      | true
        true                    | Lists.newArrayList("xxx") | false
        true                    | Lists.newArrayList("xxx") | true
    }

    def "getAvailableProductListForSubQuery"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isAvailableRangeEnabled).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        //PowerMockito.doReturn(isAllProductAvailable).when(availableRangeUtils, "isAllProductAvailable", any(), any())
        when:
        List<String> list = availableRangeUtils.getAvailableProductListForSubQuery(user, "xxx", Lists.newArrayList("xxx"))
        then:
        list != null
        where:
        isAvailableRangeEnabled | isAllProductAvailable
        false                   | true
        true                    | false
        true                    | true
    }

    def "getPriceBookByIds"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isDhtMultiLevelOrder", any())
        //PowerMockito.doReturn(Lists.newArrayList("xxx")).when(availableRangeUtils, "getRangeOrgList", any(), any())
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQuery", any(), any(), any())
        def priceBookCommonService = PowerMockito.mock(PriceBookCommonService)
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        PowerMockito.doReturn(true).when(priceBookCommonService, "isPriceBookUseful", any())
        when:
        List<IObjectData> list = availableRangeUtils.getPriceBookByIds(user, Lists.newArrayList("xxx"), "xxx")
        then:
        list != null
    }

    def "getAvailableRangeIdsByPartnerId"() {
        given:
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(metaDataFindServiceExt, "findBySearchQueryWithFieldsIgnoreAll", any(), any(), any(), any())
        when:
        Set<String> sets = availableRangeUtils.getAvailableRangeIdsByPartnerId(user, "xxx")
        then:
        sets != null
    }

    def "getAvailableRangeListByAccountIds"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        Whitebox.setInternalState(availableRangeUtils, "objectDataService", objectDataService)
        //PowerMockito.doReturn(Lists.newArrayList("xxx")).when(availableRangeUtils, "getRangeOrgList", any(), any())
        Map map = new HashMap()
        map.put("account_id", "xxx")
        map.put("_id", "xxx")
        map.put("priority", 1)
        //PowerMockito.doReturn(Lists.newArrayList(map)).when(availableRangeUtils, "findAvailableRange", any(), any(), any(), any(), any(), any(), any(), any())
        PowerMockito.doReturn(Lists.newArrayList(map)).when(objectDataService, "findBySql", any(), any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenAvailableRangePriority", any())
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        PowerMockito.when(DhtUtil.isDhtOrAccessoriesMallRequest(any(), any())).thenReturn(true)
        when:
        List<IObjectData> list = availableRangeUtils.getAvailableRangeListByAccountIds(user, Lists.newArrayList("xxx"), "xxx")
        then:
        list != null
    }

    def "getAvailableProductIdsByAvailableIds"() {
        given:
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(metaDataFindServiceExt, "findBySearchQueryWithFieldsIgnoreAll", any(), any(), any(), any())
        when:
        Set<String> sets = availableRangeUtils.getAvailableProductIdsByAvailableIds(user, Sets.newHashSet("xxx"))
        then:
        sets != null
    }

    def "getAvailableProductDataList"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())
        when:
        List<IObjectData> list = availableRangeUtils.getAvailableProductDataList(user, "xxx")
        then:
        list != null
    }

    def "setAvailableRangeDefaultValue"() {
        given:
        IObjectData objectData = getObjectData("_id")
        if (isAdd) {
            objectData.set("account_range", "{\"type\":\"ALL\"}")
        }
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isPartnerEnabled", any())
        PowerMockito.doReturn(isDhtMultiLevelOrder).when(bizConfigThreadLocalCacheService, "isDhtMultiLevelOrder", any())
        when:
        availableRangeUtils.setAvailableRangeDefaultValue("xxx", objectData, true)
        then:
        1 == 1
        where:
        isDhtMultiLevelOrder | isAdd
        true                 | true
        false                | false
    }

    def "openAvailablePriceBookNotNullToll"() {
        given:
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenAvailablePriceBook", any())
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id", "product_id", "pricebook_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQueryIgnoreAll", any(), any(), any())
        when:
        availableRangeUtils.openAvailablePriceBookNotNullToll(user, Lists.newArrayList("xxx"))
        then:
        1 == 1
    }

    def "buildValidPeriodTieredPriceSearchQuery"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isTieredPrice).when(bizConfigThreadLocalCacheService, "isOpenStratifiedOrTieredPrice", any())
        PowerMockito.doReturn(isValidPeriod).when(bizConfigThreadLocalCacheService, "isOpenPriceBookProductValidPeriod", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenMultiUnitPriceBook", any())
        PowerMockito.when(ValidDateUtils.getValidDate(any(), any(), any())).thenReturn(12424421312L)
        PowerMockito.when(GrayUtil.isTieredPriceListOptimize(any())).thenReturn(true)
        def document = getObjectDataDocument("pricebook_id")
        if (isFilterQuantity) {
            document.put("filter_quantity", 1)
        }
        Whitebox.setInternalState(availableRangeUtils, "objectDataService", objectDataService)
        Map map = Maps.newHashMap()
        map.put("_id", "xxx")
        PowerMockito.doReturn(Lists.newArrayList(map)).when(objectDataService, "findBySql", any(), any())
        when:
        availableRangeUtils.buildValidPeriodTieredPriceSearchQuery("xxx", new SearchTemplateQuery(), getObjectData("_id"), document)
        then:
        1 == 1
        where:
        isTieredPrice | isValidPeriod | isFilterQuantity
        true          | true          | false
        true          | false         | false
        false         | true          | false
        true          | true          | true
        true          | false         | true
        false         | true          | true
    }

    def "getAvailableRangeByPartner"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        GetAvailableRangeByPartnerModel.Arg arg = new GetAvailableRangeByPartnerModel.Arg()
        arg.setPartnerId("xxx")
        arg.setSearchQueryInfo("{\"limit\":50,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}")
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isDhtMultiLevelOrder", any())
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id", "product_id", "pricebook_id"], 1))
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        when:
        QueryResult<IObjectData> result = availableRangeUtils.getAvailableRangeByPartner(user, arg)
        then:
        result != null
    }

    def "handlePartnerRange"() {
        given:
        IObjectData objectData = getObjectData("_id")
        objectData.set("record_type", "distribution__c")
        objectData.set("object_describe_api_name", describeApiName)
        objectData.set("partner_range", "{\"type\":\"FIXED\", \"value\":[\"xxxx\"]}")
        objectData.set("apply_partner_range", "{\"type\":\"FIXED\", \"value\":[\"xxxx\"]}")
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isDhtMultiLevelOrder).when(bizConfigThreadLocalCacheService, "isDhtMultiLevelOrder", any())
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        PowerMockito.when(serviceFacade.findObjectDataByIdsIgnoreAll(anyString(), anyList(), anyString())).thenReturn(getObjectDataList(["_id", "name"], 1))
        when:
        availableRangeUtils.handlePartnerRange(user, objectData)
        then:
        1 == 1
        where:
        describeApiName     | isDhtMultiLevelOrder
        "AvailableRangeObj" | true
        "PriceBookObj"      | true
        "AvailableRangeObj" | false
    }

    def "processDetailProductAccountRangeData"() {
        given:
        def dbMasterData = getObjectData("_id")
        def objectData = getObjectData("_id")
        Map<String, IObjectDescribe> objectDescribes = Maps.newHashMap()
        Map<String, List<IObjectData>> detailObjectData = Maps.newHashMap()
        List<IObjectData> detailsToUpdate = getObjectDataList(["_id", "name"], 1)
        List<IObjectData> detailsToAdd = getObjectDataList(["_id", "name"], 1)
        Map<String, List<IObjectData>> tempAvailableRangeMap = Maps.newHashMap()
        when:
        Map<String, List<IObjectData>> map = availableRangeUtils.processDetailProductAccountRangeData(dbMasterData, objectData, objectDescribes, detailObjectData, user, detailsToUpdate, detailsToAdd, tempAvailableRangeMap, null, null, null)
        then:
        map != null
    }

    def "domainQueryAvailableRangeEnable"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        StandardRelatedListController.Arg arg = new StandardRelatedListController.Arg()
        arg.setRelatedListName("xxx")
        arg.setObjectData(getObjectDataDocument("object_describe_api_name"))
        DomainPluginParam domainPluginParam = new DomainPluginParam();
        Map<String, String> fieldMapping = Maps.newHashMap();
        fieldMapping.put("xxx", "xxx");
        domainPluginParam.setFieldMapping(fieldMapping);
        List<DomainPluginParam.DetailObj> details = Lists.newArrayList();
        DomainPluginParam.DetailObj detailObj = new DomainPluginParam.DetailObj();
        detailObj.setDetailKey("price-service");
        detailObj.setObjectApiName(Utils.SALES_ORDER_PRODUCT_API_NAME);
        Map<String, String> detailFieldMapping = Maps.newHashMap();
        detailFieldMapping.put("xxx", "xxx");
        detailObj.setFieldMapping(detailFieldMapping);
        details.add(detailObj);
        domainPluginParam.setDetails(details);
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        IObjectDescribe objectDescribe = new ObjectDescribe()
        IFieldDescribe fieldDescribe = new TextFieldDescribe()
        fieldDescribe.setApiName("xxx")
        fieldDescribe.setDefaultValue("xxx")
        objectDescribe.addFieldDescribe(fieldDescribe)
        PowerMockito.doReturn(objectDescribe).when(serviceFacade, "findObject", any(), any())
        when:
        availableRangeUtils.domainQueryAvailableRangeEnable(arg, true, domainPluginParam, "xxx", "xxx")
        then:
        1 == 1
    }

    def "processRemoveDetailProductAccountRangeData"() {
        given:
        def dbMasterData = getObjectData("_id")
        def objectData = getObjectData("_id")
        Map<String, List<IObjectData>> detailObjectData = Maps.newHashMap()
        when:
        Map<String, List<IObjectData>> result = availableRangeUtils.processRemoveDetailProductAccountRangeData(user, dbMasterData, objectData, detailObjectData)
        then:
        result != null
    }

    def "processRemoveDetailRangeData"() {
        given:
        IObjectData dbMasterData = getObjectData("_id")
        dbMasterData.set("account_range", "{\"type\":\"CONDITION\",\"value\":\"ALL\"}")
        IObjectData objectData = getObjectData("_id")
        objectData.set("account_range", "{\"type\":\"FIXED\",\"value\":\"ALL\"}")
        Map<String, List<IObjectData>> detailObjectData = Maps.newHashMap()
        detailObjectData.put("xxx", getObjectDataList(["_id"], 1))
        when:
        Map<String, List<IObjectData>> result = availableRangeUtils.processRemoveDetailRangeData(user, dbMasterData, objectData, detailObjectData, "xxx", "xxx", "account_range")
        then:
        result != null
    }

    def "getPriceKeyboard"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "objectDataService", objectDataService)
        Whitebox.setInternalState(availableRangeUtils, "functionUtils", functionUtils)
        Whitebox.setInternalState(availableRangeUtils, "infraServiceFacade", infraServiceFacade)
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(getObjectData("_id", "is_multiple_unit")).when(serviceFacade, "findObjectDataIgnoreAll", any(), anyString(), anyString())
        PowerMockito.doReturn(getObjectDataList(["_id", "name"], 1)).when(serviceFacade, "findObjectDataByIds", any(), any(), any())
        PowerMockito.when(ValidDateUtils.getValidDate(any(), any(), any())).thenReturn(1231313213L)
        //PowerMockito.doReturn(getObjectData("xxx")).when(availableRangeUtils, "findSaleContractInfo", any(), any())
        /*def priceBookList = null
        if (isPricebookList) {
            priceBookList = getObjectDataList(["_id", "price_book_id"], 1)
        }*/
        //PowerMockito.doReturn(priceBookList).when(availableRangeUtils, "getAvailablePriceBookList", any(), any(), any(), any(), any(), any(), any(), anyBoolean(), any())
        //PowerMockito.doReturn(isRealPriceConstraintMode).when(availableRangeUtils, "isRealPriceConstraintMode", any(), any())
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        Whitebox.setInternalState(availableRangeUtils, "metaDataFindServiceExt", metaDataFindServiceExt)
        PowerMockito.doReturn("xxx").when(priceBookCommonService, "getStandardPriceBookId", any())
        PowerMockito.doReturn(getObjectDataList(["_id", "product_id", "last_modified_time", "pricebook_id"], 1)).when(priceBookCommonService, "getPriceBookProducts", any(), any(), any())
        Whitebox.setInternalState(availableRangeUtils, "unitCoreService", unitCoreService)
        PowerMockito.doNothing().when(unitCoreService, "fillMultipleUnit", any(), any())
        //PowerMockito.doNothing().when(availableRangeUtils, "fillData", any(), any())
        //PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(availableRangeUtils, "getProductByPriceBookList", any(), any(), any(), any(), any())
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isEnforcePriority", any())
        PowerMockito.doReturn(isOpenMultiUnitPriceBook).when(bizConfigThreadLocalCacheService, "isOpenMultiUnitPriceBook", any())
        Whitebox.setInternalState(availableRangeUtils, "multiUnitService", multiUnitService)
        PowerMockito.doReturn(getObjectDataList(["_id", "unit_id", "is_pricing", "conversion_ratio"], 1)).when(multiUnitService, "getMultiUnitDataByProductIds", any(), any())
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id"], 1))
        PowerMockito.doReturn(queryResult).when(metaDataFindServiceExt, "findBySearchQuery", any(), any(), any())
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        PowerMockito.when(DhtUtil.isDhtOrAccessoriesMallRequest(any(), any())).thenReturn(true)
        when:
        List<IObjectData> result = availableRangeUtils.getPriceKeyboard(user, "xxx", "xxx", "xxx", objectData, Maps.newHashMap(), "detail", 10)
        then:
        result != null
        where:
        isRealPriceConstraintMode | isPricebookList | isOpenMultiUnitPriceBook | objectData
        true                      | true            | true                     | getObjectDataDocument("_id")
        true                      | true            | false                    | getObjectDataDocument("_id")
        false                     | true            | true                     | getObjectDataDocument("_id")
        false                     | false           | true                     | getObjectDataDocument("_id")
    }

    def "removeMobileButton"() {
        given:
        ILayout layout = new Layout()
        List<IButton> buttonList = Lists.newArrayList()
        IButton button = new Button()
        button.setName("xxx")
        button.setAction("Add")
        buttonList.add(button)
        layout.setButtons(buttonList)
        when:
        availableRangeUtils.removeMobileButton(layout)
        then:
        1 == 1
    }

    def "getSpecialFieldMap"() {
        given:
        when:
        Whitebox.invokeMethod(availableRangeUtils, "getSpecialFieldMap")
        then:
        1 == 1
    }

    def "findMinTieredPriceData"() {
        given:
        List<IObjectData> priceBookProductList = Lists.newArrayList()
        priceBookProductList.addAll(getObjectDataList(["_id", "pricebook_id", "start_count", "end_count"], 1))
        priceBookProductList.addAll(getObjectDataList(["_id", "pricebook_id", "start_count", "end_count"], 1))
        when:
        List<IObjectData> list = availableRangeUtils.findMinTieredPriceData(priceBookProductList)
        then:
        list != null
    }

    def "multiUnitConvert"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        List<IObjectData> priceBookProductList = Lists.newArrayList()
        priceBookProductList.addAll(getObjectDataList(["_id", "pricebook_id", "start_count", "end_count"], 1))
        priceBookProductList.addAll(getObjectDataList(["_id", "pricebook_id", "start_count", "end_count"], 1))
        List<IObjectData> multiUnitInfo = getObjectDataList(["_id", "unit_id", "conversion_ratio"], 1)
        Map<String, String> unitIdAndNameMaps = Maps.newHashMap()
        unitIdAndNameMaps.put("xxx", "xxx")
        List<IObjectData> allPriceBookProductList = Lists.newArrayList()
        when:
        Whitebox.invokeMethod(availableRangeUtils, "multiUnitConvert", user, priceBookProductList, multiUnitInfo, unitIdAndNameMaps, "xx1", new BigDecimal("2"), allPriceBookProductList)
        then:
        1 == 1
    }

    def "mcCurrencyConvert"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        List<IObjectData> resultObject = getObjectDataList(["mc_currency"], 1)
        resultObject.get(0).set("xxx", 123)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isCurrencyEnabled", any())
        PowerMockito.when(MtCurrentUtil.getExchangeRateMap(any(), any())).thenReturn(new HashMap<String, String>())
        PowerMockito.when(MtCurrentUtil.changePriceToCurrency(any(), any(), any(), any())).thenReturn(new BigDecimal(10))
        when:
        availableRangeUtils.mcCurrencyConvert(resultObject, "xxx", user, Lists.newArrayList("xxx"))
        then:
        1 == 1
    }

    def "filterPresetObjectData"() {
        given:
        Map<String, List<ObjectDataDocument>> detailDataList = Maps.newHashMap()
        detailDataList.put("SalesOrderProductObj", Lists.newArrayList())
        when:
        availableRangeUtils.filterPresetObjectData(detailDataList, getObjectDataDocument("object_describe_api_name"))
        then:
        1 == 1
    }

    def "buildRangeForMerchantRange"() {
        given:
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isDhtMultiLevelOrder", any())
        Whitebox.setInternalState(availableRangeUtils, "objectDataService", objectDataService)
        PowerMockito.doReturn(getObjectData("_id", "product_range_control")).when(objectDataService, "findById", any(), any(), any(), any())
        when:
        availableRangeUtils.buildRangeForMerchantRange(user, "xxx", "{\"type\":\"FIXED\", \"value\":[\"xxx\"]}", new SearchTemplateQuery(), "distribution__c", "PriceBookObj")
        then:
        1 == 1
    }

    def "buildSearchQueryForAvailableRange"() {
        given:
        when:
        availableRangeUtils.buildSearchQueryForAvailableRange(new SearchTemplateQuery(), "sale_contract_available_range_list", "xxx")
        then:
        1 == 1
    }

    def "buildSearchQueryForPriceBook"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        PowerMockito.when(GrayUtil.isPriceBookReform(any())).thenReturn(true)
        when:
        availableRangeUtils.buildSearchQueryForPriceBook(user, new SearchTemplateQuery(), "sale_contract_pricebook_list", "xxx")
        then:
        1 == 1
    }

    def "buildPriceBookRangeSearchByPartner"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isPriceBookEnabled).when(bizConfigThreadLocalCacheService, "isPriceBookEnabled", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenAvailableRange", any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenAvailablePriceBook", any())
        Whitebox.setInternalState(availableRangeUtils, "objectDataService", objectDataService)
        Map map = new HashMap()
        map.put("_id", "xxx")
        PowerMockito.doReturn(Lists.newArrayList(map)).when(objectDataService, "findBySql", any(), any())
        //PowerMockito.doReturn(getObjectDataList(["_id", "price_book_id"], 1)).when(availableRangeUtils, "getAvailableRangePriceBookList", any(), any())
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        PowerMockito.doReturn(priceBookList).when(priceBookCommonService, "getPriceBookListByPartner", any(), any(), any())
        when:
        availableRangeUtils.buildPriceBookRangeSearchByPartner(user, new SearchTemplateQuery(), partnerId, "_id")
        then:
        1 == 1
        where:
        partnerId | isPriceBookEnabled | priceBookList
        "xxx"     | true               | Lists.newArrayList("xxx")
        "xxx"     | true               | null
        null      | true               | null
        "xxx"     | false              | null
    }

    def "buildProductRangeSearchByPartner"() {
        given:
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isAvailableRangeEnabled).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        Whitebox.setInternalState(availableRangeUtils, "objectDataService", objectDataService)
        Map map = new HashMap()
        map.put("_id", "xxx")
        PowerMockito.doReturn(Lists.newArrayList(map)).when(objectDataService, "findBySql", any(), any())
        when:
        availableRangeUtils.buildProductRangeSearchByPartner(user, new SearchTemplateQuery(), partnerId)
        then:
        1 == 1
        where:
        partnerId | isAvailableRangeEnabled
        "xxx"     | true
        null      | true
        "xxx"     | false
    }

    def "getPriceBookProductCountByPartner"() {
        given:
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(isAvailableRangeEnabled).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        Whitebox.setInternalState(availableRangeUtils, "objectDataService", objectDataService)
        Map map = new HashMap()
        map.put("_id", "xxx")
        PowerMockito.doReturn(Lists.newArrayList(map)).when(objectDataService, "findBySql", any(), any())
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id"], 1))
        queryResult.setTotalNumber(1)
        PowerMockito.when(serviceFacade.findBySearchQuery(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        when:
        availableRangeUtils.getPriceBookProductCountByPartner(user, partnerId, "xxx")
        then:
        1 == 1
        where:
        partnerId | isAvailableRangeEnabled
        "xxx"     | true
        null      | true
        "xxx"     | false
    }

    def "findStandardPriceBookProductList"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        PowerMockito.doReturn(priceBookObj).when(priceBookCommonService, "getStandardPriceBook", any())
        //PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(availableRangeUtils, "getProductByPriceBookList", any(), any(), any(), any())
        when:
        availableRangeUtils.findStandardPriceBookProductList(user, productIdList)
        then:
        1 == 1
        where:
        priceBookObj         | productIdList
        getObjectData("xxx") | Lists.newArrayList("xxx")
        null                 | Lists.newArrayList("xxx")
    }

    def "getPriceBookListByRangeIds"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isAvailableRangeEnabled", any())
        //PowerMockito.doReturn(getObjectDataList(["_id", "price_book_id"], 1)).when(availableRangeUtils, "getAvailableRangePriceBookList", any(), any(), any())
        PowerMockito.when(GrayUtil.isPriceBookReform(any())).thenReturn(true)
        //PowerMockito.doReturn(true).when(availableRangeUtils, "isAllPriceBook", any(), any(), any(), any(), any())
        //PowerMockito.doReturn(new SearchTemplateQuery()).when(availableRangeUtils, "buildPriceBookRangeSearchQuery", any(), any(), any(), any())
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(priceBookCommonService, "getPriceBookList", any(), any(), any(), any(), any(), any(), anyBoolean(), any(), any())
        //PowerMockito.doReturn(getObjectDataList(["_id"], 1)).when(availableRangeUtils, "findPriceBook", any(), any(), any(), any(), any())
        PowerMockito.doReturn(isPriceBookUseful).when(priceBookCommonService, "isPriceBookUseful", any(), any())
        //PowerMockito.doNothing().when(availableRangeUtils, "fillPriceBookData", any(), any(), any(), any())
        when:
        availableRangeUtils.getPriceBookListByRangeIds(user, "xxx", "xxx", Lists.newArrayList("xxx"), new SearchTemplateQuery(), includeStaleData, 131313141321L, getObjectData("_id"), Maps.newHashMap(), "", true, getObjectData("_id"))
        then:
        1 == 1
        where:
        isPriceBookUseful | includeStaleData
        true              | true
        false             | true
        true              | false
    }

    def "buildPriceBookRangeSearchQuery"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        IObjectDescribe objectDescribe = new ObjectDescribe()
        IFieldDescribe fieldDescribe = new ObjectReferenceFieldDescribe()
        fieldDescribe.setApiName("price_book_id")
        List<LinkedHashMap> wheres = Lists.newArrayList();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("connector", Where.CONN.OR);
        List<IFilter> filters = Lists.newArrayList();
        IFilter productIdFilter = new Filter();
        productIdFilter.setFieldName(IObjectData.ID);
        productIdFilter.setOperator(Operator.IN);
        productIdFilter.setFieldValues(Lists.newArrayList("xxx"));
        filters.add(productIdFilter);
        map.put("filters", filters);
        wheres.add(map)
        fieldDescribe.setWheres(wheres as List<LinkedHashMap>)
        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe))
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(objectDescribe).when(serviceFacade, "findObject", any(), any())
        Map<String, List<ObjectDataDocument>> details = Maps.newHashMap()
        details.put("SalesOrderProductObj", Lists.newArrayList())
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        Whitebox.setInternalState(availableRangeUtils, "functionUtils", functionUtils)
        PowerMockito.doNothing().when(functionUtils, "fillFunctionFilter", any(), any(), any(), any(), any())
        when:
        availableRangeUtils.buildPriceBookRangeSearchQuery(user, getObjectData("_id", "object_describe_api_name"), details, rangeType, isActCurrentDetailRow)
        then:
        1 == 1
        where:
        rangeType | isActCurrentDetailRow
        "master"  | true
        "detail"  | true
        "master"  | false
        "detail"  | false
    }

    def "buildSubQuery"() {
        given:
        when:
        Whitebox.invokeMethod(availableRangeUtils, "buildSubQuery", Lists.newArrayList("123"))
        then:
        1 == 1
    }

    def "getStandardPriceBookProduct"() {
        given:
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(availableRangeUtils, "infraServiceFacade", infraServiceFacade)
        Whitebox.setInternalState(availableRangeUtils, "unitCoreService", unitCoreService)
        Whitebox.setInternalState(availableRangeUtils, "multiUnitService", multiUnitService)
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenMultiUnitPriceBook", any())
        PowerMockito.doReturn(getObjectDataList(["_id", "name"], 1)).when(serviceFacade, "findObjectDataByIds", any(), any(), any())
        PowerMockito.doReturn(getObjectDataList(["_id", "unit_id", "is_pricing", "conversion_ratio"], 1)).when(multiUnitService, "getMultiUnitDataByProductIds", any(), any())
        PowerMockito.doReturn("xxx").when(priceBookCommonService, "getStandardPriceBookId", any())
        PowerMockito.doReturn(getObjectDataList(["_id", "pricebook_id", "actual_unit"], 1)).when(priceBookCommonService, "getPriceBookProducts", any(), any(), any())
        PowerMockito.doReturn(new ObjectDescribe()).when(serviceFacade, "findObject", any(), any())
        when:
        Whitebox.invokeMethod(availableRangeUtils, "getStandardPriceBookProduct", user, "xxx", true)
        then:
        1 == 1
    }

    def "filterAvailableRangeMultiShoppingIds"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        user = new User("xxx", "xxx", "xxx", "xxx");
        Whitebox.setInternalState(availableRangeUtils, "multiShoppingMallProxy", multiShoppingMallProxy)
        MultiShoppingMallModel.MultiShoppingMallResult multiShoppingMallResult = new MultiShoppingMallModel.MultiShoppingMallResult();
        MultiShoppingMallModel.Result result = new MultiShoppingMallModel.Result();
        result.setAvailableRangeIds(Lists.newArrayList("xxx"))
        multiShoppingMallResult.setResult(result)
        PowerMockito.doReturn(multiShoppingMallResult).when(multiShoppingMallProxy, "getByLinkAppId", any(), any())
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isMultiShoppingMallEnabled", any())
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        when:
        Whitebox.invokeMethod(availableRangeUtils, "filterAvailableRangeMultiShoppingIds", user, getObjectDataList(["_id", "name"], 1))
        then:
        1 == 1
    }

    def "standardPriceBookEnsure"() {
        given:
        PowerMockito.mockStatic(GrayUtil.class)
        when:
        Whitebox.invokeMethod(availableRangeUtils, "standardPriceBookEnsure", user, false, "xxx", getObjectDataList(["_id", "product_id"], 1))
        then:
        1 == 1
    }

    def "priceBookProductUseful"() {
        given:
        when:
        Whitebox.invokeMethod(availableRangeUtils, "priceBookProductUseful", getObjectData("start_count", "end_count"), new BigDecimal("1"))
        then:
        1 == 1
    }

    def "isProcessRangeData"() {
        given:
        IObjectData objectData = getObjectData("_id")
        objectData.setDescribeApiName("PriceBookObj")
        Map<String, List<IObjectData>> detailObjectData = Maps.newHashMap()
        detailObjectData.put("xxx", getObjectDataList(["_id"], 1))
        AvailableConstants.UseRangeInfo oldRangeInfo = new AvailableConstants.UseRangeInfo()
        oldRangeInfo.setType("FIXED")
        AvailableConstants.UseRangeInfo rangeInfo = new AvailableConstants.UseRangeInfo()
        rangeInfo.setType("FIXED")
        Whitebox.setInternalState(availableRangeUtils, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isDhtMultiLevelOrder", any())
        when:
        Whitebox.invokeMethod(availableRangeUtils, "isProcessRangeData", user, objectData, oldRangeInfo, rangeInfo)
        then:
        1 == 1
    }

    def "processDetailRangeData"() {
        given:
        PowerMockito.mockStatic(SoCommonUtils.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(AppIdMapping.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
        PowerMockito.mockStatic(MtCurrentUtil.class)
        PowerMockito.mockStatic(ValidDateUtils.class)
        PowerMockito.mockStatic(DhtUtil.class)
        PowerMockito.mockStatic(RequestContextManager)
        def dbMasterData = getObjectData("_id")
        dbMasterData.set("account_range", "{\"type\":\"CONDITION\"}")
        def objectData = getObjectData("_id")
        objectData.set("account_range", "{\"type\":\"FIXED\"}")
        def objectDescribes = Maps.newHashMap()
        objectDescribes.put("AvailableRangeObj", new ObjectDescribe())
        def detailObjectData = Maps.newHashMap()
        detailObjectData.put("AvailableAccountObj", Lists.newArrayList(getObjectData("_id", "account_id")))
        def detailsToUpdate = Lists.newArrayList()
        def detailsToAdd = Lists.newArrayList()
        def tempAvailableRangeList = Lists.newArrayList()
        DoubleParamFunction<IObjectData, IObjectData> modifyObjectDataByDbData = { a, b -> }
        ThreeParamFunction<String, IObjectDescribe, List<IObjectData>> addMasterDetailFieldIntoDetailDataList = { a, b, c -> }
        ThreeParamFunction<IObjectData, String, List<IObjectData>> modifyDetailObjectDataToAddWhenEdit = { a, b, c -> }
        PowerMockito.when(SoCommonUtils.buildSearchTemplateQuery(anyInt())).thenReturn(new SearchTemplateQuery())
        Whitebox.setInternalState(availableRangeUtils, "serviceFacade", serviceFacade)
        QueryResult<IObjectData> queryResult = new QueryResult()
        queryResult.setData(getObjectDataList(["_id", "account_id"], 1))
        queryResult.setTotalNumber(1)
        PowerMockito.doReturn(queryResult).when(serviceFacade, "findBySearchQuery", any() as User, any() as String, any() as SearchTemplateQuery)
        when:
        Whitebox.invokeMethod(availableRangeUtils, "processDetailRangeData", dbMasterData, objectData, objectDescribes, detailObjectData, user, detailsToUpdate, detailsToAdd, tempAvailableRangeList, "AvailableAccountObj", "xxx", "account_range", modifyObjectDataByDbData, addMasterDetailFieldIntoDetailDataList, modifyDetailObjectDataToAddWhenEdit)
        then:
        1 == 1
    }

    def "addStandardPriceBook"() {
        given:
        def objectDataList = getObjectDataList(["_id"], 1)
        objectDataList.get(0).set("is_standard", false)
        Whitebox.setInternalState(availableRangeUtils, "priceBookCommonService", priceBookCommonService)
        def objectData = getObjectData("_id")
        objectData.set("active_status", "1")
        PowerMockito.doReturn(objectData).when(priceBookCommonService, "getStandardPriceBook", any())
        when:
        Whitebox.invokeMethod(availableRangeUtils, "addStandardPriceBook", user, objectDataList)
        then:
        1 == 1
    }

    def "multiUnitPriceBookConvert"() {
        given:
        def priorityPriceBookProductList = getObjectDataList(["_id", "product_id", "pricebook_id", "actual_unit"], 1)
        def multiUnitInfo = getObjectDataList(["_id", "product_id", "unit_id", "conversion_ratio"], 1)
        def unitIdAndNameMaps = Maps.newHashMap()
        unitIdAndNameMaps.put("xxx", "xxx")
        def pricingUnit = "xxx"
        def allPriceBookProductList = getObjectDataList(["_id", "product_id"], 1)
        when:
        Whitebox.invokeMethod(availableRangeUtils, "multiUnitPriceBookConvert", user, priorityPriceBookProductList, multiUnitInfo, unitIdAndNameMaps, pricingUnit, new BigDecimal("1"), allPriceBookProductList)
        then:
        1 == 1
    }

    def getObjectData(String... str) {
        IObjectData objectData = new ObjectData();
        for (String s : str) {
            if ("selling_data_define_type" == s) {
                objectData.set(s, "sys")
            } else if ("object_describe_api_name" == s) {
                objectData.set(s, "SalesOrderObj")
            } else if ("constraint_mode" == s) {
                objectData.set(s, "real_price")
            } else if ("record_type" == s) {
                objectData.set(s, "distribution__c")
            } else if ("product_range_control" == s) {
                objectData.set(s, true)
            } else if ("is_multiple_unit" == s) {
                objectData.set(s, true)
            } else if ("start_count" == s) {
                objectData.set(s, 1)
            } else if ("end_count" == s) {
                objectData.set(s, 100)
            } else {
                objectData.set(s, "xxx");
            }
        }
        return objectData
    }

    def getObjectDataDocument(String... str) {
        Map<String, Object> map = new HashMap<>()
        for (String s : str) {
            if (s == "object_describe_api_name") {
                map.put(s, "SalesOrderProductObj")
            } else {
                map.put(s, "xxx")
            }
        }
        def objectDataDocument = ObjectDataDocument.of(map)
        return objectDataDocument
    }

    def getObjectDataList(List<String> fieldList, int size) {
        def objectDataList = new ArrayList<ObjectData>();
        for (i in 0..<size) {
            def objectData = new ObjectData();
            fieldList.each {
                if ("start_count" == it) {
                    objectData.set(it, 0)
                } else if ("end_count" == it) {
                    objectData.set(it, 10)
                } else if ("priority" == it) {
                    objectData.set(it, 1)
                } else if ("last_modified_time" == it) {
                    objectData.set(it, 1253245353L)
                } else if ("is_pricing" == it) {
                    objectData.set(it, true)
                } else if ("conversion_ratio" == it) {
                    objectData.set(it, 1)
                } else {
                    objectData.set(it, "xxx")
                }
            }
            objectData.setTenantId(tenantId)
            objectDataList.add(objectData)
        }
        objectDataList
    }

    void initSpringContext() {
        def stubApplicationContext = Stub(ApplicationContext)
        def util = new SpringContextUtil()
        util.setApplicationContext(stubApplicationContext)
        Map<Class, Object> map = new HashMap<>()
        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
            map.computeIfAbsent(requiredType, { key ->
                Stub(requiredType)
            })
        }

        Map<String, Object> map2 = new HashMap<>()
        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
            map2.computeIfAbsent(name, { key ->
                Stub(requiredType)
            })
        }
    }

    def buildSearchQueryNoFilter() {
        SearchTemplateQuery query = new SearchTemplateQuery()
        query.setNeedReturnCountNum(false)
        query.setPermissionType(0)
        query.setNeedReturnQuote(false)
        query.setOffset(0)
        query.setLimit(1000)
        query
    }

    def buildSearchQuery() {
        def query = buildSearchQueryNoFilter()
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "is_deleted", true);
        query.setFilters(filters)

        List<IFilter> filters2 = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters2, "biz_status", "normal");
        query.setFilters(filters2)
        Wheres wheres1 = new Wheres();
        wheres1.setConnector(Where.CONN.OR.toString());
        wheres1.setFilters(filters2);
        query.setWheres([wheres1]);
        query
    }

}
