package com.facishare.crm.sfa.predefine.service.task


import com.facishare.crm.sfa.utilities.constant.ContractProgressConstants
import com.facishare.paas.I18N
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([I18N])
@SuppressStaticInitializationFor(["com.facishare.paas.I18N"])
class SaleContractTaskServiceTest extends Specification {

    @Shared
    private SaleContractTaskService saleContractTaskService
    @Mock
    @Shared
    private TaskService taskService

    def setupSpec() {
        MockitoAnnotations.initMocks(this)
        PowerMockito.mockStatic(I18N.class)
        saleContractTaskService = new SaleContractTaskService()
        Whitebox.setInternalState(saleContractTaskService, "taskService", taskService)
    }

    def setup() {

    }

    def "test createOrUpdateTask method"() {
        given:
        def tenantId = "90242"
        def goalCheckList = hasData ? [createMockObjectData()] : null
        def contractId = "00000000000000000001"

        when:
        PowerMockito.doNothing().when(taskService, "createOrUpdateTask", anyString(), anyString(), anyString(), any(Date.class), anyString());
        saleContractTaskService.createOrUpdateTask(tenantId, goalCheckList, contractId)

        then:
        noExceptionThrown()

        where:
        hasData     |   other
        false       |   ""
        true        |   ""
    }


    def "test deleteTask method"() {
        given:
        def tenantId = "90242"
        def dataList = hasData ? [createMockObjectData(), createMockObjectData()] : []

        when:
        PowerMockito.doNothing().when(taskService, "deleteTask", anyString(), anyString(), anyString())
        saleContractTaskService.deleteTask(tenantId, dataList)

        then:
        noExceptionThrown()

        where:
        hasData     |   other
        false       |   ""
        true        |   ""
    }


    private IObjectData createMockObjectData() {
        def objectData = new ObjectData()
        objectData.setId("test_id")
        objectData.set(ContractProgressConstants.RuleGoalCheck.RULE_GOAL_ID, "test_goal_id")
        objectData.set(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME, System.currentTimeMillis())
        return objectData
    }
} 