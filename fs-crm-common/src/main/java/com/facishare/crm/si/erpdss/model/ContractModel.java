package com.facishare.crm.si.erpdss.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2024-11-25
 */
public interface ContractModel {

    public static final String CONTRACT_FIELD_CONTRACT_NUMBER = "contract_number";
    public static final String CONTRACT_FIELD_OUTER_APP_CODE = "outer_app_code";

    @Data
    class DetailArg {
        private String id;
        private String apiName;
    }

    @Data
    @Builder
    class DetailResult {
        private ContractDetailResult detail;
        private CreateContractArg createArg;
        private boolean exist;
    }

    @Data
    @Builder
    class ContractDetailResult {
        private String name;
        private String id;
        private String previewUrl;
        private String downloadUrl;
        private String status;
        private String createTime;
    }

    @Data
    @Builder
    class CreateContractArg {
        private Map<String, Object> createData;
        private String createUrl;
    }


    @Data
    class UpdateArg {
        private String id;
        private String contractNumber;
        private String appCode;
    }

    @Data
    @Builder
    class UpdateResult {
        private boolean success;
        private String msg;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class RecordTypeMapping {
        private String apiName;
        private boolean isMain;
        private boolean changed;
        private List<RecordTypeMapping> children;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class CheckDataArg {
        private List recordTypes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class CheckDataResult {
       private Map<String, Boolean> result;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class SetProgressGoalArg {
        private long toCompleteTime;
        private String contractId;
        private List<ProgressGoalArg> progressGoalList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class SetProgressGoalResult {
        private boolean success;
        private String msg;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ProgressGoalArg {
        private String ruleId;
        private String productId;
        private BigDecimal goalValue;

        private List<ProgressGoalCheckArg> checkList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ProgressGoalCheckArg {
        private String ruleGoldId;
        private long toCheckTime;
        private BigDecimal goalValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class GetProgressGoalArg {
        private String contractId;
    }

    class GetProgressGoalResult {
        private long toCompleteTime;
        private String contractId;
        private List<ProgressGoalResult> progressGoalList;
    }

    class ProgressGoalResult {
        private String ruleId;
        private String ruleName;

    }
}
