package com.facishare.crm.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024-06-29 14:56
 * @Description
 */
public interface ChannelRpcModel {

    @Data
    class SettingArg {
        private String dataId;
        private String objectApiName;
        private String reminderTrigger;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CreateLinkContactArg {
        private String dataId;
        private String objectApiName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CreateAgreementDetailArg {
        private String dataId;
        private String objectApiName;
    }

    @Data
    class ReminderCacheArg {
        private List<ChannelManagementDTO.ReminderCache> reminderCacheList;
    }

    @Data
    class ApprovalNoticeArg {
        private String bizScope;
        private String notifyVia;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ApprovalNoticeAggregator {
        private String approvalNoticeId;
        private String notifyVia;
        private String aplApiName;
        private String actionVia;
        private String sender;
        private List<String> receiver;
        private PartnerChannelManage.PrmEmail passEmail;
        private PartnerChannelManage.PrmEmail nonPassEmail;
        private PrmManagementModel.ShortMessage passSms;
        private PrmManagementModel.ShortMessage nonPassSms;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ApprovalNoticeAggregatorResult {
        @Builder.Default
        private Boolean effective = Boolean.TRUE;
        private String signSchemeId;
        private List<String> activateRoles;
        private List<ApprovalNoticeAggregator> approvalNoticeAggregators;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignReminderInfoResult {
        @Builder.Default
        private Boolean effective = Boolean.TRUE;
        private String signTimeUnit;
        private String reminderTrigger;
        private Integer signTime;
        private String scheduleType;
        private Integer fixedMonth;
        private Integer fixedDay;
        private String agreementStartMode;
        private String agreementEndMode;
        private String customStartDate;
        private String customEndDate;
        private String startDate;
        private String renewalCycleUnit;
        private Integer renewalCycleTime;
        private Integer planDuration;
        private String planDurationUnit;
        private List<PartnerChannelManage.ReminderType> reminderTypes;
        private PartnerChannelManage.ExpireReminderPersonView expireReminderPersonView;
    }
}
