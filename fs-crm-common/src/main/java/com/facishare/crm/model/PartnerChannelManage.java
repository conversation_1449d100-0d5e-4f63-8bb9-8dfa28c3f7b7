package com.facishare.crm.model;

import com.facishare.crm.enums.*;
import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.Diff;
import com.facishare.crm.platform.annotation.Mappable;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.google.common.collect.Lists;
import lombok.*;

import java.util.*;

/**
 * <AUTHOR>
 * @time 2023-09-22 16:21
 * @Description
 */
public interface PartnerChannelManage {

    @Data
    class NoticeConfigArg {
        private String configId = "";
        private List<String> smsPhones = Lists.newArrayList();
        private SmsContent smsPassContent;
        private SmsContent smsFailContent;
        private List<String> noticeTypes;
    }

    @Data
    class SmsContent {
        private String content = "";
        private String templateId = "";
        private List<ContentParam> smsContentParam = Lists.newArrayList();
    }

    @Data
    class ContentParamMapping {
        private List<ContentParam> passParam;
        private List<ContentParam> failParam;
    }

    @Data
    class ContentParam {
        private String key;
        private String type;
        private String value;
    }

    @Data
    class NoticeConfigResult {
        private String configId;
        private String smsPhone;
        private SmsContent smsPassContent;
        private SmsContent smsFailContent;
        private List<String> noticeTypes;
    }

    @Data
    class DefaultRoleArg {
        private String configId;
        private List<String> roleCodes;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    class SpecifyRoleResult {
        private String configId;
        private List<String> roleCodes;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    class DefaultRoleResult {
        private String configId;
        private String roleCode;
        private String roleName;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    class RoleResult {
        private String configId;
        private List<DefaultRoleResult> roles;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    class Result {
        private String configId = "";
        @Builder.Default
        private List<String> smsPhones = Lists.newArrayList();
        private SmsContent smsPassContent;
        private SmsContent smsFailContent;
        @Builder.Default
        private List<String> defaultRoles = Lists.newArrayList();
        @Builder.Default
        private List<String> noticeTypes = Lists.newArrayList();
        private String owner;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    class RegistrationApprovalResult {
        @Builder.Default
        private String conditionType = "ALL";
        @Builder.Default
        private List<ApprovalNotice> approvalNotices = Lists.newArrayList();
        @Builder.Default
        private CustomText customText = CustomText.builder().build();
        @Builder.Default
        private List<EnterpriseActivationSetting> enterpriseActivationSettings = Lists.newArrayList();
    }

    @Data
    class CreateDataResult {
        private String error;
        private String dataId;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class EnterpriseActivationSettingArg {
        @Builder.Default
        private String conditionType = ConditionType.ALL.getType();
        @Builder.Default
        private CustomText customText = CustomText.builder().build();
        private List<EnterpriseActivationSetting> enterpriseActivationSettings;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ApprovalNotice {
        private String approvalNoticeId;
        private String notifyVia;
        private String aplApiName;
        private String actionVia;
        private String sender;
        private List<String> receiver;
        private List<String> noticeIds;
        @Builder.Default
        private Boolean enabled = false;
        private String bizScope;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class EnterpriseActivationSetting {
        private String enterpriseActivationSettingId;
        private String condition;
        private String conditionType;
        private List<String> defaultRoles;
        private String enterpriseType;
        private String recyclingMode;
        private Integer expireDays;
        private Integer expireCycleMonth;
        private Integer expireCycleDay;
        private Integer priority;
        @Builder.Default
        private Boolean effective = Boolean.TRUE;
    }

    @Data
    @Builder
    class SwitchScopeArg {
        private SwitchItem switchItem;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SwitchItem {
        private String approvalNoticeId;
        private String notifyVia;
        @Builder.Default
        private Boolean enabled = false;
        private String bizScope;
    }

    @Data
    @Builder
    class SwitchStatusResult {
        private SwitchItem switchItem;
    }

    @Data
    class BaseApprovalNoticeInstance {
        private String approvalNoticeId;
        private String notifyVia;
        private List<String> noticeIds;
        private String aplApiName;
        private String actionVia;
        private String bizScope;
        private String sender;
        private List<String> receivers;
    }

    @Data
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class ApprovalNoticeSmsInstanceArg extends BaseApprovalNoticeInstance {
        @Builder.Default
        private List<String> receiver = Lists.newArrayList();
        private PrmManagementModel.ShortMessage passSms;
        private PrmManagementModel.ShortMessage nonPassSms;
    }

    @Data
    @Builder
    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class ApprovalNoticeEmailInstanceArg extends BaseApprovalNoticeInstance {
        @Builder.Default
        private List<String> receiver = Lists.newArrayList();
        private String sender;
        private PrmEmail passEmail;
        private PrmEmail nonPassEmail;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class NoticeAplArg {
        private String approvalNoticeId;
        private String notifyVia;
        private String aplApiName;
        private String bizScope;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class PrmEmail {
        private String emailId;
        private String emailType;
        private String templateId;
        private String objectApiName;
        private String category;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class NoticeInstanceArg {
        private String approvalNoticeId;
        private String notifyVia;
    }

    @Data
    @Builder
    class NoticeInstanceResult {
        private String approvalNoticeId;
        private String aplApiName;
        private String notifyVia;
        private String bizScope;
        private List<String> receiver;
        private String sender;
        private PrmEmail passEmail;
        private PrmEmail nonPassEmail;
        private PrmManagementModel.ShortMessage passSms;
        private PrmManagementModel.ShortMessage nonPassSms;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ProvisionScheme implements MatchRulePriority {
        private String provisionSchemeId;
        private String schemeName;
        @Builder.Default
        private Boolean mustRead = true;
        @Builder.Default
        private List<String> provisionIds = Lists.newArrayList();
        private String conditionType;
        private String condition;
        private String aplApiName;
        private Integer priority;
        /**
         * provisionId,provisionName
         */
        private List<Map<String, String>> provisionsNameMappingList;

        @Override
        public ConditionType getConditionTypeEnum() {
            return ConditionType.fromString(getConditionType());
        }

        @Override
        public String getMatch() {
            return null;
        }

        @Override
        public List<String> getMatchList() {
            return getProvisionIds();
        }

        @Override
        public String getMatchSchemeId() {
            return getProvisionSchemeId();
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ProvisionSchemeArg {
        private List<ProvisionScheme> provisionScheme;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ProvisionSchemeResult {
        @Builder.Default
        private List<ProvisionScheme> provisionScheme = Lists.newArrayList();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignSchemeResult {
        @Builder.Default
        private List<SignScheme> signScheme = Lists.newArrayList();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignSchemeResponse<T> {
        private T data;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SimpleSignSchemeResult {
        @Builder.Default
        private List<SimpleSignScheme> signSchemes = Lists.newArrayList();
    }

    @Data
    @Convertible
    class SimpleSignScheme {
        @Mappable(fieldApiName = "_id")
        private String signSchemeId;
        private String schemeName;
        private String agreementName;
        private String agreementId;
        private String condition;
        private String conditionType;
        private Integer priority;
        private String signMode;
        private String scheduleType;
        private Boolean pushed;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignSchemeDetail {
        private SignScheme signScheme;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class PushSignSchemeResult {
        @Builder.Default
        private Boolean toggleSuccess = true;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class DeleteSignSchemeResult {
        @Builder.Default
        private Boolean deleteSuccess = true;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignSchemeArg {
        private String bizScope;
        private SignScheme signScheme;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignSchemeListArg {
        private String bizScope;
        private List<SignScheme> signScheme;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class PushSignSchemeArg {
        private String signSchemeId;
        @Builder.Default
        private Boolean pushed = false;
    }

    @Data
    @Builder
    class ChangeStatusMessage {
        private String signSchemeId;
        private String tenantId;
        private String operation;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class OpSignSchemeArg {
        private String signSchemeId;
    }

    /**
     * 固定日续约
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Convertible
    class FixedDateSignScheme extends BaseSignScheme {
        /**
         * 下一次续约日期，例如：12-01，12月1日。
         */
        private String nextRenewalDate;
        /**
         * 到期提醒方式，值对应 {@link ReminderTrigger}
         */
        private String reminderTrigger;
        /**
         * 当 {@link #scheduleType} 选择 {@link ScheduleType#CYCLE} 或者 {@link ScheduleType#FIXED_DATE} 时
         * 提醒方式
         */
        private ExpireReminderTypeView expireReminderTypeView;
        /**
         * 当 {@link #scheduleType} 选择 {@link ScheduleType#CYCLE} 或者 {@link ScheduleType#FIXED_DATE} 时
         * 提醒成员
         */
        private ExpireReminderPersonView expireReminderPersonView;
        /**
         * 续约窗口开始时间
         */
        private Integer renewalWindowStart;
        /**
         * 续约窗口结束时间
         */
        private Integer renewalWindowEnd;
        /**
         * 续约页面展示内容
         */
        private List<String> renewalPage;
    }

    /**
     * 周期续约
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Convertible
    class CycleSignScheme extends BaseSignScheme {
        /**
         * 续约周期时间值
         */
        private Integer renewalCycleTime;
        /**
         * 续约周期时间单位
         */
        private String renewalCycleUnit;
        /**
         * 到期提醒方式，值对应 {@link ReminderTrigger}
         */
        private String reminderTrigger;
        /**
         * 当 {@link #scheduleType} 选择 {@link ScheduleType#CYCLE} 或者 {@link ScheduleType#FIXED_DATE} 时
         * 提醒方式
         */
        private ExpireReminderTypeView expireReminderTypeView;
        /**
         * 当 {@link #scheduleType} 选择 {@link ScheduleType#CYCLE} 或者 {@link ScheduleType#FIXED_DATE} 时
         * 提醒成员
         */
        private ExpireReminderPersonView expireReminderPersonView;
        /**
         * 续约窗口开始时间
         */
        private Integer renewalWindowStart;
        /**
         * 续约窗口结束时间
         */
        private Integer renewalWindowEnd;
        /**
         * 续约页面展示内容
         */
        private List<String> renewalPage;

    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Convertible
    class OneTimeSignScheme extends BaseSignScheme {

    }

    @Data
    class BaseSignScheme {
        /**
         * 方案Id
         */
        protected String signSchemeId;
        /**
         * 方案名称
         */
        protected String schemeName;
        /**
         * 协议名称
         */
        protected String agreementName;
        /**
         * 协议Id
         */
        protected String agreementId;
        /**
         * 使用范围具体条件
         */
        protected String condition;
        /**
         * {@link ConditionType}
         * 使用范围类型
         */
        protected String conditionType;
        /**
         * 模版优先级，数字越大，优先级越高。
         */
        protected Integer priority;
        /**
         * {@link SignModel}
         * 签署方式
         */
        protected String signMode;
        /**
         * {@link ScheduleType}
         * 签署类型
         */
        protected String scheduleType;
        /**
         * 协议期限时间值
         */
        protected Integer planDuration;
        /**
         * 协议期限时间单位
         */
        protected String planDurationUnit;
        /**
         * 协议开始日期
         * eg:05-01
         */
        protected String startDate;
        /**
         * 审批提醒数据
         */
        protected List<SignApprovalNotice> approvalNotices;
        /**
         * 角色开通
         */
        protected List<String> activateRoles;
        private Boolean pushed;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.ALWAYS)
    class SignScheme implements MatchRulePriority {
        /**
         * 签约方案 id
         */
        private String signSchemeId;
        /**
         * 签约方案标题
         */
        private String schemeName;
        /**
         * 伙伴协议 id
         */
        private String agreementId;
        /**
         * 伙伴协议 name
         */
        private String agreementName;
        /**
         * 适用范围 json
         */
        private String condition;
        /**
         * 适用范围类型，值对应 {@link ConditionType}
         */
        private String conditionType;
        /**
         * 签署方式，值对应 {@link SignModel}
         */
        private String signMode;
        /**
         * APL 函数的 ApiName
         */
        private String aplApiName;
        /**
         * 签署类型，值对应 {@link ScheduleType}
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private String scheduleType = ScheduleType.CYCLE.getType();
        /**
         * 到期提醒方式，值对应 {@link ReminderTrigger}
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private String reminderTrigger = ReminderTrigger.AUTO.getTrigger();
        /**
         * 当 {@link #scheduleType} 选择 {@link ScheduleType#CYCLE} 或者 {@link ScheduleType#FIXED_DATE} 时
         * 提醒方式
         */
        private ExpireReminderTypeView expireReminderTypeView;
        /**
         * 当 {@link #scheduleType} 选择 {@link ScheduleType#CYCLE} 或者 {@link ScheduleType#FIXED_DATE} 时
         * 提醒成员
         */
        private ExpireReminderPersonView expireReminderPersonView;
        /**
         * 角色开通
         */
        private List<String> activateRoles;
        /**
         * 审核通知渠道，具体提醒id
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private List<String> noticeIds = Lists.newArrayList();
        /**
         * 审核通知渠道
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private List<ApprovalNotice> approvalNoticeList = Lists.newArrayList();
        /**
         * 具体提醒信息
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private List<SignApprovalNotice> approvalNotices = Lists.newArrayList();
        /**
         * 权重，数字越大，权重越大。
         */
        private Integer priority;
        /**
         * 协议开始日期，例如：12-01，12月1日。
         */
        private String startDate;
        /**
         * 协议期限时间值
         */
        private Integer planDuration;
        /**
         * 协议期限时间单位
         */
        private String planDurationUnit;
        /**
         * 下一次续约日期，例如：12-01，12月1日。
         */
        private String nextRenewalDate;
        /**
         * 续约周期时间
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private Integer renewalCycleTime;
        /**
         * 续约周期时间单位
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private String renewalCycleUnit;
        /**
         * 续约窗口开始时间
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private Integer renewalWindowStart;
        /**
         * 续约窗口结束时间
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private Integer renewalWindowEnd;
        /**
         * 续约窗口时间单位
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private String renewalWindowUnit;
        /**
         * 续约页面
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private List<String> renewalPage = Lists.newArrayList();
        /**
         * 续约逾期文案
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private String overdueMessage;
        /**
         * 逾期变量多语言内容
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private String varOverdueMessage;
        /**
         * 是否启用
         */
        @JsonSetter(nulls = Nulls.SKIP)
        private Boolean pushed = false;


        @JsonIgnore
        @Override
        public ConditionType getConditionTypeEnum() {
            return ConditionType.fromString(getConditionType());
        }

        @JsonIgnore
        @Override
        public String getMatch() {
            return getAgreementId();
        }

        @JsonIgnore
        @Override
        public List<String> getMatchList() {
            return Collections.emptyList();
        }

        @JsonIgnore
        @Override
        public String getMatchSchemeId() {
            return getSignSchemeId();
        }

        public SignModel getSignModelEnum() {
            return SignModel.fromString(getSignMode());
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ExpireReminderTypeView {
        private ExpireReminderType autoExpireReminderType;
        private ExpireReminderType manualExpireReminderType;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ExpireReminderType {
        private ReminderType prmAlertWindowReminder;
        private ReminderType smsReminder;
        private ReminderType emailReminder;
        private ReminderType crmReminder;
        private ReminderType prmCrmReminder;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.ALWAYS)
    class ReminderType {
        private String reminderTypeId;
        @JsonSetter(nulls = Nulls.SKIP)
        private String reminderTrigger = ReminderTrigger.AUTO.getTrigger();
        private String signSchemeId;
        private String reminderMethod;
        @JsonSetter(nulls = Nulls.SKIP)
        private String timeUnit = "month";
        @JsonSetter(nulls = Nulls.SKIP)
        private Integer reminderTime = 0;
        private String templateId;
        // ${协议标题}${几天}后到期
        // xxx ${var_message} xxx
        private String message;
        // 固定 ${var_message}
        private String varMessage;
        @JsonSetter(nulls = Nulls.SKIP)
        private Boolean activated = false;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ExpireReminderPersonView {
        @Builder.Default
        private List<ReminderPerson> internalPerson = Lists.newArrayList();
        @Builder.Default
        private List<ReminderPerson> externalPerson = Lists.newArrayList();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ReminderPerson {
        private String reminderPersonId;
        private String identity;
        private String memberType;
        private String signSchemeId;
        private String dataId;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignApprovalNotice {
        private String approvalNoticeId;
        private String notifyVia;
        private String aplApiName;
        private String bizScope;
        private String sender;
        private List<String> receiver;
        @Builder.Default
        private Boolean enabled = false;
        private PrmEmail passEmail;
        private PrmEmail nonPassEmail;
        private PrmManagementModel.ShortMessage passSms;
        private PrmManagementModel.ShortMessage nonPassSms;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class QualificationSchemeResult {
        @Builder.Default
        private List<QualificationScheme> qualificationScheme = Lists.newArrayList();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class QualificationSchemeArg {
        @Builder.Default
        private List<QualificationScheme> qualificationScheme = Lists.newArrayList();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class QualificationScheme implements MatchRulePriority {
        private String qualificationSchemeId;
        private String layoutApiName;
        private String mappingRecordType;
        private String condition;
        private String conditionType;
        private String aplApiName;
        @Builder.Default
        private Integer priority = 1;

        @Override
        public ConditionType getConditionTypeEnum() {
            return ConditionType.fromString(getConditionType());
        }

        @Override
        public String getMatch() {
            return getLayoutApiName();
        }

        @Override
        public List<String> getMatchList() {
            return Collections.emptyList();
        }

        @Override
        public String getMatchSchemeId() {
            return getQualificationSchemeId();
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ActivationSetting {
        private String tenantId;
        @Builder.Default
        private Set<String> deleteIds = new HashSet<>();
        @Builder.Default
        private Map<String, IObjectData> updateDataMapping = new HashMap<>();
        @Builder.Default
        private Map<String, IObjectData> originalDataMapping = new HashMap<>();
        // 需要更新激活 task 的 id，意味着回收时间或模式进行了变更
        @Builder.Default
        private Set<String> updateTaskIds = new HashSet<>();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Field {
        private String objectDescribeApiName;
        private String apiName;
        private String label;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ExpireReminderTask {
        private String tenantId;
        private Long outTenantId;
        private String dataId;
        private String objectApiName;
        private String signSchemeId;
        private String reminderMethod;
        private Date expireDate;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class RenewalSuccessful {
        private String tenantId;
        private Long outTenantId;
        private String userId;
        private String objectDataId;
        private String objectApiName;
        private String partnerAgreementDetailId;
        private String signSchemeId;
        private String language;
        private Boolean pass;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.ALWAYS)
    class CustomTextData {
        private CustomText signingText;
        private CustomText renewalText;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class CustomText {
        private String title;
        private String content;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class AdmissionData {
        private String objectDescribeApiName;
        private String dataId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.ALWAYS)
    @Diff
    class AdmissionConfig {
        private String channelAccess;
        private String channelMode;
        private String applyToApp;
        private String relatedObjectApiName;
        @Builder.Default
        private List<String> applyModules = Lists.newArrayList();
    }
}
