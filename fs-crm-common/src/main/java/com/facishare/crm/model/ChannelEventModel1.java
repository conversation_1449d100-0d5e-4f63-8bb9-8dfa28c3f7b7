package com.facishare.crm.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-02-26
 * ============================================================
 */
public interface ChannelEventModel1 {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignApprovalEvent {
        private String tenantId;
        private String approvalOperator;
        private String objectApiName;
        private String dataId;
        private String approvalStatus;
        private String app;
    }
}
