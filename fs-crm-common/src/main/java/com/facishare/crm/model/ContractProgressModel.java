package com.facishare.crm.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @描述说明：
 * 
 * @作者：chench
 * @创建日期：2025-04-17
 */
public interface ContractProgressModel {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class RuleModel {
        @JsonProperty("_id")
        @SerializedName("_id")
        private String id;

        private String name;
        private String description;
        @JsonProperty("enabled_status")
        @SerializedName("enabled_status")
        private boolean enabledStatus;

        @JsonProperty("contract_record_type")
        @SerializedName("contract_record_type") 
        private List<String> contractRecordType;

        @JsonProperty("index_type")
        @SerializedName("index_type")
        private String indexType;

        @JsonProperty("index_type_object")
        @SerializedName("index_type_object")
        private String indexTypeObject;

        @JsonProperty("index_type_object_field")
        @SerializedName("index_type_object_field")
        private String indexTypeObjectField;

        @JsonProperty("index_type_object_field_value")
        @SerializedName("index_type_object_field_value")
        private String indexTypeObjectFieldValue;

        @JsonProperty("index_type_category")
        @SerializedName("index_type_category")
        private String indexTypeCategory;

        @JsonProperty("index_type_calc_type")
        @SerializedName("index_type_calc_type")
        private String indexTypeCalcType;

        @JsonProperty("index_goal_object")
        @SerializedName("index_goal_object")
        private String indexGoalObject;

        @JsonProperty("index_goal_object_field")
        @SerializedName("index_goal_object_field")
        private String indexGoalObjectField;

        @JsonProperty("index_goal_data_object")
        @SerializedName("index_goal_data_object")
        private String indexGoalDataObject;

        @JsonProperty("index_goal_data_direct_object")
        @SerializedName("index_goal_data_direct_object")
        private String indexGoalDataDirectObject;

        @JsonProperty("index_goal_data_object_field")
        @SerializedName("index_goal_data_object_field")
        private String indexGoalDataObjectField;

        @JsonProperty("index_goal_data_ref_contract_field")
        @SerializedName("index_goal_data_ref_contract_field")
        private String indexGoalDataRefContractField;

        @JsonProperty("index_goal_data_ref_product_field")
        @SerializedName("index_goal_data_ref_product_field")
        private String indexGoalDataRefProductField;

        @JsonProperty("index_goal_data_ref_time_field")
        @SerializedName("index_goal_data_ref_time_field")
        private String indexGoalDataRefTimeField;

        @JsonProperty("index_goal_data_condition")
        @SerializedName("index_goal_data_condition")
        private String indexGoalDataCondition;
    }


    
    
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class RuleListModel {
        @JsonProperty("object_describe_api_name")
        @SerializedName("object_describe_api_name")
        private String objectDescribeApiName;

        @JsonProperty("search_query_info")
        @SerializedName("search_query_info")
        private String searchQueryInfo;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ViewGoalModel {
        @JsonProperty("contract_id")
        @SerializedName("contract_id")
        private String contractId;

        @JsonProperty("need_calculate")
        @SerializedName("need_calculate")
        private boolean needCalculate;

        @JsonProperty("include_check")
        @SerializedName("include_check")
        private boolean includeCheck;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class RuleGoalModel {
        @JsonProperty("to_complete_time")
        @SerializedName("to_complete_time")
        private long toCompleteTime;

        @JsonProperty("contract_id")
        @SerializedName("contract_id")
        private String contractId;

        @JsonProperty("progress_goal_list")
        @SerializedName("progress_goal_list")
        private List<ProgressGoalArg> progressGoalList;
    }

    @Data
    class ProgressGoalArg {
        @JsonProperty("_id")
        @SerializedName("_id")
        private String id;

        @JsonProperty("rule_id")
        @SerializedName("rule_id")
        private String ruleId;

        @JsonProperty("product_id")
        @SerializedName("product_id")
        private String productId;

        @JsonProperty("goal_value")
        @SerializedName("goal_value")
        private BigDecimal goalValue;

        @JsonProperty("check_list")
        @SerializedName("check_list")
        private List<RuleGoalCheckArg> checkList;
    }
    @Data
    class RuleGoalCheckArg {
        @JsonProperty("_id")
        @SerializedName("_id")
        private String id;

        @JsonProperty("rule_goal_id")
        @SerializedName("rule_goal_id")
        private String ruleGoldId;

        @JsonProperty("to_check_time")
        @SerializedName("to_check_time")
        private long toCheckTime;

        @JsonProperty("goal_value")
        @SerializedName("goal_value")
        private BigDecimal goalValue;

        @JsonProperty("is_checked")
        @SerializedName("is_checked")
        private boolean isChecked;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class RuleGoalCheckModel {
        @JsonProperty("rule_goal_id")
        @SerializedName("rule_goal_id")
        private String ruleGoalId;

        @JsonProperty("to_check_time")
        @SerializedName("to_check_time")
        private long toCheckTime;

        @JsonProperty("goal_value")
        @SerializedName("goal_value")
        private BigDecimal goalValue;

        @JsonProperty("is_checked")
        @SerializedName("is_checked")
        private boolean isChecked;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class RuleGoalSnapshotModel {
        @JsonProperty("rule_goal_id")
        @SerializedName("rule_goal_id")
        private String ruleGoalId;

        @JsonProperty("current_value")
        @SerializedName("current_value")
        private BigDecimal currentValue;

        @JsonProperty("goal_value")
        @SerializedName("goal_value")
        private BigDecimal goalValue;

        @JsonProperty("check_type")
        @SerializedName("check_type")
        private String checkType;
    }

    @Data
    @Builder
    class Result {
        private String msg;
        private boolean success;
        private Object data;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class SnapshotArg {

        private String tenantId;

        private String ruleGoalId;

        private String contractId;

        private Long toCheckTime;

        private String checkPointId;
    }

    @Data
    @Builder
    class SnapshotResult {
        private int errCode;
        private String errMessage;
    }
}
