package com.facishare.crm.model;

import com.facishare.paas.metadata.api.search.IFilter;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface ShopCategoryModel {
    @Data
    @Builder
    class SubCategoryArg {
        private String storeId;
        private String pid;
    }

    @Data
    class SubCategoryResult {
        private int code;
        private String message ;
        private ResultData data;

        public boolean isSuccess() {
            return code == 0;
        }
    }

    @Data
    class ResultData {
        private List<SubCategory> shopCategoryList;
    }
    @Data
    class SubCategory {
        private String id;
        private String name;
    }

    @Data
    @Builder
    class HandleMallCategoryArg {
        private List<IFilter> filters;
    }

    @Data
    class HandleMallCategoryResult {
        private int code;
        private String message ;
        private HandleMallCategoryData data;

        public boolean isSuccess() {
            return code == 0;
        }
    }

    @Data
    class HandleMallCategoryData {
        private List<IFilter> filters;
    }
}
