package com.facishare.crm.sfa.predefine.service.pricepolicy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.CommonProductConstants;
import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.PricePolicyValidator;
import com.facishare.crm.sfa.predefine.service.MutipleUnitToolService;
import com.facishare.crm.sfa.predefine.service.ProductCategoryBizIntService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.MutipleUnitInfo;
import com.facishare.crm.sfa.predefine.service.pricepolicy.model.*;
import com.facishare.crm.sfa.predefine.service.pricepolicy.save.GiftCheckManager;
import com.facishare.crm.sfa.predefine.service.pricepolicy.save.GiftSaveCheckService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.BigDecimalUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.DmDefineConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.PeriodicProductDm;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.util.DomainPluginDescribeExt;
import com.facishare.crm.util.MtCurrentUtil;
import com.facishare.crm.util.SearchTemplateQueryPlus;
import com.facishare.enterprise.common.util.BeanUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ProductCategoryService;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.NumberFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants.*;

@Component
public class GiftRuleService extends AbstractPolicyRuleService<PriceRule.GiftRule> {
    @Autowired
    private MultiUnitServiceImpl multiUnitService;
    @Autowired
    protected BizConfigThreadLocalCacheService configService;
    @Autowired
    private PricePolicyCoreServiceImpl pricePolicyCoreService;
    @Autowired
    MutipleUnitToolService mutipleUnitToolService;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private ProductCategoryService productService;
    @Autowired
    private ProductCategoryBizIntService productCategoryBizService;
    @Autowired
    private PercentileGiftExecute percentileGiftExecute;
    @Autowired
    private GiftCheckManager giftCheckManager;
    @Autowired
    private AvailableProductStatusService availableProductStatusService;

    @Override
    public String getRuleType() {
        return PricePolicyRuleType.GIFT.getRuleType();
    }

    @Override
    public PriceRule.GiftRule parseRule(String text) {
        return JSON.parseObject(text, PriceRule.GiftRule.class);
    }

    @Override
    protected Object validateExecutionResult(String executionResultStr, Map<String, Set<String>> objectFields,
            Set<String> productIds, Set<String> aggregateValueIds, Map<String, Set<String>> currentRuleObjectFields,
            Set<String> currentRuleAggregateValueIds) {
        PriceRule.GiftRule giftRule = parseRule(executionResultStr);
        giftRule.setRuleType(getRuleType());
        if (null == giftRule.getGiftList()) {
            giftRule.setGiftList(Lists.newArrayList());
        }
        if (PriceRule.GiftType.FIX.equals(giftRule.getType()) && CollectionUtils.empty(giftRule.getGiftList())) {
            throw new ValidateException(I18N.text("price_policy.gift_can_not_be_empty"));
        }
        if (giftRule.getGiftList().size() > 100) {
            throw new ValidateException(String.format(I18N.text("price_policy.gift_over_max_count"), 100));
        }
        validatePercentileGift(giftRule, objectFields, aggregateValueIds);
        for (PriceRule.GiftLine giftLine : giftRule.getGiftList()) {
            if (StringUtils.isNotBlank(giftLine.getProductId())) {
                productIds.add(giftLine.getProductId());
            }
            if (null != giftLine.getMinValue()) {
                if (giftLine.getMinValue().compareTo(BigDecimal.ZERO) < 0) {
                    throw new ValidateException(String.format(
                            I18N.text("sfa.ceiling_price_less_than_sales_price"),
                            I18N.text("sfa.price_policy.gift.min_value"),
                            "0"));
                }
                if (null != giftLine.getMaxValue() && giftLine.getMaxValue().compareTo(giftLine.getMinValue()) < 0) {
                    throw new ValidateException(String.format(
                            I18N.text("sfa.ceiling_price_less_than_sales_price"),
                            I18N.text("sfa.price_policy.gift.max_value"),
                            I18N.text("sfa.price_policy.gift.min_value")));
                }
            }
        }
        if (PriceRule.GiftType.FIX.equals(giftRule.getType())) {
            giftRule.setGiftCondition(null);
        }
        processUsedFields(giftRule.getObjectApiName(), giftRule.getCondition(), objectFields, aggregateValueIds);
        processCycleFields(giftRule.getCycleInfos(), objectFields, aggregateValueIds);
        processUsedFields(SFAPreDefine.Product.getApiName(), giftRule.getGiftCondition(), objectFields,
                aggregateValueIds);
        return giftRule;
    }

    private void validateFixedAttribute(PriceRule.GiftLine giftLine, Set<String> fixedAttributeApiName) {
        Map<String, Object> attributeMap = giftLine.getAttributeMap();
        attributeMap = attributeMap == null ? Maps.newHashMap() : attributeMap;
        attributeMap.forEach((apiName, value) -> {
            if (!fixedAttributeApiName.contains(apiName)) {
                throw new ValidateException(
                        I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_GIFT_FIXED_ATTRIBUTE_NOT_IN_CONFIG, apiName));
            }
        });

    }

    private Set<String> getFixedAttributeApiName(String objectApiName, String fixedAttributeJSON) {
        Set<String> fixedAttributeApiName = Sets.newHashSet();

        if (StringUtils.isBlank(fixedAttributeJSON)) {
            return fixedAttributeApiName;
        }
        Map<String, Map<String, List<String>>> masterApiNameToDetailApiNameToFieldApiNames = ExceptionUtils
                .trySupplier(() -> JSON.parseObject(fixedAttributeJSON, Map.class));
        if (MapUtils.isEmpty(masterApiNameToDetailApiNameToFieldApiNames)) {
            return fixedAttributeApiName;
        }
        Map<String, List<String>> detailApiNameToFieldApiNames = masterApiNameToDetailApiNameToFieldApiNames
                .get(objectApiName);
        if (detailApiNameToFieldApiNames != null) {
            detailApiNameToFieldApiNames.forEach((detailApiName, fieldApiNames) -> {
                if (CollectionUtils.notEmpty(fieldApiNames)) {
                    fixedAttributeApiName.addAll(fieldApiNames);
                }
            });
            return fixedAttributeApiName;
        }
        for (Map.Entry<String, Map<String, List<String>>> mapEntry : masterApiNameToDetailApiNameToFieldApiNames
                .entrySet()) {
            Map<String, List<String>> detailApiNameToFieldApiNamesMap = mapEntry.getValue();
            List<String> detailFieldApiNameList = detailApiNameToFieldApiNamesMap.get(objectApiName);
            if (CollectionUtils.notEmpty(detailFieldApiNameList)) {
                fixedAttributeApiName.addAll(detailFieldApiNameList);
                break;
            }
        }
        return fixedAttributeApiName;
    }

    private void validatePercentileGift(PriceRule.GiftRule giftRule, Map<String, Set<String>> objectFields,
            Set<String> aggregateValueIds) {
        if (!configService.isOpenPricePolicyPercentile(RequestContextManager.getContext().getTenantId())) {
            return;
        }
        validateCyclePercentileInOnlyOne(giftRule);
        processPercentileFields(giftRule.getPercentileInfos(), objectFields, aggregateValueIds);

    }

    private void validateCyclePercentileInOnlyOne(PriceRule.GiftRule giftRule) {
        // 判断每满和比例不能同时存在
        if (CollectionUtils.notEmpty(giftRule.getCycleInfos())
                && CollectionUtils.notEmpty(giftRule.getPercentileInfos())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.PRICE_POLICY_GIFT_AND_PERCENTILE_CAN_NOT_BE_EXIST));
        }
    }

    private void processPercentileFields(List<PriceRule.PercentileInfo> percentileInfos,
            Map<String, Set<String>> objectFields, Set<String> aggregateValueIds) {
        // 为空不校验
        if (CollectionUtils.empty(percentileInfos)) {
            return;
        }
        if (percentileInfos.size() != 1) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.PRICE_POLICY_PERCENTILE_ONLY_SUPPORT_ONE));
        }
        for (PriceRule.PercentileInfo percentileInfo : percentileInfos) {
            if (null == percentileInfo.getFieldValue()
                    || BigDecimalUtils.compare(percentileInfo.getFieldValue(), Operator.LTE, BigDecimal.ZERO)) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.PRICE_POLICY_PERCENTILE_VALUE_MUST_BE_POSITIVE));
            }
            requireField(percentileInfo);
            customerPercentileValidate(percentileInfo);
            // 是聚合规则，并且是组合规则,条件中不包括此组合规则
            if (percentileInfo.isAggregateRule() && percentileInfo.isGroupAgg()
                    && !aggregateValueIds.contains(percentileInfo.getFieldName())) {
                throw new ValidateException(
                        I18N.text(SFAI18NKeyUtil.PRICE_POLICY_PERCENTILE_GROUP_INCONSISTENT_CONDITION));
            }
            // 判断类型如果是聚合规则，放到aggregateValueIds中，如果是字段，放到objectFields
            if (percentileInfo.isAggregateRule()) {
                aggregateValueIds.add(percentileInfo.getFieldName());
            } else {
                objectFields.computeIfAbsent(percentileInfo.getObjectApiName(), key -> Sets.newHashSet())
                        .add(percentileInfo.getFieldName());
            }
        }
    }

    protected void customerPercentileValidate(PriceRule.PercentileInfo percentileInfo) {
        if (percentileInfo.isGroupAgg()) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.PRICE_POLICY_CANNOT_SET_COMBINATION_AGGREGATE_RULE));
        }
    }

    private void requireField(PriceRule.PercentileInfo percentileInfo) {
        // 必填值校验
        if (StringUtils.isBlank(percentileInfo.getFieldNameType()) ||
                StringUtils.isBlank(percentileInfo.getFieldName()) ||
                StringUtils.isBlank(percentileInfo.getObjectApiName())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        if (percentileInfo.isAggregateRule() && StringUtils.isBlank(percentileInfo.getAggValueType())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
    }

    @Override
    public String renderExecutionResult(User user, String executionResultStr,
            Map<String, IObjectDescribe> objectDescribeMap) {
        return JSON.toJSONString(getGiftRule(user, executionResultStr, objectDescribeMap));
    }

    protected PriceRule.GiftRule getGiftRule(User user, String executionResultStr,
            Map<String, IObjectDescribe> objectDescribeMap) {
        PriceRule.GiftRule giftRule = parseRule(executionResultStr);
        renderGiftRuleUnitName(giftRule, user);
        Set<String> productIds = processProductName(user, giftRule, objectDescribeMap, null);
        if (CollectionUtils.notEmpty(productIds)) {
            Map<String, String> idNameMap = getNameMapByIds(user, SFAPreDefine.Product.getApiName(), productIds);
            if (CollectionUtils.notEmpty(idNameMap)) {
                processProductName(user, giftRule, objectDescribeMap, idNameMap);
            }

            fillPricingInfo(user, giftRule, productIds);
        }
        return giftRule;
    }

    protected void fillPricingInfo(User user, PriceRule.GiftRule giftRule, Set<String> productIds) {
        // 如果开通了周期性产品，补充周期性产品信息
        if (!configService.openPeriodicProduct(RequestContextManager.getContext().getTenantId())) {
            return;
        }
        // 补充周期性产品信息
        List<IObjectData> productData = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(),
                Lists.newArrayList(productIds), SFAPreDefine.Product.getApiName());
        Map<String, IObjectData> productIdToDataMap = productData.stream()
                .collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        for (PriceRule.GiftLine giftLine : giftRule.getGiftList()) {
            IObjectData product = productIdToDataMap.get(giftLine.getProductId());
            setGiftPricingInfo(giftLine, product);
        }
    }

    private void setGiftPricingInfo(PriceRule.GiftLine giftLine, IObjectData product) {
        if (product != null) {
            giftLine.setPricingCycle(product.get(ProductConstants.PRICING_CYCLE, String.class));
            giftLine.setPricingRate(product.get(ProductConstants.PRICING_FREQUENCY, String.class));
            giftLine.setPricingMode(product.get(ProductConstants.PRICING_MODE, String.class));
            // 如果定价模式为一次性，且定价周期不为1，则设置定价周期为1
            if (ProductConstants.PricingModeEnum.ONE.getValue().equals(giftLine.getPricingMode())
                    && StringUtils.isNotBlank(giftLine.getPricingPeriod())
                    && !"1".equals(giftLine.getPricingPeriod())) {
                giftLine.setPricingPeriod("1");
            }

            if(StringUtils.isBlank(giftLine.getPricingPeriod())){
                giftLine.setPricingPeriod("1");
            }
        }
    }

    protected void renderGiftRuleUnitName(PriceRule.GiftRule giftRule, User user) {
        if (!configService.isMultipleUnit(user.getTenantId())) {
            return;
        }
        Optional.ofNullable(giftRule)
                .map(PriceRule.GiftRule::getGiftList)
                .filter(CollectionUtils::notEmpty)
                .ifPresent(giftLines -> {
                    Map<String, String> unitIdToName = getUnitIdToName(user, giftLines);
                    Optional.ofNullable(
                            serviceFacade.findObject(user.getTenantId(), SFAPreDefine.MultiUnitRelated.getApiName()))
                            .map(multiUnitRelateDescribe -> multiUnitRelateDescribe.getFieldDescribe("unit_type"))
                            .map(SelectOneFieldDescribe.class::cast)
                            .map(SelectOneFieldDescribe::getSelectOptions)
                            .filter(CollectionUtils::notEmpty)
                            .ifPresent(selectOption -> unitIdToName.putAll(selectOption.stream().collect(Collectors
                                    .toMap(ISelectOption::getValue, ISelectOption::getLabel, (v1, v2) -> v2))));
                    // 本品单位
                    unitIdToName.put(SizeUnit.CURRENT_PRODUCT_UNIT.getUnitId(),
                            I18N.text("price.policy.unit.type.current.product"));
                    for (PriceRule.GiftLine giftLine : giftLines) {
                        giftLine.setUnitIdName(unitIdToName.get(giftLine.getUnitId()));
                    }
                });

    }

    private Map<String, String> getUnitToName(User user, List<PriceRule.GiftLine> giftLines) {
        List<String> unitIdList = giftLines.stream()
                .map(PriceRule.GiftLine::getUnit)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        return getGiftNameMap(user, unitIdList);
    }

    private Map<String, String> getUnitIdToName(User user, List<PriceRule.GiftLine> giftLines) {
        List<String> unitIdList = giftLines.stream()
                .map(PriceRule.GiftLine::getUnitId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        return getGiftNameMap(user, unitIdList);
    }

    @NotNull
    private Map<String, String> getGiftNameMap(User user, List<String> unitIdList) {
        List<INameCache> unitNameCacheList = serviceFacade.findRecordName(ActionContextExt.of(user).getContext(),
                "UnitInfoObj", Lists.newArrayList(unitIdList));
        if (CollectionUtils.empty(unitNameCacheList)) {
            return Maps.newHashMap();
        }
        return unitNameCacheList.stream()
                .filter(iNameCache -> StringUtils.isNotBlank(iNameCache.getName()))
                .collect(Collectors.toMap(INameCache::getId, INameCache::getName, (v1, v2) -> v2));
    }

    @Override
    public Match.ExecutionData calRuleResult(Match.ExecutionParam param) {
        IObjectData masterData = param.getMasterData();
        String objectApiName = param.getObjectApiName();
        List<IObjectData> detailDataList = param.getDetailDataList();
        PricePolicyRule rule = param.getRule();
        Map<String, String> aggValueMap = param.getAggValueMap();
        DomainPluginDescribeExt pluginParam = param.getPluginParam();
        Match.ExecutionData executionData = Match.ExecutionData.builder().build();
        PriceRule.GiftRule giftRule = parseRule(rule.getExecutionResult());
        IObjectData detailData = CollectionUtils.empty(detailDataList) ? null : detailDataList.get(0);
        Optional<Double> cycleInfo = simpleComputeCycle(objectApiName, masterData, detailData, giftRule, aggValueMap,
                param.getProgressiveInfos());
        // 获取比例计算的原值
        Optional<Tuple<BigDecimal, BigDecimal>> percentileSourceValueOpt = getPercentileSourceValue(objectApiName,
                masterData, detailData, giftRule, aggValueMap);

        // 设置本品对应的产品信息
        setThisProductInfo(giftRule, detailData);

        fillProductInfo(giftRule);

        dealSizeUnit(giftRule, detailData, pluginParam);

        // 处理多币种价格
        setCurrencyPrice(masterData, giftRule);

        giftRule.clean();

        filterRangeShelves(giftRule, masterData, pluginParam);

        if (handlerCycleOrPercentile(executionData, giftRule, cycleInfo, percentileSourceValueOpt)) {
            return executionData;
        }
        if (!objectApiName.equals(giftRule.getObjectApiName())) {
            // 从对象
            if (detailData != null) {
                String dataIndex = detailData.get(DATA_INDEX, String.class);
                // String groupKey = detailData.get(GROUP_KEY, String.class, "");
                executionData.addToDetailRuleId(dataIndex, rule.getId());
                executionData.setDetailPricePolicyId(dataIndex, rule.getPricePolicyId());
                executionData.addGiftToDetail(dataIndex, rule.getId(), giftRule);
                // executionData.setGroupKey(dataIndex, groupKey);
            }

        } else {
            executionData.getMasterData().put(PRICE_POLICY_ID,
                    PriceRule.ChangeInfo.builder().rightValue(rule.getPricePolicyId()).build());
            executionData.addToMasterRuleId(rule.getId());
            executionData.addGiftToMaster(rule.getId(), giftRule);
        }

        return executionData;
    }

    protected void filterRangeShelves(PriceRule.GiftRule giftRule, IObjectData masterData,
            DomainPluginDescribeExt pluginParam) {
        User user = RequestContextManager.getContext().getUser();
        if (PriceRule.GiftType.FIX.equals(giftRule.getType()) || CollectionUtils.empty(giftRule.getGiftList())) {
            return;
        }
        List<PriceRule.GiftLine> giftList = giftRule.getGiftList();
        Set<String> notRequireOptionGiftProductIdSet = giftList.stream()
                .filter(x -> !Boolean.TRUE.equals(x.getRequired()))
                .map(PriceRule.GiftLine::getProductId)
                .collect(Collectors.toSet());
        Set<String> matchRangeShelvesProductIdSet = availableProductStatusService
                .getInRangeShelvesProduct(notRequireOptionGiftProductIdSet, user, masterData, pluginParam);
        // 移除非必选的赠品。不在可售范围，和已下架的产品。
        giftList.removeIf(x -> !Boolean.TRUE.equals(x.getRequired())
                && !matchRangeShelvesProductIdSet.contains(x.getProductId()));
    }

    protected boolean handlerCycleOrPercentile(Match.ExecutionData executionData, PriceRule.GiftRule giftRule,
            Optional<Double> cycleInfo, Optional<Tuple<BigDecimal, BigDecimal>> percentileSourceValueOpt) {
        // 每满和按比例只可以存在一个，在入参的时候会做校验，这里只需要判断，哪个存在就计算哪个，不是两个都计算
        if (cycleInfo.isPresent()) {
            Double cycleCount = cycleInfo.get();
            // 每满计算结果为零即不匹配赠品规则
            if (Objects.equals(cycleCount, 0.0)) {
                return true;
            }
            giftRule.setCycleCount(cycleCount);
            executionData.setCycleCount(cycleCount);
        }
        if (configService.isOpenPricePolicyPercentile(RequestContextManager.getContext().getTenantId())) {
            if (StringUtils.isBlank(giftRule.getGiftBasis())) {
                giftRule.setGiftBasis(GiftBasisType.QUANTITY);
            }
            if (percentileSourceValueOpt.isPresent()) {
                if (lessThenValue(percentileSourceValueOpt.get())) {
                    return true;
                }
                handlePercentile(giftRule, percentileSourceValueOpt.get());
            }
        }
        return false;
    }

    protected void handlePercentile(PriceRule.GiftRule giftRule,
            Tuple<BigDecimal, BigDecimal> percentileSourceValueTuple) {
        percentileGiftExecute.exec(PercentileGiftHandler.Context.builder()
                .giftRule(giftRule)
                .percentileSourceValueTuple(percentileSourceValueTuple)
                .build());
    }

    protected boolean lessThenValue(Tuple<BigDecimal, BigDecimal> percentileSourceValueTuple) {
        return BigDecimalUtils.compare(percentileSourceValueTuple.getKey(), Operator.LT,
                percentileSourceValueTuple.getValue());
    }

    /**
     * 获取百分位源值和配置值
     *
     * @param objectApiName 对象 API 名称
     * @param masterData    主数据
     * @param detailData    详细数据
     * @param giftRule      赠品规则
     * @param aggValueMap   聚合值映射
     * @return {@code Optional<Tuple<BigDecimal,BigDecimal>>} 计算原值，配置的值
     */
    protected Optional<Tuple<BigDecimal, BigDecimal>> getPercentileSourceValue(String objectApiName,
            IObjectData masterData,
            IObjectData detailData,
            PriceRule.GiftRule giftRule,
            Map<String, String> aggValueMap) {
        List<PriceRule.PercentileInfo> percentileInfos = giftRule.getPercentileInfos();
        if (!configService.isOpenPricePolicyPercentile(RequestContextManager.getContext().getTenantId())
                || CollectionUtils.empty(percentileInfos)) {
            return Optional.empty();
        }
        BigDecimal sourceValue = BigDecimal.ZERO;
        BigDecimal fieldValue = BigDecimal.ZERO;
        // 目前只支持一个比例
        for (PriceRule.PercentileInfo percentileInfo : percentileInfos) {
            if (!SFAPreDefine.AggregateRule.getApiName().equals(percentileInfo.getObjectApiName())) {
                sourceValue = objectApiName.equals(percentileInfo.getObjectApiName())
                        ? masterData.get(percentileInfo.getFieldName(), BigDecimal.class, BigDecimal.ZERO)
                        : detailData.get(percentileInfo.getFieldName(), BigDecimal.class, BigDecimal.ZERO);
            } else {
                String aggValue = aggValueMap.get(percentileInfo.getFieldName());
                sourceValue = Objects.equals(null, aggValue) ? BigDecimal.ZERO : new BigDecimal(aggValue);
            }
            fieldValue = percentileInfo.getFieldValue();
        }
        return Optional.of(new Tuple<>(sourceValue, fieldValue));
    }

    private void setCurrencyPrice(IObjectData masterData, PriceRule.GiftRule giftRule) {
        User user = RequestContextManager.getContext().getUser();
        if (configService.isCurrencyEnabled(user.getTenantId())
                && "product_price".equals(
                        configService.getBizConfig(user.getTenantId(), ConfigType.GIFT_AMORTIZE_BASIS.getKey()))) {
            Map<String, BigDecimal> pricesMap = Maps.newHashMap();
            giftRule.getGiftList().forEach(
                    giftLine -> pricesMap.put(giftLine.getProductId() + giftLine.getUnit(), giftLine.getPrice()));
            if (pricesMap.size() > 0) {
                if (StringUtils.isNotEmpty(masterData.get("mc_currency", String.class))) {
                    MtCurrentUtil.changePriceListToCurrency(user, masterData.get("mc_currency", String.class),
                            pricesMap, 8);
                }

                giftRule.getGiftList().forEach(
                        giftLine -> giftLine.setPrice(pricesMap.get(giftLine.getProductId() + giftLine.getUnit())));
            }
        }
    }

    private void setThisProductInfo(PriceRule.GiftRule giftRule, IObjectData detailData) {
        if (detailData == null) {
            return;
        }
        for (PriceRule.GiftLine gift : giftRule.getGiftList()) {
            if (PricePolicyConstants.PRICEPOLICY_THISPRODUCT.equals(gift.getProductId())) {
                gift.setProductId(detailData.get("product_id", String.class));
                gift.setProductName(detailData.get("product_id__r", String.class));
                gift.setThisProduct(true);
            }
        }
    }

    /**
     * @amortizeInfo <"pricePolicyId_ruleId_DataIndex",<fieldName,changeValue>>
     */
    @Override
    public void fillAmortizeInfo(Match.ExecutionData executionData, Map<String, Map<String, Object>> amortizeInfoMap) {
        HashMap<String, PriceRule.ChangeInfo> changeInfoHashMap = new HashMap<>();
        changeInfoHashMap.put("gift", PriceRule.ChangeInfo.builder().changeValue(null).build());
        if (CollectionUtils.notEmpty(executionData.getMasterData())) {
            putAmortizeInfoMap(
                    String.format("%s_%s", getAmortizeInfoKeyPrefix(executionData.getMasterData()), MASTER_INDEX),
                    changeInfoHashMap,
                    amortizeInfoMap);
        }
        if (CollectionUtils.notEmpty(executionData.getDetailDataMap())) {
            String key = getAmortizeInfoKeyPrefix(
                    executionData.getDetailDataMap().values().stream().findFirst().orElse(null));
            executionData.getDetailDataMap()
                    .forEach((dataIndex, fieldMap) -> putAmortizeInfoMap(String.format("%s_%s", key, dataIndex),
                            changeInfoHashMap, amortizeInfoMap));
        }
    }

    protected void fillProductInfo(PriceRule.GiftRule giftRule) {
        Set<String> productIds = Sets.newHashSet();
        productIds.addAll(giftRule.getGiftList().stream()
                .map(PriceRule.GiftLine::getProductId).collect(Collectors.toSet()));
        User user = RequestContextManager.getContext().getUser();
        Map<String, IObjectData> dataList = serviceFacade
                .findObjectDataByIds(user.getTenantId(), Lists.newArrayList(productIds), Utils.PRODUCT_API_NAME)
                .stream().filter(x -> Boolean.FALSE.equals(x.isDeleted()))
                .collect(Collectors.toMap(DBRecord::getId, y -> y));
        IObjectDescribe productDescribe = serviceFacade.findObject(user.getTenantId(), Utils.PRODUCT_API_NAME);
        Boolean isOpenDisPlayName = Optional.ofNullable(productDescribe)
                .map(IObjectDescribe::isOpenDisplayName)
                .orElse(Boolean.FALSE);
        // 只保留从库中查询出来的，没查到的赠品可能已经删掉了
        giftRule.setGiftList(
                giftRule.getGiftList().stream()
                        .filter(giftLine -> dataList.containsKey(giftLine.getProductId()))
                        .collect(Collectors.toList()));
        boolean isOpenPeriodicProduct = configService.openPeriodicProduct(user.getTenantId());

        Map<String, String> unitToLabel = getSimpleUnitLabel(productDescribe);
        giftRule.getGiftList().forEach(giftLine -> {
            IObjectData objectData = dataList.get(giftLine.getProductId());
            if (Boolean.TRUE.equals(isOpenDisPlayName)) {
                giftLine.setProductName(objectData.getDisplayName());
            } else {
                giftLine.setProductName(objectData.getName());
            }
            giftLine.setPrice(objectData.get("price", BigDecimal.class, BigDecimal.ZERO));
            giftLine.setUnit(objectData.get("unit", String.class));
            // 如果没有开启多单位，在 dealSizeUnit方法统一处理，大小单位，多单位情况
            String unitLabel = unitToLabel.get(giftLine.getUnit());
            if (StringUtils.isNotBlank(unitLabel)) {
                giftLine.setUnitName(unitLabel);
            }
            giftLine.setMultipleUnit(objectData.get("is_multiple_unit", Boolean.class, false));
            giftLine.setDisplayName(objectData.get(IObjectData.DISPLAY_NAME, String.class));
            if (isOpenPeriodicProduct) {
                Map<String, Object> periodicMap = Maps.newHashMap();
                for (String fieldName : PERIODIC_API_NAMES) {
                    periodicMap.put(fieldName, objectData.get(fieldName));
                }
                if (ProductConstants.PricingModeEnum.CYCLE.getValue()
                        .equals(periodicMap.get(ProductConstants.PRICING_MODE))) {
                    periodicMap.put(PeriodicProductDm.DetailField.SERVICE_START_TIME,
                            LocalDate.now().atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli());
                }
                setGiftPricingInfo(giftLine, objectData);
                if (ProductConstants.PricingModeEnum.ONE.getValue()
                        .equals(periodicMap.get(ProductConstants.PRICING_MODE))) {
                    giftLine.setPricingPeriod("1");
                }

                giftLine.setPeriodicMap(periodicMap);
            }
        });

    }

    /**
     * 从描述中获取单位value和label对应关系
     *
     * @param productDescribe 对象描述
     * @return {@code Map<String, String>} key为单位value，value为单位label
     */
    private Map<String, String> getSimpleUnitLabel(IObjectDescribe productDescribe) {
        Map<String, String> unitToLabel = Maps.newHashMap();
        // 如果没有开启多单位，在 dealSizeUnit方法统一处理，大小单位，多单位情况
        if (configService.isMultipleUnit(RequestContextManager.getContext().getTenantId())) {
            return unitToLabel;
        }
        Optional.ofNullable(productDescribe)
                .map(x -> x.getFieldDescribe("unit"))
                .map(SelectOneFieldDescribe.class::cast)
                .map(SelectOneFieldDescribe::getSelectOptions)
                .filter(CollectionUtils::notEmpty)
                .ifPresent(optionList -> optionList
                        .forEach(option -> unitToLabel.put(option.getValue(), option.getLabel())));
        return unitToLabel;
    }

    /**
     * 处理大小单位
     *
     * @param detailData 详细数据
     */
    protected void dealSizeUnit(PriceRule.GiftRule giftRule, IObjectData detailData,
            DomainPluginDescribeExt pluginParam) {
        if (!configService.isMultipleUnit(RequestContextManager.getContext().getTenantId())) {
            // 没有开启多单位，web端也传了单位性质数据，这里进行清空，否则web端会展示错误
            Optional.ofNullable(giftRule)
                    .map(PriceRule.GiftRule::getGiftList)
                    .filter(CollectionUtils::notEmpty)
                    .ifPresent(giftList -> {
                        for (PriceRule.GiftLine giftLine : giftList) {
                            giftLine.setUnitId(null);
                            giftLine.setUnitIdName(null);
                        }
                    });
            return;
        }
        List<String> giftProductIds = giftRule.getGiftList().stream()
                .map(PriceRule.GiftLine::getProductId)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(giftProductIds)) {
            return;
        }
        // 调用勋哥的接口
        List<IObjectData> multiUnitInfoListByProduct = multiUnitService
                .getMultiUnitInfoListByProduct(RequestContextManager.getContext().getTenantId(), giftProductIds);
        // 整理为productId sizeUnit 值为单位的形式，之后进行取出
        HashBasedTable<String, String, String> productIdToSizeUnitToMultipleUnit = getMultiUnitHashTable(
                multiUnitInfoListByProduct);
        // 整理为productId to unit_id形式
        Map<String, Set<String>> productIdToUnitId = multiUnitInfoListByProduct.stream()
                .collect(Collectors.groupingBy(objectData -> objectData.get("product_id", String.class),
                        Collectors.mapping(x -> x.get("unit_id", String.class), Collectors.toSet())));
        // 保存删除数据
        List<PriceRule.GiftLine> deleteGiftLines = Lists.newArrayList();
        giftRule.getGiftList().forEach(giftLine -> {
            // 大小单位处理
            if (SizeUnit.SMALL.getUnitId().equals(giftLine.getUnitId())
                    || SizeUnit.MEDIUM.getUnitId().equals(giftLine.getUnitId())
                    || SizeUnit.LARGE.getUnitId().equals(giftLine.getUnitId())) {
                String unit = productIdToSizeUnitToMultipleUnit.get(giftLine.getProductId(), giftLine.getUnitId());
                // 只有不为空的时候，才放入获取到的单位,没有找到对应的大小单位，则进行删除
                if (StringUtils.isNotBlank(unit)) {
                    giftLine.setUnit(unit);
                } else {
                    deleteGiftLines.add(giftLine);
                }
                // 处理本品单位情况
            } else if (SizeUnit.CURRENT_PRODUCT_UNIT.getUnitId().equals(giftLine.getUnitId())) {
                if (detailData != null) {
                    // 本品是否是多单位
                    boolean isMultipleUnit = detailData.get(CommonProductConstants.Field.IsMultipleUnit.apiName + "__v",
                            Boolean.class, false);
                    if (isMultipleUnit) {
                        giftLine.setUnit(detailData.get(
                                pluginParam.getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.ACTUAL_UNIT),
                                String.class));
                    } else {
                        giftLine.setUnit(detailData.get(
                                pluginParam.getDefaultDetailFieldApiName(CouponConstants.PluginDetailField.UNIT_FLAG),
                                String.class));
                    }
                }
                // 处理正常多单位情况
            } else if (StringUtils.isNotBlank(giftLine.getUnitId())
                    && !SizeUnit.BASE_UNIT.getUnitId().equals(giftLine.getUnitId()) && giftLine.isMultipleUnit()) {
                if (productIdToUnitId.getOrDefault(giftLine.getProductId(), Sets.newHashSet())
                        .contains(giftLine.getUnitId())) {
                    giftLine.setUnit(giftLine.getUnitId());
                } else {
                    deleteGiftLines.add(giftLine);
                }
            }
        });
        // 删除没有匹配大小单位情况
        giftRule.getGiftList().removeAll(deleteGiftLines);
        if (CollectionUtils.empty(giftRule.getGiftList())) {
            return;
        }
        Map<String, String> unitIdToName = getUnitToName(RequestContextManager.getContext().getUser(),
                giftRule.getGiftList());

        for (PriceRule.GiftLine giftLine : giftRule.getGiftList()) {
            // web端上要求，暂时清空这俩数据，导致名称显示不对
            giftLine.setUnitId(null);
            giftLine.setUnitIdName(null);
            // 小程序端要求，补充名称
            giftLine.setUnitName(unitIdToName.get(giftLine.getUnit()));
        }

    }

    public HashBasedTable<String, String, String> getMultiUnitHashTable(List<IObjectData> multiUnitInfoListByProduct) {
        // 整理为productId sizeUnit 值为单位的形式，之后进行取出
        HashBasedTable<String, String, String> productIdToSizeUnitToMultipleUnit = HashBasedTable.create();
        for (IObjectData objectData : multiUnitInfoListByProduct) {
            String productId = objectData.get("product_id", String.class);
            String unitType = objectData.get("unit_type", String.class);
            String unitId = objectData.get("unit_id", String.class);
            if (StringUtils.isNotBlank(productId) && StringUtils.isNotBlank(unitType)
                    && StringUtils.isNotBlank(unitId)) {
                productIdToSizeUnitToMultipleUnit.put(productId, unitType, unitId);
            }
        }
        return productIdToSizeUnitToMultipleUnit;
    }

    protected Set<String> processProductName(User user,
            PriceRule.GiftRule giftRule,
            Map<String, IObjectDescribe> objectDescribeMap,
            Map<String, String> productIdNameMap) {
        Set<String> productIds = Sets.newHashSet();
        if (CollectionUtils.empty(objectDescribeMap)) {
            objectDescribeMap = Maps.newHashMap();
        }
        boolean needSetFieldValue = CollectionUtils.empty(productIdNameMap);
        boolean hasLimitQuantity = false;
        for (PriceRule.GiftLine giftLine : giftRule.getGiftList()) {
            if (StringUtils.isBlank(giftLine.getProductId())) {
                continue;
            }
            if (null == giftLine.getRequired()) {
                giftLine.setRequired(Boolean.FALSE);
            }
            if (null != giftLine.getPolicyMaxValue() || null != giftLine.getAccountMaxValue()) {
                hasLimitQuantity = true;
            }

            if (PricePolicyConstants.PRICEPOLICY_THISPRODUCT.equals(giftLine.getProductId())) {
                giftLine.setProductName(I18N.text("sfa.gift.rule.this.product"));
                continue;
            }

            if (needSetFieldValue) {
                productIds.add(giftLine.getProductId());
            } else {
                giftLine.setProductName(productIdNameMap.getOrDefault(giftLine.getProductId(), ""));
            }
        }
        // 兼容历史已设置限量的数据
        if (hasLimitQuantity && null == giftRule.getLimitInfo()) {
            giftRule.setLimitInfo(PriceRule.LimitInfo.builder().limitType(PriceRule.LimitType.GIFT_QUANTITY).build());
        }
        if (PriceRule.GiftType.FIX.equals(giftRule.getType())) {
            giftRule.setGiftCondition(null);
        }
        if (!needSetFieldValue) {
            return productIds;
        }
        IObjectDescribe objectDescribe = objectDescribeMap.get(giftRule.getObjectApiName());
        if (null == objectDescribe) {
            return productIds;
        }
        giftRule.setObjectApiName__s(objectDescribe.getDisplayName());
        renderCycleInfo(user, giftRule.getCycleInfos(), objectDescribeMap);
        renderPercentileInfo(user, giftRule.getPercentileInfos(), objectDescribeMap);
        List<PriceRuleWhere> conditionList = JSON.parseArray(giftRule.getCondition(), PriceRuleWhere.class);
        if (CollectionUtils.notEmpty(conditionList)) {
            List<JSONObject> jsonConditionList = processFilterName(user, conditionList, giftRule.getObjectApiName(),
                    objectDescribeMap);
            giftRule.setCondition(JSON.toJSONString(jsonConditionList));
        }
        List<PriceRuleWhere> giftConditionList = JSON.parseArray(giftRule.getGiftCondition(), PriceRuleWhere.class);
        if (CollectionUtils.notEmpty(giftConditionList)) {
            List<JSONObject> jsonGiftConditionList = processFilterName(user, giftConditionList,
                    SFAPreDefine.Product.getApiName(), objectDescribeMap);
            giftRule.setGiftCondition(JSON.toJSONString(jsonGiftConditionList));
        }
        return productIds;
    }

    private void renderPercentileInfo(User user, List<PriceRule.PercentileInfo> percentileInfos,
            Map<String, IObjectDescribe> objectDescribeMap) {
        if (!configService.isOpenPricePolicyPercentile(user.getTenantId()) || CollectionUtils.empty(percentileInfos)) {
            return;
        }
        for (PriceRule.PercentileInfo percentileInfo : percentileInfos) {
            setCycleItemData(user, objectDescribeMap, percentileInfo);
        }
    }

    /**
     * 保存检查
     *
     * @param param 参数
     * @return {@code Map<String, Object>}
     */
    @Override
    public Map<String, Object> saveCheck(Match.CheckParam param) {
        IObjectData masterData = param.getMasterData();
        String objectApiName = param.getObjectApiName();
        PricePolicyRule rule = param.getRule();
        Map<String, List<IObjectData>> parentGiftKeyToGiftProduct = param.getParentGiftKeyToGiftProduct();
        List<IObjectData> noGiftDetailDataList = param.getNoGiftDetailDataList();
        IObjectData policyData = param.getPolicyData();
        Map<String, String> aggValueMap = param.getAggValueMap();
        Map<String, IFieldDescribe> detailFieldDescribe = param.getDetailFieldDescribe();
        Map<String, List<PriceRule.ProgressiveInfo>> progressiveInfoMaps = param.getProgressiveInfoMaps();
        PriceRule.GiftRule giftExecutionResult = parseRule(rule.getExecutionResult());
        // 是整单赠品
        if (ModifyType.MASTER.equals(policyData.get(PricePolicyField.MODIFY_TYPE))) {
            masterGiftCheck(objectApiName, masterData, parentGiftKeyToGiftProduct, rule, aggValueMap,
                    detailFieldDescribe, giftExecutionResult, progressiveInfoMaps);
        } else {
            // 获取到匹配当前规则的订单产品
            List<IObjectData> matchCurrentRuleDataList = getMatchCurrentRule(noGiftDetailDataList,
                    rule.getPricePolicyId(), rule.getId());
            // 计算满足当前规则产品的聚合值
            detailGiftCheck(objectApiName, masterData, parentGiftKeyToGiftProduct, matchCurrentRuleDataList, rule,
                    aggValueMap, detailFieldDescribe, giftExecutionResult, Optional.empty(), progressiveInfoMaps,
                    Optional.empty());
        }
        checkGiftProductAmortizePrice(parentGiftKeyToGiftProduct, masterData);
        return Maps.newHashMap();
    }

    /**
     * 校验赠品分摊价格
     *
     * @param parentGiftKeyToGiftProduct 赠品
     * @param masterData
     */
    protected void checkGiftProductAmortizePrice(Map<String, List<IObjectData>> parentGiftKeyToGiftProduct,
            IObjectData masterData) {
        if (CollectionUtils.empty(parentGiftKeyToGiftProduct)) {
            return;
        }
        IFieldDescribe giftAmortizePriceField = getGiftAmortizePriceField(parentGiftKeyToGiftProduct);
        if (giftAmortizePriceField == null) {
            return;
        }
        getProductIdToPrice(parentGiftKeyToGiftProduct, giftAmortizePriceField, masterData);
    }

    private void getProductIdToPrice(Map<String, List<IObjectData>> parentGiftKeyToGiftProduct,
            IFieldDescribe giftAmortizePriceField, IObjectData masterData) {
        String giftAmortizeType = configService.getBizConfig(RequestContextManager.getContext().getTenantId(),
                ConfigType.GIFT_AMORTIZE_BASIS.getKey());
        if (giftAmortizeBasisType.PRICE_BOOK_PRICE.equals(giftAmortizeType)) {
            doPriceBookPriceCheck(parentGiftKeyToGiftProduct, giftAmortizePriceField);
        } else if (giftAmortizeBasisType.PRODUCT_PRICE.equals(giftAmortizeType)) {
            Map<String, BigDecimal> productIdToPrice = getProductPrice(parentGiftKeyToGiftProduct,
                    giftAmortizePriceField, masterData);
            parentGiftKeyToGiftProduct.forEach((parentGift, giftProducts) -> {
                if (CollectionUtils.empty(giftProducts)) {
                    return;
                }
                for (IObjectData giftProduct : giftProducts) {
                    BigDecimal giftAmortizePrice = giftProduct.get(
                            SalesOrderConstants.SalesOrderProductField.GIFT_AMORTIZE_PRICE.getApiName(),
                            BigDecimal.class);
                    String productId = giftProduct
                            .get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class);
                    Boolean isMultipleUnit = giftProduct.get("is_multiple_unit__v", Boolean.class);
                    if (Boolean.TRUE.equals(isMultipleUnit)) {
                        String actualUnit = giftProduct
                                .get(SalesOrderConstants.SalesOrderProductField.ACTUAL_UNIT.getApiName(), String.class);
                        productId = productId.concat(actualUnit);
                    }
                    BigDecimal targetAmortizePrice = productIdToPrice.getOrDefault(productId, BigDecimal.ZERO);

                    if (!BigDecimalUtils.compare(giftAmortizePrice, targetAmortizePrice,
                            BigDecimalUtils.DEFAULT_ERROR_VALUE)) {
                        throw new ValidateException(
                                I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_CHECK_GIFT_AMORTIZE,
                                        PricePolicyValidator.getProductName(giftProduct)),
                                PricePolicyCheckCode.GIFT_AMORTIZE);
                    }
                }
            });
        }
    }

    private Map<String, BigDecimal> getProductPrice(Map<String, List<IObjectData>> parentGiftKeyToGiftProduct,
            IFieldDescribe giftAmortizePriceField, IObjectData masterData) {
        CurrencyFieldDescribe currencyFieldDescribe = (CurrencyFieldDescribe) giftAmortizePriceField;
        // 结果值
        Map<String, BigDecimal> productIdToPrice = Maps.newHashMap();
        // 记录多单位id
        List<IObjectData> normalProductDates = Lists.newArrayList();
        List<IObjectData> multipleUnitProductDates = Lists.newArrayList();
        // 区分多单位产品和非多单位产品
        splitProduct(parentGiftKeyToGiftProduct, normalProductDates, multipleUnitProductDates);
        getMultipleUnitPrice(currencyFieldDescribe, productIdToPrice, multipleUnitProductDates, masterData);
        getNormalProductPrice(currencyFieldDescribe, productIdToPrice, normalProductDates, masterData);
        return productIdToPrice;
    }

    private void getMultipleUnitPrice(CurrencyFieldDescribe currencyFieldDescribe,
            Map<String, BigDecimal> productIdToPrice, List<IObjectData> multipleUnitProductDates,
            IObjectData masterData) {
        if (CollectionUtils.notEmpty(multipleUnitProductDates)) {
            MutipleUnitInfo.CalcUnitPriceArg arg = buildMultipleUnitArg(multipleUnitProductDates, masterData);
            Map<String, BigDecimal> multipleProductIdToPrice = Maps.newHashMap();
            getNormalProductPrice(currencyFieldDescribe, multipleProductIdToPrice, multipleUnitProductDates,
                    masterData);
            DomainPluginParam pluginParam = infraServiceFacade.findPluginParam(
                    RequestContextManager.getContext().getTenantId(), SFAPreDefine.SalesOrder.getApiName(),
                    DmDefineConstants.MULTI_UNIT);
            Optional.ofNullable(mutipleUnitToolService.calcPriceByUnit(RequestContextManager.getContext().getUser(),
                    arg, DomainPluginDescribeExt.of(arg.getDescribeApiName(), pluginParam)))
                    .map(MutipleUnitInfo.CalculateResult::getCaclResult)
                    .filter(CollectionUtils::notEmpty)
                    .ifPresent(result -> {
                        for (MutipleUnitInfo.CalculateInfo calculateInfo : result) {
                            String productId = calculateInfo.getProductId();
                            BigDecimal productPrice = multipleProductIdToPrice.getOrDefault(productId, BigDecimal.ZERO);
                            productIdToPrice.put(calculateInfo.getProductId().concat(calculateInfo.getUnitId()),
                                    productPrice.multiply(BigDecimal.valueOf(calculateInfo.getConversion_ratio()))
                                            .setScale(currencyFieldDescribe.getDecimalPlaces(),
                                                    BigDecimal.ROUND_HALF_UP));
                        }
                    });
        }
    }

    private MutipleUnitInfo.CalcUnitPriceArg buildMultipleUnitArg(List<IObjectData> multipleUnitProductDates,
            IObjectData masterData) {
        MutipleUnitInfo.CalcUnitPriceArg arg = new MutipleUnitInfo.CalcUnitPriceArg();
        arg.setDescribeApiName(SFAPreDefine.SalesOrderProduct.getApiName());
        arg.setParams(getParams(multipleUnitProductDates));
        User user = RequestContextManager.getContext().getUser();
        if (configService.isCurrencyEnabled(user.getTenantId())) {
            String mcCurrency = masterData.get("mc_currency", String.class);
            if (StringUtils.isNotBlank(mcCurrency)) {
                arg.setMcCurrency(mcCurrency);
            }
        }
        return arg;
    }

    private List<MutipleUnitInfo.CalculateParam> getParams(List<IObjectData> multipleUnitProductDates) {
        List<MutipleUnitInfo.CalculateParam> params = Lists.newArrayList();
        for (IObjectData multipleUnitProductDate : multipleUnitProductDates) {
            String productId = multipleUnitProductDate
                    .get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class);
            String actualUnit = multipleUnitProductDate
                    .get(SalesOrderConstants.SalesOrderProductField.ACTUAL_UNIT.getApiName(), String.class);
            String priceBookProductId = multipleUnitProductDate
                    .get(SalesOrderConstants.SalesOrderProductField.PRICE_BOOK_PRODUCT_ID.getApiName(), String.class);
            BigDecimal quantity = multipleUnitProductDate
                    .get(SalesOrderConstants.SalesOrderProductField.QUANTITY.getApiName(), BigDecimal.class);
            MutipleUnitInfo.CalculateParam param = new MutipleUnitInfo.CalculateParam();
            param.setProductId(productId);
            param.setUnitId(actualUnit);
            param.setPriceBookProductId(priceBookProductId);
            param.setCount(quantity.doubleValue());
            params.add(param);
        }
        return params;
    }

    private void getNormalProductPrice(CurrencyFieldDescribe currencyFieldDescribe,
            Map<String, BigDecimal> productIdToPrice, List<IObjectData> normalProductDates, IObjectData masterData) {
        if (CollectionUtils.notEmpty(normalProductDates)) {
            List<String> productIds = normalProductDates.stream()
                    .map(x -> x.get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            Map<String, BigDecimal> priceMap = Optional
                    .ofNullable(serviceFacade.findObjectDataByIds(RequestContextManager.getContext().getTenantId(),
                            productIds, Utils.PRODUCT_API_NAME))
                    .map(productDates -> productDates.stream()
                            .collect(Collectors.toMap(IObjectData::getId,
                                    // BigDecimal.ROUND_HALF_UP 和端上保持一致
                                    data -> data.get(ProductConstants.PRODUCT_PRICE, BigDecimal.class, BigDecimal.ZERO)
                                            .setScale(currencyFieldDescribe.getDecimalPlaces(),
                                                    BigDecimal.ROUND_HALF_UP),
                                    (v1, v2) -> v2)))
                    .orElse(Maps.newHashMap());
            productIdToPrice.putAll(priceMap);
        }
        dealCurrency(productIdToPrice, currencyFieldDescribe, masterData);
    }

    private void dealCurrency(Map<String, BigDecimal> productIdToPrice, CurrencyFieldDescribe currencyFieldDescribe,
            IObjectData masterData) {
        User user = RequestContextManager.getContext().getUser();
        if (configService.isCurrencyEnabled(user.getTenantId())) {
            String mcCurrency = masterData.get("mc_currency", String.class);
            if (productIdToPrice.size() > 0 && StringUtils.isNotEmpty(mcCurrency)) {
                MtCurrentUtil.changePriceListToCurrency(user, mcCurrency, productIdToPrice,
                        currencyFieldDescribe.getDecimalPlaces());
            }
        }
    }

    private void splitProduct(Map<String, List<IObjectData>> parentGiftKeyToGiftProduct,
            List<IObjectData> normalProductDates, List<IObjectData> multipleUnitProductDates) {
        parentGiftKeyToGiftProduct.forEach((parentGiftKey, productList) -> {
            if (CollectionUtils.empty(productList)) {
                return;
            }
            for (IObjectData detailData : productList) {
                Boolean isMultipleUnit = detailData.get("is_multiple_unit__v", Boolean.class);
                // 多单位收集 productId 和 单位信息，非多单位收集productId即可
                if (Boolean.TRUE.equals(isMultipleUnit)) {
                    multipleUnitProductDates.add(detailData);
                } else {
                    normalProductDates.add(detailData);
                }
            }
        });
    }

    private IFieldDescribe getGiftAmortizePriceField(Map<String, List<IObjectData>> parentGiftKeyToGiftProduct) {
        for (Map.Entry<String, List<IObjectData>> stringListEntry : parentGiftKeyToGiftProduct.entrySet()) {
            List<IObjectData> giftProducts = stringListEntry.getValue();
            if (CollectionUtils.empty(giftProducts)) {
                continue;
            }
            IObjectDescribe objectDescribe = serviceFacade.findObject(RequestContextManager.getContext().getTenantId(),
                    giftProducts.get(0).getDescribeApiName());
            return objectDescribe
                    .getFieldDescribe(SalesOrderConstants.SalesOrderProductField.GIFT_AMORTIZE_PRICE.getApiName());
        }
        return null;
    }

    private void doPriceBookPriceCheck(Map<String, List<IObjectData>> parentGiftKeyToGiftProduct,
            IFieldDescribe giftAmortizePriceField) {
        CurrencyFieldDescribe currencyFieldDescribe = (CurrencyFieldDescribe) giftAmortizePriceField;
        for (Map.Entry<String, List<IObjectData>> stringListEntry : parentGiftKeyToGiftProduct.entrySet()) {
            List<IObjectData> giftProducts = stringListEntry.getValue();
            if (CollectionUtils.empty(giftProducts)) {
                continue;
            }
            for (IObjectData giftProduct : giftProducts) {
                BigDecimal priceBookPrice = giftProduct.get(
                        SalesOrderConstants.SalesOrderProductField.PRICE_BOOK_PRICE.getApiName(), BigDecimal.class,
                        BigDecimal.ZERO);
                // BigDecimal.ROUND_HALF_UP 和端上保持一致 920调整为四舍五入
                BigDecimal targetAmortizePrice = priceBookPrice.setScale(currencyFieldDescribe.getDecimalPlaces(),
                        BigDecimal.ROUND_HALF_UP);
                BigDecimal giftAmortizePrice = giftProduct.get(
                        SalesOrderConstants.SalesOrderProductField.GIFT_AMORTIZE_PRICE.getApiName(), BigDecimal.class);
                if (!BigDecimalUtils.compare(giftAmortizePrice, targetAmortizePrice,
                        BigDecimalUtils.DEFAULT_ERROR_VALUE)) {
                    throw new ValidateException(
                            I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_CHECK_GIFT_AMORTIZE,
                                    PricePolicyValidator.getProductName(giftProduct)),
                            PricePolicyCheckCode.GIFT_AMORTIZE);
                }
            }
        }
    }

    protected void detailGiftCheck(String objectApiName,
            IObjectData masterData,
            Map<String, List<IObjectData>> parentGiftKeyToGiftProduct,
            List<IObjectData> matchCurrentRuleDataList,
            PricePolicyRule rule,
            Map<String, String> aggValueMap,
            Map<String, IFieldDescribe> detailFieldDescribe,
            PriceRule.GiftRule giftExecutionResult, Optional<Double> cycleInfo,
            Map<String, List<PriceRule.ProgressiveInfo>> progressiveInfoMaps,
            Optional<Tuple<BigDecimal, BigDecimal>> percentileSourceValue) {
        List<PriceRule.GiftLine> originalGiftLine = giftExecutionResult.getGiftList();
        HashBasedTable<String, String, String> multiUnit = HashBasedTable.create();
        Map<String, String> productUnitMap = Maps.newHashMap();
        if (configService.isMultipleUnit(RequestContextManager.getContext().getTenantId())) {
            List<String> productIds = matchCurrentRuleDataList.stream()
                    .map(x -> x.get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class))
                    .collect(Collectors.toList());
            multiUnit.putAll(findMultiUnit(productIds));
            productUnitMap.putAll(findProductUnit(productIds));
        }
        // 循环订单产品
        for (IObjectData matchCurrentRuleOrderProduct : matchCurrentRuleDataList) {
            // 获取当前订单产品的赠品
            String prodPkgKey = matchCurrentRuleOrderProduct.get(PROD_PKG_KEY, String.class);
            // 订单产品行匹配到了，多组赠品规则，取当前的规则的赠品
            List<IObjectData> giftProductList = parentGiftKeyToGiftProduct
                    .getOrDefault(prodPkgKey, Lists.newArrayList())
                    .stream()
                    .filter(giftProduct -> giftProduct.get(RULE_IDS, List.class, Lists.newArrayList())
                            .contains(rule.getId()))
                    .collect(Collectors.toList());
            giftExecutionResult.setGiftList(
                    replaceThisGift(matchCurrentRuleOrderProduct, originalGiftLine, multiUnit, productUnitMap));
            giftCheck(objectApiName, masterData, aggValueMap, detailFieldDescribe, giftExecutionResult,
                    matchCurrentRuleOrderProduct, giftProductList, cycleInfo, progressiveInfoMaps, rule,
                    percentileSourceValue);
        }
    }

    private Map<String, String> findProductUnit(List<String> productIds) {
        String tenantId = RequestContextManager.getContext().getTenantId();
        List<IObjectData> productDataList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, productIds,
                SFAPreDefine.Product.getApiName());
        return productDataList.stream().collect(
                Collectors.toMap(DBRecord::getId, x -> x.get(ProductConstants.UNIT, String.class, ""), (v1, v2) -> v2));
    }

    /**
     * 替换这个礼物
     * 替换本品
     *
     * @param matchCurrentRuleOrderProduct 匹配到规则产品行
     * @param originalGiftLine             原始赠品行
     * @param multiUnit                    多单位
     * @param productUnitMap
     * @return {@code List<GiftLine>}
     */
    private List<PriceRule.GiftLine> replaceThisGift(IObjectData matchCurrentRuleOrderProduct,
            List<PriceRule.GiftLine> originalGiftLine, HashBasedTable<String, String, String> multiUnit,
            Map<String, String> productUnitMap) {
        if (CollectionUtils.empty(originalGiftLine)) {
            return originalGiftLine;
        }
        boolean hasThisGift = originalGiftLine.stream().anyMatch(x -> PRICEPOLICY_THISPRODUCT.equals(x.getProductId()));
        // 没有赠本品情况
        if (!hasThisGift) {
            return originalGiftLine;
        }
        String productId = matchCurrentRuleOrderProduct
                .get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class);
        Map<String, String> sizeUnitToUnitId = Maps.newHashMap();
        // 查询多单位信息
        if (configService.isMultipleUnit(RequestContextManager.getContext().getTenantId())) {
            sizeUnitToUnitId.putAll(multiUnit.row(productId));
        }
        List<PriceRule.GiftLine> newGiftLineList = Lists.newArrayList();
        String productName = PricePolicyValidator.getProductName(matchCurrentRuleOrderProduct);
        for (PriceRule.GiftLine giftLine : originalGiftLine) {
            if (PRICEPOLICY_THISPRODUCT.equals(giftLine.getProductId())) {
                giftLine = BeanUtil.copy(PriceRule.GiftLine.class, giftLine);
                giftLine.setProductId(productId);
                giftLine.setProductName(productName);
                // 开启了多单位，进行单位替换
                if (configService.isMultipleUnit(RequestContextManager.getContext().getTenantId())) {
                    String unitId = giftLine.getUnitId();
                    if (SizeUnit.SMALL.getUnitId().equals(unitId)
                            || SizeUnit.MEDIUM.getUnitId().equals(unitId)
                            || SizeUnit.LARGE.getUnitId().equals(unitId)) {
                        // 没有查到多单位信息，说明这个本品不是一个多单位产品，但是却配置了大中小，提示错误信息
                        String unit = sizeUnitToUnitId.get(unitId);
                        if (unit == null) {
                            throw new ValidateException(I18N.text(
                                    SFAI18NKeyUtil.SFA_PRICE_POLICY_SAVE_VALIDATE_THIS_PRODUCT_NO_HAVA_UNIT_TYPE,
                                    I18N.text("MultiUnitRelatedObj.field.unit_type.option." + unitId)));
                        }
                        giftLine.setUnitId(unit);
                    } else if (SizeUnit.CURRENT_PRODUCT_UNIT.getUnitId().equals(unitId)) {
                        giftLine.setUnitId(matchCurrentRuleOrderProduct
                                .get(CouponConstants.PluginDetailField.ACTUAL_UNIT, String.class));
                    } else if (SizeUnit.BASE_UNIT.getUnitId().equals(unitId)) {
                        String unit = productUnitMap.get(giftLine.getProductId());
                        giftLine.setUnitId(unit);
                    }
                }
            }
            newGiftLineList.add(giftLine);
        }
        return newGiftLineList;
    }

    private HashBasedTable<String, String, String> findMultiUnit(List<String> productIds) {
        HashBasedTable<String, String, String> productIdToSizeUnitToMultipleUnit = HashBasedTable.create();
        List<IObjectData> multiUnitInfoListByProduct = multiUnitService
                .getMultiUnitInfoListByProduct(RequestContextManager.getContext().getTenantId(), productIds);
        if (CollectionUtils.empty(multiUnitInfoListByProduct)) {
            return productIdToSizeUnitToMultipleUnit;
        }
        for (IObjectData objectData : multiUnitInfoListByProduct) {
            String productId = objectData.get("product_id", String.class);
            String unitType = objectData.get("unit_type", String.class);
            String unitId = objectData.get("unit_id", String.class);
            if (StringUtils.isNotBlank(unitType) && StringUtils.isNotBlank(unitId)) {
                productIdToSizeUnitToMultipleUnit.put(productId, unitType, unitId);
            }
            if (Boolean.TRUE.equals(objectData.get("is_base", Boolean.class))) {
                productIdToSizeUnitToMultipleUnit.put(productId, SizeUnit.BASE_UNIT.getUnitId(), unitId);
            }
        }
        return productIdToSizeUnitToMultipleUnit;
    }

    private void masterGiftCheck(String objectApiName, IObjectData masterData,
            Map<String, List<IObjectData>> parentGiftKeyToGiftProduct,
            PricePolicyRule rule, Map<String, String> aggValueMap, Map<String, IFieldDescribe> detailFieldDescribe,
            PriceRule.GiftRule giftExecutionResult, Map<String, List<PriceRule.ProgressiveInfo>> progressiveInfoMaps) {
        if (policyIsMatchData(rule.getPricePolicyId(), rule.getId(), masterData)) {
            // master 匹配到同一个政策，不同组的赠品规则，取当前规则的赠品
            List<IObjectData> giftProductList = parentGiftKeyToGiftProduct
                    .getOrDefault(ModifyType.MASTER, Lists.newArrayList())
                    .stream()
                    .filter(giftProduct -> giftProduct.get(RULE_IDS, List.class, Lists.newArrayList())
                            .contains(rule.getId()))
                    .collect(Collectors.toList());
            giftCheck(objectApiName, masterData, aggValueMap, detailFieldDescribe, giftExecutionResult, null,
                    giftProductList, Optional.empty(), progressiveInfoMaps, rule, Optional.empty());
        }
    }

    private void giftCheck(String objectApiName, IObjectData masterData, Map<String, String> aggValueMap,
            Map<String, IFieldDescribe> detailFieldDescribe,
            PriceRule.GiftRule giftExecutionResult, IObjectData matchCurrentRuleOrderProduct,
            List<IObjectData> giftProductList, Optional<Double> cycleInfo,
            Map<String, List<PriceRule.ProgressiveInfo>> progressiveInfoMaps,
            PricePolicyRule rule, Optional<Tuple<BigDecimal, BigDecimal>> percentileSourceValue) {
        List<PriceRule.ProgressiveInfo> progressiveInfos = null;
        if (rule.isProgressive()) {
            String key = rule.getPricePolicyId()
                    + (null == matchCurrentRuleOrderProduct ? PricePolicyConstants.MASTER_INDEX
                            : matchCurrentRuleOrderProduct.get(DATA_INDEX, String.class));
            progressiveInfos = progressiveInfoMaps.get(key);
            if (CollectionUtils.empty(progressiveInfos)) {
                progressiveInfos = ProgressiveRuleService.getProgressiveInfos(rule);
                progressiveInfoMaps.put(key, progressiveInfos);
            }
        }
        // 计算每满
        Optional<Double> cycle = cycleInfo.isPresent() ? cycleInfo
                : simpleComputeCycle(objectApiName, masterData, matchCurrentRuleOrderProduct, giftExecutionResult,
                        aggValueMap, progressiveInfos);
        Optional<Tuple<BigDecimal, BigDecimal>> percentileSourceValueOpt = percentileSourceValue.isPresent()
                ? percentileSourceValue
                : getPercentileSourceValue(objectApiName, masterData, matchCurrentRuleOrderProduct, giftExecutionResult,
                        aggValueMap);
        // 解析规则，判断规则是固定赠品，还是可选赠品
        PriceRule.GiftType type = giftExecutionResult.getType();
        Double cycleCount = 0d;
        if (cycle.isPresent()) {
            cycleCount = cycle.get();
            if (cycleCount.equals(0.0) && CollectionUtils.notEmpty(giftProductList)) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_CHECK_MATCHED_GIFT));
            }
        }
        // 赠品可能出现重复情况idToGiftProductMap,不可重复使用，在doFixTypeCheck，doOptionalTypeCheck中会更改此map
        Map<String, List<IObjectData>> idToGiftProductMap = giftProductList.stream()
                .collect(Collectors.groupingBy(data -> data
                        .get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class)));
        if (configService.isOpenPricePolicyPercentile(RequestContextManager.getContext().getTenantId())) {
            GiftSaveCheckService.Context context = GiftSaveCheckService.Context.builder()
                    .giftProductList(giftProductList)
                    .cycleCount(cycle.orElse(null))
                    .idToGiftProductMap(idToGiftProductMap)
                    .giftExecutionResult(giftExecutionResult)
                    .detailFieldDescribe(detailFieldDescribe)
                    .matchCurrentRuleOrderProduct(matchCurrentRuleOrderProduct)
                    .percentileSourceValue(percentileSourceValueOpt.orElse(null))
                    .build();
            GiftSaveCheckService service = giftCheckManager.getService(type);
            // 赠品校验核心方法
            service.check(context);
        } else {
            // 灰度阶段，依旧使用原来校验逻辑，新校验走灰度逻辑，之后考虑去掉此部分
            if (PriceRule.GiftType.FIX.equals(type)) {
                // 是固定赠品的赠送赠品是不是全部存在，并且数量和条件对应，每满情况
                doFixTypeCheck(giftExecutionResult, idToGiftProductMap, cycle, detailFieldDescribe,
                        matchCurrentRuleOrderProduct);
                checkGiftLineTotal(giftExecutionResult, giftProductList);
            } else if (PriceRule.GiftType.OPTIONAL.equals(type)) {
                // 是可选赠品，是指定赠品，还是指定范围赠送，还是两者都有，判断是否必选，最大数量，最小数量，赠品总量
                doOptionalTypeCheck(giftExecutionResult, idToGiftProductMap, giftProductList, cycle,
                        detailFieldDescribe, matchCurrentRuleOrderProduct);
            }
        }

        if (rule.isProgressive() && !PricePolicyRuleType.COMBINATION_GIFT.getRuleType().equals(rule.getRuleType())) {
            List<PriceRule.ProgressiveInfo> progressiveThis = ProgressiveRuleService.getProgressiveInfos(rule);
            ProgressiveRuleService.addUseProgressiveInfo(cycleCount, progressiveInfos, progressiveThis);
        }
    }

    /**
     * 校验赠品行数量
     *
     * @param giftExecutionResult 赠品规则
     * @param giftProductList     赠品行
     */
    private void checkGiftLineTotal(PriceRule.GiftRule giftExecutionResult, List<IObjectData> giftProductList) {
        // giftProductList 根据product_id分组，获取行数量
        Map<String, List<IObjectData>> idToGiftProductMap = giftProductList.stream()
                .collect(Collectors.groupingBy(data -> data
                        .get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class)));
        List<PriceRule.GiftLine> giftList = giftExecutionResult.getGiftList().stream().filter
                (giftLine -> !BigDecimal.ZERO.equals(giftLine.getMaxValue())).collect(Collectors.toList());
        Map<String, List<PriceRule.GiftLine>> productIdToGiftProducts = giftList.stream()
                .collect(Collectors.groupingBy(PriceRule.GiftLine::getProductId));
        productIdToGiftProducts.forEach((productId, giftLines) -> {
            List<IObjectData> giftProducts = idToGiftProductMap.get(productId);
            if (CollectionUtils.notEmpty(giftProducts) || CollectionUtils.notEmpty(giftLines)) {
                // 赠品行数量
                int giftProductSize = giftProducts.size();
                // 赠品规则中的数量
                int giftLineSize = giftLines.size();
                if (giftProductSize > giftLineSize) {
                    throw new ValidateException(
                            I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_CHECK_GIFT_LINE_TOTAL,
                                    PricePolicyValidator.getProductName(giftProducts.get(0))),
                            PricePolicyCheckCode.GIFT_LINE_TOTAL);
                }
            }
        });
    }

    private void doOptionalTypeCheck(PriceRule.GiftRule giftExecutionResult,
            Map<String, List<IObjectData>> idToGiftProductMap, List<IObjectData> giftProductList,
            Optional<Double> cycle, Map<String, IFieldDescribe> detailFieldDescribe,
            IObjectData matchCurrentRuleOrderProduct) {
        Optional.ofNullable(giftExecutionResult.getGiftKindUpperLimit()).ifPresent(giftKindUpperLimit -> {
            if (giftProductList.size() > giftKindUpperLimit) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_GIFT_TYPE_LIMIT),
                        PricePolicyCheckCode.GIFT_TYPE_LIMIT);
            }
        });
        NumberFieldDescribe fieldDescribe = (NumberFieldDescribe) detailFieldDescribe
                .get(SalesOrderConstants.SalesOrderProductField.QUANTITY.getApiName());
        // 校验总数量这里没有放在循环内，是因为可能有指定赠品，和指定范围，这两个要进行累加
        Optional.ofNullable(giftExecutionResult.getGiftTotalNum())
                .filter(StringUtils::isNotEmpty)
                .map(BigDecimal::new)
                .ifPresent(giftTotalNum -> {
                    // 存在每满情况，进行累乘
                    if (cycle.isPresent()) {
                        giftTotalNum = giftTotalNum.multiply(new BigDecimal(cycle.get().toString()));
                    }
                    // 端上如果赠品存在小数，都是先用原值计算，计算完事在进行截断
                    giftTotalNum = giftTotalNum.setScale(fieldDescribe.getDecimalPlaces(), BigDecimal.ROUND_DOWN);
                    BigDecimal sumQuantity = giftProductList.stream()
                            .map(x -> x.get(SalesOrderConstants.SalesOrderProductField.QUANTITY.getApiName(),
                                    BigDecimal.class, BigDecimal.ZERO))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (BigDecimalUtils.compare(sumQuantity, Operator.GT, giftTotalNum)) {
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_GIFT_TOTAL_LIMIT),
                                PricePolicyCheckCode.GIFT_TOTAL_LIMIT);
                    }
                });
        List<PriceRule.GiftLine> giftList = giftExecutionResult.getGiftList();
        // 指定赠品不为空，校验指定赠品
        if (CollectionUtils.notEmpty(giftList)) {
            checkGiftLine(idToGiftProductMap, cycle, fieldDescribe, giftList);
        }
        // 指定赠品范围不为空，这里校验需要查询产品列表
        if (StringUtils.isNotBlank(giftExecutionResult.getGiftCondition())) {
            checkGiftCondition(idToGiftProductMap, giftList, giftExecutionResult);
        }
    }

    private void checkGiftCondition(Map<String, List<IObjectData>> idToGiftProductMap,
            List<PriceRule.GiftLine> giftList, PriceRule.GiftRule giftExecutionResult) {
        // 2、根据giftCondition创建filter，并且增加条件，productId，进行查询
        Set<String> giftProductIds = getConditionProductIds(idToGiftProductMap, giftList);
        Set<String> sourceProductIds = Sets.newHashSet(giftProductIds);
        // 为空说明没有从条件范围中添加赠品
        if (CollectionUtils.empty(giftProductIds)) {
            return;
        }
        List<PriceRuleWhere> conditionList = JSON.parseArray(giftExecutionResult.getGiftCondition(),
                PriceRuleWhere.class);
        if (CollectionUtils.empty(conditionList)) {
            return;
        }
        SearchTemplateQueryPlus searchTemplateQueryPlus = new SearchTemplateQueryPlus();
        // 目前只支持一个condition.如果是多个的是，需要使用where
        for (PriceRuleWhere priceRuleWhere : conditionList) {
            List<PriceRuleFilter> filters = priceRuleWhere.getFilters();
            if (CollectionUtils.empty(filters)) {
                continue;
            }
            for (PriceRuleFilter filter : filters) {
                searchTemplateQueryPlus.addFilter(filter.getFieldName(), filter.getOperator(), filter.getFieldValues());
            }
        }
        // 添加id，减少查询数据量
        searchTemplateQueryPlus.addFilter(DBRecord.ID, Operator.HASANYOF, Lists.newArrayList(giftProductIds));
        searchTemplateQueryPlus.setLimit(giftProductIds.size());
        RequestContext context = RequestContextManager.getContext();
        // 处理分类逻辑
        productCategoryBizService.handleNewCategoryListFilters(context.getUser(),
                new SearchTemplateQuery().toJsonString(), searchTemplateQueryPlus);
        productService.handleCategoryFilters(context.getTenantId(), context.getUser().getUpstreamOwnerIdOrUserId(),
                searchTemplateQueryPlus.getFilters());
        Optional.ofNullable(serviceFacade.findBySearchQueryWithFieldsIgnoreAll(context.getUser(),
                SFAPreDefine.Product.getApiName(), searchTemplateQueryPlus, Lists.newArrayList(DBRecord.ID)))
                .map(QueryResult::getData)
                .ifPresent(data -> {
                    // 3、判断返回值结果，是否有少于收集productId，如果少于，则证明这个赠品行，可能不是再此giftCondition范围
                    Set<String> queryProductIds = data.stream().map(IObjectData::getId).collect(Collectors.toSet());
                    giftProductIds.removeAll(queryProductIds);
                    if (CollectionUtils.notEmpty(giftProductIds)) {
                        String name = getProductName(giftProductIds, idToGiftProductMap);
                        throw new ValidateException(
                                I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_GIFT_NOT_MATCH_CONDITION, name),
                                PricePolicyCheckCode.GIFT_NOT_MATCH_CONDITION);
                    }
                });
        boolean openMultipleUnit = configService.isMultipleUnit(RequestContextManager.getContext().getTenantId());
        if (!openMultipleUnit) {
            return;
        }
        String giftConditionUnitId = giftExecutionResult.getGiftConditionUnitId();
        // 基准单位不校验,单位为空不校验
        if (StringUtils.isBlank(giftConditionUnitId) || SizeUnit.BASE_UNIT.getUnitId().equals(giftConditionUnitId)) {
            return;
        }
        LinkedHashMap<String, List<String>> parameter = Maps.newLinkedHashMap();
        for (String giftProductId : sourceProductIds) {
            parameter.computeIfAbsent(giftProductId, key -> Lists.newArrayList()).add(giftConditionUnitId);
        }
        CheckGiftUnit.Arg arg = CheckGiftUnit.Arg.builder().arg(parameter).build();
        User user = RequestContextManager.getContext().getUser();
        String tenantId = RequestContextManager.getContext().getTenantId();
        CheckGiftUnit.Result result = pricePolicyCoreService.checkGiftUnitResult(arg, tenantId, user);
        Map<String, Map<String, CheckGiftUnit.UnitInfo>> resultMap = result.getResult();
        if (resultMap.keySet().size() != sourceProductIds.size()) {
            throw new ValidateException(
                    I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_CHOSE_GIFT_UNIT_ERROR,
                            getProductName(sourceProductIds, idToGiftProductMap)),
                    PricePolicyCheckCode.CHOSE_GIFT_UNIT_ERROR);
        }
        for (String sourceProductId : sourceProductIds) {
            Map<String, CheckGiftUnit.UnitInfo> unitInfoMap = resultMap.get(sourceProductId);
            List<IObjectData> objectDataList = idToGiftProductMap.getOrDefault(sourceProductId, Lists.newArrayList());
            for (IObjectData objectData : objectDataList) {
                String giftUnit = objectData.get(CouponConstants.PluginDetailField.ACTUAL_UNIT, String.class, "");
                CheckGiftUnit.UnitInfo unitInfo = unitInfoMap.get(giftConditionUnitId);
                if (!unitInfo.getUnitId().equals(giftUnit)) {
                    throw new ValidateException(
                            I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_CHOSE_GIFT_UNIT_ERROR,
                                    getProductName(sourceProductIds, idToGiftProductMap)),
                            PricePolicyCheckCode.CHOSE_GIFT_UNIT_ERROR);
                }
            }
        }
    }

    private String getProductName(Set<String> giftProductIds, Map<String, List<IObjectData>> idToGiftProductList) {
        StringBuilder errorName = new StringBuilder();
        for (String giftProductId : giftProductIds) {
            List<IObjectData> objectDataList = idToGiftProductList.get(giftProductId);
            if (CollectionUtils.empty(objectDataList)) {
                continue;
            }
            errorName.append(PricePolicyValidator.getProductName(objectDataList.get(0))).append(",");
        }
        errorName.deleteCharAt(errorName.lastIndexOf(","));
        return errorName.toString();
    }

    /**
     * 如果giftList为空，则是全部赠品行 都是按照范围添加的
     * 如果giftList不为空，找到不在giftList 中的赠品行，即为指定范围赠品
     *
     * @param idToGiftProductMap id礼物产品列表
     * @param giftList           礼物清单
     * @return {@code Set<String>}
     */
    private Set<String> getConditionProductIds(Map<String, List<IObjectData>> idToGiftProductMap,
            List<PriceRule.GiftLine> giftList) {
        Set<String> giftProductIds = Sets.newHashSet();
        // 如果giftList为空，则是全部赠品行，收集productId
        if (CollectionUtils.empty(giftList)) {
            idToGiftProductMap.forEach((id, giftProductDataList) -> giftProductIds.addAll(
                    giftProductDataList.stream()
                            .map(x -> x.get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(),
                                    String.class))
                            .collect(Collectors.toList())));
            // 找到不再giftList 中的赠品行，收集productId
        } else {
            Map<String, List<PriceRule.GiftLine>> productIdToGiftLine = giftList.stream()
                    .collect(Collectors.groupingBy(PriceRule.GiftLine::getProductId));
            idToGiftProductMap.forEach((id, giftProductDataList) -> {
                if (CollectionUtils.empty(giftProductDataList)) {
                    return;
                }
                for (IObjectData objectData : giftProductDataList) {
                    String productId = objectData
                            .get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class);
                    if (CollectionUtils.empty(productIdToGiftLine.get(productId))) {
                        giftProductIds.add(productId);
                    }
                }
            });
        }
        return giftProductIds;
    }

    /**
     * 校验行
     *
     * @param idToGiftProductMap id礼物产品列表
     * @param cycle              周期
     * @param fieldDescribe      字段描述
     * @param giftList           礼物清单
     */
    private void checkGiftLine(Map<String, List<IObjectData>> idToGiftProductMap, Optional<Double> cycle,
            NumberFieldDescribe fieldDescribe, List<PriceRule.GiftLine> giftList) {
        boolean openMultipleUnit = configService.isMultipleUnit(RequestContextManager.getContext().getTenantId());
        // 增加价格政策赠品行计数器，只有赠品一个都不满足的时候才抛出异常，还有下一个的时候，不会抛出异常
        Multiset<String> giftLineCounter = HashMultiset.create();
        for (PriceRule.GiftLine giftLine : giftList) {
            giftLineCounter.add(giftLine.getProductId());
        }
        for (PriceRule.GiftLine giftLine : giftList) {
            // 赠品行计数器 -1
            giftLineCounter.setCount(giftLine.getProductId(), giftLineCounter.count(giftLine.getProductId()) - 1);
            int count = giftLineCounter.count(giftLine.getProductId());
            // 可能存在重复的赠品，如果存在重复的赠品，匹配到其中的一个，即可通过校验
            List<IObjectData> currentGiftLineProduct = idToGiftProductMap.getOrDefault(giftLine.getProductId(),
                    Lists.newArrayList());
            // 如果当前赠品是可选的，一个都没有选，则通过校验
            if (CollectionUtils.empty(currentGiftLineProduct)) {
                // 必选赠品没有值
                if (Optional.ofNullable(giftLine.getRequired()).orElse(false)) {
                    throw new ValidateException(
                            I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_GIFT_MUST_HAVE, giftLine.getProductName()),
                            PricePolicyCheckCode.GIFT_MUST_HAVE);
                }
                continue;
            }
            BigDecimal maxValue = giftLine.getMaxValue();
            BigDecimal minValue = giftLine.getMinValue();
            // 最大值，最小值均没有值不校验
            if (maxValue == null && minValue == null) {
                if (openMultipleUnit) {
                    doChoseGiftUnitCheck(currentGiftLineProduct, giftLine, count);
                }
                continue;
            }
            boolean matchMaxValue = false;
            boolean matchMinValue = false;
            // 是否匹配多单位
            boolean hasUnitMatch = false;
            // 存在每满情况，进行累乘
            if (maxValue != null) {
                maxValue = multiCycleValue(cycle, fieldDescribe, maxValue);
            }
            if (minValue != null) {
                minValue = multiCycleValue(cycle, fieldDescribe, minValue);
            }
            /*
             * 规则赠品数量 明细赠品数量
             * 1-5 3
             * 2-3 9
             * 重复赠品，防止一直匹配一个赠品
             */
            IObjectData needDeleteData = null;
            for (IObjectData giftProduct : currentGiftLineProduct) {
                BigDecimal quantity = giftProduct.get(SalesOrderConstants.SalesOrderProductField.QUANTITY.getApiName(),
                        BigDecimal.class, BigDecimal.ZERO);
                matchMaxValue = isMatchValue(quantity, Operator.LTE, maxValue);
                matchMinValue = isMatchValue(quantity, Operator.GTE, minValue);
                if (openMultipleUnit) {
                    hasUnitMatch = isHasUnitMatch(giftLine, giftProduct);
                    if (matchMaxValue && matchMinValue && hasUnitMatch) {
                        needDeleteData = giftProduct;
                        break;
                    }
                } else {
                    // 都满足了停止循环
                    if (matchMaxValue && matchMinValue) {
                        needDeleteData = giftProduct;
                        break;
                    }
                }
            }
            if (!matchMaxValue && count == 0) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_GIFT_TOTAL_MORE_THEN_MAX_LIMIT,
                        giftLine.getProductName()), PricePolicyCheckCode.GIFT_TOTAL_MORE_THEN_MAX_LIMIT);
            }
            if (!matchMinValue && count == 0) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_GIFT_TOTAL_LESS_THEN_MAX_LIMIT,
                        giftLine.getProductName()), PricePolicyCheckCode.GIFT_TOTAL_LESS_THEN_MAX_LIMIT);
            }
            // 开了多单位，但是没有匹配到单位
            if (openMultipleUnit && !hasUnitMatch && count == 0) {
                throw new ValidateException(
                        I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_CHOSE_GIFT_UNIT_ERROR, giftLine.getProductName()),
                        PricePolicyCheckCode.CHOSE_GIFT_UNIT_ERROR);
            }
            currentGiftLineProduct.remove(needDeleteData);
        }
        // 可选的时候，会有范围赠送，则不会在价格政策赠品列表中，这里先将校验去掉
        // checkOrderGiftInGiftRange(idToGiftProductMap, giftList);
    }

    private void doChoseGiftUnitCheck(List<IObjectData> currentGiftLineProduct, PriceRule.GiftLine giftLine,
            int count) {
        // 是否匹配多单位
        boolean hasUnitMatch = false;
        IObjectData needDeleteData = null;
        for (IObjectData giftProduct : currentGiftLineProduct) {
            hasUnitMatch = isHasUnitMatch(giftLine, giftProduct);
            if (hasUnitMatch) {
                needDeleteData = giftProduct;
                break;
            }
        }
        // 开了多单位，但是没有匹配到单位
        if (!hasUnitMatch && count == 0) {
            throw new ValidateException(
                    I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_CHOSE_GIFT_UNIT_ERROR, giftLine.getProductName()),
                    PricePolicyCheckCode.CHOSE_GIFT_UNIT_ERROR);
        }
        currentGiftLineProduct.remove(needDeleteData);
    }

    private boolean isHasUnitMatch(PriceRule.GiftLine giftLine, IObjectData giftProduct) {
        String unitId = giftLine.getUnitId();
        // 没有单位,赋值为实际单位
        if (StringUtils.isBlank(unitId)) {
            unitId = giftProduct.get(CouponConstants.PluginDetailField.ACTUAL_UNIT, String.class, "");
        }
        String giftUnit = giftProduct.get(CouponConstants.PluginDetailField.ACTUAL_UNIT, String.class, "");
        return unitId.equals(giftUnit);
    }

    /**
     * 检查订单赠品，在价格政策赠品范围内
     *
     * @param idToGiftProductMap id礼物产品
     * @param giftList           赠品
     */
    private void checkOrderGiftInGiftRange(Map<String, List<IObjectData>> idToGiftProductMap,
            List<PriceRule.GiftLine> giftList) {
        if (CollectionUtils.empty(giftList)) {
            return;
        }
        Set<String> giftProductIds = giftList.stream()
                .map(PriceRule.GiftLine::getProductId)
                .collect(Collectors.toSet());
        Set<String> orderProductGiftIds = idToGiftProductMap.keySet();
        // 订单赠品中移除所有价格政策赠品
        orderProductGiftIds.removeAll(giftProductIds);
        if (CollectionUtils.notEmpty(orderProductGiftIds)) {
            String productNames = getProductName(orderProductGiftIds, idToGiftProductMap);
            throw new ValidateException(I18N
                    .text(SFAI18NKeyUtil.SFA_PRICE_POLICY_ORDER_PRODUCT_GIFT_NOT_IN_POLICY_GIFT_BY_NAME, productNames),
                    PricePolicyCheckCode.ORDER_PRODUCT_GIFT_NOT_IN_POLICY_GIFT);
        }
    }

    private BigDecimal multiCycleValue(Optional<Double> cycle, NumberFieldDescribe fieldDescribe, BigDecimal value) {
        if (cycle.isPresent()) {
            value = value.multiply(new BigDecimal(cycle.get().toString()));
        }
        // 端上如果赠品存在小数，都是先用原值计算，计算完事在进行截断
        value = value.setScale(fieldDescribe.getDecimalPlaces(), BigDecimal.ROUND_DOWN);
        return value;
    }

    private boolean isMatchValue(BigDecimal quantity, Operator operator, BigDecimal value) {
        if (value != null) {
            return BigDecimalUtils.compare(quantity, operator, value);
        } else {
            return true;
        }
    }

    private void doFixTypeCheck(PriceRule.GiftRule giftExecutionResult,
            Map<String, List<IObjectData>> idToGiftProductMap, Optional<Double> cycle,
            Map<String, IFieldDescribe> detailFieldDescribe, IObjectData matchCurrentRuleOrderProduct) {
        List<PriceRule.GiftLine> giftList = giftExecutionResult.getGiftList();
        boolean openMultipleUnit = configService.isMultipleUnit(RequestContextManager.getContext().getTenantId());
        // 循环规则中的固定赠品，匹配明细行中的赠品，因为是固定赠品，所以赠的什么和赠品数量都是固定的，不可以更换
        for (PriceRule.GiftLine giftLine : giftList) {
            if (null != giftLine.getMaxValue() && BigDecimal.ZERO.compareTo(giftLine.getMaxValue()) == 0) {
                continue;
            }
            String productId = giftLine.getProductId();
            List<IObjectData> giftProductList = idToGiftProductMap.get(productId);
            if (CollectionUtils.empty(giftProductList)) {
                // 如果这个产品更换了名称，giftLine.getProductName() 取值可能不太对，需要从库里进行查询，这里先直接取值，之后看看需不需要查询
                throw new ValidateException(
                        I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_GIFT_LOSE, giftLine.getProductName()),
                        PricePolicyCheckCode.POLICY_GIFT_LOSE);
            }
            // 存在最大值，进行校验
            doMaxValueAndUnitCheck(cycle, detailFieldDescribe, openMultipleUnit, giftLine, giftProductList);
        }
        checkOrderGiftInGiftRange(idToGiftProductMap, giftList);
    }

    private void doMaxValueAndUnitCheck(Optional<Double> cycle, Map<String, IFieldDescribe> detailFieldDescribe,
            boolean openMultipleUnit, PriceRule.GiftLine giftLine, List<IObjectData> giftProductList) {
        Optional.ofNullable(giftLine.getMaxValue()).ifPresent(maxValue -> {
            // 存在每满情况，进行累乘
            if (cycle.isPresent()) {
                maxValue = maxValue.multiply(new BigDecimal(cycle.get().toString()));
            }
            NumberFieldDescribe fieldDescribe = (NumberFieldDescribe) detailFieldDescribe
                    .get(SalesOrderConstants.SalesOrderProductField.QUANTITY.getApiName());
            // 端上如果赠品存在小数，都是先用原值计算，计算完事在进行截断
            maxValue = maxValue.setScale(fieldDescribe.getDecimalPlaces(), BigDecimal.ROUND_DOWN);
            boolean hasMatchData = false;
            boolean hasUnitMatch = false;
            /*
             * 已经符合条件的赠品进行删除，防止重复的赠品一直匹配的是一个
             */
            IObjectData needDeleteData = null;
            // 存在多个相同的赠品，只要一个通过校验就通过了校验
            for (IObjectData giftProduct : giftProductList) {
                BigDecimal quantity = giftProduct.get(SalesOrderConstants.SalesOrderProductField.QUANTITY.getApiName(),
                        BigDecimal.class);
                hasMatchData = BigDecimalUtils.compare(quantity, Operator.EQ, maxValue);
                if (openMultipleUnit) {
                    hasUnitMatch = isHasUnitMatch(giftLine, giftProduct);
                    if (hasMatchData && hasUnitMatch) {
                        needDeleteData = giftProduct;
                        break;
                    }
                } else {
                    if (hasMatchData) {
                        needDeleteData = giftProduct;
                        break;
                    }
                }
            }
            if (!hasMatchData) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_GIFT_TOTAL_ERROR,
                        giftLine.getProductName(), maxValue), PricePolicyCheckCode.GIFT_TOTAL_ERROR);
            }
            // 开了多单位，但是没有匹配到单位
            if (openMultipleUnit && !hasUnitMatch) {
                throw new ValidateException(
                        I18N.text(SFAI18NKeyUtil.SFA_PRICE_POLICY_GIFT_UNIT_ERROR, giftLine.getProductName()),
                        PricePolicyCheckCode.GIFT_UNIT_ERROR);
            }
            giftProductList.remove(needDeleteData);
        });
    }
}
