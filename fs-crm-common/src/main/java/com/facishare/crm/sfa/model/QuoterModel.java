package com.facishare.crm.sfa.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface QuoterModel {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg {
        private String queryInfo;
        private String apiName;
        private boolean trial;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class DomainArg {
        private String pluginApiName;
    }

    class AdvancedFormulaModel {
        public static final String OBJECT_NAME = "ref_object_api_name";
        public static final String FIELD_NAME = "ref_field_name";
        public static final String FORMULA = "formula";
        public static final String PRODUCT_ID = "product_id";
        public static final String BOM_ID = "bom_id";
        public static final String ENABLED_TRIAL = "enabled_trial";
        public static final String ATTR_PREFIX = "EXT#ATTR#";
        public static final String NON_ATTR_PREFIX = "EXT#NON_ATTR#";
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class CalculateDataParam {
        //从对象apiName
        private String detailObjectApiName;
        //从对象数据(key数据编号)
        private Map<String, ObjectDataDocument> detailDataMap;
        //要计算的字段(key对象apiName和数据编号)
        private Map<String,Map<String,List<String>>> detailCalculateFieldApiNames;
        //主对象数据
        private ObjectDataDocument masterData;
        //字段映射
        private Map<String, String> fieldsMap;
        //页面id，对于同一个新建/编辑页调用的请求，都用同一个id
        private String seriesId;
    }
}
