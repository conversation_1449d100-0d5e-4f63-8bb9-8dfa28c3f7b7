package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Objects;

import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SFA_OBJECT_DESCRIBE_NOT_EXISTS;

@Service
@Slf4j
public class MetaDataFindServiceExt {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private ObjectDataProxy dataProxy;
    @Autowired
    private MetaDataService metaDataService;

    public QueryResult<IObjectData> findBySearchQuery(User user, String objectApiName, SearchTemplateQuery searchTemplateQueryDeep) {
        return findBySearchQueryWithFields(user, objectApiName, searchTemplateQueryDeep, null, false);
    }

    public QueryResult<IObjectData> findBySearchQueryIgnoreAll(User user, String objectApiName, SearchTemplateQuery searchTemplateQueryDeep) {
        return findBySearchQueryWithFields(user, objectApiName, searchTemplateQueryDeep, null, true);
    }

    public QueryResult<IObjectData> findBySearchQueryWithFields(User user, String objectApiName, SearchTemplateQuery searchTemplateQueryDeep, List<String> fieldList, boolean ignoreAll) {
        IActionContext actionContext = ActionContextExt.of(user).skipRelevantTeam().getContext();
        actionContext.put("needJoinDept", false);
        QueryResult<IObjectData> result = new QueryResult<>();
        boolean emptyFields = CollectionUtils.empty(fieldList);
//        SearchTemplateQueryExt.of(searchTemplateQueryDeep).addDeletedFilterIfNoDeletedFilter();
        if (searchTemplateQueryDeep.getLimit() <= 1000 && searchTemplateQueryDeep.getLimit() != 0) {
            if (emptyFields) {
                result = ignoreAll ? serviceFacade.findBySearchQueryIgnoreAll(user, objectApiName, searchTemplateQueryDeep) : serviceFacade.findBySearchQuery(user, objectApiName, searchTemplateQueryDeep);
            } else {
                result = ignoreAll ? serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, objectApiName, searchTemplateQueryDeep, fieldList) : serviceFacade.findBySearchTemplateQueryWithFields(actionContext, objectApiName, searchTemplateQueryDeep, fieldList);
            }
            return result;
        }

        SearchTemplateQuery searchTemplateQueryDeepCopy = new SearchTemplateQuery();
        try {
            BeanUtils.copyProperties(searchTemplateQueryDeepCopy, searchTemplateQueryDeep);
        } catch (Exception e) {
            log.error("MetaDataFindServiceExt error tenantId:{},objectApiName:{}", user.getTenantId(), objectApiName, e);
            searchTemplateQueryDeepCopy = searchTemplateQueryDeep;
        }

        int limit = searchTemplateQueryDeepCopy.getLimit() == 0 ? Integer.MAX_VALUE : searchTemplateQueryDeepCopy.getLimit();
        int size = 2000;
        searchTemplateQueryDeepCopy.setLimit(size);
        int offset = searchTemplateQueryDeepCopy.getOffset();
        int sumTotalNumber = 0;
        int eachTotalNumber = size;
        List<IObjectData> data = Lists.newArrayList();
        if (CollectionUtils.empty(searchTemplateQueryDeepCopy.getOrders())) {
            searchTemplateQueryDeepCopy.setOrders(Lists.newArrayList(SearchUtil.orderByDataId()));
        }

        IObjectDescribe objectDescribe = null;
        for (; offset <= limit && eachTotalNumber == size; offset += size) {
            searchTemplateQueryDeepCopy.setOffset(offset);
            QueryResult<IObjectData> searchResult;
            if (emptyFields) {
                searchResult = ignoreAll ? queryDataListIgnoreAll(user, objectApiName, searchTemplateQueryDeepCopy) : serviceFacade.findBySearchQuery(user, objectApiName, searchTemplateQueryDeepCopy);
            } else {
                searchResult = ignoreAll ? serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, objectApiName, searchTemplateQueryDeepCopy, fieldList) : serviceFacade.findBySearchTemplateQueryWithFields(actionContext, objectApiName, searchTemplateQueryDeepCopy, fieldList);
            }
            if (null != searchResult) {
                if (!ignoreAll) {
                    if (null == objectDescribe) {
                        objectDescribe = serviceFacade.findObject(user.getTenantId(), objectApiName);
                    }
                    infraServiceFacade.fillQuoteFieldValue(user, searchResult.getData(), objectDescribe, null, false);
                }
                sumTotalNumber = searchResult.getTotalNumber();
                data.addAll(searchResult.getData());
                eachTotalNumber = searchResult.getData().size();
            }
        }

        result.setData(data);
        result.setTotalNumber(sumTotalNumber);
        return result;
    }

    private QueryResult<IObjectData> queryDataListIgnoreAll(User user, String objectApiName, SearchTemplateQuery searchTemplateQuery) {
        IActionContext context = ActionContextExt.of(user).allowUpdateInvalid(false).disableDeepQuote().skipRelevantTeam().getContext();
        context.setDoCalculate(false);
        context.put(ActionContextKey.NEED_JOIN_DEPT, false);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setNeedReturnQuote(false);
        return serviceFacade.findBySearchQuery(context, objectApiName, searchTemplateQuery);
    }

    public void dealDataByTemplate(User user, String objectApiName, SearchTemplateQuery templateQuery, ProcessInSearch method) {
        dealDataByTemplate(user, 200, objectApiName, templateQuery, method);
    }

    public void dealDataByTemplate(User user, int limitSize, String objectApiName, SearchTemplateQuery templateQuery, ProcessInSearch method) {
        if (StringUtils.isBlank(objectApiName)) {
            return;
        }
        if (templateQuery == null) {
            return;
        }
        templateQuery.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, true)));
        templateQuery.setLimit(limitSize);
        //跳过数据权限
        templateQuery.setPermissionType(0);
        //不返回总记录数
        templateQuery.setNeedReturnCountNum(false);
        // 每次查询个数
        int limit = limitSize;
        // 当前循环次数
        int loop = 0;
        int queryDataSize;
        do {
            templateQuery.setOffset(loop++ * limit);
            List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, objectApiName, templateQuery).getData();
            queryDataSize = dataList.size();
            method.invoke(dataList);
        } while (queryDataSize >= limit);
    }

    public List<IObjectData> bulkUpdateByFields(User user, List<IObjectData> dataList, List<String> updateFieldList) {
        List<IObjectData> rst = Lists.newArrayList();
        List<List<IObjectData>> partition = Lists.partition(dataList, 200);
        for (List<IObjectData> list : partition) {
            List<IObjectData> rstData = serviceFacade.batchUpdateByFields(user, list, updateFieldList);
            rst.addAll(rstData);
        }
        return rst;
    }

    public void bulkUpdateByFields(String tenantId, List<IObjectData> dataList, List<String> updateFieldList) {
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        bulkUpdateByFields(user, dataList, updateFieldList);
    }

    public Long getObjectCountNumber(User user, String objectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        query.setFilters(filters);
        CountFieldDescribe count = new CountFieldDescribe();
        count.setApiName(objectApiName);
        count.setFieldApiName("count0");
        count.setCountFieldApiName("id");
        count.setSubObjectDescribeApiName(objectApiName);
        count.setCountType(Count.TYPE_COUNT);
        count.setReturnType("number");
        count.setDecimalPlaces(0);
        Long result = null;
        try {
            Object calculateValue = dataProxy.getCountResult(user.getTenantId(), count.getApiName(), count, query);
            if (!Objects.isNull(calculateValue)) {
                result = Long.parseLong(calculateValue.toString());
            }
        } catch (MetadataServiceException e) {
            log.error("get object count error, tenant:{}, apiName:{}", user.getTenantId(), objectApiName, e);
        }
        return result;
    }

    public IObjectData findObjectData(User user, String id, String objectApiName) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        SearchUtil.fillFilterEq(searchTemplateQuery.getFilters(), DBRecord.ID, id);
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, objectApiName, searchTemplateQuery).getData();
        if (CollectionUtils.empty(dataList)) {
            return null;
        }
        return dataList.get(0);
    }

    public IObjectData findObjectByIdIgnoreAll(User user, String dataId, String objectApiName) {
        if (StringUtils.isAnyBlank(dataId, objectApiName)) {
            log.warn("findObjectByIdIgnoreAll param error, dataId:{}, objectApiName:{}", dataId, objectApiName);
            return null;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterEq(filters, DBRecord.ID, dataId);
        List<IObjectData> dataList = findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0);
        }
        return null;
    }

    public IObjectData findObjectByIdIgnoreAll(String tenantId, String dataId, String objectApiName) {
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        return findObjectByIdIgnoreAll(user, dataId, objectApiName);
    }

    public IObjectData findObjectByNameIgnoreAll(User user, String name, String objectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterEq(filters, "name", name);
        List<IObjectData> dataList = findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0);
        }
        return null;
    }

    public List<IObjectData> findObjectByIdsIgnoreAll(User user, List<String> ids, String objectApiName) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter
                > filters = query.getFilters();
        SearchUtil.fillFilterIn(filters, "_id", ids);
        query.setLimit(0);
        return findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
    }


    public List<IObjectData> findObjectByIdsWithFieldsIgnoreAll(User user, List<String> ids, String objectApiName, List<String> fields) {
        if (CollectionUtils.empty(ids) || CollectionUtils.empty(fields)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterIn(filters, "_id", ids);
        query.setLimit(0);
        return findBySearchQueryWithFieldsIgnoreAll(user, objectApiName, query, fields);
    }

    public List<IObjectData> findAllObjectIgnoreAll(User user, String objectApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1000);
        // 正序
        query.setOrders(Lists.newArrayList(new OrderBy(DBRecord.CREATE_TIME, true)));
        return findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
    }

    public IObjectData findObjectByField(String tenantId, String objectApiName, String field, String value) {
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        return findObjectByField(user, objectApiName, field, value);
    }

    public IObjectData findObjectByField(User user, String objectApiName, String field, String value) {
        if (StringUtils.isBlank(field) || StringUtils.isBlank(value)) {
            return null;
        }
        int limit = 1;
        SearchTemplateQuery query = buildSearchQueryByField(field, Lists.newArrayList(value), limit);
        if (query == null) {
            return null;
        }
        QueryResult<IObjectData> searchData = serviceFacade.findBySearchQuery(user, objectApiName, query);
        List<IObjectData> dataList = searchData.getData();
        if (CollectionUtils.notEmpty(dataList)) {
            return dataList.get(0);
        }
        return null;
    }

    private SearchTemplateQuery buildSearchQueryByField(String field, List<String> values, int limit) {
        values.remove("");
        if (StringUtils.isBlank(field) || org.apache.commons.collections.CollectionUtils.isEmpty(values)) {
            return null;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        IFilter filter = new Filter();
        filter.setFieldName(field);
        if (values.size() == 1) {
            filter.setOperator(Operator.EQ);
        } else {
            filter.setOperator(Operator.IN);
        }
        filter.setFieldValues(values);
        query.setFilters(Lists.newArrayList(filter));
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setLimit(limit);
        return query;
    }

    /**
     * 最多查 50w 条数据
     *
     * @param user
     * @param objectApiName
     * @param query
     * @param fieldApiNames
     * @return
     */
    public List<IObjectData> findBySearchQueryWithFieldsIgnoreAll(User user, String objectApiName, SearchTemplateQuery query, List<String> fieldApiNames) {
        if (StringUtils.isBlank(objectApiName)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.empty(fieldApiNames)) {
            return Lists.newArrayList();
        }
        if (query == null) {
            return Lists.newArrayList();
        }
        List<IObjectData> result = Lists.newArrayList();
        // 每次查询个数
        int limitSize = 500;
        query.setLimit(limitSize);
        query.setNeedReturnCountNum(false);
        query.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, true)));
        // 当前循环次数
        int loop = 0;
        int queryDataSize;
        // 最多循环10次
        do {
            query.setOffset(loop++ * limitSize);
            List<IObjectData> dataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, objectApiName, query, fieldApiNames).getData();
            queryDataSize = dataList.size();
            result.addAll(dataList);
        } while (queryDataSize >= limitSize && loop < 1000);
        return result;
    }

    public List<IObjectData> bulkSaveObjectData(List<IObjectData> createDataList, User user) {
        if (CollectionUtils.empty(createDataList)) {
            return Lists.newArrayList();
        }
        List<IObjectData> result = Lists.newArrayList();
        List<List<IObjectData>> partition = Lists.partition(createDataList, 100);
        for (List<IObjectData> list : partition) {
            List<IObjectData> dataList = metaDataService.bulkSaveObjectData(list, user);
            result.addAll(dataList);
        }
        return result;
    }

    public List<IObjectData> findObjectByIds(User user, List<String> ids, String objectApiName) {
        if (CollectionUtils.empty(ids) || StringUtils.isBlank(objectApiName)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterIn(filters, "_id", ids);
        query.setLimit(0);
        return findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
    }

    /**
     * 限制 500 条数据
     *
     * @param user
     * @param ids
     * @param objectApiName
     * @return
     */
    public List<IObjectData> findObjectByIdsWithHtml(User user, List<String> ids, String objectApiName) {
        if (CollectionUtils.empty(ids) || StringUtils.isBlank(objectApiName)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterIn(filters, "_id", ids);
        query.setLimit(500);
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), objectApiName);
        if (describe == null) {
            throw new ValidateException(I18N.text(SFA_OBJECT_DESCRIBE_NOT_EXISTS, objectApiName));
        }
        return serviceFacade.findBySearchQuery(user, describe, objectApiName, query, false, false, true, true).getData();
    }

    /**
     * 限制 500 条数据
     *
     * @param user
     * @param id
     * @param objectApiName
     * @return
     */
    public @Nullable IObjectData findObjectByIdWithHtml(User user, String id, String objectApiName) {
        if (org.apache.commons.lang3.StringUtils.isAnyBlank(id, objectApiName)) {
            return null;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterEq(filters, "_id", id);
        query.setLimit(1);
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), objectApiName);
        if (describe == null) {
            throw new ValidateException(I18N.text(SFA_OBJECT_DESCRIBE_NOT_EXISTS, objectApiName));
        }
        List<IObjectData> objectDataList = serviceFacade.findBySearchQuery(user, describe, objectApiName, query, false, false, true, true).getData();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(objectDataList)) {
            return objectDataList.get(0);
        }
        return null;
    }

    public List<IObjectData> findObjectByQueryWithHtml(User user, String objectApiName, SearchTemplateQuery query) {
        if (StringUtils.isBlank(objectApiName)) {
            return Lists.newArrayList();
        }
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), objectApiName);
        if (describe == null) {
            throw new ValidateException(I18N.text(SFA_OBJECT_DESCRIBE_NOT_EXISTS, objectApiName));
        }
        return serviceFacade.findBySearchQuery(user, describe, objectApiName, query, false, false, true, true).getData();
    }
}
