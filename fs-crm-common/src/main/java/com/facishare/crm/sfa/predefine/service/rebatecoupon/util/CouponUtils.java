package com.facishare.crm.sfa.predefine.service.rebatecoupon.util;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.constants.RebateConstants;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.RuleEngineLogicService;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.dao.CouponDao;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponQuery;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RuleWhere;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.translate.TranslateManager;
import com.facishare.crm.sfa.utilities.util.PricePolicyUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券工具类
 *
 * <AUTHOR>
 * @date 2021/12/21
 */
@Slf4j
public class CouponUtils extends CouponBaseUtils{
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private static final RuleEngineLogicService ruleEngineLogicService = SpringUtil.getContext().getBean(RuleEngineLogicService.class);
    private static final TranslateManager translateManager = SpringUtil.getContext().getBean(TranslateManager.class);
    private static final ObjectDataServiceImpl ObjectDataServiceImpl = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private static final BizConfigThreadLocalCacheService config = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    private static final CouponDao couponDao = SpringUtil.getContext().getBean(CouponDao.class);


    /**
     * 有优惠券实例
     *
     * @param user       用户
     * @param objectData 对象数据
     * @return boolean
     */
    public static boolean hasCouponInstance(User user, IObjectData objectData) {
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        ext.addFilter(Operator.EQ, CouponConstants.CouponInstanceField.COUPON_PLAN_ID.getApiName(), objectData.getId());
        ext.addFilter(Operator.IN, DBRecord.IS_DELETED, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()), String.valueOf(DELETE_STATUS.NORMAL.getValue())));
        ext.setLimit(1);
        ext.setOffset(0);
        QueryResult<IObjectData> couponInstanceDates = serviceFacade.findBySearchQueryIgnoreAll(user, CouponConstants.COUPON_INSTANCE_API_NAME, (SearchTemplateQuery) ext.getQuery());
        return couponInstanceDates != null && CollectionUtils.notEmpty(couponInstanceDates.getData());
    }

    /**
     * 有优惠券实例的set集合
     *
     * @param user 用户
     * @param ids  id
     * @return {@code Set<String>}
     */
    public static Set<String> hasCouponInstance(User user, List<String> ids) {
        return hasCouponDown(user, ids, CouponConstants.COUPON_INSTANCE_API_NAME, CouponConstants.CouponInstanceField.COUPON_PLAN_ID.getApiName());
    }

    /**
     * 查询优惠券批次是否存在
     *
     * @param user
     * @param ids
     * @return
     */
    public static Set<String> hasCoupon(User user, List<String> ids) {
        try {
            IObjectDescribe couponDesc = serviceFacade.findObject(user.getTenantId(), CouponConstants.COUPON_API_NAME);
            if (couponDesc == null) {
                return Sets.newHashSet();
            }
        } catch (Exception e) {
            log.warn(" has not couponObj" + e.getMessage());
            return Sets.newHashSet();
        }


        return hasCouponDown(user, ids, CouponConstants.COUPON_API_NAME, CouponConstants.CouponInstanceField.COUPON_PLAN_ID.getApiName());
    }

    public static Set<String> hasCouponDown(User user, List<String> ids, String objApiName, String planField) {
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        ext.setLimit(ids.size());
        ext.setOffset(0);
        ext.addFilter(Operator.EQ, planField, ids);
        ext.addFilter(Operator.IN, DBRecord.IS_DELETED, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()), String.valueOf(DELETE_STATUS.INVALID.getValue())));
        SearchTemplateQuery query = (SearchTemplateQuery) ext.getQuery();
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList(planField));
        query.setGroupByParameter(groupByParameter);
        try {
            IActionContext context = ActionContextExt.of(user).getContext();
            context.put(ActionContextKey.IGNORE_BASE_VISIBLE_RANGE, true);
            List<IObjectData> objectDataList = ObjectDataServiceImpl.aggregateFindBySearchQuery(context, (SearchTemplateQuery) ext.getQuery(),
                    objApiName);
            if (CollectionUtils.empty(objectDataList)) {
                return Sets.newHashSet();
            }
            return objectDataList.stream()
                    .filter(o -> o.get("groupbycount", Long.class) > 0)
                    .map(o -> o.get(planField, String.class))
                    .collect(Collectors.toSet());
        } catch (MetadataServiceException e) {
            log.error("aggregateFindBySearchQuery error,SearchTemplateQuery is {},error msg is {}", JSON.toJSONString(query), e.getMessage());
        }
        return Sets.newHashSet();
    }




    /**
     * 删除聚合和规则引擎
     *
     * @param user          用户
     * @param dataList      数据列表
     * @param objectApiName 对象名称
     */
    public static void deleteAggValueAndEngineRule(User user, List<IObjectData> dataList, String objectApiName) {
        int limit = AppFrameworkConfig.getBulkDeleteLimit();
        List<List<IObjectData>> partition = Lists.partition(dataList, limit);
        //分批删除
        for (List<IObjectData> iObjectDates : partition) {
            //agg id
            List<String> aggregateValueIds = Lists.newArrayListWithCapacity(limit);
            //规则引擎id
            List<String> ids = Lists.newArrayListWithCapacity(limit);
            //condition 类型的规则id，用来删除引用关系
            Set<String> conditionTypeIds = Sets.newHashSet();
            Set<String> hasRebateDetail;
            if (CouponConstants.COUPON_PLAN_API_NAME.equals(objectApiName)) {
                hasRebateDetail = hasCouponInstance(user, iObjectDates.stream().map(IObjectData::getId).collect(Collectors.toList()));
            } else if (RebateConstants.REBATE_API_NAME.equals(objectApiName)) {
                hasRebateDetail = RebateUtils.hasRebateDetail(user, iObjectDates.stream().map(IObjectData::getId).collect(Collectors.toList()));
            } else {
                continue;
            }
            for (IObjectData objectData : iObjectDates) {
                //已经使用的返利单\或者优惠券，不可删除
                if (hasRebateDetail.contains(objectData.getId())) {
                    continue;
                }
                //产品返利没有规则，只有返货的产品范围
                if (RebateConstants.REBATE_API_NAME.equals(objectApiName)
                        && RebateConstants.RebateType.PRODUCT.getValue().equals(objectData.get(RebateConstants.RebateField.REBATE_TYPE.getApiName(), String.class))) {
                    conditionTypeIds.add(objectData.getId());
                }

                String productConditionRule = objectData.get(RebateConstants.RebateField.PRODUCT_CONDITION_RULE.getApiName(), String.class);
                // 如果是复杂产品模式，则向conditionTypeIds加入id
                if (CouponConstants.ProductConditionType.HARD.getValue().equals(objectData.get(RebateConstants.RebateField.PRODUCT_CONDITION_TYPE.getApiName()))) {
                    conditionTypeIds.add(objectData.getId());
                }
                if (StringUtils.isBlank(productConditionRule)) {
                    continue;
                }
                List<RuleWhere> aggRuleWheres;
                try {
                    aggRuleWheres = ExceptionUtils.trySupplier(() -> JSON.parseArray(productConditionRule, RuleWhere.class));
                } catch (Exception e) {
                    //遇到异常继续删除，不退出循环
                    log.error("delete agg thread has error msg is {}", e.getMessage());
                    continue;
                }
                if (CollectionUtils.empty(aggRuleWheres)) {
                    continue;
                }
                aggregateValueIds.addAll(getAggregateValueIds(aggRuleWheres));
                ids.add(objectData.getId());
                //如果是条件字段是condition 或者hard，则把注册规则也进行删除
                if (CouponConstants.ProductConditionType.CONDITION.getValue()
                        .equals(objectData.get(RebateConstants.RebateField.PRODUCT_CONDITION_TYPE.getApiName()))) {
                    ids.add(getSpecialId(objectData.getId()));
                    conditionTypeIds.add(objectData.getId());
                }
            }
            List<IObjectData> aggDates = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), aggregateValueIds, SFAPreDefine.AggregateRule.getApiName());
            if (CollectionUtils.notEmpty(aggDates)) {
                serviceFacade.bulkDeleteDirect(aggDates, new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID));
            }
            // ids非空的时候调用规则引擎，删除ids
            if (!ids.isEmpty()) {
                ruleEngineLogicService.deleteRule(new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID), ids);
            }
            //删除引用
            PricePolicyUtils.deleteReferenceRelation(user.getTenantId(), Lists.newArrayList(conditionTypeIds));
        }
    }


    /**
     * 呈现记录列表
     * 先获取原始DB中的类型，因为必须根据这个类型才能获取到，
     * 如果前后更改了类型，则使用已经更改的类型
     *
     * @param recordList              记录列表
     * @param user                    用户
     * @param apiName                 api名称
     * @param conditionTypeApiName    条件类型api名称
     * @param conditionContentApiName 条件内容api名称
     */
    public static void renderRecordList(List<LogRecord> recordList, User user, String apiName, String conditionTypeApiName, String conditionContentApiName) {
        String dataId = getDataIdByLogRecordList(recordList, apiName);
        if (org.apache.commons.lang.StringUtils.isBlank(dataId)) {
            return;
        }
        //获取修改类型
        LogInfo.DiffObjectData beforeDiffType = null;
        //倒序循环日志记录，一定要倒叙才是操作的顺序
        for (int i = recordList.size() - 1; i >= 0; i--) {
            LogRecord logRecord = recordList.get(i);
            if (!ActionType.Modify.getId().equals(logRecord.getOperationType())) {
                continue;
            }
            List<LogInfo.DiffObjectData> diffObjectDataList = logRecord.getObjectData();
            //获取修改类型
            LogInfo.DiffObjectData diffType = getDiffObjectData(conditionTypeApiName, diffObjectDataList);
            //不为空的是否赋值上一个conditionType
            if (diffType != null) {
                beforeDiffType = diffType;
            }
            //获取修改类型
            LogInfo.DiffObjectData diffConditionContent = getDiffObjectData(conditionContentApiName, diffObjectDataList);
            if (diffConditionContent != null) {
                ObjectData oldObjectData = new ObjectData(diffConditionContent.getOldValue());
                ObjectData newObjectData = new ObjectData(diffConditionContent.getValue());
                if (diffType != null) {
                    Object oldType = diffType.getOldValue().get(conditionTypeApiName);
                    Object newType = diffType.getValue().get(conditionTypeApiName);
                    if (oldType != null && newType != null) {
                        renderType(diffConditionContent, oldObjectData, newObjectData, oldType.toString(), newType.toString(), conditionContentApiName, user);
                    }
                } else {
                    String dbConditionType;
                    if (beforeDiffType == null) {
                        //此次并未变更 conditionType，则查出库中
                        IObjectData objectData = serviceFacade.findObjectData(user, dataId, apiName);
                        dbConditionType = objectData.get(conditionTypeApiName, String.class);
                    } else {
                        dbConditionType = beforeDiffType.getValue().get(conditionTypeApiName).toString();
                    }
                    renderType(diffConditionContent, oldObjectData, newObjectData, dbConditionType, dbConditionType, conditionContentApiName, user);
                }
            }
        }
    }


    public static void renderType(LogInfo.DiffObjectData diffObjectData, ObjectData oldObjectData, ObjectData newObjectData,
                                  String oldType, String newType, String conditionContentApiName, User user) {
        translateManager.getTranslateService(oldType).render(oldObjectData, user);
        translateManager.getTranslateService(newType).render(newObjectData, user);
        diffObjectData.getOldValue().put(conditionContentApiName, oldObjectData.get(conditionContentApiName));
        diffObjectData.getValue().put(conditionContentApiName, newObjectData.get(conditionContentApiName));
    }



    public static void setDefaultCouponType(BaseListController.Result result, String tenantId) {
        if (!config.isOpenPaperCoupon(tenantId)) {
            return;
        }
        Optional.ofNullable(result)
                .map(BaseListController.Result::getDataList)
                .filter(CollectionUtils::notEmpty)
                .ifPresent(dataList -> {
                    for (ObjectDataDocument objectDataDocument : dataList) {
                        IObjectData objectData = objectDataDocument.toObjectData();
                        String couponType = objectData.get(CouponConstants.CouponPlanField.COUPON_TYPE.getApiName(), String.class);
                        if (StringUtils.isBlank(couponType)) {
                            objectData.set(CouponConstants.CouponPlanField.COUPON_TYPE.getApiName(), CouponConstants.CouponType.ELECTRONIC.getValue());
                        }
                    }
                });
    }



    public static RebateCouponQuery.PublicObjInfo getPublicObjectInfo(User user) {
        RebateCouponQuery.PublicObjInfo info = RebateCouponQuery.PublicObjInfo.builder().build();
        if (config.isOpenCoupon(user.getTenantId())) {
            IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), CouponConstants.COUPON_PLAN_API_NAME);
            if (describe.isPublicObject()) {
                info.setPublicObject(true);
                info.setUpstreamTenantId(describe.getUpstreamTenantId());
                info.setTenantId(user.getTenantId());
            }
        }
        return info;
    }

    public static void setDTenantId(IObjectDescribe objectDescribe, String tenantId, IObjectData objectData) {
        if (objectDescribe.isPublicObject()) {
            String firstTenantId = objectDescribe.getUpstreamTenantId();
            if (tenantId.equals(firstTenantId)) {
                objectData.setIsPublic(true);
            } else {
                Set<String> allTenantIds = com.beust.jcommander.internal.Sets.newHashSet();
                allTenantIds.add(firstTenantId);
                allTenantIds.add(tenantId);
                allTenantIds = couponDao.getDownAndUpTenantIds(allTenantIds, tenantId);
                objectData.set(CouponConstants.D_TENANT_ID, Lists.newArrayList(allTenantIds));
            }
        }
    }


    public static void checkMemberProgramMatch(User user, IObjectData couponInstanceData, IObjectData couponPlanData) {
        if (!config.isOpenIncentive(user.getTenantId())) {
            return;
        }
        String memberId = couponInstanceData.get(CouponConstants.LOYALTY_MEMBER_ID, String.class);
        if (StringUtils.isBlank(memberId)) {
            return;
        }
        Optional<IObjectData> iObjectDataOptional = Optional.ofNullable(serviceFacade.findObjectDataIgnoreAll(user, memberId, CouponConstants.LoyaltyMemberObj));
        if (!iObjectDataOptional.isPresent()) {
            return;
        }
        IObjectData memberData = iObjectDataOptional.get();
        String memberProgramId = memberData.get(CouponConstants.PROGRAM_ID, String.class);
        String couponPlanProgramId = couponPlanData.get(CouponConstants.PROGRAM_ID, String.class);
        if (!Objects.equals(memberProgramId, couponPlanProgramId)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_COUPON_MEMBER_PROGRAM_NOT_MATCH));
        }

    }
}
