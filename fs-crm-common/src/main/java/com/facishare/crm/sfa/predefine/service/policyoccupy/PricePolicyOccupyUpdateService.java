package com.facishare.crm.sfa.predefine.service.policyoccupy;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SystemConstants.ActionCode;
import com.facishare.crm.model.OrderUniformityModel;
import com.facishare.crm.model.PolicyOccupySyncModel;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.rest.dto.ApprovalStatusEnum;
import com.facishare.crm.sfa.predefine.service.policyoccupy.model.PolicyOccupyUpdate;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyLimit.LimitObjType;
import com.facishare.crm.sfa.predefine.service.task.PolicyOccupyTaskService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.policyoccupy.PricePolicyLimitConstants.*;
import com.facishare.crm.sfa.utilities.util.CommonSearchUtil;
import com.facishare.crm.util.BranchTransactionUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.transaction.core.model.BranchTransactionDto;
import com.fxiaoke.transaction.core.model.ExecutionResultType;
import com.fxiaoke.transaction.tcc.annotation.TccTransactional;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 分布式事务适配参考文档：https://wiki.firstshare.cn/pages/viewpage.action?pageId=302842627
 * 业务接入分布式事务文档：https://wiki.firstshare.cn/pages/viewpage.action?pageId=228614430
 */
@Service
@Slf4j
public class PricePolicyOccupyUpdateService {
    private final MetaDataActionService metaDataActionService;
    private final MetaDataFindService metaDataFindService;
    private final PricePolicyOccupyService pricePolicyOccupyService;
    private final PolicyOccupyTaskService occupyTaskService;
    private static final List<String> approvalStatus = Lists.newArrayList(ApprovalStatusEnum.CANCEL.getValue(), ApprovalStatusEnum.REJECT.getValue());
    private static final List<String> orderOccupyUpdateFields = Lists.newArrayList(OccupyCommonField.OCCUPY, OrderOccupyField.APPROVAL_PROCESS);

    @Autowired
    private PolicyOccupyTaskService policyOccupyTaskService;

    private static final List<String> addAndEditBranchNameList = Lists.newArrayList("defaultUpdateDataEditActHandler|handle", "defaultSaveDataAddActHandler|handle");

    private static final List<String> invalidBranchNameList = Lists.newArrayList("defaultInvalidDataInvalidActHandler|handler");

    private static final String KEY_ACTIVITY_ID = "activity_id";

    private static final String KEY_PRICE_POLICY_IDS = "price_policy_ids";

    private static final String MAP_KEY_DELETE = "delete";
    private static final String MAP_KEY_ADD = "add";
    private static final String MAP_KEY_EDIT = "edit";
    @Autowired
    public PricePolicyOccupyUpdateService(MetaDataActionService metaDataActionService,
        MetaDataFindService metaDataFindService,
        PricePolicyOccupyService pricePolicyOccupyService, PolicyOccupyTaskService occupyTaskService) {
        this.metaDataActionService = metaDataActionService;
        this.metaDataFindService = metaDataFindService;
        this.pricePolicyOccupyService = pricePolicyOccupyService;
        this.occupyTaskService = occupyTaskService;
    }


    @Transactional
    public void orderChangeOccupyUpdateHandle(PolicyOccupyUpdate.Arg arg) {
        log.info("sfa_tcc orderChangeOccupyUpdateHandle arg is : {}", JSON.toJSONString(arg));
        //编辑不管是否触发审批流，都走相同逻辑
        PricePolicyOccupyUpdateService service = (PricePolicyOccupyUpdateService)AopContext.currentProxy();
        if(OccupyUpdateTypeEnum.Add_Un_Approval.name().equals(arg.getType())) {
            service.orderAddUpdateOccupy(PolicyOccupyUpdate.Arg.toNewArg(arg));
        } else if(OccupyUpdateTypeEnum.Edit_Un_Approval.name().equals(arg.getType()) || OccupyUpdateTypeEnum.Edit_Approval.name().equals(arg.getType())) {
            service.orderEditUpdateOccupy(PolicyOccupyUpdate.Arg.toNewArg(arg));
        } else if(OccupyUpdateTypeEnum.Invalid_Un_Approval.name().equals(arg.getType()) || OccupyUpdateTypeEnum.Custom_Subtract.name().equals(arg.getType())) {
            service.orderInvalidUpdateOccupy(PolicyOccupyUpdate.Arg.toNewArg(arg));
        } else if(OccupyUpdateTypeEnum.Edit_Approval_Operate.name().equals(arg.getType()) || OccupyUpdateTypeEnum.AddOrInvalid_AgreeOrDisagree_Approval.name().equals(arg.getType())) {
            service.orderFlowCompletedUpdateOccupy(PolicyOccupyUpdate.Arg.toNewArg(arg));
        } else {
            //不处理
        }
        try {
            syncOccupyToFmcg(arg);
        } catch (Exception e) {
            log.error("syncOccupyToFmcg error: "+e.getMessage(), e);
        }
    }

    /**
     * 订单新增，加分布式事务，如果上下文没有开启分布式事务，分支事务默认不生效
     */
    @Transactional
    @TccTransactional(name="orderAddUpdateOccupy", confirmMethod = "orderAddUpdateOccupyConfirm", cancelMethod = "orderAddUpdateOccupyCancel")
    public void orderAddUpdateOccupy(PolicyOccupyUpdate.NewArg arg) {
        log.info("sfa_tcc orderAddUpdateOccupy begin executing...");
        batchUpdateOccupy(arg.getUser(), PolicyOccupyUpdate.NewArg.toArg(arg).getUseMap(), arg.getActionCode());
        log.info("sfa_tcc orderAddUpdateOccupy end execution");
    }

    public void orderAddUpdateOccupyConfirm(BranchTransactionalContext context, PolicyOccupyUpdate.NewArg arg) {
        log.info("sfa_tcc tccTransactional orderAddUpdateOccupyConfirm called! arg is : {}，branchName is : {}", JSON.toJSONString(arg), context.getBranchName());
    }

    /**
     * add 变成 delete
     * edit 中的 occupy 变成 取 occupy_old值
     * delete 变成 add
     * @param context
     * @param arg
     */
    @Transactional
    public void orderAddUpdateOccupyCancel(BranchTransactionalContext context, PolicyOccupyUpdate.NewArg arg) {
        if(CollectionUtils.empty(context.getRegisteredBranchTransactions()))  {
            return;
        }
        //1、新建数据触发回滚,需要物理删除 defaultSaveDataAddActHandler 保存到数据库的数据，
        //2、全局事务内发生异常，如果主单据数据已经作废、删除，则主单据不做回滚操作，需要业务侧做最终一致性处理
        //3、全局事务内发生异常，如果主单据数据未作废、删除，则各分支事务做回滚操作
        if(ActionCode.Edit.getActionCode().equals(arg.getActionCode()) || ActionCode.Add.getActionCode().equals(arg.getActionCode())) {
            //查询是否执行了新增、编辑保存数据的分支事务，如果执行了，且是confirm状态，则不需要回滚
            Optional<BranchTransactionDto> optional = context.getRegisteredBranchTransactions().stream().filter(p -> addAndEditBranchNameList.contains(p.getBranchName())).findFirst();
            //如果主单据已经confirm成功，则不做回滚操作
            if (optional.isPresent() && ExecutionResultType.SUCCESS.getType().equals(optional.get().getExecutionResult().getType())) {
                return;
            }
            log.info("sfa_tcc tccTransactional orderAddUpdateOccupyCancel called! actionCode is : {}, arg is : {}，branchName is  : {}", arg.getActionCode(), JSON.toJSONString(arg), context.getBranchName());
            occupyCancel(arg.getUser(), PolicyOccupyUpdate.NewArg.toArg(arg));
        } else if (ActionCode.Invalid.getActionCode().equals(arg.getActionCode()) || ActionCode.BulkInvalid.getActionCode().equals(arg.getActionCode())) {
            //查询是否执行了作废保存数据的分支事务，如果执行了，且是confirm状态，则需要回滚，作废是在after阶段
            Optional<BranchTransactionDto> optional = context.getRegisteredBranchTransactions().stream().filter(p -> invalidBranchNameList.contains(p.getBranchName())).findFirst();
            //如果主单据已经confirm成功，则不做回滚操作
            if (optional.isPresent() && ExecutionResultType.SUCCESS.getType().equals(optional.get().getExecutionResult().getType())) {
                log.info("sfa_tcc tccTransactional orderAddUpdateOccupyCancel called! actionCode is : {}, arg is : {}，branchName is   : {}",arg.getActionCode(), JSON.toJSONString(arg), context.getBranchName());
                occupyCancel(arg.getUser(), PolicyOccupyUpdate.NewArg.toArg(arg));
            }
        } else {
            log.info("sfa_tcc tccTransactional orderAddUpdateOccupyCancel called! actionCode is : {}, arg is : {}，branchName  is : {}", arg.getActionCode(), JSON.toJSONString(arg), context.getBranchName());
            //其他操作则不处理
        }
    }
    /**
     * 订单编辑
     */
    @Transactional
    @TccTransactional(name="orderEditUpdateOccupy", confirmMethod = "orderEditUpdateOccupyConfirm", cancelMethod = "orderEditUpdateOccupyCancel")
    public void orderEditUpdateOccupy(PolicyOccupyUpdate.NewArg arg) {
        log.info("sfa_tcc orderEditUpdateOccupy begin executing...");
        String type = arg.getType();
        if (type.equals(OccupyUpdateTypeEnum.Edit_Un_Approval.name())) {
            // 编辑未触发审批流
            batchUpdateOccupy(arg.getUser(), PolicyOccupyUpdate.NewArg.toArg(arg).getUseMap(), arg.getActionCode());
        } else if (type.equals(OccupyUpdateTypeEnum.Edit_Approval.name())){
            // 编辑触发审批流
            batchUpdateOccupyOnEditApproval(arg.getUser(), PolicyOccupyUpdate.NewArg.toArg(arg).getUseMap());
        }
        log.info("sfa_tcc orderEditUpdateOccupy end execution");
    }

    public void orderEditUpdateOccupyConfirm(BranchTransactionalContext context, PolicyOccupyUpdate.NewArg arg) {
        log.info("sfa_tcc tccTransactional orderEditUpdateOccupyConfirm called! arg is : {}，branchName is : {}", JSON.toJSONString(arg), context.getBranchName());
    }

    @Transactional
    public void orderEditUpdateOccupyCancel(BranchTransactionalContext context, PolicyOccupyUpdate.NewArg arg) {
        log.info("sfa_tcc tccTransactional orderEditUpdateOccupyCancel called! arg is : {}，branchName is : {}", JSON.toJSONString(arg), context.getBranchName());
        //编辑数据触发回滚，（1）若数据已经入库，则不回滚数据；（2）若数据未入库，回滚各分支事务
        if(BranchTransactionUtil.getWriteDbFlag(context)) {
            log.info("sfa_tcc tccTransactional orderEditUpdateOccupyCancel, 数据已入库，不做回滚处理");
        } else {
            log.info("sfa_tcc tccTransactional orderEditUpdateOccupyCancel, 数据未入库，做回滚处理");
            occupyCancel(arg.getUser(), PolicyOccupyUpdate.NewArg.toArg(arg));
        }
    }


    /**
     * 订单作废
     */
    @Transactional
    @TccTransactional(name="orderInvalidUpdateOccupy", confirmMethod = "orderInvalidUpdateOccupyConfirm", cancelMethod = "orderInvalidUpdateOccupyCancel")
    public void orderInvalidUpdateOccupy(PolicyOccupyUpdate.NewArg arg){
        log.info("sfa_tcc orderInvalidUpdateOccupy begin executing...");
        Map<String, List<IObjectData>> useMap = Maps.newHashMap();
        Map<String, List<String>> updateOrderOccupyMaps = salesOrderUpdateOccupy(arg.getUser(), PolicyOccupyUpdate.NewArg.toArg(arg).getMasterDataList(), useMap);
        //作废数据逆向处理比较麻烦，只能放到分支事务中的上下文里，没法放到入口参数
        if (updateOrderOccupyMaps != null && updateOrderOccupyMaps.size() > 0 && BranchTransactionalContext.getCurrent() != null) {
           BranchTransactionalContext.getCurrent().getActionContext().put("useMap", useMap);
        }
        log.info("sfa_tcc orderInvalidUpdateOccupy end execution");
    }

    public void orderInvalidUpdateOccupyConfirm(BranchTransactionalContext context, PolicyOccupyUpdate.NewArg arg) {
        log.info("sfa_tcc tccTransactional orderInvalidUpdateOccupyConfirm called! arg is : {}，branchName is : {}", JSON.toJSONString(arg), context.getBranchName());
    }

    @Transactional
    public void orderInvalidUpdateOccupyCancel(BranchTransactionalContext context, PolicyOccupyUpdate.NewArg arg) {
        log.info("sfa_tcc tccTransactional orderInvalidUpdateOccupyCancel called! arg is : {}，branchName is : {}", JSON.toJSONString(arg), context.getBranchName());
        //如果已作废，并且数据入库了，则不回滚数据
        if(BranchTransactionUtil.getWriteDbFlag(context)) {
            log.info("sfa_tcc tccTransactional orderInvalidUpdateOccupyCancel, 数据已入库，不做回滚处理");
        } else {
            log.info("sfa_tcc tccTransactional orderInvalidUpdateOccupyCancel, 数据未入库，做回滚处理");
            Map<String, List<IObjectData>> useMap = (Map<String, List<IObjectData>>) context.getActionContext().get("useMap");
            PolicyOccupyUpdate.Arg a = PolicyOccupyUpdate.NewArg.toArg(arg);
            a.setUseMap(useMap);
            occupyCancel(arg.getUser(), a);
        }
    }



    /**
     * 订单触发审批流 流操作完成
     */
    @Transactional
    @TccTransactional(name="orderFlowCompletedUpdateOccupy", confirmMethod = "orderFlowCompletedUpdateOccupyConfirm", cancelMethod = "orderFlowCompletedUpdateOccupyCancel")
    public void orderFlowCompletedUpdateOccupy(PolicyOccupyUpdate.NewArg arg) {
        log.info("sfa_tcc orderFlowCompletedUpdateOccupy begin executing...");
        IObjectData salesOrder = PolicyOccupyUpdate.NewArg.toArg(arg).getMasterDataList().get(0);
        if(null == salesOrder) {
            return;
        }
        User user = arg.getUser();
        List<String> orderIds = Lists.newArrayList(salesOrder.getId());
        List<IObjectData> orderOccupy = pricePolicyOccupyService.getPolicyLimitOccupy(user, null, orderIds, Utils.ORDER_OCCUPY_API_NAME);
        if (CollectionUtils.size(orderOccupy) == 0) {
            return;
        }
        String type = arg.getType();
        OrderUniformityModel.OrderUniformityModelBuilder uniformityBuilder = OrderUniformityModel.builder()
            .tenantId(user.getTenantId())
            .userId(user.getUserId())
            .orderIds(orderIds)
            .type("occupy")
            .actionCode("flowCompleted");
        Map<String, List<String>> snapshotMap = Maps.newHashMap();
        if(OccupyUpdateTypeEnum.AddOrInvalid_AgreeOrDisagree_Approval.name().equals(type)) {
            Map<String, List<IObjectData>> useMap = Maps.newHashMap();
            snapshotMap = salesOrderUpdateOccupy(user, Lists.newArrayList(salesOrder), useMap);
        } else if(OccupyUpdateTypeEnum.Edit_Approval_Operate.name().equals(type)){
            String flowCompletedStatus = arg.getFlowCompletedStatus();
            snapshotMap = orderEditApprovalOccupyHandle(user, salesOrder, flowCompletedStatus);
        }
        uniformityBuilder.orderSnapshot(JSON.toJSONString(snapshotMap));
        occupyTaskService.orderOccupyUniformitySendMsg(uniformityBuilder.build());
        log.info("sfa_tcc orderFlowCompletedUpdateOccupy end execution");
    }

    public void orderFlowCompletedUpdateOccupyConfirm(BranchTransactionalContext context, PolicyOccupyUpdate.NewArg arg) {
        log.info("sfa_tcc tccTransactional orderFlowCompletedUpdateOccupyConfirm called! arg is : {}，branchName is : {}", JSON.toJSONString(arg), context.getBranchName());
    }

    @Transactional
    public void orderFlowCompletedUpdateOccupyCancel(BranchTransactionalContext context, PolicyOccupyUpdate.NewArg arg) {
        log.info("sfa_tcc tccTransactional orderFlowCompletedUpdateOccupyCancel called! arg is : {}，branchName is : {}", JSON.toJSONString(arg), context.getBranchName());
        occupyCancel(arg.getUser(), PolicyOccupyUpdate.NewArg.toArg(arg));
    }


    @Transactional
    public void batchUpdateOccupy(User user, Map<String, List<IObjectData>> occupyMaps, String actionCode) {
        List<IObjectData> editList = occupyMaps.get(MAP_KEY_EDIT);
        List<IObjectData> addList = occupyMaps.get(MAP_KEY_ADD);
        List<IObjectData> deleteList = occupyMaps.getOrDefault(MAP_KEY_DELETE, Lists.newArrayList());
        if (CollectionUtils.size(editList) > 0) {
            Map<String, List<IObjectData>> editMaps = editList.stream().collect(Collectors.groupingBy(IObjectData::getDescribeApiName));
            editMaps.forEach((describeApiName, needEditList) -> {
                // 未触发审批流需要处理删除编辑前数据
                if (ActionCode.Edit.getActionCode().equals(actionCode) && describeApiName.equals(Utils.ORDER_OCCUPY_API_NAME)) {
                    deleteList.addAll(needEditList);
                    return;
                }
                StringBuilder sb = new StringBuilder();
                needEditList.forEach(needEdit -> sb.append(String.format("id:%s,occupy:%s,approval_status:%s", needEdit.getId(), needEdit.get(OccupyCommonField.OCCUPY), needEdit.get(OrderOccupyField.APPROVAL_PROCESS))));
                log.info("sfa_tcc actionCode:"+actionCode+", update info:"+sb);
                metaDataActionService.batchUpdateByFields(user, needEditList, orderOccupyUpdateFields);
            });
        }
        if (CollectionUtils.size(addList) > 0) {
            Map<String, List<IObjectData>> addMaps = addList.stream().collect(Collectors.groupingBy(IObjectData::getDescribeApiName));
            addMaps.forEach((describeApiName, needAddList) -> {
                // 未触发审批流需要处理编辑后数据 变更为正常
                if (ActionCode.Edit.getActionCode().equals(actionCode) && describeApiName.equals(Utils.ORDER_OCCUPY_API_NAME)) {
                    needAddList.forEach(needAdd -> needAdd.set(OrderOccupyField.APPROVAL_PROCESS, ApprovalProcessEnum.normal.name()));
                }
                if (describeApiName.equals(Utils.POLICY_OCCUPY_API_NAME)) {
                    needAddList.forEach(needAdd -> {
                        needAdd.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
                        needAdd.setOwner(Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
                    });
                }
                StringBuilder sb = new StringBuilder();
                needAddList.forEach(needAdd -> sb.append(String.format("id:%s,occupy:%s,approval_status:%s", needAdd.getId(), needAdd.get(OccupyCommonField.OCCUPY), needAdd.get(OrderOccupyField.APPROVAL_PROCESS))));
                log.info("sfa_tcc actionCode:"+actionCode+", add info:"+sb);
                metaDataActionService.bulkSaveObjectData(needAddList, user);
            });
        }
        if (CollectionUtils.size(deleteList) > 0) {
            StringBuilder sb = new StringBuilder();
            deleteList.forEach(needDel -> sb.append(String.format("id:%s,occupy:%s,approval_status:%s", needDel.getId(), needDel.get(OccupyCommonField.OCCUPY), needDel.get(OrderOccupyField.APPROVAL_PROCESS))));
            log.info("sfa_tcc actionCode:"+actionCode+", del info:"+sb);
            metaDataActionService.bulkDeleteDirect(deleteList, user);
        }
    }

    /**
     * userMap 用于存放变更的限额限量明细占用数据和删除的订单占用数据，逆向时需要用到
     * @param user
     * @param masterDataList
     * @param useMap
     * @return
     */
    @Transactional
    public Map<String, List<String>> salesOrderUpdateOccupy(User user, List<IObjectData> masterDataList, Map<String, List<IObjectData>> useMap) {
        Map<String, List<String>> updateOrderOccupyMaps = Maps.newHashMap();
        List<String> orderIds = masterDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<IObjectData> orderOccupyList = pricePolicyOccupyService.getPolicyLimitOccupy(user, null, orderIds, Utils.ORDER_OCCUPY_API_NAME);
        if (CollectionUtils.size(orderOccupyList) > 0) {
            List<IObjectData> editList = pricePolicyOccupyService.getEditPolicyOccupyList(user, orderOccupyList, masterDataList);
            if (CollectionUtils.size(editList) > 0) {
                metaDataActionService.batchUpdateByFields(user, editList, Lists.newArrayList(OccupyCommonField.OCCUPY));
            } else {
                log.info("salesOrderUpdateOccupy no need to update PolicyOccupyObj, orderIds:{}", orderIds);
            }
            metaDataActionService.bulkDeleteDirect(orderOccupyList, user);
            updateOrderOccupyMaps.put("deleted", orderOccupyList.stream().map(DBRecord::getId).collect(Collectors.toList()));
            useMap.put(MAP_KEY_EDIT, editList);
            useMap.put(MAP_KEY_DELETE, orderOccupyList);
        }
        return updateOrderOccupyMaps;
    }

    /**
     * 编辑触发审批流审核通过/不通过对占用量处理
     *
     * @param user       用户信息
     * @param masterData 订单主对象
     * @param status     审批通过/不通过
     */
    @Transactional
    public Map<String, List<String>> orderEditApprovalOccupyHandle(User user, IObjectData masterData, String status) {
        Map<String, List<String>> updateOrderOccupyMaps = Maps.newHashMap();
        String orderId = masterData.getId();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, OrderOccupyField.ORDER_ID, orderId);
        SearchTemplateQuery query = CommonSearchUtil.getSearchTemplateQuery(filters, 1000);
        // 获取订单占用量
        List<IObjectData> allOrderOccupy = CommonSearchUtil.findDataBySearchQueryIgnoreAll(user, Utils.ORDER_OCCUPY_API_NAME, query);
        List<IObjectData> updateOrderOccupyList = Lists.newArrayList();
        List<IObjectData> deleteOccupyList = Lists.newArrayList();
        // 编辑之前数据
        List<IObjectData> editBeforeOrderOccupy = allOrderOccupy.stream()
            .filter(v -> v.get(OrderOccupyField.APPROVAL_PROCESS, String.class).equals(ApprovalProcessEnum.edit_before.name()))
            .collect(Collectors.toList());
        // 编辑中数据
        List<IObjectData> editAfterOrderOccupy = allOrderOccupy.stream()
            .filter(v -> v.get(OrderOccupyField.APPROVAL_PROCESS, String.class).equals(ApprovalProcessEnum.edit_after.name()))
            .collect(Collectors.toList());
        List<String> detailLimitIds = allOrderOccupy.stream().map(v -> v.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class)).distinct().
            collect(Collectors.toList());
        // 明细限量信息
        List<IObjectData> policyLimitList = metaDataFindService
            .findObjectDataByIdsIgnoreAll(user.getTenantId(), detailLimitIds, Utils.PRICE_POLICY_LIMIT_ACCOUNT_API_NAME);
        // 获取明细限量占有量
        List<IObjectData> policyOccupyList = pricePolicyOccupyService.getPolicyLimitOccupy(user, detailLimitIds, null, Utils.POLICY_OCCUPY_API_NAME);
        List<IObjectData> updatePolicyOccupyList = getEditPolicyOccupyList(user, masterData, policyLimitList, editBeforeOrderOccupy, editAfterOrderOccupy, policyOccupyList, status);
        if (approvalStatus.contains(status)) {
            // 不通过或者取消审批 编辑之前的变为normal并且删除编辑之后的
            editBeforeOrderOccupy.forEach(orderOccupy -> orderOccupy.set(OrderOccupyField.APPROVAL_PROCESS, ApprovalProcessEnum.normal.name()));
            updateOrderOccupyList = editBeforeOrderOccupy;
            deleteOccupyList = editAfterOrderOccupy;
        } else if (ApprovalStatusEnum.PASS.getValue().equals(status)) {
            // 通过 编辑之后变为normal并且删除编辑之前
            editAfterOrderOccupy.forEach(orderOccupy -> orderOccupy.set(OrderOccupyField.APPROVAL_PROCESS, ApprovalProcessEnum.normal.name()));
            updateOrderOccupyList = editAfterOrderOccupy;
            deleteOccupyList = editBeforeOrderOccupy;
        }
        metaDataActionService.batchUpdateByFields(user, updateOrderOccupyList, Lists.newArrayList(OrderOccupyField.APPROVAL_PROCESS));
        metaDataActionService.bulkDeleteDirect(deleteOccupyList, user);
        if (CollectionUtils.size(updatePolicyOccupyList) > 0) {
            metaDataActionService.batchUpdateByFields(user, updatePolicyOccupyList, Lists.newArrayList(OccupyCommonField.OCCUPY));
        }
        updateOrderOccupyMaps.put("update", updateOrderOccupyList.stream().map(IObjectData::getId).collect(Collectors.toList()));
        updateOrderOccupyMaps.put("deleted", deleteOccupyList.stream().map(IObjectData::getId).collect(Collectors.toList()));
        return updateOrderOccupyMaps;
    }

    private List<IObjectData> getEditPolicyOccupyList(User user, IObjectData masterData, List<IObjectData> policyLimitList,
        List<IObjectData> editBeforeOrderOccupy, List<IObjectData> editAfterOrderOccupy, List<IObjectData> policyOccupyList,
        String status) {
        List<IObjectData> editList = Lists.newArrayList();
        Map<String, LimitObjType> limitObjTypeMap = pricePolicyOccupyService.getPricePolicyLimitObjTypeMap(user.getTenantId());
        // 获取明细限量占有量
        policyOccupyList.forEach(policyOccupyData -> {
            String detailLimitId = policyOccupyData.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class);
            BigDecimal totalAlready = policyOccupyData.get(OccupyCommonField.OCCUPY, BigDecimal.class);
            // 明细限量信息
            IObjectData policyLimit = policyLimitList.stream().filter(v -> detailLimitId.equals(v.getId())).findAny().orElse(null);
            if (null == policyLimit) {
                return;
            }
            String aggregateFieldValue = pricePolicyOccupyService.getLimitObjTypeAggregateFieldValue(policyLimit, masterData, limitObjTypeMap);
            if (StringUtils.isNotEmpty(aggregateFieldValue) && !policyOccupyData.get(OccupyCommonField.EQUAL_ID, String.class)
                .equals(aggregateFieldValue)) {
                return;
            }
            Predicate<IObjectData> beforePredicate = buildOrderOccupyPredicate(detailLimitId, ApprovalProcessEnum.edit_before.name());
            Predicate<IObjectData> afterPredicate = buildOrderOccupyPredicate(detailLimitId, ApprovalProcessEnum.edit_after.name());
            IObjectData editBefore = editBeforeOrderOccupy.stream().filter(beforePredicate).findAny().orElse(new ObjectData());
            IObjectData editAfter = editAfterOrderOccupy.stream().filter(afterPredicate).findAny().orElse(new ObjectData());
            BigDecimal editBeforeOccupy = editBefore.get(OccupyCommonField.OCCUPY, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal editAfterOccupy = editAfter.get(OccupyCommonField.OCCUPY, BigDecimal.class, BigDecimal.ZERO);
            if (editBeforeOccupy.compareTo(editAfterOccupy) > 0) {
                if (approvalStatus.contains(status)) {
                    return;
                } else if (ApprovalStatusEnum.PASS.getValue().equals(status)) {
                    totalAlready = totalAlready.subtract(editBeforeOccupy).add(editAfterOccupy);
                }
            } else {
                if (approvalStatus.contains(status)) {
                    totalAlready = totalAlready.subtract(editAfterOccupy).add(editBeforeOccupy);
                } else if (ApprovalStatusEnum.PASS.getValue().equals(status)) {
                    return;
                }
            }

            policyOccupyData.set(OccupyCommonField.OCCUPY, totalAlready);
            editList.add(policyOccupyData);
        });
        return editList;
    }

    /**
     * 编辑触发审批流对占用量进行维护
     *
     * @param user       用户信息
     * @param occupyMaps 占用量信息
     */
    @Transactional
    public void batchUpdateOccupyOnEditApproval(User user, Map<String, List<IObjectData>> occupyMaps) {
        // 编辑触发审批流 只做新增和修改，不做删除。待审批通过/不通过 在去处理
        List<IObjectData> editList = occupyMaps.get(MAP_KEY_EDIT);
        List<IObjectData> addList = occupyMaps.get(MAP_KEY_ADD);
        // 取消的价格政策的订单占用量信息
        List<IObjectData> deleteList = occupyMaps.getOrDefault(MAP_KEY_DELETE, Lists.newArrayList());
        List<String> deleteOrderOccupyDetailLimitIds = deleteList.stream().map(v -> v.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class)).collect(Collectors.toList());
        if (CollectionUtils.size(editList) > 0) {
            List<IObjectData> policyOccupyEditList = editList.stream().filter(v -> v.getDescribeApiName().equals(Utils.POLICY_OCCUPY_API_NAME)).collect(Collectors.toList());
            policyOccupyEditList.forEach(policyOccupyEdit -> {
                String detailLimitId = policyOccupyEdit.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class);
                BigDecimal totalAlready = policyOccupyEdit.get(OccupyCommonField.OCCUPY, BigDecimal.class);
                // 价格政策占用量匹配取消的订单占用量 需要叠加回来并且把该条数据添加到editList中 approval_process默置为修改之前
                if (deleteOrderOccupyDetailLimitIds.contains(detailLimitId)) {
                    IObjectData deleteOrderOccupyData = deleteList.stream().filter(v -> v.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class).equals(detailLimitId)).findAny().orElse(null);
                    if(null != deleteOrderOccupyData) {
                        BigDecimal deleteOrderOccupy = deleteOrderOccupyData.get(OccupyCommonField.OCCUPY, BigDecimal.class);
                        totalAlready = totalAlready.add(deleteOrderOccupy);
                        deleteOrderOccupyData.set(OrderOccupyField.APPROVAL_PROCESS, ApprovalProcessEnum.edit_before.name());
                        editList.add(deleteOrderOccupyData);
                    }
                } else {
                    Predicate<IObjectData> condition = v -> v.getDescribeApiName().equals(Utils.ORDER_OCCUPY_API_NAME);
                    Predicate<IObjectData> beforePredicate = buildOrderOccupyPredicate(detailLimitId, ApprovalProcessEnum.edit_before.name());
                    Predicate<IObjectData> afterPredicate = buildOrderOccupyPredicate(detailLimitId, ApprovalProcessEnum.edit_after.name());
                    IObjectData editBefore = editList.stream().filter(condition.and(beforePredicate)).findAny().orElse(new ObjectData());
                    IObjectData editAfter = addList.stream().filter(condition.and(afterPredicate)).findAny().orElse(new ObjectData());
                    BigDecimal editBeforeOccupy = editBefore.get(OccupyCommonField.OCCUPY, BigDecimal.class, BigDecimal.ZERO);
                    BigDecimal editAfterOccupy = editAfter.get(OccupyCommonField.OCCUPY, BigDecimal.class, BigDecimal.ZERO);
                    if (editBeforeOccupy.compareTo(editAfterOccupy) > 0) {
                        totalAlready = totalAlready.subtract(editAfterOccupy).add(editBeforeOccupy);
                    }
                }
                policyOccupyEdit.set(OccupyCommonField.OCCUPY, totalAlready);
            });
            Map<String, List<IObjectData>> editMaps = editList.stream().collect(Collectors.groupingBy(IObjectData::getDescribeApiName));
            editMaps.forEach((describeApiName, needEditList) -> metaDataActionService.batchUpdateByFields(user, needEditList, orderOccupyUpdateFields));
        }
        if (CollectionUtils.size(addList) > 0) {
            Map<String, List<IObjectData>> addMaps = addList.stream().collect(Collectors.groupingBy(IObjectData::getDescribeApiName));
            addMaps.forEach((describeApiName, needAddList) -> {
                if (describeApiName.equals(Utils.POLICY_OCCUPY_API_NAME)) {
                    needAddList.forEach(needAdd -> {
                        needAdd.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
                        needAdd.setOwner(Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
                    });
                }
                metaDataActionService.bulkSaveObjectData(needAddList, user);
            });
        }
    }

    private Predicate<IObjectData> buildOrderOccupyPredicate(String detailLimitId, String approvalProcess) {
        Predicate<IObjectData> detailLimitIdPredicate = v -> v.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class).equals(detailLimitId);
        Predicate<IObjectData> approvalProcessPredicate = v -> v.get(OrderOccupyField.APPROVAL_PROCESS, String.class)
            .equals(approvalProcess);
        return detailLimitIdPredicate.and(approvalProcessPredicate);
    }

    public void handleOrderOccupyUniformity(User user, Map<String, List<IObjectData>> useMap, String actionCode, List<String> orderIds, String objectApiName) {
        handleOrderOccupyUniformity(user, useMap, actionCode, orderIds, null, objectApiName);
    }

    public void handleOrderOccupyUniformity(User user, Map<String, List<IObjectData>> useMap, String actionCode, List<String> orderIds, String requestId, String objectApiName) {
        String orderSnapshot = null;
        if (CollectionUtils.size(useMap) > 0) {
            orderSnapshot = JSON.toJSONString(objectDataConvertClass(useMap));
        }
        OrderUniformityModel model = OrderUniformityModel.builder().tenantId(user.getTenantId()).userId(user.getUserId()).orderIds(orderIds).type("occupy")
                .actionCode(actionCode).orderSnapshot(orderSnapshot).requestId(requestId).objectApiName(objectApiName).build();
        occupyTaskService.orderOccupyUniformitySendMsg(model);
    }

    private Map<String, List<PolicyOccupyData>> objectDataConvertClass(Map<String, List<IObjectData>> useMap) {
        Map<String, List<PolicyOccupyData>> occupyMap = Maps.newHashMap();
        useMap.forEach((key, objectDataList) -> {
            List<PolicyOccupyData> policyOccupy = objectDataList.stream().map(objectData -> PolicyOccupyData.builder()
                .id(objectData.getId())
                .describeApiName(objectData.getDescribeApiName())
                .orderId(objectData.get(OrderOccupyField.ORDER_ID, String.class, null))
                .approvalProcess(objectData.get(OrderOccupyField.APPROVAL_PROCESS, String.class, null))
                .detailLimitId(objectData.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class, null))
                .occupy(objectData.get(OccupyCommonField.OCCUPY, BigDecimal.class))
                .equalId(objectData.get(OccupyCommonField.EQUAL_ID, String.class, null))
                .build())
                .collect(Collectors.toList());
            occupyMap.put(key, policyOccupy);
        });
        return occupyMap;
    }

    /**
     * 限额限量明细占用与订单占用 逆向操作
     * @param user
     * @param arg
     */
    @Transactional
    public void occupyCancel(User user, PolicyOccupyUpdate.Arg arg) {
        Map<String,List<IObjectData>> occupyMaps = arg.getUseMap();
        if(CollectionUtils.empty(occupyMaps)) {
            return;
        }
        log.info("sfa_tcc tccTransactional occupyCancel called! occupyMaps is : {}", JSON.toJSONString(occupyMaps));
        List<IObjectData> editList = occupyMaps.get(MAP_KEY_EDIT);
        List<IObjectData> addList = occupyMaps.get(MAP_KEY_ADD);
        List<IObjectData> deleteList = occupyMaps.get(MAP_KEY_DELETE);

        if (CollectionUtils.notEmpty(editList)) {
            Map<String, List<IObjectData>> editMaps = editList.stream().collect(Collectors.groupingBy(IObjectData::getDescribeApiName));
            editMaps.forEach((describeApiName, needEditList) -> {
                //限量明细占用量 中的 occupy 变成 从 occupy_old 中取值进行还原
                needEditList.stream().forEach(e-> {
                    if(Utils.POLICY_OCCUPY_API_NAME.equals(describeApiName)) {
                        e.set(OccupyCommonField.OCCUPY, e.get(OccupyCommonField.OCCUPY_OLD, BigDecimal.class, BigDecimal.ZERO));
                    } else {
                        e.set(OrderOccupyField.APPROVAL_PROCESS, "normal");
                    }
                });
                StringBuilder sb = new StringBuilder();
                needEditList.forEach(needEdit -> sb.append(String.format("id:%s,occupy:%s,approval_status:%s", needEdit.getId(), needEdit.get(OccupyCommonField.OCCUPY), needEdit.get(OrderOccupyField.APPROVAL_PROCESS))));
                log.info("sfa_tcc tccTransactional occupyCancel, update info:"+sb);
                metaDataActionService.batchUpdateByFields(user, needEditList, orderOccupyUpdateFields);
            });
        }

        if (CollectionUtils.notEmpty(addList)) {
            Map<String, List<IObjectData>> addMaps = addList.stream().collect(Collectors.groupingBy(IObjectData::getDescribeApiName));
            addMaps.forEach((describeApiName, needAddList) -> {
                //价格政策占用，需要还原老的值
                if(Utils.POLICY_OCCUPY_API_NAME.equals(describeApiName)) {
                    needAddList.forEach(needAdd ->
                        needAdd.set(OccupyCommonField.OCCUPY, needAdd.get(OccupyCommonField.OCCUPY_OLD, BigDecimal.class, BigDecimal.ZERO))
                    );

                    StringBuilder sb = new StringBuilder();
                    needAddList.forEach(needAdd -> sb.append(String.format("id:%s,occupy:%s,approval_status:%s", needAdd.getId(), needAdd.get(OccupyCommonField.OCCUPY), needAdd.get(OrderOccupyField.APPROVAL_PROCESS))));
                    log.info("sfa_tcc tccTransactional occupyCancel, add info:"+sb);
                    metaDataActionService.batchUpdateByFields(user, needAddList, orderOccupyUpdateFields);
                } else {
                    //订单占用新增要变成删除
                    StringBuilder sb = new StringBuilder();
                    needAddList.forEach(needAdd -> sb.append(String.format("id:%s,occupy:%s,approval_status:%s", needAdd.getId(), needAdd.get(OccupyCommonField.OCCUPY), needAdd.get(OrderOccupyField.APPROVAL_PROCESS))));
                    log.info("sfa_tcc tccTransactional occupyCancel, del info:"+sb);
                    metaDataActionService.bulkDeleteDirect(needAddList, user);
                }

            });
        }
        //删除数据要变成新增
        if (CollectionUtils.notEmpty(deleteList)) {
            deleteList.forEach(e -> e.set(DBRecord.ID, IdGenerator.get()));
            metaDataActionService.bulkSaveObjectData(deleteList, user);
        }

        try {
            syncOccupyToFmcg(arg);
        } catch (Exception e) {
            log.error("sfa_tcc syncOccupyToFmcg error: {}", e);
        }
    }

    /**
     * 将于活动申请有关的价格政策对应的限额限量明细占用情况数据同步给快消，以MQ的形式做同步
     * @param arg
     */
    private void syncOccupyToFmcg(PolicyOccupyUpdate.Arg arg) {
        if(CollectionUtils.empty(arg.getMasterDataList())) {
            return;
        }

        List<IObjectData> list = arg.getMasterDataList().stream().filter(data -> Utils.SALES_ORDER_API_NAME.equals(data.getDescribeApiName()) && StringUtils.isNotBlank(data.get(KEY_ACTIVITY_ID, String.class))).collect(Collectors.toList());
        //判断销售订单来源于快消的活动申请，有数据才考虑同步，没数据不考虑
        if(CollectionUtils.empty(list) || CollectionUtils.empty(arg.getUseMap())) {
            return;
        }
        //获取活动申请的id(多价格政策明细限量没有存储活动信息，需从销售订单中获取)
        String activityId = list.get(0).get(KEY_ACTIVITY_ID, String.class);

        List<String> detailLimitIdList = arg.getUseMap().values().stream().flatMap(List::stream).map(d -> d.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class)).distinct().collect(Collectors.toList());
        if(CollectionUtils.empty(detailLimitIdList)) {
            return;
        }

        //取出限额限量明细的数据
        List<IObjectData> dataList = metaDataFindService.findObjectDataByIdsIgnoreAll(arg.getUser().getTenantId(), detailLimitIdList, Utils.PRICE_POLICY_LIMIT_ACCOUNT_API_NAME);
        if(CollectionUtils.empty(dataList)) {
            return;
        }

        List<IObjectData> needSyncDataList = dataList.stream()
                .filter(data ->
                        StringUtils.isNotBlank(data.get(KEY_ACTIVITY_ID, String.class)) || CollectionUtils.notEmpty(data.get(KEY_PRICE_POLICY_IDS, List.class))
                )
                .collect(Collectors.toList());
        List<String> needSyncDetailLimitIdList = dataList.stream()
                .filter(data ->
                        StringUtils.isNotBlank(data.get(KEY_ACTIVITY_ID, String.class)) || CollectionUtils.notEmpty(data.get(KEY_PRICE_POLICY_IDS, List.class))
                )
                .map(DBRecord::getId).collect(Collectors.toList());

        if(CollectionUtils.empty(needSyncDetailLimitIdList) ||  CollectionUtils.empty(needSyncDataList)) {
            return;
        }

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(2000);
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setNeedReturnQuote(false);
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);

        if(needSyncDetailLimitIdList.size()==1) {
            SearchUtil.fillFilterEq(filters, OccupyCommonField.DETAIL_LIMIT_ID, needSyncDetailLimitIdList.get(0));
        } else {
            SearchUtil.fillFilterIn(filters, OccupyCommonField.DETAIL_LIMIT_ID, needSyncDetailLimitIdList);
        }

        QueryResult<IObjectData> queryResult = metaDataFindService.findBySearchQuery(arg.getUser(), Utils.POLICY_OCCUPY_API_NAME, searchQuery);
        List<IObjectData> data = queryResult.getData();
        if(CollectionUtils.empty(data)) {
            return;
        }

        Map<String, IObjectData> limitedIdToMap = needSyncDataList.stream().collect(Collectors.toMap(IObjectData::getId, d -> d, (v1, v2) -> v1));

        List<PolicyOccupySyncModel.PolicyOccupySync> occupyDataList = Lists.newArrayList();
        data.stream().forEach(e -> {
            String detailLimitId = e.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class, "");
            IObjectData objectData = limitedIdToMap.get(detailLimitId);
            PolicyOccupySyncModel.PolicyOccupySync.PolicyOccupySyncBuilder occupySyncBuilder = PolicyOccupySyncModel.PolicyOccupySync
                    .builder();
            PolicyOccupySyncModel.PolicyOccupySync occupySync = null;
            if(objectData == null) {
                occupySync = occupySyncBuilder.occupy(e.get(OccupyCommonField.OCCUPY, BigDecimal.class, BigDecimal.ZERO))
                        .detailLimitId(detailLimitId)
                        .build();
            } else {
                boolean isEqual = AccountModeEnum.EQUAL.name().equals(objectData.get(PricePolicyLimitDetailField.ACCOUNT_MODE, String.class));
                occupySync = occupySyncBuilder.occupy(e.get(OccupyCommonField.OCCUPY, BigDecimal.class, BigDecimal.ZERO))
                        .detailLimitId(detailLimitId)
                        .activityId(objectData.get(KEY_ACTIVITY_ID, String.class, activityId))
                        .accountId(isEqual?e.get(OccupyCommonField.EQUAL_ID, String.class):objectData.get(PricePolicyLimitDetailField.ACCOUNT_ID, String.class))
                        .accountMode(objectData.get(PricePolicyLimitDetailField.ACCOUNT_MODE, String.class))
                        .limitObjType(objectData.get(PricePolicyLimitDetailField.LIMIT_OBJ_TYPE, String.class))
                        .range(objectData.get(PricePolicyLimitDetailField.RANGE, String.class))
                        .build();
            }
            occupyDataList.add(occupySync);
        });
        PolicyOccupySyncModel.Arg modelArg = PolicyOccupySyncModel.Arg.builder().userId(arg.getUser().getUserId()).tenantId(arg.getUser().getTenantId()).dataList(occupyDataList).build();
        policyOccupyTaskService.sendOccupy2fmcg(modelArg);

    }
}
