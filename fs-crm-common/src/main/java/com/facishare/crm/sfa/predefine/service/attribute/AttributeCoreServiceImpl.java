package com.facishare.crm.sfa.predefine.service.attribute;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.attribute.dao.AttributeDao;
import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeValue;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants.CategoryField;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants.Field;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants.ProductAttributeField;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants.ValueField;
import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.dto.ai.AIDto;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 属性核心服务类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AttributeCoreServiceImpl implements AttributeCoreService {
    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private AttributeDao attributeDao;

    private static final int MAX_BATCH_SIZE = 1000;

    @Autowired
    private ProductAttributeService productAttributeService;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    private AttributeRangeService attributeRangeService;

    @Override
    public List<Attribute> getAttributeByIds(User user, Set<String> ids) {
        return getAttributeByIds(user, ids, null);
    }

    @Override
    public List<Attribute> getAttributeByIds(User user, Set<String> ids, Set<String> groupIds) {
        List<IObjectData> objectDataList = attributeDao.getAttributesByIds(user, Lists.newArrayList(ids));

        List<Attribute> list = new ArrayList<>();
        String attrGroupId;
        for (IObjectData objectData : objectDataList) {
            attrGroupId = objectData.get(AttributeConstants.GroupField.ATTRIBUTE_GROUP_ID, String.class);
            Attribute attribute = new Attribute();
            attribute.setId(objectData.getId());
            attribute.setName(AttributeUtils.getI18nName(objectData));
            attribute.setFieldNum(objectData.get(Field.FIELD_NUM, Integer.class));
            attribute.setCode(AttributeUtils.getI18nCode(objectData));
            attribute.setGroupId(attrGroupId);
            list.add(attribute);
            if (StringUtils.isNotBlank(attrGroupId) && Objects.nonNull(groupIds)) {
                groupIds.add(attrGroupId);
            }
        }
        return list;
    }

    @Override
    public List<Attribute> getAttributesByStatus(User user, String status, boolean needInvalid) {
        List<IObjectData> objectDataList = attributeDao.getAttributesByStatus(user, status, needInvalid);
        return objectDataList.stream().map(objectData ->{
            Attribute attribute = new Attribute();
            attribute.setId(objectData.getId());
            attribute.setName(AttributeUtils.getI18nName(objectData));
            attribute.setFieldNum(objectData.get(Field.FIELD_NUM, Integer.class));
            attribute.setCode(AttributeUtils.getI18nCode(objectData));
            return attribute;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AttributeValue> getAttributeValueByAttributeIds(User user, Set<String> attributeIds) {
        List<IObjectData> objectDataList = attributeDao.getAttributeValuesByAttributeIds(user, Lists.newArrayList(attributeIds));

        return objectDataList.stream().map(objectData -> AttributeValue.builder()
                        .id(objectData.getId())
                        .name(AttributeUtils.getI18nName(objectData))
                        .code(AttributeUtils.getI18nCode(objectData))
                        .attributeId(objectData.get(ValueField.ATTRIBUTE_ID, String.class))
                        .order_field(objectData.get(ValueField.ORDER_FIELD, Integer.class))
                        .is_default(objectData.get(ValueField.IS_DEFAULT, String.class))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, List<Attribute>> getAttributeByProductIds(User user, List<String> productIds) {
        return getAttributeByProductIds(user, productIds, false);
    }

    @Override
    public Map<String, List<Attribute>> getAttributeByProductIds(User user, List<String> productIds, Boolean isPricing) {
        return getAttributeByProductIds(user, productIds, false, Lists.newArrayList());
    }

    @Override
    public Map<String, List<Attribute>> getAttributeByProductIds(User user, List<String> productIds, Boolean isPricing, List<IObjectData> productList) {

        Map<String, List<Attribute>> result = Maps.newHashMap();
        List<IObjectData> productAttributes;
        if (CollectionUtils.notEmpty(productList)) {
            productAttributes = productList.stream()
                    .filter(objectData -> ProductAttributeExt.of(objectData).hasRelateAttribute())
                    .collect(Collectors.toList());
        } else {
            productAttributes = productAttributeService.getByProductIds(user, productIds);
        }
        if (CollectionUtils.empty(productAttributes)) {
            return result;
        }
        if (isPricing) {
            productAttributes = productAttributes.stream()
                    .filter(x -> x.get(ProductAttributeField.PRICING_ATTRIBUTE_IDS) != null)
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(productAttributes)) {
                return result;
            }
        }

        String filterFieldName = isPricing ? ProductAttributeField.PRICING_ATTRIBUTE_IDS : ProductAttributeField.ATTRIBUTE_IDS;

        //获取产品关联的属性和属性值
        Set<String> attributeIds = Sets.newHashSet();
        productAttributes.forEach(objectData -> attributeIds.addAll(
                objectData.get(filterFieldName, ArrayList.class)));
        if (CollectionUtils.empty(attributeIds)) {
            return result;
        }
        Set<String> groupIds = Sets.newHashSet();
        List<Attribute> attributes = getAttributeByIds(user, attributeIds, groupIds);
        //只收集产品关联的属性值
        Set<String> attributeValueIds = collectAttributeValueIds(attributes, productAttributes, filterFieldName);
        //List<AttributeValue> attributeValues = getAttributeValueByAttributeIds(user, attributeIds);
        List<AttributeValue> attributeValues = getAttributeValueByIds(user, attributeValueIds);
        Map<String, IObjectData> groupMap = queryAttrGroupMap(user, groupIds);
        //拼装结果，形如{产品A:[{"颜色":[{"code":"black","name":"黑色"},{"code":"red","name":"红色"}]}]}
        productAttributes.forEach(productAttribute -> {
            List<Attribute> relateAttributes = Lists.newArrayList();
            ProductAttributeExt productAttributeExt = ProductAttributeExt.of(productAttribute);
            if (productAttributeExt.hasRelateAttribute()) {
                List<String> relateAttributeIds = productAttribute.get(filterFieldName, ArrayList.class);
                relateAttributeIds.forEach(relateAttributeId -> attributes.stream().filter(x -> relateAttributeId.equals(x.getId())).findAny()
                        .ifPresent(attribute -> {
                            IObjectData attrGroupData = groupMap.get(attribute.getGroupId());
                            relateAttributes.add(Attribute.builder()
                                    .id(relateAttributeId)
                                    .name(attribute.getName())
                                    .fieldNum(attribute.getFieldNum())
                                    .groupId(Objects.isNull(attrGroupData) ? "" : attrGroupData.getId())
                                    .groupName(Objects.isNull(attrGroupData) ? "" : AttributeUtils.getI18nName(attrGroupData))
                                    .groupNo(Objects.isNull(attrGroupData) ? 0 : attrGroupData.get(AttributeConstants.GroupField.SERIAL_NO, Integer.class))
                                    .attributeValues(buildAttributeValue(user.getTenantId(), attribute, attributeValues, productAttributeExt))
                                    .build());
                        }));
                result.put(productAttribute.getId(), resortAttribute(relateAttributeIds, relateAttributes));
            }
        });

        return result;
    }

    private List<Attribute> resortAttribute(List<String> attributeIds, List<Attribute> attributesList) {
        List<Attribute> list = Lists.newArrayList();
        if(CollectionUtils.empty(attributeIds) || CollectionUtils.empty(attributesList)){
            return list;
        }
        Map<String, Attribute> attributeMap = attributesList.stream().collect(Collectors.toMap(Attribute::getId, v->v, (v1, v2) -> v1));
        attributeIds.stream().forEach(id -> {
            if (attributeMap.containsKey(id)) {
                list.add(attributeMap.get(id));
            }
        });
        return list;
    }
    private Set<String> collectAttributeValueIds(List<Attribute> attributes, List<IObjectData> productList, String filterFieldName) {
        if(CollectionUtils.empty(productList) || CollectionUtils.empty(attributes)){
            return Sets.newHashSet();
        }
        Map<String, Integer> attributeMap = attributes.stream().collect(Collectors.toMap(Attribute::getId, Attribute::getFieldNum));
        Set<String> attributeValueIds = Sets.newHashSet();
        productList.forEach(product -> {
            ProductAttributeExt productAttributeExt = ProductAttributeExt.of(product);
            List<String> relateAttributeIds = product.get(filterFieldName, ArrayList.class, Lists.newArrayList());
            relateAttributeIds.stream().forEach(attributeId -> {
                    //产品关联的属性，有可能属性已经删除，忽略已经删除的属性及属性值
                    if (attributeMap.containsKey(attributeId) && attributeMap.get(attributeId) != null) {
                        attributeValueIds.addAll(productAttributeExt.getAttributeValue(attributeMap.get(attributeId)));
                    }
                }
            );
        });
        return attributeValueIds;
    }
    private List<AttributeValue> getAttributeValueByIds(User user, Set<String> attributeValueIds) {
        if(CollectionUtils.empty(attributeValueIds)){
            return Lists.newArrayList();
        }
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(attributeValueIds), Utils.ATTRIBUTE_VALUE_OBJ_API_NAME);
        return objectDataList.stream().map(objectData -> AttributeValue.builder()
                        .id(objectData.getId())
                        .name(AttributeUtils.getI18nName(objectData))
                        .code(AttributeUtils.getI18nCode(objectData))
                        .attributeId(objectData.get(ValueField.ATTRIBUTE_ID, String.class))
                        .order_field(objectData.get(ValueField.ORDER_FIELD, Integer.class))
                        .is_default(objectData.get(ValueField.IS_DEFAULT, String.class))
                        .build())
                .collect(Collectors.toList());
    }

    public Map<String, IObjectData> queryAttrGroupMap(User user, Set<String> groupIds) {
        Map<String, IObjectData> groupMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(groupIds)) {
            List<IObjectData> groupList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(groupIds), Utils.ATTRIBUTE_GROUP_OBJ_API_NAME);
            if (CollectionUtils.notEmpty(groupList)) {
                groupMap = groupList.stream().collect(Collectors.toMap(DBRecord::getId, Function.identity(), (k1, k2) -> k1));
            }
        }
        return groupMap;
    }

    private List<AttributeValue> buildAttributeValue(String tenantId, Attribute attribute,
                                                     List<AttributeValue> attributeValues,
                                                     ProductAttributeExt productAttributeExt) {
        List<String> attributeValueIds = productAttributeExt.getAttributeValue(attribute.getFieldNum());
        List<AttributeValue> result = Lists.newArrayList();
        if (CollectionUtils.empty(attributeValues)) {
            return result;
        }
        Map<String, AttributeValue> attributeValueMap = attributeValues.stream().collect(Collectors.toMap(AttributeValue::getId, Function.identity(), (k1, k2) -> k1));
        for (String attributeValueId : attributeValueIds){
            AttributeValue attributeValue = attributeValueMap.get(attributeValueId);
            if(attributeValue != null && Objects.equals(attributeValue.getAttributeId(), attribute.getId())) {
                result.add(AttributeValue.builder()
                        .id(attributeValue.getId())
                        .code(attributeValue.getCode())
                        .name(attributeValue.getName())
                        .order_field(attributeValue.getOrder_field())
                        .is_default(attributeValue.getIs_default())
                        .build());
            }
        }
        if (!GrayUtil.isUncheckAttributeValueDefault(tenantId)) {
            //第一个是默认属性值，其他的不是
            if (CollectionUtils.notEmpty(result)) {
                result.forEach(x -> x.setIs_default("0"));
                result.get(0).setIs_default("1");
            }
        }

        result.sort((o1, o2) -> {
            if (o1.getOrder_field() == null) {
                return (o2.getOrder_field() == null) ? 1 : 0;
            } else if (o2.getOrder_field() == null) {
                return 1;
            } else {
                return o1.getOrder_field().compareTo(o2.getOrder_field());
            }
        });
        return result;
    }

    @Override
    public void attachAttributeData(User user, List<IObjectData> objectDataList, String productFieldName) {
        if (bizConfigThreadLocalCacheService.isOpenAttribute(user.getTenantId())) {
            List<String> productIds = objectDataList.stream()
                    .map(x -> x.get(productFieldName, String.class))
                    .collect(Collectors.toList());
            Map<String, List<Attribute>> attributeMap = getAttributeByProductIds(user, productIds);
            objectDataList.forEach(objectData -> objectData.set(AttributeConstants.ATTRIBUTE,
                    attributeMap.get(objectData.get(productFieldName, String.class))));
        }
    }

    @Override
    public void fillAttributeText(User user, List<IObjectData> objectDataList) {
        if (bizConfigThreadLocalCacheService.isOpenAttribute(user.getTenantId())) {
            List<DetailAttributeExt> relateAttributeDataList = DetailAttributeExt.ofList(
                    objectDataList.stream()
                            .filter(x -> DetailAttributeExt.of(x).hasAttribute())
                            .collect(Collectors.toList()));
            Set<String> attributeIds = Sets.newHashSet();
            relateAttributeDataList.forEach(x -> attributeIds.addAll(x.getAttributeKeySet()));
            Map<String, Attribute> attributes = AttributeExt.toAttributeMap(getAttributeByIds(user, attributeIds));
            Map<String, AttributeValue> attributeValues = AttributeExt.toAttributeValueMap(
                    getAttributeValueByAttributeIds(user, attributeIds));
            relateAttributeDataList.forEach(x -> x.fillAttributeText(attributes, attributeValues));
        }
    }

    @Override
    public void batchDeleteProductCategoryAttributeRelationByCategory(String tenantId, String categoryId, List<String> attributeIds) {
        if (categoryId.isEmpty()) {
            return;
        }
        List<IObjectData> list = getCategoryInfoByCategoryId(tenantId, categoryId);
        list = list.stream().filter(x -> attributeIds.contains(x.get(CategoryField.ATTRIBUTE_ID.getApiName(), String.class))).collect(Collectors.toList());
        if (list != null && !list.isEmpty()) {
            serviceFacade.bulkDeleteWithInternalDescribe(list, new User(tenantId, User.SUPPER_ADMIN_USER_ID));
        }
        attributeRangeService.batchDeleteProductCategoryAttrRange(User.systemUser(tenantId),Lists.newArrayList(categoryId),attributeIds);
    }

    @Override
    public void batchSaveProductCategoryAttribute(String tenantId, String categoryId, List<String> attributeIds, List<AttributeProductCategoryModel.Attribute> attributeList) {
        if (categoryId.isEmpty() || attributeIds.isEmpty()) {
            return;
        }
        List<IObjectData> objectDataList = new ArrayList<>();
        IObjectDescribe describe = serviceFacade.findObject(tenantId, Utils.PRODUCT_CATEGORY_ATTRIBUTE_OBJ_API_NAME);
        //先删后插
        List<IObjectData> list = getCategoryInfoByCategoryId(tenantId, categoryId);
        if (list != null && !list.isEmpty()) {
            serviceFacade.bulkDeleteWithInternalDescribe(list, new User(tenantId, User.SUPPER_ADMIN_USER_ID));
        }

        if (CollectionUtils.notEmpty(attributeList)) {
            attributeList.forEach(x -> {
                IObjectData objectData = getObjectData(tenantId, categoryId, describe, x.getAttributeId());
                objectData.set(AttributeProductCategoryModel.ATTRIBUTE_VALUE_IDS, x.getAttributeValueIds());
                objectDataList.add(objectData);
            });
        } else {
            for (String attributeId : attributeIds) {
                objectDataList.add(getObjectData(tenantId, categoryId, describe, attributeId));
            }
        }
        serviceFacade.bulkSaveObjectData(objectDataList, new User(tenantId, User.SUPPER_ADMIN_USER_ID));
    }

    @NotNull
    private IObjectData getObjectData(String tenantId, String categoryId, IObjectDescribe describe, String attributeId) {
        IObjectData objectData = new ObjectData();
        objectData.setTenantId(tenantId);
        objectData.setDescribeApiName(Utils.PRODUCT_CATEGORY_ATTRIBUTE_OBJ_API_NAME);
        objectData.setDescribeId(describe.getId());
        objectData.setId(serviceFacade.generateId());
        objectData.set(AttributeProductCategoryModel.PRODUCT_CATEGORY_ID, categoryId);
        objectData.set(AttributeProductCategoryModel.ATTRIBUTE_ID, attributeId);
        objectData.setCreateTime(System.currentTimeMillis());
        return objectData;
    }


    @Override
    public void batchSaveAttributeUserRelation(User user, String categoryId, List<String> attributeIds, Boolean isRecover, List<AttributeProductCategoryModel.Attribute> attributeList) {
        List<IObjectData> objectDataList = new ArrayList<>();
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), Utils.ATTRIBUTE_USER_RELATION_OBJ_API_NAME);
        //先删后插
        List<IObjectData> list = getAttributeUserRelationByCategoryIdAndUser(user.getTenantId(), categoryId, user.getUpstreamOwnerIdOrUserId());
        if (list != null && !list.isEmpty()) {
            serviceFacade.bulkDeleteWithInternalDescribe(list, new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID));
        }

        if (Boolean.FALSE.equals(isRecover)) {
            IObjectData objectData = new ObjectData();
            objectData.setTenantId(user.getTenantId());
            objectData.setDescribeApiName(Utils.ATTRIBUTE_USER_RELATION_OBJ_API_NAME);
            objectData.setDescribeId(describe.getId());
            objectData.setId(serviceFacade.generateId());
            objectData.set("product_category_id", categoryId);
            objectData.set("user_id", user.getUpstreamOwnerIdOrUserId());
            if (CollectionUtils.notEmpty(attributeList)) {
                objectData.set(AttributeProductCategoryModel.ATTRIBUTE_JSON, JSON.toJSONString(attributeList));
            } else {
                objectData.set("attribute_ids", attributeIds);
            }
            objectData.set("create_time", System.currentTimeMillis());
            objectData.set("last_modified_time", System.currentTimeMillis());
            objectDataList.add(objectData);
            serviceFacade.bulkSaveObjectData(objectDataList, new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID));
        }
    }


    @Override
    public List<IObjectData> getCategoryInfoByCategoryId(String tenantId, String categoryId) {
        List<IObjectData> list = new ArrayList<>();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AttributeConstants.CategoryField.PRODUCT_CATEGORY_ID.getApiName(), categoryId);
        query.setFilters(filters);
        query.setNeedReturnCountNum(false);
        query.setLimit(MAX_BATCH_SIZE);
        query.setPermissionType(0);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(new User(tenantId, User.SUPPER_ADMIN_USER_ID),
                Utils.PRODUCT_CATEGORY_ATTRIBUTE_OBJ_API_NAME, query);
        if (queryResult != null && !queryResult.getData().isEmpty()) {
            list = queryResult.getData();
        }
        return list;
    }

    @Override
    public List<IObjectData> getAttributeUserRelationByCategoryIdAndUser(String tenantId, String categoryId, String userId) {
        List<IObjectData> list = new ArrayList<>();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AttributeConstants.CategoryField.PRODUCT_CATEGORY_ID.getApiName(), categoryId);
        SearchUtil.fillFilterEq(filters, CategoryField.USER_ID.getApiName(), userId);
        query.setFilters(filters);
        query.setNeedReturnCountNum(false);
        query.setLimit(MAX_BATCH_SIZE);
        query.setPermissionType(0);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(new User(tenantId, User.SUPPER_ADMIN_USER_ID),
                Utils.ATTRIBUTE_USER_RELATION_OBJ_API_NAME, query);
        if (queryResult != null && !queryResult.getData().isEmpty()) {
            list = queryResult.getData();
        }
        return list;
    }

    @Override
    public int findFieldNum(List<Integer> fieldNumList) {
        Collections.sort(fieldNumList);
        if (fieldNumList.isEmpty()) {
            return 1;
        } else {
            int fieldNum = 1;
            for (Integer num : fieldNumList) {
                if (num - fieldNum > 0) {
                    return fieldNum;
                } else {
                    fieldNum = num + 1;
                }
            }
            return fieldNum;
        }
    }

    public List<BaseImportAction.ImportError> attributeImportCustomValidate(User user, List<BaseImportDataAction.ImportData> dataList) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        // 超出属性对象上限30个，请重新导入
        String errorMessage = I18N.text("sfa.attribute.number.exceeds");
        List<Attribute> existDataList = getAttributesByStatus(user, AttributeConstants.Status.ALL.getStatus(), true);
        Integer limit = AttributeUtils.getAttributeLimit(user.getTenantId());
        if (existDataList.size() + dataList.size() > limit) {
            dataList.forEach(x -> errorList.add(new BaseImportAction.ImportError(x.getRowNo(), errorMessage)));
        }
        return errorList;
    }

    public void attributeCustomDefaultValue(User user, List<IObjectData> validList) {
        List<Attribute> attributes = getAttributesByStatus(user, AttributeConstants.Status.ALL.getStatus(), true);
        List<Integer> fieldNumList = new ArrayList<>();
        if (attributes != null && !attributes.isEmpty()) {
            fieldNumList = attributes.stream()
                    .filter(x -> !Objects.isNull(x))
                    .map(Attribute::getFieldNum).collect(Collectors.toList());
        }

        for (IObjectData data : validList) {
            int fieldNum = findFieldNum(fieldNumList);
            data.set(AttributeConstants.Field.FIELD_NUM, fieldNum);
            fieldNumList.add(fieldNum);
        }
    }

    public List<BaseImportAction.ImportError> attributeValueImportCustomValidate(User user, List<BaseImportDataAction.ImportData> dataList) {
        List<BaseImportAction.ImportError> errorList = checkSameName(user, dataList);
        if (!GrayUtil.isUncheckAttributeValueDefault(user.getTenantId())) {
            errorList.addAll(checkDefaultValue(user, dataList));
        }

        return errorList;
    }

    private List<BaseImportAction.ImportError> checkSameName(User user, List<BaseImportDataAction.ImportData> dataList) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        // author：xielh 2021年4月7日 优化
        // 获取导入数据的属性id
        Set<String> attributeId = dataList.stream().filter(r -> r.getData().get(AttributeConstants.ValueField.ATTRIBUTE_ID) != null)
                .map(r -> r.getData().get(AttributeConstants.ValueField.ATTRIBUTE_ID, String.class)).collect(Collectors.toSet());
        // 获取指定属性已有的属性值
        List<AttributeValue> attributeValueList = getAttributeValueByAttributeIds(user, attributeId);
        // 使用Set 属性id-属性值名 判断是否存在重复
        Set<String> attributeIdAttributeNameSet = attributeValueList.stream().map(attributeValue -> attributeValue.getAttributeId() + "_" + attributeValue.getName()).collect(Collectors.toSet());
        String errorMessage = I18N.text("sfa.attribute.check.samename");
        dataList.forEach(importData -> {
            String attributeIdAttributeName = importData.getData().get(AttributeConstants.ValueField.ATTRIBUTE_ID, String.class) + "_" + importData.getData().getName();
            if (!attributeIdAttributeNameSet.add(attributeIdAttributeName)) {
                errorList.add(new BaseImportAction.ImportError(importData.getRowNo(), errorMessage));
            }
        });
        return errorList;
    }

    private List<BaseImportAction.ImportError> checkDefaultValue(User user, List<BaseImportDataAction.ImportData> dataList) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        Map<String, List<BaseImportDataAction.ImportData>> objGroupByAttributeId = dataList.stream()
                .filter(x -> x.getData().get(AttributeConstants.Field.ATTRIBUTE_ID) != null)
                .collect(Collectors.groupingBy(o -> o.getData().get(AttributeConstants.Field.ATTRIBUTE_ID, String.class)));
        if (objGroupByAttributeId != null && !objGroupByAttributeId.isEmpty()) {
            List<AttributeValue> attributeValues =
                    getAttributeValueByAttributeIds(user, objGroupByAttributeId.keySet());
            List<String> existAttributeIds = attributeValues.stream().map(AttributeValue::getAttributeId).collect(Collectors.toList());
            String errorMsg = I18N.text("sfa.attributevalue.check.uniquevalue");
            objGroupByAttributeId.forEach((attributeId, attributeValueList) -> {
                if (existAttributeIds.contains(attributeId)) {
                    for (BaseImportDataAction.ImportData attributeValue : attributeValueList) {
                        if (attributeValue.getData().get(AttributeConstants.ValueField.IS_DEFAULT).equals("1")) {
                            errorList.add(new BaseImportAction.ImportError(attributeValue.getRowNo(), errorMsg));
                        }
                    }
                } else {
                    List<BaseImportDataAction.ImportData> defaultValues = attributeValueList.stream()
                            .filter(r -> r.getData().get(AttributeConstants.ValueField.IS_DEFAULT).equals("1"))
                            .collect(Collectors.toList());
                    if (defaultValues.size() > 1) {
                        for (int n = 1; n < defaultValues.size(); n++) {
                            //导入第一个，剩下提示
                            errorList.add(new BaseImportAction.ImportError(defaultValues.get(n).getRowNo(), errorMsg));
                        }
                    } else if (defaultValues.isEmpty()) {
                        for (BaseImportDataAction.ImportData attributeValue : attributeValueList) {
                            errorList.add(new BaseImportAction.ImportError(attributeValue.getRowNo(), errorMsg));
                        }
                    }
                }

            });
        }
        return errorList;
    }


    /**
     * 属性值导入校验
     *
     * @param user     用户信息
     * @param dataList 导入数据
     * @return errorList 错误数据
     * <AUTHOR>
     */
    public List<BaseImportAction.ImportError> attributeValueImportValidate(User user, List<BaseImportDataAction.ImportData> dataList) {
        // 同属性不能重名
        List<BaseImportAction.ImportError> errorList = checkSameName(user, dataList);
        errorList.addAll(checkSameName(user, dataList));
        return errorList;
    }

    /**
     * 设置属性值是否默认为非默认
     *
     * @param validList 属性值数据
     * <AUTHOR>
     */
    public void attributeValueDefaultValue(List<IObjectData> validList) {
        for (IObjectData data : validList) {
            data.set(ValueField.IS_DEFAULT, "0");
        }
    }

    /**
     * 替换属性和属性值名称
     * @param attrIdMap 属性值映射 key:属性ID, value:属性值ID
     * @param originalText 原始文本 格式如"容量:50ml;颜色:红"
     * @return 替换后的文本
     */
    public String replaceAttributeAndValueNames(Map<String, String> attrIdMap, String originalText ,String tenantId) {
        try {
            if (CollectionUtils.empty(attrIdMap)) {
                log.warn("Invalid parameters: originalText={}, attrValueMap={}", originalText, attrIdMap);
                return originalText;
            }

            // 查询属性信息
            List<IObjectData> attributes = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(attrIdMap.keySet()), Utils.ATTRIBUTE_OBJ_API_NAME);

            // 查询属性值信息
            List<IObjectData> attrValues = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(attrIdMap.values()), Utils.ATTRIBUTE_VALUE_OBJ_API_NAME);

            // 构建属性和属性值的映射
            Map<String, String> attrNameMap = buildNameMap(attributes);
            Map<String, String> valueNameMap = buildNameMap(attrValues);

            // 处理原始文本
            return replaceText(attrIdMap, attrNameMap, valueNameMap,true);

        } catch (Exception e) {
            log.error("Failed to replace attribute names, originalText:{}, attrValueMap:{}, error:",
                    originalText, attrIdMap, e);
            return originalText;
        }
    }

    public String replaceNonAttributeAndValueNames(Map<String, String> attrIdMap, String originalText ,String tenantId) {
        try {
            if (CollectionUtils.empty(attrIdMap)) {
                log.warn("Invalid parameters: originalText={}, attrValueMap={}", originalText, attrIdMap);
                return originalText;
            }
            // 查询非标属性信息
            List<IObjectData> attributes = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(attrIdMap.keySet()), Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME);
            // 构建非标属性的映射
            Map<String, String> attrNameMap = buildNameMap(attributes);
            // 处理原始文本
            return replaceText(attrIdMap, attrNameMap, Maps.newHashMap(),false);

        } catch (Exception e) {
            log.error("Failed to replace attribute names, originalText:{}, attrValueMap:{}, error:",
                    originalText, attrIdMap, e);
            return originalText;
        }
    }


    /**
     * 构建名称映射
     */
    private Map<String, String> buildNameMap(List<IObjectData> dataList) {
        Map<String, String> nameMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(dataList)) {
            dataList.forEach(data -> {
                String name = AttributeUtils.getI18nName(data);
                if (StringUtils.isNotBlank(name)) {
                    nameMap.put(data.getId(), name);
                }
            });
        }
        return nameMap;
    }

    /**
     * 替换文本
     */
    private String replaceText(Map<String, String> attrIdMap,
                               Map<String, String> attrNameMap,
                               Map<String, String> valueNameMap,
                               boolean isReplaceValue) {
        StringJoiner result = new StringJoiner(";");

        // 查找并替换属性名和属性值
        for (Map.Entry<String, String> entry : attrIdMap.entrySet()) {
            String attrId = entry.getKey();
            String valueId = entry.getValue();

            String newAttrName = attrNameMap.get(attrId);
            String newAttrValue = valueNameMap.get(valueId);
            // 只有当新的属性名和属性值都存在时才替换
            if(isReplaceValue){
                if (StringUtils.isNotBlank(newAttrName) && StringUtils.isNotBlank(newAttrValue)) {
                    result.add(newAttrName+":"+newAttrValue);
                }
            }else{
                if (StringUtils.isNotBlank(newAttrName) ) {
                    result.add(newAttrName+":"+valueId);
                }
            }
        }

        return result.toString();
    }

    /**
 * 将属性值JSON字符串转换为Map
 * @param jsonStr 属性值JSON字符串
 * @return 属性ID和属性值ID的映射
 */
public Map<String, String> convertJsonToAttrMap(String jsonStr) {
    try {
        if (StringUtils.isBlank(jsonStr)) {
            return Maps.newHashMap();
        }

        // 使用 FastJSON 解析 JSON 字符串为 Map
        Map<String, String> attrValueMap = JSON.parseObject(
            jsonStr,
            new TypeReference<Map<String, String>>() {}
        );

        // 验证转换结果
        if (CollectionUtils.empty(attrValueMap)) {
            log.warn("Empty attribute value map from JSON: {}", jsonStr);
            return Maps.newHashMap();
        }

        return attrValueMap;

    } catch (Exception e) {
        log.warn("Failed to convert JSON to map: {}, error:", jsonStr, e);
        return Maps.newHashMap();
    }
}

    @Override
    public void fillAttributeAndNonAttributeText(List<IObjectData> objectDataList, String tenantId) {
        if (CollectionUtils.empty(objectDataList)) {
            return ;
        }
        objectDataList.forEach(x->{
            x.set(AttributeConstants.ATTRIBUTE,replaceAttributeAndValueNames(convertJsonToAttrMap(x.get(AttributeConstants.ATTRIBUTE_JSON,String.class)), x.get(AttributeConstants.ATTRIBUTE,String.class), tenantId));
            x.set("nonstandard_attribute",replaceNonAttributeAndValueNames(convertJsonToAttrMap(x.get("nonstandard_attribute_json",String.class)), x.get("nonstandard_attribute",String.class), tenantId));
        });
    }

    /**
     *
     * @param user
     * @param map  key 是属性ID， value 是属性值ID 列表
     * @return map key 是attributexxx xxx表示属性的槽位号， value 是属性值ID 列表
     */
    @Override
    public Map<String, List<String>> fillAttributeNum(User user, Map<String, List<String>> map) {
        Map<String, List<String>> result = Maps.newHashMap();
        if(MapUtils.isEmpty(map)) {
            return result;
        }
        serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(map.keySet()), AttributeConstants.API_NAME).forEach(x->{
            Integer number = x.get(Field.FIELD_NUM, Integer.class);
            if(number!=null) {
                result.put("attribute"+number.toString(), map.get(x.getId()));
            }
        });
        return result;
    }


    /**
     * key为AttributeObj ID ,value 为AttributeValueObj ID
     * @param user
     * @param attributeJsonMap
     * @return
     */
    @Override
    public List<ObjectDataDocument> findAttributeJson(User user, Map<String, String> attributeJsonMap) {
        if(MapUtils.isEmpty(attributeJsonMap)) {
            return Lists.newArrayList();
        }
        Set<String> keySet = attributeJsonMap.keySet();
        Set<String> valueSet = attributeJsonMap.values().stream().collect(Collectors.toSet());
        IActionContext context = ActionContextExt.of(user).skipRelevantTeam().getContext();
        context.setPrivilegeCheck(false);
        List<IObjectData> attributeList = serviceFacade.findObjectDataByIds(context, Lists.newArrayList(keySet), Utils.ATTRIBUTE_OBJ_API_NAME);
        if(CollectionUtils.empty(attributeList)) {
            return Lists.newArrayList();
        }
        Map<String, ObjectDataDocument> attributeMap = attributeList.stream().collect(Collectors.toMap(x->x.getId(), x-> ObjectDataDocument.of(x), (o,n) -> o));
        List<IObjectData> attributeValueList = serviceFacade.findObjectDataByIds(context, Lists.newArrayList(valueSet), Utils.ATTRIBUTE_VALUE_OBJ_API_NAME);
        if(CollectionUtils.notEmpty(attributeValueList)) {
            attributeValueList.stream().forEach(x-> {
                ObjectDataDocument attribute = attributeMap.getOrDefault(x.get(ValueField.ATTRIBUTE_ID, String.class, ""), new ObjectDataDocument());
                attribute.put("attributeValue", ObjectDataDocument.of(x));
            });
        }
        return attributeMap.values().stream().collect(Collectors.toList());
    }
}