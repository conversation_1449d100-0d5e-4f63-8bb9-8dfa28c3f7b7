package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.model.QuoterModel;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public interface AdvancedFormulaService {

    void init(String tenantId);

    void validateByField(IObjectDescribe describe, IFieldDescribe fieldDescribe);

    List<ObjectDescribeDocument> domainList(User user, String pluginApiName);

    List<ObjectDataDocument> formulaCalculate(QuoterModel.CalculateDataParam arg, ServiceContext serviceContext);

    Map<String, Map<String,Map<String, CalculateRelation>>> queryAdvancedFormulaList(User user, QuoterModel.Arg arg);

    String translateFormula(IObjectDescribe describe, Pattern pattern, Map<String, IObjectData> finalProductMap, IObjectData data,String tenantId);

    void checkRepeat(IObjectData objectData, Map<String, List<IObjectData>> detailObjectData, User user);

    void checkFieldFormula(IObjectDescribe objectDescribe,IObjectData objectData, List<IObjectData> dataList, User user);
}
