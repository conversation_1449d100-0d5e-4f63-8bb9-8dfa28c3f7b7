package com.facishare.crm.sfa.predefine;

import lombok.extern.slf4j.Slf4j;

/**
 * SFA 预定义对象定义并注册
 * <p>
 * Created by liyiguang on 2017/7/9.
 */

@Slf4j
public enum SFAPreDefine {

    AccountMainData("AccountMainDataObj"), Account("AccountObj"), Contact("ContactObj"), SalesOrder("SalesOrderObj"), PriceBook("PriceBookObj"), PriceBookProduct("PriceBookProductObj"), Product("ProductObj"),
    Payment("PaymentObj"), Refund("RefundObj"), Opportunity("OpportunityObj"), Leads("LeadsObj"), LeadsTransferLog("LeadsTransferLogObj"), ReturnedGoodsInvoice("ReturnedGoodsInvoiceObj"),
    Quote("QuoteObj"), QuoteLines("QuoteLinesObj"), ReturnedGoodsInvoiceProduct("ReturnedGoodsInvoiceProductObj"),
    SalesOrderProduct("SalesOrderProductObj"), Cases("CasesObj"), Partner("PartnerObj"),
    //    GoalValue("GoalValueObj"),
//    Visiting("VisitingObj"),
    NewOpportunity("NewOpportunityObj"), NewOpportunityLines("NewOpportunityLinesObj"), NewOpportunityContacts("NewOpportunityContactsObj"), SPU("SPUObj"),
    Specification("SpecificationObj"), SpecificationValue("SpecificationValueObj"), Contract("ContractObj"),
    InvoiceApplication("InvoiceApplicationObj"), MarketingEvent("MarketingEventObj"), AccountAddr("AccountAddrObj"), AccountFinInfo("AccountFinInfoObj"),
    LeadsPoolAllocateRuleObj("LeadsPoolAllocateRuleObj"), LeadsPoolAllocateRuleMemberObj("LeadsPoolAllocateRuleMemberObj"),
    LeadsPoolAllocateRuleMemberRecordObj("LeadsPoolAllocateRuleMemberRecordObj"), RuleFilterObj("RuleFilterObj"), CrmFeedTag("crm_feed_tag"),
    SubProduct("SubProductObj"), CRMFeedObj("CRMFeedObj"), SaleEventObj("SaleEventObj"), UnitInfo("UnitInfoObj"), MultiUnitRelated("MultiUnitRelatedObj"),
    //    TieredPriceBook("TieredPriceBookObj"), TieredPriceBookProduct("TieredPriceBookProductObj"), TieredPriceBookRule("TieredPriceBookRuleObj"),
    ProductConstraint("ProductConstraintObj"), ProductConstraintLines("ProductConstraintLinesObj"), SubProductCatalog("SubProductCatalogObj"), BehaviorIntegralDetail("BehaviorIntegralDetailObj"),
    InvoiceApplicationLines("InvoiceApplicationLinesObj"), ProductCategory("ProductCategoryObj"), LeadsFlowRecord("LeadsFlowRecordObj"),
    Bom("BOMObj"), ProductGroup("ProductGroupObj"),
    AvailableRange("AvailableRangeObj"),
    AvailablePriceBook("AvailablePriceBookObj"),
    AvailableAccount("AvailableAccountObj"),
    AvailableProduct("AvailableProductObj"),
    AvailableAccountResult("AvailableAccountResultObj"),
    AvailableProductResult("AvailableProductResultObj"),
    CommonUnit("CommonUnitObj"),
    Attribute("AttributeObj"), AttributeValue("AttributeValueObj"), ProductAttribute("ProductAttributeObj"), ProductCategoryAttribute("ProductCategoryAttributeObj"),
    AttributePriceBook("AttributePriceBookObj"), AttributePriceBookLines("AttributePriceBookLinesObj"), AttributeApplicablePriceBook("AttributeApplicablePriceBookObj"),
    CampaignMembers("CampaignMembersObj"), EnterpriseInfo("EnterpriseInfoObj"),
    PricePolicy("PricePolicyObj"), PricePolicyAccount("PricePolicyAccountObj"), PricePolicyProduct("PricePolicyProductObj"),
    PricePolicyRule("PricePolicyRuleObj"), AggregateRule("AggregateRuleObj"), MarketingKeyword("MarketingKeywordObj"), KeywordServingPlan("KeywordServingPlanObj"), TermServingLines("TermServingLinesObj"), PricePolicyExcludeAccount("PricePolicyExcludeAccountObj"), PricePolicyLimitAccount("PricePolicyLimitAccountObj"),
    AmortizeInfo("AmortizeInfoObj"), LandingPage("LandingPageObj"), LandingPageVisitorDetail("LandingPageVisitorDetailObj"), Project("ProjectObj"),
    SaleContract("SaleContractObj"), SaleContractLine("SaleContractLineObj"), BehaviorRecord("BehaviorRecordObj"),
    MarketingAttribution("MarketingAttributionObj"), MarketingEventInfluence("MarketingEventInfluenceObj"), Rebate("RebateObj"), RebateDetail("RebateDetailObj"),
    RebateRule("RebateRuleObj"), CouponPlan("CouponPlanObj"), CouponInstance("CouponInstanceObj"), RebatePolicy("RebatePolicyObj"), RebatePolicyRule("RebatePolicyRuleObj"), RebatePolicyLog("RebatePolicyLogObj"), PriceBookAccount("PriceBookAccountObj"),
    PartnerAddr("PartnerAddrObj"), SaleContractAvailableRangeRelation("SaleContractAvailableRangeRelationObj"), SaleContractPriceBookRelation("SaleContractPriceBookRelationObj"), Settlement("SettlementObj"), SettlementDetail("SettlementDetailObj"),
    ActivityContactInsight("ActivityContactInsightObj");

    private final String apiName;

    SFAPreDefine(String apiName) {
        this.apiName = apiName;
    }

    public String getApiName() {
        return apiName;
    }
}
