package com.facishare.crm.sfa.utilities.constant;

/**
 * @IgnoreI18nFile
 */
public interface AggregateRuleConstants {

    String ENTITY_SOURCE_TYPE = "CRM_AGGREGATE_RULE";
    String ENTITY_SOURCE_LABEL = "聚合规则";


    String ENTITY_COUPON_SOURCE_TYPE = "CRM_AGGREGATE_RULE_COUPON";
    String ENTITY_COUPON_SOURCE_LABEL = "优惠券可用范围";

    class Field {

        /**
         * 聚合对象
         */
        public static final String AGGREGATE_OBJECT = "aggregate_object";

        /**
         * 聚合对象
         */
        public static final String AGGREGATE_OBJECT_REAL = "aggregate_object__r";
        /**
         * 聚合字段
         */
        public static final String AGGREGATE_FIELD = "aggregate_field";
        /**
         * 聚合条件
         */
        public static final String CONDITION = "condition";
        /**
         * 日期范围
         */
        public static final String DATE_RANGE = "date_range";
        /**
         * 聚合类型
         */
        public static final String AGGREGATE_TYPE = "aggregate_type";
        /**
         * 计算状态
         */
        public static final String CALCULATE_STATUS = "calculate_status";
        /**
         * 聚合日期字段
         */
        public static final String DATE_FIELD = "date_field";
        /**
         * 聚合维度
         */
        public static final String DIMENSION = "dimension";

        /**
         * 日期范围 存储真实值
         */
        public static final String DATE_RANGE_REAL = "date_range__r";
        /**
         * 聚合字段 存储真实值
         */
        public static final String AGGREGATE_FIELD_REAL = "aggregate_field__r";
        /**
         * 聚合日期字段 存储真实值
         */
        public static final String DATE_FIELD_REAL = "date_field__r";
        /**
         * 聚合维度 存储真实值
         */
        public static final String DIMENSION_REAL = "dimension__r";

        public static final String DATA_SOURCE = "data_source";

        /**
         * 规则类型
         */
        public static final String RULE_TYPE = "rule_type";
        /**
         * 聚合方式
         */
        public static final String AGGREGATE_WAY = "aggregate_way";


    }

    enum DataSourceEnum {
        SYSTEM("system"), PACKAGE("package");
        private String value;

        DataSourceEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    enum AggregateWayEnum {
        SUM("sum", "求和");

        private String value;
        private String label;

        AggregateWayEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }
    }

    enum AggregateTypeEnum {
        CURRENT("current", "当单"), HISTORY("history", "历史");
        private String value;
        private String label;

        AggregateTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }
    }
    enum RuleTypeEnum {
        AGGREGATE("aggregate", "聚合值"), GROUP("group", "订单组合"),COUPON_PLAN_RANGE("couponPlanRange", "优惠券可用范围"),
        INCENTIVE("incentive", "激励指标可用范围");
        private String value;
        private String label;

        RuleTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }
    }


    enum DateRangeEnum {
        THIS_QUARTER("this_quarter", "本季度"),
        LAST_QUARTER("last_quarter", "上一季度"),
        THIS_MONTH("this_month", "本月"),
        LAST_MONTH("last_month", "上月"),
        THIS_WEEK("this_week", "本周"),
        LAST_WEEK("last_week", "上周"),
        CUSTOM("custom", "自定义");
        private String value;
        private String label;

        DateRangeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }

        public String getLabel() {
            return label;
        }
    }

    enum CalculateStatusEnum {
        RUN("0", "计算中"), END("1", "计算完成");
        private String value;
        private String label;

        CalculateStatusEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }
    }

}
