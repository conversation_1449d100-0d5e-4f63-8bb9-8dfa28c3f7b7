package com.facishare.crm.sfa.predefine.service.quoter.model;

import com.facishare.enterprise.common.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2023-12-12
 */
public interface AttributeConstraintModel {

    /**
     * @描述说明：属性级联约束查询参数
     * @作者：chench
     * @创建日期：2023-12-12
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg {

        /**
         * @描述说明：属性级联约束ID
         */
        private String id;

        private int version;
    }

    /**
     * @描述说明：属性级联约束查询结果
     * @作者：chench
     * @创建日期：2023-12-12
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        private List<AttributeConstraintNode> dataList;

        private int version;

        private boolean isChanged;
    }

    /**
     * @描述说明：属性级联约束节点信息
     * @作者：chench
     * @创建日期：2023-12-12
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    class AttributeConstraintNode {
        /**
         * @描述说明：节点类型 1-属性 2-属性值
         */
        private int nodeType;
        /**
         * 节点所在层级
         */
        private int nodeLevel;
        /**
         * 最大层级（根节点）
         */
        private int maxLevel;
        /**
         * @描述说明：节点ID
         */
        private String id;
        /**
         * 主对象ID
         */
        private String attributeConstraintId;
        /**
         * 根节点ID
         */
        private String rootNodeId;
        /**
         * 属性值IDs （值中带有","则表示非标属性的值）
         */
        private List<String> values;
        /**
         * 父节点ID
         */
        private String parentId;
        /**
         * 是否必选 true-必选 false-非必选
         */

        private Boolean isRequired;
        /**
         * 是否多选 true-多选 false-单选
         */
        private Boolean isMultiSelected;
        /**
         * 是否标准属性 true-标准属性  false-非标属性
         */
        private Boolean isStandardAttribute;
        /**
         * 子节点
         */
        private List<AttributeConstraintNode> children;

        /**
         * 必须属性值IDs
         */
        private List<String> requiredSelected;

        /**
         * 前端展示要使用属性的field_num信息
         */
        private Integer fieldNum;
    }
}
