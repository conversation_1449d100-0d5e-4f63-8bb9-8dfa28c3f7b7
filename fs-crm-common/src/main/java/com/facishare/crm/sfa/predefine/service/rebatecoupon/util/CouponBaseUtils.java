package com.facishare.crm.sfa.predefine.service.rebatecoupon.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.constants.RebateConstants;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.HardProductData;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponQuery;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RuleWhere;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.translate.TranslateConditionToRuleService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ObjectInfo;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.impl.ui.layout.component.NavigationComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.collect.Lists;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Consumer;

/**
 * 优惠券工具类
 *
 * <AUTHOR>
 * @date 2021/12/21
 */
@Slf4j
public class CouponBaseUtils{
    protected static final List<String> TO_REMOVE_BUTTON = Lists.newArrayList(ObjectAction.BATCH_IMPORT.getActionCode(), ObjectAction.BATCH_EXPORT.getActionCode(), ObjectAction.INTELLIGENTFORM.getActionCode());
    protected static final String RULE_CONDITION_JSON = "{\n" +
            "                    \"api_name\": \"related_rule_condition\",\n" +
            "                    \"nameI18nKey\": \"paas.udobj.related_rule_condition\",\n" +
            "                    \"header\": \"产品条件\",\n" +
            "                    \"type\": \"rule_condition\",\n" +
            "                    \"order\": 999,\n" +
            "                    \"is_hidden\": false,\n" +
            "                    \"_id\": \"related_rule_condition\"" +
            "                }";
    protected static final String RULE_RANGE_JSON = "{\n" +
            "                    \"api_name\": \"related_rule_condition\",\n" +
            "                    \"nameI18nKey\": \"paas.udobj.related_product_range\",\n" +
            "                    \"header\": \"返利品范围\",\n" +
            "                    \"type\": \"rule_condition\",\n" +
            "                    \"order\": 999,\n" +
            "                    \"is_hidden\": false,\n" +
            "                    \"_id\": \"related_rule_condition\"" +
            "                }";

    /**
     * 保存规则引擎时，condition需要保存两次规则，一次是聚合值 大于满额，另外一次是规则本身
     * 此方法就是用来拼接规则本身一次的rule_code
     *
     * @param id id
     * @return {@code String}
     */
    public static String getSpecialId(String id) {
        return id.concat("_c");
    }


    /**
     * 是否是优惠券使用范围
     *
     * @param data
     * @return boolean
     */
    public static boolean isCouponRangeType(HardProductData.ProductData data) {
        return CouponConstants.HardModeItemType.AGGREGATE_RULE.getValue().equals(data.getType());
    }


    public static boolean isFieldType(String fieldNameType) {
        return PricePolicyConstants.FieldNameType.FIELD.toString().toLowerCase().equals(fieldNameType);
    }

    public static boolean isAggregateType(String fieldNameType) {
        return PricePolicyConstants.FieldNameType.AGGREGATE.toString().toLowerCase().equals(fieldNameType) || RebateConstants.REBATE_RANGE.equals(fieldNameType);
    }

    public static void removeButton(StandardListHeaderController.Result result) {
        removeButton(result, null);
    }

    public static void removeButton(StandardListHeaderController.Result result, List<String> removes) {
        if (null == result || null == result.getLayout()) {
            return;
        }
        ILayout layout = new Layout(result.getLayout());
        List<IButton> buttonList = layout.getButtons();
        if (CollectionUtils.empty(buttonList)) {
            return;
        }
        List<String> removeButton = Lists.newArrayList(TO_REMOVE_BUTTON);
        if (CollectionUtils.notEmpty(removes)) {
            removeButton.addAll(removes);
        }
        if (null != result.getObjectDescribe() && RebateConstants.REBATE_API_NAME.equals(result.getObjectDescribe().get("api_name"))) {
            removeButton.remove(ObjectAction.BATCH_EXPORT.getActionCode());
            removeButton.remove(ObjectAction.BATCH_IMPORT.getActionCode());
        }
        if (null != result.getObjectDescribe() && CouponConstants.COUPON_PLAN_API_NAME.equals(result.getObjectDescribe().get("api_name"))) {
            removeButton.remove(ObjectAction.BATCH_EXPORT.getActionCode());
        }
        buttonList.removeIf(button -> removeButton.contains(button.getAction()));
        //移动端移除新建按钮
        if (!RequestUtil.isWebRequest() || RequestUtil.isMobileDeviceRequest()) {
            buttonList.removeIf(button -> ObjectAction.CREATE.getActionCode().contains(button.getAction()));
        }
        layout.setButtons(buttonList);
    }


    /**
     * 删除规则并保存规则
     *
     * @param objectDataCp                    对象数据cp
     * @param dbMasterData                    数据库中主数据
     * @param user                            用户
     * @param translateConditionToRuleService 翻译条件规则服务
     * @param consumer                        消费者
     */
    public static void deleteRuleAndSaveRule(IObjectData objectDataCp, IObjectData dbMasterData, User user, TranslateConditionToRuleService translateConditionToRuleService,
                                             Consumer<String> consumer) {
        //先删除之前的聚合规则，和规则引擎中的数据
        translateConditionToRuleService.deleteAggRuleEngine(dbMasterData, user);
        //log.info("delete agg rule and engine data success");
        try {
            //创建新的聚合规则，和保存到规则引擎
            //log.info("begin create update agg rule and engine data");
            translateConditionToRuleService.create(objectDataCp, user);
            //log.info("success create update agg rule and engine data");
            //log.info("begin update data");
            //执行具体的保存，或者更新逻辑
            consumer.accept(null);
            log.info("success update data");
        } catch (Exception e) {
            translateConditionToRuleService.deleteAggRuleEngine(objectDataCp, user);
            log.error("create data exception rollback data msg is{}", e.getMessage());
            //如果发生异常，则把之前的规则重新进行保存
            translateConditionToRuleService.create(dbMasterData, user);
            log.error("rollback data success");
            throw e;
        }
    }


    public static JSONObject parseMiscContent(String miscContent) {
        JSONObject jsonObject;
        try {
            jsonObject = (JSONObject) JSON.parse(miscContent);
        } catch (Exception e) {
            log.error("parseMiscContent error" + miscContent);
            jsonObject = new JSONObject();
        }

        return jsonObject;
    }

    public static Map getMiscMap(Object miscContent) {
        if (miscContent != null) {
            if (miscContent instanceof Map) {
                return (Map) miscContent;
            } else {
                String miscContentStr = miscContent.toString();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(miscContentStr)) {
                    return parseMiscContent(miscContentStr);
                }
            }
        }

        return null;
    }

    /**
     * 得到聚合值id
     *
     * @param aggRuleWheres aggRuleWheres
     * @return {@code List<String>}
     */
    public static List<String> getAggregateValueIds(List<RuleWhere> aggRuleWheres) {
        //聚合值id
        List<String> aggregateValueIds = Lists.newArrayList();
        for (RuleWhere aggRuleWhere : aggRuleWheres) {
            List<RuleWhere.FiltersBean> filters = aggRuleWhere.getFilters();
            if (CollectionUtils.empty(filters)) {
                continue;
            }
            for (RuleWhere.FiltersBean filter : filters) {
                if (isAggregateType(filter.getFieldNameType())) {
                    aggregateValueIds.add(filter.getFieldName());
                }
            }
        }
        return aggregateValueIds;
    }



    public static boolean isCouponPlanObj(IObjectData objectData) {
        String describeApiName = Optional.ofNullable(objectData.get(IObjectData.DESCRIBE_API_NAME, String.class)).orElse("");
        return CouponConstants.COUPON_PLAN_API_NAME.equals(describeApiName);
    }



    public static LogInfo.DiffObjectData getDiffObjectData(String fieldApiName, List<LogInfo.DiffObjectData> diffObjectDataList) {
        if (CollectionUtils.empty(diffObjectDataList)) {
            return null;
        }
        return diffObjectDataList.stream()
                .filter(diffObjectData -> fieldApiName.equals(diffObjectData.getFieldApiName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 在recordList获取指定对象的id
     *
     * @param recordList 记录列表
     * @param apiName    api名称
     * @return {@code String}
     */
    public static String getDataIdByLogRecordList(List<LogRecord> recordList, String apiName) {
        return recordList.stream()
                .filter(logRecord -> ActionType.Add.getId().equals(logRecord.getOperationType()))
                .map(LogRecord::getObjectInfo)
                .filter(x -> apiName.equals(x.getObjectApiName()))
                .findFirst()
                .map(ObjectInfo::getObjectDatas)
                .map(objectDate -> objectDate
                        .stream()
                        .map(ObjectInfo.ObjectData::getDataId)
                        .findFirst()
                        .orElse(Strings.EMPTY)
                )
                .orElse(Strings.EMPTY);

    }

    public static void formFieldConsumer(StandardDescribeLayoutController.Result result, Consumer<List<IFormField>> consumer) {
        Optional.ofNullable(result)
                .map(StandardDescribeLayoutController.Result::getLayout)
                .map(LayoutExt::of)
                .flatMap(LayoutExt::getFormComponent)
                .ifPresent(x -> {
                    FormComponentExt ext = FormComponentExt.of(x);
                    List<IFieldSection> fieldSections = ext.getFieldSections();
                    for (IFieldSection fieldSection : fieldSections) {
                        if (!"base_field_section__c".equals(fieldSection.getName())) {
                            continue;
                        }
                        List<IFormField> fields = fieldSection.getFields();
                        if (CollectionUtils.empty(fields)) {
                            return;
                        }
                        consumer.accept(fields);
                        fieldSection.setFields(fields);
                    }
                    ext.setFieldSections(fieldSections);
                });
    }

    /**
     * 下发条件选项卡，并且根据返利类型修改其选项卡名称
     *
     * @param result 结果
     */
    public static void issueConditionTab(AbstractStandardDetailController.Result result) {
        String componentName = "related_rule_condition";
        //判断是不是货返,如果是货返显示的名称不一样
        boolean isProductType = Optional.ofNullable(result)
                .map(AbstractStandardDetailController.Result::getData)
                .map(x -> x.get(RebateConstants.RebateField.REBATE_TYPE.getApiName()))
                .map(rebateType -> RebateConstants.RebateType.PRODUCT.getValue().equals(rebateType))
                .orElse(false);

        Optional.ofNullable(result).map(AbstractStandardDetailController.Result::getLayout)
                .ifPresent(x -> {
                    ILayout layout = new Layout(x);
                    LayoutExt layoutExt = LayoutExt.of(layout);
                    try {
                        List<IComponent> components = layoutExt.getComponents();
                        //如果布局中已经存在 产品条件，则不再下发
                        IComponent iComponent = components.stream()
                                .filter(component -> componentName.equals(component.getName()))
                                .findFirst()
                                .orElse(null);
                        if (iComponent == null) {
                            TabSection tabSection = new TabSection();
                            tabSection.setApiName("tab_related_rule_condition");
                            List<String> whatComponents = Lists.newArrayList(componentName);
                            if (isProductType) {
                                tabSection.setHeader(I18N.text("paas.udobj.related_product_range"));
                                CouponBaseUtils.issueWhatTab(result, tabSection, whatComponents, RULE_RANGE_JSON);
                            } else {
                                tabSection.setHeader(I18N.text("paas.udobj.related_rule_condition"));
                                CouponBaseUtils.issueWhatTab(result, tabSection, whatComponents, RULE_CONDITION_JSON);
                            }
                        } else {
                            //如果是货返进行名称修改
                            if (isProductType) {
                                iComponent.setHeader(I18N.text("paas.udobj.related_product_range"));
                                iComponent.set("nameI18nKey", "paas.udobj.related_product_range");
                                //处理布局在tab时候的名称
                                components.stream()
                                        .filter(component -> "tabs".equals(component.getType()))
                                        .findFirst()
                                        .map(TabsComponent.class::cast)
                                        .ifPresent(tabsComponent -> {
                                            List<TabSection> tabs = tabsComponent.getTabs();
                                            if (CollectionUtils.notEmpty(tabs)) {
                                                tabs.stream()
                                                        .filter(tab -> Optional.ofNullable(tab.getApiName()).orElse("").startsWith(componentName))
                                                        .findFirst()
                                                        .ifPresent(tabSection -> {
                                                            tabSection.setHeader(I18N.text("paas.udobj.related_product_range"));
                                                            tabSection.set("nameI18nKey", "paas.udobj.related_product_range");
                                                        });
                                            }
                                            tabsComponent.setTabs(tabs);
                                        });
                            } else {
                                iComponent.setHeader(I18N.text("paas.udobj.related_rule_condition"));
                                iComponent.set("nameI18nKey", "paas.udobj.related_rule_condition");
                            }


                        }
                    } catch (MetadataServiceException e) {
                        log.error("getComponents has error msg is{}", e.getMessage());
                    }
                });
    }

    /**
     * 下发what标签
     *
     * @param result         结果
     * @param tabSection     标签部分
     * @param whatComponents what组件
     * @param componentJson  组件json
     */
    public static void issueWhatTab(AbstractStandardDetailController.Result result, TabSection tabSection, List<String> whatComponents, String componentJson) {
        Optional.ofNullable(result).map(AbstractStandardDetailController.Result::getLayout)
                .ifPresent(x -> {
                    ILayout layout = new Layout(x);
                    LayoutExt layoutExt = LayoutExt.of(layout);
                    try {
                        //web端显示tab按钮
                        List<IComponent> components = layoutExt.getComponents();
                        components.stream().filter(z -> "tabs".equals(z.getType()))
                                .findFirst().ifPresent(component -> {
                            TabsComponent tabsComponent = (TabsComponent) component;
                            List<TabSection> tabs = tabsComponent.getTabs();
                            tabs.add(tabSection);
                            tabsComponent.setTabs(tabs);

                            List<List<String>> componentsList = tabsComponent.getComponents();
                            componentsList.add(whatComponents);
                            tabsComponent.setComponents(componentsList);
                        });
                        //移动端显示tab按钮
                        components.stream().filter(z -> "navigation".equals(z.getType()))
                                .findFirst().ifPresent(component -> {
                            NavigationComponent navigationComponent = (NavigationComponent) component;
                            List<String> componentsList = navigationComponent.getComponents();
                            componentsList.addAll(whatComponents);
                            navigationComponent.setComponents(componentsList);
                        });
                        IComponent iComponent = ComponentFactory.newInstance(componentJson);
                        iComponent.setHeader(I18N.text((String) iComponent.get("nameI18nKey")));
                        components.add(iComponent);
                        layoutExt.setComponents(components);
                    } catch (MetadataServiceException e) {
                        log.error("getComponents error");
                    }
                });
    }

    /**
     * 获取公共对象的用户
     *
     * @param actionContext
     * @param objectDescribe
     * @return
     */
    public static User getPublicUser(ActionContext actionContext, IObjectDescribe objectDescribe) {
        User user = actionContext.getUser();
        if (objectDescribe.isPublicObject()) {
            user = User.systemUser(objectDescribe.getUpstreamTenantId());
        }
        return user;
    }

    public static User getCouponObjUser(User srcUser, RebateCouponQuery.PublicObjInfo publicObjInfo) {
        User user = srcUser;
        if (null != publicObjInfo && publicObjInfo.isPublicObject()) {
            user = User.systemUser(publicObjInfo.getUpstreamTenantId());
        }
        return user;
    }
}
