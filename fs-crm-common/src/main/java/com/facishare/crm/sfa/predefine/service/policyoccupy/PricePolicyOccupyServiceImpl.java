package com.facishare.crm.sfa.predefine.service.policyoccupy;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SystemConstants.ActionCode;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyAmortizeService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants.AmortizeInfoField;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants.ModifyType;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants.PricePolicyField;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyLimit.LimitObjType;
import com.facishare.crm.sfa.predefine.service.pricepolicy.model.PriceRule.LimitType;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.DetailProductConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants.SalesOrderProductField;
import com.facishare.crm.sfa.utilities.constant.policyoccupy.PricePolicyLimitConstants.*;
import com.facishare.crm.sfa.utilities.util.CommonSearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.StopWatch;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_POLICY_OCCUPY_LOCK;


@Service
@Slf4j
public class PricePolicyOccupyServiceImpl implements PricePolicyOccupyService {

    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private static final PricePolicyAmortizeService amortizeService = SpringUtil.getContext().getBean(PricePolicyAmortizeService.class);
    private static final RedissonClient redissonClient = SpringUtil.getContext().getBean("redissonClient", RedissonClient.class);
    private static final InfraServiceFacade infraServiceFacade = SpringUtil.getContext().getBean(InfraServiceFacade.class);

    private static Map<String, LimitObjType> limitObjTypeMap;

    static {
        // 获取价格政策限制对象配置
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config", config -> {
            if (StringUtils.isEmpty(config.get("price_policy.limit_obj_type"))) {
                limitObjTypeMap = Maps.newHashMap();
            } else {
                String limitObjType = config.get("price_policy.limit_obj_type");
                List<LimitObjType> limitObjTypes = JSON.parseArray(limitObjType, LimitObjType.class);
                limitObjTypeMap = limitObjTypes.stream().collect(Collectors.toMap(LimitObjType::getName, Function.identity(), (key1, key2) -> key2));
            }
        });
    }

    @Override
    public List<String> getOccupyRLock(List<IObjectData> policyLimits, IObjectData masterData, String tenantId) {
        StopWatch stopWatch = StopWatch.createStarted("getOccupyRLock");
        Map<String, LimitObjType> allLimitObjTypeMap = getPricePolicyLimitObjTypeMap(tenantId);
        stopWatch.lap("getPricePolicyLimitObjTypeMap");
        Set<String> rLocks = Sets.newHashSet();
        policyLimits.forEach(policyLimit -> {
            String aggregateFieldValue = null;
            String accountMode = policyLimit.get(PricePolicyLimitDetailField.ACCOUNT_MODE, String.class);
            if (StringUtils.isNotEmpty(accountMode) && AccountModeEnum.EQUAL.name().equals(accountMode)) {
                String limitObjType = policyLimit.get(PricePolicyLimitDetailField.LIMIT_OBJ_TYPE, String.class);
                String aggregateFieldApiName = allLimitObjTypeMap.get(limitObjType).getAggregateFieldApiName();
                aggregateFieldValue = masterData.get(aggregateFieldApiName, String.class);
            }
            String newKey = Stream.of(tenantId, policyLimit.getId(), aggregateFieldValue).filter(StringUtils::isNotBlank).collect(Collectors.joining("_"));
            stopWatch.lap("getKey");
            RLock tryLock = infraServiceFacade.tryLock(AppFrameworkConfig.getDuplicateSearchLockWaitTimeSeconds(), AppFrameworkConfig.getDuplicateSearchLockWaitLeaseTimeSeconds(), TimeUnit.SECONDS, newKey);
            stopWatch.lap("tryLock");
            if (null == tryLock) {
                throw new ValidateException(I18N.text(SFA_POLICY_OCCUPY_LOCK));
            }
            rLocks.add(newKey);
        });
        stopWatch.logSlow(2);
        return Lists.newArrayList(rLocks);
    }

    @Override
    public List<String> getOccupyRLock(User user, List<IObjectData> masterDataList) {
        List<String> allRLock = Lists.newArrayList();
        List<String> orderIds = masterDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<IObjectData> orderOccupyList = getPolicyLimitOccupy(user, null, orderIds, Utils.ORDER_OCCUPY_API_NAME);
        if (CollectionUtils.size(orderOccupyList) == 0) {
            return allRLock;
        }
        String tenantId = user.getTenantId();
        List<String> policyLimitIds = orderOccupyList.stream().map(v -> v.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class)).collect(Collectors.toList());
        List<IObjectData> policyLimitList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, policyLimitIds, Utils.PRICE_POLICY_LIMIT_ACCOUNT_API_NAME);
        for (IObjectData masterData : masterDataList) {
            allRLock.addAll(getOccupyRLock(policyLimitList, masterData, tenantId));
        }
        return allRLock;
    }

    @Override
    public void unOccupyLock(List<String> rLocks) {
        if (CollectionUtils.size(rLocks) > 0) {
            List<String> keys = Lists.newArrayList();
            rLocks.forEach(rLockName -> {
                RLock rLock = redissonClient.getLock(rLockName);
                if (rLock.isLocked()) {
                    infraServiceFacade.unlock(rLock);
                    keys.add(rLockName);
                }
            });
            log.info("occupy unlock keys :{}", JSON.toJSONString(keys));
        }
    }

    @Override
    public Map<String, LimitObjType> getPricePolicyLimitObjTypeMap(String tenantId) {
        return getPricePolicyLimitObjTypeMap(tenantId, null, null);
    }

    @Override
    public Map<String, LimitObjType> getPricePolicyLimitObjTypeMap(String tenantId, String masterApiName, String detailApiName) {
        String customLimitObjType = bizConfigThreadLocalCacheService.getBizConfig(tenantId, "custom_limit_obj_type");
        Map<String, LimitObjType> allLimitObjTypeMap = Maps.newHashMap();
        allLimitObjTypeMap.putAll(limitObjTypeMap);
        if (StringUtils.isNotEmpty(customLimitObjType)) {
            List<LimitObjType> customLimitObjTypes = JSON.parseArray(customLimitObjType, LimitObjType.class);
            allLimitObjTypeMap.putAll(customLimitObjTypes.stream().collect(Collectors.toMap(LimitObjType::getName, Function.identity(), (key1, key2) -> key2)));
        }
        if (CollectionUtils.size(allLimitObjTypeMap) > 0) {
            allLimitObjTypeMap.forEach((key, limitObjType) ->
                    limitObjType.setAggregateObjApiName(limitObjType.getMasterFlag() ? masterApiName : detailApiName));
        }
        return allLimitObjTypeMap;
    }

    @Override
    public List<String> getAllPricePolicyIds(IObjectData data, List<IObjectData> detailDataList) {
        // 所有政策ids
        Set<String> allPricePolicyIds = Sets.newHashSet();
        String masterPolicyId = data.get(PricePolicyConstants.PRICE_POLICY_ID, String.class);
        // 主对象的政策id
        if (StringUtils.isNotBlank(masterPolicyId)) {
            allPricePolicyIds.add(masterPolicyId);
        }
        // 兼容查询主对象占用量的价格政策ids
        List<String> occupyPolicyIds = data.get(PricePolicyConstants.PRICE_OCCUPY_POLICY_IDS, List.class, Lists.newArrayList());
        if (CollectionUtils.size(occupyPolicyIds) > 0) {
            allPricePolicyIds.addAll(occupyPolicyIds);
        }
        detailDataList.forEach(detail -> {
            List<String> priceOccupyPolicyIds = detail.get(PricePolicyConstants.PRICE_OCCUPY_POLICY_IDS, List.class);
            String detailPricePolicyId = detail.get(PricePolicyConstants.PRICE_POLICY_ID, String.class);
            if (StringUtils.isNotEmpty(detailPricePolicyId)) {
                allPricePolicyIds.add(detailPricePolicyId);
            }
            if (CollectionUtils.size(priceOccupyPolicyIds) > 0) {
                allPricePolicyIds.addAll(priceOccupyPolicyIds);
            }
        });
        log.info("now order matching policy {}", JSON.toJSONString(allPricePolicyIds));
        return Lists.newArrayList(allPricePolicyIds);
    }

    @Override
    public List<String> getAllPricePolicyRuleIds(IObjectData data, List<IObjectData> detailDataList) {
        // 所有规则ids
        Set<String> allPricePolicyRuleIds = Sets.newHashSet();
        //主对象的规则ids
        List<String> masterRuleIds = data.get(PricePolicyConstants.RULE_IDS, List.class, Lists.newArrayList());
        if (CollectionUtils.size(masterRuleIds) > 0) {
            allPricePolicyRuleIds.addAll(masterRuleIds);
        }
        // 占用量规则ids
        List<String> masterOccupyRuleIds = data.get(PricePolicyConstants.PRICE_OCCUPY_POLICY_RULE_IDS, List.class, Lists.newArrayList());
        if (CollectionUtils.size(masterOccupyRuleIds) > 0) {
            allPricePolicyRuleIds.addAll(masterOccupyRuleIds);
        }
        // 从对象的规则ids
        detailDataList.forEach(detail -> {
            List<String> priceOccupyRuleIds = detail.get(PricePolicyConstants.PRICE_OCCUPY_POLICY_RULE_IDS, List.class, Lists.newArrayList());
            List<String> detailRuleIds = detail.get(PricePolicyConstants.RULE_IDS, List.class, Lists.newArrayList());
            if (CollectionUtils.size(priceOccupyRuleIds) > 0) {
                allPricePolicyRuleIds.addAll(priceOccupyRuleIds);
            }
            if (CollectionUtils.size(detailRuleIds) > 0) {
                allPricePolicyRuleIds.addAll(detailRuleIds);
            }
        });
        log.info("now order matching policy rule {}", JSON.toJSONString(allPricePolicyRuleIds));
        return Lists.newArrayList(allPricePolicyRuleIds);
    }

    @Override
    public Map<String, BigDecimal> getDynamicAmount(User user, String requestId) {
        Map<String, Map<String, Object>> amortizeMap = amortizeService.getAmortizeCache(user, requestId);
        log.info("getDynamicAmount amortizeMap:{}", CollectionUtils.size(amortizeMap) > 0 ? JSON.toJSONString(amortizeMap) : "Not DynamicAmount");
        // key规则id value具体金额
        Map<String, BigDecimal> dynamicAmountMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(amortizeMap)) {
            IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), Utils.AMORTIZE_INFO_API_NAME);
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(AmortizeInfoField.AMORTIZE_AMOUNT);
            int decimalPlaces = Integer.parseInt(fieldDescribe.get("decimal_places", String.class));
            BigDecimal policyDynamicAmount;
            BigDecimal dynamicAmount;
            for (Map.Entry<String, Map<String, Object>> ruleMap : amortizeMap.entrySet()) {
                Map<String, Object> ruleValueMap = ruleMap.getValue();
                policyDynamicAmount = new BigDecimal(ruleValueMap.getOrDefault(PricePolicyConstants.POLICY_DYNAMIC_AMOUNT, "0").toString());
                // 负转正并且设置小数位
                dynamicAmount = BigDecimal.ZERO.subtract(policyDynamicAmount).setScale(decimalPlaces, BigDecimal.ROUND_UP);
                String[] keys = ruleMap.getKey().split("_");
                // 按照政策合并
                String key = keys[0];
                dynamicAmountMap.merge(key, dynamicAmount, BigDecimal::add);
                // 按照规则合并
                String key1 = String.join("_", keys[1]);
                dynamicAmountMap.merge(key1, dynamicAmount, BigDecimal::add);
                // 按照规则+index 合并
                String key2 = String.join("_", keys[1], keys[2]);
                dynamicAmountMap.merge(key2, dynamicAmount, BigDecimal::add);
                // 按照政策+index 合并
                String key3 = String.join("_", keys[0], keys[2]);
                dynamicAmountMap.merge(key3, dynamicAmount, BigDecimal::add);
            }
        }
        log.info("getDynamicAmount amortizeMap convert dynamicAmountMap:{}", CollectionUtils.size(dynamicAmountMap) > 0 ? JSON.toJSONString(dynamicAmountMap) : "Not DynamicAmount");
        return dynamicAmountMap;
    }

    @Override
    public List<IObjectData> getPricePolicyDetailLimit(User user, List<String> productIds, List<String> policyIds, List<String> policyRuleIds) {
        SearchTemplateQuery query = buildPolicyLimitQuery(user, productIds, policyIds, policyRuleIds);
        return CommonSearchUtil.findDataBySearchQueryIgnoreAll(user, Utils.PRICE_POLICY_LIMIT_ACCOUNT_API_NAME, query);
    }

    private SearchTemplateQuery buildPolicyLimitQuery(User user, List<String> productIds, List<String> pricePolicyIds, List<String> pricePolicyRuleIds) {
        boolean limitMultiPolicy = bizConfigThreadLocalCacheService.isOpenLimitMultiPolicy(user.getTenantId());
        List<IFilter> filters = Lists.newArrayList();
        if (limitMultiPolicy) {
            SearchUtil.fillFilterHasAnyOf(filters, PricePolicyLimitDetailField.PRICE_POLICY_IDS, pricePolicyIds);
        }
        SearchUtil.fillFilterIn(filters, PricePolicyLimitDetailField.PRICE_POLICY_ID, pricePolicyIds);
        SearchUtil.fillFilterIn(filters, PricePolicyLimitDetailField.PRICE_POLICY_RULE_ID, pricePolicyRuleIds);
        SearchUtil.fillFilterIsNull(filters, PricePolicyLimitDetailField.PRICE_POLICY_RULE_ID);
        SearchUtil.fillFilterIsNull(filters, PricePolicyLimitDetailField.PRODUCT_ID);
        if (CollectionUtils.size(productIds) > 0) {
            SearchUtil.fillFilterIn(filters, PricePolicyLimitDetailField.PRODUCT_ID, productIds);
        }
        SearchTemplateQuery query = CommonSearchUtil.getSearchTemplateQuery(filters, 1000);
        // 注意如果新增/删除filter 需要同步更新 populateFilters方法
        if (limitMultiPolicy) {
            if (CollectionUtils.notEmpty(productIds)) {
                query.setPattern("(1 or 2) and (3 or 4) and (5 or 6)");
            } else {
                query.setPattern("(1 or 2) and (3 or 4) and (5)");
            }
        } else {
            if (CollectionUtils.notEmpty(productIds)) {
                query.setPattern("1 and (2 or 3) and (4 or 5)");
            } else {
                query.setPattern("1 and (2 or 3) and (4)");
            }
        }
        return query;
    }

    @Override
    public BigDecimal getCurrentAlready(IObjectData masterData, List<IObjectData> detailDataList, IObjectData policyLimit, IObjectData policyData, Map<String, BigDecimal> dynamicAmountMap, Map<String, LimitObjType> limitObjTypeMap) {
        String limitObjType = policyLimit.get(PricePolicyLimitDetailField.LIMIT_OBJ_TYPE, String.class);
        String limitType = policyLimit.get(PricePolicyLimitDetailField.LIMIT_TYPE, String.class);
        String range = policyLimit.get(PricePolicyLimitDetailField.RANGE, String.class);
        String dimension = policyLimit.get(PricePolicyLimitDetailField.DIMENSION, String.class);
        String productId = policyLimit.get(PricePolicyLimitDetailField.PRODUCT_ID, String.class);
        String policyId = policyLimit.get(PricePolicyLimitDetailField.PRICE_POLICY_ID, String.class);
        String ruleId = policyLimit.get(PricePolicyLimitDetailField.PRICE_POLICY_RULE_ID, String.class);
        List policyIds = policyLimit.get(PricePolicyLimitDetailField.PRICE_POLICY_IDS, List.class);
        String dataId = policyData.getId();
        if (!RangeEnum.MULTI_POLICY.name().equals(range)) {
            // 按政策限制还是按规则限制
            dataId = policyLimit.get(RangeEnum.valueOf(range).getName(), String.class);
        } else {
            // 多政策校验
            if (CollectionUtils.notEmpty(policyIds) && policyIds.contains(policyData.getId())) {
                policyId = policyData.getId();
            } else {
                return BigDecimal.ZERO;
            }
        }
        // 限制对象对应字段
        String limitFieldApiName = limitObjTypeMap.get(limitObjType).getLimitFieldApiName();
        // 限制对象对应字段的值
        String fieldValue = policyLimit.get(limitFieldApiName, String.class);
        // 订单对象对应字段
        String targetFieldApiName = limitObjTypeMap.get(limitObjType).getAggregateFieldApiName();
//        String targetObjApiName = limitObjTypeMap.get(limitObjType).getAggregateObjApiName();
        boolean orderMaster = limitObjTypeMap.get(limitObjType).getMasterFlag();
        // 对比限制对象去匹配订单
        if (StringUtils.isNotBlank(fieldValue)) {
            if (orderMaster) {
                String targetFieldValue = masterData.get(targetFieldApiName, String.class);
                log.info("targetFieldApiName {}, order targetFieldValue {} limit fieldValue {}", targetFieldApiName, targetFieldValue, fieldValue);
                if (!fieldValue.equals(targetFieldValue)) {
                    return BigDecimal.ZERO;
                }
            } else {
                Predicate<IObjectData> predicate = v -> StringUtils.isNotEmpty(v.get(targetFieldApiName, String.class)) && v.get(targetFieldApiName, String.class).equals(fieldValue);
                if (detailDataList.stream().noneMatch(predicate)) {
                    return BigDecimal.ZERO;
                }
            }
        }
        // 价格政策信息 修改类型 master/detail
        String modifyType = policyData.get(PricePolicyField.MODIFY_TYPE, String.class);
        // 聚合订单明细行上字段名称 主品赠品数量、赠品金额、主品的优惠金额
        String aggregateApiName = LimitType.valueOf(limitType).getAggregateApiName();
        BigDecimal already;
        if (LimitType.ORIGINAL_AMOUNT.name().equals(limitType)) {
            already = getCurrentDynamicAmount(detailDataList, dynamicAmountMap, orderMaster, dataId, targetFieldApiName, fieldValue);
        } else {
            CurrentUsedPredicateArg currentUsedPredicateArg = CurrentUsedPredicateArg.builder()
                    .dimension(dimension).productId(productId)
                    .targetFieldApiName(targetFieldApiName).orderFieldValue(fieldValue)
                    .range(range).policyId(policyId).ruleId(ruleId).
                    modifyType(modifyType).orderMaster(orderMaster).build();
            Predicate<IObjectData> currentUsedPredicate = buildCurrentUsedPredicate(currentUsedPredicateArg);
            already = getCurrentOccupy(detailDataList, aggregateApiName, currentUsedPredicate);
        }
        return already;
    }

    /**
     * 整单条件
     *
     * @param arg CurrentUsedPredicateArg
     * @return Predicate<IObjectData>
     */
    private Predicate<IObjectData> buildCurrentUsedPredicate(CurrentUsedPredicateArg arg) {
        String dimension = arg.getDimension();
        String productId = arg.getProductId();
        String targetFieldApiName = arg.getTargetFieldApiName();
        String orderFieldValue = arg.getOrderFieldValue();
        String range = arg.getRange();
        String policyId = arg.getPolicyId();
        String ruleId = arg.getRuleId();
        String modifyType = arg.getModifyType();
        List policyIds = arg.getPolicyIds();
        Set<String> policyIdSet = new HashSet<>();
        if (StringUtils.isNotBlank(policyId)) {
            policyIdSet.add(policyId);
        }
        if (CollectionUtils.notEmpty(policyIds)) {
            policyIdSet.addAll(policyIds);
        }
        boolean orderMaster = arg.getOrderMaster();
        Predicate<IObjectData> currentUsedPredicate;
        Predicate<IObjectData> policyPredicate = v -> StringUtils.isNotEmpty(v.get(PricePolicyConstants.PRICE_POLICY_ID, String.class, null))
                && policyIdSet.contains(v.get(PricePolicyConstants.PRICE_POLICY_ID, String.class));
        Predicate<IObjectData> rulePredicate = v -> StringUtils.isNotEmpty(v.get(PricePolicyConstants.RULE_IDS, String.class, null)) &&
                JSON.parseArray(v.get(PricePolicyConstants.RULE_IDS, String.class)).toJavaList(String.class).contains(ruleId);
        if (range.equals(RangeEnum.RULE.name())) {
            policyPredicate = policyPredicate.and(rulePredicate);
        }
        // 赠品
        Predicate<IObjectData> giftPredicate = v -> StringUtils.isNotBlank(v.get(PricePolicyLimitDetailField.PRICE_POLICY_ID, String.class))
                && StringUtils.isNotBlank(v.get(SalesOrderProductField.IS_GIVEAWAY.getApiName(), String.class)) && v.get(SalesOrderProductField.IS_GIVEAWAY.getApiName(), String.class).equals("1");
        // 本品
        Predicate<IObjectData> giveawayPredicate = v -> StringUtils.isEmpty(v.get(SalesOrderProductField.IS_GIVEAWAY.getApiName(), String.class, null)) || "0"
                .equals(v.get(SalesOrderProductField.IS_GIVEAWAY.getApiName(), String.class, null));
        // 本品
        Predicate<IObjectData> selfPredicate = giveawayPredicate;
        // 本品不是组合
        Predicate<IObjectData> selfPackagePredicate = v -> !v.containsField(DetailProductConstants.FIELD_BOM_ID) || StringUtils.isEmpty(v.get(DetailProductConstants.FIELD_BOM_ID, String.class, null));
        selfPredicate = selfPredicate.and(selfPackagePredicate);
        // 本品是组合 并且是母件
        Predicate<IObjectData> selfCpqPredicate = v -> v.containsField(DetailProductConstants.IS_PACKAGE_V) && v.containsField(DetailProductConstants.FIELD_BOM_ID)
                && StringUtils.isNotEmpty(v.get(DetailProductConstants.IS_PACKAGE_V, String.class, null))
                && v.get(DetailProductConstants.IS_PACKAGE_V, Boolean.class)
                && StringUtils.isNotEmpty(v.get(DetailProductConstants.FIELD_BOM_ID, String.class, null));
        selfCpqPredicate = selfCpqPredicate.and(giveawayPredicate);
        if (ModifyType.DETAIL.equals(modifyType)) {
            selfPredicate = selfPredicate.and(policyPredicate);
            selfCpqPredicate = selfCpqPredicate.and(policyPredicate);
        }
        currentUsedPredicate = DimensionEnum.GIFT.getValue().equals(dimension) ? giftPredicate.and(policyPredicate) : selfPredicate.or(selfCpqPredicate);
        // 整单指定赠品
        if (StringUtils.isNotBlank(productId)) {
            Predicate<IObjectData> productPredicate = v -> v.get(PricePolicyLimitDetailField.PRODUCT_ID, String.class).equals(productId);
            currentUsedPredicate = currentUsedPredicate.and(productPredicate);
        }
        if (StringUtils.isNotBlank(targetFieldApiName) && StringUtils.isNotBlank(orderFieldValue) && !orderMaster) {
            Predicate<IObjectData> otherCondition = v -> StringUtils.isNotEmpty(v.get(targetFieldApiName, String.class, null)) && v.get(targetFieldApiName, String.class).equals(orderFieldValue);
            currentUsedPredicate = currentUsedPredicate.and(otherCondition);
        }
        return currentUsedPredicate;
    }

    /**
     * 当单优惠金额
     *
     * @param originals          主品信息
     * @param dynamicAmountMap   当单分配金额
     * @param orderMaster        限制对象是否主对象
     * @param dataId             政策id/规则id
     * @param targetFieldApiName 从对象过滤字段 比如经销商
     * @param fieldValue         从对象过滤值
     * @return 优惠金额
     */
    private BigDecimal getCurrentDynamicAmount(List<IObjectData> originals, Map<String, BigDecimal> dynamicAmountMap, boolean orderMaster,
                                               String dataId, String targetFieldApiName, String fieldValue) {
        log.info("getCurrentDynamicAmount parameter dataId:{}, targetFieldApiName:{}, fieldValue:{}, dynamicAmountMap: {}", dataId, targetFieldApiName, fieldValue, JSON.toJSONString(dynamicAmountMap));
        if (orderMaster) {
            return dynamicAmountMap.getOrDefault(dataId, BigDecimal.ZERO);
        } else {
            BigDecimal tempAlready = BigDecimal.ZERO;
            for (IObjectData original : originals) {
                String orderFieldValue = original.get(targetFieldApiName, String.class);
                if (StringUtils.isNotEmpty(orderFieldValue) && orderFieldValue.equals(fieldValue)) {
                    String dataIndex = original.get(PricePolicyConstants.ORDER_DATA_INDEX, String.class);
                    String dynamicAmountKey = String.join("_", dataId, dataIndex);
                    BigDecimal tempAmount = dynamicAmountMap.getOrDefault(dynamicAmountKey, new BigDecimal(0));
                    log.info("getCurrentDynamicAmount parameter dataIndex:{}, dynamicAmountKey:{}, dynamicAmountKey-Value:{}", dataIndex, dynamicAmountKey, tempAmount);
                    tempAlready = tempAlready.add(tempAmount);
                }
            }
            log.info("getCurrentDynamicAmount reutrn value:{}", tempAlready);
            return tempAlready;
        }
    }

    /**
     * 获取当单 占用量
     *
     * @param originals        主品信息
     * @param aggregateApiName 聚合apiName(quantity数量，price_book_subtotal价目表小计)
     * @param predicate        过滤条件
     * @return 当单使用量
     */
    public BigDecimal getCurrentOccupy(List<IObjectData> originals, String aggregateApiName, Predicate<IObjectData> predicate) {
        return originals.stream().filter(predicate).map(v -> v.get(aggregateApiName, BigDecimal.class, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getHistoryAlready(List<IObjectData> occupyList, IObjectData policyLimit, IObjectData masterData, Map<String, LimitObjType> limitObjTypeMap) {
        Predicate<IObjectData> policyLimitIdPredicate = buildEqualPredicate(policyLimit, masterData, limitObjTypeMap);
        IObjectData occupyData = occupyList.stream().filter(policyLimitIdPredicate).findAny().orElse(null);
        if (null != occupyData) {
            return occupyData.get(OccupyCommonField.OCCUPY, BigDecimal.class);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 构建占用量过滤条件
     *
     * @param policyLimit 限制对象
     * @param masterData  主对象
     * @return 过滤条件
     */
    private Predicate<IObjectData> buildEqualPredicate(IObjectData policyLimit, IObjectData masterData, Map<String, LimitObjType> allLimitObjTypeMap) {
        String pricePolicyLimitId = policyLimit.getId();
        Predicate<IObjectData> policyLimitIdPredicate = v -> pricePolicyLimitId.equals(v.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class));
        String aggregateFieldValue = getLimitObjTypeAggregateFieldValue(policyLimit, masterData, allLimitObjTypeMap);
        if (StringUtils.isNotEmpty(aggregateFieldValue)) {
            Predicate<IObjectData> otherPredicate = v -> aggregateFieldValue.equals(v.get(OccupyCommonField.EQUAL_ID, String.class));
            policyLimitIdPredicate = policyLimitIdPredicate.and(otherPredicate);
        }
        return policyLimitIdPredicate;
    }

    /**
     * 获取限制对象 主对象（销售订单） 的值
     *
     * @param policyLimit 限制对象
     * @param masterData  主对象
     * @return 主对象的值
     */
    @Override
    public String getLimitObjTypeAggregateFieldValue(IObjectData policyLimit, IObjectData masterData, Map<String, LimitObjType> allLimitObjTypeMap) {
        String accountMode = policyLimit.get(PricePolicyLimitDetailField.ACCOUNT_MODE, String.class);
        if (StringUtils.isNotEmpty(accountMode) && AccountModeEnum.EQUAL.name().equals(accountMode)) {
            String limitObjType = policyLimit.get(PricePolicyLimitDetailField.LIMIT_OBJ_TYPE, String.class);
            String aggregateFieldApiName = allLimitObjTypeMap.get(limitObjType).getAggregateFieldApiName();
            return masterData.get(aggregateFieldApiName, String.class);
        }
        return null;
    }

    @Override
    public List<IObjectData> getPolicyLimitOccupy(User user, List<String> detailLimitIds, List<String> orderIds, String describeName) {
        if (CollectionUtils.empty(detailLimitIds) && CollectionUtils.empty(orderIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        if (CollectionUtils.size(detailLimitIds) > 0) {
            SearchUtil.fillFilterIn(filters, OccupyCommonField.DETAIL_LIMIT_ID, detailLimitIds);
        }
        if (CollectionUtils.size(orderIds) > 1) {
            SearchUtil.fillFilterIn(filters, OrderOccupyField.ORDER_ID, orderIds);
        } else if (CollectionUtils.size(orderIds) > 0) {
            SearchUtil.fillFilterEq(filters, OrderOccupyField.ORDER_ID, orderIds.get(0));
        }

        SearchTemplateQuery query = CommonSearchUtil.getSearchTemplateQuery(filters, 1000);
        return CommonSearchUtil.findDataBySearchQueryIgnoreAll(user, describeName, query);
    }

    /**
     * 处理价格政策占用量
     *
     * @param user         用户信息
     * @param masterData   订单主对象数据
     * @param occupyMaps   占用量map
     * @param occupyList   占用量list
     * @param totalAlready 当前匹配占用量总计
     * @param policyLimit  限量明细
     */
    private void handlePolicyOccupy(User user, IObjectData masterData, Map<String, List<IObjectData>> occupyMaps,
                                    List<IObjectData> occupyList, BigDecimal totalAlready, IObjectData policyLimit, Map<String, LimitObjType> allLimitObjTypeMap) {
        Predicate<IObjectData> policyLimitIdPredicate = buildEqualPredicate(policyLimit, masterData, allLimitObjTypeMap);
        IObjectData occupyData = occupyList.stream().filter(policyLimitIdPredicate).findAny().orElse(null);
        List<IObjectData> addOccupyList = occupyMaps.getOrDefault("add", Lists.newArrayList());
        List<IObjectData> updateOccupyList = occupyMaps.getOrDefault("edit", Lists.newArrayList());
        if (null != occupyData) {
            occupyData.set(OccupyCommonField.OCCUPY_OLD, occupyData.get(OccupyCommonField.OCCUPY, BigDecimal.class, BigDecimal.ZERO));
            occupyData.set(OccupyCommonField.OCCUPY, totalAlready);
            updateOccupyList.add(occupyData);
        } else {
            String equalsId = getLimitObjTypeAggregateFieldValue(policyLimit, masterData, allLimitObjTypeMap);
            IObjectData policyLimitOccupy = handlePolicyOccupy(user, masterData, totalAlready, policyLimit.getId(), equalsId);
            addOccupyList.add(policyLimitOccupy);
        }
        if (CollectionUtils.size(addOccupyList) > 0) {
            occupyMaps.put("add", addOccupyList);
        }
        if (CollectionUtils.size(updateOccupyList) > 0) {
            occupyMaps.put("edit", updateOccupyList);
        }
    }

    /**
     * 处理订单占用量
     *
     * @param context         ValidatorContext
     * @param occupyMaps      占用量更新map
     * @param orderOccupyList 订单占用量
     * @param policyLimit     明细限量
     * @param currentAlready  当前占用量
     */
    private void handleOrderOccupy(ValidatorContext context, Map<String, List<IObjectData>> occupyMaps, List<IObjectData> orderOccupyList,
                                   IObjectData policyLimit, BigDecimal currentAlready, String equalsId) {
        IObjectData masterData = context.getObjectData();
        String actionCode = context.getAction().getActionCode();
        String pricePolicyLimitId = policyLimit.getId();
        List<IObjectData> addOccupyList = occupyMaps.getOrDefault("add", Lists.newArrayList());
        List<IObjectData> updateOccupyList = occupyMaps.getOrDefault("edit", Lists.newArrayList());
        OccupyCommonArg.OccupyCommonArgBuilder argBuilder = OccupyCommonArg.builder()
                .tenantId(context.getUser().getTenantId())
                .masterData(masterData)
                .already(currentAlready)
                .equalId(equalsId)
                .pricePolicyLimitId(pricePolicyLimitId);
        // 当单占用量-新增
        if (ActionCode.Add.getActionCode().equals(actionCode)) {
            IObjectData orderOccupy = buildOrderOccupy(argBuilder.approvalProcess(ApprovalProcessEnum.normal.name()).build());
            addOccupyList.add(orderOccupy);
        } else if (ActionCode.Edit.getActionCode().equals(actionCode)) {
            // 当单占用量-编辑
            String editOrderId = masterData.getId();
            Predicate<IObjectData> policyLimitIdPredicate = v -> v.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class).equals(pricePolicyLimitId);
            Predicate<IObjectData> orderIdPredicate = v -> v.get(OrderOccupyField.ORDER_ID, String.class).equals(editOrderId);
            IObjectData orderOccupyData = orderOccupyList.stream().filter(policyLimitIdPredicate.and(orderIdPredicate)).findAny().orElse(null);
            // 存在订单占用量记录
            if (null != orderOccupyData) {
                // 编辑前数据
                orderOccupyData.set(OrderOccupyField.APPROVAL_PROCESS, ApprovalProcessEnum.edit_before.name());
                updateOccupyList.add(orderOccupyData);
            }
            // 编辑后数据
            IObjectData orderOccupy = buildOrderOccupy(argBuilder.approvalProcess(ApprovalProcessEnum.edit_after.name()).build());
            // 新增编辑后数据
            addOccupyList.add(orderOccupy);
        }
        if (CollectionUtils.size(addOccupyList) > 0) {
            occupyMaps.put("add", addOccupyList);
        }
        if (CollectionUtils.size(updateOccupyList) > 0) {
            occupyMaps.put("edit", updateOccupyList);
        }
    }

    @Override
    public void handleOccupy(ValidatorContext context, Map<String, List<IObjectData>> occupyMaps, List<IObjectData> occupyList,
                             List<IObjectData> orderOccupyList, BigDecimal totalAlready, BigDecimal currentAlready, IObjectData policyLimit, Map<String, LimitObjType> allLimitObjTypeMap) {
        IObjectData masterData = context.getObjectData();
        String equalsId = getLimitObjTypeAggregateFieldValue(policyLimit, masterData, allLimitObjTypeMap);
        handlePolicyOccupy(context.getUser(), masterData, occupyMaps, occupyList, totalAlready, policyLimit, allLimitObjTypeMap);
        handleOrderOccupy(context, occupyMaps, orderOccupyList, policyLimit, currentAlready, equalsId);
    }

    public List<IObjectData> getEditPolicyOccupyList(User user, List<IObjectData> orderOccupyList, List<IObjectData> masterDataList) {
        List<IObjectData> editList = Lists.newArrayList();
        // 提取价格政策明细限制id
        List<String> detailLimitIds = orderOccupyList.stream().map(v -> v.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class)).collect(Collectors.toList());
        // 获取明细限量占有量
        List<IObjectData> policyLimitOccupyList = getPolicyLimitOccupy(user, detailLimitIds, null, Utils.POLICY_OCCUPY_API_NAME);
        // 获取明细限量信息
        List<IObjectData> policyLimitList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), detailLimitIds, Utils.PRICE_POLICY_LIMIT_ACCOUNT_API_NAME);
        Map<String, LimitObjType> allLimitObjTypeMap = getPricePolicyLimitObjTypeMap(user.getTenantId());
        orderOccupyList.forEach(orderOccupyData -> {
            String detailLimitId = orderOccupyData.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class);
            String orderId = orderOccupyData.get(OrderOccupyField.ORDER_ID, String.class);
            BigDecimal orderOccupy = orderOccupyData.get(OccupyCommonField.OCCUPY, BigDecimal.class);
            // 明细限量信息
            IObjectData policyLimit = policyLimitList.stream().filter(v -> detailLimitId.equals(v.getId())).findAny().orElse(new ObjectData());
            // 销售订单信息
            IObjectData masterData = masterDataList.stream().filter(v -> orderId.equals(v.getId())).findAny().orElse(new ObjectData());
            Predicate<IObjectData> policyLimitIdPredicate = buildEqualPredicate(policyLimit, masterData, allLimitObjTypeMap);
            IObjectData occupyData = policyLimitOccupyList.stream().filter(policyLimitIdPredicate).findAny().orElse(new ObjectData());
            BigDecimal policyOccupy = occupyData.get(OccupyCommonField.OCCUPY, BigDecimal.class);
            BigDecimal totalAlready = policyOccupy.subtract(orderOccupy);
            occupyData.set(OccupyCommonField.OCCUPY, totalAlready);
            occupyData.set(OccupyCommonField.OCCUPY_OLD, policyOccupy);
            editList.add(occupyData);
        });
        return editList;
    }

    @Override
    public IObjectData handlePolicyOccupy(User user, IObjectData objectData, BigDecimal occupy, String detailLimitId, String equalId) {
        IObjectData policyOccupy = new ObjectData();
        policyOccupy.set(DBRecord.ID, serviceFacade.generateId());
        policyOccupy.set(OccupyCommonField.OCCUPY, occupy);
        policyOccupy.set(OccupyCommonField.DETAIL_LIMIT_ID, detailLimitId);
        policyOccupy.set(OccupyCommonField.EQUAL_ID, equalId);
        policyOccupy.setTenantId(user.getTenantId());
        policyOccupy.setOwner(objectData.getOwner());
        policyOccupy.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        policyOccupy.setDataOwnDepartment(objectData.getDataOwnDepartment());
        policyOccupy.setDataOwnOrganization(objectData.getDataOwnOrganization());
        policyOccupy.setDescribeApiName(Utils.POLICY_OCCUPY_API_NAME);
        return policyOccupy;
    }

    private IObjectData buildOrderOccupy(OccupyCommonArg arg) {
        IObjectData masterData = arg.getMasterData();
        IObjectData occupyData = new ObjectData();
        occupyData.set(DBRecord.ID, serviceFacade.generateId());
        occupyData.setTenantId(arg.getTenantId());
        occupyData.setDescribeApiName(Utils.ORDER_OCCUPY_API_NAME);
        occupyData.set(OrderOccupyField.ORDER_ID, masterData.getId());
        occupyData.set(OrderOccupyField.APPROVAL_PROCESS, arg.getApprovalProcess());
        occupyData.set(OccupyCommonField.DETAIL_LIMIT_ID, arg.getPricePolicyLimitId());
        occupyData.set(OccupyCommonField.OCCUPY, arg.getAlready());
        occupyData.set(OccupyCommonField.EQUAL_ID, arg.getEqualId());
        return occupyData;
    }

    @Override
    public List<ObjectDataDocument> getPolicyBeyond(User user, IObjectData masterData, List<IObjectData> detailDataList, String actionCode) {
        if (CollectionUtils.size(detailDataList) == 0) {
            return Lists.newArrayList();
        }
        List<String> allPricePolicyIds = getAllPricePolicyIds(masterData, detailDataList);
        List<String> allPricePolicyRuleIds = getAllPricePolicyRuleIds(masterData, detailDataList);
        if (CollectionUtils.size(allPricePolicyIds) == 0) {
            return Lists.newArrayList();
        }
        // 所有产品id
        Predicate<IObjectData> productPredicate = o -> StringUtils.isNotEmpty(o.get(PricePolicyLimitDetailField.PRODUCT_ID, String.class));
        Set<String> productIds = detailDataList.stream().filter(productPredicate).map(v -> v.get(PricePolicyLimitDetailField.PRODUCT_ID, String.class)).collect(Collectors.toSet());
        // 根据政策和规则查询明细限量
        List<IObjectData> policyLimits = getPricePolicyDetailLimit(user, Lists.newArrayList(productIds), allPricePolicyIds, allPricePolicyRuleIds);
        if (CollectionUtils.size(policyLimits) == 0) {
            return Lists.newArrayList();
        }
        List<String> detailLimitIds = policyLimits.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<IObjectData> occupyList = getPolicyLimitOccupy(user, detailLimitIds, null, Utils.POLICY_OCCUPY_API_NAME);
        String detailApiName = detailDataList.get(0).getDescribeApiName();
        Map<String, LimitObjType> limitObjTypeMaps = getPricePolicyLimitObjTypeMap(user.getTenantId(), masterData.getDescribeApiName(), detailApiName);
        List<IObjectData> orderOccupyList = Lists.newArrayList();
        // 订单id不为空，才获取订单占用
        if (ActionCode.Edit.getActionCode().equals(actionCode) && StringUtils.isNotEmpty(masterData.getId())) {
            List<IObjectData> tempOrderOccupyList = getPolicyLimitOccupy(user, null, Lists.newArrayList(masterData.getId()), Utils.ORDER_OCCUPY_API_NAME);
            orderOccupyList.addAll(tempOrderOccupyList);
        }
        Map<String, BigDecimal> policyLimitMaps = Maps.newHashMap();
        List<ObjectDataDocument> objectDataDocuments = Lists.newArrayList();
        policyLimits.forEach(policyLimit -> {
            String policyId = policyLimit.get(PricePolicyLimitDetailField.PRICE_POLICY_ID, String.class);
            String policyRuleId = policyLimit.get(PricePolicyLimitDetailField.PRICE_POLICY_RULE_ID, String.class);
            String productId = policyLimit.get(PricePolicyLimitDetailField.PRODUCT_ID, String.class);
            String limitType = policyLimit.get(PricePolicyLimitDetailField.LIMIT_TYPE, String.class);
            String range = policyLimit.get(PricePolicyLimitDetailField.RANGE, String.class);
            String mapKey = Stream.of(RangeEnum.MULTI_POLICY.name().equals(range) ? policyLimit.getId() : policyId, policyRuleId, productId, limitType).filter(StringUtils::isNotBlank).collect(Collectors.joining("_"));
            BigDecimal limitNumber = policyLimit.get(PricePolicyLimitDetailField.LIMIT_NUMBER, BigDecimal.class);
            String limitObjType = policyLimit.get(PricePolicyLimitDetailField.LIMIT_OBJ_TYPE, String.class);
            String dimension = policyLimit.get(PricePolicyLimitDetailField.DIMENSION, String.class);
            List policyIds = policyLimit.get(PricePolicyLimitDetailField.PRICE_POLICY_IDS, List.class);
            LimitObjType objType = limitObjTypeMaps.get(limitObjType);
            if (objType == null) {
                return;
            }
            String limitFieldApiName = objType.getLimitFieldApiName();
            // 限制对象对应字段的值
            String fieldValue = policyLimit.get(limitFieldApiName, String.class);
            String targetObjApiName = objType.getAggregateObjApiName();
            // 订单对象对应字段
            String targetFieldApiName = objType.getAggregateFieldApiName();
            boolean masterFlag = objType.getMasterFlag();
            // 对比限制对象去匹配订单
            if (StringUtils.isNotBlank(fieldValue)) {
                if (masterFlag) {
                    String targetFieldValue = masterData.get(targetFieldApiName, String.class);
                    log.info("targetFieldApiName {}, order targetFieldValue {} limit fieldValue {}", targetFieldApiName, targetFieldValue, fieldValue);
                    if (StringUtils.isEmpty(targetFieldValue) || !fieldValue.equals(targetFieldValue)) {
                        return;
                    }
                } else {
                    Predicate<IObjectData> predicate = v -> fieldValue.equals(v.get(targetFieldApiName, String.class));
                    if (DimensionEnum.GIFT.getValue().equals(dimension)) {
                        Predicate<IObjectData> giftPredicate = v -> StringUtils.isNotBlank(v.get(SalesOrderProductField.IS_GIVEAWAY.getApiName(), String.class))
                                && v.get(SalesOrderProductField.IS_GIVEAWAY.getApiName(), String.class).equals("1");
                        predicate = predicate.and(giftPredicate);
                    }
                    if (detailDataList.stream().noneMatch(predicate)) {
                        return;
                    }
                }
            }
            BigDecimal historyAlready = getHistoryAlready(occupyList, policyLimit, masterData, limitObjTypeMaps);
            // 可用 = 限制-已经占用
            BigDecimal usable = limitNumber.subtract(historyAlready);
            if (ActionCode.Edit.getActionCode().equals(actionCode)) {
                IObjectData orderOccupyData = orderOccupyList.stream().filter(v -> policyLimit.getId().equals(v.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class))).findAny().orElse(null);
                if (null != orderOccupyData) {
                    BigDecimal editHistoryAlready = orderOccupyData.get(OccupyCommonField.OCCUPY, BigDecimal.class);
                    // 编辑  可用 = 可用 + 当前单
                    usable = usable.add(editHistoryAlready);
                }
            }

            if (policyLimitMaps.containsKey(mapKey)) {
                BigDecimal oldUsable = policyLimitMaps.get(mapKey);
                if (usable.compareTo(oldUsable) >= 0) {
                    return;
                }
                objectDataDocuments.removeIf(v -> mapKey.equals(v.get("mapKey")));
            }
            policyLimitMaps.put(mapKey, usable);
            ObjectDataDocument condition = new ObjectDataDocument();
            condition.put("policy_id", policyId);
            condition.put("policy_ids", policyIds);
            condition.put("policy_rule_id", policyRuleId);
            condition.put("product_id", productId);
            condition.put("range", range);
            condition.put("aggregateObjApiName", targetObjApiName);
            condition.put("aggregateFieldApiName", targetFieldApiName);
            condition.put("limitFieldValue", policyLimit.get(limitFieldApiName, String.class));
            condition.put("dimension", policyLimit.get(PricePolicyLimitDetailField.DIMENSION, String.class));
            ObjectDataDocument limitsDocument = new ObjectDataDocument();
            limitsDocument.put(DBRecord.ID, policyLimit.getId());
            limitsDocument.put("mapKey", mapKey);
            limitsDocument.put(PricePolicyLimitDetailField.LIMIT_TYPE, policyLimit.get(PricePolicyLimitDetailField.LIMIT_TYPE, String.class));
            limitsDocument.put("usable", usable);
            limitsDocument.put("condition", condition);
            objectDataDocuments.add(limitsDocument);
        });
        return objectDataDocuments;
    }

    @Override
    public void filterPolicyLimit(String tenantId, IObjectData masterData, List<IObjectData> detailDataList,
                                  List<IObjectData> policyLimits) {
        List<String> limitTypes = policyLimits.stream().map(v -> v.get(PricePolicyLimitDetailField.LIMIT_OBJ_TYPE, String.class)).distinct().collect(Collectors.toList());
        Predicate<IObjectData> removePredicate = null;
        Map<String, LimitObjType> allLimitObjTypeMap = getPricePolicyLimitObjTypeMap(tenantId);
        for (String limitObjType : limitTypes) {
            // 订单对象对应字段
            String limitFieldApiName = allLimitObjTypeMap.get(limitObjType).getLimitFieldApiName();
            String targetFieldApiName = allLimitObjTypeMap.get(limitObjType).getAggregateFieldApiName();
            boolean masterFlag = allLimitObjTypeMap.get(limitObjType).getMasterFlag();
            if (masterFlag) {
                String orderAggregateFieldValue = masterData.get(targetFieldApiName, String.class);
                if (StringUtils.isNotEmpty(orderAggregateFieldValue)) {
                    Predicate<IObjectData> masterPredicate = v -> !orderAggregateFieldValue.equals(v.get(limitFieldApiName, String.class))
                            && limitObjType.equals(v.get(PricePolicyLimitDetailField.LIMIT_OBJ_TYPE, String.class));
                    if (limitObjType.equals(LimitObjTypeEnum.account.name())) {
                        masterPredicate = masterPredicate.and(v -> AccountModeEnum.DIFFERENCE.name().equals(v.get(PricePolicyLimitDetailField.ACCOUNT_MODE, String.class)));
                    }
                    removePredicate = removePredicate == null ? masterPredicate : removePredicate.or(masterPredicate);
                }
            } else {
                List<String> orderProductAggregateFieldValue = detailDataList.stream().map(v -> v.get(targetFieldApiName, String.class)).distinct().collect(Collectors.toList());
                if (CollectionUtils.notEmpty(orderProductAggregateFieldValue)) {
                    Predicate<IObjectData> detailPredicate = v -> !orderProductAggregateFieldValue.contains(v.get(limitFieldApiName, String.class))
                            && limitObjType.equals(v.get(PricePolicyLimitDetailField.LIMIT_OBJ_TYPE, String.class));
                    removePredicate = removePredicate == null ? detailPredicate : removePredicate.or(detailPredicate);
                }
            }
        }
        if (null != removePredicate) {
            policyLimits.removeIf(removePredicate);
        }
    }

    @Override
    public Boolean closePolicyOccupy(User user, String orderId, List<IObjectData> detailDataList) {
        return changePolicyOccupy(user, orderId, detailDataList, true);
    }

    public Boolean changePolicyOccupy(User user, String orderId, List<IObjectData> detailDataList, boolean close) {
        List<IObjectData> orderOccupyList = getPolicyLimitOccupy(user, null, Lists.newArrayList(orderId), Utils.ORDER_OCCUPY_API_NAME);
        if (CollectionUtils.empty(orderOccupyList)) {
            return true;
        }

        List<String> limitIds = orderOccupyList.stream().map(v -> v.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class)).collect(Collectors.toList());
        List<IObjectData> limitList = getLimitList(user, limitIds);
        Map<String, IObjectData> limitMap = Maps.newHashMap();
        for (IObjectData limit : limitList) {
            if (LimitType.ORIGINAL_AMOUNT.name().equals(limit.get(PricePolicyLimitDetailField.LIMIT_TYPE, String.class))) {
                // 不支持关闭本品优惠额。
                throw new ValidateException(I18N.text("sfa.policy.occupy.close.this.original.amount"));
            }
            limitMap.put(limit.getId(), limit);
        }

        List<IObjectData> occupyList = getPolicyLimitOccupy(user, limitIds, null, Utils.POLICY_OCCUPY_API_NAME);
        Map<String, IObjectData> occupyMap = occupyList.stream().collect(Collectors.toMap(limit -> limit.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class), Function.identity()));
        Map<String, LimitObjType> limitObjTypeMap = getPricePolicyLimitObjTypeMap(user.getTenantId(), Utils.SALES_ORDER_API_NAME, Utils.SALES_ORDER_PRODUCT_API_NAME);

        Map<String, IObjectData> detailSrcMap = detailDataList.stream().collect(Collectors.toMap(limit -> limit.get("id", String.class), Function.identity()));
        List<IObjectData> details = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(detailSrcMap.keySet()), Utils.SALES_ORDER_PRODUCT_API_NAME);
        for (IObjectData detail : details) {
            IObjectData detailSrc = detailSrcMap.get(detail.getId());
            if (detail.get(SalesOrderProductField.QUANTITY.getApiName(), BigDecimal.class).compareTo(detailSrc.get(SalesOrderProductField.QUANTITY.getApiName(), BigDecimal.class)) < 0) {
                //"订单产品{0}数量小于实际数量。"
                throw new ValidateException(I18N.text("sfa.policy.occupy.close.product.quantity.less",detail.getName()));
            }
        }

        List<IObjectData> masterDataList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(orderId), Utils.SALES_ORDER_API_NAME);
        List<String> allRLock = getOccupyRLock(limitList, masterDataList.get(0), user.getTenantId());
        List<IObjectData> upOrderOccupy = Lists.newArrayList();
        List<IObjectData> upLimitOccupy = Lists.newArrayList();
        for (IObjectData orderOccupy : orderOccupyList) {
            IObjectData limit = limitMap.get(orderOccupy.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class));

            BigDecimal notUse = getNotUse(limit, limitObjTypeMap, details, detailSrcMap);

            setUpOccupyData(close, orderOccupy, notUse, occupyMap, limit, upOrderOccupy, upLimitOccupy);
        }
        if (CollectionUtils.notEmpty(upOrderOccupy)) {
            serviceFacade.batchUpdateByFields(user, upOrderOccupy, Lists.newArrayList(OccupyCommonField.OCCUPY));
            serviceFacade.batchUpdateByFields(user, upLimitOccupy, Lists.newArrayList(OccupyCommonField.OCCUPY));
            log.info("occupy change flag:{} OrderOccupy:{} LimitOccupy:{}", close, JSON.toJSONString(upOrderOccupy), JSON.toJSONString(upLimitOccupy));
        }

        unOccupyLock(allRLock);
        return true;
    }

    private List<IObjectData> getLimitList(User user, List<String> limitIds) {
        return serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), limitIds, Utils.PRICE_POLICY_LIMIT_ACCOUNT_API_NAME);
    }

    private void setUpOccupyData(boolean close, IObjectData orderOccupy, BigDecimal notUse, Map<String, IObjectData> occupyMap, IObjectData limit, List<IObjectData> upOrderOccupy, List<IObjectData> upLimitOccupy) {
        if (notUse.compareTo(BigDecimal.ZERO) > 0) {
            IObjectData occupy = occupyMap.get(orderOccupy.get(OccupyCommonField.DETAIL_LIMIT_ID, String.class));
            if (close) {
                orderOccupy.set(OccupyCommonField.OCCUPY, orderOccupy.get(OccupyCommonField.OCCUPY, BigDecimal.class).subtract(notUse));
                occupy.set(OccupyCommonField.OCCUPY, occupy.get(OccupyCommonField.OCCUPY, BigDecimal.class).subtract(notUse));
                // 关闭检验订单占用量是否小于0
                if (BigDecimal.ZERO.compareTo(orderOccupy.get(OccupyCommonField.OCCUPY, BigDecimal.class)) > 0) {
                    // 订单占用量小于0，不能关闭。
                    throw new ValidateException(I18N.text("sfa.policy.occupy.close.less.zero"));
                }
            } else {
                orderOccupy.set(OccupyCommonField.OCCUPY, orderOccupy.get(OccupyCommonField.OCCUPY, BigDecimal.class).add(notUse));
                occupy.set(OccupyCommonField.OCCUPY, occupy.get(OccupyCommonField.OCCUPY, BigDecimal.class).add(notUse));
                // 打开检验是否超量
                if (occupy.get(OccupyCommonField.OCCUPY, BigDecimal.class).compareTo(limit.get(PricePolicyLimitDetailField.LIMIT_NUMBER, BigDecimal.class)) > 0) {
                    throw new ValidateException(I18N.text("sfa.policy.occupy.open.limit"));
                }
            }

            upOrderOccupy.add(orderOccupy);
            upLimitOccupy.add(occupy);
        }
    }

    private BigDecimal getNotUse(IObjectData limit, Map<String, LimitObjType> limitObjTypeMap, List<IObjectData> details, Map<String, IObjectData> detailSrcMap) {
        String policyId = limit.get(PricePolicyLimitDetailField.PRICE_POLICY_ID, String.class);
        String limitObjType = limit.get(PricePolicyLimitDetailField.LIMIT_OBJ_TYPE, String.class);
        String range = limit.get(PricePolicyLimitDetailField.RANGE, String.class);
        String dimension = limit.get(PricePolicyLimitDetailField.DIMENSION, String.class);
        String productId = limit.get(PricePolicyLimitDetailField.PRODUCT_ID, String.class);
        String ruleId = limit.get(PricePolicyLimitDetailField.PRICE_POLICY_RULE_ID, String.class);
        // 限制对象对应字段
        String limitFieldApiName = limitObjTypeMap.get(limitObjType).getLimitFieldApiName();
        // 限制对象对应字段的值
        String fieldValue = limit.get(limitFieldApiName, String.class);
        // 订单对象对应字段
        String targetFieldApiName = limitObjTypeMap.get(limitObjType).getAggregateFieldApiName();
        boolean orderMaster = limitObjTypeMap.get(limitObjType).getMasterFlag();
        CurrentUsedPredicateArg currentUsedPredicateArg = CurrentUsedPredicateArg.builder()
                .dimension(dimension).productId(productId)
                .targetFieldApiName(targetFieldApiName).orderFieldValue(fieldValue)
                .range(range).policyId(policyId).ruleId(ruleId).policyIds(limit.get(PricePolicyLimitDetailField.PRICE_POLICY_IDS, List.class)).
                modifyType(ModifyType.DETAIL).orderMaster(orderMaster).build();
        Predicate<IObjectData> currentUsedPredicate = buildCurrentUsedPredicate(currentUsedPredicateArg);
        List<IObjectData> detailThis = details.stream().filter(currentUsedPredicate).collect(Collectors.toList());

        BigDecimal notUse = BigDecimal.ZERO;
        for (IObjectData detail : detailThis) {
            IObjectData detailSrc = detailSrcMap.get(detail.getId());
            if (LimitType.GIFT_QUANTITY.name().equals(limit.get(PricePolicyLimitDetailField.LIMIT_TYPE, String.class))
                    || LimitType.ORIGINAL_QUANTITY.name().equals(limit.get(PricePolicyLimitDetailField.LIMIT_TYPE, String.class))) {
                notUse = notUse.add(detailSrc.get(SalesOrderProductField.QUANTITY.getApiName(), BigDecimal.class));
            } else if (LimitType.GIFT_AMOUNT.name().equals(limit.get(PricePolicyLimitDetailField.LIMIT_TYPE, String.class))) {
                notUse = notUse.add(detail.get(LimitType.GIFT_AMOUNT.getAggregateApiName(), BigDecimal.class)
                        .multiply(detailSrc.get(SalesOrderProductField.QUANTITY.getApiName(), BigDecimal.class))
                        .divide(detail.get(SalesOrderProductField.QUANTITY.getApiName(), BigDecimal.class), 2, RoundingMode.HALF_UP));
            }
        }
        return notUse;
    }

    @Override
    public Boolean openPolicyOccupy(User user, String orderId, List<IObjectData> detailDataList) {
        return changePolicyOccupy(user, orderId, detailDataList, false);
    }
}