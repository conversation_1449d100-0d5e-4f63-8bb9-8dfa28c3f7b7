package com.facishare.crm.sfa.predefine.service.attribute.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface AttributeRangeMode {
    String API_NAME = "AttributeRangeObj";
    String DATA_ID = "data_id";
    String DATA_OBJECT_DESCRIBE_API_NAME="data_object_describe_api_name";
    String REF_DATA_ID="ref_data_id";
    String REF_OBJECT_DESCRIBE_API_NAME="ref_object_describe_api_name";
    String REF_DATA_NO="ref_data_no";
    String ATTR_GROUP_ID="attr_group_id";
    String ATTR_GROUP_NO="attr_group_no";
    String ATTR_RANGE = "attr_range";

    @Data
    @Builder
    class AttributeRange{
        private String id;
        private String name;
        private String apiName;
        private String type;
        private Integer attrNo;
        @JSONField(name = "attribute_values")
        @JsonProperty("attribute_values")
        private List<AttributeValue> attributeValues;
        private String groupId;
        private String groupName;
        private Integer groupNo;
    }
    @Data
    class Arg {
        private String dataId;
        private String apiName;
        private List<RangeEntity> dataList;
    }
    @Data
    class RangeEntity{
        private String groupId;
        private int groupNo;
        private List<AttrEntity> attrList;
        private List<AttrEntity> nonAttrList;
    }
    @Data
    class AttrEntity{
        private String id;
        private int attrNo;
    }
    @Data
    @Builder
    class Result {
        private Boolean isSuccess;
    }

    @Data
    class QueryArg {
        private List<String> dataIds;
        private String apiName;
    }
    @Data
    @Builder
    class QueryResult {
        private Boolean isSuccess;
        private List<ObjectDataDocument> dataList;
    }
}
