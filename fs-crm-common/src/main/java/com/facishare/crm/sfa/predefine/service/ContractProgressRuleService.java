package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.model.ContractProgressModel;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.task.SaleContractTaskService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.ContractProgressConstants;
import com.facishare.crm.sfa.utilities.util.CommonSearchUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.CopyOnWriteMap;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述说明：
 * 
 * @作者：chench
 * @创建日期：2025-04-15
 */
@Service
@Slf4j
public class ContractProgressRuleService {

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private SaleContractTaskService saleContractTaskService;

    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    private final static String  I18N_KEY_CONTRACT_PROGRESS_RULE_NOT_EXIST = "contract.progress.rule.not.exist";
    private final static String  I18N_KEY_CONTRACT_PROGRESS_RULE_ID_NOT_EXIST = "contract.progress.rule.id.not.exist";

    private final static String  I18N_KEY_CONTRACT_PROGRESS_RULE_BE_USED_EXIST = "contract.progress.rule.be.used.exist";

    private final static String GOAL_KEY = "goal";
    private final static String GOAL_KEY_ADD = "goalAdd";
    private final static String GOAL_KEY_DELETE = "goalDelete";
    private final static String GOAL_KEY_UPDATE = "goalUpdate";
    private final static String GOAL_CHECK_KEY = "goalCheck";
    private final static String GOAL_CHECK_KEY_ADD = "goalCheckAdd";
    private final static String GOAL_CHECK_KEY_DELETE = "goalCheckDelete";
    private final static String GOAL_CHECK_KEY_UPDATE = "goalCheckUpdate";

    public ContractProgressModel.Result saveRule(User user, ContractProgressModel.RuleModel rule) {
        IObjectData data = new ObjectData();
        data.setTenantId(user.getTenantId());
        fillCommonField(data, rule);
        data = serviceFacade.saveObjectData(user, data);
        return ContractProgressModel.Result.builder().msg("ok").success(true).data(ObjectDataDocument.of(data)).build();
    }

    public ContractProgressModel.Result editRule(User user, ContractProgressModel.RuleModel rule) {
        if(StringUtils.isBlank(rule.getId())) {
            throw new ValidateException(I18N.text(I18N_KEY_CONTRACT_PROGRESS_RULE_ID_NOT_EXIST));
        }
        IObjectData data = serviceFacade.findObjectData(user, rule.getId(), ContractProgressConstants.Rule.DESC_API_NAME);
        if(data == null) {
            throw new ValidateException(I18N.text(I18N_KEY_CONTRACT_PROGRESS_RULE_NOT_EXIST));
        }
        fillCommonField(data, rule);
        serviceFacade.updateObjectData(user, data);
        return ContractProgressModel.Result.builder().msg("ok").success(true).data(ObjectDataDocument.of(data)).build();
    }

    public ContractProgressModel.Result invalidRule(User user, ContractProgressModel.RuleModel rule) {
        if(StringUtils.isBlank(rule.getId())) {
            throw new ValidateException(I18N.text(I18N_KEY_CONTRACT_PROGRESS_RULE_ID_NOT_EXIST));
        }
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setPermissionType(0);
        searchQuery.setLimit(1);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, ContractProgressConstants.RuleGoal.RULE_ID, rule.getId());
        searchQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, ContractProgressConstants.RuleGoal.DESC_API_NAME, searchQuery);
        if (queryResult != null && CollectionUtils.isNotEmpty(queryResult.getData())) {
            throw new ValidateException(I18N.text(I18N_KEY_CONTRACT_PROGRESS_RULE_BE_USED_EXIST));
        }
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsExcludeInvalid(ActionContextExt.of(user).getContext(), Lists.newArrayList(rule.getId()), ContractProgressConstants.Rule.DESC_API_NAME);
        if(CollectionUtils.isEmpty(dataList)) {
            throw new ValidateException(I18N.text(I18N_KEY_CONTRACT_PROGRESS_RULE_NOT_EXIST));
        }
        serviceFacade.bulkInvalid(dataList, user);
        return ContractProgressModel.Result.builder().msg("ok").success(true).data(null).build();
    }


    public ContractProgressModel.Result queryRuleList(User user, ContractProgressModel.RuleListModel arg) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        //默认取2000条，如果前端有传参数过来，则以前端的参数为准
        searchQuery.setLimit(2000);
        if(StringUtils.isNotBlank(arg.getSearchQueryInfo())) {
            searchQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
        }
        searchQuery.setPermissionType(0);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, arg.getObjectDescribeApiName(), searchQuery);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return ContractProgressModel.Result.builder().msg("ok").success(true).data(Lists.newArrayList()).build();
        } else {
            List<IObjectData> dataList = queryResult.getData();
            wrapperRule(user, dataList);
            return ContractProgressModel.Result.builder().msg("ok").success(true).data(ObjectDataDocument.ofList(dataList)).build();
        }
    }

    public void sendTask(User user, List<IObjectData> dataList, String contractId) {
        saleContractTaskService.createOrUpdateTask(user.getTenantId(), dataList, contractId);
    }
    /**
     * 批量保存目标与检查点
     * 
     * @param user
     * @param arg
     */
    @Transactional
    public ContractProgressModel.Result batchSaveGoalAndCheckPoint(User user, ContractProgressModel.RuleGoalModel arg, String saleContractName) {
        //如果目标为空，则认为是删除所有之前设置过的目标
        if(CollectionUtils.isEmpty(arg.getProgressGoalList())) {
            deleteGoalAndCheckPoint(user, arg.getContractId());
            return ContractProgressModel.Result.builder().msg("ok").success(true).data(Lists.newArrayList()).build();
        }
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        if(arg.getToCompleteTime() < c.getTimeInMillis()) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTRACT_PROGRESS_RULE_GOAL_COMPLETE_TIME_INVALID));
        }
        int goalIndex = 1;
        for(ContractProgressModel.ProgressGoalArg goalArg : arg.getProgressGoalList()) {
            if(goalArg.getGoalValue() == null || goalArg.getGoalValue().signum() < 0) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTRACT_PROGRESS_RULE_GOAL_INDEX_GOAL_VALUE_INVALID, goalIndex));
            }
            if(CollectionUtils.isNotEmpty(goalArg.getCheckList())) {
                int checkIndex = 1;
                for (ContractProgressModel.RuleGoalCheckArg checkArg : goalArg.getCheckList()) {
                    if(checkArg.getGoalValue() == null || checkArg.getGoalValue().signum() < 0 || checkArg.getGoalValue().compareTo(goalArg.getGoalValue()) > 0) {
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTRACT_PROGRESS_RULE_GOAL_CHECK_GOAL_VALUE_INVALID, goalIndex, checkIndex));
                    }
                    if(checkArg.getToCheckTime() == 0l || checkArg.getToCheckTime()< c.getTimeInMillis() || checkArg.getToCheckTime()>arg.getToCompleteTime()) {
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTRACT_PROGRESS_RULE_GOAL_CHECK_TIME_INVALID, goalIndex, checkIndex));
                    }
                    checkIndex++;
                }
            }
            goalIndex++;
        }

        if(StringUtils.isBlank(arg.getContractId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTRACT_PROGRESS_RULE_GOAL_CONTRACT_NOT_EMPTY));
        }

        Map<String, List<IObjectData>> goalAndCheckPointMap = buildAllGoalAndCheckPointMap(user, arg);

        if (CollectionUtils.isNotEmpty(goalAndCheckPointMap.get(GOAL_KEY_DELETE))) {
            serviceFacade.bulkDeleteDirect(goalAndCheckPointMap.get(GOAL_KEY_DELETE), user);
        }
        if (CollectionUtils.isNotEmpty(goalAndCheckPointMap.get(GOAL_CHECK_KEY_DELETE))) {
            serviceFacade.bulkDeleteDirect(goalAndCheckPointMap.get(GOAL_CHECK_KEY_DELETE), user);
        }

        if(CollectionUtils.isNotEmpty(goalAndCheckPointMap.get(GOAL_KEY_ADD))) {
            serviceFacade.bulkSaveObjectData(goalAndCheckPointMap.get(GOAL_KEY_ADD), user);
        }
        if(CollectionUtils.isNotEmpty(goalAndCheckPointMap.get(GOAL_KEY_UPDATE))) {
            serviceFacade.batchUpdateByFields(user, goalAndCheckPointMap.get(GOAL_KEY_UPDATE), Lists.newArrayList(
                    ContractProgressConstants.RuleGoal.RULE_ID,
                    ContractProgressConstants.RuleGoal.TO_COMPLETE_TIME,
                    ContractProgressConstants.RuleGoal.GOAL_VALUE,
                    ContractProgressConstants.RuleGoal.PRODUCT_ID));
        }

        if (CollectionUtils.isNotEmpty(goalAndCheckPointMap.get(GOAL_CHECK_KEY_ADD))) {
            serviceFacade.bulkSaveObjectData(goalAndCheckPointMap.get(GOAL_CHECK_KEY_ADD), user);
        }

        if (CollectionUtils.isNotEmpty(goalAndCheckPointMap.get(GOAL_CHECK_KEY_UPDATE))) {
            serviceFacade.batchUpdateByFields(user, goalAndCheckPointMap.get(GOAL_CHECK_KEY_UPDATE), Lists.newArrayList(
                    ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME,
                    ContractProgressConstants.RuleGoalCheck.GOAL_VALUE
            ));
        }


        saleContractTaskService.deleteTask(user.getTenantId(), goalAndCheckPointMap.get(GOAL_CHECK_KEY_DELETE));
        saleContractTaskService.createOrUpdateTask(user.getTenantId(), goalAndCheckPointMap.get(GOAL_CHECK_KEY_ADD), arg.getContractId());
        saleContractTaskService.createOrUpdateTask(user.getTenantId(), goalAndCheckPointMap.get(GOAL_CHECK_KEY_UPDATE), arg.getContractId());
        return queryGoalProgressByContractId(user, ContractProgressModel.ViewGoalModel.builder().contractId(arg.getContractId()).needCalculate(false).includeCheck(true).build(), saleContractName);
    }

    public ContractProgressModel.Result queryGoalProgressByContractId(User user, ContractProgressModel.ViewGoalModel arg, String saleContractName) {
        if(StringUtils.isBlank(arg.getContractId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTRACT_PROGRESS_RULE_GOAL_VIEW_PROGRESS_CONTRACT_NOT_EMPTY));
        }
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnQuote(Boolean.TRUE);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, ContractProgressConstants.RuleGoal.CONTRACT_ID, arg.getContractId());
        searchQuery.setFilters(filters);

        List<IObjectData> result = CommonSearchUtil.findDataBySearchQuery(user, ContractProgressConstants.RuleGoal.DESC_API_NAME, searchQuery);
        if(CollectionUtils.isEmpty(result)) {
            return ContractProgressModel.Result.builder().msg("ok").success(true).data(Lists.newArrayList()).build();
        } else {
            if(arg.isNeedCalculate()) {
                calculateCurrentGoal(user, arg.getContractId(), result, false);
            }
            if(arg.isIncludeCheck()) {
                fillCheckPointInfo(user, result);
            }
            fillGoalReferenceLabel(user, result, saleContractName);
            return ContractProgressModel.Result.builder().msg("ok").success(true).data(ObjectDataDocument.ofList(result)).build();
        }
    }

    private void deleteGoalAndCheckPoint(User user, String contractId) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnQuote(Boolean.TRUE);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, ContractProgressConstants.RuleGoal.CONTRACT_ID, contractId);
        searchQuery.setFilters(filters);
        List<IObjectData> result = CommonSearchUtil.findDataBySearchQuery(user, ContractProgressConstants.RuleGoal.DESC_API_NAME, searchQuery);
        if(CollectionUtils.isNotEmpty(result)) {
            List<String> goalIdList = result.stream().map(IObjectData::getId).collect(Collectors.toList());
            serviceFacade.bulkDeleteDirect(result, user);
            deleteData(user, ContractProgressConstants.RuleGoalCheck.DESC_API_NAME, ContractProgressConstants.RuleGoalCheck.RULE_GOAL_ID, goalIdList);
            deleteData(user, ContractProgressConstants.RuleGoalSnapshot.DESC_API_NAME, ContractProgressConstants.RuleGoalSnapshot.RULE_GOAL_ID, goalIdList);
        }
    }


    private void deleteData(User user, String objectApiName, String fieldApiName, List<String> fieldValues) {
        if(CollectionUtils.isEmpty(fieldValues)) {
            return;
        }
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnQuote(Boolean.TRUE);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, fieldApiName, fieldValues);
        searchQuery.setFilters(filters);
        List<IObjectData> result = CommonSearchUtil.findDataBySearchQuery(user, objectApiName, searchQuery);
        if(CollectionUtils.isNotEmpty(result)) {
            serviceFacade.bulkDeleteDirect(result, user);
        }
    }


    private void fillGoalReferenceLabel(User user, List<IObjectData> goalList, String saleContractName) {
        if(CollectionUtils.isEmpty(goalList)) {
            return;
        }
        Set<String> ruleIdSet = Sets.newHashSet();
        Set<String> productIdSet = Sets.newHashSet();
        for(IObjectData goal : goalList) {
            ruleIdSet.add(goal.get(ContractProgressConstants.RuleGoal.RULE_ID, String.class));
            productIdSet.add(goal.get(ContractProgressConstants.RuleGoal.PRODUCT_ID, String.class));
        }
        productIdSet.remove(null);//移除为null的元素
        //填充产品相关信息
        Map<String, IObjectData> productIdAndNameMap = Maps.newHashMap();
        List<IObjectData> productList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(productIdSet), Utils.PRODUCT_API_NAME);
        if(CollectionUtils.isNotEmpty(productList)) {
            productIdAndNameMap = productList.stream().collect(Collectors.toMap(IObjectData::getId, v->v, (oldV, newV)->newV));
        }

        List<IObjectData> ruleList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(ruleIdSet), ContractProgressConstants.Rule.DESC_API_NAME);
        if(CollectionUtils.isEmpty(ruleList)) {
            return;
        }
        //对规则进行填充标签信息
        wrapperRule(user, ruleList);
        Map<String,  IObjectData> ruleMap = ruleList.stream().collect(Collectors.toMap(IObjectData::getId, v->v, (oldV, newV)->newV));

        for(IObjectData goal : goalList) {
            IObjectData rule = ruleMap.getOrDefault(goal.get(ContractProgressConstants.RuleGoal.RULE_ID, String.class), new ObjectData());
            goal.set(concatUnderlineO(ContractProgressConstants.RuleGoal.RULE_ID), ObjectDataDocument.of(rule));
            goal.set(concatUnderlineR(ContractProgressConstants.RuleGoal.CONTRACT_ID), saleContractName);
            goal.set(concatUnderlineO(ContractProgressConstants.RuleGoal.PRODUCT_ID), productIdAndNameMap.get(goal.get(ContractProgressConstants.RuleGoal.PRODUCT_ID, String.class)));
        }
    }

    /**
     * 将规则中的对象及字段信息的Label填充到规则中
     * @param user
     * @param ruleList
     */
    private void wrapperRule(User user, List<IObjectData> ruleList) {
        if(CollectionUtils.isEmpty(ruleList)) {
            return;
        }
        Set<String> objectApiNameSet = Sets.newHashSet();
        for(IObjectData rule : ruleList) {
            objectApiNameSet.add(rule.get(ContractProgressConstants.Rule.INDEX_TYPE_OBJECT, String.class));
            objectApiNameSet.add(rule.get(ContractProgressConstants.Rule.INDEX_GOAL_OBJECT, String.class));
            objectApiNameSet.add(rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_OBJECT, String.class));
            objectApiNameSet.add(rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_DIRECT_OBJECT, String.class));
        }
        objectApiNameSet.add(ContractProgressConstants.Rule.DESC_API_NAME);
        objectApiNameSet.remove(null);//移除为null的元素
        Map<String, IObjectDescribe> objectDescribeMap = serviceFacade.findObjects(user.getTenantId(), objectApiNameSet);
        for(IObjectData rule : ruleList) {
            //处理index_type字段
            IObjectDescribe ruleDescribe = objectDescribeMap.get(ContractProgressConstants.Rule.DESC_API_NAME);
            if(ruleDescribe != null && ruleDescribe.getFieldDescribe(ContractProgressConstants.Rule.INDEX_TYPE) != null) {
                List<CopyOnWriteMap> options = ruleDescribe.getFieldDescribe(ContractProgressConstants.Rule.INDEX_TYPE).get("options", List.class, Lists.newArrayList());
                Optional<CopyOnWriteMap> option = options.stream().filter(v -> Objects.equals(rule.get(ContractProgressConstants.Rule.INDEX_TYPE), v.get("value"))).findFirst();
                if(option.isPresent()) {
                    //填充字段值的Label信息
                    rule.set(concatUnderlineR(ContractProgressConstants.Rule.INDEX_TYPE), option.get().get("label"));
                }
            }
            wrapperIndexObject(rule,
                    objectDescribeMap.get(rule.get(ContractProgressConstants.Rule.INDEX_TYPE_OBJECT, String.class)),
                            ContractProgressConstants.Rule.INDEX_TYPE_OBJECT,
                            ContractProgressConstants.Rule.INDEX_TYPE_OBJECT_FIELD,
                            ContractProgressConstants.Rule.INDEX_TYPE_OBJECT_FIELD_VALUE);
            wrapperIndexObject(rule,
                    objectDescribeMap.get(rule.get(ContractProgressConstants.Rule.INDEX_GOAL_OBJECT, String.class)),
                            ContractProgressConstants.Rule.INDEX_GOAL_OBJECT,
                            ContractProgressConstants.Rule.INDEX_GOAL_OBJECT_FIELD,
                            null);
            wrapperIndexObject(rule,
                    objectDescribeMap.get(rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_OBJECT, String.class)),
                            ContractProgressConstants.Rule.INDEX_GOAL_DATA_OBJECT,
                            ContractProgressConstants.Rule.INDEX_GOAL_DATA_OBJECT_FIELD,
                            null);
            wrapperIndexObject(rule,
                    objectDescribeMap.get(rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_DIRECT_OBJECT, String.class)),
                            ContractProgressConstants.Rule.INDEX_GOAL_DATA_DIRECT_OBJECT,
                            null,
                            null);
        }
    }

    private void wrapperIndexObject(IObjectData rule, IObjectDescribe describe, String objectField, String fieldField,  String valueField) {
        if(describe == null || rule == null) {
            return;
        }
        rule.set(concatUnderlineR(objectField), describe.getDisplayName());
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(rule.get(fieldField, String.class));
        if(fieldDescribe != null) {
            //填充字段信息
            rule.set(concatUnderlineR(fieldField), fieldDescribe.getLabel());
            String value = rule.get(valueField, String.class);
            if(StringUtils.isNotBlank(value)) {
                List<CopyOnWriteMap> options = fieldDescribe.get("options", List.class, Lists.newArrayList());
                Optional<CopyOnWriteMap> option = options.stream().filter(v -> Objects.equals(value, v.get("value"))).findFirst();
                if(option.isPresent()) {
                    //填充字段值的Label信息
                    rule.set(concatUnderlineR(valueField), option.get().get("label"));
                }
            }
        }
    }
    private String concatUnderlineO(String name) {
        return name+"__o";
    }

    private String concatUnderlineR(String name) {
        return name+"__r";
    }
    private void fillCheckPointInfo(User user, List<IObjectData> goalList) {
        if(CollectionUtils.isEmpty(goalList)) {
            return;
        }
        Map<String, IObjectData> map = goalList.stream().collect(Collectors.toMap(IObjectData::getId, v->v, (oldV, newV)->newV));
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnQuote(Boolean.TRUE);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, ContractProgressConstants.RuleGoalCheck.RULE_GOAL_ID, map.keySet());
        searchQuery.setFilters(filters);
        searchQuery.setOrders(Lists.newArrayList(new OrderBy(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME, Boolean.TRUE)));
        List<IObjectData> result = CommonSearchUtil.findDataBySearchQuery(user, ContractProgressConstants.RuleGoalCheck.DESC_API_NAME, searchQuery);
        if(CollectionUtils.isNotEmpty(result)) {
            Map<String, List<IObjectData>> checkMapByGoalId = result.stream().collect(Collectors.groupingBy(x->x.get(ContractProgressConstants.RuleGoalCheck.RULE_GOAL_ID, String.class)));
            map.keySet().stream().forEach(key->{
                IObjectData goal = map.get(key);
                List<IObjectData> checkPointList = checkMapByGoalId.get(key);
                if(CollectionUtils.isNotEmpty(checkPointList)) {
                    List<ObjectDataDocument> checkPointListResult = ObjectDataDocument.ofList(checkPointList);
                    goal.set("checkList", checkPointListResult);
                }
            });
        }
    }
    private void calculateCurrentGoal(User user, String contractId, List<IObjectData> result, boolean isFromCreateSnapshot) {
        if(CollectionUtils.isEmpty(result)) {
            return;
        }
        List<String> ruleIdList = result.stream().map(data -> data.get(ContractProgressConstants.RuleGoal.RULE_ID, String.class)).distinct().collect(Collectors.toList());
        //查询出规则
        List<IObjectData> ruleList = serviceFacade.findObjectDataByIds(user.getTenantId(), ruleIdList, ContractProgressConstants.Rule.DESC_API_NAME);
        //根据规则设置的取值条件，计算出当前规则的进度
        if(CollectionUtils.isEmpty(ruleList)) {
            return;
        }
        Map<String, IObjectData> ruleId2DataMap = ruleList.stream().collect(Collectors.toMap(IObjectData::getId, v->v, (k1, k2) -> k2));
        for (IObjectData ruleGoal : result) {
            Calendar c = Calendar.getInstance();
            c.setTime(new Date(ruleGoal.get(ContractProgressConstants.RuleGoal.TO_COMPLETE_TIME, Long.class)));
            c.set(Calendar.HOUR_OF_DAY, 23);
            c.set(Calendar.MINUTE, 59);
            c.set(Calendar.SECOND, 59);
            long completeTime = c.getTimeInMillis();

            IObjectData rule = ruleId2DataMap.get(ruleGoal.get(ContractProgressConstants.RuleGoal.RULE_ID, String.class));
            if(rule == null) {
                log.info("calculateCurrentGoal: rule not exist, goalId:{}, ruleId:{} ", ruleGoal.getId(), ruleGoal.get(ContractProgressConstants.RuleGoal.RULE_ID, String.class));
                continue;
            }

            SearchTemplateQuery aggQuery = new SearchTemplateQuery();
            String condition = rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_CONDITION, String.class);
            if(StringUtils.isNotBlank(condition)) {
                aggQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(condition);
            }

            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
            SearchUtil.fillFilterEq(filters, "life_status", ObjectLifeStatus.NORMAL.getCode());
            String timeField = rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_REF_TIME_FIELD, String.class);
            SearchUtil.fillFilterLT(filters, timeField, completeTime);

            String indexTypeObjectField = rule.get(ContractProgressConstants.Rule.INDEX_TYPE_OBJECT_FIELD, String.class);
            String indexTypeObjectFieldValue = rule.get(ContractProgressConstants.Rule.INDEX_TYPE_OBJECT_FIELD_VALUE, String.class);

            String indexGoalDataObject = rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_OBJECT, String.class);
            String indexGoalDataDirectObject = rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_DIRECT_OBJECT, String.class); //聚合查询时直接对象
            String indexGoalDataObjectField = rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_OBJECT_FIELD, String.class); //求聚合值的字段
            String contractField = rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_REF_CONTRACT_FIELD, String.class);
            String indexGoalDataRefProductField = rule.get(ContractProgressConstants.Rule.INDEX_GOAL_DATA_REF_PRODUCT_FIELD, String.class);

            String indexType = rule.get(ContractProgressConstants.Rule.INDEX_TYPE, String.class);
            if(ContractProgressConstants.Rule.IndexTypeEnum.ContractProduct.getValue().equals(indexType)) {//合同产品， 将产品作为其中的有一个过滤条件
                SearchUtil.fillFilterEq(filters, indexGoalDataRefProductField, ruleGoal.get(ContractProgressConstants.RuleGoal.PRODUCT_ID, String.class));//只查指定产品的数据
                //产品+关联当前合同的主对象 或 产品+关联当前合同的主对象下的明细对象数据
                boolean needContinue = fillMasterDataIds(user, contractId, indexGoalDataObject, indexGoalDataDirectObject, contractField, filters);
                if(!needContinue) {
                    continue;
                }
            } else if(ContractProgressConstants.Rule.IndexTypeEnum.Product.getValue().equals(indexType)) {//产品上的单选字段值作为一个过滤条件
                //产品+关联当前合同的主对象 或 产品+关联当前合同的主对象下的明细对象数据
                IFilter iFilter = SearchUtil.filter(indexGoalDataRefProductField+"."+indexTypeObjectField, Operator.EQ, indexTypeObjectFieldValue); //构造产品字段的查询条件
                iFilter.setIsMasterField(true);
                filters.add(iFilter);
                boolean needContinue = fillMasterDataIds(user, contractId, indexGoalDataObject, indexGoalDataDirectObject, contractField, filters);
                if(!needContinue) {
                    continue;
                }
            } else if(ContractProgressConstants.Rule.IndexTypeEnum.Contract.getValue().equals(indexType)) {//合同数值，直接拿合同ID作为一个过滤条件
                //设置合同ID
                SearchUtil.fillFilterEq(filters, contractField, contractId);
            } else {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTRACT_PROGRESS_RULE_GOAL_INDEX_TYPE_OBJECT_INVALID, indexType));
            }


            aggQuery.setFilters(filters);
            aggQuery.setPermissionType(0);

            String whereInfo = aggQuery.toJsonString();
            ruleGoal.set("where_condition",  whereInfo);

            //实时计算时，如果达成时间已经过了，则不计算进度
            if(!isFromCreateSnapshot && completeTime < System.currentTimeMillis()) {
                continue;
            }

            AggFunctionArg aggFunctionArg = new AggFunctionArg();
            aggFunctionArg.setAggField(indexGoalDataObjectField);
            aggFunctionArg.setAggFunction(Count.TYPE_SUM);

            //设置分组参数
            IGroupByParameter groupByParameter = new GroupByParameter();
            groupByParameter.setAggFunctions(Collections.singletonList(aggFunctionArg));

            aggQuery.setGroupByParameter(groupByParameter);
            log.info("calculateCurrentGoal:query condition:"+aggQuery.toJsonString());
            List<IObjectData> sumResult = serviceFacade.aggregateFindBySearchQuery(User.systemUser(user.getTenantId()), aggQuery, indexGoalDataDirectObject);
            if(CollectionUtils.isNotEmpty(sumResult)) {
                IObjectData sumData = sumResult.get(0);
                BigDecimal currentGoal = sumData.get("sum_" + indexGoalDataObjectField, BigDecimal.class, BigDecimal.ZERO);
                ruleGoal.set(ContractProgressConstants.RuleGoal.CURRENT_VALUE, currentGoal);
            }
        }
    }

    private boolean fillMasterDataIds(User user, String contractId, String indexGoalDataObject, String indexGoalDataDirectObject, String contractField, List<IFilter> filters) {
        if(!Objects.equals(indexGoalDataObject, indexGoalDataDirectObject)) {//属于主从关系，需要根据合同ID查找出主对象的数据，然后将主对象的ID再加入到从对象的过滤条件中
            IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), indexGoalDataDirectObject);
            if(describe == null) {
                log.info("calculateCurrentGoal: object describe not exist, object apiName:{}", indexGoalDataDirectObject);
                return false;
            }
            Optional<IFieldDescribe> masterDetailField = describe.getFieldDescribes().stream().filter(fieldDescribe -> "master_detail".equals(fieldDescribe.getType())).findFirst();
            if(masterDetailField.isPresent()) {
                List<String> masterDataIds = getMasterDataIdsByContractId(user, contractField, contractId, indexGoalDataObject);
                if(CollectionUtils.isEmpty(masterDataIds)) {
                    log.info("calculateCurrentGoal: masterDataIds is empty, object apiName:{}", indexGoalDataObject);
                    return false;
                }
                SearchUtil.fillFilterIn(filters, masterDetailField.get().getApiName(), masterDataIds);
            } else {
                log.info("calculateCurrentGoal: masterDetailField not exist, object apiName:{}", indexGoalDataDirectObject);
                return false;
            }
        } else {
            //设置合同ID
            SearchUtil.fillFilterEq(filters, contractField, contractId);
        }
        return true;
    }
    private List<String> getMasterDataIdsByContractId(User user, String contractField, String contractId, String describeApiName) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnQuote(Boolean.FALSE);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, contractField, contractId);
        searchQuery.setFilters(filters);
        List<IObjectData> result = CommonSearchUtil.findDataBySearchQuery(user, describeApiName, searchQuery);
        if (CollectionUtils.isEmpty(result)) {
            return Lists.newArrayList();
        } else {
           return result.stream().map(IObjectData::getId).collect(Collectors.toList());
        }
    }

    private void fillCommonField(IObjectData data, ContractProgressModel.RuleModel rule) {
        data.setDescribeApiName(ContractProgressConstants.Rule.DESC_API_NAME);
        data.set(ContractProgressConstants.Rule.NAME, rule.getName());
        data.set(ContractProgressConstants.Rule.DESCRIPTION, rule.getDescription());
        data.set(ContractProgressConstants.Rule.ENABLED_STATUS, rule.isEnabledStatus());
        data.set(ContractProgressConstants.Rule.CONTRACT_RECORD_TYPE, rule.getContractRecordType());
        data.set(ContractProgressConstants.Rule.INDEX_TYPE, rule.getIndexType());
        data.set(ContractProgressConstants.Rule.INDEX_TYPE_OBJECT, rule.getIndexTypeObject());
        data.set(ContractProgressConstants.Rule.INDEX_TYPE_OBJECT_FIELD, rule.getIndexTypeObjectField());
        data.set(ContractProgressConstants.Rule.INDEX_TYPE_OBJECT_FIELD_VALUE, rule.getIndexTypeObjectFieldValue());
        data.set(ContractProgressConstants.Rule.INDEX_TYPE_CATEGORY, "numberType"); //固定值
        data.set(ContractProgressConstants.Rule.INDEX_TYPE_CALC_TYPE, "sum"); //固定值
        data.set(ContractProgressConstants.Rule.INDEX_GOAL_OBJECT, rule.getIndexGoalObject());
        data.set(ContractProgressConstants.Rule.INDEX_GOAL_OBJECT_FIELD, rule.getIndexGoalObjectField());
        data.set(ContractProgressConstants.Rule.INDEX_GOAL_DATA_OBJECT, rule.getIndexGoalDataObject());
        data.set(ContractProgressConstants.Rule.INDEX_GOAL_DATA_DIRECT_OBJECT, rule.getIndexGoalDataDirectObject());
        data.set(ContractProgressConstants.Rule.INDEX_GOAL_DATA_OBJECT_FIELD, rule.getIndexGoalDataObjectField());
        data.set(ContractProgressConstants.Rule.INDEX_GOAL_DATA_REF_CONTRACT_FIELD, rule.getIndexGoalDataRefContractField());
        data.set(ContractProgressConstants.Rule.INDEX_GOAL_DATA_REF_PRODUCT_FIELD, rule.getIndexGoalDataRefProductField());
        data.set(ContractProgressConstants.Rule.INDEX_GOAL_DATA_REF_TIME_FIELD, rule.getIndexGoalDataRefTimeField());
        data.set(ContractProgressConstants.Rule.INDEX_GOAL_DATA_CONDITION, rule.getIndexGoalDataCondition());
    }

    private Map<String, List<IObjectData>> buildAllGoalAndCheckPointMap(User user, ContractProgressModel.RuleGoalModel arg) {
        Map<String, Map<String, IObjectData>> oldGoalMap = findDbGoalAndCheckPointMap(user, arg.getContractId());
        Map<String, List<IObjectData>> goalAndCheckPointMap = Maps.newHashMap();

        Set<String> goalIdSet = Sets.newHashSet();
        Set<String>  checkPointIdSet = Sets.newHashSet();
        List<IObjectData> addGoalList = Lists.newArrayList();
        List<IObjectData> updateGoalList = Lists.newArrayList();
        arg.getProgressGoalList().stream().forEach(progressGoalArg -> {
            IObjectData goal = oldGoalMap.getOrDefault(GOAL_KEY, Maps.newHashMap()).getOrDefault(progressGoalArg.getId(), new ObjectData());
            boolean isCreate = false;
            boolean needUpdate = false;
            String goalId = goal.getId();
            if(StringUtils.isBlank(goalId)) {
                isCreate = true;
                goalId = IdGenerator.get();
                goal.setCreateTime(System.currentTimeMillis());
                goal.setDescribeApiName(ContractProgressConstants.RuleGoal.DESC_API_NAME);
                goal.set(ContractProgressConstants.RuleGoal.CONTRACT_ID, arg.getContractId());
                goal.set(ContractProgressConstants.RuleGoal.RULE_ID, progressGoalArg.getRuleId());
                goal.set(ContractProgressConstants.RuleGoal.TO_COMPLETE_TIME, arg.getToCompleteTime());
                goal.set(ContractProgressConstants.RuleGoal.GOAL_VALUE, progressGoalArg.getGoalValue());
                goal.set(ContractProgressConstants.RuleGoal.PRODUCT_ID, progressGoalArg.getProductId());
                goal.setTenantId(user.getTenantId());
                goal.setId(goalId);
            } else {
                needUpdate = compareGoalAndMerge(goal, progressGoalArg, arg.getToCompleteTime());
            }
            if(isCreate) {
                addGoalList.add(goal);
            }
            if(needUpdate) {
                updateGoalList.add(goal);
            }
            goalIdSet.add(goalId);

            buildCheckPoint(user, checkPointIdSet, goalAndCheckPointMap, progressGoalArg.getCheckList(), goal, oldGoalMap.getOrDefault(GOAL_CHECK_KEY, Maps.newHashMap()), isCreate);

        });
        goalAndCheckPointMap.computeIfAbsent(GOAL_KEY_ADD, k -> Lists.newArrayList()).addAll(addGoalList);
        goalAndCheckPointMap.computeIfAbsent(GOAL_KEY_UPDATE, k -> Lists.newArrayList()).addAll(updateGoalList);
        //处理删除的数据
        Map<String, IObjectData> dbGoalMap = oldGoalMap.getOrDefault(GOAL_KEY, Maps.newHashMap());
        Map<String, IObjectData> dbCheckPointMap = oldGoalMap.getOrDefault(GOAL_CHECK_KEY, Maps.newHashMap());

        Set<String> dbGoalIdSet = dbGoalMap.keySet();
        Set<String> dbCheckPointIdSet = dbCheckPointMap.keySet();
        //过滤出待删除的数据
        dbGoalIdSet.stream().filter(id -> !goalIdSet.contains(id)).forEach(id -> goalAndCheckPointMap.computeIfAbsent(GOAL_KEY_DELETE, k -> Lists.newArrayList()).add(dbGoalMap.get(id)));
        //目标达成的检查点，不删除，需要排查掉
        dbCheckPointIdSet.stream().filter(id -> !checkPointIdSet.contains(id) && !dbGoalIdSet.contains(id)).forEach(id -> goalAndCheckPointMap.computeIfAbsent(GOAL_CHECK_KEY_DELETE, k -> Lists.newArrayList()).add(dbCheckPointMap.get(id)));

        return goalAndCheckPointMap;
    }

    private void buildCheckPoint(User user, Set<String> checkPointIdSet, Map<String, List<IObjectData>> goalAndCheckPointMap, List<ContractProgressModel.RuleGoalCheckArg> checkList, IObjectData goal, Map<String, IObjectData> oldCheckPointList, boolean isCreate) {
        //如果当前规则没有设置检查点，需要添加目标达成的时间作为一个检查点，不能直接返回
        if(checkList == null) {
            checkList = Lists.newArrayList();//确保下面处理逻辑一致，且不出现空指针
        }
        List<IObjectData> addCheckPointList = Lists.newArrayList();
        List<IObjectData> updateCheckPointList = Lists.newArrayList();
        if(isCreate || MapUtils.isEmpty(oldCheckPointList)) {
            checkList.stream().forEach(checkArg -> {
                IObjectData checkPoint = createOneCheckPoint(user, goal.getId(), checkArg);
                addCheckPointList.add(checkPoint);
                checkPointIdSet.add(checkPoint.getId());
            });
            //根节点（每个目标完成时的检查点）也创建一下，用于标记目标完成时是否需要检查
            addCheckPointList.add(buildOneCheckPointForGoal(goal, user));
            //目标的检查点ID ，设置为与目标ID一致
            checkPointIdSet.add(goal.getId());
        } else {
            //处理前端传过来的检查点数据
            checkList.stream().forEach(checkArg -> {
                IObjectData checkPoint = oldCheckPointList.getOrDefault(checkArg.getId(), new ObjectData());
                boolean create = false;
                boolean needUpdate = false;
                if(StringUtils.isBlank(checkPoint.getId())) {
                    create = true;
                    checkPoint = createOneCheckPoint(user, goal.getId(), checkArg);
                } else {
                    needUpdate = compareCheckPointAndMerge(checkPoint, checkArg);
                }
                if(create) {
                    addCheckPointList.add(checkPoint);
                }
                if (needUpdate) {
                    updateCheckPointList.add(checkPoint);
                }
                checkPointIdSet.add(checkPoint.getId());
            });
            //处理目标的达成时间，作为一个检查点，达成时间的检查点，如果不存在，则将目标的id作为检查点的ID
            IObjectData checkPointForGoal = oldCheckPointList.get(goal.getId());
            boolean isRootCreate = false;
            if(checkPointForGoal == null) {
                checkPointForGoal = createOneCheckPoint(user, goal.getId(), checkList.get(0));
                checkPointForGoal.setId(goal.getId());
                isRootCreate = true;
            }
            checkPointIdSet.add(checkPointForGoal.getId());
            //任务的完成时间发生改变，则需要更新对应的节点
            if(!Objects.equals(goal.get(ContractProgressConstants.RuleGoal.TO_COMPLETE_TIME, Long.class), checkPointForGoal.get(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME, Long.class))) {
                checkPointForGoal.set(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME, goal.get(ContractProgressConstants.RuleGoal.TO_COMPLETE_TIME));
                if(isRootCreate) {
                    addCheckPointList.add(checkPointForGoal);
                } else {
                    updateCheckPointList.add(checkPointForGoal);
                }
            }
        }
        goalAndCheckPointMap.computeIfAbsent(GOAL_CHECK_KEY_ADD, k -> Lists.newArrayList()).addAll(addCheckPointList);
        goalAndCheckPointMap.computeIfAbsent(GOAL_CHECK_KEY_UPDATE, k -> Lists.newArrayList()).addAll(updateCheckPointList);
    }
    private boolean compareGoalAndMerge(IObjectData goal, ContractProgressModel.ProgressGoalArg progressGoalArg, long toCompleteTime) {
        //goal 的比较原则  rule_id+product_id+goal_value+to_complete_time
        boolean needUpdate =  !Objects.equals(goal.get(ContractProgressConstants.RuleGoal.RULE_ID), progressGoalArg.getRuleId())
                || !Objects.equals(goal.get(ContractProgressConstants.RuleGoal.PRODUCT_ID), progressGoalArg.getProductId())
                || !Objects.equals(goal.get(ContractProgressConstants.RuleGoal.GOAL_VALUE), progressGoalArg.getGoalValue())
                || !Objects.equals(goal.get(ContractProgressConstants.RuleGoal.TO_COMPLETE_TIME), toCompleteTime);
        if(needUpdate) {
            goal.set(ContractProgressConstants.RuleGoal.RULE_ID, progressGoalArg.getRuleId());
            goal.set(ContractProgressConstants.RuleGoal.PRODUCT_ID, progressGoalArg.getProductId());
            goal.set(ContractProgressConstants.RuleGoal.GOAL_VALUE, progressGoalArg.getGoalValue());
            goal.set(ContractProgressConstants.RuleGoal.TO_COMPLETE_TIME, toCompleteTime);
        }
        return needUpdate;
    }

    private boolean compareCheckPointAndMerge(IObjectData checkPoint, ContractProgressModel.RuleGoalCheckArg ruleGoalCheckArg) {
        //checkpooint 的比较原则  rule_goal_id+goal_value+to_check_time
        boolean needUpdate = !Objects.equals(checkPoint.get(ContractProgressConstants.RuleGoalCheck.RULE_GOAL_ID, String.class), ruleGoalCheckArg.getRuleGoldId())
                || !Objects.equals(checkPoint.get(ContractProgressConstants.RuleGoalCheck.GOAL_VALUE, BigDecimal.class), ruleGoalCheckArg.getGoalValue())
                || !Objects.equals(checkPoint.get(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME), ruleGoalCheckArg.getToCheckTime());
        if(needUpdate) {
            checkPoint.set(ContractProgressConstants.RuleGoalCheck.GOAL_VALUE, ruleGoalCheckArg.getGoalValue());
            checkPoint.set(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME, ruleGoalCheckArg.getToCheckTime());
        }
        return needUpdate;
    }

    private IObjectData createOneCheckPoint(User user, String goalId, ContractProgressModel.RuleGoalCheckArg checkArg) {
        IObjectData checkPoint = new ObjectData();
        checkPoint.setTenantId(user.getTenantId());
        checkPoint.setCreateTime(System.currentTimeMillis());
        checkPoint.setDescribeApiName(ContractProgressConstants.RuleGoalCheck.DESC_API_NAME);
        checkPoint.set(ContractProgressConstants.RuleGoalCheck.RULE_GOAL_ID, goalId);
        checkPoint.set(ContractProgressConstants.RuleGoalCheck.is_checked, false);
        checkPoint.set(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME, checkArg.getToCheckTime());
        checkPoint.set(ContractProgressConstants.RuleGoalCheck.GOAL_VALUE, checkArg.getGoalValue());
        checkPoint.setId(IdGenerator.get());
        return checkPoint;
    }
    /**
     * @param user
     * @param contractId
     * @return
     */
    private Map<String, Map<String, IObjectData>>  findDbGoalAndCheckPointMap(User user, String contractId) {
        Map<String, Map<String, IObjectData>> goalAndCheckPointMap = Maps.newHashMap();
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, ContractProgressConstants.RuleGoal.CONTRACT_ID, contractId);
        searchTemplateQuery.setFilters(filters);

        QueryResult<IObjectData> goalList = serviceFacade.findBySearchQuery(user, ContractProgressConstants.RuleGoal.DESC_API_NAME, searchTemplateQuery);
        if(goalList != null && CollectionUtils.isNotEmpty(goalList.getData() )) {
            goalAndCheckPointMap.put(GOAL_KEY, goalList.getData().stream().collect(Collectors.toMap(IObjectData::getId, v->v, (k1, k2)->k2)));
            SearchTemplateQuery checkPointQuery = new SearchTemplateQuery();
            checkPointQuery.setNeedReturnCountNum(false);
            checkPointQuery.setLimit(2000);
            checkPointQuery.setOffset(0);
            checkPointQuery.setPermissionType(0);
            List<IFilter> checkPointQueryFilters = Lists.newArrayList();
            checkPointQueryFilters.add(SearchUtil.filter(ContractProgressConstants.RuleGoalCheck.RULE_GOAL_ID, Operator.IN, goalList.getData().stream().map(IObjectData::getId).collect(Collectors.toList())));
            checkPointQuery.setFilters(checkPointQueryFilters);
            QueryResult<IObjectData> checkList = serviceFacade.findBySearchQuery(user, ContractProgressConstants.RuleGoalCheck.DESC_API_NAME, checkPointQuery);
            if(checkList != null && CollectionUtils.isNotEmpty(checkList.getData())) {
                goalAndCheckPointMap.put(GOAL_CHECK_KEY, checkList.getData().stream().collect(Collectors.toMap(IObjectData::getId, v->v, (k1, k2)->k2)));
            }
        }
        return goalAndCheckPointMap;
    }

    /**
     * 构建根节点（每个目标完成时的检查点）的checkPoint数据
     * @param goal
     */
    private IObjectData buildOneCheckPointForGoal(IObjectData goal, User user) {
        IObjectData checkPoint = new ObjectData();
        checkPoint.setTenantId(user.getTenantId());
        checkPoint.setCreateTime(System.currentTimeMillis());
        checkPoint.setDescribeApiName(ContractProgressConstants.RuleGoalCheck.DESC_API_NAME);
        checkPoint.set(ContractProgressConstants.RuleGoalCheck.RULE_GOAL_ID, goal.getId());
        checkPoint.set(ContractProgressConstants.RuleGoalCheck.is_checked, false);
        checkPoint.set(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME, goal.get(ContractProgressConstants.RuleGoal.TO_COMPLETE_TIME));
        checkPoint.set(ContractProgressConstants.RuleGoalCheck.GOAL_VALUE, goal.get(ContractProgressConstants.RuleGoal.GOAL_VALUE));
        checkPoint.setId(goal.getId());
        return checkPoint;
    }

    @Transactional
    public void createSnapshot(User user, ContractProgressModel.SnapshotArg snapshotArg) {
        if(StringUtils.isAnyBlank(snapshotArg.getContractId(), snapshotArg.getRuleGoalId(), snapshotArg.getCheckPointId(), snapshotArg.getTenantId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
        }
        List<IObjectData> goalList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(snapshotArg.getRuleGoalId()), ContractProgressConstants.RuleGoal.DESC_API_NAME);
        if(CollectionUtils.isEmpty(goalList)) {
            return;
        }
        BigDecimal goalValue = goalList.get(0).get(ContractProgressConstants.RuleGoal.GOAL_VALUE, BigDecimal.class, BigDecimal.ZERO);
        calculateCurrentGoal(user, snapshotArg.getContractId(), goalList, true);
        BigDecimal currentValue = goalList.get(0).get(ContractProgressConstants.RuleGoal.CURRENT_VALUE, BigDecimal.class, BigDecimal.ZERO);

        serviceFacade.batchUpdateByFields(user, goalList, Lists.newArrayList(ContractProgressConstants.RuleGoal.CURRENT_VALUE));
        List<IObjectData> checkPointList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(snapshotArg.getCheckPointId()), ContractProgressConstants.RuleGoalCheck.DESC_API_NAME);
        if(CollectionUtils.isNotEmpty(checkPointList)) {
            checkPointList.stream().forEach(checkPoint -> {
                checkPoint.set(ContractProgressConstants.RuleGoalCheck.is_checked, true);
            });
            serviceFacade.batchUpdateByFields(user, checkPointList, Lists.newArrayList(ContractProgressConstants.RuleGoalCheck.is_checked));
        }
        IObjectData snapshot = new ObjectData();
        snapshot.setId(IdGenerator.get());
        snapshot.setTenantId(user.getTenantId());
        snapshot.setCreateTime(System.currentTimeMillis());
        snapshot.setDescribeApiName(ContractProgressConstants.RuleGoalSnapshot.DESC_API_NAME);
        snapshot.set(ContractProgressConstants.RuleGoalSnapshot.RULE_GOAL_ID, snapshotArg.getRuleGoalId());
        snapshot.set(ContractProgressConstants.RuleGoalSnapshot.GOAL_VALUE, goalValue);
        snapshot.set(ContractProgressConstants.RuleGoalSnapshot.CURRENT_VALUE, currentValue);
        String checkType = Objects.equals(snapshotArg.getRuleGoalId(), snapshotArg.getCheckPointId()) ?  ContractProgressConstants.SnapshotCheckType.FINALITY.getCode() : ContractProgressConstants.SnapshotCheckType.HALFWAY.getCode();
        snapshot.set(ContractProgressConstants.RuleGoalSnapshot.CHECK_TYPE, checkType);
        serviceFacade.saveObjectData(user, snapshot);
    }

    /**
     * 批量作废合同，需要联动作废合同目标
     * @param user
     * @param dataList
     */
    public void bulkInvalidRuleGoalByContract(User user, List<IObjectData> dataList) {
        if(CollectionUtils.isEmpty(dataList) || !bizConfigThreadLocalCacheService.isOpenContractProgress(user.getTenantId())) {
            return;
        }
        List<String> contractIds = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnQuote(Boolean.FALSE);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, ContractProgressConstants.RuleGoal.CONTRACT_ID, contractIds);
        searchQuery.setFilters(filters);
        List<IObjectData> result = CommonSearchUtil.findDataBySearchQuery(user, ContractProgressConstants.RuleGoal.DESC_API_NAME, searchQuery);
        if(CollectionUtils.isEmpty(result)) {
            return;
        }
        serviceFacade.bulkInvalid(result, user);
    }

    /**
     * 批量作废合同，需要联动作废合同目标
     * @param user
     * @param dataList
     */
    public void bulkRecoverRuleGoalByContract(User user, List<IObjectData> dataList) {
        if(CollectionUtils.isEmpty(dataList) || !bizConfigThreadLocalCacheService.isOpenContractProgress(user.getTenantId())) {
            return;
        }
        List<String> contractIds = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setNeedReturnCountNum(false);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.setPermissionType(0);
        searchQuery.setNeedReturnQuote(Boolean.FALSE);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, ContractProgressConstants.RuleGoal.CONTRACT_ID, contractIds);
        searchQuery.setFilters(filters);
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), ContractProgressConstants.RuleGoal.DESC_API_NAME);
        if(describe == null) {
            return;
        }
        List<IObjectData> result = CommonSearchUtil.findBySearchQueryWithDeleted(user, describe, searchQuery);
        if(CollectionUtils.isEmpty(result)) {
            return;
        }
        serviceFacade.bulkRecover(result, user);
    }
}
