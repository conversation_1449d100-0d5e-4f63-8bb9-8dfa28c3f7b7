package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.elasticsearch.common.Strings;

import java.util.*;

/**
 * Created by liux on 2020/10/15.
 */
public class AttributeUtils {
    private static final String VALUE = "value";
    private static final Integer LIMIT = 30;
    private static final String ATTRIBUTE_STR = "attribute";
    protected static Map<String,Integer> attributeLimitMap = Maps.newHashMap();
    protected static List<String> attributeLimitGrayConfig;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config", config -> {
            if (Strings.isNullOrEmpty(config.get("attribute_limit"))) {
                attributeLimitGrayConfig = Lists.newArrayList();
            } else {
                attributeLimitGrayConfig = Lists.newArrayList(config.get("attribute_limit").split(","));
            }
            if (CollectionUtils.notEmpty(attributeLimitGrayConfig)) {
                Map<String, Integer> attributeLimitMapTemp = new HashMap<>();
                for (String item : attributeLimitGrayConfig) {
                    String[] tenantIdLimitPair=item.split(":");
                    if (tenantIdLimitPair.length == 2) {
                        String tenantId = tenantIdLimitPair[0];
                        Integer limit = Integer.valueOf(tenantIdLimitPair[1]);
                        attributeLimitMapTemp.put(tenantId, limit);
                    }
                }
                //有数据再替换全局配置，防止覆盖全局配置。
                if(attributeLimitMapTemp != null && attributeLimitMapTemp.size() > 0) {
                    attributeLimitMap = attributeLimitMapTemp;
                }
            }
        });

    }
    public static Integer getAttributeLimit(String tenantId) {
        if (CollectionUtils.notEmpty(attributeLimitMap)) {
            if (attributeLimitMap.containsKey(tenantId)) {
                return attributeLimitMap.get(tenantId);
            }
        }
        return LIMIT;
    }
    public static Set<String> getAttributeFieldList(String tenantId) {
        Integer limit = getAttributeLimit(tenantId);
        Set<String> result = new HashSet<>();
        for (int i = 1; i <= limit; i++) {
            result.add(ATTRIBUTE_STR + i);
        }
        return result;
    }


    public static List<Map<String, Object>> setAttr(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return Lists.newArrayList();
        }
        List<Map<String, Object>> dataMapList = Lists.newArrayList();
        objectDataList.forEach(attr -> {
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("id", attr.getId());
            dataMap.put("name", AttributeUtils.getI18nName(attr));
            dataMap.put("type",attr.get("type"));
            dataMap.put("default_value", attr.get("default_value"));
            dataMap.put(AttributeConstants.GroupField.GROUP_ID,attr.get(AttributeConstants.GroupField.GROUP_ID));
            dataMap.put(AttributeConstants.GroupField.GROUP_NAME,attr.get(AttributeConstants.GroupField.GROUP_NAME));
            dataMap.put(AttributeConstants.GroupField.GROUP_NO,attr.get(AttributeConstants.GroupField.GROUP_NO));
            if (Objects.nonNull(attr.get(VALUE))) {
                dataMap.put(VALUE, attr.get(VALUE, List.class));
            }
            dataMapList.add(dataMap);
        });
        return dataMapList;
    }

    public static boolean checkNumber(String str){
        return NumberUtils.isCreatable(str);
    }

    public static String getI18nName(IObjectData objectData){
        return StringUtils.isNotEmpty(objectData.get("name__r", String.class)) ? objectData.get("name__r", String.class) : objectData.getName();
    }

    public static String getI18nCode(IObjectData objectData){
        return StringUtils.isNotEmpty(objectData.get("code__r", String.class)) ? objectData.get("code__r", String.class) :objectData.get("code", String.class);
    }
}
