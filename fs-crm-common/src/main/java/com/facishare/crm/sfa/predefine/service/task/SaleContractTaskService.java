package com.facishare.crm.sfa.predefine.service.task;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.utilities.constant.ContractProgressConstants;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.List;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2025-04-27
 */
@Service
@Slf4j
public class SaleContractTaskService {
    private static final String BIZ = "contract_goal_snapshot_create";

    @Autowired
    TaskService taskService;

    public void createOrUpdateTask(String tenantId, List<IObjectData> goalCheckList, String contractId) {
        if(CollectionUtils.isEmpty(goalCheckList)) {
            return;
        }
        goalCheckList.forEach(data -> {
            JSONObject check = new JSONObject();
            check.put(IObjectData.TENANT_ID, tenantId);
            check.put("check_point_id", data.getId());
            check.put(ContractProgressConstants.RuleGoal.CONTRACT_ID, contractId);
            check.put(ContractProgressConstants.RuleGoalCheck.RULE_GOAL_ID, data.get(ContractProgressConstants.RuleGoalCheck.RULE_GOAL_ID, String.class));
            check.put(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME, data.get(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME, Long.class));
            Calendar calendar = Calendar.getInstance();
            //隔天凌晨1点执行
            calendar.setTimeInMillis(data.get(ContractProgressConstants.RuleGoalCheck.TO_CHECK_TIME, Long.class));
            calendar.set(Calendar.HOUR_OF_DAY, 1);// 把时间设置在1点
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH)+1);//第二天凌晨1点执行
            taskService.createOrUpdateTask(BIZ, tenantId, data.getId(), calendar.getTime(), JSONObject.toJSONString(check));
        });
    }

    public void deleteTask(String tenantId, IObjectData data) {
        if(data != null) {
            taskService.deleteTask(BIZ, tenantId, data.getId());
        }
    }

    public void deleteTask(String tenantId, List<IObjectData> dataList) {
        if(CollectionUtils.isEmpty(dataList)) {
            return;
        }
        dataList.stream().forEach(data -> deleteTask(tenantId, data));
    }
}
