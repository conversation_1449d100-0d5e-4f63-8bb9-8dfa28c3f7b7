package com.facishare.crm.sfa.predefine.service.attribute;

import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeValue;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 属性核心接口类
 *
 * <AUTHOR>
 */
public interface AttributeCoreService {

    /**
     * 根据属性Id列表获取属性
     *
     * @param user
     * @param ids
     * @return
     */
    List<Attribute> getAttributeByIds(User user, Set<String> ids);
    /**
     * 根据属性Id列表获取属性和分组Id
     *
     * @param user
     * @param ids
     * @return
     */
    List<Attribute> getAttributeByIds(User user, Set<String> ids, Set<String> groupIds);

    /**
     * 获取所有启用的属性
     *
     * @param user
     * @return
     */
    List<Attribute> getAttributesByStatus(User user, String status,boolean needInvalid);

    /**
     * 根据属性Id列表获取属性值
     *
     * @param user
     * @param attributeIds
     * @return
     */
    List<AttributeValue> getAttributeValueByAttributeIds(User user, Set<String> attributeIds);

    /**
     * 根据产品id获取其关联的属性和属性值
     *
     * @param user
     * @param productIds
     * @return
     */
    Map<String, List<Attribute>> getAttributeByProductIds(User user, List<String> productIds);

    /**
     * 根据产品id获取其关联的属性和属性值
     *
     * @param user
     * @param productIds
     * @param isPricing 是否参与定价
     * @return
     */
    Map<String, List<Attribute>> getAttributeByProductIds(User user, List<String> productIds, Boolean isPricing);
    /**
     * 根据产品id获取其关联的属性和属性值
     *
     * @param user
     * @param productIds
     * @param isPricing 是否参与定价
     * @return
     */
    Map<String, List<Attribute>> getAttributeByProductIds(User user, List<String> productIds, Boolean isPricing,List<IObjectData> productList);

    /**
     * 查询分组
     * @param user
     * @param groupIds
     * @return
     */
    Map<String, IObjectData> queryAttrGroupMap(User user, Set<String> groupIds);
    /**
     * 附加属性数据
     * @param user
     * @param objectDataList
     * @param productFieldName
     */
    void attachAttributeData(User user, List<IObjectData> objectDataList, String productFieldName);

    /**
     * 填充报价单明细、订单产品属性文本
     * @param user
     * @param objectDataList
     */
    void fillAttributeText(User user, List<IObjectData> objectDataList);

    /**
     * 根据产品分类ID批量解除产品分类和属性关联关系
     */
    void batchDeleteProductCategoryAttributeRelationByCategory(String tenantId, String categoryId, List<String> attributeIds);

    /**
     * 批量插入产品分类和属性关联关系
     */
    void batchSaveProductCategoryAttribute(String tenantId, String categoryId, List<String> attributeIds, List<AttributeProductCategoryModel.Attribute> attributeList);

    /**
     * 批量插入产品分类和属性用户关联关系
     */
    void batchSaveAttributeUserRelation(User user, String categoryId, List<String> attributeIds, Boolean isRecover, List<AttributeProductCategoryModel.Attribute> attributeList);
    /**
     * 根据分类id获取分类关联信息
     */
    List<IObjectData> getCategoryInfoByCategoryId(String tenantId, String categoryId);
    /**
     * 根据分类id获取分类关联信息
     */
    List<IObjectData> getAttributeUserRelationByCategoryIdAndUser(String tenantId, String categoryId, String userId);
    /**
     * 根据已有槽位取下一个可用槽位
     */
    int findFieldNum(List<Integer> fieldNumList);

    /**
     * 替换属性文本多语
     */
    void fillAttributeAndNonAttributeText(List<IObjectData> objectDataList, String tenantId);

    Map<String, List<String>> fillAttributeNum(User user, Map<String, List<String>> map);

    /**
     * key为AttributeObj ID ,value 为AttributeValueObj ID
     * @param user
     * @param attributeJsonMap
     * @return
     */
    List<ObjectDataDocument> findAttributeJson(User user, Map<String, String> attributeJsonMap);
}
