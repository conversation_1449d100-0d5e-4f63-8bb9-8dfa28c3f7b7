package com.facishare.crm.sfa.utilities.constant;

import org.elasticsearch.common.util.set.Sets;

import java.util.Collections;
import java.util.Set;

/**
 * @IgnoreI18nFile
 */
public interface ProductConstants {
    String PRODUCT_ID = "product_id";
    String PRODUCT_PRICE = "price";
    String PRODUCT_OWNER = "owner";
    String PRODUCT_RELEVANT_TEAM = "relevant_team";
    String PRODUCT_SPU_ID = "spu_id";
    String UNIT = "unit";
    String IS_SALEABLE = "is_saleable";
    String IS_PACKAGE = "is_package";
    String PRODUCT_SPEC = "product_spec";
    String PRODUCT_CODE = "product_code";
    String BARCODE = "barcode";
    String PRODUCT_STATUS = "product_status";
    String CATEGORY = "category";

    String NON_ATTRIBUTE_VALUES = "non_attribute_values";
    String NONSTANDARD_ATTRIBUTE_IDS = "nonstandard_attribute_ids";
    //虚拟字段
    String VIRTUAL_PRODUCT_PRICE = "virtual_product_price";
    String VIRTUAL_PRICE_BOOK_SELLING_PRICE = "virtual_price_book_selling_price";
    String VIRTUAL_PRICE_BOOK_PRICE = "virtual_price_book_price";
    String VIRTUAL_BASE_PRICE_BOOK_PRICE = "virtual_base_price_book_price";
    String VIRTUAL_AVAILABLE_STOCK = "virtual_available_stock";


    String VIRTUAL_PRICE_BOOK_SELLING_PRICE__N = "virtual_price_book_selling_price__n";
    String VIRTUAL_PRICE_BOOK_PRICE__N = "virtual_price_book_price__n";
    String VIRTUAL_BASE_PRICE_BOOK_PRICE__N = "virtual_base_price_book_price__n";

    //一品文化【最小起订量】
    String VIRTUAL_MIN_ORDER_QUANTITY = "virtual_min_order_quantity";

    String MC_CURRENCY__R = "mc_currency__r";
    String MC_FUNCTIONAL_CURRENCY__R = "mc_functional_currency__r";

    String PRICE_BOOK_ID = "price_book_id";
    String PRICE_BOOK_PRODUCT_ID = "price_book_product_id";
    String PRICE_BOOK_DISCOUNT = "price_book_discount";
    String PRICE_BOOK_PRICE = "price_book_price";
    String PRICE_BOOK_ID__R = "price_book_id__r";
    String PRICE_BOOK_PRODUCT_ID__R = "price_book_product_id__r";

    String PRODUCT_CATEGORY_ID__R = "product_category_id__r";
    /**
     * 小计
     */
    String SUBTOTAL = "subtotal";
    /**
     * 销售单价
     */
    String SALES_PRICE = "sales_price";
    /**
     * 数量
     */
    String QUANTITY = "quantity";
    String DECIMAL_PLACES = "decimal_places";
    String PRICING_MODE = "pricing_mode";
    String PRICING_CYCLE = "pricing_cycle";
    String PRICING_FREQUENCY = "pricing_frequency";
    String SETTLEMENT_MODE = "settlement_mode";
    String SETTLEMENT_CYCLE = "settlement_cycle";
    String SETTLEMENT_FREQUENCY = "settlement_frequency";
    String WHOLE_PERIOD_SALE = "whole_period_sale";

    String PRODUCT_TYPE = "product_type";

    String STORE_ID = "store_id";
    String MALL_CATEGORY_ID = "mall_category_id";

    int MAX_SKU_LIMIT_OF_SAME_GROUP = 400;
    public static final String ADD_ACTION = "Add";
    public static final String ABOLISH_ACTION = "Abolish";
    public static final String DELETE_ACTION = "Delete";
    public static final String RECOVER_ACTION = "Recover";
    public static final String SUPER_ADMIN_USER_ID = "-10000";

    //最近订购页签
    String TAB_RECENTLY_ORDERED = "tab_recently_ordered";
    //促销产品页签
    String TAB_PROMOTION = "tab_promotion";

    String PRODUCT_MULTI_UNIT_CALL_BACK_KEY = "product_multi_unit_call_back_key";

    Set<String> PRODUCT_VIRTUAL_FIELDS = Sets.newHashSet(VIRTUAL_AVAILABLE_STOCK, VIRTUAL_BASE_PRICE_BOOK_PRICE,
            VIRTUAL_PRICE_BOOK_PRICE, VIRTUAL_PRICE_BOOK_SELLING_PRICE);

    /**
     * 深研单独更新产品对象不可更新预置字段的集合
     */
    Set<String> CANT_SUPPORT_UPDATE_SKU_FIELDS = Collections.unmodifiableSet(
            Sets.newHashSet("name", "order_field", "unit", "product_line", "category", "product_category_id", "owner", "lock_status",
                    "life_status", "relevant_team", "record_type", "data_own_department", "product_spec", "batch_sn",
                    "shop_category_id", "store_id", "mall_category_id"));

    enum Field {
        STATUS("product_status", "状态"),
        PRICE("price", "标准价格");

        private final String apiName;
        private final String label;

        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

        public String getApiName() {
            return this.apiName;
        }
    }

    enum Status {
        ON("1", "已上架"), OFF("2", "已下架"), HIDE("99", "已作废");

        private final String status;
        private final String name;

        Status(String status, String name) {
            this.status = status;
            this.name = name;
        }

        public String getStatus() {
            return this.status;
        }
    }

    enum SkuEditStatus {
        /**
         * 要编辑的数据
         */
        EDIT(1),
        /**
         * 要新增的数据
         */
        ADD(2),
        /**
         * 要作废的数据
         */
        INVALID(3),
        /**
         * 已经作废的数据
         */
        INVALIDED(4),
        /**
         * 更换规格值的数据
         */
        SKU_EDIT_STATUS(5),
        /**
         * 新增规格
         */
        ADD_SPEC(6);

        private final int status;

        SkuEditStatus(int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }
    }

    enum DescribeField {
        FIELD_DESCRIBE_ID("object_describe_id", "描述ID"),
        FIELD_DESCRIBE_API_NAME("object_describe_api_name", "apiName");

        private final String apiName;
        private final String label;

        DescribeField(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

        public String getApiName() {
            return this.apiName;
        }
    }

    enum DeleteStatus {
        IS_DELETE_NORMAL("0", "正常"),
        IS_DELETE_INVALID("1", "作废"),
        IS_DELETE_DELETE("-1", "删除");

        private final String deleteCode;
        private final String deleteName;

        DeleteStatus(String deleteCode, String deleteName) {
            this.deleteCode = deleteCode;
            this.deleteName = deleteName;
        }

        public String getDeleteCode() {
            return deleteCode;
        }
    }

    /**
     * 产品关联价目表数据来源（关联到价目表明细）
     */
    enum ProductRelationPriceBookDataSource {
        PRICE_BOOK_DETAIL("0","价目表明细关联"),
        PRODUCT_CREATE("1","产品新增关联");
        private final String code;
        private final String label;
        ProductRelationPriceBookDataSource(String code,String label) {
            this.code = code;
            this.label = label;
        }
        public String getCode() {
            return code;
        }

        public String getLabel() {
            return label;
        }
    }

    enum PricingModeEnum {
        ONE("one", "一次性"),// ignoreI18n
        CYCLE("cycle", "周期性");// ignoreI18n

        private String value;
        private String label;

        PricingModeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return this.value;
        }
    }

    enum ProductTypeEnum {
        STANDARD("standard", "普通品"),// ignoreI18n
        NON_STANDARD("non_standard", "非标品");// ignoreI18n

        private String value;
        private String label;

        ProductTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return this.value;
        }
    }


}
