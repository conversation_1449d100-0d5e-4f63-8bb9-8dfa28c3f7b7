package com.facishare.crm.sfa.utilities.constant;

/**
 * @描述说明：
 * 
 * @作者：chench
 * @创建日期：2025-04-15
 *
 * @IgnoreI18n
 */
public interface ContractProgressConstants {

    interface Rule {
        String DESC_API_NAME = "ContractProgressRuleObj";
        String NAME = "name";
        String DESCRIPTION = "description";
        String ENABLED_STATUS = "enabled_status";
        String CONTRACT_RECORD_TYPE = "contract_record_type";
        String INDEX_TYPE = "index_type";
        String INDEX_TYPE_OBJECT = "index_type_object";
        String INDEX_TYPE_OBJECT_FIELD = "index_type_object_field";
        String INDEX_TYPE_OBJECT_FIELD_VALUE = "index_type_object_field_value";
        String INDEX_TYPE_CATEGORY = "index_type_category";
        String INDEX_TYPE_CALC_TYPE = "index_type_calc_type";
        String INDEX_GOAL_OBJECT = "index_goal_object";
        String INDEX_GOAL_OBJECT_FIELD = "index_goal_object_field";
        String INDEX_GOAL_DATA_OBJECT = "index_goal_data_object";
        String INDEX_GOAL_DATA_DIRECT_OBJECT = "index_goal_data_direct_object";
        String INDEX_GOAL_DATA_OBJECT_FIELD = "index_goal_data_object_field";
        String INDEX_GOAL_DATA_REF_CONTRACT_FIELD = "index_goal_data_ref_contract_field";
        String INDEX_GOAL_DATA_REF_PRODUCT_FIELD = "index_goal_data_ref_product_field";
        String INDEX_GOAL_DATA_REF_TIME_FIELD = "index_goal_data_ref_time_field";
        String INDEX_GOAL_DATA_CONDITION = "index_goal_data_condition";

        enum IndexTypeEnum {
            ContractProduct("1", "合同产品"),
            Product("2", "产品分类"),
            Contract("3", "合同数值");
            private String value;
            private String name;


            IndexTypeEnum(String value, String name) {
                this.value = value;
                this.name = name;
            }

            public String getValue() {
                return value;
            }
        }
    }

    interface RuleGoal {
        String DESC_API_NAME = "ContractProgressRuleGoalObj";

        String RULE_ID = "rule_id";
        String CONTRACT_ID = "contract_id";

        String PRODUCT_ID = "product_id";

        String GOAL_VALUE = "goal_value";

        String CURRENT_VALUE = "current_value";

        String TO_COMPLETE_TIME = "to_complete_time";
    }

    interface RuleGoalCheck {
        String DESC_API_NAME = "ContractProgressRuleGoalCheckObj";
        String RULE_GOAL_ID = "rule_goal_id";

        String GOAL_VALUE = "goal_value";

        String TO_CHECK_TIME = "to_check_time";

        String is_checked = "is_checked";

    }

    interface RuleGoalSnapshot {
        String DESC_API_NAME = "ContractProgressRuleGoalSnapshotObj";

        String RULE_GOAL_ID = "rule_goal_id";

        String GOAL_VALUE = "goal_value";

        String CURRENT_VALUE = "current_value";

        String CHECK_TYPE = "check_type";
    }

    enum SnapshotCheckType {
        HALFWAY("1", "过程检查"),
        FINALITY("2", "完结检查");

        private String code;
        private String name;

        SnapshotCheckType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }
    }

}
