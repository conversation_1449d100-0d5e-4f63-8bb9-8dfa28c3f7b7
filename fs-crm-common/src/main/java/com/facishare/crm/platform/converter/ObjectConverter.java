package com.facishare.crm.platform.converter;

import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.DefaultValue;
import com.facishare.crm.platform.annotation.Mappable;
import com.facishare.crm.platform.exception.ConverterException;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * Created by Sundy on 2024/10/15 10:21
 */
@Slf4j
@Component
public class ObjectConverter implements Converter {
    @Override
    public <I, O> O convertDTO(I inputData, Class<O> outputClazz) {
        O outputObj;
        try {
            outputObj = outputClazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("outputClazz newInstance error", e);
            throw new ConverterException("outputClazz newInstance error");
        }
        if (!outputClazz.isAnnotationPresent(Convertible.class)) {
            log.error("convertDTO#outputClazz is not annotated with @Convertible, outputClazz:{}", outputClazz.getName());
            throw new ConverterException("outputObj is not annotated with @Convertible");
        }
        Convertible clazzAnnotation = outputClazz.getAnnotation(Convertible.class);
        // 输出对象
        Field[] outputFields = getFieldsWithoutDuplicates(outputClazz);
        for (Field outputField : outputFields) {
            if (!clazzAnnotation.allField() && !outputField.isAnnotationPresent(Mappable.class)) {
                continue;
            }
            try {
                outputField.setAccessible(true);
                Object inputFieldValue = getInputFieldValueWithDefault(inputData, outputField);
                outputField.set(outputObj, inputFieldValue);
            } catch (Exception e) {
                log.error("DoConverter convert IObjectData error", e);
                throw new ConverterException("DoConverter convert IObjectData error");
            }
        }
        return outputObj;
    }

    private static <O> Field[] getFieldsWithoutDuplicates(Class<O> clazz) {
        List<Field> fields = new ArrayList<>();
        Set<String> fieldNames = new HashSet<>();  // 用于记录已添加字段的名称，以避免重复
        // 先处理子类字段
        while (clazz != null) {
            Field[] declaredFields = clazz.getDeclaredFields();
            for (Field field : declaredFields) {
                if (!fieldNames.contains(field.getName())) {  // 如果该字段尚未添加
                    fields.add(field);
                    fieldNames.add(field.getName());  // 记录该字段的名称，避免重复添加
                }
            }
            clazz = (Class<O>) clazz.getSuperclass();  // 向上遍历父类
        }
        return fields.toArray(new Field[0]);
    }

    @Override
    public <I, O> List<O> convertDTOList(List<I> inputDataList, Class<O> outputClazz) {
        if (CollectionUtils.isEmpty(inputDataList)) {
            return new ArrayList<>();
        }
        List<O> outputObjects = new ArrayList<>(inputDataList.size());
        for (I inputData : inputDataList) {
            O item = convertDTO(inputData, outputClazz);
            outputObjects.add(item);
        }
        return outputObjects;
    }


    @Override
    public <T> IObjectData convertObjectData(T inputObj) {
        Class<?> inputClazz = inputObj.getClass();
        if (!inputClazz.isAnnotationPresent(Convertible.class)) {
            log.error("inputClazz is not annotated with @Convertible, clazz:{}", inputClazz.getName());
            throw new ConverterException("inputObj is not annotated with @Convertible");
        }
        Convertible clazzAnnotation = inputClazz.getAnnotation(Convertible.class);
        Map<String, Object> map = Maps.newHashMap();
        Field[] fields = getFieldsWithoutDuplicates(inputClazz);
        for (Field field : fields) {
            if (!clazzAnnotation.allField() && !field.isAnnotationPresent(Mappable.class)) {
                continue;
            }
            try {
                field.setAccessible(true);
                String fieldApiName = getFieldApiName(field);
                Object value = field.get(inputObj);
                value = getFieldValueWithDefault(field, value);
                map.put(fieldApiName, value);
            } catch (Exception e) {
                log.error("DoConverter convert IObjectData error", e);
                throw new ConverterException("DoConverter convert IObjectData error");
            }
        }
        return ObjectDataDocument.of(map).toObjectData();
    }

    @Override
    public <T> T convertDTO(IObjectData objectData, Class<T> outputClazz) {
        T outputObj;
        try {
            outputObj = outputClazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("outputClazz newInstance error", e);
            throw new ConverterException("outputClazz newInstance error");
        }
        if (!outputClazz.isAnnotationPresent(Convertible.class)) {
            log.error("outputClazz is not annotated with @Convertible, outputClazz:{}", outputClazz.getName());
            throw new ConverterException("outputClazz is not annotated with @Convertible");
        }
        Convertible clazzAnnotation = outputClazz.getAnnotation(Convertible.class);
        // 输出对象
        Field[] outputFields = getFieldsWithoutDuplicates(outputClazz);
        for (Field outputField : outputFields) {
            if (!clazzAnnotation.allField() && !outputField.isAnnotationPresent(Mappable.class)) {
                continue;
            }
            try {
                outputField.setAccessible(true);
                Object fieldValue = getObjectDataFieldValueWithDefault(objectData, outputField);
                outputField.set(outputObj, fieldValue);
            } catch (Exception e) {
                log.error("DoConverter convert DTO error", e);
                throw new ConverterException("DoConverter convert DTO error");
            }
        }
        return outputObj;
    }

    @Override
    public <T> List<T> convertInPutDataList(List<IObjectData> objectDataList, Class<T> outputClazz) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return new ArrayList<>();
        }
        List<T> outputObjects = new ArrayList<>(objectDataList.size());
        for (IObjectData objectData : objectDataList) {
            T item = convertDTO(objectData, outputClazz);
            outputObjects.add(item);
        }
        return outputObjects;
    }

    private Object getObjectDataFieldValueWithDefault(IObjectData objectData, Field outputField) {
        Object fieldValue = getObjectDataFieldValue(objectData, outputField);
        return getFieldValueWithDefault(outputField, fieldValue);
    }

    private <I> Object getInputFieldValue(I inputObj, Field outputField) {
        Class<?> inputClazz = inputObj.getClass();
        String fieldMethodName = "get" + getFromFieldMethodName(outputField);
        Method method;
        try {
            method = inputClazz.getMethod(fieldMethodName);
        } catch (NoSuchMethodException e) {
            //ignore
            log.warn("getSourceFieldValue#fieldMethodName:{}", fieldMethodName, e);
            return null;
        }
        try {
            method.setAccessible(true);
            return method.invoke(inputObj);
        } catch (Exception e) {
            log.error("getSourceFieldValue#method invoke error, fieldMethodName:{}", fieldMethodName, e);
            throw new ConverterException("getSourceFieldValue#method invoke error");
        }
    }

    private String getFromFieldMethodName(Field field) {
        Mappable mappableAnnotation = field.getAnnotation(Mappable.class);
        String fieldName = field.getName();
        if (mappableAnnotation != null && mappableAnnotation.from() != null && !mappableAnnotation.from().isEmpty()) {
            fieldName = mappableAnnotation.from();
        }
        return NamingConventionConverter.capitalize(fieldName);

    }

    private String getFieldApiName(@NotNull Field field) {
        Mappable mappableAnnotation = field.getAnnotation(Mappable.class);
        if (mappableAnnotation == null) {
            return NamingConventionConverter.camelToSnake(field.getName());
        }
        return StringUtils.isNotBlank(mappableAnnotation.fieldApiName())
                ? mappableAnnotation.fieldApiName()
                : NamingConventionConverter.camelToSnake(field.getName());
    }

    private Object getObjectDataFieldValue(@NotNull IObjectData objectData, @NotNull Field outputField) {
        String fieldApiName = getFieldApiName(outputField);
        return ObjectDataUtils.getValue(objectData, fieldApiName, outputField.getType(), null);
    }

    private <I> Object getInputFieldValueWithDefault(I inputData, Field outputField) {
        Object inputFieldValue = getInputFieldValue(inputData, outputField);
        return getFieldValueWithDefault(outputField, inputFieldValue);
    }

    private Object getFieldValueWithDefault(@NotNull Field field, @Nullable Object fieldValue) {
        if (fieldValue != null || !field.isAnnotationPresent(DefaultValue.class)) {
            return fieldValue;
        }
        return TypeValueParser.parse(field.getType(), field.getAnnotation(DefaultValue.class).value());
    }
}
