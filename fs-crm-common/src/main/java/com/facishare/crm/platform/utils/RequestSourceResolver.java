package com.facishare.crm.platform.utils;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-07-07
 * ============================================================
 */
@Service
@Slf4j
public class RequestSourceResolver {

    public boolean isErAppRequest() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            log.warn("Context is null");
            return false;
        }
        return StringUtils.isNotBlank(context.getAppId());
    }
}
