package com.facishare.crm.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;

import static com.facishare.crm.constants.PrmI18NConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * <AUTHOR>
 * @time 2024-06-27 17:58
 * @Description
 */
public enum SignModel {
    /**
     * 电子签
     */
    E_SIGNATURE("e_signature"),
    /**
     * 手动上传签署文件
     */
    MANUAL("manual"),
    /**
     * APL 函数
     */
    APL("apl");

    private final String model;

    SignModel(String model) {
        this.model = model;
    }

    public static SignModel fromString(String model) {
        for (SignModel e : values()) {
            if (e.model.equals(model)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "signModel"));
    }
}
