package com.facishare.crm.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.constants.PrmI18NConstants.PRM_PARAM_PROTOCOL_ERROR;

@Getter
public enum ScheduleType {
    /**
     * 固定周期签署
     */
    CYCLE("cycle"),
    /**
     * 固定日签署
     */
    FIXED_DATE("fixed_date"),
    /**
     * 一次性签署
     */
    ONE_TIME("one_time");

    private final String type;

    ScheduleType(String type) {
        this.type = type;
    }

    public static ScheduleType from(String type) {
        for (ScheduleType e : values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "scheduleType"));
    }
    public static ScheduleType find(String type) {
        return find(type, null);
    }

    public static ScheduleType find(String type, ScheduleType defaultValue) {
        for (ScheduleType e : values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        return defaultValue;
    }
}
