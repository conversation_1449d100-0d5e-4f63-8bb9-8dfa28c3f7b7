package com.facishare.crm.enums;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public enum ConfigType {
    NONE("-1", "查无此项目", "0"),
    IS_ALLOW_EXPORT_CUSTOMER_AND_CONTACT("1", "是否允许员工导出客户和联系人", "1"),
    IS_ALLOW_CREATE_CUSTOMER("2", "是否禁止员工添加客户", "1"),
    IS_FUZZY_QUERY("4", "是否开启模糊查询", "1"),
    IS_OPEN_FILING("3", "是否启用客户报备", "0"),
    CHECK_DUPLICATE("5", "客户查重显示项", "1,2,6,5,3,4"),
    IS_OPEN_CUSTOMER_CHECK_REPEAT("6", "是否开启客户查重", "0"),
    IS_ALLOW_NO_RIGHT_SEE_COMPLETE_CUSTOMER_NAME("7", "是否禁止无客户权限的员工在工作流中看到完整客户名称", "0"),
    SERVICE_EMPLOYEE_FIND_CATEGORY("9", "服务人员查询类别 1、模糊查询；2、精确查询", "1"),
    LEADER_VIEW_SCOPE("10", "(5.2新增) 上级可见数据范围=10;[ EValue：1-所有下级数据，2-直属下级数据]", "2"),
    IS_ALLOWED_TO_EDIT_CUSTOMER_NAME("11", "(5.3新增) 允许负责人修改客户名称 [1-可以;0-不可以]", "0"),
    IS_ALLOWED_SET_PERSONAL_GOAL("12", "(5.3新增)允许个人设置目标 [1-可以;0-不可以]", "0"),
    IS_DISCOUNT_AUTO_CALCULATED("13", "(5.3新增) 折扣是否自动计算 [1-是;0-否]", "0"),
    CRM_VERSION("15", "(5.4新增) CRM版本 1、普通版本；2、快销版本 具体参照枚举 CRMVersionEnum", "1"),
    CUSTOMER_ORDER_RULE("16", "(5.4新增) 订单规则 ，默认勾选3。其中12互斥,1 订单金额=产品合计*折扣，产品是必填项,2 订单金额、产品合计、折扣互相独立，产品是选填项,3 订单金额、产品合计、折扣互相独立，产品是必填项", "0,0,1"),
    DUPLICATE_SEARCH_BASIC_SETTING("17", "(5.5 新增) 查重基本规则", "[{\"ObjectType\":2,\"AddDulicate\":true,\"ToolDulicate\":true},{\"ObjectType\":3,\"AddDulicate\":true,\"ToolDulicate\":true},{\"ObjectType\":1,\"AddDulicate\":false,\"ToolDulicate\":true},{\"ObjectType\":8,\"AddDulicate\":false,\"ToolDulicate\":false}]"),
    OPPORTUNITY_DUPLICATE_SEARCH("20", "(5.5 新增) 商机判重 1-判重  0-不判重", "0"),
    TRADE_ORDER_WORKFLOW_TYPE("21", "(5.5 新增) 审批流订单类型 1-自定义审批流  0-固定审批流", "0"),
    RETURN_ORDER_WORKFLOW_TYPE("22", "(5.5 新增) 审批流退货单类型 1-自定义审批流  0-固定审批流", "0"),
    IS_DING_HUO_TONG_ENABLED("23", "(5.5 新增) 是否启用订货通(0-不启用 1-启用)", "0"),
    IS_FAST_SELLING_ENABLED("24", " (5.5新增）是否启用快销版功能（0-不起用 1-启用）", "0"),
    CUSTOMER_FOLLOW_DEAL_SETTING("25", "(5.6新增）客户跟进成交动作配置项", ""),
    UNIQUENESS_RULES_SETTING("26", "(6.0新增）唯一性规则", ""),
    UNION_DS_SETTING("27", "(6.0新增）联合查重配置", ""),
    IS_PRICE_BOOK_ENABLED("28", "(6.1新增）是否启用价目表（0-不起用 1-启用）", "0"),
    IS_CUSTOMER_ACCOUNT_ENABLED("29", "(6.1Added) 客户账户是否启用（0-不启用,1-启用)", "0"),
    IsNewCustomerAccountEnabled("is_customer_account_enable", "客户账户2.0是否启用（1-不启用,2-启用)", "1"),
    IsPaymentEnterAccountEnable("is_payment_enter_account_enable", "回款是否入账", "0"),
    IsPaymentPayEnable("is_payment_pay_enable", "回款支付", "0"),
    IsSalesOrderPayDirectlyEnable("is_sales_order_pay_directly_enable", "订单直接支付", "0"),
    ClaimUponReceiptOfPayment("claim_upon_receipt_of_payment", "回款到款认领", "0"),
    OrderStatusTab("order_status_tab", "订单状态页签（0-不启用,1-启用)", "1"),
    DHT_SHOPPING_CART_CUSTOM_FIELD_CONFIG("dht_shopping_cart_custom_field_config", "购物车底部自定义字段配置", "order_amount,product_amount"),
    IS_PAYMENT_WITH_DETAIL_ENTER_ACCOUNT_ENABLE("is_payment_with_detail_enter_account_enable", "有回款明细可以入账", "0"),
    IsInventoryEnabled("30", "(6.2Added)库存是否启用", "0"),
    IsPromotionEnabled("31", "(6.2Added)促销是否启用", "0"),
    IsDeliveryNoteEnabled("33", "(6.2Added)发货单是否启用", "0"),
    IsShowRankingList("34", "(6.2Added)是否显示排行榜", "0"),
    IsDeviceManagementEnabled("35", "(6.3Added)是否开启设备管理", "0"),
    DuplicateSearchLastModifyBy("36", "(6.3Added)查重最后修改人", ""),
    IsOpenPartner("37", "(6.3Added)是否开启合作伙伴", "0"),
    IsSalesRecordTypeNotNull("38", "(6.3.1Added)销售记录类型是否必填", "0"),
    IsServiceRecordTypeNotNull("39", "(6.3.1Added)服务记录类型是否必填", "0"),
    IsEnableTransExistCustomer("40", "(6.3.1Added)是否启用线索转换时关联已有客户", "1"),
    LeadsTransferSetting("41", "(6.3.2Added)线索转换设置 客户--1必选  联系人--1必选，0可选 商机--1必选，0可选 商机2.0--1必选，0可选，与商机最多二选一", "1,0,0,0"),
    IsOpenNewOpportunity("42", "(6.3.4Added)开启商机2.0", "0"),
    IsOpenNewOpportunityByNewKey("config_newopportunity_open", "(6.3.4Added)开启商机2.0", "0"),
    IsTradeProductRepeatable("43", "(6.3.4Added)订单产品是否可重复  1-可重复，0-不可重复 开通订货通需要更改为可重复", "1"),
    IsIndustryPriceBookEnabled("44", "(6.3.4Added)行业价目表", "0"),
    IsSendFeedContactWithCustomer("45", "联系人的销售记录同关联到客户下必填可配置", "0"),
    CanEditOrderWhenPromotionEnabled("46", "(6.5Added)当促销开启时,是否可编辑订单", "0"),
    IsBatchNumberOrSerialNumberEnabled("47", "(6.5Added)是否开启批次、序列号管理 必须先开启库存，不可关闭", "0"),
    IsAllowedToEditPartnerName("48", "允许负责人修改合作伙伴名称 [1-可以;0-不可config_change_close_date_if_win以]【默认可以】", "1"),
    IsOpenSalesOrder("49", "销售订单是否开启", "0"),
    IsPromotionEnabledWhenEditSalesOrder("50", "编辑订单仍然适配促销", "0"),
    ConfigChangeCloseDateIfWin("config_change_close_date_if_win", "赢单是否更改结单日期", "0"),
    TransferToAccountChangeStageSetting("51", "线索转换触发线索阶段变更设置", "1,SQL"),
    TransferToOppChangeStageSetting("52", "线索转换触发线索阶段变更设置", "1,SQL"),
    PUT_CRM_FEED_SELECT_MODE("put_crm_feed_select_mode", "线索转换带入销售记录配置", "1"),
    PUT_TEAMS_SELECT_MODE("put_teams_select_mode", "线索转换带入相关团队配置", "1"),
    UPSTREAM_PROMOTION_OPERATION("upstream_promotion_operation", "功能操作", "{\"create\":true, \"update\":true}"),
    UPSTREAM_PROMOTION_ORDER_RECORD_TYPE("upstream_promotion_order_record_type", "订单业务类型", "all"),
    IS_SHOW_DUPLICATE_SEARCH("IsShowDuplicateSearch", "是否开启查重", "1"),
    NEW_INVOICE("new_invoice", "new_invoice", "0"),
    INVOICE_IS_ALLOWED_OVERFLOW("invoice_is_allowed_overflow", "invoice_is_allowed_overflow", "0"),
    MODULE_CPQ("cpq", "销售Bom", "0"),
    CHECK_BOM_TREE("check_bom_tree", "校验bom结构", "0"),
    MODULE_SIMPLE_CPQ("simple_cpq", "固定搭配", "0"),
    BOM_TEMP_NODE("bom_temp_node", "临时子件", "0"),
    MULTIPLEXED_BOM_MODE("multiplexed_bom_mode", "嵌套BOM开关", "0"),
    BOM_SINGLE_LEAF_NODE_CLOSED("bom_single_leaf_node_closed", "分组单选时自动收起分组", "0"),
    GENERATE_STANDARD_BOM_BASED_ON_ORDER("generate_standard_bom_based_on_order", "根据下单选配结果生成标准bom", "0"),
    MODULE_CPQ_TIEREDPRICE("cpq_tieredprice", "cpq_tieredprice", "0"),
    CPQ_ENABLED_LATEST_VERSION("cpq_enabled_latest_version", "CPQ下单支持默认最新版本", "0"),
    MODULE_SPU("config_spu_or_sku_selector", "config_spu_or_sku_selector", "0"),
    MODULE_PRM("config_partner_open", "config_partner_open", "0"),
    MODULE_PRICE_BOOK("pricebook", "pricebook", "0"),
    MODULE_CHANGE_TO_NEW_OPPORTUNITY("change_to_new_opportunity", "change_to_new_opportunity", "0"),
    MODULE_MULTIPLE_UNIT("multiple_unit", "multiple_unit", "0"),
    DEFAULT_MODULE("default_module", "default_module", "0"),
    MODULE_SAVE_NEW_OPPORTUNITY_USER_FILTER("new_opportuntiy_user_filter", "new_opportuntiy_user_filter", "0"),
    SPU("spu", "商品设置", "0"),
    CPQ("cpq", "配置产品组合", "0"),
    ORDER_CLOSE("order_close", "订单行关闭", "{\"status\":\"0\",\"delivery_status\":\"0\",\"accounts_receivable_status\":\"0\"}"),
    STANDARD_CPQ("standard_cpq", "开启标准BOM", "0"),
    BOM_DELETE_ROOT("bom_delete_root", "单据过滤bom根节点", "0"),
    BOM_INSTANCE("bom_instance", "产品选配实例", "0"),
    INVOICE_MODE("invoice_mode", "开票模式", "normal"),
    ACCOUNT_TREE_FIELDS("account_tree_fields", "客户层级关系展示字段", "name"),
    NEW_OPPORTUNTIY_LEADS_SETTING("new_opportuntiy_leads_setting", "商机归因设置0:不自动关联；1：自动关联第一个；2：自动关联最近一个", "0"),
    IS_KX_PECULIARITY_ENABLED("is_kx_peculiarity_enabled", "是否是快销企业", "0"),
    IS_KX_OPEN_SCAN_CODE_ENABLED("is_kx_open_scan_code_enabled", "是否开启扫码", "0"),
    AVAILABLE_RANGE("available_range", "是否开启可售范围", "0"),
    ENFORCE_PRIORITY("enforce_priority", "是否强制执行价目表优先级最优价格", "0"),
    CONTACT_RELATIONSHIP_TYPE("contact_relationship_type", "联系人关系类型", "[{\"value\":\"1\",\"label\":\"上级\"}]"),
    IS_OPEN_AVAILABLE_RANGE_PRIORITY("is_open_available_range_priority", "是否开启可售范围优先级", "0"),
    CLONE_HISTORY_ORDER_PRODUCT("clone_history_order_product", "复制历史订单产品", "0"),
    BOM_PRINT_TEMPLATE_INDENT("bom_print_template_indent", "XX明细打印Bom支持缩进", "0"),
    BOM_PRINT_TEMPLATE_HAS_SUB_NODE("bom_print_template_has_sub_node", "XX明细打印Bom是否包含子节点", "0"),
    PROMOTION_MOBILE_H5("promotion_mobile_h5", "促销是否移动端跳转订单h5", "0"),
    IS_OPEN_ATTRIBUTE("is_open_attribute", "是否开启属性属性值", "0"),
    IS_OPEN_NONSTANDARD_ATTRIBUTE("is_open_nonstandard_attribute", "是否开启非标属性", "0"),
    CHANGE_BUSINESS_TYPE("change_business_type", "是否工商回填", "0"),
    PROMOTION_STATUS("promotion_status", "是否开启促销", "0"),
    ACCOUNT_LEADS_TO_ENTERPRISE("account_leads_to_enterprise", "线索/客户同步企业库规则", "{\"use_default_rule\":\"1\",\"func_api_name\":\"\",\"func_name\":\"\",\"binding_object_api_name\":\"NONE\"}"),
    ACCOUNT_MAIN_DATA_ENABLE_COMMON_PRIVILEGE("account_main_data_enable_common_privilege", "新建客户选主数据对象使用系统通用权限过滤可选数据范围", "0"),
    LEADS_TRANSFER_RIGHT_SETTING("leads_transfer_right_setting", "线索是否允许转换为无查看权限客户", "1"),
    LEADS_TRANSFER_GRAY_PRICE_BOOK("leads_transfer_gray_price_book", "线索转换同时新建客户和商机灰度选择全部价目表", "0"),
    IS_ENABLE_GEO_MANUAL_MAINTENANCE("is_enable_geo_manual_maintenance", "是否开启地址GEO手动维护", "0"),
    IS_ENABLE_LEADS_TRANSFER_DRAFTS("is_enable_leads_transfer_drafts", "是否开启线索一转三草稿箱", "1"),
    THE_LEADS_POOL_TEAM("the_leads_pool_team", "线索池分管小组", "0"),
    MULTIPLE_UNIT("multiple_unit", "是否开启多单位", "0"),
    AVAILABLE_RANGE_DUPLICATED_CHECK("available_range_duplicated_check", "available_range_duplicated_check", "0"),
    PERIOD_PRODUCT("periodic_product_plugin", "周期性产品插件", "[]"),
    NON_STANDARD_PRODUCT("non_standard_product", "非标产品", "0"),
    CHANGE_PRODUCT_TYPE_REFRESH_PRICE("change_product_type_refresh_price", "非标品切换普通品是否重新取价(默认重新取价)", "0"),
    /*产品关键字检索模式
    空-单关键词搜索
    and-多关键词且模式
    or-多关键词或模式*/
    PRODUCT_KEYWORD_SEARCH_MODE("product_keyword_search_mode", "产品关键字检索模式", ""),
    GET_PRICE_WHEN_CONVERT("get_price_when_convert", "转换时是否重新取价", "0"),
    PRICE_POLICY("price_policy", "价格政策", "0"),
    PRICE_POLICY_SALES_ORDER_OBJ("price_policy_SalesOrderObj", "销售订单使用价格政策", "0"),
    PRICE_POLICY_QUOTE_OBJ("price_policy_QuoteObj", "报价单使用价格政策", "0"),
    //0-关闭，1-开启,885暂时灰度，后期会全网放开
    PRICE_POLICY_SUPPORT_PERCENTILE_GIFT("price_policy_support_percentile_gift", "价格政策支持按比例送赠品", "0"),

    /**
     * 保存后清除分摊缓存
     */
    PRICE_POLICY_SAVE_MOVE_AMORTIZE("price_policy_save_move_amortize", "保存后清除分摊缓存", "1"),
    /**
     * 0-不开启
     * 1-开启
     * 整单分摊是否创建分摊明细数据，新企业默认开启1，旧的企业需要刷成0
     */
    PRICE_POLICY_CREATE_FULL_AMORTIZE("price_policy_create_full_amortize", "整单分摊是否创建分摊明细数据", "1"),
    /**
     * 0-不启用
     * 1-启用
     */
    ENABLE_GIFT_RANGE_SHELVES("enable_gift_range_shelves", "价格政策启用过滤上架产品，和可售范围", "0"),
    /**
     * 0-不创建分摊数据（默认值）
     * 1-创建分摊数据（灰度此功能的企业，为此状态）
     * 整单额外调整，是否允许创建分摊明细数据
     */
    DYNAMIC_ALLOW_AMORTIZE("dynamic_allow_amortize", "整单额外调整，是否允许创建分摊明细数据", "0"),

    ONE_CLICK_ORDER_TRANSFER("one_click_order_transfer", "一键转单","0"),

    /**
     * 价格政策匹配模式
     * immediately ：即时多次匹配
     * once ：最终一次匹配
     */
    MATCH_MODE("match_mode", "价格政策匹配模式", "immediately"),

    /**
     * 移动端：商机2.0/报价单/销售合同/销售订单，选择数据页面左下角字段显示支持配置
     */
    MOBILE_SUMMARY_SETTING("mobile_bottom_summary_setting", "移动端选择数据页面左下角字段显示配置", "{\"totalAmount\":\"1\",\"categories\":\"1\"}"),

    /**
     * 移动端移动端新建/编辑页面，左下角增加【合计】字段，支持配置显示或者隐藏
     */
    SALES_ORDER_MOBILE_EDIT_PAGE_SUMMARY_SETTING("order_mobile_edit_page_summary_setting", "【合计】字段仅在移动端新建/编辑页面左下角显示", "0"),

    /**
     * 1、展示
     * 0、不展示（default）
     */
    SHOW_PRICE_POLICY_NAME("show_price_policy_name", "移动端订单选产品，促销产品是否显示价格政策名称", "0"),
    /**
     * 是否可以切换订单主对象价格政策
     * 1、允许（default）
     * 0、不允许
     */
    ALLOW_SWITCH_MASTER_PRICE_POLICY("allow_switch_master_price_policy", "是否可以切换订单主对象价格政策", "1"),
    /**
     * 是否可以切换订单从对象价格政策
     * 1、允许（default）
     * 0、不允许
     */
    ALLOW_SWITCH_DETAIL_PRICE_POLICY("allow_switch_detail_price_policy", "是否可以切换订单从对象价格政策", "1"),
    PRICE_POLICY_UNIT("price_policy_unit", "价格政策产品默认单位", "{\"product_unit\":\"baseUnit\",\"gift_unit\":\"baseUnit\"}"),
    GET_PRICE_WHEN_COPY("get_price_when_copy", "复制订单时是否重新取价", "1"),
    GET_PRICE_WHEN_COPY_QUOTE("get_price_when_copy_quote", "复制报价单时是否重新取价", "1"),
    GET_PRICE_WHEN_COPY_CONTRACT("get_price_when_copy_contract", "复制销售合同时是否重新取价", "1"),
    GET_PRICE_WHEN_COPY_NEWOPPORTUNITY("get_price_when_copy_newopportunity", "复制商机2.0时是否重新取价", "1"),
    IS_TEST_CALCULATE("is_test_calculate", "是否开启报价单报价试算", "0"),
    MAP_MODE_SETTING_TYPE("map_mode_setting_type", "客户地图颜色配置", ""),
    ENFORCE_PRICE_POLICY_PRIORITY("enforce_price_policy_priority", "是否强制执行价格政策优先级", "0"),
    WHETHER_FILTER_ORDER_SELECT_PRODUCT("whether_filter_order_select_product", "订单选择产品，产品选择页面，增加产品是否过滤配置", "0"),
    TENANT_WHETHER_FILTER_ORDER_SELECT_PRODUCT("tenant_whether_filter_order_select_product", "订单选择产品，产品选择页面，增加产品是否过滤配置, 租户级", "0"),
    DELIVERY_NOTE_STATUS("delivery_note_status", "开启发货单", "0"),
    ORDER_TO_QUOTE_DEFAULT_VALUE_COVER("order_to_quote_default_value_cover", "报价单转订单，收货人、收货人电话、收货人地址、仓库、默认值不覆盖映射值-灰度功能", "0"),
    CHANGE_BUSINESS_QUERY("change_business_query", "工商查询是否包含个体户（0-不勾选 1-勾选）", "0"),
    ALLOW_AFTER_ACTION_CHANGE("allow_after_action_change", "允许流程后动作更新成交状态和最后一次成交时间字段（0 不启用  1启用）", "0"),
    ALL_PRODUCT_DISPLAY_PROMOTION("all_product_display_promotion", "促销本品为所有产品时是否要显示促", "0"),

    /**
     * 0 未开启 1 开启中 2 已经开启3 开启失败
     */
    ACCOUNTS_RECEIVABLE_STATUS_SFA("accounts_receivable_status_sfa", "应收管理是否开启", "0"),
    ACCOUNTS_RECEIVABLE_STATUS("accounts_receivable_status", "应收管理是否开启", "0"),
    /**
     * amount 发票金额  count 开票数量 none 隐藏
     */
    INVOICE_SHOW_QUICK_OP_RULE("invoice_show_quick_op_rule", "开票明细快捷操作显示规则", "amount"),
    /**
     * 0 不显示  1 显示
     */
    INVOICE_SHOW_SUM_DATA("invoce_show_sum_data", "开票界面是否显示汇总信息", "1"),
    //公海、线索池回收规则最大规则数量
    POOL_RECYCLING_RULE_MAX_NUMBER("pool_recycling_rule_max_number", "公海、线索池回收规则最大规则数量", "30"),
    /**
     * 0 默认不开启此功能
     */
    ORDER_ENLARGE_EDIT_PRIVILEGE("order_enlarge_edit_privilege", "订单编辑权限扩大，不限于订单管理员和财务", "0"),
    PROMOTION_ORDER_RECORD_TYPE("promotion_order_record_type", "促销根据适配订单业务类型", ""),
    IGNORE_PRICE_BOOK_VALID_PERIOD("ignore_price_book_valid_period", "是否自定义匹配价目表有效期", "0"),
    MATCH_PRICE_BOOK_VALID_FIELD("match_price_book_valid_field", "匹配价目表有效期的字段", ""),
    /**
     * 0、子件产品跟随母件选择价目表
     * 1、子件产品按照客户适配的可售范围中价目表优先级带出
     */
    BOM_ADAPTATION_PRICE_LIST_RULES("bom_adaptation_price_list_rules", "子件适配价目表带出规则", "0"),
    /**
     * 业务函数存储
     */
    BIZ_FUNCTION("biz_function", "业务函数存储", ""),

    ASYNC_CREATE_ORDER("async_create_order", "订单异步化", "0"),

    /**
     * 默认原来逻辑：产品组合.价格(元)（原始）+sum(选中子产品.调整价格（最新）*数量)-sum(默认子产品.调整价格（之前）*数量（默认）)
     */
    BOM_PRICE_CALCULATION_CONFIGURATION("bom_price_calculation_configuration", "产品包价格计算公式", "0"),
    MODULE_SALE_CONTRACT("sale_contract", "销售合同", "0"),
    PARTNER_TREE_FIELDS("partner_tree_fields", "合作伙伴层级字段", "name"),
    NEW_OPPORTUNITY_TREE_FIELDS("new_opportunity_tree_fields", "商机2.0伙伴层级字段", "name"),
    /**
     * 项目或阶段是否开启系统自动计算完成度，默认关闭
     */
    CONFIG_PROJ_STAGE_AUTO_CALCULATE_COMPLETE("config_proj_stage_auto_calculate_complete", "自动计算项目阶段与项目的完成度", "0"),
    /**
     * 基于任务状态自动变更项目与项目阶段的状态，默认开启
     */
    PROJECT_BIZ_STATUS_EDIT("project_biz_status_edit", "自动变更项目与项目阶段的状态", "0"),
    /**
     * 项目详情按钮打开
     * <ul>
     * <li>0-详情页</li>
     * <li>1-甘特图页，默认值</li>
     * </ul>
     */
    PROJECT_DETAIL_REDIRECT("project_detail_redirect", "项目详情按钮打开", "1"),

    /**
     * 0. 不允许修改
     * 1. 允许修改（默认）
     */
    GANTT_ALLOW_UPDATE_DATE("gantt_allow_update_date", "甘特图页面允许更新日期", "1"),
    /**
     * 赠品费用分摊依据
     * price_book_price 价目表价格
     * product_price 产品档案价格
     */
    GIFT_AMORTIZE_BASIS("gift_amortize_basis", "赠品费用分摊依据", "price_book_price"),

    GIFT_ATTEND_AMORTIZE("gift_attend_amortize", "赠品参与分摊", "0"),

    MANUAL_GIFT("manual_gift", "手工赠品", "0"),

    REBATE("rebate", "返利", "0"),

    COUPON("coupon", "优惠券", "0"),

    LIMIT_MULTI_POLICY("limit_multi_policy", "多政策共同限量", "0"),
    DISCOUNT_ON_DISCOUNT("discount_on_discount", "折上折", "0"),
    MULTIPLE_OBJECT_PRICE_POLICY("multiple_object_price_policy", "跨对象促销", "0"),
    LIMIT_CRITICAL_VALUE("limit_critical_value", "限额限量临界值", "0"),
    /**
     * 产品分类，展示类型，tree or list
     * 1. tree
     * 2. list
     */
    CATEGORY_MODEL_TYPE("category_model_type", "产品分类展示形式", "2"),

    HOSPITAL_STATUS("hospital_status", "医疗数据开关", "0"),

    /**
     * 联系人负责人规则设置
     * 0 false; 1 true
     */
    CONTACT_OWNER_RULE_SETTING("contact_owner_rule_setting", "联系人负责人规则设置", "1"),

    /**
     * capsule、胶囊模式
     * tiled、平铺模式
     * last_level_tiled、末级平铺模式
     */
    MULTI_SPEC_DISPLAY_STYLE("multi_spec_display_style", "多规格商品选择产品样式", "capsule"),

    VIRTUAL_EXTENSION("virtual_extension", "商品/产品虚拟字段", "0"),

    AVAILABLE_RANGE_FILTER("available_range_filter", "可售范围支持过滤", "{\"status\":\"0\",\"filter_field\":\"\",\"filter_function\":\"\"}"),

    AVAILABLE_RANGE_FILTER_CONDITION("available_range_filter_condition", "可售范围过滤条件", "{\"filter_field\":\"\",\"filter_value\":\"\"}"),

    INPUT_CUSTOM_FIELDS("input_custom_fields", "移动端加车支持输入字段", "0"),

    INPUT_DISPLAY_FIELDS("input_display_fields", "移动端加车显示字段", ""),
    RECENT_ORDER("recent_order", "最近订购", "0"),

    /**
     * 是否展示-注销异常企业配置项
     * 0不展示
     * 1展示
     */
    BUSINESS_ALLOWCONFIG_NO_ABNORMAL("business_allowconfig_no_abnormal", "是否展示-注销异常企业配置项", "1"),

    /**
     * 注销异常企业配置项
     * 0不展示
     * 1展示
     */
    BUSINESS_NO_ABNORMAL("business_no_abnormal", "注销异常企业配置项", "0"),
    /**
     * 1：关闭
     * 0：没关闭
     * 新企业,，默认都是关闭
     */
    CLOSE_OLD_CATEGORY("close_old_category", "关闭了老的产品分类", "0"),
    CATEGORY_TOTAL_LIMIT("category_total_limit", "分类总数限制", "5000"),
    MULTI_UNIT_PRICE_BOOK("multi_unit_price_book", "多单位价目表", "0"),

    PROCUREMENT_TRANSFER_SETTING("procurement_transfer_setting", "招投标转换设置", "AccountObj"),

    /**
     * 招投标查询是否展示公告详情信息
     */
    PROCUREMENT_SEARCH_SETTING("procurement_search_setting", "招投标转换设置", "AccountObj"),


    DHT_SALES_ORDER_RECORD_TYPE("dht_sales_order_record_type", "订货通订单业务类型", ""),
    SHOPPING_MALL_MODE("shopping_mall_mode", "订货通商城模式开关", "1"),
    INIT_MASTER_DATA_APP("init_master_data_app", "开启客户主数据按钮", "0"),

    //新建编辑客户当客户名称与系统中已有客户名称重复时进行提示
    CHECK_ACCOUNT_NAME_DUPLICATED("check_account_name_duplicated", "新建编辑客户当客户名称与系统中已有客户名称重复时进行提示", "1"),
    GENERATE_ACCOUNT_RELATION_TREE_RULE("generate_account_relation_tree_rule", "客户树关系生成规则", "{\"config\":[{\"config_key\": \"select_largest\", \"config_value\": \"true\", \"is_enable\": false},{\"config_key\": \"select_total_largest\", \"config_value\": \"true\", \"is_enable\": false},{\"config_key\": \"select_branch\", \"config_value\": \"true\", \"is_enable\": false},{\"config_key\": \"min_percent\", \"config_value\": \"10\", \"is_enable\": true}]}"),

    GENERATE_RELATION_TREE_RULE_ACCOUNTOBJ("generate_relation_tree_rule_AccountObj", "客户树关系生成规则", "{\"config\":[{\"config_key\": \"select_largest\", \"config_value\": \"true\", \"is_enable\": false},{\"config_key\": \"select_total_largest\", \"config_value\": \"true\", \"is_enable\": false},{\"config_key\": \"select_branch\", \"config_value\": \"true\", \"is_enable\": false},{\"config_key\": \"min_percent\", \"config_value\": \"10\", \"is_enable\": true}]}"),
    GENERATE_ACCOUNT_RELATION_TREE_MATCH_RULE("generate_account_relation_tree_match_rule", "客户树关系生成客户主数据匹配规则", "[]"),
    /**
     * 价格政策开启后，可以开启手工改价，修改价目表价格
     * 0、不支持修改 默认值
     * 1、支持修改
     */
    ALLOW_EDIT_PRICE_BOOK_PRICE("allow_edit_price_book_price", "是否允许编辑价目表价格", "0"),

    /**
     * 改价模式
     * direct  直接改价, indirect 间接改价
     */
    CHANGE_PRICE_TYPE("change_price_type", "改价模式", "indirect"),

    //历史报价开关,默认开启
    QUOTE_HISTORY_PRICE("quote_history_price", "报价单历史报价", "1"),

    MUST_TRANSFER_BY_TYPE("must_transfer_by_type", "根据业务类型设置必转", ""),

    QYWX_CHANGE_OWNER_SYNC_OBJ("qywx_change_owner_sync_obj", "企业微信同步负责人配置", ""),

    /**
     * {"enable":1,"relate_object_name":"DemoObj","mapping_rule":"DemoRule"}
     *
     * @see <a href="url">https://wiki.firstshare.cn/pages/viewpage.action?pageId=202125887<a/>
     */
    QYWX_MULTI_ORG_RELATE_CREATE_CONFIG("qywx_multi_org_relate_create_config", "企业微信多组织关联创建配置", ""),

    CONTACT_MEMBER_RELATIONSHI_FUNC_SETTING("contact_member_relationship_func_setting", "联系人与成员关系图谱功能是否开启设置", "0"),

    CONTACT_MEMBER_RELATIONSHI_FUNC_ADDRESS_BOOK_SETTING("contact_member_relationship_func_addressBook_setting", "联系人与成员关系图谱中获取通讯录功能是否开启设置", "0"),

    WHETHER_FILTER_PRICE_BOOK_SELECT_PRODUCT("whether_filter_price_book_select_product", "包含本价目表已选产品", "0"),

    AVAILABLE_PRICE_BOOK("available_price_book", "可售价目表", "0"),

    PRICE_BOOK_PRODUCT_VALID_PERIOD("price_book_product_valid_period", "价目表明细支持有效期配置", "0"),

    PRICE_BOOK_PRODUCT_TIERED_PRICE("price_book_product_tiered_price", "价目表明细支持定义产品阶梯价", "0"),

    STRATIFIED_PRICING("stratified_pricing", "分层定价", "0"),

    PRICE_BOOK_REFORM("price_book_reform", "价目表改造", "0"),

    DHT_MULTI_LEVEL_ORDER("dht_multi_level_order", "是否开启多级订货", "0"),

    MULTI_SHOPPING_MALL("multi_shopping_mall", "配件商城", "0"),

    IGNORE_CHECK_CEILING_FLOOR_PRICE("ignore_check_ceiling_floor_price", "取消价目表明细浮动上下线强制校验", "0"),

    SEND_USER("send_user", "发送前端日志用户设置", "{\"allUser\":false,\"user\":[],\"filter\":[]}"),

    PAPER_COUPON("paper_coupon", "纸质券", "0"),

    MULTI_UNIT_MODE_CONFIG("multi_unit_mode_config", "多单位模式配置", "0"),

    MULTI_UNIT_SHOW_TYPE("multi_unit_show_type", "多单位显示样式", "0"),

    MULTI_UNIT_LINKAGE("multi_unit_linkage", "可填写的单位数量联动效果", "0"),

    REBATE_POLICY_SOURCE("rebate_policy_source", "返利产生来源", "[{\"api_name\":\"SalesOrderObj\",\"api_name__r\":\"销售订单\",\"account_filed\":\"account_id\",\"detail_api_name\":\"SalesOrderProductObj\",\"detail_api_name__r\":\"订单产品\",\"used\":true,\"type\":\"system\"},{\"api_name\":\"PaymentObj\",\"api_name__r\":\"回款\",\"account_filed\":\"account_id\",\"detail_api_name\":\"OrderPaymentObj\",\"detail_api_name__r\":\"回款明细\",\"used\":false,\"type\":\"system\"}]"),

    /**
     * 0-不启用
     * 1-启用
     */
    REBATE_PRODUCT_RANGE_SHELVES("rebate_product_range_shelves", "产品返利启用过滤上架产品，和可售范围", "0"),


    // 840 开启合作伙伴地址
    PARTNER_ADDRESS("partner_address", "开启合作伙伴地址", "0"),
    // 870 合作伙伴：分子公司
    PARTNER_SUBSIDIARY("partner_subsidiary", "合作伙伴分子公司", "0"),

    /**
     * 元气，组合规则取消不匹配其他行
     */
    NOT_MATCH_GROUP_OTHER_ROW("not_match_group_other_row", "组合规则取消不匹配其他行", "0"),

    /**
     * 明细促销按照优先级进行匹配。即组合和单品促按照价格政策优先级执行
     */
    DETAIL_MATCH_AS_PRIORITY("detail_match_as_priority", "明细促销按照优先级进行匹配", "0"),

    /**
     * 拆分策略为：多条或单条
     * single: 单条
     * many: 多条
     */
    PLOY_MODE("ploy_mode", "策略拆分模式", "many"),
    /**
     * 价格政策规则每满条件支持最大数量
     */
    PRICE_POLICY_CYCLE_CONDITION_MAXIMUM("price_policy_cycle_condition_maximum", "价格政策每满条件最大数量", "5"),

    /**
     * 价格政策使用客户条件支持最大条数
     */
    PRICE_POLICY_ACCOUNT_CONDITION_MAXIMUM("price_policy_account_condition_maximum", "价格政策使用客户条件支持最大条数", "5"),

    /**
     * 校验价格政策是否调用match和有分摊没有价格政策
     */
    CHECK_PRICE_POLICY_MATCH("check_price_policy_match", "校验价格政策是否调用match", "0"),
    /**
     * 校验价格政策
     */
    CHECK_PRICE_POLICY("check_price_policy", "校验价格政策", "0"),

    /**
     * 价格政策校验跳过配置
     * action+recordType+function+peerName+code,action+recordType+function+peerName+code
     */
    SKIP_CHECK_PRICE_POLICY("skip_check_price_policy", "价格政策校验跳过配置", ""),

    /**
     * 1、支持
     * 0、不支持（default）
     */
    PRICE_POLICY_DEPT_NOT_THIS("price_policy_dept_not_this", "价格政策适用客户的部门条件支持选非本部门", "0"),

    /**
     * 售中配置引导页时，设置的行业编码
     */
    BOOT_CONFIG_INDUSTRY_CODE("boot_config_industry_code", "配置引导页信息存储KEY", "COMMON"),

    /**
     * 是否开启账户授权
     * 2-开启
     */
    ACCOUNT_AUTH("is_account_auth_enable", "是否开启账户授权", "0"),
    /**
     * 是否开启账户校验规则
     * 2-开启
     */
    ACCOUNT_CHECK_ENABLE("is_account_check_enable", "是否开账户校验规则", "0"),
    OPEN_MARKETING_TAG_OBJECTS("open_marketing_tag_objects", "开启营销通标签对象", "[]"),

    CONTRACT_CONSTRAINT_MODE("contract_constraint_mode", "销售合同约束模式", "0"),
    CONTRACT_CONSTRAINT_PRICEBOOK_AVAILABLE_RANGE("contract_constraint_pricebook_available_range", "销售合同价目表可售范围模式", "0"),
    SALES_PRICE_DIFF("sales_price_diff", "销售订单单价校验允许误差", "0"),
    PROGRESSIVE_FIELD_NUM("progressive_field_num", "差额累进字段个数", "3"),
    SFA_ADVANCED_FORMULA_OPEN("sfa_advanced_formula_open", "高级公式", "0"),

    PRM_PRODUCT_RANGE("prm_product_range", "代理通中下发的产品筛选方式", "product_range"),

    IS_OPEN_QUOTER("is_open_quoter", "是否启用报价器", "0"),

    BOM_DUPLICATE_CHECK("bom_duplicate_check", "BOM查重校验", "0"),
    BOM_NODE_SHARE("bom_node_share", "BOM子件分摊", "0"),
    SKIP_BOM_NUMBER("skip_bom_number", "跳过bom数量校验", "0"),

    NOT_SHOW_BOM("not_show_bom", "选产品组合，不弹出配置页面", "0"),

    LATEST_VERSION_BOM("latest_version_bom", "产品组合最新版本开关", "1"),

    BOM_DETAIL_LAYOUT_CLOSED("bom_detail_layout_closed", "详情页是否收起BOM结构", "0"),

    CPQ_UI_MODE("cpq_ui_mode", "产品选配模式", "0"),

    NEW_OPPORTUNITY_ADD_SALES_PROCESS_TRIGGER_MODE("NewOpportunityObj.add.sales_process.trigger.mode", "新增商机2.0时销售流程触发模式", "0"),

    OPENING_BALANCE_DATE("opening_balance_date", "期初日期校验", ""),

    OPENING_BALANCE_FORCE_CHECK("opening_balance_force_check", "期初日期强制校验", "0"),

    INVOICE_ORDER_BINDING_STATUS("invoice_order_binding_status", "开票与订单解耦(1不解耦，0解耦)", "1"),
    REFUND_ORDER_BINDING_STATUS("refund_order_binding_status", "退款与订单解耦(1不解耦，0解耦)", "1"),
    PAYMENT_PLAN_ORDER_BINDING_STATUS("payment_plan_order_binding_status", "回款计划与订单解耦(1不解耦，0解耦)", "1"),

    INVOICE_SUPPORT_NEGATIVE_AND_ZERO("invoice_support_negative_and_zero", "开票允许为负数和0", "0"),

    SFA_OPEN_ACCOUNTMAINDATA("sfa_open_accountMainData", "开通多组织标识", ""),
    USE_SHOP_CATEGORY("use_shop_category", "使用商城分类", "0"),
    DEFAULT_CONDITION_BASIS_API_NAME("default_condition_basis_api_name", "默认条件依据Api", ""),
    MATCH_ENTERPRISE_RULE_CONFIG_KEY("generate_relation_tree_enterprise_match_rule", "生成客户树匹配企业库", ""),
    ORDER_ADDR_SUPPORT_SEARCH("order_addr_support_search", "销售订单创建页面（PC端操作），地址组件中支持筛选能力", "0"),
    GIFT_FIXED_ATTRIBUTE("gift_fixed_attribute", "赠品指定属性字段配置", ""),
    IS_OPEN_RECEIVED_PAYMENT("is_open_received_payment", "是否开启到款", "0"),
    ORDER_PAYMENT_REQUIRED("order_payment_required", "回款明细是否必填配置", "{\"status\":\"0\",\"recordTypeList\":[]}"),
    IS_OPEN_ORDER_PAYMENT_MULTI_SOURCE("is_open_order_payment_multi_source", "是否开启回款明细多来源", "0"),
    ORDER_PAYMENT_MAPPING_RULE("order_payment_mapping_rule", "回款明细字段映射", "[]"),
    SETTLEMENT_DETAIL_MAPPING_RULE("settlement_detail_mapping_rule", "结算单明细字段映射", "[]"),
    ADD_ACCOUNT_MEANWHILE_ADD_ADDR("add_account_meanwhile_add_addr", "新建客户时同步生成客户地址的主地址", "1"),
    ACCOUNT_AND_ADDR_SYNC_UPD_LOCATION("account_and_addr_sync_upd_location", "客户地址的主地址与客户的地区定位同步更新", "1"),
    IS_OPEN_ACCOUNT_ADDR_CONFIG("is_open_account_addr_config", "是否开启客户地址配置", "0"),
    IS_OPEN_AR_QUICK_ADD("is_open_ar_quick_add", "创建应收单，增加选配界面辅助完成创建", "0"),
    CREATE_AR_BY_OBJECTS("create_ar_by_objects", "创建应收单，由哪个对象辅助选配", ""),
    IS_OPEN_AUTO_MATCH("is_open_auto_match", "是否开启自动核销", "0"),
    AUTO_MATCH_RULE("auto_match_rule", "自动核销匹配规则", "[]"),
    IS_OPEN_INCREMENTAL_PRICING("is_open_incremental_pricing", "是否开启属性增量定价", "0"),
    ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY("accounts_receivable_auto_match_button_switch_key", "快消自动核销", "0"),
    UPGRADE_NEW_OPPORTUNITY_CONTACT_ATLAS("upgrade_new_opportunity_contact_atlas", "商机联系人关系图谱对象化", "0"),
    IS_OPEN_PERIODIC_ACCOUNTS_RECEIVABLE("is_open_periodic_accounts_receivable", "启用自动应收", "0"),
    PERIODIC_ACCOUNTS_RECEIVABLE_TYPE("periodic_accounts_receivable_type", "应收创建方式", "{\"Once\":\"0\"}"),
    /**
     * @see <a href="https://wiki.firstshare.cn/pages/viewpage.action?pageId=*********">940 需求</a>
     */
    SYNC_CHANGE_PARTNER_OWNER("sync_change_partner_owner", "允许更换外部负责人", "0"),
    CUSTOMER_PROFILE_AGENT("customer_profile_agent", "是否开启客户画像agent", "0"),
    TARGET_INDUSTRIES("target_industries", "公司目标行业", ""),
    PROSPEROUS_INDUSTRIES("prosperous_industries", "公司景气行业", ""),
    TARGET_PRODUCTS("target_products", "公司目标产品", ""),
    SERVICE_MODELS("service_models", "公司服务形态", ""),

    CONTRACT_CONNECTOR("contract_connector", "合同连接器", "[]"),
    OUTER_CONTRACT_MAPPING("outer_contract_mapping", "外部合同映射", "[]"),
    PERIODIC_PRODUCT("periodic_product", "周期性产品", "0"),
    ENABLE_PROJECT_SETTLEMENT("enable_project_settlement", "启用项目结算", "0"),
    ENABLE_EXPENSE_COST_COLLECTION("enable_expense_cost_collection", "启用费用成本归集", "0"),
    INVOICE_LINES_MULTI_SOURCE("invoice_lines_multi_source", "开票多来源", "0"),
    INVOICE_LINES_MAPPING_RULE("invoice_lines_mapping_rule", "开票明细字段映射", "[]"),
    INVOICE_LINES_REQUIRED("invoice_lines_required", "开票明细是否必填开关", "{\"status\":\"0\",\"recordTypeList\":[]}"),
    LOYALTY_SWITCH("sfa_loyalty_init", "会员开关", "false"),
    LOYALTY_PLUGIN_SWITCH_SALES_ORDER("sfa_loyalty_plugin_switch_apply_SalesOrderObj", "会员插件/销售订单", "false"),
    LOYALTY_PLUGIN_PARTNER_SWITCH_SALES_ORDER("sfa_loyalty_plugin_partner_switch_apply_SalesOrderObj", "会员插件/销售订单/合作伙伴", "false"),
    IS_OPEN_ADDITIONAL_CONTRACT("is_open_additional_contract", "是否启用合同分层结构（0-不启用 1-启用）", "0"),
    OPEN_CHANNEL_ACCESS("open_channel_access", "开启渠道准入", "close"),
    SALE_CONTRACT_RECORD_TYPE_MAPPING("sale_contract_record_type_mapping", "销售合同业务类型映射", "0"),
    SFA_MANUAL_CONVERT_WECHAT_SESSION_TO_ACTIVE_RECORD("sfa_manual_convert_wechat_session_to_active_record", "是否启用企微会话存档手动留存销售记录（0-不启用 1-启用）", "0"),
    SFA_AUTO_CONVERT_WECHAT_SESSION_TO_ACTIVE_RECORD ("sfa_auto_convert_wechat_session_to_active_record", "是否启用企微会话存档自动留存销售记录（0-不启用 1-启用）", "0"),
    SFA_ACTIVITY_SET_AI_SWITCH("sfa_activity_set_ai_switch", "activity设置获取ai建议开关", "false"),
    INTERACTION_STRATEGY_LIMIT_TOTAL("interaction_strategy_limit_total", "互动策略最大数量", "50"),
    DONT_SAVE_REAL_TIME_RECORD_AUDIO("dont_save_real_time_record_audio", "不留存实时录音的音频", "0"),
    ENABLE_DUPLICATE_ACCOUNT_FILTER_PARTNERS("enable_duplicate_account_filter_partners", "线索转客户，疑似客户基于合作伙伴过滤", "0"),
    IS_OPEN_CONTRACT_PROGRESS("is_open_contract_progress", "合同履约进度管理", "0"),
    BOM_FIRST_COLUMN_WITH_LAYOUT("bom_first_column_with_layout", "BOM首列走布局", "0"),
    IS_OPEN_PAYMENT_MULTI_SOURCE_FIELD("is_open_payment_multi_source_field", "是否开启多来源字段", "0"),
    IS_OPEN_SALES_AGENT("is_open_sales_agent", "是否开启销售Agent", "0"),
    SFA_ACTIVITY_RECORD_AUDIO_LAYOUT("sfa_activity_record_audio_layout", "activity开启录音界面布局", "0"); // 0-默认 1-前端展示布局必填字段|后端不跳过必填校验


    private final String key;
    private final String defaultValue;
    private final String label;


    public static final Map<String, ConfigType> CONFIG_TYPE_MAP;

    static {
        Map<String, ConfigType> map = Maps.newHashMap();
        for (ConfigType configType : ConfigType.values()) {
            map.put(configType.getKey(), configType);
        }
        CONFIG_TYPE_MAP = ImmutableMap.copyOf(map);
    }

    ConfigType(String key, String label, String defaultValue) {
        this.key = key;
        this.defaultValue = defaultValue;
        this.label = label;
    }

    public String getKey() {
        return this.key;
    }

    public String getLabel() {
        return this.label;
    }

    public String getDefaultValue() {
        return this.defaultValue;
    }

    public String getValue(String value) {
        if (this.key.equals("41")) {
            if (value.split(",").length == 3) {
                value += ",0";
            }
            return value;
        } else {
            return value;
        }
    }

    public static ConfigType getConfigType(String key) {
        ConfigType configType = ConfigType.CONFIG_TYPE_MAP.get(key);
        return null == configType ? ConfigType.NONE : configType;
    }

    public static List<String> getAllKeys() {
        List<String> rst = Lists.newArrayList();
        ConfigType[] values = ConfigType.values();
        if (values != null && values.length > 0) {
            for (ConfigType value : values) {
                rst.add(value.getKey());
            }
        }
        return rst;
    }
}
