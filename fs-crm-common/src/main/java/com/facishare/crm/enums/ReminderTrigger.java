package com.facishare.crm.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.constants.PrmI18NConstants.PRM_PARAM_PROTOCOL_ERROR;

/**
 * Created by Sundy on 2024/10/9 17:31
 */
@Getter
public enum ReminderTrigger {
    MANUAL("manual"),
    AUTO("auto");
    private final String trigger;

    ReminderTrigger(String trigger) {
        this.trigger = trigger;
    }

    public static ReminderTrigger from(String trigger) {
        for (ReminderTrigger e : values()) {
            if (e.trigger.equals(trigger)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_PARAM_PROTOCOL_ERROR, "reminder_trigger"));
    }

    public static ReminderTrigger find(String trigger, ReminderTrigger defaultValue) {
        for (ReminderTrigger e : values()) {
            if (e.trigger.equals(trigger)) {
                return e;
            }
        }
        return defaultValue;
    }

    public static ReminderTrigger find(String trigger) {
        return find(trigger, null);
    }
}