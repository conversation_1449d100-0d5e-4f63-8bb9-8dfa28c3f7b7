package com.facishare.crm.constants;


import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.ImmutableSet;

/**
 * 优惠券常量
 *
 * <AUTHOR>
 * @IgnoreI18nFile
 */
public interface RebateConstants {
    String REBATE_API_NAME = "RebateObj";
    String REBATE_DETAIL_API_NAME = "RebateDetailObj";
    String REBATE_RULE_API_NAME = "RebateRuleObj";
    String REFERENCE_SOURCE_LABEL = "返利单";
    String REBATE_RULE_REFERENCE_SOURCE_LABEL = "使用返利规则";
    String HAS_PRODUCT_RANGE = "1";
    String REBATE_RANGE = "rebate_range";
    String SOURCE = "source";
    String METADATA = "metadata";
    String UN_USE_EXPIRE_DATA ="charge_off_data";

    /**
     * TPM调用传的peerName
     */
    String TPM_REST = "fs-crm-fmcg-service";
    /**
     * 编辑时可以修改字段
     */
    ImmutableSet<String> EDIT_CAN_MODIFY_FIELD = ImmutableSet.of(
            RebateField.SUM_AMOUNT.getApiName(),
            RebateField.START_DATE.getApiName(),
            RebateField.END_DATE.getApiName(),
            RebateField.REMARK.getApiName(),
            //余量字段不可编辑，但是需要进行保存，因此设置可编辑字段
            RebateField.UNUSED_AMOUNT.getApiName());

    /**
     * 插入导入可以不支持字段
     */
    ImmutableSet<String> INSERT_IMPORT_CAN_NOT_SUPPORT_FIELD = ImmutableSet.of(
            IObjectData.NAME,
            RebateField.REBATE_POLICY_ID.getApiName(),
            RebateField.REBATE_POLICY_RULE_ID.getApiName(),
            RebateField.REBATE_POLICY_SOURCE_AMOUNT.getApiName(),
            RebateField.REBATE_POLICY_EXPECT_AMOUNT.getApiName(),
            RebateField.REBATE_POLICY_BATCH.getApiName(),
            RebateField.USED_AMOUNT.getApiName(),
            RebateField.PRODUCT_CONDITION_TYPE.getApiName(),
            RebateField.PRODUCT_CONDITION_CONTENT.getApiName(),
            RebateField.PRODUCT_CONDITION_RULE.getApiName(),
            RebateField.PRODUCT_RANGE.getApiName(),
            RebateField.ENTER_INTO_ACCOUNT.getApiName(),
            RebateField.USED_OBJECT_API_NAME.getApiName(),
            RebateField.FUND_ACCOUNT_ID.getApiName(),
            RebateField.UNUSED_AMOUNT.getApiName(),
            RebateField.REBATE_SOURCE_ID.getApiName(),
            RebateField.REBATE_SOURCE_API_NAME.getApiName()
    );
    String AMORTIZE_DATA_KEY = "rebate_amortize_data_key";
    String REBATE_USE_LESS_KEY = "rebate_use_less_key";

    enum RebateField {
        TOPIC("topic", "主题"),
        USED_OBJECT_API_NAME("used_object_api_name", "返利使用对象"),
        ACCOUNT_ID("account_id", "客户名称"),
        ACTIVE_STATUS("active_status", "启用状态"),
        REBATE_TYPE("rebate_type", "返利类型"),
        USE_TYPE("use_type", "使用方式"),
        SUM_AMOUNT("sum_amount", "总量"),
        USED_AMOUNT("used_amount", "已使用量"),
        UNUSED_AMOUNT("unused_amount", "余量"),
        START_DATE("start_date", "有效起期"),
        END_DATE("end_date", "有效止期"),
        REMARK("remark", "返利单备注"),
        PRODUCT_CONDITION_TYPE("product_condition_type", "产品条件类型"),
        PRODUCT_CONDITION_CONTENT("product_condition_content", "产品条件数据"),
        PRODUCT_CONDITION_RULE("product_condition_rule", "产品条件规则"),
        PRODUCT_RANGE("product_range", "产品返利范围"),
        PRODUCT_RANGE_TYPE("product_range_type", "产品返利范围类型"),
        REBATE_POLICY_ID("rebate_policy_id", "返利产生政策"),
        REBATE_POLICY_RULE_ID("rebate_policy_rule_id", "返利产生规则"),
        REBATE_POLICY_SOURCE_AMOUNT("rebate_policy_source_amount", "返利产生原值"),
        REBATE_POLICY_EXPECT_AMOUNT("rebate_policy_expect_amount", "预计返利"),
        FUND_ACCOUNT_ID("fund_account_id", "入账账户"),
        ENTER_INTO_ACCOUNT("enter_into_account", "是否入账"),
        TPM_ACTIVITY_ID("tpm_activity_id", "关联活动"),
        TPM_DEALER_ACTIVITY_COST_ID("tpm_dealer_activity_cost_id", "费用核销单"),
        REBATE_SOURCE_API_NAME("rebate_source_api_name", "返利来源"),
        REBATE_SOURCE_ID("rebate_source_id", "返利来源id"),
        REBATE_POLICY_BATCH("rebate_policy_batch", "返利产生批次");

        private final String filedApiName;

        RebateField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }

    /**
     * 规则内容字段
     */
    enum RuleContentField {
        CONDITION("condition", "返利政策条件"),
        MASTER_CONDITION("master_condition", "条件"),
        CALCULATE_TYPE("calculate_type", "计算类型"),
        CYCLE_INFO("cycle_info", "每满信息"),
        EXPRESSIONS("expressions", "表达式"),
        OBJECT_API_NAME("object_api_name", "对象apiName"),
        OBJECT_API_NAME__S("object_api_name__s", "对象名称");

        private final String filedApiName;

        RuleContentField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }

    /**
     * 执行类型
     *
     * <AUTHOR>
     * @date 2021/12/23
     */
    enum ExecuteType {
        CALCULATE("CALCULATE", "计算方式"),
        CALCULATE_PRO("CALCULATE_PRO", "高级计算公式"),
        CONSTANT("CONSTANT", "固定值");

        private final String filedApiName;

        ExecuteType(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }


    /**
     * 计算类型
     *
     * <AUTHOR>
     * @date 2021/12/23
     */
    enum CalculateType {
        CYCLE("CYCLE", "每满计算"),
        EXPRESSION("EXPRESSION", "全量计算");

        private final String filedApiName;

        CalculateType(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }

    /**
     * 产品状态类型
     */
    enum ProductConditionType {
        CONDITION("CONDITION", "指定范围"),
        FIXED("FIXED", "指定产品"),
        ALL("ALL", "全部");

        private final String value;

        ProductConditionType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 启用状态状态
     */
    enum ActiveStatus {
        ENABLE("enable", "启用"),
        DISABLE("disable", "(未启用");
        private final String value;

        ActiveStatus(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 返利类型
     */
    enum RebateType {
        MONEY("Money", "金额返利"),
        PRODUCT("Product", "产品返利");


        private final String value;

        RebateType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 使用类型
     */
    enum UseType {
        QUANTITY("Quantity", "按数量返"),
        AMOUNT("Amount", "按金额返"),
        DISCOUNT("Discount", "分摊折价"),
        CASH("Cash", "冲抵回款");
        private final String value;

        UseType(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 生活状态
     */
    enum LifeStatus {
        INEFFECTIVE("ineffective", "未生效"),
        UNDERREVIEW("under_review", "审核中"),
        NORMAL("normal", "正常"),
        INCHANGE("in_change", "变更中"),
        INVALID("invalid", "作废");

        private final String code;

        LifeStatus(String code, String statusName) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }

    /**
     * 返利明细
     */
    enum RebateDetailField {

        SOURCE_ID("source_id", "返利单或者优惠券id"),
        SOURCE_OBJECT_API_NAME("source_object_api_name", "返利单或者优惠券ApiName"),
        OBJECT_DATA_ID("object_data_id", "关联id"),
        OBJECT_API_NAME("object_api_name", "关联对象ApiName"),
        USED_AMOUNT("used_amount", "使用金额"),
        BIZ_STATUS("biz_status", "明细状态");

        private final String filedApiName;

        RebateDetailField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }

    /**
     * 明细状态
     */
    enum BizStatus {
        EFFECTIVE("Effective", "生效"),
        NOTEFFECTIVE("NotEffective", "未生效)");
        private final String value;

        BizStatus(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 使用返利规则
     */
    enum RebateRuleField {
        ACTIVE_STATUS("active_status", "启用状态"),
        SOURCE_OBJECT_API_NAME("source_object_api_name", "使用对象"),
        RULE_CONTENT("rule_content", "规则内容"),
        RULE_TYPE("rule_type", "规则类型"),
        PRODUCT_RANGE_TYPE("product_range_type", "产品范围"),
        PRIORITY("priority", "优先级"),
        REBATE_CONDITION("rebate_condition", "返利单条件");

        private final String filedApiName;

        RebateRuleField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }

    enum SourceObjectApiName {
        SALES_ORDER_OBJ("SalesOrderObj", "订单");
        private final String value;

        SourceObjectApiName(String value, String label) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 插件主对象字段 ，公用字段使用 com.facishare.crm.constants.CouponConstants.PluginField
     */
    class PluginField {
        public static final String REBATE_RULE_ID = "rebate_rule_id";
        public static final String PRODUCT_REBATE_RULE_ID = "product_rebate_rule_id";
        public static final String RANGE_REBATE_RULE_IDS = "range_rebate_rule_ids";
    }

}
