<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.facishare</groupId>
        <artifactId>fs-crm-sfa</artifactId>
        <version>9.6.0-SNAPSHOT</version>
    </parent>

    <artifactId>fs-crm-provider-sfa</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-management</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.athaydes</groupId>
                    <artifactId>spock-reports</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-domain-sfa</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-prm</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-master-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-project-manage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-loyalty</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-activity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-profile</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>