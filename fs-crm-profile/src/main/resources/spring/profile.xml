<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd">
    <context:component-scan base-package="com.facishare.crm.profile"/>
    <context:annotation-config/>

    <bean id="profileProducer" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start" destroy-method="close">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="crm-feature-profile-producer"/>
    </bean>
</beans>