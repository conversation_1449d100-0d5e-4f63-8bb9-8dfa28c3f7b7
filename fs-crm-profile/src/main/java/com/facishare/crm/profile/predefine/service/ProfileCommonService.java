package com.facishare.crm.profile.predefine.service;

import com.facishare.crm.profile.predefine.ProfilePredefineObject;
import com.facishare.crm.sfa.predefine.service.SFALicenseService;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/6/25
 */
@Slf4j
@Service
public class ProfileCommonService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private SFALicenseService sfaLicenseService;

    private static final String PROFILE_QUOTA_KEY = "sales_portrait_agent_100_portraits_quantity_limit";

    /**
     * 总额度
     */
    public int getProfileQuota(String tenantId) {
        return sfaLicenseService.getQuotaByModule(tenantId, PROFILE_QUOTA_KEY);
    }

    /**
     * 剩余额度
     */
    public int getRemainProfileCount(String tenantId) {
        int quota = getProfileQuota(tenantId);
        int usedCount = getUsedProfileCount(tenantId);
        log.info("getRemainProfileCount tenantId:{} quota:{} usedCount:{}", tenantId, quota, usedCount);
        return Math.max(quota - usedCount, 0);
    }

    /**
     * 已使用额度
     */
    public int getUsedProfileCount(String tenantId) {
        SearchTemplateQuery searchTemplateQuery = SoCommonUtils.buildSearchTemplateQuery(1);
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, DBRecord.IS_DELETED, "0");
        Integer count = serviceFacade.countObjectDataFromDB(tenantId, ProfilePredefineObject.SALES_COACH_RECORD.getApiName(), searchTemplateQuery);
        return count == null ? 0 : count;
    }
}
