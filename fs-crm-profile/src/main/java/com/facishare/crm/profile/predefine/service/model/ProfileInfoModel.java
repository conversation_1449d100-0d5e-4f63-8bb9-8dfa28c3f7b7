package com.facishare.crm.profile.predefine.service.model;

import java.util.List;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/5/12
 */
public interface ProfileInfoModel {

    @Data
    class Arg {

        private String objectApiName;

        private String objectId;

        private String methodologyId;

        private String methodologyType;
    }

    @Builder
    @Data
    class Result {

        private Integer stageLevel;

        private ObjectDataDocument profile;

        private List<ObjectDataDocument> nodeInstance;

        private List<ObjectDataDocument> profileItemScore;
    }

}
