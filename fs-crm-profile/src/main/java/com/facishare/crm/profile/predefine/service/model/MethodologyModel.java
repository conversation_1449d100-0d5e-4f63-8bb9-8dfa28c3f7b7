package com.facishare.crm.profile.predefine.service.model;

import java.util.List;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/5/20
 */
public interface MethodologyModel {

    @Data
    class Arg {

        private String objectApiName;
    }

    @Builder
    @Data
    class Result {

        private List<ObjectDataDocument> methodology;
    }
}
