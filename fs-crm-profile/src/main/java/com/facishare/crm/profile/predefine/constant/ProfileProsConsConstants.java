package com.facishare.crm.profile.predefine.constant;

/**
 * 画像优劣势
 * 
 * <AUTHOR>
 * @since 2025/5/20
 */
public interface ProfileProsConsConstants {
    /**
     * 画像
     */
    String PROFILE_ID = "profile_id";
    /**
     * 维度
     */
    String FEATURE_DIMENSION_ID = "feature_dimension_id";
    /**
     * 类型
     */
    String TYPE = "type";
    enum Type {
        /**
         * 优势
         */
        PROS("pros"),
        /**
         * 劣势
         */
        CONS("cons"),
        /**
         * 风险
         */
        RISK("risk")
        ;

        private final String value;

        public String getValue() {
            return value;
        }

        Type(String value) {
            this.value = value;
        }
    }
    /**
     * 范围
     */
    String RANGE_TYPE = "range_type";
    enum RangeType {
        /**
         * 维度
         */
        DIMENSION("dimension"),
        /**
         * 画像
         */
        PROFILE("profile");

        private final String value;

        public String getValue() {
            return value;
        }

        RangeType(String value) {
            this.value = value;
        }
    }
    /**
     * 信息
     */
    String INFORMATION = "information";
    /**
     * 是否采纳
     */
    String IS_ACCEPT = "is_accept";
    /**
     * 是否忽略
     */
    String IS_IGNORE = "is_ignore";
    /**
     * 序号
     */
    String SEQ = "seq";
}