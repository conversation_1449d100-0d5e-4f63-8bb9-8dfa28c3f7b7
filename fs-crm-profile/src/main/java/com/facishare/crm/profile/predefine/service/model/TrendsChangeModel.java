package com.facishare.crm.profile.predefine.service.model;

import java.util.List;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/5/23
 */
public interface TrendsChangeModel {

    @Data
    class Arg {

        private String objectApiName;

        private String objectId;

        private String methodologyId;

        private String methodologyType;

        private String featureDimensionId;
    }

    @Builder
    @Data
    class Result {

        private List<ObjectDataDocument> trendsChange;
    }
}
