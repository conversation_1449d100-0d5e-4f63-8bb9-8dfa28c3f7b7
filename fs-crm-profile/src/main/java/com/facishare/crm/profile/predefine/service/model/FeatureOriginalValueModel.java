package com.facishare.crm.profile.predefine.service.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/5/27
 */
public interface FeatureOriginalValueModel {

    @Data
    class Arg {

        private String profileId;

        private String featureDimensionId;

        private String methodologyType;

        private String methodologyInstanceId;

        private String objectApiName;

        private String objectId;
    }

    @Builder
    @Data
    class Result {

        private List<FeatureTriggerValue> featureTriggerValueList;

    }

    @Data
    class FeatureTriggerValue {

        private String featureDimensionId;

        private String featureId;

        private String featureName;

        private String type;

        private List<Map<String, Object>> fieldDescribes;

        private String originalValue;

        private ObjectDataDocument originalValueDocument;

    }
}
