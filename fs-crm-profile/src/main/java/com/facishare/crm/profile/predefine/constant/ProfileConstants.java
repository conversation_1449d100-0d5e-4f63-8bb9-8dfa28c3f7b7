package com.facishare.crm.profile.predefine.constant;

/**
 * 画像
 * 
 * <AUTHOR>
 * @since 2025/5/12
 */
public interface ProfileConstants {
    /**
     * 方法论实例
     */
    String METHODOLOGY_INSTANCE_ID = "methodology_instance_id";
    /**
     * 方法论
     */
    String METHODOLOGY_ID = "methodology_id";
    /**
     * 线索
     */
    String LEAD_ID = "lead_id";
    /**
     * 客户
     */
    String ACCOUNT_ID = "account_id";
    /**
     * 商机
     */
    String OPPORTUNITY_ID = "opportunity_id";
    /**
     * 类型
     */
    String TYPE = "type";
    /**
     * 综合分
     */
    String INTEGRATED_SCORE = "integrated_score";
    /**
     * 阶段总分
     */
    String PHASE_CUMULATIVE_SCORE = "phase_cumulative_score";
    /**
     * 计算时间
     */
    String CALC_TIME = "calc_time";
    /**
     * 是否最新画像
     */
    String IS_LATEST = "is_latest";
    /**
     * 最后修改时间
     */
    String LAST_MODIFIED_TIME = "last_modified_time";
    /**
     * 当前节点
     */
    String CURRENT_NODE_ID = "current_node_id";
    /**
     * 下一节点
     */
    String NEXT_NODE_ID = "next_node_id";
    /**
     * 当前节点分数
     */
    String CURRENT_SCORE = "current_score";
    /**
     * 趋势总结
     */
    String TREND_SUMMARY = "trend_summary";

    /**
     * 综合分变化
     */
    String INTEGRATED_SCORE_CHANGE = "integrated_score_change";
}