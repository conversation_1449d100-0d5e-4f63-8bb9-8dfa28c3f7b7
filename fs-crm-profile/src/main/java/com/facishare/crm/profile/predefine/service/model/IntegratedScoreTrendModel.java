package com.facishare.crm.profile.predefine.service.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/13
 */
public interface IntegratedScoreTrendModel {

    @Data
    class Arg {

        private String methodologyId;

        private String methodologyType;

        private String objectApiName;

        private String objectId;

        private Long startDate;

        private Long endDate;
    }

    @Builder
    @Data
    class Result {

        private List<ObjectDataDocument> integratedScoreTrend;
    }
}
