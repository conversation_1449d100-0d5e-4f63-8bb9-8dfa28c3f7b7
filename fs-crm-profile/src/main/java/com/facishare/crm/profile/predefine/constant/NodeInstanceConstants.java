package com.facishare.crm.profile.predefine.constant;

/**
 * 节点实例常量
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
public interface NodeInstanceConstants {
	/**
     * API名称
     */
	String API_NAME = "NodeInstanceObj";
	/**
     * 方法论
     */
	String METHODOLOGY_ID = "methodology_id";
	/**
     * 方法论实例
     */
	String METHODOLOGY_INSTANCE_ID = "methodology_instance_id";
	/**
     * 关联对象
     */
	String OBJECT_API_NAME = "object_api_name";
	/**
     * 对象id
     */
	String OBJECT_ID = "object_id";
	/**
     * 流程节点
     */
	String NODE_ID = "node_id";
	/**
     * 节点完成状态
     */
	String STATUS = "status";
	enum StatusType {
		/**
		 * 未启动
		 */
		NOT_STARTED("0"),
		/**
		 * 进行中
		 */
		PROGRESS("1"),
		/**
		 * 完成
		 */
		COMPLETED("2");

		private final String status;

		public String getStatusType() {
			return status;
		}

		StatusType(String status) {
			this.status = status;
		}
	}
	/**
     * 负责人所在部门
     */
	String OWNER_DEPARTMENT = "owner_department";
	/**
     * 相关团队
     */
	String RELEVANT_TEAM = "relevant_team";
	/**
     * 节点级别
     */
	String LEVEL = "level";
	enum LevelType {
		/**
		 * 一级
		 */
		ONE("1"),
		/**
		 * 二级
		 */
		TWO("2"),
		/**
		 * 三级
		 */
		THREE("3"),
		/**
		 * 四级
		 */
		FOUR("4"),
		/**
		 * 五级
		 */
		FIVE("5"),
		/**
		 * 六级
		 */
		SIX("6");

		private final String level;

		public String getLevelType() {
			return level;
		}

		LevelType(String level) {
			this.level = level;
		}
	}
	/**
     * 顺序
     */
	String NODE_ORDER = "node_order";
	/**
	 * 根节点
	 */
	String ROOT_ID = "root_id";
	/**
	 * 父节点
	 */
	String PARENT_ID = "parent_id";
	/**
	 * treePath
	 */
	String TREE_PATH = "tree_path";
}