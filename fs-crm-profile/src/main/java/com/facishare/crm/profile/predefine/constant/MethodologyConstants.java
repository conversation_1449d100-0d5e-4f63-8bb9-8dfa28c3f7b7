package com.facishare.crm.profile.predefine.constant;

/**
 * 方法论常量
 * 
 * <AUTHOR>
 * @since 2025/5/12
 */
public interface MethodologyConstants {
    /**
     * 优先级
     */
    String PRIORITY = "priority";

    /**
     * 类型
     */
    String TYPE = "type";
    /**
     * 负责人所在部门
     */
    String OWNER_DEPARTMENT = "owner_department";
    /**
     * 相关团队
     */
    String RELEVANT_TEAM = "relevant_team";

    /**
     * 状态
     */
    String STATUS = "status";

    /**
     * 临时实例id
     */
    String TEMP_INSTANCE = "temp_instance";

    enum Type {
        /**
         * 流程
         */
        FLOW("flow"),
        /**
         * 方法论
         */
        METHODOLOGY("methodology");

        private final String type;

        public String getType() {
            return type;
        }

        Type(String type) {
            this.type = type;
        }
    }

    /**
     * 已发布
     */
    String STATUS_PUBLISHED = "published";
    /**
     * 未发布
     */
    String STATUS_UNPUBLISHED = "unpublished";

}