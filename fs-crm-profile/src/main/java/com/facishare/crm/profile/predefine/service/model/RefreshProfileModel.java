package com.facishare.crm.profile.predefine.service.model;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
public interface RefreshProfileModel {

    @Data
    class Arg {

        private String methodologyId;

        private String methodologyType;

        private String objectApiName;

        private String objectId;
    }

    @Builder
    class Result {

    }

    @Data
    @Builder
    class ProfileScore {

        private String tenantId;

        private String objectApiName;

        private String objectId;

        private String userId;

        private String methodologyInstanceId;
    }
}
