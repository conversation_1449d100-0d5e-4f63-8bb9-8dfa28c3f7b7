package com.facishare.crm.profile.predefine;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;

public enum ProfilePredefineObject implements PreDefineObject {

    PROFILE("ProfileObj"), PROFILE_ITEM_SCORE("ProfileItemScoreObj"), PROFILE_PROS_CONS("ProfileProsConsObj"),
    PROFILE_ADVICE("ProfileAdviceObj"), METHODOLOGY_INSTANCE("MethodologyInstanceObj"), METHODOLOGY("MethodologyObj"),
    NODE_INSTANCE("NodeInstanceObj"), WORKFLOW_NODE("WorkflowNodeObj"), FEATURE_DIMENSION("FeatureDimensionObj"),
    WORKFLOW_TASK("WorkflowTaskObj"), METHODOLOGY_RULE("MethodologyRuleObj"), FEATURE("FeatureObj"),
    SALES_COACH_RECORD("SalesCoachRecordObj");

    private static final String PACKAGE_NAME = "com.facishare.crm.profile.predefine";
    private static final String PACKAGE_NAME_ACTION = PACKAGE_NAME + ".action.";
    private static final String PACKAGE_NAME_CONTROLLER = PACKAGE_NAME + ".controller.";
    private static final String SUFFIX_ACTION = "Action";
    private static final String SUFFIX_CONTROLLER = "Controller";

    private final String apiName;

    ProfilePredefineObject(String apiName) {
        this.apiName = apiName;
    }

    public static void init() {
        for (ProfilePredefineObject object : ProfilePredefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String methodName) {
        return new ActionClassInfo(generateClassName(PACKAGE_NAME_ACTION, methodName, SUFFIX_ACTION));
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        return new ControllerClassInfo(
                generateClassName(PACKAGE_NAME_CONTROLLER, methodName, SUFFIX_CONTROLLER));
    }

    private String generateClassName(String packageName, String methodName, String suffix) {
        return packageName + this + methodName + suffix;
    }
}
