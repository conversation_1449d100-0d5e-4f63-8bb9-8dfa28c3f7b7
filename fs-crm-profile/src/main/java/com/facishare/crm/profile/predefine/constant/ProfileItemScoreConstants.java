package com.facishare.crm.profile.predefine.constant;

/**
 * 画像分项分数
 * 
 * <AUTHOR>
 * @since 2025/5/12
 */
public interface ProfileItemScoreConstants {
    /**
     * 画像
     */
    String PROFILE_ID = "profile_id";
    /**
     * 类型
     */
    String TYPE = "type";
    enum Type {
        /**
         * 维度
         */
        DIMENSION("dimension"),
        /**
         * 节点
         */
        NODE("node");

        private final String value;

        public String getValue() {
            return value;
        }

        Type(String value) {
            this.value = value;
        }
    }
    /**
     * 阶段
     */
    String NODE_ID = "node_id";
    /**
     * 维度
     */
    String FEATURE_DIMENSION_ID = "feature_dimension_id";
    /**
     * 分数
     */
    String SCORE = "score";
    /**
     * 总结
     */
    String SUMMARY = "summary";
    /**
     * 趋势总结
     */
    String TREND_SUMMARY = "trend_summary";
    /**
     * 完成任务id
     */
    String COMPLETED_TASK_IDS = "completed_task_ids";
    /**
     * 未完成任务id
     */
    String INCOMPLETE_TASK_IDS = "incomplete_task_ids";
    /**
     * 任务
     */
    String TASK_ID = "task_id";
    /**
     * 特征触发值
     */
    String FEATURE_TRIGGER_VALUES = "feature_trigger_values";
}