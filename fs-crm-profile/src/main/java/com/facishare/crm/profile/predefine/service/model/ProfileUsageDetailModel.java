package com.facishare.crm.profile.predefine.service.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/25
 */
public interface ProfileUsageDetailModel {

    @Data
    class Arg {
        private String searchQueryInfo;
    }

    @Builder
    @Data
    class Result {
        private List<ObjectDataDocument> dataList;
        private Integer total;
    }
}
