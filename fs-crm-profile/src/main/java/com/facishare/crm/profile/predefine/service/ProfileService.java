package com.facishare.crm.profile.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.beust.jcommander.internal.Maps;
import com.facishare.crm.profile.predefine.ProfilePredefineObject;
import com.facishare.crm.profile.predefine.constant.*;
import com.facishare.crm.profile.predefine.service.model.*;
import com.facishare.crm.profile.predefine.util.ProfileUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.appframework.metadata.dataconvert.BatchFieldDataConverterManager;
import com.facishare.paas.appframework.metadata.dataconvert.DataConvertContext;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
@Slf4j
@Component
@ServiceModule("profile")
public class ProfileService {

    @Resource(name = "profileProducer")
    private AutoConfMQProducer profileProducer;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private RedissonServiceImpl redissonService;

    @Autowired
    private BatchFieldDataConverterManager fieldDataConverterManager;

    @Autowired
    private ProfileCommonService profileCommonService;

    private static final List<String> CONVERT_FIELD_TYPE = Lists.newArrayList(IFieldType.OBJECT_REFERENCE, IFieldType.OBJECT_REFERENCE_MANY, IFieldType.EMPLOYEE, IFieldType.EMPLOYEE_MANY, IFieldType.DEPARTMENT, IFieldType.DEPARTMENT_MANY);

    /**
     * 刷新评分
     */
    @ServiceMethod("refresh_profile")
    public RefreshProfileModel.Result refreshProfile(ServiceContext context, RefreshProfileModel.Arg arg) {
        commonValidate(arg.getObjectApiName(), arg.getObjectId(), arg.getMethodologyType(), arg.getMethodologyId());
        // 获取方法论实例
        List<IObjectData> methodologyInstanceList = getMethodologyInstance(context, arg.getObjectApiName(),
                arg.getObjectId(), arg.getMethodologyId(), arg.getMethodologyType());
        if (CollectionUtils.isEmpty(methodologyInstanceList)) {
            throw new ValidateException(I18N.text("sfa.config.methodology.instance.not.found"));
        }
        String methodologyInstanceId = methodologyInstanceList.get(0).getId();
        // 查画像
        /*List<IObjectData> profileList = getLatestProfile(context, arg.getObjectApiName(), arg.getObjectId(), methodologyInstanceId);
        if (CollectionUtils.isNotEmpty(profileList)) {
            IObjectData profile = profileList.get(0);
            long lastModifiedTime = profile.get(ProfileConstants.LAST_MODIFIED_TIME, Long.class);
            if (DateUtils.isSameDay(new Date(lastModifiedTime), new Date())) {
                throw new ValidateException(I18N.text("sfa.config.today.refresh.profile.warn"));
            }
        }*/
        RLock rLock = redissonService.tryLock(0, 10, TimeUnit.MINUTES,
                String.format("refresh_profile_%s_%s_%s_%s", context.getTenantId(), arg.getObjectApiName(),
                        arg.getObjectId(), MethodologyConstants.Type.FLOW.getType().equals(arg.getMethodologyType())
                                ? arg.getMethodologyType() : arg.getMethodologyId()));
        if (rLock == null) {
            throw new ValidateException(I18N.text("sfa.config.frequent.refresh.profile.warn"));
        }
        /*int currentProfileAddCount = currentProfileAddCount(context.getUser(), arg.getObjectApiName(), arg.getObjectId());
        int remainProfileCount = profileCommonService.getRemainProfileCount(context.getTenantId());
        if (currentProfileAddCount > remainProfileCount) {
            throw new ValidateException(I18N.text("sfa.profile.quota.exceeded.warn"));
        }*/
        RefreshProfileModel.ProfileScore profileScore = RefreshProfileModel.ProfileScore.builder()
                .methodologyInstanceId(methodologyInstanceId).objectApiName(arg.getObjectApiName())
                .objectId(arg.getObjectId()).tenantId(context.getTenantId()).userId(context.getUser().getUserId())
                .build();
        DefaultTopicMessage msg = new DefaultTopicMessage(JSON.toJSONBytes(profileScore));
        profileProducer.send(msg);
        return RefreshProfileModel.Result.builder().build();
    }

    /**
     * 获取方法论实例
     */
    @ServiceMethod("get_methodology_instance")
    public MethodologyInstanceModel.Result getMethodologyInstanceInfo(ServiceContext context,
                                                                      MethodologyInstanceModel.Arg arg) {
        if (StringUtils.isAnyEmpty(arg.getObjectApiName(), arg.getObjectId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        if (MethodologyConstants.Type.METHODOLOGY.getType().equals(arg.getMethodologyType())
                && StringUtils.isEmpty(arg.getMethodologyId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        // 获取方法论实例列表
        List<IObjectData> methodologyInstanceList = getMethodologyInstance(context, arg.getObjectApiName(),
                arg.getObjectId(), arg.getMethodologyId(), arg.getMethodologyType());
        return MethodologyInstanceModel.Result.builder()
                .methodologyInstanceList(assignMethodologyInfo(context.getTenantId(), methodologyInstanceList))
                .build();
    }

    /**
     * 获取画像信息
     */
    @ServiceMethod("get_profile_info")
    public ProfileInfoModel.Result getProfileInfo(ServiceContext context,
                                                  ProfileInfoModel.Arg arg) {
        commonValidate(arg.getObjectApiName(), arg.getObjectId(), arg.getMethodologyType(), arg.getMethodologyId());
        ProfileInfoModel.Result result = ProfileInfoModel.Result.builder().build();
        List<IObjectData> methodologyInstanceList = getMethodologyInstance(context, arg.getObjectApiName(),
                arg.getObjectId(), arg.getMethodologyId(), arg.getMethodologyType());
        handleProfileInfo(context, arg, methodologyInstanceList, result);
        handleNodeInstanceList(context, arg, result);
        handleProfileItemList(context, result);
        return result;
    }

    /**
     * 获取画像评分趋势
     */
    @ServiceMethod("integrated_score_trend")
    public IntegratedScoreTrendModel.Result integratedScoreTrend(ServiceContext context,
                                                                 IntegratedScoreTrendModel.Arg arg) {
        commonValidate(arg.getObjectApiName(), arg.getObjectId(), arg.getMethodologyType(), arg.getMethodologyId());
        List<IObjectData> methodologyInstanceList = getMethodologyInstance(context, arg.getObjectApiName(),
                arg.getObjectId(), arg.getMethodologyId(), arg.getMethodologyType());
        if (CollectionUtils.isEmpty(methodologyInstanceList)) {
            return IntegratedScoreTrendModel.Result.builder().build();
        }
        String methodologyInstanceId = methodologyInstanceList.get(0).getId();
        SearchTemplateQuery query = SoCommonUtils.buildSearchTemplateQuery(5);
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query)
                .addFilter(Operator.EQ, ProfileUtil.parseFilterFiled(arg.getObjectApiName()), arg.getObjectId())
                .addFilter(Operator.EQ, ProfileConstants.METHODOLOGY_INSTANCE_ID, methodologyInstanceId);
        Optional.ofNullable(arg.getStartDate()).ifPresent(date ->
                queryExt.addFilter(Operator.GTE, ProfileConstants.CALC_TIME, String.valueOf(date)));
        Optional.ofNullable(arg.getEndDate()).ifPresent(date ->
                queryExt.addFilter(Operator.LTE, ProfileConstants.CALC_TIME, String.valueOf(date)));
        query.setOrders(Lists.newArrayList(new OrderBy(ProfileConstants.CALC_TIME, false)));
        List<IObjectData> profileList = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(),
                ProfilePredefineObject.PROFILE.getApiName(), query).getData();
        return IntegratedScoreTrendModel.Result.builder()
                .integratedScoreTrend(ObjectDataDocument.ofList(profileList))
                .build();
    }

    /**
     * 获取画像优劣势
     */
    @ServiceMethod("get_pros_cons")
    public ProsConsModel.Result getProsCons(ServiceContext context, ProsConsModel.Arg arg) {
        if (StringUtils.isEmpty(arg.getProfileId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        List<IObjectData> profileProsConsList = getProfileProsCons(context, Lists.newArrayList(arg.getProfileId()), arg.getFeatureDimensionId());
        return ProsConsModel.Result.builder()
                .profileProsCons(ObjectDataDocument.ofList(profileProsConsList))
                .build();
    }

    /**
     * 获取画像建议
     */
    @ServiceMethod("get_advice")
    public AdviceModel.Result getAdvice(ServiceContext context, AdviceModel.Arg arg) {
        if (StringUtils.isEmpty(arg.getProfileId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        IObjectData profile = serviceFacade.findObjectDataIgnoreAll(context.getUser(), arg.getProfileId(), ProfilePredefineObject.PROFILE.getApiName());
        if (profile == null) {
            return AdviceModel.Result.builder().build();
        }
        String profileAdviceSummary = generateProfileAdviceSummary(profile, context, arg.getFeatureDimensionId());
        List<IObjectData> profileAdviceList = getProfileAdvice(context, Lists.newArrayList(arg.getProfileId()), arg.getFeatureDimensionId());
        return AdviceModel.Result.builder()
                .profileAdviceSummary(profileAdviceSummary)
                .profileAdvice(ObjectDataDocument.ofList(profileAdviceList))
                .build();
    }

    /**
     * 获取画像历史
     */
    @ServiceMethod("profile_history")
    public ProfileHistoryModel.Result getProfileHistory(ServiceContext context, ProfileHistoryModel.Arg arg) {
        if (StringUtils.isAnyEmpty(arg.getObjectApiName(), arg.getObjectId(),
                arg.getMethodologyInstanceId(), arg.getMethodologyType())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        List<IObjectData> profileHistoryList = getProfileList(context, arg.getObjectApiName(),
                arg.getObjectId(), arg.getMethodologyInstanceId(), 7, false);
        if (CollectionUtils.isEmpty(profileHistoryList)) {
            return ProfileHistoryModel.Result.builder().build();
        }
        enrichProfileHistoryList(context, profileHistoryList, arg.getMethodologyType(), arg.getFeatureDimensionId());
        return ProfileHistoryModel.Result.builder()
                .profileHistory(ObjectDataDocument.ofList(profileHistoryList))
                .build();
    }

    /**
     * 优劣势采纳忽略
     */
    @ServiceMethod("pros_cons_accept")
    public ProsConsAcceptModel.Result prosConsAccept(ServiceContext context, ProsConsAcceptModel.Arg arg) {
        if (StringUtils.isEmpty(arg.getProsConsId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        List<IObjectData> prosConsList = serviceFacade.findObjectDataByIdsIgnoreAll(context.getTenantId(),
                Lists.newArrayList(arg.getProsConsId()), ProfilePredefineObject.PROFILE_PROS_CONS.getApiName());
        Set<String> fieldSet = Sets.newHashSet();
        prosConsList.forEach(prosCons -> {
            if (arg.getIsAccept() != null) {
                prosCons.set(ProfileProsConsConstants.IS_ACCEPT, arg.getIsAccept());
                fieldSet.add(ProfileProsConsConstants.IS_ACCEPT);
            }
            if (arg.getIsIgnore() != null) {
                prosCons.set(ProfileProsConsConstants.IS_IGNORE, arg.getIsIgnore());
                fieldSet.add(ProfileProsConsConstants.IS_IGNORE);
            }
        });
        serviceFacade.batchUpdateByFields(context.getUser(), prosConsList, Lists.newArrayList(fieldSet));
        return ProsConsAcceptModel.Result.builder().build();
    }

    /**
     * 建议采纳忽略
     */
    @ServiceMethod("advice_accept")
    public AdviceAcceptModel.Result adviceAccept(ServiceContext context, AdviceAcceptModel.Arg arg) {
        if (StringUtils.isEmpty(arg.getAdviceId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        List<IObjectData> adviceList = serviceFacade.findObjectDataByIdsIgnoreAll(context.getTenantId(),
                Lists.newArrayList(arg.getAdviceId()), ProfilePredefineObject.PROFILE_ADVICE.getApiName());
        Set<String> fieldSet = Sets.newHashSet();
        adviceList.forEach(advice -> {
            if (arg.getIsAccept() != null) {
                advice.set(ProfileAdviceConstants.IS_ACCEPT, arg.getIsAccept());
                fieldSet.add(ProfileAdviceConstants.IS_ACCEPT);
            }
            if (arg.getIsIgnore() != null) {
                advice.set(ProfileAdviceConstants.IS_IGNORE, arg.getIsIgnore());
                fieldSet.add(ProfileAdviceConstants.IS_IGNORE);
            }
        });
        serviceFacade.batchUpdateByFields(context.getUser(), adviceList, Lists.newArrayList(fieldSet));
        return AdviceAcceptModel.Result.builder().build();
    }

    /**
     * 获取方法论
     */
    @ServiceMethod("get_methodology")
    public MethodologyModel.Result getMethodology(ServiceContext context, MethodologyModel.Arg arg) {
        List<String> methodologyIds = null;
        if (StringUtils.isNotEmpty(arg.getObjectApiName())) {
            SearchTemplateQuery methodologyRuleQuery = SoCommonUtils.buildSearchTemplateQuery();
            SearchTemplateQueryExt.of(methodologyRuleQuery).addFilter(Operator.EQ, MethodologyRuleConstants.OBJECT_API_NAME, arg.getObjectApiName());
            List<IObjectData> methodologyRuleList = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(),
                    ProfilePredefineObject.METHODOLOGY_RULE.getApiName(), methodologyRuleQuery).getData();
            methodologyIds = methodologyRuleList.stream()
                    .map(objectData -> objectData.get(MethodologyRuleConstants.METHODOLOGY_ID, String.class))
                    .collect(Collectors.toList());
        }
        SearchTemplateQuery query = SoCommonUtils.buildSearchTemplateQuery();
        if (CollectionUtils.isNotEmpty(methodologyIds)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.IN, DBRecord.ID, methodologyIds);
        }
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, MethodologyConstants.STATUS, MethodologyConstants.STATUS_PUBLISHED);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, MethodologyConstants.TYPE, MethodologyConstants.Type.METHODOLOGY.getType());
        List<IObjectData> methodologyList = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(),
                ProfilePredefineObject.METHODOLOGY.getApiName(), query).getData();
        IObjectData flowObjectData = new ObjectData();
        flowObjectData.set("name", I18N.text("sfa.ai.profile.generic.profile"));
        flowObjectData.set("type", MethodologyConstants.Type.FLOW.getType());
        if (CollectionUtils.isEmpty(methodologyList)) {
            methodologyList = Lists.newArrayList(flowObjectData);
        } else {
            methodologyList.add(0, flowObjectData);
        }
        return MethodologyModel.Result.builder()
                .methodology(ObjectDataDocument.ofList(methodologyList))
                .build();
    }

    /**
     * 获取画像趋势变化
     */
    @ServiceMethod("trends_change")
    public TrendsChangeModel.Result trendsChange(ServiceContext context, TrendsChangeModel.Arg arg) {
        commonValidate(arg.getObjectApiName(), arg.getObjectId(), arg.getMethodologyType(), arg.getMethodologyId());
        List<IObjectData> methodologyInstanceList = getMethodologyInstance(context, arg.getObjectApiName(),
                arg.getObjectId(), arg.getMethodologyId(), arg.getMethodologyType());
        if (CollectionUtils.isEmpty(methodologyInstanceList)) {
            return TrendsChangeModel.Result.builder().build();
        }
        String methodologyInstanceId = methodologyInstanceList.get(0).getId();
        List<IObjectData> profileList = getProfileList(context, arg.getObjectApiName(),
                arg.getObjectId(), methodologyInstanceId, 7, false);
        if (CollectionUtils.isEmpty(profileList)) {
            return TrendsChangeModel.Result.builder().build();
        }
        List<String> profileIds = profileList.stream().map(IObjectData::getId).collect(Collectors.toList());
        Map<String, IObjectData> profileItemScoreMap = profileList.stream().collect(Collectors.toMap(IObjectData::getId, profileItemScore -> profileItemScore));
        List<IObjectData> profileItemScoreList = getProfileItemScore(context, profileIds, arg.getMethodologyType(), arg.getFeatureDimensionId());
        profileItemScoreList.forEach(profileItemScore -> {
            IObjectData profile = profileItemScoreMap.get(profileItemScore.get(ProfileItemScoreConstants.PROFILE_ID, String.class));
            profileItemScore.set(ProfileConstants.CALC_TIME, profile.get(ProfileConstants.CALC_TIME, String.class));
            if (StringUtils.isEmpty(arg.getFeatureDimensionId())) {
                profileItemScore.set(ProfileItemScoreConstants.TREND_SUMMARY, profile.get(ProfileConstants.TREND_SUMMARY, String.class));
            }
        });
        return TrendsChangeModel.Result.builder()
                .trendsChange(ObjectDataDocument.ofList(profileItemScoreList))
                .build();
    }

    /**
     * 变化趋势原始值
     */
    @ServiceMethod("feature_original_value")
    @SuppressWarnings("unchecked")
    public FeatureOriginalValueModel.Result featureOriginalValue(ServiceContext context, FeatureOriginalValueModel.Arg arg) {
        if (StringUtils.isAnyEmpty(arg.getProfileId(), arg.getMethodologyType(), arg.getMethodologyInstanceId(), arg.getObjectApiName(), arg.getObjectId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        //校验该画像是否是第一个，如果是第一个则返回为空
        List<IObjectData> profileList = getProfileList(context, arg.getObjectApiName(),
                arg.getObjectId(), arg.getMethodologyInstanceId(), 1, true);
        String firstProfileId = profileList.get(0).getId();
        if (Objects.equals(firstProfileId, arg.getProfileId())) {
            return FeatureOriginalValueModel.Result.builder().build();
        }
        // 获取画像项得分
        List<IObjectData> profileItemScoreList = getProfileItemScore(context, Lists.newArrayList(arg.getProfileId()), arg.getMethodologyType(), arg.getFeatureDimensionId());
        if (CollectionUtils.isEmpty(profileItemScoreList)) {
            return FeatureOriginalValueModel.Result.builder().build();
        }
        Map<String, Map<String, String>> dimensionToFeatureIdsMap = Maps.newHashMap();
        List<String> featureIds = Lists.newArrayList();
        List<String> triggerObjectApiNames = Lists.newArrayList();
        profileItemScoreList.forEach(profileItemScore -> {
            String featureDimensionId = Objects.equals(MethodologyConstants.Type.METHODOLOGY.getType(), arg.getMethodologyType()) ?
                    profileItemScore.get(ProfileItemScoreConstants.NODE_ID, String.class) :
                    profileItemScore.get(ProfileItemScoreConstants.FEATURE_DIMENSION_ID, String.class);
            String featureTriggerValues = profileItemScore.get(ProfileItemScoreConstants.FEATURE_TRIGGER_VALUES, String.class);
            if (StringUtils.isEmpty(featureTriggerValues)) {
                return;
            }
            JSON.parseObject(featureTriggerValues, new TypeReference<Map<String, String>>() {
                    })
                    .forEach((featureId, triggerValue) -> {
                        if (StringUtils.isEmpty(triggerValue)) {
                            return;
                        }
                        featureIds.add(featureId);
                        dimensionToFeatureIdsMap.computeIfAbsent(featureDimensionId, k -> new HashMap<>())
                                .put(featureId, triggerValue);
                        Map<String, String> triggerValueMap = JSON.parseObject(triggerValue, new TypeReference<Map<String, String>>() {
                        });
                        if ("field".equals(triggerValueMap.get("type"))) {
                            String triggerObjectApiName = String.valueOf(triggerValueMap.get("trigger_object_api_name"));
                            if (StringUtils.isNotEmpty(triggerObjectApiName)) {
                                triggerObjectApiNames.add(triggerObjectApiName);
                            }
                        }
                    });
        });
        // 获取对象描述
        Map<String, IObjectDescribe> objectDescribeMap = serviceFacade.findObjects(context.getTenantId(), triggerObjectApiNames);
        // 获取特征
        List<IObjectData> featureList = getFeatureByFeatureIds(context, featureIds);
        // 获取特征map
        Map<String, IObjectData> featureMap = featureList.stream().collect(Collectors.toMap(IObjectData::getId, feature -> feature));
        // 处理特征原始值
        List<FeatureOriginalValueModel.FeatureTriggerValue> featureTriggerValueList = Lists.newArrayList();
        dimensionToFeatureIdsMap.forEach((dimensionId, featureIdToTriggerValueMap) -> {
            featureTriggerValueList.addAll(featureIdToTriggerValueMap.entrySet().stream()
                    .map(entry -> {
                        String featureId = entry.getKey();
                        Map<String, Object> triggerValueObjectMap = JSON.parseObject(entry.getValue(), Map.class);
                        String type = String.valueOf(triggerValueObjectMap.get("type"));
                        FeatureOriginalValueModel.FeatureTriggerValue featureTriggerValue = new FeatureOriginalValueModel.FeatureTriggerValue();
                        IObjectData feature = featureMap.get(featureId);
                        featureTriggerValue.setFeatureId(featureId);
                        featureTriggerValue.setFeatureName(feature.getName());
                        featureTriggerValue.setFeatureDimensionId(dimensionId);
                        featureTriggerValue.setType(type);
                        if (StringUtils.equals(type, "field")) {
                            Map<String, Object> triggerValueMap = (Map<String, Object>) triggerValueObjectMap.get("trigger_value");
                            if (triggerValueMap != null && !triggerValueMap.isEmpty()) {
                                String triggerObjectApiName = String.valueOf(triggerValueObjectMap.get("trigger_object_api_name"));
                                IObjectDescribe triggerObjectDescribe = objectDescribeMap.get(triggerObjectApiName);
                                IObjectData triggerObjectData = ObjectDataExt.of(triggerValueMap).copy();
                                List<Map<String, Object>> fieldDescribeList = triggerValueMap.keySet().stream()
                                        .map(o -> {
                                            IFieldDescribe fieldDescribe = triggerObjectDescribe.getFieldDescribe(o);
                                            if (CONVERT_FIELD_TYPE.contains(fieldDescribe.getType())) {
                                                fieldDataConverterManager.getBatchFieldDataConverter(fieldDescribe.getType())
                                                        .convertFieldData(Lists.newArrayList(triggerObjectData), fieldDescribe,
                                                                DataConvertContext.of(context.getUser()));
                                            }
                                            if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType())) {
                                                String masterDetailId = triggerObjectData.get(fieldDescribe.getApiName(), String.class);
                                                if (StringUtils.isNotEmpty(masterDetailId)) {
                                                    SearchTemplateQuery query = SoCommonUtils.buildSearchTemplateQuery();
                                                    SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, fieldDescribe.getApiName(), masterDetailId);
                                                    List<IObjectData> detailDataList = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(), fieldDescribe.getDescribeApiName(), query).getData();
                                                    if (CollectionUtils.isNotEmpty(detailDataList)) {
                                                        triggerObjectData.set(fieldDescribe.getApiName() + "__r", detailDataList.stream()
                                                                .map(IObjectData::getName).collect(Collectors.joining(",")));
                                                    }
                                                }
                                            }
                                            Map<String, Object> fieldDescribeMap = JSON.parseObject(fieldDescribe.toJsonString(), Map.class);
                                            return fieldDescribeMap;
                                        })
                                        .collect(Collectors.toList());
                                featureTriggerValue.setOriginalValueDocument(ObjectDataDocument.of(triggerObjectData));
                                featureTriggerValue.setFieldDescribes(fieldDescribeList);
                            }
                        } else {
                            String triggerValue = String.valueOf(triggerValueObjectMap.get("feature_value"));
                            String dataType = String.valueOf(triggerValueObjectMap.get("data_type"));
                            if (StringUtils.isNotEmpty(triggerValue)) {
                                if (Objects.equals("bool", dataType)) {
                                    featureTriggerValue.setOriginalValue("true".equals(triggerValueObjectMap.get("feature_value"))
                                            ? I18N.text(I18NKey.YES) : I18N.text(I18NKey.NO));
                                } else {
                                    featureTriggerValue.setOriginalValue(triggerValue);
                                }
                            }
                        }
                        return featureTriggerValue;
                    })
                    .collect(Collectors.toList()));
        });
        return FeatureOriginalValueModel.Result.builder()
                .featureTriggerValueList(featureTriggerValueList)
                .build();
    }

    /**
     * 查询资源使用情况
     */
    @ServiceMethod("profile_usage_detail")
    public ProfileUsageDetailModel.Result getProfileUsageDetail(ServiceContext context, ProfileUsageDetailModel.Arg arg) {
        if (StringUtils.isEmpty(arg.getSearchQueryInfo())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
        searchTemplateQuery.setPermissionType(0);
        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(context.getUser(), ProfilePredefineObject.SALES_COACH_RECORD.getApiName(), searchTemplateQuery);
        return ProfileUsageDetailModel.Result.builder()
                .dataList(ObjectDataDocument.ofList(result.getData()))
                .total(result.getTotalNumber())
                .build();
    }

    private List<String> getMethodologyByType(ServiceContext context, String methodologyId, String methodologyType) {
        if (StringUtils.isEmpty(methodologyType)) {
            return Collections.emptyList();
        }
        if (MethodologyConstants.Type.METHODOLOGY.getType().equals(methodologyType)) {
            return Lists.newArrayList(methodologyId);
        }
        SearchTemplateQuery query = SoCommonUtils.buildSearchTemplateQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, MethodologyConstants.TYPE, MethodologyConstants.Type.FLOW.getType());
        return serviceFacade.findBySearchQueryIgnoreAll(context.getUser(), ProfilePredefineObject.METHODOLOGY.getApiName(), query).getData()
                .stream().map(objectData -> objectData.get(DBRecord.ID, String.class)).collect(Collectors.toList());
    }

    /**
     * 获取方法论实例
     */
    private List<IObjectData> getMethodologyInstance(ServiceContext context, String objectApiName,
                                                     String objectId, String methodologyId, String methodologyType) {
        List<String> methodologyIds = getMethodologyByType(context, methodologyId, methodologyType);
        SearchTemplateQuery query = SoCommonUtils.buildSearchTemplateQuery();
        SearchTemplateQueryExt.of(query)
                .addFilter(Operator.EQ, ProfileUtil.parseFilterFiled(objectApiName), objectId)
                .addFilter(Operator.EQ, MethodologyInstanceConstants.STATUS,
                        MethodologyInstanceConstants.StatusType.ENABLE.getStatusType());
        if (CollectionUtils.isNotEmpty(methodologyIds)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.IN, MethodologyInstanceConstants.METHODOLOGY_ID, methodologyIds);
        }
        // 获取方法论实例列表
        return serviceFacade.findBySearchQueryIgnoreAll(context.getUser(),
                ProfilePredefineObject.METHODOLOGY_INSTANCE.getApiName(), query).getData();
    }

    /**
     * 获取最新画像
     */
    private List<IObjectData> getLatestProfile(ServiceContext context, String objectApiName,
                                               String objectId, String methodologyInstanceId) {
        SearchTemplateQuery methodologyInstanceQuery = SoCommonUtils.buildSearchTemplateQuery(1);
        SearchTemplateQueryExt.of(methodologyInstanceQuery)
                .addFilter(Operator.EQ, ProfileUtil.parseFilterFiled(objectApiName), objectId)
                .addFilter(Operator.EQ, ProfileConstants.METHODOLOGY_INSTANCE_ID, methodologyInstanceId)
                .addFilter(Operator.EQ, ProfileConstants.IS_LATEST, "true");
        return serviceFacade.findBySearchQueryIgnoreAll(context.getUser(),
                ProfilePredefineObject.PROFILE.getApiName(), methodologyInstanceQuery).getData();
    }

    private List<IObjectData> getProfileItemScore(ServiceContext context, String profileId) {
        return getProfileItemScore(context, Lists.newArrayList(profileId), null, null);
    }

    /**
     * 获取画像各分项
     */
    private List<IObjectData> getProfileItemScore(ServiceContext context, List<String> profileIds, String methodologyType, String featureDimensionId) {
        SearchTemplateQuery profileItemQuery = SoCommonUtils.buildSearchTemplateQuery();
        if (profileIds.size() == 1) {
            SearchTemplateQueryExt.of(profileItemQuery).addFilter(Operator.EQ, ProfileItemScoreConstants.PROFILE_ID, profileIds.get(0));
        } else {
            SearchTemplateQueryExt.of(profileItemQuery).addFilter(Operator.IN, ProfileItemScoreConstants.PROFILE_ID, profileIds);
        }
        if (StringUtils.isNotEmpty(methodologyType)) {
            if (MethodologyConstants.Type.FLOW.getType().equals(methodologyType)) {
                SearchTemplateQueryExt.of(profileItemQuery).addFilter(Operator.EQ, ProfileItemScoreConstants.TYPE, ProfileItemScoreConstants.Type.DIMENSION.getValue());
            } else if (MethodologyConstants.Type.METHODOLOGY.getType().equals(methodologyType)) {
                SearchTemplateQueryExt.of(profileItemQuery).addFilter(Operator.EQ, ProfileItemScoreConstants.TYPE, ProfileItemScoreConstants.Type.NODE.getValue());
            }
        }
        if (StringUtils.isNotEmpty(featureDimensionId)) {
            if (MethodologyConstants.Type.FLOW.getType().equals(methodologyType)) {
                SearchTemplateQueryExt.of(profileItemQuery).addFilter(Operator.EQ, ProfileItemScoreConstants.FEATURE_DIMENSION_ID, featureDimensionId);
            } else if (MethodologyConstants.Type.METHODOLOGY.getType().equals(methodologyType)) {
                SearchTemplateQueryExt.of(profileItemQuery).addFilter(Operator.EQ, ProfileItemScoreConstants.NODE_ID, featureDimensionId);
            }
        }
        profileItemQuery.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, false)));
        return serviceFacade.findBySearchQueryIgnoreAll(context.getUser(),
                ProfilePredefineObject.PROFILE_ITEM_SCORE.getApiName(), profileItemQuery).getData();
    }

    private List<IObjectData> getProfileProsCons(ServiceContext context, List<String> profileIds, String featureDimensionId) {
        SearchTemplateQuery query = SoCommonUtils.buildSearchTemplateQuery();
        if (profileIds.size() == 1) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileProsConsConstants.PROFILE_ID, profileIds.get(0));
        } else {
            SearchTemplateQueryExt.of(query).addFilter(Operator.IN, ProfileProsConsConstants.PROFILE_ID, profileIds);
        }
        if (StringUtils.isNotEmpty(featureDimensionId)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileProsConsConstants.RANGE_TYPE, ProfileProsConsConstants.RangeType.DIMENSION.getValue());
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileProsConsConstants.FEATURE_DIMENSION_ID, featureDimensionId);
        } else {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileProsConsConstants.RANGE_TYPE, ProfileProsConsConstants.RangeType.PROFILE.getValue());
        }
        List<IObjectData> profileProsConsList = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(),
                ProfilePredefineObject.PROFILE_PROS_CONS.getApiName(), query).getData();
        if (CollectionUtils.isNotEmpty(profileProsConsList)) {
            // 按profileId分组
            Map<String, List<IObjectData>> profileProsConsMap = profileProsConsList.stream()
                    .collect(Collectors.groupingBy(objectData -> objectData.get(ProfileProsConsConstants.PROFILE_ID, String.class)));
            // 对每个profileId的优劣势进行处理
            return profileProsConsMap.values().stream()
                    .flatMap(prosConsList -> {
                        // 按类型分组
                        Map<String, List<IObjectData>> prosConsByType = prosConsList.stream()
                                .collect(Collectors.groupingBy(objectData -> objectData.get(ProfileProsConsConstants.TYPE, String.class)));
                        // 每个profileId取3条优势和3条劣势
                        List<IObjectData> prosList = prosConsByType.getOrDefault(ProfileProsConsConstants.Type.PROS.getValue(), Collections.emptyList());
                        List<IObjectData> consList = prosConsByType.getOrDefault(ProfileProsConsConstants.Type.CONS.getValue(), Collections.emptyList());
                        return Stream.concat(
                                prosList.stream().sorted(Comparator.comparing(o -> o.get(ProfileProsConsConstants.SEQ, Integer.class))).limit(3),
                                consList.stream().sorted(Comparator.comparing(o -> o.get(ProfileProsConsConstants.SEQ, Integer.class))).limit(3)
                        );
                    })
                    .collect(Collectors.toList());
        }
        return profileProsConsList;
    }

    private List<IObjectData> getProfileAdvice(ServiceContext context, List<String> profileIds, String featureDimensionId) {
        SearchTemplateQuery query = SoCommonUtils.buildSearchTemplateQuery();
        if (profileIds.size() == 1) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileAdviceConstants.PROFILE_ID, profileIds.get(0));
        } else {
            SearchTemplateQueryExt.of(query).addFilter(Operator.IN, ProfileAdviceConstants.PROFILE_ID, profileIds);
        }
        if (StringUtils.isNotEmpty(featureDimensionId)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileAdviceConstants.RANGE_TYPE, ProfileProsConsConstants.RangeType.DIMENSION.getValue());
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileAdviceConstants.FEATURE_DIMENSION_ID, featureDimensionId);
        } else {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, ProfileAdviceConstants.RANGE_TYPE, ProfileProsConsConstants.RangeType.PROFILE.getValue());
        }
        query.setOrders(Lists.newArrayList(new OrderBy(ProfileAdviceConstants.SEQ, true)));
        List<IObjectData> profileAdviceList = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(),
                ProfilePredefineObject.PROFILE_ADVICE.getApiName(), query).getData();
        // 收集所有非空的KNOWLEDGE_DOCUMENT_IDS
        List<String> allKnowledgeIds = profileAdviceList.stream()
                .filter(advice -> ProfileAdviceConstants.Type.CUSTOMER.getValue().equals(advice.get(ProfileAdviceConstants.TYPE, String.class)))
                .map(advice -> advice.get(ProfileAdviceConstants.KNOWLEDGE_DOCUMENT_IDS, List.class))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(ids -> ((List<String>) ids).stream())
                .distinct()
                .collect(Collectors.toList());
        // 如果有知识库文档ID，则查询对应的文档信息
        if (CollectionUtils.isNotEmpty(allKnowledgeIds)) {
            List<IObjectData> knowledgeDocs = serviceFacade.findObjectDataByIdsIgnoreAll(context.getTenantId(), allKnowledgeIds, "ServiceKnowledgeObj");
            Map<String, String> idToTitleMap = knowledgeDocs.stream()
                    .collect(Collectors.toMap(IObjectData::getId, doc -> doc.get("title", String.class)));
            // 为每条建议设置对应的知识库文档标题
            profileAdviceList.forEach(advice -> {
                List<String> docIds = advice.get(ProfileAdviceConstants.KNOWLEDGE_DOCUMENT_IDS, List.class);
                if (CollectionUtils.isNotEmpty(docIds)) {
                    List<String> docTitles = docIds.stream()
                            .map(idToTitleMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    advice.set(ProfileAdviceConstants.KNOWLEDGE_DOCUMENT_NAMES, docTitles);
                }
            });
        }
        Map<String, String> fieldMappings = new HashMap<>();
        fieldMappings.put("task_id__r", "name");
        assignObjectInfo(context.getTenantId(), profileAdviceList, "task_id", ProfilePredefineObject.WORKFLOW_TASK, fieldMappings);
        return profileAdviceList;
    }

    private void handleProfileInfo(ServiceContext context, ProfileInfoModel.Arg arg,
                                   List<IObjectData> methodologyInstanceList, ProfileInfoModel.Result result) {
        if (CollectionUtils.isEmpty(methodologyInstanceList)) {
            return;
        }
        IObjectData methodologyInstance = methodologyInstanceList.get(0);
        result.setStageLevel(methodologyInstance.get(MethodologyInstanceConstants.STAGE_LEVEL, Integer.class, 1));
        List<IObjectData> profileList = getProfileList(context, arg.getObjectApiName(),
                arg.getObjectId(), methodologyInstance.getId(), 2, false);
        Map<Boolean, List<IObjectData>> profileMap = profileList.stream()
                .collect(Collectors.partitioningBy(x -> Boolean.TRUE.equals(x.get(ProfileConstants.IS_LATEST, Boolean.class))));
        List<IObjectData> currentProfileList = profileMap.get(true);
        List<IObjectData> historyProfileList = profileMap.get(false);
        if (CollectionUtils.isNotEmpty(currentProfileList)) {
            IObjectData currentProfile = currentProfileList.get(0);
            BigDecimal currentScore = currentProfile.get(ProfileConstants.INTEGRATED_SCORE, BigDecimal.class, BigDecimal.ZERO);
            if (CollectionUtils.isNotEmpty(historyProfileList)) {
                IObjectData historyProfile = historyProfileList.get(0);
                BigDecimal historyScore = historyProfile.get(ProfileConstants.INTEGRATED_SCORE, BigDecimal.class, BigDecimal.ZERO);
                currentProfile.set(ProfileConstants.INTEGRATED_SCORE_CHANGE, currentScore.subtract(historyScore));
            }
            result.setProfile(ObjectDataDocument.of(currentProfile));
        }
    }

    private void handleNodeInstanceList(ServiceContext context, ProfileInfoModel.Arg arg, ProfileInfoModel.Result result) {
        if (result.getProfile() == null) {
            return;
        }
        IObjectData profile = result.getProfile().toObjectData();
        SearchTemplateQuery nodeInstanceQuery = SoCommonUtils.buildSearchTemplateQuery();
        SearchTemplateQueryExt.of(nodeInstanceQuery)
                .addFilter(Operator.EQ, NodeInstanceConstants.LEVEL, String.valueOf(result.getStageLevel()))
                .addFilter(Operator.EQ, NodeInstanceConstants.METHODOLOGY_INSTANCE_ID, profile.get(ProfileConstants.METHODOLOGY_INSTANCE_ID, String.class));
        if (MethodologyConstants.Type.FLOW.getType().equals(profile.get("methodology_type", String.class))) {
            SearchTemplateQueryExt.of(nodeInstanceQuery).addFilter(Operator.EQ, NodeInstanceConstants.OBJECT_API_NAME, arg.getObjectApiName());
        }
        nodeInstanceQuery.setOrders(Lists.newArrayList(new OrderBy(NodeInstanceConstants.NODE_ORDER, true)));
        List<IObjectData> nodeInstanceList = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(),
                ProfilePredefineObject.NODE_INSTANCE.getApiName(), nodeInstanceQuery).getData();
        result.setNodeInstance(assignNodeInfo(context.getTenantId(), nodeInstanceList));
    }

    private void handleProfileItemList(ServiceContext context, ProfileInfoModel.Result result) {
        if (result.getProfile() == null) {
            return;
        }
        IObjectData profile = result.getProfile().toObjectData();
        List<IObjectData> profileItemList = getProfileItemScore(context, profile.getId());
        Map<Boolean, List<IObjectData>> profileItemMap = profileItemList.stream()
                .collect(Collectors.partitioningBy(x -> ProfileItemScoreConstants.Type.NODE.getValue().equals(x.get(ProfileItemScoreConstants.TYPE, String.class))));
        assignNodeInfo(context.getTenantId(), profileItemMap.get(true));
        assignDimensionInfo(context.getTenantId(), profileItemMap.get(false));
        result.setProfileItemScore(ObjectDataDocument.ofList(profileItemList));
        assignTaskInfo(context.getTenantId(), profileItemMap.get(true), profile);
    }

    /**
     * 分配方法论信息
     */
    private List<ObjectDataDocument> assignMethodologyInfo(String tenantId, List<IObjectData> objectDataList) {
        Map<String, String> fieldMappings = new HashMap<>();
        fieldMappings.put("methodology_id__r", "name");
        fieldMappings.put("methodology_type", "type");
        return assignObjectInfo(tenantId, objectDataList, "methodology_id", ProfilePredefineObject.METHODOLOGY, fieldMappings);
    }

    /**
     * 分配节点信息
     */
    private List<ObjectDataDocument> assignNodeInfo(String tenantId, List<IObjectData> objectDataList) {
        Map<String, String> fieldMappings = new HashMap<>();
        fieldMappings.put("node_id__r", "name");
        fieldMappings.put("node_short_name", "short_name");
        fieldMappings.put("node_order", "node_order");
        return assignObjectInfo(tenantId, objectDataList, "node_id", ProfilePredefineObject.WORKFLOW_NODE, fieldMappings);
    }

    /**
     * 分配维度信息
     */
    private void assignDimensionInfo(String tenantId, List<IObjectData> objectDataList) {
        Map<String, String> fieldMappings = new HashMap<>();
        fieldMappings.put("feature_dimension_id__r", "name");
        fieldMappings.put("dimension_order", "dimension_order");
        assignObjectInfo(tenantId, objectDataList, "feature_dimension_id", ProfilePredefineObject.FEATURE_DIMENSION, fieldMappings);
    }

    /**
     * 分配任务信息
     */
    @SuppressWarnings("unchecked")
    private void assignTaskInfo(String tenantId, List<IObjectData> profileItemList, IObjectData profile) {
        if (CollectionUtils.isEmpty(profileItemList)
                || MethodologyConstants.Type.FLOW.getType().equals(profile.get("methodology_type", String.class))) {
            return;
        }
        Set<String> taskIds = Sets.newHashSet();
        profileItemList.forEach(objectData -> {
            taskIds.addAll(Optional.ofNullable(objectData.get("completed_task_ids", List.class))
                    .orElse(Collections.emptyList()));
            taskIds.addAll(Optional.ofNullable(objectData.get("incomplete_task_ids", List.class))
                    .orElse(Collections.emptyList()));
        });
        List<IObjectData> taskList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(taskIds), ProfilePredefineObject.WORKFLOW_TASK.getApiName());
        // 创建任务ID到任务对象的映射，避免重复过滤
        Map<String, IObjectData> taskMap = taskList.stream()
                .collect(Collectors.toMap(IObjectData::getId, task -> task, (v1, v2) -> v1));
        profileItemList.forEach(profileItem -> {
            ObjectDataDocument profileItemDoc = ObjectDataDocument.of(profileItem);
            List<String> completedTaskIds = profileItem.get("completed_task_ids", List.class);
            List<String> incompleteTaskIds = profileItem.get("incomplete_task_ids", List.class);
            profileItemDoc.put("completedTask", ObjectDataDocument.ofList(
                    CollectionUtils.isEmpty(completedTaskIds) ? null :
                            completedTaskIds.stream()
                                    .map(taskMap::get)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList())
            ));
            profileItemDoc.put("incompleteTask", ObjectDataDocument.ofList(
                    CollectionUtils.isEmpty(incompleteTaskIds) ? null :
                            incompleteTaskIds.stream()
                                    .map(taskMap::get)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList())
            ));
        });
    }

    /*
     * 通用的对象信息分配方法
     */
    private List<ObjectDataDocument> assignObjectInfo(String tenantId,
                                                      List<IObjectData> objectDataList,
                                                      String idField,
                                                      ProfilePredefineObject objectType,
                                                      Map<String, String> fieldMappings) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Collections.emptyList();
        }
        // 获取所有唯一ID
        List<String> ids = objectDataList.stream()
                .map(instance -> instance.get(idField, String.class))
                .distinct()
                .collect(Collectors.toList());
        // 获取对象信息映射
        Map<String, Map<String, String>> infoMap = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, ids, objectType.getApiName())
                .stream().collect(Collectors.toMap(
                        IObjectData::getId,
                        obj -> {
                            Map<String, String> info = new HashMap<>();
                            fieldMappings.forEach((targetField, sourceField) ->
                                    info.put(sourceField, obj.get(sourceField, String.class)));
                            return info;
                        },
                        (v1, v2) -> v1));
        // 分配信息到对象
        return objectDataList.stream().map(objectData -> {
                    String objectDataId = objectData.get(idField, String.class);
                    return Optional.ofNullable(infoMap.get(objectDataId))
                            .map(info -> {
                                ObjectDataDocument doc = ObjectDataDocument.of(objectData);
                                fieldMappings.forEach(
                                        (targetField, sourceField) -> doc.put(targetField, info.get(sourceField)));
                                return doc;
                            })
                            .orElse(null);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<IObjectData> getProfileList(ServiceContext context, String objectApiName,
                                             String objectId, String methodologyInstanceId,
                                             int limit, boolean isAsc) {
        SearchTemplateQuery profileQuery = SoCommonUtils.buildSearchTemplateQuery(limit);
        SearchTemplateQueryExt.of(profileQuery)
                .addFilter(Operator.EQ, ProfileUtil.parseFilterFiled(objectApiName), objectId)
                .addFilter(Operator.EQ, ProfileConstants.METHODOLOGY_INSTANCE_ID, methodologyInstanceId);
        profileQuery.setOrders(Lists.newArrayList(new OrderBy(ProfileConstants.CALC_TIME, isAsc)));
        List<IObjectData> profileList = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(),
                ProfilePredefineObject.PROFILE.getApiName(), profileQuery).getData();
        assignMethodologyInfo(context.getTenantId(), profileList);
        return profileList;
    }

    private List<IObjectData> getFeatureByFeatureIds(ServiceContext context, List<String> featureIds) {
        if (CollectionUtils.isEmpty(featureIds)) {
            return Collections.emptyList();
        }
        return serviceFacade.findObjectDataByIdsIgnoreAll(context.getTenantId(), featureIds, ProfilePredefineObject.FEATURE.getApiName());
    }

    private String generateProfileAdviceSummary(IObjectData profile, ServiceContext context, String featureDimensionId) {
        if (StringUtils.isNotEmpty(featureDimensionId)) {
            return StringUtils.EMPTY;
        }
        return Optional.ofNullable(profile.get(ProfileConstants.CURRENT_SCORE, BigDecimal.class))
                .map(score -> score.compareTo(new BigDecimal(80)) >= 0
                        ? Optional.ofNullable(profile.get(ProfileConstants.NEXT_NODE_ID, String.class))
                        .filter(StringUtils::isNotEmpty)
                        .map(nodeId -> serviceFacade.findObjectDataIgnoreAll(context.getUser(), nodeId, ProfilePredefineObject.WORKFLOW_NODE.getApiName()))
                        .map(node -> String.format(I18N.text("sfa.profile.advice.greater.than.score.have.stage"), node.getName()))
                        .orElse(I18N.text("sfa.profile.advice.greater.than.score.no.stage"))
                        : I18N.text("sfa.profile.advice.less.than.score"))
                .orElse("");
    }

    private void commonValidate(String objectApiName, String objectId, String methodologyType, String methodologyId) {
        if (StringUtils.isAnyEmpty(objectApiName, objectId, methodologyType)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        if (MethodologyConstants.Type.METHODOLOGY.getType().equals(methodologyType)
                && StringUtils.isEmpty(methodologyId)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
    }

    private void enrichProfileHistoryList(ServiceContext context, List<IObjectData> profileHistoryList,
                                          String methodologyType, String featureDimensionId) {
        List<String> profileIds = profileHistoryList.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<IObjectData> profileItemScoreList = Collections.emptyList();
        if (StringUtils.isNotEmpty(featureDimensionId)) {
            profileItemScoreList = getProfileItemScore(context, profileIds, methodologyType, featureDimensionId);
        }
        List<IObjectData> profileProsConsList = getProfileProsCons(context, profileIds, featureDimensionId);
        List<IObjectData> profileAdviceList = getProfileAdvice(context, profileIds, featureDimensionId);
        Map<String, List<IObjectData>> profileItemScoreMap = profileItemScoreList.stream()
                .collect(Collectors.groupingBy(objectData -> objectData.get(ProfileItemScoreConstants.PROFILE_ID, String.class)));
        Map<String, List<IObjectData>> profileProsConsMap = profileProsConsList.stream()
                .collect(Collectors.groupingBy(objectData -> objectData.get(ProfileProsConsConstants.PROFILE_ID, String.class)));
        Map<String, List<IObjectData>> profileAdviceMap = profileAdviceList.stream()
                .collect(Collectors.groupingBy(objectData -> objectData.get(ProfileAdviceConstants.PROFILE_ID, String.class)));
        for (IObjectData profileHistory : profileHistoryList) {
            String profileId = profileHistory.getId();
            profileHistory.set("profileItemScore", ObjectDataDocument.ofList(profileItemScoreMap.get(profileId)));
            profileHistory.set("profileProsCons", ObjectDataDocument.ofList(profileProsConsMap.get(profileId)));
            profileHistory.set("profileAdvice", ObjectDataDocument.ofList(profileAdviceMap.get(profileId)));
        }
    }

    public int currentProfileAddCount(User user, String objectApiName, String objectId) {
        SearchTemplateQuery profileQuery = SoCommonUtils.buildSearchTemplateQuery(1);
        SearchTemplateQueryExt.of(profileQuery).addFilter(Operator.EQ, ProfileUtil.parseFilterFiled(objectApiName), objectId);
        List<IObjectData> profileList = serviceFacade.findBySearchQueryIgnoreAll(user, ProfilePredefineObject.PROFILE.getApiName(), profileQuery).getData();
        return CollectionUtils.isNotEmpty(profileList) ? 0 : 1;
    }

}
