package com.facishare.crm.profile.predefine.constant;

/**
 * 画像建议
 * 
 * <AUTHOR>
 * @since 2025/5/20
 */
public interface ProfileAdviceConstants {
    /**
     * 画像
     */
    String PROFILE_ID = "profile_id";
    /**
     * 维度
     */
    String FEATURE_DIMENSION_ID = "feature_dimension_id";
    /**
     * 范围
     */
    String RANGE_TYPE = "range_type";
    enum RangeType {
        /**
         * 维度
         */
        DIMENSION("dimension"),
        /**
         * 画像
         */
        PROFILE("profile");

        private final String value;

        public String getValue() {
            return value;
        }

        RangeType(String value) {
            this.value = value;
        }
    }
    /**
     * 类型
     */
    String TYPE = "type";
    enum Type {
        /**
         * 客户知识建议
         */
        CUSTOMER("customer"),
        /**
         * 纷享知识建议
         */
        FS("fs");

        private final String value;

        public String getValue() {
            return value;
        }

        Type(String value) {
            this.value = value;
        }
    }
    /**
     * 建议
     */
    String ADVICE = "advice";
    /**
     * 是否采纳
     */
    String IS_ACCEPT = "is_accept";
    /**
     * 是否忽略
     */
    String IS_IGNORE = "is_ignore";
    /**
     * 序号
     */
    String SEQ = "seq";
    /**
     * 任务
     */
    String TASK_ID = "task_id";
    /**
     * 建议来源知识库
     */
    String KNOWLEDGE_DOCUMENT_IDS = "knowledge_document_ids";
    /**
     * 建议来源知识库名称
     */
    String KNOWLEDGE_DOCUMENT_NAMES = "knowledge_document_names";
}