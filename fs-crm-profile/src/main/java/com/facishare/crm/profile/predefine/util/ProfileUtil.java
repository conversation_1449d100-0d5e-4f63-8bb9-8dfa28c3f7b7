package com.facishare.crm.profile.predefine.util;

import com.facishare.crm.openapi.Utils;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
public class ProfileUtil {

    public static String parseFilterFiled(String objectApiName) {
        String filterField = null;
        switch (objectApiName) {
            case Utils.LEADS_API_NAME:
                filterField = "lead_id";
                break;
            case Utils.ACCOUNT_API_NAME:
                filterField = "account_id";
                break;
            case Utils.NEW_OPPORTUNITY_API_NAME:
                filterField = "opportunity_id";
                break;
            default:
                break;
        }
        return filterField;
    }
}
