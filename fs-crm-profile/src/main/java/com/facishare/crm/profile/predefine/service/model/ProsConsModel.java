package com.facishare.crm.profile.predefine.service.model;

import java.util.List;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/5/15
 */
public interface ProsConsModel {

    @Data
    class Arg {

        private String profileId;

        private String featureDimensionId;

    }

    @Builder
    @Data
    class Result {

        private List<ObjectDataDocument> profileProsCons;
        
    }

}
