package com.facishare.crm.profile.predefine.constant;

/**
 * 方法论实例
 *
 */
public interface MethodologyInstanceConstants {
    /**
     * 方法论
     */
    String METHODOLOGY_ID = "methodology_id";
    /**
     * 线索
     */
    String LEAD_ID = "lead_id";
    /**
     * 客户
     */
    String ACCOUNT_ID = "account_id";
    /**
     * 商机
     */
    String OPPORTUNITY_ID = "opportunity_id";
    /**
     * 创建时间
     */
    String CREATE_TIME = "create_time";
    /**
     * 状态
     */
    String STATUS = "status";
    /**
     * 流程阶段层级
     */
    String STAGE_LEVEL = "stage_level";

    /**
     * 状态类型
     */
    enum StatusType {
        ENABLE("1"),
        DISABLE("0");

        private final String statusType;

        StatusType(String statusType) {
            this.statusType = statusType;
        }

        public String getStatusType() {
            return statusType;
        }
    }
}