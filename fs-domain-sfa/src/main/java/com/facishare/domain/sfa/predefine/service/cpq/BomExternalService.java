package com.facishare.domain.sfa.predefine.service.cpq;

import com.facishare.crm.sfa.predefine.service.cpq.*;
import com.facishare.crm.sfa.predefine.service.cpq.model.*;
import com.facishare.crm.sfa.predefine.service.cpq.model.CheckBomModel.CheckResult;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.resource.ObjectResource;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> 2020-02-27
 * @instruction BOM对外暴露的接口
 */
@ServiceModule("bom")
@Component
@Slf4j
public class BomExternalService {
    @Autowired
    private BomCoreService bomCoreService;


    @Autowired
    private BomCoreV3Service bomCoreV3Service;

    @Autowired
    private BomPriceService bomPriceService;

    @Autowired
    private BomService bomService;
    @Autowired
    private BomConstraintService bomConstraintService;

    @Autowired
    private ObjectResource objectResource;

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor executor;

    @ServiceMethod("haveConfigBOMPrivilege")
    @Deprecated
    public HaveConfigBOMPrivilege.Result haveConfigBOMPrivilege(ServiceContext serviceContext) {
        return HaveConfigBOMPrivilege.Result.builder()
                .value(bomCoreService.haveConfigBOMPrivilege(serviceContext.getUser()))
                .build();
    }

    @ServiceMethod("existConstraint")
    public CheckBomModel.CheckResult existConstraint(ServiceContext serviceContext, CheckBomModel.CheckArgs arg) {
        boolean exist = bomCoreService.existConstraint(serviceContext.getUser(), arg);
        return CheckBomModel.CheckResult.builder()
                .exist(exist)
                .build();
    }
    @ServiceMethod("nodeList")
    public NodeListModel.Result rootNodeList(ServiceContext serviceContext, NodeListModel.Arg arg) {
        SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getQueryInfo());
        return bomCoreService.nodeList(serviceContext.getUser(), searchTemplateQuery, arg.getIsQueryRoot());
    }

    @ServiceMethod("bomDeploy")
    @MetadataTransactional
    public BomTreeModel.Result bomDeploy(ServiceContext serviceContext, BomTreeModel.Arg arg) {
        StopWatch stopWatch = StopWatch.create("BomExternalService#bomDeploy");
        if (Objects.isNull(arg)) {
            return BomTreeModel.Result.builder().isSuccess(true).build();
        }
        if (StringUtils.isBlank(arg.getRootProductId())) {
            return BomTreeModel.Result.builder().isSuccess(false).message(I18N.text(BomI18NKeyUtil.SFA_MISS_PARAM)).build();
        }
        List<ObjectDataDocument> delList = CollectionUtils.nullToEmpty(arg.getDeletedBomAndGroupList());
        delList.removeIf(o-> Objects.isNull(o)||Objects.isNull(o.getId()));
        try {
            List<IObjectData> updateList = bomCoreV3Service.saveBomTreeV3(serviceContext.getUser(), CollectionUtils.nullToEmpty(arg.getNodeList()), delList, arg.getRootProductId(), stopWatch,arg);
            bomCoreV3Service.updateProductIsPackage(serviceContext.getUser(), Lists.newArrayList(arg.getRootProductId()), Boolean.TRUE);
            stopWatch.lap("updateProductIsPackage");
            return BomTreeModel.Result.builder().isSuccess(true).dataList(ObjectDataDocument.ofList(updateList)).build();
        } finally {
            stopWatch.logSlow(1000);
        }

    }

    @ServiceMethod("syncToOtherNode")
    @MetadataTransactional
    public BomTreeModel.Response bomInfoSyncToOthers(ServiceContext serviceContext, BomTreeModel.Param arg) {
        if (!GrayUtil.bomMasterSlaveMode(serviceContext.getTenantId())) {
            List<String> failList = bomCoreV3Service.execute(serviceContext.getUser(), arg.getNodeList(), arg.getNode(), arg.isSame());
            return BomTreeModel.Response.builder().isSuccess(true).failList(failList).build();
        }
        return BomTreeModel.Response.builder().isSuccess(true).failList(Lists.newArrayList()).build();
    }

    @ServiceMethod("query_bom_price")
    public List<ObjectDataDocument> queryBomPrice(ServiceContext context, QueryBomPriceModel.Param entity) {
        //并发处理时，子线程上下文丢失，需要在此设置
        if(RequestContextManager.getContext() == null) {
            RequestContextManager.setContext(context.getRequestContext());
        }
        return bomPriceService.queryBomPrice(context, entity);
    }

    @ServiceMethod("batch_query_bom_price")
    public Map<String, List<ObjectDataDocument>> batchQueryBomPrice(ServiceContext context, QueryBomPriceModel.BatchParam batchParam) {
        Map<String, List<ObjectDataDocument>> map = Maps.newHashMap();
        if(batchParam == null || CollectionUtils.empty(batchParam.getNewBomMap())){
            return map;
        }
        StopWatch stopWatch = StopWatch.create("BomExternalService#batchQueryBomPrice");

        List<QueryBomPriceModel.Param> bomCoreList = buildBomCoreList(batchParam);
        if(CollectionUtils.empty(bomCoreList)){
            return map;
        }
        if(bomCoreList.size() == 1){
            QueryBomPriceModel.Param param = bomCoreList.get(0);
            List<ObjectDataDocument> result = queryBomPrice(context, param);
            map.put(param.getVirtualId(), result);
            stopWatch.lap("batchQueryBomPrice-single");
        } else {
            try {
                CompletableFuture[] cfs = bomCoreList.stream().map(x ->
                        CompletableFuture.supplyAsync(() -> queryBomPrice(context, x), executor).whenCompleteAsync((r, e) -> {
                            map.put(x.getVirtualId(), r);
                        }, executor)
                ).toArray(CompletableFuture[]::new);
                CompletableFuture.allOf(cfs).join();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                if(e.getCause() != null && e.getCause() instanceof ValidateException){
                    throw new ValidateException(e.getCause().getMessage());
                } else {
                    throw new ValidateException(e.getMessage());
                }
            }
            stopWatch.lap("batchQueryBomPrice-multi");
        }
        stopWatch.logSlow(300);
        return map;
    }

    private List<QueryBomPriceModel.Param> buildBomCoreList(QueryBomPriceModel.BatchParam batchParam) {
        List<QueryBomPriceModel.Param> bomCoreList = Lists.newArrayList();
        if(CollectionUtils.empty(batchParam.getNewBomMap())){
            return bomCoreList;
        }
        batchParam.getNewBomMap().forEach((k, v) -> {
            QueryBomPriceModel.Param param = QueryBomPriceModel.Param.builder()
                    .rootRowId(k)
                    .coreId(v.getCoreId())
                    .nodeBomCoreType(v.getNodeBomCoreType())
                    .nodeBomCoreVersion(v.getNodeBomCoreVersion())
                    .newBomPath(v.getNewBomPath())
                    .priority(v.isPriority())
                    .rootBomId(v.getRootBomId())
                    .rootSubtotal(v.getRootSubtotal())
                    .rootAmount(v.getRootAmount())
                    .priceBookDiscount(v.getPriceBookDiscount())
                    .bomList(v.getBomList())
                    .apiName(batchParam.getApiName())
                    .detailApiName(batchParam.getDetailApiName())
                    .accountId(batchParam.getAccountId())
                    .partnerId(batchParam.getPartnerId())
                    .noCalPrice(batchParam.isNoCalPrice())
                    .switchMasterPriceBook(batchParam.isSwitchMasterPriceBook())
                    .mcCurrency(batchParam.getMcCurrency())
                    .calculateArg(batchParam.getCalculateArg())
                    .objectData(batchParam.getObjectData())
                    .details(batchParam.getDetails())
                    .virtualId(k)
                    .build();
            bomCoreList.add(param);
        });
        return bomCoreList;
    }

    @ServiceMethod("get_product_group_by_ids")
    public List<ObjectDataDocument> getProductGroupByIds(ServiceContext context, List<String> ids) {
        return bomService.getProductGroupByIds(context, ids);
    }

    @ServiceMethod("getAllParentProdList")
    public GetAllParentProdListModel.Result getAllParentProdList(ServiceContext context, GetAllParentProdListModel.Arg arg) {
        return bomService.getAllParentProdList(context, arg);
    }

    @ServiceMethod("check_bom")
    public CheckProductBomModel.Result checkBom(ServiceContext serviceContext, CheckProductBomModel.Arg arg) {
        arg.setSelectionPage(true);
        return bomService.checkBom(serviceContext.getUser(), arg);
    }

    @ServiceMethod("check_bom_constraint")
    public CheckBomModel.Result checkBomConstraint(ServiceContext serviceContext, CheckBomModel.Args arg) {
        return bomConstraintService.checkBomConstraint(serviceContext.getUser(), arg);
    }


    @ServiceMethod("query_all_sub_bom")
    public QueryAllSubBomModel.Result queryAllSubBom(ServiceContext serviceContext, QueryAllSubBomModel.Arg arg) {
        return bomService.queryAllSubBomCore(serviceContext.getUser(), arg);
    }

    @ServiceMethod("check_bom_cycle")
    public Boolean checkBomCycle(ServiceContext serviceContext, BomTreeModel.CycleArg arg) {
        if (!Objects.isNull(arg.getMasterData())&&CollectionUtils.notEmpty(arg.getNodeList())) {
            bomCoreV3Service.checkBomCycle(serviceContext.getUser(), arg.getMasterData().toObjectData(), ObjectDataDocument.ofDataList(arg.getNodeList()),1);
        }
        return true;
    }

    /**
     * 提供手动创建BOM的接口，自动和手动及各自的新增和编辑统一走此接口
     * @param serviceContext
     * @param arg
     * @return
     */
    @ServiceMethod("create")
    public CreateBomModel.Result createBom(ServiceContext serviceContext, CreateBomModel.Arg arg) {
        boolean flag = true;
        String msg = "ok";
        try {
            StopWatch stopWatch = StopWatch.create(this.getClass().getSimpleName() + "#createBom");
            RequestContext requestContext = serviceContext.getRequestContext();
            Optional<CreateBomModel.CreateBomInfo> root = arg.getBomList().stream().filter(CreateBomModel.CreateBomInfo::isRoot).findFirst();
            if (!root.isPresent()) {
                log.error("createBom:创建标准BOM时，没有获取到母件节点："+I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
            }
            //手动生成时，需校验是否已经关联过标准BOM，自动生成时，则不需要校验，考虑到变更时的场景，子件已经发生变化，所以都需要重新生成
            if(!arg.isAutoCreate()) {
                //是否已经生成过标准BOM校验
                bomService.existedCreateBomCheck(serviceContext.getUser(), root.get().getObjectApiName(), root.get().getFromId());
            }
            stopWatch.lap("createBom:existedCreateBomCheck");
            //如果是添加本身是标准，则将标准BOM的信息更新到标准BOM相关字段上
            boolean needNext = bomService.standardBomHandler(serviceContext.getUser(), root.get(), arg);
            stopWatch.lap("createBom:bomService.standardBomHandler");
            if(needNext) {
                //预处理数量关系
                bomService.preHandlerCreateBom(serviceContext.getUser(), arg, root.get());
                stopWatch.lap("createBom:preProcessCreateBom");
                //查重校验
                bomService.checkDuplicateBom(serviceContext.getUser(), arg);
                stopWatch.lap("checkDuplicateBom");
                CreateBomModel.CoreArg coreArg = bomService.buildCoreArg(serviceContext.getUser(), arg);
                stopWatch.lap("createBom:bomService.buildCoreArg");
                String body = JacksonUtils.toJson(coreArg);
                Object result = objectResource.executeAction(requestContext, BomCoreConstants.DESC_API_NAME, ObjectAction.CREATE.getActionCode(), body);
                stopWatch.lap("createBom:objectResource.executeAction");
                if (result != null) {
                    BaseObjectSaveAction.Result rst = (BaseObjectSaveAction.Result) result;
                    String bomCoreId = rst.getObjectData().getId();
                    bomService.updateSourceObjInfo(serviceContext.getUser(), bomCoreId, arg);
                    stopWatch.lap("createBom:bomService.updateSourceObjInfo");
                    flag = true;
                } else {
                    flag = false;
                    msg = I18N.text(SFAI18NKeyUtil.SFA_BILL_CREATE_STANDARD_BOM_RESPONSE_NULL);
                }
            } else {
                flag = false;
                msg = I18N.text(SFAI18NKeyUtil.SFA_BILL_ASSOCIATED_STANDARD_BOM_WARN);
            }
        } catch (Exception e) {
            flag = false;
            msg = e.getMessage();
            if(arg.isAutoCreate()) {
                log.error("createBom:自动创建标准BOM时，"+e.getMessage(), e);
            } else {
                log.error("createBom:手动创建标准BOM时，"+e.getMessage(), e);
            }
        }
        log.info(String.format("createBom: 返回结果[%s], %s", flag, msg));
        return CreateBomModel.Result.builder().success(flag).msg("createBom:"+msg).build();
    }

    @ServiceMethod("query_bom_tree")
    public BomTreeModel.BomTreeResult queryBomTree(ServiceContext serviceContext, BomTreeModel.BomTreeArg arg) {
        return bomCoreService.queryBomTree(serviceContext.getUser(), arg);
    }

    @ServiceMethod("expression_check")
    public BomFormulaModel.CheckResult expressionCheck(ServiceContext serviceContext, BomFormulaModel.CheckArg arg) {
        return bomConstraintService.expressionCheck(serviceContext, arg);
    }

    @ServiceMethod("formula_calculate")
    public BomFormulaModel.CalculateResult formulaCalculate(ServiceContext serviceContext, BomFormulaModel.CalculateArg arg) {
        return bomConstraintService.formulaCalculate(serviceContext, arg);
    }

    @ServiceMethod("formula_check")
    public BomFormulaModel.FormulaCheckResult formulaCheck(ServiceContext serviceContext, BomFormulaModel.FormulaCheckArg arg) {
        return bomConstraintService.formulaCheck(serviceContext, arg);
    }

    @ServiceMethod("calculate_by_apl")
    public BomFormulaModel.AplResult calculateByApl(ServiceContext serviceContext, BomFormulaModel.AplArg arg) {
        return bomConstraintService.calculateByApl(serviceContext, arg);
    }
}