package com.facishare.domain.sfa.predefine.service.rebatecoupon

import com.facishare.crm.rest.FundAccountProxy
import com.facishare.crm.rest.dto.FundAccountModel
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.rebatecoupon.BaseServiceManager
import com.facishare.crm.sfa.predefine.service.rebatecoupon.RebateAutoBackService
import com.facishare.crm.sfa.predefine.service.rebatecoupon.RebateReduceService
import com.facishare.crm.sfa.predefine.service.rebatecoupon.RebateServiceImpl
import com.facishare.crm.sfa.predefine.service.rebatecoupon.model.*
import com.facishare.crm.constants.RebatePolicyConstants
import com.facishare.crm.sfa.cache.RedisDataAccessor
import com.facishare.crm.sfa.task.ManualCreateRebateProducer
import com.facishare.crm.sfa.utilities.constant.dmConstants.DmDefineConstants
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.SFABizLogUtil
import com.facishare.domain.sfa.BaseGroovyTest
import com.facishare.paas.appframework.core.model.InfraServiceFacade
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.LoggerFactory
import org.spockframework.runtime.Sputnik

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([SFABizLogUtil.class, GrayUtil.class, DmDefineConstants.class])
@PowerMockIgnore(["javax.management.*", "com.facishare.paas.appframework.core.util.UdobjGrayConfig"])
class RebateServiceTest extends BaseGroovyTest {
    def logger = LoggerFactory.getLogger(RebateService.class)
    def baseServiceManager = PowerMockito.spy(new BaseServiceManager())
    def serviceFacade = PowerMockito.mock(ServiceFacade)
    def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
    def rebateService = PowerMockito.spy(new RebateService())
    def infraServiceFacade = PowerMockito.mock(InfraServiceFacade)
    def rebateServiceImpl =  PowerMockito.spy(new RebateServiceImpl())
    def autoBackService =  PowerMockito.spy(new RebateAutoBackService())
    def reduceService =  PowerMockito.spy(new RebateReduceService())
    def setup() {
        Whitebox.setInternalState(RebateService, "log", logger)
        PowerMockito.mockStatic(GrayUtil.class)
        serviceContext = getServiceContext("SFA", "query")
        Whitebox.setInternalState(rebateService, "infraServiceFacade", infraServiceFacade)
        Whitebox.setInternalState(rebateService, "baseServiceManager", baseServiceManager)
        Whitebox.setInternalState(rebateService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(rebateService, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
        Whitebox.setInternalState(rebateService, "autoBackService", autoBackService)
        Whitebox.setInternalState(rebateService, "reduceService", reduceService)

        PowerMockito.doReturn(domain()).when(infraServiceFacade, "findPluginParam", any(), any(), any())
        PowerMockito.doReturn(rebateServiceImpl).when(baseServiceManager, "getService", any())
    }

    def "test query"() {
        given:
        PowerMockito.when(GrayUtil.isSfaAuditLog(any())).thenReturn(false)
        PowerMockito.doReturn(RebateCouponQuery.Result.builder().build()).when(rebateServiceImpl, "query", any())
        RebateCouponQuery.Arg arg = RebateCouponQuery.Arg.builder().user(user).masterObjectApiName("SalesOrderObj").build()

        when:
        RebateCouponQuery.Result result = rebateService.query(serviceContext,arg)
        then:
        result !=null
    }

    def "test getConditionFields"() {
        given:
        PowerMockito.doReturn(RebateCouponConditionField.Result.builder().build()).when(rebateServiceImpl, "getConditionFields", any())
        RebateCouponConditionField.Arg arg = RebateCouponConditionField.Arg.builder().user(user).masterObjectApiName("SalesOrderObj").build()

        when:
        RebateCouponConditionField.Result result = rebateService.getConditionFields(serviceContext,arg)
        then:
        result !=null
    }

    def "test matchAmortize"() {
        given:
        PowerMockito.when(GrayUtil.isSfaAuditLog(any())).thenReturn(false)
        PowerMockito.doReturn(RebateCouponMatch.Result.builder().build()).when(rebateServiceImpl, "matchAmortize", any(), any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenCoupon", any())
        RebateCouponMatch.Arg arg = RebateCouponMatch.Arg.builder().user(user).masterObjectApiName("SalesOrderObj").build()

        when:
        RebateCouponMatch.Result result = rebateService.matchAmortize(serviceContext,arg)
        then:
        result !=null
    }

    def "test autoBack"() {
        given:
        PowerMockito.doReturn(RebateAutoBack.Result.builder().build()).when(autoBackService, "autoBack", any())
        RebateAutoBack.Arg arg = RebateAutoBack.Arg.builder().user(user).masterObjectApiName("SalesOrderObj").build()

        when:
        RebateAutoBack.Result result = rebateService.autoBack(serviceContext,arg)
        then:
        result !=null
    }

    def "test autoReduce"() {
        given:
        PowerMockito.doReturn(RebateReduce.Result.builder().build()).when(reduceService, "autoReduce", any())
        RebateReduce.Arg arg = RebateReduce.Arg.builder().user(user).tenantId("82681").build()

        when:
        RebateReduce.Result result = rebateService.autoReduce(serviceContext,arg)
        then:
        result !=null
    }

    def "test autoReduceAmount"() {
        given:
        PowerMockito.doReturn(RebateReduce.Result.builder().build()).when(reduceService, "autoReduce", any())
        RebateReduce.Arg arg = RebateReduce.Arg.builder().user(user).tenantId("82681").build()

        when:
        RebateReduce.Result result = rebateService.autoReduceAmount(serviceContext,arg)
        then:
        result !=null
    }

    def "test canUseAmount"() {
        given:
        PowerMockito.when(GrayUtil.isSfaAuditLog(any())).thenReturn(false)
        PowerMockito.doReturn(RebateUseAmount.Result.builder().build()).when(rebateServiceImpl, "canUseAmount", any(),any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenCoupon", any())
        RebateUseAmount.Arg arg =new RebateUseAmount.Arg()
        arg.setUser(user)
        arg.setAmount(BigDecimal.ONE)

        when:
        RebateUseAmount.Result result = rebateService.canUseAmount(serviceContext,arg)
        then:
        result !=null
    }

    def "test productRebateUnusedAmount"() {
        given:
        PowerMockito.when(GrayUtil.isSfaAuditLog(any())).thenReturn(false)
        PowerMockito.doReturn(RebateUseAmount.ProductResult.builder().build()).when(reduceService, "productRebateUnusedAmount", any())
        RebateUseAmount.ProductArg arg =new RebateUseAmount.ProductArg()
        arg.setUser(user)

        when:
        RebateUseAmount.ProductResult result = rebateService.productRebateUnusedAmount(serviceContext,arg)
        then:
        result !=null
    }

    def "test autoUse"() {
        given:
        PowerMockito.when(GrayUtil.isSfaAuditLog(any())).thenReturn(false)
        PowerMockito.doReturn(RebateCouponMatch.Result.builder().build()).when(rebateServiceImpl, "matchAmortize", any(), any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenCoupon", any())
        PowerMockito.doReturn(RebateCouponAutoUse.Result.builder().build()).when(rebateServiceImpl, "autoUse", any(),any())
        Map<String, Object> miscCoupon = Maps.newHashMap()
        miscCoupon.put("coupon",Lists.newArrayList(["id":"620e08848a103f0001c3a1a6","amount":200]))
        RebateCouponAutoUse.Arg arg =new RebateCouponAutoUse.Arg()
        arg.setUser(user)
        arg.setMasterObjectApiName("SalesOrderObj")
        arg.setMasterData(ObjectDataDocument.of(new ObjectData(["_id": "id", "account_id": "accountId2","misc_content":miscCoupon])))

        when:
        RebateCouponAutoUse.Result result = rebateService.autoUse(serviceContext,arg)
        then:
        result !=null
    }

    def "test manualCreate"() {
        given:
        def manualCreateRebateProducer =  PowerMockito.spy(new ManualCreateRebateProducer())
        Whitebox.setInternalState(rebateService, "manualCreateRebateProducer", manualCreateRebateProducer)
        IObjectData rebate = new ObjectData(["active_status": "enable", "account_id": "accountId2"])
        PowerMockito.when(serviceFacade.findObjectData(any() as User, anyString(), anyString())).thenReturn(rebate)
        PowerMockito.doNothing().when(manualCreateRebateProducer, "create", any(), any(), any())
        CreateRebateData.Arg arg = CreateRebateData.Arg.builder().objectId("111").rebatePolicyId("82681").objectApiName("SalesOrderObj").build()

        when:
        CreateRebateData.Result result = rebateService.manualCreate(serviceContext,arg)
        then:
        result !=null
    }

    def "test eachCreate"() {
        given:
        def manualCreateRebateProducer =  PowerMockito.spy(new ManualCreateRebateProducer())
        Whitebox.setInternalState(rebateService, "manualCreateRebateProducer", manualCreateRebateProducer)
        IObjectData rebate = new ObjectData(["active_status": "enable", "account_id": "accountId2","execute_mode":"each"])
        PowerMockito.when(serviceFacade.findObjectData(any() as User, anyString(), anyString())).thenReturn(rebate)
        PowerMockito.when(serviceFacade.findObjectDataIgnoreAll(any() as User, anyString(), anyString())).thenReturn(rebate)
        PowerMockito.doNothing().when(manualCreateRebateProducer, "create", any(), any(), any())
        CreateRebateData.Arg arg = CreateRebateData.Arg.builder().objectId("111").rebatePolicyId("82681").objectApiName("SalesOrderObj").build()

        when:
        CreateRebateData.Result result = rebateService.eachCreate(serviceContext,arg)
        then:
        result !=null
    }

    def "test queryRebateFields"() {
        given:
        def fundAccountProxy = PowerMockito.mock(FundAccountProxy)
        Whitebox.setInternalState(rebateService, "fundAccountProxy", fundAccountProxy)
        FundAccountModel.AuthorizeData authorizeData = new FundAccountModel.AuthorizeData()
        authorizeData.setAccessModule("2")
        authorizeData.setTradeAmountFieldapiname("trade_amount")
        FundAccountModel.Result result1 = new FundAccountModel.Result()
        result1.setDatas(Lists.newArrayList(authorizeData))
        FundAccountModel.AuthorizeResult authResult = new FundAccountModel.AuthorizeResult()
        authResult.setResult(result1)
        PowerMockito.doReturn(authResult).when(fundAccountProxy, "getAuthorization", any(), any())
        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenRebate", any())
        RebateFields.Arg arg = RebateFields.Arg.builder().id("123").masterObjectApiName("SalesOrderObj").build()

        when:
        RebateFields.Result result = rebateService.queryRebateFields(serviceContext,arg)
        then:
        result !=null
    }


def "test expectedByPolicy"() {
    given:
    def manualCreateRebateProducer = PowerMockito.spy(new ManualCreateRebateProducer())
    Whitebox.setInternalState(rebateService, "manualCreateRebateProducer", manualCreateRebateProducer)
    IObjectData rebate = new ObjectData(["active_status": "enable", "account_id": "accountId2", "execute_mode": "each"])
    PowerMockito.when(serviceFacade.findObjectDataIgnoreAll(any() as User, anyString(), anyString())).thenReturn(rebate)
    PowerMockito.doNothing().when(manualCreateRebateProducer, "create", any(), any(), any())
    
    ExpectedRebateData.Arg arg = ExpectedRebateData.Arg.builder()
            .rebatePolicyId("82681")
            .requestId("req123")
            .objectData(ObjectDataDocument.of(["_id": "id", "account_id": "accountId2"]))
            .objectApiName("SalesOrderObj")
            .build()

    when:
    ExpectedRebateData.Result result = rebateService.expectedByPolicy(serviceContext, arg)
    
    then:
    result != null
    result.getResultCode() == RebatePolicyConstants.ManualCreate.SUCCESS
}

def "test queryExpected"() {
    given:
    def redisDataAccessor = PowerMockito.mock(RedisDataAccessor)
    Whitebox.setInternalState(rebateService, "redisDataAccessor", redisDataAccessor)
    
    ExpectedRebateData.Arg arg = ExpectedRebateData.Arg.builder()
            .requestId("req123")
            .build()
    
    String key = RebatePolicyConstants.EXPECTED_REBATE_REDIS_KEY_PREFIX + serviceContext.getTenantId() + "req123"
    PowerMockito.when(redisDataAccessor.get(key)).thenReturn("100.50")

    when:
    ExpectedRebateData.QueryResult result = rebateService.queryExpected(serviceContext, arg)
    
    then:
    result != null
    result.getResultCode() == RebatePolicyConstants.ManualCreate.SUCCESS
    result.getAmount() == new BigDecimal("100.50")
}

def "test queryExpected with null value"() {
    given:
    def redisDataAccessor = PowerMockito.mock(RedisDataAccessor)
    Whitebox.setInternalState(rebateService, "redisDataAccessor", redisDataAccessor)
    
    ExpectedRebateData.Arg arg = ExpectedRebateData.Arg.builder()
            .requestId("req123")
            .build()
    
    String key = RebatePolicyConstants.EXPECTED_REBATE_REDIS_KEY_PREFIX + serviceContext.getTenantId() + "req123"
    PowerMockito.when(redisDataAccessor.get(key)).thenReturn(null)
    PowerMockito.when(redisDataAccessor.get(key+"err")).thenReturn("错误消息")

    when:
    ExpectedRebateData.QueryResult result = rebateService.queryExpected(serviceContext, arg)
    
    then:
    result != null
    result.getResultCode() == RebatePolicyConstants.ManualCreate.FAIL
    result.getAmount() == BigDecimal.ZERO
    result.getMessage() == "错误消息"
}


    def domain() {
        def domain = new DomainPluginParam()
        def detail = new DomainPluginParam.DetailObj()
        detail.setObjectApiName("SalesOrderObj")
        Map<String, String> fieldMapping = Maps.newHashMap()
        fieldMapping.put("misc_content", "misc_content")
        detail.setFieldMapping(fieldMapping)
        domain.setDetails([detail])
        return domain
    }
}
