package com.facishare.crm.sfa.predefine.action

import cn.hutool.extra.spring.SpringUtil
import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.cache.RedisDataAccessor
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreV3Service
import com.facishare.crm.sfa.predefine.service.task.ProductStatusChangeTaskService
import com.facishare.crm.sfa.utilities.util.BomUtils
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction
import com.facishare.paas.appframework.core.predef.action.StandardAction
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.util.SpringContextUtil
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.ArgumentMatchers.anyList
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([BaseObjectSaveAction.class, PreDefineAction.class, StandardAction.class, SpringUtil.class,BomUtils.class,UdobjGrayConfig.class])
@SuppressStaticInitializationFor(["com.facishare.paas.I18N", "com.facishare.paas.appframework.core.predef.action.StandardAction", "com.facishare.paas.appframework.core.model.PreDefineAction", "com.facishare.paas.metadata.util.SpringUtil","com.facishare.crm.sfa.utilities.util.BomUtils","com.facishare.paas.appframework.core.util.UdobjGrayConfig"])
class BomCoreEditActionTest extends EnhancedBaseGroovyTest {
    @Shared
    protected ActionContext actionContext
    @Shared
    protected BomCoreEditAction tester
    @Shared
    protected RedisDataAccessor redisDataAccessor
    @Shared
    protected User user

    def setupSpec() {
        initSpringContext()
        tester = new BomCoreEditAction()
        actionContext = Mock(ActionContext)
        user = Spy(new User("1", "1"))
        user.getUpstreamOwnerIdOrUserId()>>"1"
        actionContext.getUser() >> user
        Whitebox.setInternalState(tester, "actionContext", actionContext)
    }

    def setup() {
        PowerMockito.mockStatic(PreDefineAction.class)
        PowerMockito.mockStatic(StandardAction.class)
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(I18N.I18NContext.class)
        PowerMockito.mockStatic(BomUtils.class)
        PowerMockito.mockStatic(UdobjGrayConfig.class)
    }

    def "test dataInit method"() {
        given:
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("product_id", productId)
        data.put("_id", id)
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(data)
        arg.setDetails(detailsArg())
        Whitebox.setInternalState(tester, "arg", arg)
        tester.actionContext = actionContext
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        redisDataAccessor = PowerMockito.mock(RedisDataAccessor)
        Whitebox.setInternalState(tester, "redisDataAccessor", redisDataAccessor)
        PowerMockito.doReturn(null).when(serviceFacade, "findBySearchQueryWithFieldsIgnoreAll", any(), any(), any(), any())
        def bomCoreV3Service = PowerMockito.mock(BomCoreV3Service)
        Whitebox.setInternalState(tester, "bomCoreV3Service", bomCoreV3Service)
        PowerMockito.doReturn(detailsArg()).when(bomCoreV3Service,"analyzeBOMV3Tree",any() as User,anyList(),anyString(),anyInt(),anyList(),anyString())
        when:
        Whitebox.invokeMethod(tester, "dataInit", arg)
        then:
        notThrown(Exception)
        where:
        productId | id  | rootId | objectList
        "123"     | "1" | "111"  | dataEmptyList()
    }

    def "test doUpdateData method"() {
        given:
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("product_id", productId)
        data.put("_id", id)
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(data)
        arg.setDetails(detailsArg())
        Whitebox.setInternalState(tester, "arg", arg)
        Whitebox.setInternalState(tester, "groupToAdd", objectList)
        Whitebox.setInternalState(tester, "groupToUpdate", objectList)
        Whitebox.setInternalState(tester, "groupToDelete", objectList)
        Whitebox.setInternalState(tester, "detailsToAdd", objectList)
        Whitebox.setInternalState(tester, "dbDetailDataMap", ["BOMObj":objectList])
        Whitebox.setInternalState(tester, "objectDescribe", new ObjectDescribe())
        Whitebox.setInternalState(tester, "objectData", new ObjectData("object_describe_api_name": "BOMObj","_id":"1","bom_path":"p1"))
        tester.actionContext = actionContext
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        redisDataAccessor = PowerMockito.mock(RedisDataAccessor)
        Whitebox.setInternalState(tester, "redisDataAccessor", redisDataAccessor)
        PowerMockito.doReturn(null).when(serviceFacade, "findBySearchQueryWithFieldsIgnoreAll", any(), any(), any(), any())
        when:
        try {
            tester.doUpdateData()
        } catch (Exception e) {

        }
        then:
        notThrown(Exception)
        where:
        productId | id  | rootId | objectList
        "123"     | "1" | "111"  | [new ObjectData("object_describe_api_name": "BOMObj","_id":"1","bom_path":"p1")]
    }

    def "test prepareDetailObjectData method"() {
        given:
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("product_id", productId)
        data.put("_id", id)
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(data)
        arg.setDetails(detailsArg())
        Whitebox.setInternalState(tester, "arg", arg)
        Whitebox.setInternalState(tester, "groupToAdd", objectList)
        Whitebox.setInternalState(tester, "groupToUpdate", objectList)
        Whitebox.setInternalState(tester, "groupToDelete", objectList)
        Whitebox.setInternalState(tester, "detailsToAdd", objectList)
        Whitebox.setInternalState(tester, "dbDetailDataMap", ["BOMObj":objectList])
        Whitebox.setInternalState(tester, "objectDescribe", new ObjectDescribe())
        Whitebox.setInternalState(tester, "objectData", new ObjectData("object_describe_api_name": "BOMObj","_id":"1","bom_path":"p1"))
        tester.actionContext = actionContext
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        redisDataAccessor = PowerMockito.mock(RedisDataAccessor)
        Whitebox.setInternalState(tester, "redisDataAccessor", redisDataAccessor)
        PowerMockito.doReturn(null).when(serviceFacade, "findBySearchQueryWithFieldsIgnoreAll", any(), any(), any(), any())
        when:
        try {
            tester.prepareDetailObjectData()
        } catch (Exception e) {
        }
        then:
        notThrown(Exception)
        where:
        productId | id  | rootId | objectList
        "123"     | "1" | "111"  | [new ObjectData("object_describe_api_name": "BOMObj","_id":"1","bom_path":"p1")]
    }

    def "test handUpdateData method"() {
        given:
        Whitebox.setInternalState(tester, "detailsToUpdate", updateList)
        tester.actionContext = actionContext
        def productStatusChangeTaskService = PowerMockito.mock(ProductStatusChangeTaskService)
        Whitebox.setInternalState(tester, "productStatusChangeTaskService", productStatusChangeTaskService)
        PowerMockito.doNothing().when(productStatusChangeTaskService, "createOrUpdateTask", actionContext.getTenantId(), Lists.newArrayList(), Lists.newArrayList(""))
        when:
        Whitebox.invokeMethod(tester, "handUpdateData", bomMap, bomIdMap)
        then:
        re
        where:
        re   | updateList                                                                                                                                                                                                                                                                                                                                                                                                       | bomMap                  | bomIdMap
        true | updateList(new ObjectData("_id": "0", "name": "产品2", "object_describe_api_name": "BOMObj", "is_package": false, "action_type": "update"))                                                                                                                                                                                                                                                                        | ["id1": ["id2", "id3"]] | ["id1": new ObjectData("_id": "id1")]
        true | updateList(new ObjectData("_id": "id1", "is_required": true, "price_editable": false, "enabled_status": false, "name": "产品2", "object_describe_api_name": "BOMObj", "is_package": false, "action_type": "update"))                                                                                                                                                                                               | bomMap()                | ["id1": new ObjectData("_id": "id1"), "id2": new ObjectData("_id": "id2", "adjust_price": "1")]
        true | [new ObjectData("_id": "id1", "is_required": true, "price_editable": false, "enabled_status": false, "name": "产品2", "object_describe_api_name": "BOMObj", "is_package": false, "action_type": "update"), new ObjectData("_id": "id3", "is_required": true, "price_editable": false, "enabled_status": false, "name": "产品2", "object_describe_api_name": "BOMObj", "is_package": false, "action_type": "update")] | bomMap()                | ["id1": new ObjectData("_id": "id1"), "id2": new ObjectData("_id": "id2", "adjust_price": "1")]
    }

    def bomMap() {
        def map = Maps.newHashMap()
        def set = Sets.newHashSet()
        set.add("id2")
        set.add("id3")
        map.put("id1", set)
        map
    }

    def "test handDeletedData method"() {
        given:
        Whitebox.setInternalState(tester, "detailsToDelete", deleteList)
        Whitebox.setInternalState(tester, "groupToDelete", groupToDeleteList)
        tester.actionContext = actionContext
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.doReturn([new ObjectData("_id": "g")]).when(serviceFacade, "findObjectDataByIdsIgnoreAll", any(), any(), any())
        when:
        Whitebox.invokeMethod(tester, "handDeletedData", bomMap, groupBomMap, bomIdMap)
        then:
        groupToDeleteList.size() == re
        where:
        re | deleteList   | groupToDeleteList                                                                                                                                                   | bomMap   | bomIdMap                                                                                             | groupBomMap
        0  | deleteList() | []                                                                                                                                                                  | bomMap() | ["id1": new ObjectData("_id": "id1")]                                                                | ["g1": [new ObjectData("_id": "id1", "product_group_id": "g1")]]
        1  | deleteList() | [new ObjectData("_id": "g1", "name": "g1", "object_describe_api_name": "ProductGroupObj", "parent_bom_id": "1", "action_type": "delete")]                           | bomMap() | ["id1": new ObjectData("_id": "id1")]                                                                | ["g1": [new ObjectData("_id": "id1", "product_group_id": "g1")]]
        2  | deleteList() | [new ObjectData("delete_child_bom": true, "_id": "g1", "name": "g1", "object_describe_api_name": "ProductGroupObj", "parent_bom_id": "1", "action_type": "delete")] | bomMap() | ["id1": new ObjectData("_id": "id1"), "id2": new ObjectData("_id": "id2", "product_group_id": "g1")] | ["g1": [new ObjectData("_id": "id1", "product_group_id": "g1")]]
    }

    def "test dealData method"() {
        given:
        tester.actionContext = actionContext
        when:
        Whitebox.invokeMethod(tester, "dealData", dbBomList, bomMap, groupBomMap, bomIdMap)
        then:
        groupBomMap.size() == re
        where:
        re | dbBomList                                                                                                                                              | bomMap | bomIdMap | groupBomMap
        0  | []                                                                                                                                                     | [:]    | [:]      | [:]
        0  | [new ObjectData("_id": "g1", "name": "g1", "object_describe_api_name": "BOMObj", "parent_bom_id": "1", "bom_path": "1.1")]                             | [:]    | [:]      | [:]
        1  | [new ObjectData("_id": "g1", "name": "g1", "object_describe_api_name": "BOMObj", "parent_bom_id": "1", "bom_path": "1.1.1", "product_group_id": "g1")] | [:]    | [:]      | [:]

    }

    def "test handParam method"() {
        given:
        Whitebox.setInternalState(tester, "detailsToUpdate", updateList)
        when:
        Whitebox.invokeMethod(tester, "handParam")
        then:
        updateList.size() == re
        where:
        re | updateList
        0  | []
        1  | [new ObjectData("_id": "g1", "name": "g1", "object_describe_api_name": "BOMObj", "parent_bom_id": "1", "bom_path": "1.1")]
        0  | [new ObjectData("_id": "g1", "name": "g1", "object_describe_api_name": "BOMObj", "parent_bom_id": "1", "bom_path": "1.1", "action_type": "delete")]
    }

    def detailsArg() {
        Map<String, List<ObjectDataDocument>> map = Maps.newHashMap()
        map.put("BOMObj", ObjectDataDocument.ofList([new ObjectData("_id": "2", "name": "产品1", "object_describe_api_name": "BOMObj", "parent_bom_id": "1", "action_type": "create")]))
        map.put("ProductGroupObj", ObjectDataDocument.ofList([new ObjectData("_id": "2","object_describe_api_name": "ProductGroupObj", "action_type": "create"),new ObjectData("_id": "2","object_describe_api_name": "ProductGroupObj", "action_type": "update"),new ObjectData("_id": "2","object_describe_api_name": "ProductGroupObj", "action_type": "delete")]))
        map
    }


    def "test create node"() {
        given:
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("product_id", productId)
        data.put("_id", id)
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(data)
        tester.actionContext = actionContext
        when:
        Whitebox.invokeMethod(tester, "createRootNode", arg, rootId, objectList)
        then:
        notThrown(Exception)
        where:
        productId | id  | rootId | objectList
        "123"     | "1" | "111"  | dataEmptyList()
        "123"     | "1" | "111"  | dataNEmptyList("123", "1")
    }

    def "test check method"() {
        given:
        Whitebox.setInternalState(tester, "detailsToUpdate", updateList)
        Whitebox.setInternalState(tester, "detailsToDelete", deleteList)
        Whitebox.setInternalState(tester, "detailsToAdd", addList)
        Whitebox.setInternalState(tester, "objectData", new ObjectData("product_id": "p1"))
        Whitebox.setInternalState(tester, "dbBomIdMap", dbBomIdMap)
        tester.actionContext = actionContext
        def bomCoreV3Service = PowerMockito.mock(BomCoreV3Service)
        Whitebox.setInternalState(tester, "bomCoreV3Service", bomCoreV3Service)
        when:
        try {
            Whitebox.invokeMethod(tester, "check")
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        addList                                                                                                        | updateList                                                                                                                | deleteList   | dbBomIdMap
        addList(null)                                                                                                  | updateList(new ObjectData("_id": "0", "name": "产品2", "object_describe_api_name": "BOMObj", "is_package": false))          | deleteList() | dbBomIdMap("0", new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "is_package": true))
        addList(null)                                                                                                  | updateList(new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "is_package": true, "parent_bom_id": "1")) | deleteList() | dbBomIdMap("1", new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "is_package": true))
        addList(new ObjectData("_id": "2", "name": "产品1", "object_describe_api_name": "BOMObj", "parent_bom_id": "1")) | updateList(null)                                                                                                          | deleteList() | dbBomIdMap("1", new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "is_package": true))
        addList(new ObjectData("_id": "2", "name": "产品2", "object_describe_api_name": "BOMObj", "product_id": "p1"))   | updateList(null)                                                                                                          | deleteList() | dbBomIdMap("1", new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "is_package": true))
    }

    def "test checkGroupRule method"() {
        given:
        Whitebox.setInternalState(tester, "detailsToUpdate", updateList)
        Whitebox.setInternalState(tester, "detailsToDelete", deleteList)
        Whitebox.setInternalState(tester, "detailsToAdd", addList)
        Whitebox.setInternalState(tester, "objectData", new ObjectData("product_id": "p1"))
        Whitebox.setInternalState(tester, "allGroupList", allGroupList)
        when:
        try {
            Whitebox.invokeMethod(tester, "checkGroupRule")
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        re
        where:
        re   | addList                                                                                                                                                                                                                                                                                                                                                                    | updateList       | deleteList   | allGroupList
        true | addList(new ObjectData("_id": "2", "name": "产品2", "object_describe_api_name": "BOMObj", "product_id": "p1"))                                                                                                                                                                                                                                                               | updateList(null) | deleteList() | []
        true | addList(new ObjectData("_id": "2", "name": "产品2", "object_describe_api_name": "BOMObj", "product_id": "p1"))                                                                                                                                                                                                                                                               | updateList(null) | deleteList() | [new ObjectData("_id": "g1")]
        true | [new ObjectData("_id": "1", "name": "产品1", "object_describe_api_name": "BOMObj", "product_id": "p1", "product_group_id": "g1", "selected_by_default": true, "action_type": "create"), new ObjectData("_id": "2", "selected_by_default": true, "action_type": "create", "name": "产品2", "object_describe_api_name": "BOMObj", "product_id": "p2", "product_group_id": "g1")] | updateList(null) | deleteList() | [new ObjectData("_id": "g1", "max_prod_count": "1")]
    }

    def dataEmptyList() {
        List<ObjectDataDocument> list = Lists.newArrayList()
        list
    }

    def dataNEmptyList(String productId, String id) {
        List<ObjectDataDocument> list = Lists.newArrayList()
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("product_id", productId)
        data.put("_id", id)
        list.add(data)
        list
    }

    def "check checkNodeBomCore"() {
        given:
        Whitebox.setInternalState(tester, "detailsToUpdate", updateList)
        Whitebox.setInternalState(tester, "detailsToDelete", deleteList)
        Whitebox.setInternalState(tester, "detailsToAdd", addList)
        Whitebox.setInternalState(tester, "objectData", new ObjectData("product_id": "p1"))
        Whitebox.setInternalState(tester, "dbBomIdMap", dbBomIdMap)
        when:
        try {
            Whitebox.invokeMethod(tester, "checkNodeBomCore",dbBomIdMap,addList,updateList)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        addList                                                                                                        | updateList                                                                                                                | deleteList   | dbBomIdMap
        addList(null)                                                                                                  | updateList(new ObjectData("_id": "0", "name": "产品2", "object_describe_api_name": "BOMObj", "is_package": false))          | deleteList() | dbBomIdMap("0", new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "is_package": true))
        addList(null)                                                                                                  | updateList(new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "is_package": true, "parent_bom_id": "1")) | deleteList() | dbBomIdMap("1", new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "is_package": true))
        addList(new ObjectData("_id": "2", "name": "产品1", "object_describe_api_name": "BOMObj", "parent_bom_id": "1")) | updateList(null)                                                                                                          | deleteList() | dbBomIdMap("1", new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "is_package": true))
        addList(new ObjectData("_id": "2", "name": "产品2", "object_describe_api_name": "BOMObj", "product_id": "p1"))   | updateList(null)                                                                                                          | deleteList() | dbBomIdMap("1", new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "is_package": true))
    }


    def dbBomIdMap(String key, ObjectData obj) {
        def map = Maps.newHashMap()
        map.put(key, obj)
        map
    }

    def addList(ObjectData obj) {
        List<IObjectData> list = Lists.newArrayList()
        if (obj != null) {
            list.add(obj)
        }
        list
    }

    def updateList(ObjectData obj) {
        List<IObjectData> list = Lists.newArrayList()
        if (obj != null) {
            list.add(obj)
        }
        list
    }

    def deleteList() {
        List<IObjectData> list = Lists.newArrayList()
        list.add(new ObjectData("name": "产品1", "object_describe_api_name": "BOMObj", "_id": "id1"))
        list.add(new ObjectData("name": "产品2", "object_describe_api_name": "BOMObj", "_id": "id2"))
        list
    }
}
