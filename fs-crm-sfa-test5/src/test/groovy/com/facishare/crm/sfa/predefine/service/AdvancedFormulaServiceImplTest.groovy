package com.facishare.crm.sfa.predefine.service

import com.alibaba.fastjson.JSON
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.model.QuoterModel
import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.metadata.cache.RedissonService
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance
import com.facishare.paas.appframework.metadata.search.Query
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.apache.commons.lang3.StringUtils
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import java.util.regex.Pattern

import static org.mockito.ArgumentMatchers.*

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([SFAConfigUtil.class, I18N.class])
@SuppressStaticInitializationFor(["com.facishare.crm.sfa.utilities.util.SFAConfigUtil"])
class AdvancedFormulaServiceImplTest extends RemoveUseless {
    @Shared
    private AdvancedFormulaServiceImpl tester
    @Shared
    private ServiceFacade serviceFacade
    @Shared
    private EnterpriseInitService enterpriseInitService
    @Shared
    private User user

    def setup() {
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(GrayUtil.class)
        PowerMockito.mockStatic(SFAConfigUtil.class)
        serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
    }

    def setupSpec(){
        tester = new AdvancedFormulaServiceImpl()
        enterpriseInitService = PowerMockito.mock(EnterpriseInitService)
        Whitebox.setInternalState(tester, "enterpriseInitService", enterpriseInitService)
        removeConfigFactory()
        removeI18N()
        user = PowerMockito.mock(User)
    }

    def "test validateByField"() {
        given:
        def fieldRelationCalculateService = PowerMockito.mock(FieldRelationCalculateService)
        Whitebox.setInternalState(tester, "fieldRelationCalculateService", fieldRelationCalculateService)
        PowerMockito.when(fieldRelationCalculateService.validateByFields(any(), any(), eq(true), any())).thenReturn(null)
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("BOMObj")
        IFieldDescribe fieldDescribe = new TextFieldDescribe();
        fieldDescribe.setApiName("f_abc")
        fieldDescribe.setDefaultValue(defaultValue)
        describe.setFieldDescribes(Lists.newArrayList(fieldDescribe))
        when:
        tester.validateByField(describe, fieldDescribe)
        then:
        1 == 1
        where:
        defaultValue                                                                                                                                                                | re
        "{\"return_type\":\"number\",\"expression\":\"IF(\$EXT#ATTR#6586c748d37628000788e522\$=='6586c748d37628000788e526', 5, 2)\",\"decimal_places\":2,\"default_to_zero\":true}" | 1
        "{\"return_type\":\"currency\",\"expression\":\"10000+(\$EXT#NON_ATTR#65e9375782823000078eeba6\$-2)/2*2000\",\"decimal_places\":2,\"default_to_zero\":true}"                | 1
    }

    def "test init"() {
        given:
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("BOMObj")
        IFieldDescribe fieldDescribe = new TextFieldDescribe();
        fieldDescribe.setApiName("f_abc")
        describe.setFieldDescribes(Lists.newArrayList(fieldDescribe))
        PowerMockito.when(enterpriseInitService.getDescribeFromLocalResource(any())).thenReturn(describe)
        PowerMockito.when(enterpriseInitService.initDescribeForTenant(any() as String, any() as ObjectDescribe)).thenReturn("")
        PowerMockito.when(enterpriseInitService.initMultiLayoutForOneTenant(any() as List, any() as String, any() as List)).thenReturn("")
        PowerMockito.when(enterpriseInitService.initPrivilegeRelate(any(), any(), any(), any(), any())).thenReturn("")
        PowerMockito.when(SFAConfigUtil.isCPQ(anyString())).thenReturn(true)
        when:
        tester.init("1")
        then:
        1 == 1
    }

    def "test domainList"() {
        given:
        QueryResult<IObjectData> queryResult = new QueryResult();
        queryResult.setData(dataList)
        def infraServiceFacade = PowerMockito.mock(InfraServiceFacade)
        Whitebox.setInternalState(tester, "infraServiceFacade", infraServiceFacade)
        PowerMockito.when(infraServiceFacade.findPluginInstanceByQuery(any(), any())).thenReturn(queryResult)
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("BOMObj")
        IFieldDescribe fieldDescribe = new TextFieldDescribe();
        fieldDescribe.setApiName("f_abc")
        describe.setFieldDescribes(Lists.newArrayList(fieldDescribe))
        PowerMockito.when(serviceFacade.findObjectList(any(), any())).thenReturn(Lists.newArrayList(describe))
        when:
        tester.domainList(user, "bom")
        then:
        1 == 1
        where:
        re | dataList
        1  | []
        1  | getDomainList("[{\"tenantId\":\"724865\",\"plugin_api_name\":\"bom\",\"ref_object_api_name\":\"SalesOrderObj\",\"record_type_list\":[\"default__c\"],\"plugin_param\":{\"details\":[{\"detailKey\":\"bom_detail\",\"fieldMapping\":{\"bom_id\":\"bom_id__c\",\"node_no\":\"node_no__c\",\"bom_type\":\"bom_type__c\",\"discount\":\"discount__c\",\"quantity\":\"quantity__c\",\"subtotal\":\"subtotal__c\",\"increment\":\"increment__c\",\"node_type\":\"node_type__c\",\"amount_any\":\"amount_any__c\",\"is_package\":\"is_package__c\",\"max_amount\":\"max_amount__c\",\"min_amount\":\"min_amount__c\",\"price_mode\":\"price_mode__c\",\"product_id\":\"product_id__c\",\"bom_core_id\":\"bom_core_id__c\",\"bom_version\":\"bom_version__c\",\"sales_price\":\"sales_price__c\",\"new_bom_path\":\"new_bom_path__c\",\"prod_pkg_key\":\"prod_pkg_key__c\",\"price_book_id\":\"price_book_id__c\",\"product_price\":\"product_price__c\",\"price_editable\":\"price_editable__c\",\"amount_editable\":\"amount_editable__c\",\"related_core_id\":\"related_core_id__c\",\"price_book_price\":\"price_book_price__c\",\"product_group_id\":\"product_group_id__c\",\"root_prod_pkg_key\":\"root_prod_pkg_key__c\",\"temp_node_group_id\":\"temp_node_group_id__c\",\"parent_prod_pkg_key\":\"parent_prod_pkg_key__c\",\"price_book_discount\":\"price_book_discount__c\",\"price_book_product_id\":\"price_book_product_id__c\"},\"objectApiName\":\"SalesOrderProductObj\"}],\"fieldMapping\":{}}}]")
    }

    def "test queryAdvancedFormulaList"() {
        given:
        QueryResult<IObjectData> queryResult = new QueryResult();
        queryResult.setData(dataList)
        def fieldRelationCalculateService = PowerMockito.mock(FieldRelationCalculateService)
        Whitebox.setInternalState(tester, "fieldRelationCalculateService", fieldRelationCalculateService)
        PowerMockito.when(fieldRelationCalculateService.computeCalculateRelationWithExt(any(), any(), any())).thenReturn(null)
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("BOMObj")
        describe.setFieldDescribes(getFields())
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.when(serviceFacade.findObject(any(), any())).thenReturn(describe)
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any(), any(), any())).thenReturn(queryResult)
        def query = new QuoterModel.Arg()
        query.setApiName(apiName)
        query.setQueryInfo(queryInfo)
        when:
        tester.queryAdvancedFormulaList(user, query)
        then:
        1 == 1
        where:
        dataList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | apiName                | queryInfo
        []                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | "SalesOrderProductObj" | "{\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"bom_id\",\"field_values\":[\"6656e111bff7980007efce31\",\"6656e111bff7980007efce32\",\"6641e5c6ab2ab00007bbfb03\",\"6641e5c6ab2ab00007bbfb04\",\"6656dcf6bff7980007ef1600\",\"663c71d935634d0007a80394\",\"663c71d935634d0007a80395\"],\"operator\":\"IN\"},{\"field_name\":\"ref_object_api_name\",\"field_values\":[\"SalesOrderProductObj\"],\"operator\":\"EQ\"}]}]}"
        [new ObjectData("_id": "1", "ref_field_name": "EXT#ATTR#6586c748d37628000788e522", "product_id": "p1", "formula": "{\"return_type\":\"number\",\"expression\":\"IF(\$EXT#ATTR#6586c748d37628000788e522\$=='6586c748d37628000788e526', 5, 2)\",\"decimal_places\":2,\"default_to_zero\":true}")]                                                                                                                                                                                                      | "SalesOrderProductObj" | "{\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"bom_id\",\"field_values\":[\"6656e111bff7980007efce31\",\"6656e111bff7980007efce32\",\"6641e5c6ab2ab00007bbfb03\",\"6641e5c6ab2ab00007bbfb04\",\"6656dcf6bff7980007ef1600\",\"663c71d935634d0007a80394\",\"663c71d935634d0007a80395\"],\"operator\":\"IN\"},{\"field_name\":\"ref_object_api_name\",\"field_values\":[\"SalesOrderProductObj\"],\"operator\":\"EQ\"}]}]}"
        [new ObjectData("_id": "1", "ref_field_name": "EXT#ATTR#6586c748d37628000788e522", "product_id": "p1", "formula": "{\"return_type\":\"currency\",\"expression\":\"IF(\\n    \$EXT#NON_ATTR#65954ea312e1da0006466941\$ ==10&&\$EXT#NON_ATTR#6596a8d92d49e20007f1a234\$ == 10,\\n    10,\\n    IF(\\n        \$EXT#NON_ATTR#65954ea312e1da0006466941\$ ==20 &&\$EXT#NON_ATTR#6596a8d92d49e20007f1a234\$ == 20,\\n        20,\\n        30\\n    ))\",\"decimal_places\":4,\"default_to_zero\":true}")] | "SalesOrderProductObj" | "{\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"bom_id\",\"field_values\":[\"6656e111bff7980007efce31\",\"6656e111bff7980007efce32\",\"6641e5c6ab2ab00007bbfb03\",\"6641e5c6ab2ab00007bbfb04\",\"6656dcf6bff7980007ef1600\",\"663c71d935634d0007a80394\",\"663c71d935634d0007a80395\"],\"operator\":\"IN\"},{\"field_name\":\"ref_object_api_name\",\"field_values\":[\"SalesOrderProductObj\"],\"operator\":\"EQ\"}]}]}"
        [new ObjectData("_id": "1", "ref_field_name": "field_G22r2__c", "product_id": "p1", "formula": "{\"return_type\":\"currency\",\"expression\":\"(\$quote_id__r.field_G22r22__c\$\",\"decimal_places\":2,\"default_to_zero\":true}")]                                                                                                                                                                                                                                                                  | "SalesOrderProductObj" | "{\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"bom_id\",\"field_values\":[\"6656e111bff7980007efce31\",\"6656e111bff7980007efce32\",\"6641e5c6ab2ab00007bbfb03\",\"6641e5c6ab2ab00007bbfb04\",\"6656dcf6bff7980007ef1600\",\"663c71d935634d0007a80394\",\"663c71d935634d0007a80395\"],\"operator\":\"IN\"},{\"field_name\":\"ref_object_api_name\",\"field_values\":[\"SalesOrderProductObj\"],\"operator\":\"EQ\"}]}]}"
        [new ObjectData("_id": "1", "ref_field_name": "field_G22r21__c", "product_id": "p1", "formula": "{\"return_type\":\"currency\",\"expression\":\"(\$order_id__r.field_G22r21__c\$\",\"decimal_places\":2,\"default_to_zero\":true}")]                                                                                                                                                                                                                                                                 | "SalesOrderProductObj" | "{\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"bom_id\",\"field_values\":[\"6656e111bff7980007efce31\",\"6656e111bff7980007efce32\",\"6641e5c6ab2ab00007bbfb03\",\"6641e5c6ab2ab00007bbfb04\",\"6656dcf6bff7980007ef1600\",\"663c71d935634d0007a80394\",\"663c71d935634d0007a80395\"],\"operator\":\"IN\"},{\"field_name\":\"ref_object_api_name\",\"field_values\":[\"SalesOrderProductObj\"],\"operator\":\"EQ\"}]}]}"
    }

    def "test translateFormula"() {
        given:
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("BOMObj")
        describe.setFieldDescribes(getFields())
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.when(serviceFacade.findObject(any(), any())).thenReturn(describe)
        String var = "\\\$(.*?)\\\$"
        if (StringUtils.isNotBlank(attr)) {
            data.set("attribute", getAttribute(attr))
        }
        if (StringUtils.isNotBlank(nonattr)) {
            data.set("nonstandardAttribute", getNonAttribute(nonattr))
        }
        when:
        tester.translateFormula(describe, Pattern.compile(var), getDataMap(data), data, "1")
        then:
        1 == 1
        where:
        data                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | attr                                                                                                                                                                                                                                                                                              | nonattr
        new ObjectData("_id": "1", "ref_field_name": "EXT#ATTR#6586c748d37628000788e522", "product_id": "1", "formula": "{\"return_type\":\"number\",\"expression\":\"IF(\$EXT#ATTR#6586c748d37628000788e522\$=='6586c748d37628000788e526', 5, 2)\",\"decimal_places\":2,\"default_to_zero\":true}")                                                                                                                                                                                                      | "[{\"id\":\"6586c748d37628000788e522\",\"name\":\"卸货港\",\"fieldNum\":39,\"attribute_values\":[{\"id\":\"65e6866211714e000755e715\",\"code\":\"06601\",\"name\":\"纽约港\",\"is_default\":\"0\"},{\"id\":\"65e704c011714e000762b201\",\"code\":\"06602\",\"name\":\"澳大利亚港\",\"is_default\":\"1\"}]}]" | ""
        new ObjectData("_id": "1", "ref_field_name": "EXT#ATTR#6586c748d37628000788e522", "product_id": "1", "formula": "{\"return_type\":\"currency\",\"expression\":\"IF(\\n    \$EXT#NON_ATTR#65954ea312e1da0006466941\$ ==10&&\$EXT#NON_ATTR#6596a8d92d49e20007f1a234\$ == 10,\\n    10,\\n    IF(\\n        \$EXT#NON_ATTR#65954ea312e1da0006466941\$ ==20 &&\$EXT#NON_ATTR#6596a8d92d49e20007f1a234\$ == 20,\\n        20,\\n        30\\n    ))\",\"decimal_places\":4,\"default_to_zero\":true}") | ""                                                                                                                                                                                                                                                                                                | "[{\"_id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\"}]"
        new ObjectData("_id": "1", "ref_field_name": "field_G22r2__c", "product_id": "1", "formula": "{\"return_type\":\"currency\",\"expression\":\"(\$quote_id__r.field_G22r22__c\$\",\"decimal_places\":2,\"default_to_zero\":true}")                                                                                                                                                                                                                                                                  | ""                                                                                                                                                                                                                                                                                                | ""
        new ObjectData("_id": "1", "ref_field_name": "field_G22r21__c", "product_id": "1", "formula": "{\"return_type\":\"currency\",\"expression\":\"(\$order_id__r.field_G22r21__c\$\",\"decimal_places\":2,\"default_to_zero\":true}")                                                                                                                                                                                                                                                                 | ""                                                                                                                                                                                                                                                                                                | ""
    }

    def "test checkRepeat"() {
        given:
        QueryResult<IObjectData> queryResult = new QueryResult();
        queryResult.setData(dataList)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(tester, "serviceFacade", serviceFacade)
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        Map<String, List<IObjectData>> detailObjectData = Maps.newHashMap()
        detailObjectData.put(com.facishare.crm.openapi.Utils.ADVANCED_FORMULA_LINE_API_NAME,Lists.newArrayList(data))
        when:
        try {
            tester.checkRepeat(data,detailObjectData, user)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        data                                                                                                                            | dataList
        new ObjectData("_id": "1", "product_id": "1", "ref_object_api_name": "SalesOrderObj", "ref_field_name": "price")                | []
        new ObjectData("_id": "1", "bom_id": "1", "product_id": "1", "ref_object_api_name": "SalesOrderObj", "ref_field_name": "price") | [new ObjectData("_id": "1", "bom_id": "1", "product_id": "1")]
    }

    def "test formulaCalculate"() {
        given:
        def redissonService = PowerMockito.mock(RedissonService)
        Whitebox.setInternalState(tester, "redissonService", redissonService)
        PowerMockito.doNothing().when(redissonService, "unlock", any())
        def advancedFormulaCalculateService = PowerMockito.mock(AdvancedFormulaCalculateService)
        Whitebox.setInternalState(tester, "advancedFormulaCalculateService", advancedFormulaCalculateService)
        PowerMockito.when(advancedFormulaCalculateService.formulaCalculate(any(), any())).thenReturn(null)
        QuoterModel.CalculateDataParam arg = QuoterModel.CalculateDataParam.builder().seriesId(seriesId).build()
        when:
        tester.formulaCalculate(arg, getServiceContext(user,"",""))
        then:
        1 == 1
        where:
        seriesId | uid
        ""       | "1"
        "1"      | "1"
    }

    ServiceContext getServiceContext(User user, String serviceName, String serviceMethod) {
        RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder()
        requestContextBuilder.user(user)
        requestContextBuilder.tenantId(user.getTenantId())
        return new ServiceContext(requestContextBuilder.build(), serviceName, serviceMethod)
    }

    def "test checkFieldFormula"() {
        given:
        def infraServiceFacade = PowerMockito.mock(InfraServiceFacade)
        Whitebox.setInternalState(tester, "infraServiceFacade", infraServiceFacade)
        
        // Mock service facade responses
        IObjectDescribe orderProductDescribe = new ObjectDescribe()
        orderProductDescribe.setApiName("SalesOrderProductObj")
        IFieldDescribe priceField = new TextFieldDescribe()
        priceField.setApiName("price")
        priceField.setLabel("价格")
        IFieldDescribe quantityField = new TextFieldDescribe()
        quantityField.setApiName("quantity")
        quantityField.setLabel("数量")
        IFieldDescribe masterField = new MasterDetailFieldDescribe()
        masterField.setApiName("BOMObj")
        masterField.setTargetApiName("BOMObj")
        orderProductDescribe.setFieldDescribes([priceField, quantityField, masterField])
        
        PowerMockito.when(serviceFacade.findObject(any() as String, any() as String)).thenReturn(orderProductDescribe)
        
        // Mock domain plugin query result
        QueryResult<DomainPluginInstance> pluginResult = new QueryResult()
        pluginResult.setData(pluginInstances)
        PowerMockito.when(infraServiceFacade.findPluginInstanceByQuery(any() as User, any() as Query)).thenReturn(pluginResult)
        
        // Mock I18N text
        PowerMockito.mockStatic(I18N.class)
        PowerMockito.when(I18N.text(anyString())).thenReturn(expectedMessage)
        1
        when:
        try {
            tester.checkFieldFormula(orderProductDescribe, objectData, dataList, new User("1", "1"))
        } catch (ValidateException e) {

        }
        
        then:
        1==1
        
        where:
        objectData | dataList | pluginInstances | expectedException | expectedMessage
        // Case 1: bomId is empty, should return directly
        new ObjectData("_id": "1", "bom_id": "", "object_name": "SalesOrderProductObj") | [] | [] | false | null
        
        // Case 2: no master-detail relationship, should return directly
        new ObjectData("_id": "1", "bom_id": "bom1", "object_name": "SalesOrderProductObj") | [] | [] | false | null
        
        // Case 3: no domain plugin found, should return directly
        new ObjectData("_id": "1", "bom_id": "bom1", "object_name": "SalesOrderProductObj") | [] | [] | false | null
        
        // Case 4: price field references price field, should throw exception
        new ObjectData("_id": "1", "bom_id": "bom1", "object_name": "SalesOrderProductObj", "ref_field_name": "price", "formula": jsonStr("price")) |
        [new ObjectData("_id": "2", "ref_field_name": "price")] |
                getDomainList("[{\"plugin_param\":{\"details\":[{\"detailKey\":\"bom_detail\",\"fieldMapping\":{\"quantity\":\"quantity\",\"price\":\"price\"},\"objectApiName\":\"SalesOrderProductObj\"}],\"fieldMapping\":{}}}]") |
        true | "价格字段不能引用价格字段"
        
        // Case 5: quantity field references quantity field, should throw exception
        new ObjectData("_id": "1", "bom_id": "bom1", "object_name": "SalesOrderProductObj", "ref_field_name": "quantity", "formula": jsonStr("quantity")) |
        [new ObjectData("_id": "2", "ref_field_name": "quantity")] |
                getDomainList("[{\"plugin_param\":{\"details\":[{\"detailKey\":\"bom_detail\",\"fieldMapping\":{\"quantity\":\"quantity\",\"price\":\"price\"},\"objectApiName\":\"SalesOrderProductObj\"}],\"fieldMapping\":{}}}]") |
        true | "数量字段不能引用数量字段"
        
        // Case 6: other field references price field, should throw exception
        new ObjectData("_id": "1", "bom_id": "bom1", "object_name": "SalesOrderProductObj", "ref_field_name": "price", "formula": jsonStr("price")) |
        [new ObjectData("_id": "2", "ref_field_name": "price1", "formula": jsonStr("price"))] |
                getDomainList("[{\"plugin_param\":{\"details\":[{\"detailKey\":\"bom_detail\",\"fieldMapping\":{\"quantity\":\"quantity\",\"price\":\"price\"},\"objectApiName\":\"SalesOrderProductObj\"}],\"fieldMapping\":{}}}]") |
        true | "其他字段不能引用价格字段"
        
        // Case 7: other field references quantity field, should throw exception
        new ObjectData("_id": "1", "bom_id": "bom1", "object_name": "SalesOrderProductObj", "ref_field_name": "quantity", "formula": jsonStr("quantity")) |
        [new ObjectData("_id": "2", "ref_field_name": "quantity1", "formula": jsonStr("quantity"))] |
                getDomainList("[{\"plugin_param\":{\"details\":[{\"detailKey\":\"bom_detail\",\"fieldMapping\":{\"quantity\":\"quantity\",\"price\":\"price\"},\"objectApiName\":\"SalesOrderProductObj\"}],\"fieldMapping\":{}}}]") |
        true | "其他字段不能引用数量字段"
        
        // Case 8: valid formula with no price/quantity references, should pass
        new ObjectData("_id": "1", "bom_id": "bom1", "object_name": "SalesOrderProductObj", "ref_field_name": "other", "formula": jsonStr("other_field")) |
        [new ObjectData("_id": "2", "ref_field_name": "other_field")] |
                getDomainList("[{\"plugin_param\":{\"details\":[{\"detailKey\":\"bom_detail\",\"fieldMapping\":{\"quantity\":\"quantity\",\"price\":\"price\"},\"objectApiName\":\"SalesOrderProductObj\"}],\"fieldMapping\":{}}}]") |
        false | null
    }

    def jsonStr(String str){
        Map<String, String> map = Maps.newHashMap()
        String value = "\$".concat(str).concat("\$")
        map.put("expression",value)
        JSON.toJSONString(map)
    }

    def getDomainList(String str) {
        def array = JSON.parseArray(str, DomainPluginInstance.class)
        array
    }

    def getDataMap(IObjectData data) {
        Map<String, IObjectData> map = Maps.newHashMap()
        map.put(data.getId(), data)
        map

    }

    def getAttribute(String str) {
        List<Attribute> standardAttribute = Lists.newArrayList()
        if (StringUtils.isNotBlank(str)) {
            standardAttribute = JSON.parseArray(str, Attribute.class)
        }
        standardAttribute
    }

    def getNonAttribute(String str) {
        List<IObjectData> standardAttribute = Lists.newArrayList()
        if (StringUtils.isNotBlank(str)) {
            standardAttribute = JSON.parseArray(str, IObjectData.class)
        }
        standardAttribute
    }

    def getFields() {
        List<IFieldDescribe> list = Lists.newArrayList()
        IFieldDescribe fieldDescribe1 = new MasterDetailFieldDescribe()
        fieldDescribe1.setId("1")
        fieldDescribe1.setApiName("order_id")
        fieldDescribe1.setTargetApiName("bomObj")
        IFieldDescribe fieldDescribe2 = new TextFieldDescribe()
        fieldDescribe2.setApiName("EXT#ATTR#6586c748d37628000788e522")
        fieldDescribe2.setId("2")
        IFieldDescribe fieldDescribe3 = new TextFieldDescribe()
        fieldDescribe3.setApiName("field_G22r2__c")
        fieldDescribe3.setId("3")
        IFieldDescribe fieldDescribe4 = new TextFieldDescribe()
        fieldDescribe4.setApiName("field_G22r21__c")
        fieldDescribe4.setId("4")
        IFieldDescribe fieldDescribe5 = new TextFieldDescribe()
        fieldDescribe5.setApiName("field_G22r22__c")
        fieldDescribe5.setId("5")
        IFieldDescribe fieldDescribe6 = new TextFieldDescribe()
        fieldDescribe6.setApiName("quote_id")
        fieldDescribe6.setId("6")
        list.add(fieldDescribe1)
        list.add(fieldDescribe2)
        list.add(fieldDescribe3)
        list.add(fieldDescribe4)
        list.add(fieldDescribe5)
        list.add(fieldDescribe6)
        list
    }


}
