package com.facishare.crm.sfa.predefine.service

import cn.hutool.extra.spring.SpringUtil
import com.alibaba.fastjson.JSON
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.model.BomConstraintLineModel
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.cpq.BomConstraintServiceImpl
import com.facishare.crm.sfa.predefine.service.cpq.model.BomFormulaModel
import com.facishare.crm.sfa.predefine.service.cpq.model.CheckBomModel
import com.facishare.crm.sfa.utilities.constant.BomConstants
import com.facishare.crm.sfa.utilities.enums.EnumUtil
import com.facishare.crm.sfa.utilities.util.GrayUtil
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction
import com.facishare.paas.appframework.core.predef.service.CalculateService
import com.facishare.paas.appframework.core.predef.service.dto.calculate.ExpressionCheck
import com.facishare.paas.appframework.function.FunctionLogicService
import com.facishare.paas.appframework.function.UdefFunctionExt
import com.facishare.paas.appframework.function.dto.RunResult
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IUdefFunction
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.UdefFunction
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.fasterxml.jackson.core.type.TypeReference
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.*
import org.apache.commons.lang3.StringUtils
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.spy

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([GrayUtil.class, I18N.class, BaseObjectSaveAction.class, SpringUtil.class, I18nServiceImpl.class, ProductConstraintUtil.class])
@SuppressStaticInitializationFor(["com.facishare.crm.sfa.utilities.util.GrayUtil", "com.facishare.paas.I18N", "com.facishare.paas.metadata.util.SpringUtil", "com.fxiaoke.i18n.client.impl.I18nServiceImpl", "com.facishare.crm.sfa.utilities.util.ProductConstraintUtil"])
class BomConstraintServiceImplTest extends RemoveUseless {
    @Mock
    @Shared
    private ServiceFacade serviceFacade;
    @Mock
    @Shared
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Mock
    @Shared
    private FunctionLogicService functionLogicService;
    @Mock
    @Shared
    private CalculateService calculateService;
    @Mock
    @Shared
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    @Shared
    private BomConstraintServiceImpl bomConstraintServiceImplTest

    @Shared
    private User user

    def setupSpec() {
        MockitoAnnotations.initMocks(this);
        user = spy(new User("71568", "-10000"))
    }

    def setup() {
        bomConstraintServiceImplTest = new BomConstraintServiceImpl();
        bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
        serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomConstraintServiceImplTest, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(bomConstraintServiceImplTest, "calculateService", calculateService);
        Whitebox.setInternalState(bomConstraintServiceImplTest, "expressionCalculateLogicService", expressionCalculateLogicService);
        Whitebox.setInternalState(bomConstraintServiceImplTest, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService);

        PowerMockito.mockStatic(I18N.class)
        PowerMockito.mockStatic(GrayUtil)
        PowerMockito.mockStatic(ProductConstraintUtil)
        Whitebox.setInternalState(ProductConstraintUtil, "serviceFacade", serviceFacade);
    }


    def "checkRootNode"() {

        given:
        def map1 = Maps.newHashMap()
        map1.put("k1","v1")
        def map2 = Maps.newHashMap()
        map2.put("k2","v2")
        when:
        Whitebox.invokeMethod(bomConstraintServiceImplTest, "checkRootNode", map1, map2)
        then:
        1==1
    }

    def "parseBomIds"() {
        given:
        List<String> formulaExpression = Arrays.asList("\$bom1\$", "\$bom2\$", "other_text")
        when:
        Whitebox.invokeMethod(bomConstraintServiceImplTest, "parseBomIds", formulaExpression)
        then:
        1==1
    }

    def "parseBomIdsIncludeAmountField"() {
        given:
        List<String> formulaExpression = Arrays.asList("\$bom1\$", "\$bom2\$", "other_text","\$bom1.amount\$")
        when:
        Whitebox.invokeMethod(bomConstraintServiceImplTest, "parseBomIdsIncludeAmountField", formulaExpression)
        then:
        1==1
    }

    def "test check group rule"() {
        given:
        when:
        try {
            Whitebox.invokeMethod(bomConstraintServiceImplTest, "checkGroupRule", groupData, requiredMap, requiredRangeMap, exclusionMap, groupMap)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        notThrown(ValidateException)
        where:
        groupData                  | requiredMap       | requiredRangeMap  | exclusionMap      | groupMap
        ArrayListMultimap.create() | dataMap()         | Maps.newHashMap() | Maps.newHashMap() | Maps.newHashMap()
        buildGroupData()           | dataMap()         | Maps.newHashMap() | Maps.newHashMap() | Maps.newHashMap()
        buildGroupData()           | dataMap()         | dataMap()         | Maps.newHashMap() | groupMap("1", "2")
        buildGroupData()           | Maps.newHashMap() | dataMap()         | Maps.newHashMap() | groupMap("1", "2")
        buildGroupData()           | Maps.newHashMap() | Maps.newHashMap() | dataMap()         | groupMap("1", "2")
        buildGroupData()           | dataMap()         | dataMap()         | dataMap()         | groupMap("1", "2")
        buildGroupData()           | dataMap()         | Maps.newHashMap() | Maps.newHashMap() | groupMap("2", "3")
        buildGroupData()           | Maps.newHashMap() | dataMap()         | Maps.newHashMap() | groupMap("2", "3")
        buildGroupData()           | Maps.newHashMap() | Maps.newHashMap() | dataMap()         | groupMap("2", "3")
        buildGroupData()           | dataMap()         | dataMap()         | dataMap()         | groupMap("2", "3")
    }

    def "test checkRule"() {
        given:
        Whitebox.setInternalState(ProductConstraintUtil, "serviceFacade", serviceFacade);
        PowerMockito.when(ProductConstraintUtil.getRootBom(any(), any(), any())).thenReturn(new ObjectData("_id": "id1"))
        def queryResult = new QueryResult()
        queryResult.setData(bomList)
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any() as User, any() as String, any() as SearchTemplateQuery)).thenReturn(queryResult)
        when:
        try {
            PowerMockito.mockStatic(I18N);
            PowerMockito.when(I18N.text(any(), any())).thenReturn("参数不正确");
            PowerMockito.when(I18N.text(any())).thenReturn("参数不正确");
            PowerMockito.mockStatic(GrayUtil.class);
            PowerMockito.when(GrayUtil.bomMasterSlaveMode(any())).thenReturn(true);
            bomConstraintServiceImplTest.checkRule(detailList, new User("1", "1"), masterData)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        detailList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | masterData                                                                                                                                                                              | bomList
        getDetailList("[]")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | new ObjectData("object_describe_api_name": "BomAttributeConstraintObj", "active_status": true, "object_describe_id": "65951333ea4ea100077a209e", "core_id": "65e85c36828230000702e8ad") | []
        getDetailList("[{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e898\",\"product_id\":\"分组A1-1\",\"group_id\":\"65e85c36828230000702e898\",\"group_name\":\"分组A1-1\"}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e89b\",\"product_id\":\"分组A4-1\",\"group_id\":\"65e85c36828230000702e89b\",\"group_name\":\"分组A4-1\"}]},{\"record_type\":\"default__c\",\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e8ac\",\"product_path\":\"65954b986ab8210001098f4d.65e0242ff2801b0007a922e8\",\"product_id\":\"65e0242ff2801b0007a922e8\",\"product_name\":\"集成调试>子件1\",\"attribute\":[{\"id\":\"65e8300aac852100075df6d3\",\"name\":\"扩展功能：手机盾\",\"attribute_values\":[{\"id\":\"65e8300aac852100075df6d4\",\"name\":\"是\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65f16a1eb38cae000114649e\",\"product_path\":\"65954b986ab8210001098f4d.65e0243cf2801b0007a9252c\",\"product_id\":\"65e0243cf2801b0007a9252c\",\"product_name\":\"集成调试>子件2\"}]},{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"2\",\"condition_range\":[{\"bom_id\":\"65f16a1eb38cae00011464a0\",\"product_path\":\"65954b986ab8210001098f4d.65ed6a8c6e19300001e36703\",\"product_id\":\"65ed6a8c6e19300001e36703\",\"product_name\":\"集成调试>手BOM-3\"}],\"result_range\":[{\"bom_id\":\"65f16a1eb38cae00011464a1\",\"product_path\":\"65954b986ab8210001098f4d.65ed6a7f6e19300001e3656e\",\"product_id\":\"65ed6a7f6e19300001e3656e\",\"product_name\":\"集成调试>手BOM-2\"}]},{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"3\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c3\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]}]") | new ObjectData("object_describe_api_name": "BomAttributeConstraintObj", "active_status": true, "object_describe_id": "65951333ea4ea100077a209e", "core_id": "65e85c36828230000702e8ad") | []
        getDetailList("[{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e898\",\"product_id\":\"分组A1-1\",\"group_id\":\"65e85c36828230000702e898\",\"group_name\":\"分组A1-1\"}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e89b\",\"product_id\":\"分组A4-1\",\"group_id\":\"65e85c36828230000702e89b\",\"group_name\":\"分组A4-1\"}]},{\"record_type\":\"default__c\",\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e8ac\",\"product_path\":\"65954b986ab8210001098f4d.65e0242ff2801b0007a922e8\",\"product_id\":\"65e0242ff2801b0007a922e8\",\"product_name\":\"集成调试>子件1\",\"attribute\":[{\"id\":\"65e8300aac852100075df6d3\",\"name\":\"扩展功能：手机盾\",\"attribute_values\":[{\"id\":\"65e8300aac852100075df6d4\",\"name\":\"是\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65f16a1eb38cae000114649e\",\"product_path\":\"65954b986ab8210001098f4d.65e0243cf2801b0007a9252c\",\"product_id\":\"65e0243cf2801b0007a9252c\",\"product_name\":\"集成调试>子件2\"}]},{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"2\",\"condition_range\":[{\"bom_id\":\"65f16a1eb38cae00011464a0\",\"product_path\":\"65954b986ab8210001098f4d.65ed6a8c6e19300001e36703\",\"product_id\":\"65ed6a8c6e19300001e36703\",\"product_name\":\"集成调试>手BOM-3\"}],\"result_range\":[{\"bom_id\":\"65f16a1eb38cae00011464a1\",\"product_path\":\"65954b986ab8210001098f4d.65ed6a7f6e19300001e3656e\",\"product_id\":\"65ed6a7f6e19300001e3656e\",\"product_name\":\"集成调试>手BOM-2\"}]},{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"3\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c3\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]}]") | new ObjectData("object_describe_api_name": "BomAttributeConstraintObj", "active_status": true, "object_describe_id": "65951333ea4ea100077a209e", "core_id": "65e85c36828230000702e8ad") | [new ObjectData("_id": "65e85c36828230000702e8ac", "bom_path": "65e85c36828230000702e895.65e85c36828230000702e8ac"), new ObjectData("_id": "65f16a1eb38cae000114649e", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae000114649e"), new ObjectData("_id": "65f16a1eb38cae00011464a0", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae00011464a0"), new ObjectData("_id": "65f16a1eb38cae00011464a1", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae00011464a1"), new ObjectData("_id": "65e85c36828230000702e896", "bom_path": "65e85c36828230000702e895.65e85c36828230000702e896")]
        getDetailList("[{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c3\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]},{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c3\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]}]")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | new ObjectData("object_describe_api_name": "BomAttributeConstraintObj", "active_status": true, "object_describe_id": "65951333ea4ea100077a209e", "core_id": "")                         | [new ObjectData("_id": "65e85c36828230000702e8ac", "bom_path": "65e85c36828230000702e895.65e85c36828230000702e8ac"), new ObjectData("_id": "65f16a1eb38cae000114649e", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae000114649e"), new ObjectData("_id": "65f16a1eb38cae00011464a0", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae00011464a0"), new ObjectData("_id": "65f16a1eb38cae00011464a1", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae00011464a1"), new ObjectData("_id": "65e85c36828230000702e896", "bom_path": "65e85c36828230000702e895.65e85c36828230000702e896")]
        getDetailList("[{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c3\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]},{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"2\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c3\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]}]")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | new ObjectData("object_describe_api_name": "BomAttributeConstraintObj", "active_status": true, "object_describe_id": "65951333ea4ea100077a209e", "core_id": "")                         | [new ObjectData("_id": "65e85c36828230000702e8ac", "bom_path": "65e85c36828230000702e895.65e85c36828230000702e8ac"), new ObjectData("_id": "65f16a1eb38cae000114649e", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae000114649e"), new ObjectData("_id": "65f16a1eb38cae00011464a0", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae00011464a0"), new ObjectData("_id": "65f16a1eb38cae00011464a1", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae00011464a1"), new ObjectData("_id": "65e85c36828230000702e896", "bom_path": "65e85c36828230000702e895.65e85c36828230000702e896")]
        getDetailList("[{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c3\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]},{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"2\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c31\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]}]")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | new ObjectData("object_describe_api_name": "BomAttributeConstraintObj", "active_status": true, "object_describe_id": "65951333ea4ea100077a209e", "core_id": "")                         | [new ObjectData("_id": "65e85c36828230000702e8ac", "bom_path": "65e85c36828230000702e895.65e85c36828230000702e8ac"), new ObjectData("_id": "65f16a1eb38cae000114649e", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae000114649e"), new ObjectData("_id": "65f16a1eb38cae00011464a0", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae00011464a0"), new ObjectData("_id": "65f16a1eb38cae00011464a1", "bom_path": "65e85c36828230000702e895.65f16a1eb38cae00011464a1"), new ObjectData("_id": "65e85c36828230000702e896", "bom_path": "65e85c36828230000702e895.65e85c36828230000702e896")]
        getDetailList("[{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c3\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]},{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"2\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c31\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]}]")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | new ObjectData("object_describe_api_name": "BomAttributeConstraintObj", "active_status": true, "object_describe_id": "65951333ea4ea100077a209e", "core_id": "")                         | [new ObjectData("_id": "65e85c36828230000702e8ac", "bom_path": "65e85c36828230000702e895.65e85c36828230000702e8ac")]
    }

    def "test findBomConstrainByCoreId"() {
        given:
        when:
        try {
            PowerMockito.mockStatic(ProductConstraintUtil.class);
            PowerMockito.when(ProductConstraintUtil.getRootBom(any(), any(), any())).thenReturn(new ObjectData("_id": "id1"))
            def queryResult = new QueryResult()
            queryResult.setData(dataList)
            PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any(), any(), any())).thenReturn(queryResult)
            bomConstraintServiceImplTest.findBomConstrainByCoreId(new User("1", "1"), coreIds, flag)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        coreIds | flag  | dataList
        []      | true  | []
        ["1"]   | true  | []
        ["1"]   | false | []
        ["1"]   | false | [new ObjectData("_id": "1")]
    }

    def "test findBomConstrainByRootBomId"() {
        given:
        def bomConstraintServiceImplTest = PowerMockito.spy(new BomConstraintServiceImpl())
        PowerMockito.mockStatic(ProductConstraintUtil.class);
        PowerMockito.when(ProductConstraintUtil.getRootBom(any(), any(), any())).thenReturn(new ObjectData("_id": "id1"))
        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomConstraintServiceImplTest, "serviceFacade", serviceFacade)
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any(), any(), any())).thenReturn(queryResult)
        PowerMockito.mockStatic(GrayUtil.class);
        PowerMockito.when(GrayUtil.bomMasterSlaveMode(any())).thenReturn(true);
        when:
        try {
            bomConstraintServiceImplTest.findBomConstrainByRootBomId(new User("1", "1"), coreIds, flag)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        coreIds | flag  | dataList
        []      | true  | []
        ["1"]   | true  | []
        ["1"]   | false | []
        ["1"]   | false | [new ObjectData("_id": "1")]
    }

    def "test findBomConstraintLinesByMasterId"() {
        given:
        def bomConstraintServiceImplTest = PowerMockito.spy(new BomConstraintServiceImpl())
        PowerMockito.mockStatic(ProductConstraintUtil.class);
        PowerMockito.when(ProductConstraintUtil.getRootBom(any(), any(), any())).thenReturn(new ObjectData("_id": "id1"))
        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        Whitebox.setInternalState(bomConstraintServiceImplTest, "serviceFacade", serviceFacade)
        PowerMockito.when(serviceFacade.findBySearchQueryWithFieldsIgnoreAll(any(), any(), any(), any())).thenReturn(queryResult)
        PowerMockito.mockStatic(GrayUtil.class);
        PowerMockito.when(GrayUtil.bomMasterSlaveMode(any())).thenReturn(true);
        when:
        try {
            bomConstraintServiceImplTest.findBomConstraintLinesByMasterId(new User("1", "1"), coreIds)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        coreIds | flag  | dataList
        []      | true  | []
        ["1"]   | true  | []
        ["1"]   | false | []
        ["1"]   | false | [new ObjectData("_id": "1")]
    }

    def "test checkBomConstraintAttr"() {
        given:
        def queryResult = new QueryResult()
        queryResult.setData(dataList)
        PowerMockito.when(serviceFacade.findBySearchQueryIgnoreAll(any(User.class), any(String.class), any(SearchTemplateQuery.class))).thenReturn(queryResult)
        PowerMockito.when(bizConfigThreadLocalCacheService.isCPQEnabled(anyString())).thenReturn(cpq)
        PowerMockito.mockStatic(I18N);
        PowerMockito.when(I18N.text(any())).thenReturn("参数不正确");
        PowerMockito.mockStatic(ProductConstraintUtil);
        PowerMockito.when(ProductConstraintUtil.getRootBom(any(), any(), any())).thenReturn(ObjectDataDocument.of(["_id": "id1"]).toObjectData());
        when:
        try {
            bomConstraintServiceImplTest.checkBomConstraintAttr(new User("1", "1"), map, flag)
        } catch (Exception e) {
            assert e instanceof ValidateException
        }
        then:
        1 == 1
        where:
        map             | flag  | dataList | cpq
        getDataMap("")  | true  | [] | false
        getDataMap("")  | true  | [] | true
        getDataMap("1") | true  | [] | true
        getDataMap("1") | true  | ObjectDataDocument.ofDataList(getBomAttributeConstraintLines("[{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"root_id\":\"r1\",\"_id\":\"65e85c36828230000702e896\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c3\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]}]")) | true
        getDataMap("1") | false | ObjectDataDocument.ofDataList(getBomAttributeConstraintLines("[{\"object_describe_api_name\":\"BomAttributeConstraintLinesObj\",\"constraint_type\":\"1\",\"root_id\":\"r1\",\"_id\":\"65e85c36828230000702e896\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"condition_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6861911714e000755d928\",\"name\":\"起运港\",\"attribute_values\":[{\"id\":\"65e6861911714e000755d92a\",\"name\":\"宁波港\"}]},{\"id\":\"65e685de11714e000755d03a\",\"name\":\"出发地\",\"attribute_values\":[{\"id\":\"65e685de11714e000755d03c\",\"name\":\"宁波\"}]}],\"nonstandardAttribute\":[{\"id\":\"65e80c5711714e0007bbde7f\",\"name\":\"对接系统数量\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"200\",\"name\":\"1 — 200\"}]}]}],\"result_range\":[{\"bom_id\":\"65e85c36828230000702e896\",\"product_path\":\"65954b986ab8210001098f4d.65e67e5e11714e0007552b4e\",\"product_id\":\"65e67e5e11714e0007552b4e\",\"product_name\":\"集成调试>报关费\",\"attribute\":[{\"id\":\"65e6866211714e000755e714\",\"name\":\"卸货港\",\"attribute_values\":[{\"id\":\"65e704c011714e000762b201\",\"name\":\"澳大利亚港\"}]},{\"id\":\"65e6864211714e000755e2ea\",\"name\":\"目的地\",\"attribute_values\":[{\"id\":\"65e704a711714e000762a5c3\",\"name\":\"澳大利亚\"}]}],\"nonstandardAttribute\":[]}]}]")) | true
    }

    def getDataMap(String str) {
        ArrayListMultimap<String, String> dataMap = ArrayListMultimap.create()
        if (StringUtils.isNotBlank(str)) {
            dataMap.put("65e67e5e11714e0007552b4e", "65e6861911714e000755d928")
            dataMap.put("65e67e5e11714e0007552b4e", "65e80c5711714e0007bbde7f")
        }
        dataMap
    }

    def getDetailList(String str) {
        List<ObjectDataDocument> array = JSON.parseArray(str, ObjectDataDocument.class)
        ObjectDataDocument.ofDataList(array)
    }

    def "test matchConstraintLine"() {
        given:
        when:
        def result = Whitebox.invokeMethod(bomConstraintServiceImplTest, "matchConstraintLine", selectBomMap, conditionList, groupMap, bomMap, "")
        then:
        result == ret
        where:
        selectBomMap      | conditionList      | groupMap                   | bomMap   | ret
        Maps.newHashMap() | conditionList("1") | ArrayListMultimap.create() | bomMap() | false
        selectBomMap("1") | conditionList("1") | buildGroupData()           | bomMap() | false
        selectBomMap("1") | conditionList("2") | buildGroupData()           | bomMap() | false
    }

    def "test matchConstrainedLine"() {
        given:
        def tableFlag = flag
        def tableSize = size
        def requireTableResult = requireTable
        def exclusionTableResult = exclusionTable
        def requireRangeTableResult = requireRangeTable
        def bomConstraintServiceImplTest = PowerMockito.spy(new BomConstraintServiceImpl())
        when:
        Whitebox.invokeMethod(bomConstraintServiceImplTest, "matchConstrainedLine", selectBomMap, requireTable, exclusionTable, resultList, type, requireRangeTable, groupMap, bomMap, "")
        then:
        if (tableFlag == 0) {
            requireTableResult.size() == tableSize
        } else if (tableFlag == 1) {
            exclusionTableResult.size() == tableSize
        } else if (tableFlag == 2) {
            requireRangeTableResult.size() == tableSize
        } else {
            1 == 1
        }
        where:
        selectBomMap      | requireTable            | exclusionTable          | resultList           | type | requireRangeTable       | groupMap         | bomMap   | flag | size
        selectBomMap("1") | HashBasedTable.create() | HashBasedTable.create() | Lists.newArrayList() | 1    | HashBasedTable.create() | buildGroupData() | bomMap() | 0    | 0
        selectBomMap("1") | HashBasedTable.create() | HashBasedTable.create() | resultList("1")      | 1    | HashBasedTable.create() | buildGroupData() | bomMap() | 0    | 0
        selectBomMap("2") | HashBasedTable.create() | HashBasedTable.create() | resultList("1")      | 1    | HashBasedTable.create() | buildGroupData() | bomMap() | 0    | 1
        selectBomMap("1") | HashBasedTable.create() | HashBasedTable.create() | resultList("1")      | 2    | HashBasedTable.create() | buildGroupData() | bomMap() | 1    | 1
        selectBomMap("2") | HashBasedTable.create() | HashBasedTable.create() | resultList("1")      | 2    | HashBasedTable.create() | buildGroupData() | bomMap() | 1    | 0
        selectBomMap("1") | HashBasedTable.create() | HashBasedTable.create() | resultList("1")      | 3    | HashBasedTable.create() | buildGroupData() | bomMap() | 2    | 0
        selectBomMap("2") | HashBasedTable.create() | HashBasedTable.create() | resultList("1")      | 3    | HashBasedTable.create() | buildGroupData() | bomMap() | 2    | 1
    }

    def "checkBomConstraint method by not apl"() {
        given:
        def tester = PowerMockito.spy(new BomConstraintServiceImpl())
        def queryResult = new QueryResult()
        queryResult.setData([new ObjectData("product_id": "p1")])
        def arg = new CheckBomModel.Arg()
        arg.setProductName(productName)
        arg.setRootId(rootId)
        arg.setSelectBomList(selectBomList)
        arg.setBomAttributeConstraintLines(bomAttributeConstraintLines)
        def args = new CheckBomModel.Args()
        args.setTreeList([arg])
        when:
        PowerMockito.mockStatic(I18N);
        PowerMockito.when(I18N.text(any(), any())).thenReturn("参数不正确");
        def result = tester.checkBomConstraint(User.systemUser("71653"), args)
        then:
        if (re == 0) {
            result.success
        } else {
            !result.success
        }
        where:
        re | productName | rootId                     | selectBomList | bomAttributeConstraintLines
        //"电脑 (顶层)"   | "660670d93cd7150007664315" |getBomList("")|getBomAttributeConstraintLines("")
        0  | "电脑 (顶层)"   | "660670d93cd7150007664315" | getBomList("[{\"bomId\":\"660670d93cd7150007664315\",\"product_group_id\":\"\",\"current_root_new_path\":\"660670d93cd7150007664315\"}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"66066d1b3cd7150007651796.66066d2b3cd7150007651d97\",\"product_name\":\"电脑 (顶层)>主板(二级)\",\"bom_id\":\"660670d93cd7150007664317\",\"attribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}]}],\"nonstandardAttribute\":[]}],\"result_range\":[{\"product_path\":\"66066d1b3cd7150007651796.660d38fd5e6b450001a3c271\",\"product_name\":\"电脑 (顶层)>4K显示器(二级)\",\"bom_id\":\"660d39315e6b450001a3e030\",\"attribute\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"白\"}]},{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895d\",\"name\":\"超大\"}]}]}],\"constraint_type\":\"1\",\"root_id\":\"660670d93cd7150007664315\",\"constraint_type__o\":null,\"_id\":\"6613c52f0267530007859218\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"_newRuleId\":\"660670d93cd7150007664315.6613c52f0267530007859218\"}]")
        0  | "电脑 (顶层)"   | "660670d93cd7150007664315" | getBomList("[{\"bomId\":\"660670d93cd7150007664315\",\"product_group_id\":\"\",\"current_root_new_path\":\"660670d93cd7150007664315\"},{\"bomId\":\"660670d93cd7150007664317\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\"}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"66066d1b3cd7150007651796.66066d2b3cd7150007651d97\",\"product_name\":\"电脑 (顶层)>主板(二级)\",\"bom_id\":\"660670d93cd7150007664317\",\"attribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}]}],\"nonstandardAttribute\":[]}],\"result_range\":[{\"product_path\":\"66066d1b3cd7150007651796.660d38fd5e6b450001a3c271\",\"product_name\":\"电脑 (顶层)>4K显示器(二级)\",\"bom_id\":\"660d39315e6b450001a3e030\",\"attribute\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"白\"}]},{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895d\",\"name\":\"超大\"}]}]}],\"constraint_type\":\"1\",\"root_id\":\"660670d93cd7150007664315\",\"constraint_type__o\":null,\"_id\":\"6613c52f0267530007859218\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"_newRuleId\":\"660670d93cd7150007664315.6613c52f0267530007859218\"}]")
        1  | "电脑 (顶层)"   | "660670d93cd7150007664315" | getBomList("[{\"bomId\":\"660670d93cd7150007664317\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"attributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attributeValues\":{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}}]},{\"bomId\":\"660670d93cd7150007664315\",\"current_root_new_path\":\"660670d93cd7150007664315\"}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"66066d1b3cd7150007651796.66066d2b3cd7150007651d97\",\"product_name\":\"电脑 (顶层)>主板(二级)\",\"bom_id\":\"660670d93cd7150007664317\",\"attribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}]}],\"nonstandardAttribute\":[]}],\"result_range\":[{\"product_path\":\"66066d1b3cd7150007651796.660d38fd5e6b450001a3c271\",\"product_name\":\"电脑 (顶层)>4K显示器(二级)\",\"bom_id\":\"660d39315e6b450001a3e030\",\"attribute\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"白\"}]},{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895d\",\"name\":\"超大\"}]}]}],\"constraint_type\":\"1\",\"root_id\":\"660670d93cd7150007664315\",\"constraint_type__o\":null,\"_id\":\"6613c52f0267530007859218\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"_newRuleId\":\"660670d93cd7150007664315.6613c52f0267530007859218\"}]")
        1  | "电脑 (顶层)"   | "660670d93cd7150007664315" | getBomList("[{\"bomId\":\"660d39315e6b450001a3e030\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"attributes\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"大小\",\"attributeValues\":{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"大\"}}]},{\"bomId\":\"660670d93cd7150007664317\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"attributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attributeValues\":{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}}]},{\"bomId\":\"660670d93cd7150007664315\",\"current_root_new_path\":\"660670d93cd7150007664315\"}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"66066d1b3cd7150007651796.66066d2b3cd7150007651d97\",\"product_name\":\"电脑 (顶层)>主板(二级)\",\"bom_id\":\"660670d93cd7150007664317\",\"attribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}]}],\"nonstandardAttribute\":[]}],\"result_range\":[{\"product_path\":\"66066d1b3cd7150007651796.660d38fd5e6b450001a3c271\",\"product_name\":\"电脑 (顶层)>4K显示器(二级)\",\"bom_id\":\"660d39315e6b450001a3e030\",\"attribute\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"白\"}]},{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895d\",\"name\":\"超大\"}]}]}],\"constraint_type\":\"2\",\"root_id\":\"660670d93cd7150007664315\",\"_id\":\"6613c52f0267530007859218\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"_newRuleId\":\"660670d93cd7150007664315.6613c52f0267530007859218\"}]")
        1  | "电脑 (顶层)"   | "660670d93cd7150007664315" | getBomList("[{\"bomId\":\"660670d93cd7150007664317\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"nonAttributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attributeValues\":{\"value\":\"1\",\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}}]},{\"bomId\":\"660670d93cd7150007664315\",\"current_root_new_path\":\"660670d93cd7150007664315\"}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"66066d1b3cd7150007651796.66066d2b3cd7150007651d97\",\"product_name\":\"电脑 (顶层)>主板(二级)\",\"bom_id\":\"660670d93cd7150007664317\",\"nonAttributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}]}],\"nonstandardAttribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\",\"value\":\"1\"}]}]}],\"result_range\":[{\"product_path\":\"66066d1b3cd7150007651796.660d38fd5e6b450001a3c271\",\"product_name\":\"电脑 (顶层)>4K显示器(二级)\",\"bom_id\":\"660d39315e6b450001a3e030\",\"nonAttributes\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"白\",\"value\":\"1\"}]},{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895d\",\"name\":\"超大\"}]}]}],\"constraint_type\":\"1\",\"root_id\":\"660670d93cd7150007664315\",\"constraint_type__o\":null,\"_id\":\"6613c52f0267530007859218\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"_newRuleId\":\"660670d93cd7150007664315.6613c52f0267530007859218\"}]")
        1  | "电脑 (顶层)"   | "660670d93cd7150007664315" | getBomList("[{\"bomId\":\"660d39315e6b450001a3e030\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"nonAttributes\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"大小\",\"attributeValues\":{\"value\":\"1\",\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"大\"}}]},{\"bomId\":\"660670d93cd7150007664317\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"nonAttributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attributeValues\":{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\",\"value\":\"1\"}}]},{\"bomId\":\"660670d93cd7150007664315\",\"current_root_new_path\":\"660670d93cd7150007664315\"}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"66066d1b3cd7150007651796.66066d2b3cd7150007651d97\",\"product_name\":\"电脑 (顶层)>主板(二级)\",\"bom_id\":\"660670d93cd7150007664317\",\"nonAttributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}]}],\"nonstandardAttribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\",\"value\":\"1\"}]}]}],\"result_range\":[{\"product_path\":\"66066d1b3cd7150007651796.660d38fd5e6b450001a3c271\",\"product_name\":\"电脑 (顶层)>4K显示器(二级)\",\"bom_id\":\"660d39315e6b450001a3e030\",\"nonAttributes\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"白\",\"value\":\"1\"}]},{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895d\",\"name\":\"超大\"}]}]}],\"constraint_type\":\"2\",\"root_id\":\"660670d93cd7150007664315\",\"constraint_type__o\":null,\"_id\":\"6613c52f0267530007859218\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"_newRuleId\":\"660670d93cd7150007664315.6613c52f0267530007859218\"}]")
        1  | "电脑 (顶层)"   | "660670d93cd7150007664315" | getBomList("[{\"bomId\":\"660670d93cd7150007664317\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"attributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attributeValues\":{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\",\"value\":\"1\"}}]},{\"bomId\":\"660670d93cd7150007664315\",\"current_root_new_path\":\"660670d93cd7150007664315\"}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"66066d1b3cd7150007651796.66066d2b3cd7150007651d97\",\"product_name\":\"电脑 (顶层)>主板(二级)\",\"bom_id\":\"660670d93cd7150007664317\",\"attribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}]}],\"nonstandardAttribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\",\"value\":\"1\"}]}]}],\"result_range\":[{\"product_path\":\"66066d1b3cd7150007651796.660d38fd5e6b450001a3c271\",\"product_name\":\"电脑 (顶层)>4K显示器(二级)\",\"bom_id\":\"660d39315e6b450001a3e030\",\"attribute\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"白\",\"value\":\"1\"}]},{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895d\",\"name\":\"超大\"}]}]}],\"constraint_type\":\"3\",\"root_id\":\"660670d93cd7150007664315\",\"constraint_type__o\":null,\"_id\":\"6613c52f0267530007859218\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"_newRuleId\":\"660670d93cd7150007664315.6613c52f0267530007859218\"}]")
        1  | "电脑 (顶层)"   | "660670d93cd7150007664315" | getBomList("[{\"bomId\":\"660670d93cd7150007664317\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"nonAttributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attributeValues\":{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\",\"value\":\"1\"}}]},{\"bomId\":\"660670d93cd7150007664315\",\"current_root_new_path\":\"660670d93cd7150007664315\"}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"66066d1b3cd7150007651796.66066d2b3cd7150007651d97\",\"product_name\":\"电脑 (顶层)>主板(二级)\",\"bom_id\":\"660670d93cd7150007664317\",\"nonAttributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}]}],\"nonstandardAttribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\",\"value\":\"1\"}]}]}],\"result_range\":[{\"product_path\":\"66066d1b3cd7150007651796.660d38fd5e6b450001a3c271\",\"product_name\":\"电脑 (顶层)>4K显示器(二级)\",\"bom_id\":\"660d39315e6b450001a3e030\",\"nonstandardAttribute\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"白\",\"value\":\"1\"}]},{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895d\",\"name\":\"超大\"}]}]}],\"constraint_type\":\"3\",\"root_id\":\"660670d93cd7150007664315\",\"constraint_type__o\":null,\"_id\":\"6613c52f0267530007859218\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"_newRuleId\":\"660670d93cd7150007664315.6613c52f0267530007859218\"}]")
        1  | "电脑 (顶层)"   | "660670d93cd7150007664315" | getBomList("[{\"bomId\":\"660670d93cd7150007664317\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\"},{\"bomId\":\"660670d93cd7150007664315\",\"current_root_new_path\":\"660670d93cd7150007664315\"}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"66066d1b3cd7150007651796.66066d2b3cd7150007651d97\",\"group_name\":\"电脑 (顶层)>主板(二级)\",\"group_id\":\"660670d93cd7150007664316\",\"nonstandardAttribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\",\"value\":\"1\"}]}]}],\"result_range\":[{\"product_path\":\"66066d1b3cd7150007651796.660d38fd5e6b450001a3c271\",\"group_name\":\"电脑 (顶层)>4K显示器(二级)\",\"group_id\":\"660d39315e6b450001a3e030\",\"nonstandardAttribute\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"白\",\"value\":\"1\"}]},{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895d\",\"name\":\"超大\"}]}]}],\"constraint_type\":\"1\",\"root_id\":\"660670d93cd7150007664315\",\"constraint_type__o\":null,\"_id\":\"6613c52f0267530007859218\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"_newRuleId\":\"660670d93cd7150007664315.6613c52f0267530007859218\"}]")
        1  | "电脑 (顶层)"   | "660670d93cd7150007664315" | getBomList("[{\"bomId\":\"660d39315e6b450001a3e030\",\"product_group_id\":\"660670d93cd7150007664316\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"nonAttributes\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"大小\",\"attributeValues\":{\"value\":\"1\",\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"大\"}}]},{\"bomId\":\"660670d93cd7150007664317\",\"product_group_id\":\"660670d93cd7150007664317\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"nonAttributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attributeValues\":{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\",\"value\":\"1\"}}]},{\"bomId\":\"660670d93cd7150007664315\",\"current_root_new_path\":\"660670d93cd7150007664315\"}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"66066d1b3cd7150007651796.66066d2b3cd7150007651d97\",\"group_name\":\"电脑 (顶层)>主板(二级)\",\"group_id\":\"660670d93cd7150007664317\",\"nonAttributes\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\"}]}],\"nonstandardAttribute\":[{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895a\",\"name\":\"大\",\"value\":\"1\"}]}]}],\"result_range\":[{\"product_path\":\"66066d1b3cd7150007651796.660d38fd5e6b450001a3c271\",\"group_name\":\"电脑 (顶层)>4K显示器(二级)\",\"group_id\":\"660670d93cd7150007664316\",\"nonAttributes\":[{\"id\":\"6107b8d128b99a00019c9436\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"6107b8d128b99a00019c94dc\",\"name\":\"白\",\"value\":\"1\"}]},{\"id\":\"6107b89728b99a00019c88f5\",\"name\":\"大小\",\"attribute_values\":[{\"id\":\"6107b89728b99a00019c895d\",\"name\":\"超大\"}]}]}],\"constraint_type\":\"2\",\"root_id\":\"660670d93cd7150007664315\",\"constraint_type__o\":null,\"_id\":\"6613c52f0267530007859218\",\"current_root_new_path\":\"660670d93cd7150007664315\",\"_newRuleId\":\"660670d93cd7150007664315.6613c52f0267530007859218\"}]")
        0  | "测试123"     | "682bef270ccfe3000763cbb8" | getBomList("[{\"new_bom_path\":\"682bef270ccfe3000763cbb8\",\"attribute\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"fieldNum\":121,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}]},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"fieldNum\":123,\"groupId\":\"66e550d03fc002000734c2ef\",\"groupName\":\"增值服务\",\"groupNo\":2,\"attribute_values\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}]},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"fieldNum\":133,\"groupId\":\"\",\"groupName\":\"\",\"groupNo\":0,\"attribute_values\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}]},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"fieldNum\":132,\"groupId\":\"\",\"groupName\":\"\",\"groupNo\":0,\"attribute_values\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}]}],\"bom_id\":\"682bef270ccfe3000763cbb8\",\"name\":\"测试123\",\"attribute_ids\":[\"66e5526ee45a130007dc1c5e\",\"66e552e9e45a130007dc236f\",\"677ceebf7afc980007614dfb\",\"677cee977afc980007614842\"],\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"product_id\":\"677b4821201f020007a52d49\",\"_isRoot\":true,\"isChecked\":true,\"product_id__r\":\"测试123\",\"rowId\":\"1747710056066516\",\"pricebook_id\":\"6594c79bc715080007e4b05b\",\"attribute_json\":{\"66e5526ee45a130007dc1c5e\":\"66e5526ee45a130007dc1c5f\",\"66e552e9e45a130007dc236f\":\"66e552e9e45a130007dc2370\",\"677ceebf7afc980007614dfb\":\"677ceebf7afc980007614dfc\",\"677cee977afc980007614842\":\"677cee977afc980007614843\"},\"pricing_period\":1,\"_attrConMark\":true,\"selectedAttr\":{\"66e5526ee45a130007dc1c5e\":{\"name\":\"现场服务\",\"value_ids\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"name\":\"是\"}]},\"66e552e9e45a130007dc236f\":{\"name\":\"网络仲裁\",\"value_ids\":[{\"id\":\"66e552e9e45a130007dc2370\",\"name\":\"是\"}]},\"677ceebf7afc980007614dfb\":{\"name\":\"属性无分组2\",\"value_ids\":[{\"id\":\"677ceebf7afc980007614dfc\",\"name\":\"8aod\"}]},\"677cee977afc980007614842\":{\"name\":\"属性无分组1\",\"value_ids\":[{\"id\":\"677cee977afc980007614843\",\"name\":\"5768678\"}]}},\"bomId\":\"682bef270ccfe3000763cbb8\",\"attributes\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"attributeValues\":{\"id\":\"66e5526ee45a130007dc1c5f\",\"name\":\"是\"}},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"attributeValues\":{\"id\":\"66e552e9e45a130007dc2370\",\"name\":\"是\"}},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"attributeValues\":{\"id\":\"677ceebf7afc980007614dfc\",\"name\":\"8aod\"}},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"attributeValues\":{\"id\":\"677cee977afc980007614843\",\"name\":\"5768678\"}}],\"product_group_id\":\"\",\"isRoot\":true},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"life_status_before_invalid\":null,\"product_id\":\"68196f714ba23c00076bfb10\",\"owner_department_id\":\"1000\",\"field_Q1v5m__c\":null,\"searchAfterId\":[\"20250520010564\",\"682bef270ccfe3000763cbb9\"],\"modified_adjust_price\":0,\"price_mode\":\"1\",\"adjust_price__r\":\"CNY 0.00\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"leaderName\":null,\"leaderUserId\":null,\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"order_field\":\"1\",\"field_h5B9Q__c\":true,\"product_life_status\":\"正常\",\"share_rate\":null,\"version\":\"2\",\"bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbb9\",\"price_book_id\":null,\"product_bom_path\":[\"测试123\",\"050601\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"field_CzlWL__c\":null,\"related_core_id\":null,\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY 10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"field_8lkD8__c\":null,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"origin_source\":null,\"product_id__r\":\"050601\",\"product_id__relation_ids\":\"68196f714ba23c00076bfb10\",\"core_id\":\"682bef270ccfe3000763cbbb\",\"field_1JIjq__c\":null,\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"groupId\":\"\",\"groupName\":\"\",\"groupNo\":0,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"},{\"id\":\"66e1045e75903a0007ae9bab\",\"code\":\"1-0\",\"name\":\"灰色\",\"order_field\":2,\"is_default\":\"0\"},{\"id\":\"66ec23e5214fc7000777eb2b\",\"code\":\"1-9\",\"name\":\"颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":3,\"is_default\":\"0\"},{\"id\":\"66ec24d3214fc7000777fdf5\",\"code\":\"1-10\",\"name\":\"颜色2颜色2颜色2颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"67c66f0917873700016de376\",\"code\":\"浅蓝\",\"name\":\"浅蓝\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"order_field\":6,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"order_field\":7,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"order_field\":12,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"order_field\":13,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"order_field\":14,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"order_field\":15,\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fac\",\"code\":\"深黑\",\"name\":\"深黑\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa7\",\"code\":\"深蓝\",\"name\":\"深蓝\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fab\",\"code\":\"深绿\",\"name\":\"深绿\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa8\",\"code\":\"深红\",\"name\":\"深红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa9\",\"code\":\"深紫\",\"name\":\"深紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2faa\",\"code\":\"深灰\",\"name\":\"深灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa5\",\"code\":\"浅黄\",\"name\":\"浅黄\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa2\",\"code\":\"浅红\",\"name\":\"浅红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa3\",\"code\":\"浅紫\",\"name\":\"浅紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa4\",\"code\":\"浅灰\",\"name\":\"浅灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa6\",\"code\":\"浅棕\",\"name\":\"浅棕\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"order_field\":8,\"is_default\":\"0\"}]},{\"id\":\"67c66e7417873700016dbb91\",\"name\":\"屏幕尺寸\",\"fieldNum\":140,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"},{\"id\":\"67c66e7417873700016dbb93\",\"code\":\"15英寸\",\"name\":\"15英寸\",\"is_default\":\"0\"},{\"id\":\"67c66e7417873700016dbb94\",\"code\":\"16英寸\",\"name\":\"16英寸\",\"is_default\":\"0\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1747709738982,\"life_status\":\"normal\",\"enabled_status\":true,\"out_tenant_id\":null,\"amount_editable\":true,\"product_group_id\":\"\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"pricing_period\":\"1\",\"order_by\":\"10\",\"max_amount\":null,\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbb9\",\"product_id_path\":[\"677b4821201f020007a52d49\",\"68196f714ba23c00076bfb10\"],\"field_7gDe2__c\":null,\"crumb_bread\":[\"682bef270ccfe3000763cbb8\",\"682bef270ccfe3000763cbb9\"],\"extend_obj_data_id\":\"682bef280ccfe3000763cca4\",\"amount_any\":true,\"created_by__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"node_type\":\"standard\",\"total_num\":2,\"owner_department\":\"研发部门\",\"field_CNRJC__c\":null,\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1747709736946,\"min_amount\":null,\"created_by\":[\"1000\"],\"relevant_team\":null,\"data_own_department\":[\"1000\"],\"core_id__r\":\"20250520-001930\",\"bom_id__r\":\"20250520010564\",\"name\":\"20250520010564\",\"_id\":\"682bef270ccfe3000763cbb9\",\"field_6h59q__c\":null,\"selected_by_default\":true,\"remark\":null,\"core_id__relation_ids\":\"682bef270ccfe3000763cbbb\",\"lock_user\":null,\"adjust_price\":\"0.00\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试123\",\"050601\"],\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"root_id\":\"682bef270ccfe3000763cbb8\",\"out_owner\":null,\"node_bom_core_version\":null,\"owner__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"owner\":[\"1000\"],\"amount\":\"1.0000\",\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"last_modified_by\":[\"-10000\"],\"field_tuzm6__c\":null,\"mc_currency\":\"CNY\",\"record_type\":\"default__c\",\"last_modified_by__r\":{\"picAddr\":null,\"mobile\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"empNum\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"enterpriseName\":null,\"email\":null,\"status\":null},\"parent_bom_id\":\"682bef270ccfe3000763cbb8\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"682bef270ccfe3000763cbb9\",\"__amount\":\"1.0000\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"0.00\",\"__pricing_period\":\"1.00\",\"pricing_mode\":\"one\",\"__price_per_set\":0,\"price_per_set\":0,\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\",\"amount\"],\"_attrConMark\":true,\"_defaultAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"}]},\"67c66e7417873700016dbb91\":{\"name\":\"屏幕尺寸\",\"value_ids\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"}]},\"67c66e7417873700016dbb91\":{\"name\":\"屏幕尺寸\",\"value_ids\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"}]}},\"rowId\":\"1747710056068517\",\"isChecked\":true,\"attribute_json\":{\"65d4635dedb9f50001015881\":\"65d4635dedb9f50001015882\",\"67c66e7417873700016dbb91\":\"67c66e7417873700016dbb92\"},\"nsAttr\":{},\"_level\":0,\"isShow\":true,\"_index\":0,\"bomId\":\"682bef270ccfe3000763cbb9\",\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015882\",\"name\":\"红色\"}},{\"id\":\"67c66e7417873700016dbb91\",\"name\":\"屏幕尺寸\",\"attributeValues\":{\"id\":\"67c66e7417873700016dbb92\",\"name\":\"14英寸\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"groupName\":null,\"groupId\":null,\"name\":\"长度\",\"default_value\":\"444\",\"id\":\"67c6fd56d6c99800017f47ef\",\"type\":\"1\",\"groupNo\":null},{\"groupName\":null,\"groupId\":null,\"name\":\"限高\",\"default_value\":\"332\",\"id\":\"67d93bbdfab4bc0001bb27bb\",\"type\":\"1\",\"groupNo\":null}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"life_status_before_invalid\":null,\"product_id\":\"681c6984c5600b00073cb6be\",\"owner_department_id\":\"1000\",\"field_Q1v5m__c\":null,\"searchAfterId\":[\"20250520010565\",\"682bef270ccfe3000763cbba\"],\"modified_adjust_price\":0,\"price_mode\":\"1\",\"adjust_price__r\":\"CNY 0.00\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"leaderName\":null,\"leaderUserId\":null,\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"order_field\":\"2\",\"field_h5B9Q__c\":true,\"product_life_status\":\"正常\",\"share_rate\":null,\"version\":\"2\",\"bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbba\",\"price_book_id\":null,\"product_bom_path\":[\"测试123\",\"重型吊装机\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"field_CzlWL__c\":null,\"related_core_id\":null,\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY 10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"field_8lkD8__c\":null,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"origin_source\":null,\"product_id__r\":\"重型吊装机\",\"product_id__relation_ids\":\"681c6984c5600b00073cb6be\",\"core_id\":\"682bef270ccfe3000763cbbb\",\"field_1JIjq__c\":null,\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"67c66e3017873700016da58b\",\"name\":\"CPU\",\"fieldNum\":139,\"groupId\":\"\",\"groupName\":\"\",\"groupNo\":0,\"attribute_values\":[{\"id\":\"67c66e3017873700016da58e\",\"code\":\"i71\",\"name\":\"i7\",\"is_default\":\"1\"},{\"id\":\"67c66e3017873700016da58d\",\"code\":\"i61\",\"name\":\"i6\",\"is_default\":\"0\"}]},{\"id\":\"66f4d16c5c3b990007e946a9\",\"name\":\"内存\",\"fieldNum\":126,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"66f4d16c5c3b990007e946aa\",\"code\":\"128G\",\"name\":\"128G\",\"is_default\":\"1\"},{\"id\":\"66f4d16c5c3b990007e946ac\",\"code\":\"512G\",\"name\":\"512G\",\"is_default\":\"0\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1747709738982,\"life_status\":\"normal\",\"enabled_status\":true,\"out_tenant_id\":null,\"amount_editable\":true,\"product_group_id\":\"\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"pricing_period\":\"1\",\"order_by\":\"20\",\"max_amount\":null,\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbba\",\"product_id_path\":[\"677b4821201f020007a52d49\",\"681c6984c5600b00073cb6be\"],\"field_7gDe2__c\":null,\"crumb_bread\":[\"682bef270ccfe3000763cbb8\",\"682bef270ccfe3000763cbba\"],\"extend_obj_data_id\":\"682bef280ccfe3000763cca5\",\"amount_any\":true,\"created_by__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"node_type\":\"standard\",\"total_num\":2,\"owner_department\":\"研发部门\",\"field_CNRJC__c\":null,\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1747709736947,\"min_amount\":null,\"created_by\":[\"1000\"],\"relevant_team\":null,\"data_own_department\":[\"1000\"],\"core_id__r\":\"20250520-001930\",\"bom_id__r\":\"20250520010565\",\"name\":\"20250520010565\",\"_id\":\"682bef270ccfe3000763cbba\",\"field_6h59q__c\":null,\"selected_by_default\":true,\"remark\":null,\"core_id__relation_ids\":\"682bef270ccfe3000763cbbb\",\"lock_user\":null,\"adjust_price\":\"0.00\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试123\",\"重型吊装机\"],\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"root_id\":\"682bef270ccfe3000763cbb8\",\"out_owner\":null,\"node_bom_core_version\":null,\"owner__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"owner\":[\"1000\"],\"amount\":\"1.0000\",\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"last_modified_by\":[\"-10000\"],\"field_tuzm6__c\":null,\"mc_currency\":\"CNY\",\"record_type\":\"default__c\",\"last_modified_by__r\":{\"picAddr\":null,\"mobile\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"empNum\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"enterpriseName\":null,\"email\":null,\"status\":null},\"parent_bom_id\":\"682bef270ccfe3000763cbb8\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"682bef270ccfe3000763cbba\",\"__amount\":\"1.0000\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"0.00\",\"__pricing_period\":\"1.00\",\"pricing_mode\":\"one\",\"__price_per_set\":0,\"price_per_set\":0,\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\",\"amount\"],\"_attrConMark\":true,\"_defaultAttr\":{\"67c66e3017873700016da58b\":{\"name\":\"CPU\",\"value_ids\":[{\"id\":\"67c66e3017873700016da58e\",\"code\":\"i71\",\"name\":\"i7\",\"is_default\":\"1\"}]},\"66f4d16c5c3b990007e946a9\":{\"name\":\"内存\",\"value_ids\":[{\"id\":\"66f4d16c5c3b990007e946aa\",\"code\":\"128G\",\"name\":\"128G\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"67c66e3017873700016da58b\":{\"name\":\"CPU\",\"value_ids\":[{\"id\":\"67c66e3017873700016da58e\",\"code\":\"i71\",\"name\":\"i7\",\"is_default\":\"1\"}]},\"66f4d16c5c3b990007e946a9\":{\"name\":\"内存\",\"value_ids\":[{\"id\":\"66f4d16c5c3b990007e946aa\",\"code\":\"128G\",\"name\":\"128G\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"67c6fd56d6c99800017f47ef\":{\"value\":\"444\",\"name\":\"长度\"},\"67d93bbdfab4bc0001bb27bb\":{\"value\":\"332\",\"name\":\"限高\"}},\"rowId\":\"1747710056068518\",\"isChecked\":true,\"attribute_json\":{\"67c66e3017873700016da58b\":\"67c66e3017873700016da58e\",\"66f4d16c5c3b990007e946a9\":\"66f4d16c5c3b990007e946aa\"},\"_level\":0,\"isShow\":true,\"_index\":1,\"nonstandard_attribute_json\":{},\"nonstandard_attribute\":\"\",\"bomId\":\"682bef270ccfe3000763cbba\",\"nonAttributes\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attributeValues\":{\"value\":\"444\"}},{\"id\":\"67d93bbdfab4bc0001bb27bb\",\"name\":\"限高\",\"attributeValues\":{\"value\":\"332\"}}],\"attributes\":[{\"id\":\"67c66e3017873700016da58b\",\"name\":\"CPU\",\"attributeValues\":{\"id\":\"67c66e3017873700016da58e\",\"name\":\"i7\"}},{\"id\":\"66f4d16c5c3b990007e946a9\",\"name\":\"内存\",\"attributeValues\":{\"id\":\"66f4d16c5c3b990007e946aa\",\"name\":\"128G\"}}]}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"677b4821201f020007a52d49\",\"product_name\":\"测试123\",\"bom_id\":\"682bef270ccfe3000763cbb8\",\"attribute\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"attribute_values\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"name\":\"是\"}]}]}],\"result_range\":[{\"product_path\":\"677b4821201f020007a52d49\",\"product_name\":\"测试123\",\"bom_id\":\"682bef270ccfe3000763cbb8\",\"attribute\":[{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"attribute_values\":[{\"id\":\"677ceebf7afc980007614dfc\",\"name\":\"8aod\"}]}],\"_ruleId\":\"682bef270ccfe3000763cbb8.682befac0ccfe3000763f259\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\"}],\"constraint_type\":\"1\",\"root_id\":\"682bef270ccfe3000763cbb8\",\"constraint_type__o\":null,\"_id\":\"682befac0ccfe3000763f259\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"_newRuleId\":\"682bef270ccfe3000763cbb8.682befac0ccfe3000763f259\",\"productId__r\":\"测试123\",\"coreId\":\"682bef270ccfe3000763cbbb\",\"coreId__r\":\"20250520-001930\"},{\"condition_range\":[{\"product_path\":\"677b4821201f020007a52d49.68196f714ba23c00076bfb10\",\"product_name\":\"测试123>050601\",\"bom_id\":\"682bef270ccfe3000763cbb9\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015882\",\"name\":\"红色\"}]}]},{\"product_path\":\"677b4821201f020007a52d49.681c6984c5600b00073cb6be\",\"product_name\":\"测试123>重型吊装机\",\"bom_id\":\"682bef270ccfe3000763cbba\",\"attribute\":[{\"id\":\"66f4d16c5c3b990007e946a9\",\"name\":\"内存\",\"attribute_values\":[{\"id\":\"66f4d16c5c3b990007e946aa\",\"name\":\"128G\"}]}],\"nonstandardAttribute\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]}]}],\"result_range\":[{\"product_path\":\"677b4821201f020007a52d49.68196f714ba23c00076bfb10\",\"product_name\":\"测试123>050601\",\"bom_id\":\"682bef270ccfe3000763cbb9\",\"attribute\":[{\"id\":\"67c66e7417873700016dbb91\",\"name\":\"屏幕尺寸\",\"attribute_values\":[{\"id\":\"67c66e7417873700016dbb92\",\"name\":\"14英寸\"}]}]},{\"product_path\":\"677b4821201f020007a52d49.681c6984c5600b00073cb6be\",\"product_name\":\"测试123>重型吊装机\",\"bom_id\":\"682bef270ccfe3000763cbba\",\"attribute\":[{\"id\":\"67c66e3017873700016da58b\",\"name\":\"CPU\",\"attribute_values\":[{\"id\":\"67c66e3017873700016da58d\",\"name\":\"i6\"}]}],\"nonstandardAttribute\":[{\"id\":\"67d93bbdfab4bc0001bb27bb\",\"name\":\"限高\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]}]}],\"constraint_type\":\"1\",\"root_id\":\"682bef270ccfe3000763cbb8\",\"constraint_type__o\":null,\"_id\":\"682befac0ccfe3000763f25a\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"_newRuleId\":\"682bef270ccfe3000763cbb8.682befac0ccfe3000763f25a\",\"productId__r\":\"测试123\",\"coreId\":\"682bef270ccfe3000763cbbb\",\"coreId__r\":\"20250520-001930\"}]")
        0  | "测试123"     | "682bef270ccfe3000763cbb8" | getBomList("[{\"settlement_cycle\":null,\"order_field\":null,\"version\":\"5\",\"product_status\":\"1\",\"safety_stock\":null,\"settlement_mode\":null,\"reference_field_1__c\":null,\"new_bom_path\":\"682bef270ccfe3000763cbb8\",\"extend_obj_data_id\":null,\"barcode\":null,\"purchase_unit_price\":null,\"created_by\":[\"1000\"],\"multiple_choice_1__c\":null,\"field_mdure__c\":null,\"field_bSw47__c\":null,\"field_2ehsb__c\":null,\"object_describe_api_name\":\"ProductObj\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"pricing_cycle\":null,\"product_type\":null,\"lock_rule\":null,\"pricing_attribute_ids\":[\"66e5526ee45a130007dc1c5e\",\"66e552e9e45a130007dc236f\",\"677ceebf7afc980007614dfb\",\"677cee977afc980007614842\"],\"product_category_id__relation_ids\":\"6594c7c0cfd011000162fddf\",\"_id\":\"677b4821201f020007a52d49\",\"pricing_mode\":\"one\",\"is_saleable\":true,\"owner_department_id\":\"1000\",\"attribute\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"fieldNum\":121,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}]},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"fieldNum\":123,\"groupId\":\"66e550d03fc002000734c2ef\",\"groupName\":\"增值服务\",\"groupNo\":2,\"attribute_values\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}]},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"fieldNum\":133,\"groupId\":\"\",\"groupName\":\"\",\"groupNo\":0,\"attribute_values\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}]},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"fieldNum\":132,\"groupId\":\"\",\"groupName\":\"\",\"groupNo\":0,\"attribute_values\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}]}],\"out_tenant_id\":null,\"bom_id\":\"682bef270ccfe3000763cbb8\",\"product_category_id\":\"6594c7c0cfd011000162fddf\",\"owner_department\":\"研发部门\",\"attribute119\":null,\"attr_range\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"66e550d03fc002000734c2ef\",\"groupName\":\"增值服务\",\"groupNo\":2,\"attribute_values\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"99999999\",\"groupName\":\"\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}],\"isStandardAttribute\":true},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"99999999\",\"groupName\":\"\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}],\"isStandardAttribute\":true}],\"name\":\"测试123\",\"lock_user\":null,\"price\":\"100.00\",\"life_status_before_invalid\":null,\"product_code\":\"测试123\",\"is_package\":true,\"last_modified_time\":1736240862839,\"life_status\":\"normal\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"product_id\":\"677b4821201f020007a52d49\",\"pricebook_id\":\"6594c79bc715080007e4b05b\",\"selectedAttr\":{\"66e5526ee45a130007dc1c5e\":{\"name\":\"现场服务\",\"value_ids\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"}]},\"66e552e9e45a130007dc236f\":{\"name\":\"网络仲裁\",\"value_ids\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"}]},\"677ceebf7afc980007614dfb\":{\"name\":\"属性无分组2\",\"value_ids\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}]},\"677cee977afc980007614842\":{\"name\":\"属性无分组1\",\"value_ids\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}]}},\"nsAttr\":{},\"bomId\":\"682bef270ccfe3000763cbb8\",\"attributes\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"attributeValues\":{\"id\":\"66e5526ee45a130007dc1c5f\",\"name\":\"是\"}},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"attributeValues\":{\"id\":\"66e552e9e45a130007dc2370\",\"name\":\"是\"}},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"attributeValues\":{\"id\":\"677ceebf7afc980007614dfc\",\"name\":\"8aod\"}},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"attributeValues\":{\"id\":\"677cee977afc980007614843\",\"name\":\"5768678\"}}],\"product_group_id\":\"\",\"isRoot\":true},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"groupName\":\"未分组\",\"groupId\":\"99999999\",\"name\":\"长度\",\"default_value\":\"5.00\",\"id\":\"67c6fd56d6c99800017f47ef\",\"type\":\"1\",\"groupNo\":99999999}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"life_status_before_invalid\":null,\"product_id\":\"68196f714ba23c00076bfb10\",\"owner_department_id\":\"1000\",\"field_Q1v5m__c\":null,\"searchAfterId\":[\"20250520010564\",\"682bef270ccfe3000763cbb9\"],\"modified_adjust_price\":\"0.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY 0.00\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"leaderName\":null,\"leaderUserId\":null,\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"order_field\":\"1\",\"field_h5B9Q__c\":true,\"product_life_status\":\"正常\",\"share_rate\":null,\"version\":\"2\",\"bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbb9\",\"price_book_id\":null,\"product_bom_path\":[\"测试123\",\"050601\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"field_CzlWL__c\":null,\"related_core_id\":null,\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY 10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"field_8lkD8__c\":null,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"origin_source\":null,\"product_id__r\":\"050601\",\"product_id__relation_ids\":\"68196f714ba23c00076bfb10\",\"core_id\":\"682bef270ccfe3000763cbbb\",\"field_1JIjq__c\":null,\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"groupId\":\"99999999\",\"groupName\":\"未分组\",\"groupNo\":99999999,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"},{\"id\":\"66e1045e75903a0007ae9bab\",\"code\":\"1-0\",\"name\":\"灰色\",\"order_field\":2,\"is_default\":\"0\"},{\"id\":\"66ec23e5214fc7000777eb2b\",\"code\":\"1-9\",\"name\":\"颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":3,\"is_default\":\"0\"},{\"id\":\"66ec24d3214fc7000777fdf5\",\"code\":\"1-10\",\"name\":\"颜色2颜色2颜色2颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"67c66f0917873700016de376\",\"code\":\"浅蓝\",\"name\":\"浅蓝\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"order_field\":6,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"order_field\":7,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"order_field\":12,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"order_field\":13,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"order_field\":14,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"order_field\":15,\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fac\",\"code\":\"深黑\",\"name\":\"深黑\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa7\",\"code\":\"深蓝\",\"name\":\"深蓝\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fab\",\"code\":\"深绿\",\"name\":\"深绿\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa8\",\"code\":\"深红\",\"name\":\"深红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa9\",\"code\":\"深紫\",\"name\":\"深紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2faa\",\"code\":\"深灰\",\"name\":\"深灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa5\",\"code\":\"浅黄\",\"name\":\"浅黄\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa2\",\"code\":\"浅红\",\"name\":\"浅红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa3\",\"code\":\"浅紫\",\"name\":\"浅紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa4\",\"code\":\"浅灰\",\"name\":\"浅灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa6\",\"code\":\"浅棕\",\"name\":\"浅棕\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"order_field\":8,\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"},{\"id\":\"66e1045e75903a0007ae9bab\",\"code\":\"1-0\",\"name\":\"灰色\",\"order_field\":2,\"is_default\":\"0\"},{\"id\":\"66ec23e5214fc7000777eb2b\",\"code\":\"1-9\",\"name\":\"颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":3,\"is_default\":\"0\"},{\"id\":\"66ec24d3214fc7000777fdf5\",\"code\":\"1-10\",\"name\":\"颜色2颜色2颜色2颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"67c66f0917873700016de376\",\"code\":\"浅蓝\",\"name\":\"浅蓝\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"order_field\":6,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"order_field\":7,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"order_field\":12,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"order_field\":13,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"order_field\":14,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"order_field\":15,\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fac\",\"code\":\"深黑\",\"name\":\"深黑\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa7\",\"code\":\"深蓝\",\"name\":\"深蓝\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fab\",\"code\":\"深绿\",\"name\":\"深绿\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa8\",\"code\":\"深红\",\"name\":\"深红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa9\",\"code\":\"深紫\",\"name\":\"深紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2faa\",\"code\":\"深灰\",\"name\":\"深灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa5\",\"code\":\"浅黄\",\"name\":\"浅黄\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa2\",\"code\":\"浅红\",\"name\":\"浅红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa3\",\"code\":\"浅紫\",\"name\":\"浅紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa4\",\"code\":\"浅灰\",\"name\":\"浅灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa6\",\"code\":\"浅棕\",\"name\":\"浅棕\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"order_field\":8,\"is_default\":\"0\"}]},{\"id\":\"67c66e7417873700016dbb91\",\"name\":\"屏幕尺寸\",\"fieldNum\":140,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"},{\"id\":\"67c66e7417873700016dbb93\",\"code\":\"15英寸\",\"name\":\"15英寸\",\"is_default\":\"0\"},{\"id\":\"67c66e7417873700016dbb94\",\"code\":\"16英寸\",\"name\":\"16英寸\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"},{\"id\":\"67c66e7417873700016dbb93\",\"code\":\"15英寸\",\"name\":\"15英寸\",\"is_default\":\"0\"},{\"id\":\"67c66e7417873700016dbb94\",\"code\":\"16英寸\",\"name\":\"16英寸\",\"is_default\":\"0\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1747709738982,\"life_status\":\"normal\",\"enabled_status\":true,\"out_tenant_id\":null,\"amount_editable\":true,\"product_group_id\":\"\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"pricing_period\":\"1.00\",\"order_by\":\"10\",\"max_amount\":null,\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbb9\",\"product_id_path\":[\"677b4821201f020007a52d49\",\"68196f714ba23c00076bfb10\"],\"field_7gDe2__c\":null,\"crumb_bread\":[\"682bef270ccfe3000763cbb8\",\"682bef270ccfe3000763cbb9\"],\"extend_obj_data_id\":\"682bef280ccfe3000763cca4\",\"amount_any\":true,\"created_by__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"node_type\":\"standard\",\"total_num\":2,\"owner_department\":\"研发部门\",\"field_CNRJC__c\":null,\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1747709736946,\"min_amount\":null,\"created_by\":[\"1000\"],\"relevant_team\":null,\"data_own_department\":[\"1000\"],\"core_id__r\":\"20250520-001930\",\"bom_id__r\":\"20250520010564\",\"name\":\"20250520010564\",\"_id\":\"682bef270ccfe3000763cbb9\",\"field_6h59q__c\":null,\"selected_by_default\":true,\"remark\":null,\"core_id__relation_ids\":\"682bef270ccfe3000763cbbb\",\"lock_user\":null,\"adjust_price\":\"0.00\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试123\",\"050601\"],\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"root_id\":\"682bef270ccfe3000763cbb8\",\"out_owner\":null,\"node_bom_core_version\":null,\"owner__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"owner\":[\"1000\"],\"amount\":\"1.0000\",\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"last_modified_by\":[\"-10000\"],\"field_tuzm6__c\":null,\"mc_currency\":\"CNY\",\"record_type\":\"default__c\",\"last_modified_by__r\":{\"picAddr\":null,\"mobile\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"empNum\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"enterpriseName\":null,\"email\":null,\"status\":null},\"parent_bom_id\":\"682bef270ccfe3000763cbb8\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"682bef270ccfe3000763cbb9\",\"__amount\":\"1.0000\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"0.00\",\"__pricing_period\":\"1.00\",\"pricing_mode\":\"one\",\"__price_per_set\":0,\"price_per_set\":0,\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\",\"amount\"],\"_attrConMark\":true,\"_defaultAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"}]},\"67c66e7417873700016dbb91\":{\"name\":\"屏幕尺寸\",\"value_ids\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"}]},\"67c66e7417873700016dbb91\":{\"name\":\"屏幕尺寸\",\"value_ids\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"67c6fd56d6c99800017f47ef\":{\"name\":\"长度\",\"value\":\"200\"}},\"rowId\":\"1747721739324913\",\"isChecked\":true,\"_level\":0,\"isShow\":true,\"_index\":0,\"nonstandard_attribute_json\":{},\"nonstandard_attribute\":\"\",\"noSelectDef\":true,\"attributeHtml\":\"颜色:红色;屏幕尺寸:14英寸;长度:200\",\"bomId\":\"682bef270ccfe3000763cbb9\",\"nonAttributes\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attributeValues\":{\"value\":\"10\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015882\",\"name\":\"红色\"}},{\"id\":\"67c66e7417873700016dbb91\",\"name\":\"屏幕尺寸\",\"attributeValues\":{\"id\":\"67c66e7417873700016dbb92\",\"name\":\"14英寸\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"groupName\":\"未分组\",\"groupId\":\"99999999\",\"name\":\"长度\",\"default_value\":\"444\",\"id\":\"67c6fd56d6c99800017f47ef\",\"type\":\"1\",\"groupNo\":99999999,\"notAllowedValueRange\":{}},{\"groupName\":\"未分组\",\"groupId\":\"99999999\",\"name\":\"限高\",\"default_value\":\"332\",\"id\":\"67d93bbdfab4bc0001bb27bb\",\"type\":\"1\",\"groupNo\":99999999,\"notAllowedValueRange\":{}}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"life_status_before_invalid\":null,\"product_id\":\"681c6984c5600b00073cb6be\",\"owner_department_id\":\"1000\",\"field_Q1v5m__c\":null,\"searchAfterId\":[\"20250520010565\",\"682bef270ccfe3000763cbba\"],\"modified_adjust_price\":\"0.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY 0.00\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"leaderName\":null,\"leaderUserId\":null,\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"order_field\":\"2\",\"field_h5B9Q__c\":true,\"product_life_status\":\"正常\",\"share_rate\":null,\"version\":\"2\",\"bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbba\",\"price_book_id\":null,\"product_bom_path\":[\"测试123\",\"重型吊装机\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"field_CzlWL__c\":null,\"related_core_id\":null,\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY 10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"field_8lkD8__c\":null,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"origin_source\":null,\"product_id__r\":\"重型吊装机\",\"product_id__relation_ids\":\"681c6984c5600b00073cb6be\",\"core_id\":\"682bef270ccfe3000763cbbb\",\"field_1JIjq__c\":null,\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"67c66e3017873700016da58b\",\"name\":\"CPU\",\"fieldNum\":139,\"groupId\":\"99999999\",\"groupName\":\"未分组\",\"groupNo\":99999999,\"attribute_values\":[{\"id\":\"67c66e3017873700016da58e\",\"code\":\"i71\",\"name\":\"i7\",\"is_default\":\"1\",\"disabled\":false,\"showError\":false},{\"id\":\"67c66e3017873700016da58d\",\"code\":\"i61\",\"name\":\"i6\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"67c66e3017873700016da58e\",\"code\":\"i71\",\"name\":\"i7\",\"is_default\":\"1\",\"disabled\":false,\"showError\":false},{\"id\":\"67c66e3017873700016da58d\",\"code\":\"i61\",\"name\":\"i6\",\"is_default\":\"0\"}]},{\"id\":\"66f4d16c5c3b990007e946a9\",\"name\":\"内存\",\"fieldNum\":126,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"66f4d16c5c3b990007e946aa\",\"code\":\"128G\",\"name\":\"128G\",\"is_default\":\"1\"},{\"id\":\"66f4d16c5c3b990007e946ac\",\"code\":\"512G\",\"name\":\"512G\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"66f4d16c5c3b990007e946aa\",\"code\":\"128G\",\"name\":\"128G\",\"is_default\":\"1\"},{\"id\":\"66f4d16c5c3b990007e946ac\",\"code\":\"512G\",\"name\":\"512G\",\"is_default\":\"0\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1747709738982,\"life_status\":\"normal\",\"enabled_status\":true,\"out_tenant_id\":null,\"amount_editable\":true,\"product_group_id\":\"\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"pricing_period\":\"1.00\",\"order_by\":\"20\",\"max_amount\":null,\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbba\",\"product_id_path\":[\"677b4821201f020007a52d49\",\"681c6984c5600b00073cb6be\"],\"field_7gDe2__c\":null,\"crumb_bread\":[\"682bef270ccfe3000763cbb8\",\"682bef270ccfe3000763cbba\"],\"extend_obj_data_id\":\"682bef280ccfe3000763cca5\",\"amount_any\":true,\"created_by__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"node_type\":\"standard\",\"total_num\":2,\"owner_department\":\"研发部门\",\"field_CNRJC__c\":null,\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1747709736947,\"min_amount\":null,\"created_by\":[\"1000\"],\"relevant_team\":null,\"data_own_department\":[\"1000\"],\"core_id__r\":\"20250520-001930\",\"bom_id__r\":\"20250520010565\",\"name\":\"20250520010565\",\"_id\":\"682bef270ccfe3000763cbba\",\"field_6h59q__c\":null,\"selected_by_default\":true,\"remark\":null,\"core_id__relation_ids\":\"682bef270ccfe3000763cbbb\",\"lock_user\":null,\"adjust_price\":\"0.00\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试123\",\"重型吊装机\"],\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"root_id\":\"682bef270ccfe3000763cbb8\",\"out_owner\":null,\"node_bom_core_version\":null,\"owner__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"owner\":[\"1000\"],\"amount\":\"1.0000\",\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"last_modified_by\":[\"-10000\"],\"field_tuzm6__c\":null,\"mc_currency\":\"CNY\",\"record_type\":\"default__c\",\"last_modified_by__r\":{\"picAddr\":null,\"mobile\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"empNum\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"enterpriseName\":null,\"email\":null,\"status\":null},\"parent_bom_id\":\"682bef270ccfe3000763cbb8\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"682bef270ccfe3000763cbba\",\"__amount\":\"1.0000\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"0.00\",\"__pricing_period\":\"1.00\",\"pricing_mode\":\"one\",\"__price_per_set\":0,\"price_per_set\":0,\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\",\"amount\"],\"_defaultAttr\":{\"67c66e3017873700016da58b\":{\"name\":\"CPU\",\"value_ids\":[{\"id\":\"67c66e3017873700016da58e\",\"code\":\"i71\",\"name\":\"i7\",\"is_default\":\"1\"}]},\"66f4d16c5c3b990007e946a9\":{\"name\":\"内存\",\"value_ids\":[{\"id\":\"66f4d16c5c3b990007e946aa\",\"code\":\"128G\",\"name\":\"128G\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"67c66e3017873700016da58b\":{\"name\":\"CPU\",\"value_ids\":[{\"id\":\"67c66e3017873700016da58e\",\"code\":\"i71\",\"name\":\"i7\",\"is_default\":\"1\"}]},\"66f4d16c5c3b990007e946a9\":{\"name\":\"内存\",\"value_ids\":[{\"id\":\"66f4d16c5c3b990007e946aa\",\"code\":\"128G\",\"name\":\"128G\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"67c6fd56d6c99800017f47ef\":{\"name\":\"长度\",\"value\":\"1\"},\"67d93bbdfab4bc0001bb27bb\":{\"name\":\"限高\",\"value\":\"1\"}},\"rowId\":\"1747721739324914\",\"isChecked\":true,\"_level\":0,\"isShow\":true,\"_index\":1,\"nonstandard_attribute_json\":{},\"nonstandard_attribute\":\"\",\"attributeHtml\":\"CPU:i7;内存:128G;长度:1,限高:1\",\"bomId\":\"682bef270ccfe3000763cbba\",\"nonAttributes\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attributeValues\":{\"value\":\"1\"}},{\"id\":\"67d93bbdfab4bc0001bb27bb\",\"name\":\"限高\",\"attributeValues\":{\"value\":\"1\"}}],\"attributes\":[{\"id\":\"67c66e3017873700016da58b\",\"name\":\"CPU\",\"attributeValues\":{\"id\":\"67c66e3017873700016da58e\",\"name\":\"i7\"}},{\"id\":\"66f4d16c5c3b990007e946a9\",\"name\":\"内存\",\"attributeValues\":{\"id\":\"66f4d16c5c3b990007e946aa\",\"name\":\"128G\"}}]}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"677b4821201f020007a52d49.68196f714ba23c00076bfb10\",\"product_name\":\"测试123>050601\",\"bom_id\":\"682bef270ccfe3000763cbb9\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015882\",\"name\":\"红色\"}]}],\"nonstandardAttribute\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]}]}],\"result_range\":[{\"product_path\":\"677b4821201f020007a52d49.681c6984c5600b00073cb6be\",\"product_name\":\"测试123>重型吊装机\",\"bom_id\":\"682bef270ccfe3000763cbba\",\"attribute\":[{\"id\":\"67c66e3017873700016da58b\",\"name\":\"CPU\",\"attribute_values\":[{\"id\":\"67c66e3017873700016da58e\",\"name\":\"i7\"}]}],\"nonstandardAttribute\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]},{\"id\":\"67d93bbdfab4bc0001bb27bb\",\"name\":\"限高\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]}],\"_ruleId\":\"682bef270ccfe3000763cbb8.682befac0ccfe3000763f25a\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\"}],\"constraint_type\":\"2\",\"root_id\":\"682bef270ccfe3000763cbb8\",\"constraint_type__o\":null,\"_id\":\"682befac0ccfe3000763f25a\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"_newRuleId\":\"682bef270ccfe3000763cbb8.682befac0ccfe3000763f25a\",\"productId__r\":\"测试123\",\"coreId\":\"682bef270ccfe3000763cbbb\",\"coreId__r\":\"20250520-001930\"}]")
        0  | "测试123"     | "682bef270ccfe3000763cbb8" | getBomList("[{\"settlement_cycle\":null,\"order_field\":null,\"version\":\"5\",\"product_status\":\"1\",\"safety_stock\":null,\"settlement_mode\":null,\"reference_field_1__c\":null,\"new_bom_path\":\"682bef270ccfe3000763cbb8\",\"extend_obj_data_id\":null,\"barcode\":null,\"purchase_unit_price\":null,\"created_by\":[\"1000\"],\"multiple_choice_1__c\":null,\"field_mdure__c\":null,\"field_bSw47__c\":null,\"field_2ehsb__c\":null,\"object_describe_api_name\":\"ProductObj\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"pricing_cycle\":null,\"product_type\":null,\"lock_rule\":null,\"pricing_attribute_ids\":[\"66e5526ee45a130007dc1c5e\",\"66e552e9e45a130007dc236f\",\"677ceebf7afc980007614dfb\",\"677cee977afc980007614842\"],\"product_category_id__relation_ids\":\"6594c7c0cfd011000162fddf\",\"_id\":\"677b4821201f020007a52d49\",\"pricing_mode\":\"one\",\"is_saleable\":true,\"owner_department_id\":\"1000\",\"attribute\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"fieldNum\":121,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}]},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"fieldNum\":123,\"groupId\":\"66e550d03fc002000734c2ef\",\"groupName\":\"增值服务\",\"groupNo\":2,\"attribute_values\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}]},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"fieldNum\":133,\"groupId\":\"\",\"groupName\":\"\",\"groupNo\":0,\"attribute_values\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}]},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"fieldNum\":132,\"groupId\":\"\",\"groupName\":\"\",\"groupNo\":0,\"attribute_values\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}]}],\"out_tenant_id\":null,\"bom_id\":\"682bef270ccfe3000763cbb8\",\"product_category_id\":\"6594c7c0cfd011000162fddf\",\"owner_department\":\"研发部门\",\"attribute119\":null,\"attr_range\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"66e550d03fc002000734c2ef\",\"groupName\":\"增值服务\",\"groupNo\":2,\"attribute_values\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"99999999\",\"groupName\":\"\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}],\"isStandardAttribute\":true},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"99999999\",\"groupName\":\"\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}],\"isStandardAttribute\":true}],\"name\":\"测试123\",\"lock_user\":null,\"price\":\"100.00\",\"life_status_before_invalid\":null,\"product_code\":\"测试123\",\"is_package\":true,\"last_modified_time\":1736240862839,\"life_status\":\"normal\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"product_id\":\"677b4821201f020007a52d49\",\"pricebook_id\":\"6594c79bc715080007e4b05b\",\"selectedAttr\":{\"66e5526ee45a130007dc1c5e\":{\"name\":\"现场服务\",\"value_ids\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"}]},\"66e552e9e45a130007dc236f\":{\"name\":\"网络仲裁\",\"value_ids\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"}]},\"677ceebf7afc980007614dfb\":{\"name\":\"属性无分组2\",\"value_ids\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}]},\"677cee977afc980007614842\":{\"name\":\"属性无分组1\",\"value_ids\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}]}},\"nsAttr\":{},\"bomId\":\"682bef270ccfe3000763cbb8\",\"attributes\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"attributeValues\":{\"id\":\"66e5526ee45a130007dc1c5f\",\"name\":\"是\"}},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"attributeValues\":{\"id\":\"66e552e9e45a130007dc2370\",\"name\":\"是\"}},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"attributeValues\":{\"id\":\"677ceebf7afc980007614dfc\",\"name\":\"8aod\"}},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"attributeValues\":{\"id\":\"677cee977afc980007614843\",\"name\":\"5768678\"}}],\"product_group_id\":\"\",\"isRoot\":true},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"groupName\":\"未分组\",\"groupId\":\"99999999\",\"name\":\"长度\",\"default_value\":\"5.00\",\"id\":\"67c6fd56d6c99800017f47ef\",\"type\":\"1\",\"groupNo\":99999999}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"life_status_before_invalid\":null,\"product_id\":\"68196f714ba23c00076bfb10\",\"owner_department_id\":\"1000\",\"field_Q1v5m__c\":null,\"searchAfterId\":[\"20250520010564\",\"682bef270ccfe3000763cbb9\"],\"modified_adjust_price\":\"0.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY 0.00\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"leaderName\":null,\"leaderUserId\":null,\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"order_field\":\"1\",\"field_h5B9Q__c\":true,\"product_life_status\":\"正常\",\"share_rate\":null,\"version\":\"2\",\"bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbb9\",\"price_book_id\":null,\"product_bom_path\":[\"测试123\",\"050601\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"field_CzlWL__c\":null,\"related_core_id\":null,\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY 10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"field_8lkD8__c\":null,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"origin_source\":null,\"product_id__r\":\"050601\",\"product_id__relation_ids\":\"68196f714ba23c00076bfb10\",\"core_id\":\"682bef270ccfe3000763cbbb\",\"field_1JIjq__c\":null,\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"groupId\":\"99999999\",\"groupName\":\"未分组\",\"groupNo\":99999999,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"},{\"id\":\"66e1045e75903a0007ae9bab\",\"code\":\"1-0\",\"name\":\"灰色\",\"order_field\":2,\"is_default\":\"0\"},{\"id\":\"66ec23e5214fc7000777eb2b\",\"code\":\"1-9\",\"name\":\"颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":3,\"is_default\":\"0\"},{\"id\":\"66ec24d3214fc7000777fdf5\",\"code\":\"1-10\",\"name\":\"颜色2颜色2颜色2颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"67c66f0917873700016de376\",\"code\":\"浅蓝\",\"name\":\"浅蓝\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"order_field\":6,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"order_field\":7,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"order_field\":12,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"order_field\":13,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"order_field\":14,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"order_field\":15,\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fac\",\"code\":\"深黑\",\"name\":\"深黑\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa7\",\"code\":\"深蓝\",\"name\":\"深蓝\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fab\",\"code\":\"深绿\",\"name\":\"深绿\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa8\",\"code\":\"深红\",\"name\":\"深红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa9\",\"code\":\"深紫\",\"name\":\"深紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2faa\",\"code\":\"深灰\",\"name\":\"深灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa5\",\"code\":\"浅黄\",\"name\":\"浅黄\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa2\",\"code\":\"浅红\",\"name\":\"浅红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa3\",\"code\":\"浅紫\",\"name\":\"浅紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa4\",\"code\":\"浅灰\",\"name\":\"浅灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa6\",\"code\":\"浅棕\",\"name\":\"浅棕\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"order_field\":8,\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"},{\"id\":\"66e1045e75903a0007ae9bab\",\"code\":\"1-0\",\"name\":\"灰色\",\"order_field\":2,\"is_default\":\"0\"},{\"id\":\"66ec23e5214fc7000777eb2b\",\"code\":\"1-9\",\"name\":\"颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":3,\"is_default\":\"0\"},{\"id\":\"66ec24d3214fc7000777fdf5\",\"code\":\"1-10\",\"name\":\"颜色2颜色2颜色2颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"67c66f0917873700016de376\",\"code\":\"浅蓝\",\"name\":\"浅蓝\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"order_field\":6,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"order_field\":7,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"order_field\":12,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"order_field\":13,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"order_field\":14,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"order_field\":15,\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fac\",\"code\":\"深黑\",\"name\":\"深黑\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa7\",\"code\":\"深蓝\",\"name\":\"深蓝\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fab\",\"code\":\"深绿\",\"name\":\"深绿\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa8\",\"code\":\"深红\",\"name\":\"深红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa9\",\"code\":\"深紫\",\"name\":\"深紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2faa\",\"code\":\"深灰\",\"name\":\"深灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa5\",\"code\":\"浅黄\",\"name\":\"浅黄\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa2\",\"code\":\"浅红\",\"name\":\"浅红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa3\",\"code\":\"浅紫\",\"name\":\"浅紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa4\",\"code\":\"浅灰\",\"name\":\"浅灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa6\",\"code\":\"浅棕\",\"name\":\"浅棕\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"order_field\":8,\"is_default\":\"0\"}]},{\"id\":\"67c66e7417873700016dbb91\",\"name\":\"屏幕尺寸\",\"fieldNum\":140,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"},{\"id\":\"67c66e7417873700016dbb93\",\"code\":\"15英寸\",\"name\":\"15英寸\",\"is_default\":\"0\"},{\"id\":\"67c66e7417873700016dbb94\",\"code\":\"16英寸\",\"name\":\"16英寸\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"},{\"id\":\"67c66e7417873700016dbb93\",\"code\":\"15英寸\",\"name\":\"15英寸\",\"is_default\":\"0\"},{\"id\":\"67c66e7417873700016dbb94\",\"code\":\"16英寸\",\"name\":\"16英寸\",\"is_default\":\"0\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1747709738982,\"life_status\":\"normal\",\"enabled_status\":true,\"out_tenant_id\":null,\"amount_editable\":true,\"product_group_id\":\"\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"pricing_period\":\"1.00\",\"order_by\":\"10\",\"max_amount\":null,\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbb9\",\"product_id_path\":[\"677b4821201f020007a52d49\",\"68196f714ba23c00076bfb10\"],\"field_7gDe2__c\":null,\"crumb_bread\":[\"682bef270ccfe3000763cbb8\",\"682bef270ccfe3000763cbb9\"],\"extend_obj_data_id\":\"682bef280ccfe3000763cca4\",\"amount_any\":true,\"created_by__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"node_type\":\"standard\",\"total_num\":2,\"owner_department\":\"研发部门\",\"field_CNRJC__c\":null,\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1747709736946,\"min_amount\":null,\"created_by\":[\"1000\"],\"relevant_team\":null,\"data_own_department\":[\"1000\"],\"core_id__r\":\"20250520-001930\",\"bom_id__r\":\"20250520010564\",\"name\":\"20250520010564\",\"_id\":\"682bef270ccfe3000763cbb9\",\"field_6h59q__c\":null,\"selected_by_default\":true,\"remark\":null,\"core_id__relation_ids\":\"682bef270ccfe3000763cbbb\",\"lock_user\":null,\"adjust_price\":\"0.00\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试123\",\"050601\"],\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"root_id\":\"682bef270ccfe3000763cbb8\",\"out_owner\":null,\"node_bom_core_version\":null,\"owner__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"owner\":[\"1000\"],\"amount\":\"1.0000\",\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"last_modified_by\":[\"-10000\"],\"field_tuzm6__c\":null,\"mc_currency\":\"CNY\",\"record_type\":\"default__c\",\"last_modified_by__r\":{\"picAddr\":null,\"mobile\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"empNum\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"enterpriseName\":null,\"email\":null,\"status\":null},\"parent_bom_id\":\"682bef270ccfe3000763cbb8\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"682bef270ccfe3000763cbb9\",\"__amount\":\"1.0000\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"0.00\",\"__pricing_period\":\"1.00\",\"pricing_mode\":\"one\",\"__price_per_set\":0,\"price_per_set\":0,\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\",\"amount\"],\"_attrConMark\":true,\"_defaultAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"}]},\"67c66e7417873700016dbb91\":{\"name\":\"屏幕尺寸\",\"value_ids\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"}]},\"67c66e7417873700016dbb91\":{\"name\":\"屏幕尺寸\",\"value_ids\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"67c6fd56d6c99800017f47ef\":{\"name\":\"长度\",\"value\":\"200\"}},\"rowId\":\"1747721739324913\",\"isChecked\":true,\"_level\":0,\"isShow\":true,\"_index\":0,\"nonstandard_attribute_json\":{},\"nonstandard_attribute\":\"\",\"noSelectDef\":true,\"attributeHtml\":\"颜色:红色;屏幕尺寸:14英寸;长度:200\",\"bomId\":\"682bef270ccfe3000763cbb9\",\"nonAttributes\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attributeValues\":{\"value\":\"10\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015882\",\"name\":\"红色\"}},{\"id\":\"67c66e7417873700016dbb91\",\"name\":\"屏幕尺寸\",\"attributeValues\":{\"id\":\"67c66e7417873700016dbb92\",\"name\":\"14英寸\"}}]}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"677b4821201f020007a52d49.68196f714ba23c00076bfb10\",\"product_name\":\"测试123>050601\",\"bom_id\":\"682bef270ccfe3000763cbb9\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015882\",\"name\":\"红色\"}]}],\"nonstandardAttribute\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]}]}],\"result_range\":[{\"product_path\":\"677b4821201f020007a52d49.681c6984c5600b00073cb6be\",\"product_name\":\"测试123>重型吊装机\",\"bom_id\":\"682bef270ccfe3000763cbba\",\"attribute\":[{\"id\":\"67c66e3017873700016da58b\",\"name\":\"CPU\",\"attribute_values\":[{\"id\":\"67c66e3017873700016da58e\",\"name\":\"i7\"}]}],\"nonstandardAttribute\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]},{\"id\":\"67d93bbdfab4bc0001bb27bb\",\"name\":\"限高\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]}],\"_ruleId\":\"682bef270ccfe3000763cbb8.682befac0ccfe3000763f25a\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\"}],\"constraint_type\":\"1\",\"root_id\":\"682bef270ccfe3000763cbb8\",\"constraint_type__o\":null,\"_id\":\"682befac0ccfe3000763f25a\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"_newRuleId\":\"682bef270ccfe3000763cbb8.682befac0ccfe3000763f25a\",\"productId__r\":\"测试123\",\"coreId\":\"682bef270ccfe3000763cbbb\",\"coreId__r\":\"20250520-001930\"}]")
        0  | "测试123"     | "682bef270ccfe3000763cbb8" | getBomList("[{\"settlement_cycle\":null,\"order_field\":null,\"version\":\"5\",\"product_status\":\"1\",\"safety_stock\":null,\"settlement_mode\":null,\"reference_field_1__c\":null,\"new_bom_path\":\"682bef270ccfe3000763cbb8\",\"extend_obj_data_id\":null,\"barcode\":null,\"purchase_unit_price\":null,\"created_by\":[\"1000\"],\"multiple_choice_1__c\":null,\"field_mdure__c\":null,\"field_bSw47__c\":null,\"field_2ehsb__c\":null,\"object_describe_api_name\":\"ProductObj\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"pricing_cycle\":null,\"product_type\":null,\"lock_rule\":null,\"pricing_attribute_ids\":[\"66e5526ee45a130007dc1c5e\",\"66e552e9e45a130007dc236f\",\"677ceebf7afc980007614dfb\",\"677cee977afc980007614842\"],\"product_category_id__relation_ids\":\"6594c7c0cfd011000162fddf\",\"_id\":\"677b4821201f020007a52d49\",\"pricing_mode\":\"one\",\"is_saleable\":true,\"owner_department_id\":\"1000\",\"attribute\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"fieldNum\":121,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}]},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"fieldNum\":123,\"groupId\":\"66e550d03fc002000734c2ef\",\"groupName\":\"增值服务\",\"groupNo\":2,\"attribute_values\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}]},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"fieldNum\":133,\"groupId\":\"\",\"groupName\":\"\",\"groupNo\":0,\"attribute_values\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}]},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"fieldNum\":132,\"groupId\":\"\",\"groupName\":\"\",\"groupNo\":0,\"attribute_values\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}]}],\"out_tenant_id\":null,\"bom_id\":\"682bef270ccfe3000763cbb8\",\"product_category_id\":\"6594c7c0cfd011000162fddf\",\"owner_department\":\"研发部门\",\"attribute119\":null,\"attr_range\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e5526ee45a130007dc1c60\",\"code\":\"02否\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"66e550d03fc002000734c2ef\",\"groupName\":\"增值服务\",\"groupNo\":2,\"attribute_values\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"},{\"id\":\"66e552e9e45a130007dc2371\",\"code\":\"否04\",\"name\":\"否\",\"is_default\":\"0\"}],\"isStandardAttribute\":true},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"99999999\",\"groupName\":\"\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}],\"isStandardAttribute\":true},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"apiName\":\"AttributeObj\",\"attrNo\":10000,\"groupId\":\"99999999\",\"groupName\":\"\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}],\"isStandardAttribute\":true}],\"name\":\"测试123\",\"lock_user\":null,\"price\":\"100.00\",\"life_status_before_invalid\":null,\"product_code\":\"测试123\",\"is_package\":true,\"last_modified_time\":1736240862839,\"life_status\":\"normal\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"product_id\":\"677b4821201f020007a52d49\",\"pricebook_id\":\"6594c79bc715080007e4b05b\",\"selectedAttr\":{\"66e5526ee45a130007dc1c5e\":{\"name\":\"现场服务\",\"value_ids\":[{\"id\":\"66e5526ee45a130007dc1c5f\",\"code\":\"02是\",\"name\":\"是\",\"is_default\":\"1\"}]},\"66e552e9e45a130007dc236f\":{\"name\":\"网络仲裁\",\"value_ids\":[{\"id\":\"66e552e9e45a130007dc2370\",\"code\":\"是04\",\"name\":\"是\",\"is_default\":\"1\"}]},\"677ceebf7afc980007614dfb\":{\"name\":\"属性无分组2\",\"value_ids\":[{\"id\":\"677ceebf7afc980007614dfc\",\"code\":\"689789\",\"name\":\"8aod\",\"is_default\":\"1\"}]},\"677cee977afc980007614842\":{\"name\":\"属性无分组1\",\"value_ids\":[{\"id\":\"677cee977afc980007614843\",\"code\":\"7896789\",\"name\":\"5768678\",\"is_default\":\"1\"}]}},\"nsAttr\":{},\"bomId\":\"682bef270ccfe3000763cbb8\",\"attributes\":[{\"id\":\"66e5526ee45a130007dc1c5e\",\"name\":\"现场服务\",\"attributeValues\":{\"id\":\"66e5526ee45a130007dc1c5f\",\"name\":\"是\"}},{\"id\":\"66e552e9e45a130007dc236f\",\"name\":\"网络仲裁\",\"attributeValues\":{\"id\":\"66e552e9e45a130007dc2370\",\"name\":\"是\"}},{\"id\":\"677ceebf7afc980007614dfb\",\"name\":\"属性无分组2\",\"attributeValues\":{\"id\":\"677ceebf7afc980007614dfc\",\"name\":\"8aod\"}},{\"id\":\"677cee977afc980007614842\",\"name\":\"属性无分组1\",\"attributeValues\":{\"id\":\"677cee977afc980007614843\",\"name\":\"5768678\"}}],\"product_group_id\":\"\",\"isRoot\":true},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"groupName\":\"未分组\",\"groupId\":\"99999999\",\"name\":\"长度\",\"default_value\":\"5.00\",\"id\":\"67c6fd56d6c99800017f47ef\",\"type\":\"1\",\"groupNo\":99999999}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"life_status_before_invalid\":null,\"product_id\":\"68196f714ba23c00076bfb10\",\"owner_department_id\":\"1000\",\"field_Q1v5m__c\":null,\"searchAfterId\":[\"20250520010564\",\"682bef270ccfe3000763cbb9\"],\"modified_adjust_price\":\"0.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY 0.00\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"leaderName\":null,\"leaderUserId\":null,\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"order_field\":\"1\",\"field_h5B9Q__c\":true,\"product_life_status\":\"正常\",\"share_rate\":null,\"version\":\"2\",\"bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbb9\",\"price_book_id\":null,\"product_bom_path\":[\"测试123\",\"050601\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"field_CzlWL__c\":null,\"related_core_id\":null,\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY 10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"field_8lkD8__c\":null,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"origin_source\":null,\"product_id__r\":\"050601\",\"product_id__relation_ids\":\"68196f714ba23c00076bfb10\",\"core_id\":\"682bef270ccfe3000763cbbb\",\"field_1JIjq__c\":null,\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"groupId\":\"99999999\",\"groupName\":\"未分组\",\"groupNo\":99999999,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"},{\"id\":\"66e1045e75903a0007ae9bab\",\"code\":\"1-0\",\"name\":\"灰色\",\"order_field\":2,\"is_default\":\"0\"},{\"id\":\"66ec23e5214fc7000777eb2b\",\"code\":\"1-9\",\"name\":\"颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":3,\"is_default\":\"0\"},{\"id\":\"66ec24d3214fc7000777fdf5\",\"code\":\"1-10\",\"name\":\"颜色2颜色2颜色2颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"67c66f0917873700016de376\",\"code\":\"浅蓝\",\"name\":\"浅蓝\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"order_field\":6,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"order_field\":7,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"order_field\":12,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"order_field\":13,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"order_field\":14,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"order_field\":15,\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fac\",\"code\":\"深黑\",\"name\":\"深黑\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa7\",\"code\":\"深蓝\",\"name\":\"深蓝\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fab\",\"code\":\"深绿\",\"name\":\"深绿\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa8\",\"code\":\"深红\",\"name\":\"深红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa9\",\"code\":\"深紫\",\"name\":\"深紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2faa\",\"code\":\"深灰\",\"name\":\"深灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa5\",\"code\":\"浅黄\",\"name\":\"浅黄\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa2\",\"code\":\"浅红\",\"name\":\"浅红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa3\",\"code\":\"浅紫\",\"name\":\"浅紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa4\",\"code\":\"浅灰\",\"name\":\"浅灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa6\",\"code\":\"浅棕\",\"name\":\"浅棕\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"order_field\":8,\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"},{\"id\":\"66e1045e75903a0007ae9bab\",\"code\":\"1-0\",\"name\":\"灰色\",\"order_field\":2,\"is_default\":\"0\"},{\"id\":\"66ec23e5214fc7000777eb2b\",\"code\":\"1-9\",\"name\":\"颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":3,\"is_default\":\"0\"},{\"id\":\"66ec24d3214fc7000777fdf5\",\"code\":\"1-10\",\"name\":\"颜色2颜色2颜色2颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色颜色\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"67c66f0917873700016de376\",\"code\":\"浅蓝\",\"name\":\"浅蓝\",\"order_field\":4,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"order_field\":6,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"order_field\":7,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"order_field\":12,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"order_field\":13,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"order_field\":14,\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"order_field\":15,\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fac\",\"code\":\"深黑\",\"name\":\"深黑\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa7\",\"code\":\"深蓝\",\"name\":\"深蓝\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fab\",\"code\":\"深绿\",\"name\":\"深绿\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa8\",\"code\":\"深红\",\"name\":\"深红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa9\",\"code\":\"深紫\",\"name\":\"深紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2faa\",\"code\":\"深灰\",\"name\":\"深灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa5\",\"code\":\"浅黄\",\"name\":\"浅黄\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa2\",\"code\":\"浅红\",\"name\":\"浅红\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa3\",\"code\":\"浅紫\",\"name\":\"浅紫\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa4\",\"code\":\"浅灰\",\"name\":\"浅灰\",\"is_default\":\"0\"},{\"id\":\"67c67b2bc969f400019a2fa6\",\"code\":\"浅棕\",\"name\":\"浅棕\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"order_field\":8,\"is_default\":\"0\"}]},{\"id\":\"67c66e7417873700016dbb91\",\"name\":\"屏幕尺寸\",\"fieldNum\":140,\"groupId\":\"66e54f923fc0020007347f4d\",\"groupName\":\"高级功能\",\"groupNo\":1,\"attribute_values\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"},{\"id\":\"67c66e7417873700016dbb93\",\"code\":\"15英寸\",\"name\":\"15英寸\",\"is_default\":\"0\"},{\"id\":\"67c66e7417873700016dbb94\",\"code\":\"16英寸\",\"name\":\"16英寸\",\"is_default\":\"0\"}],\"isStandardAttribute\":true,\"children\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"},{\"id\":\"67c66e7417873700016dbb93\",\"code\":\"15英寸\",\"name\":\"15英寸\",\"is_default\":\"0\"},{\"id\":\"67c66e7417873700016dbb94\",\"code\":\"16英寸\",\"name\":\"16英寸\",\"is_default\":\"0\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1747709738982,\"life_status\":\"normal\",\"enabled_status\":true,\"out_tenant_id\":null,\"amount_editable\":true,\"product_group_id\":\"\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"pricing_period\":\"1.00\",\"order_by\":\"10\",\"max_amount\":null,\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"682bef270ccfe3000763cbb8.682bef270ccfe3000763cbb9\",\"product_id_path\":[\"677b4821201f020007a52d49\",\"68196f714ba23c00076bfb10\"],\"field_7gDe2__c\":null,\"crumb_bread\":[\"682bef270ccfe3000763cbb8\",\"682bef270ccfe3000763cbb9\"],\"extend_obj_data_id\":\"682bef280ccfe3000763cca4\",\"amount_any\":true,\"created_by__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"node_type\":\"standard\",\"total_num\":2,\"owner_department\":\"研发部门\",\"field_CNRJC__c\":null,\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1747709736946,\"min_amount\":null,\"created_by\":[\"1000\"],\"relevant_team\":null,\"data_own_department\":[\"1000\"],\"core_id__r\":\"20250520-001930\",\"bom_id__r\":\"20250520010564\",\"name\":\"20250520010564\",\"_id\":\"682bef270ccfe3000763cbb9\",\"field_6h59q__c\":null,\"selected_by_default\":true,\"remark\":null,\"core_id__relation_ids\":\"682bef270ccfe3000763cbbb\",\"lock_user\":null,\"adjust_price\":\"0.00\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试123\",\"050601\"],\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"root_id\":\"682bef270ccfe3000763cbb8\",\"out_owner\":null,\"node_bom_core_version\":null,\"owner__r\":{\"picAddr\":\"\",\"mobile\":null,\"description\":\"\",\"dept\":\"1000\",\"supervisorId\":null,\"title\":null,\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"position\":null,\"enterpriseName\":null,\"email\":\"\",\"status\":0},\"owner\":[\"1000\"],\"amount\":\"1.0000\",\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"last_modified_by\":[\"-10000\"],\"field_tuzm6__c\":null,\"mc_currency\":\"CNY\",\"record_type\":\"default__c\",\"last_modified_by__r\":{\"picAddr\":null,\"mobile\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"empNum\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"enterpriseName\":null,\"email\":null,\"status\":null},\"parent_bom_id\":\"682bef270ccfe3000763cbb8\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"682bef270ccfe3000763cbb9\",\"__amount\":\"1.0000\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"0.00\",\"__pricing_period\":\"1.00\",\"pricing_mode\":\"one\",\"__price_per_set\":0,\"price_per_set\":0,\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\",\"amount\"],\"_attrConMark\":true,\"_defaultAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"}]},\"67c66e7417873700016dbb91\":{\"name\":\"屏幕尺寸\",\"value_ids\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"order_field\":1,\"is_default\":\"1\"}]},\"67c66e7417873700016dbb91\":{\"name\":\"屏幕尺寸\",\"value_ids\":[{\"id\":\"67c66e7417873700016dbb92\",\"code\":\"14英寸\",\"name\":\"14英寸\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"67c6fd56d6c99800017f47ef\":{\"name\":\"长度\",\"value\":\"200\"}},\"rowId\":\"1747721739324913\",\"isChecked\":true,\"_level\":0,\"isShow\":true,\"_index\":0,\"nonstandard_attribute_json\":{},\"nonstandard_attribute\":\"\",\"noSelectDef\":true,\"attributeHtml\":\"颜色:红色;屏幕尺寸:14英寸;长度:200\",\"bomId\":\"682bef270ccfe3000763cbb9\",\"nonAttributes\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attributeValues\":{\"value\":\"10\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015882\",\"name\":\"红色\"}},{\"id\":\"67c66e7417873700016dbb91\",\"name\":\"屏幕尺寸\",\"attributeValues\":{\"id\":\"67c66e7417873700016dbb92\",\"name\":\"14英寸\"}}]}]") | getBomAttributeConstraintLines("[{\"condition_range\":[{\"product_path\":\"677b4821201f020007a52d49.68196f714ba23c00076bfb10\",\"product_name\":\"测试123>050601\",\"bom_id\":\"682bef270ccfe3000763cbb9\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015882\",\"name\":\"红色\"}]}],\"nonstandardAttribute\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]}]}],\"result_range\":[{\"product_path\":\"677b4821201f020007a52d49.681c6984c5600b00073cb6be\",\"product_name\":\"测试123>重型吊装机\",\"bom_id\":\"682bef270ccfe3000763cbba\",\"attribute\":[{\"id\":\"67c66e3017873700016da58b\",\"name\":\"CPU\",\"attribute_values\":[{\"id\":\"67c66e3017873700016da58e\",\"name\":\"i7\"}]}],\"nonstandardAttribute\":[{\"id\":\"67c6fd56d6c99800017f47ef\",\"name\":\"长度\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]},{\"id\":\"67d93bbdfab4bc0001bb27bb\",\"name\":\"限高\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"100\"}]}],\"_ruleId\":\"682bef270ccfe3000763cbb8.682befac0ccfe3000763f25a\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\"}],\"constraint_type\":\"3\",\"root_id\":\"682bef270ccfe3000763cbb8\",\"constraint_type__o\":null,\"_id\":\"682befac0ccfe3000763f25a\",\"current_root_new_path\":\"682bef270ccfe3000763cbb8\",\"_newRuleId\":\"682bef270ccfe3000763cbb8.682befac0ccfe3000763f25a\",\"productId__r\":\"测试123\",\"coreId\":\"682bef270ccfe3000763cbbb\",\"coreId__r\":\"20250520-001930\"}]")

    }


    def "checkBomConstraint method by apl with no function"() {
        given:
        User user = User.builder().tenantId("89242").userId("1000").build();
        CheckBomModel.Args args = getCheckBomModelArgs(true);
        when:
        PowerMockito.mockStatic(I18N);
        PowerMockito.when(I18N.text(any(), any())).thenReturn("函数不存在");
        PowerMockito.doReturn(functionLogicService).when(serviceFacade, "getFunctionLogicService");
        PowerMockito.doReturn(null).when(functionLogicService, "findUDefFunction", any(), any(), any());
        CheckBomModel.Result result = bomConstraintServiceImplTest.checkBomConstraint(user, args);
        then:
        ValidateException validateException = thrown(ValidateException); ;
        validateException.getMessage() == "函数不存在";
    }

    def "checkBomConstraint method by apl with exist function"() {
        given:
        User user = User.builder().tenantId("89242").userId("1000").build();
        CheckBomModel.Args args = getCheckBomModelArgs(true);
        when:
        PowerMockito.doReturn(functionLogicService).when(serviceFacade, "getFunctionLogicService");
        PowerMockito.doReturn(getFunction()).when(functionLogicService, "findUDefFunction", any(), any(), any());
        PowerMockito.doReturn(getRunResult()).when(functionLogicService, "executeUDefFunction", any(), any(), any(), any(), any());
        CheckBomModel.Result result = bomConstraintServiceImplTest.checkBomConstraint(user, args);
        then:
        result.success == true;
    }

    def "checkBomConstraint method by apl with exist function fail"() {
        given:
        User user = User.builder().tenantId("89242").userId("1000").build();
        CheckBomModel.Args args = getCheckBomModelArgs(true);
        when:
        PowerMockito.doReturn(functionLogicService).when(serviceFacade, "getFunctionLogicService");
        PowerMockito.doReturn(getFunction()).when(functionLogicService, "findUDefFunction", any(), any(), any());
        PowerMockito.doReturn(getRunResultFail()).when(functionLogicService, "executeUDefFunction", any(), any(), any(), any(), any());
        CheckBomModel.Result result = bomConstraintServiceImplTest.checkBomConstraint(user, args);
        then:
        result.success == false;
    }

    def "checkBomConstraint method by apl with exist function execute fail"() {
        given:
        User user = User.builder().tenantId("89242").userId("1000").build();
        CheckBomModel.Args args = getCheckBomModelArgs(true);
        when:
        PowerMockito.doReturn(functionLogicService).when(serviceFacade, "getFunctionLogicService");
        PowerMockito.doReturn(getFunction()).when(functionLogicService, "findUDefFunction", any(), any(), any());
        PowerMockito.doReturn(getRunResultError()).when(functionLogicService, "executeUDefFunction", any(), any(), any(), any(), any());
        CheckBomModel.Result result = bomConstraintServiceImplTest.checkBomConstraint(user, args);
        then:
        result.success == false;
    }

    def "test expressionCheck"() {
        given:
        BomFormulaModel.CheckArg arg = buildBomFormulaModelCheckArg(expressEmpty);
        when:
        BomFormulaModel.CheckResult result = null;
        ValidateException exception = null;
        boolean realResult = false;
        try {
            PowerMockito.mockStatic(I18N);
            PowerMockito.when(I18N.text(any())).thenReturn("参数不正确");
            PowerMockito.doReturn(getExpressionCheckResult(expressionCheckResult)).when(calculateService, "expressionCheck", any(ExpressionCheck.Arg.class), any(ServiceContext.class));
            result = bomConstraintServiceImplTest.expressionCheck(getServiceContext("", ""), arg);
        } catch (ValidateException e) {
            exception = e;
        }
        if (exception != null && result == null) {
            realResult = true;
        } else if (exception == null && result != null) {
            realResult = true;
        } else {
            realResult = false;
        }
        then:
        realResult == true;
        where:
        expressEmpty | expectResult | expressionCheckResult
        true         | true         | false
        false        | true         | false
        false        | true         | true
        false        | true         | true

    }

    def "test formulaCalculate"() {
        given:
        BomFormulaModel.CalculateArg arg = buildBomFormulaModelCalculateArg(formulaEmpty, selectBomListEmpty, bomIdEmpty, bomIdNotExist, expressEmpty, isSkipInvalidExpression);
        when:
        BomFormulaModel.CalculateResult result = null;
        ValidateException exception = null;
        try {
            PowerMockito.mockStatic(I18N);
            PowerMockito.when(I18N.text(any())).thenReturn("参数不正确");
            PowerMockito.when(I18N.text(any(), any())).thenReturn("参数不正确");
            PowerMockito.when(I18N.text(any(), any(), any())).thenReturn("参数不正确");
            PowerMockito.doReturn(buildObjectDescribe()).when(serviceFacade, "findObject", anyString(), anyString());
            PowerMockito.doNothing().when(expressionCalculateLogicService, "bulkCalculateWithExpression", any(IObjectDescribe.class), anyList(), anyList());
            result = bomConstraintServiceImplTest.formulaCalculate(getServiceContext("", ""), arg);
        } catch (ValidateException e) {
            exception = e;
        }
        then:
        (result == null && exception != null) || (result != null && exception == null);
        where:
        formulaEmpty | selectBomListEmpty | expressEmpty | bomIdEmpty | bomIdNotExist | isSkipInvalidExpression
        true         | true               | true         | true       | true          | false
        false        | true               | true         | true       | true          | false
        false        | false              | true         | true       | true          | false
        false        | false              | true         | false      | true          | false
        false        | false              | false        | false      | true          | false
        false        | false              | false        | false      | false         | false
        false        | false              | false        | false      | false         | true
    }

    def "test formulaCheck"() {
        given:
        BomFormulaModel.FormulaCheckArg arg = buildBomFormulaModelFormulaCheckArg(selectedBomListEmpty, formulasEmpty);
        when:
        PowerMockito.mockStatic(I18N);
        PowerMockito.when(I18N.text(any())).thenReturn("参数不正确");
        PowerMockito.when(I18N.text(any(), any())).thenReturn("参数不正确");
        PowerMockito.when(I18N.text(any(), any(), any())).thenReturn("参数不正确");
        PowerMockito.doReturn(buildObjectDescribe()).when(serviceFacade, "findObject", anyString(), anyString());
        PowerMockito.doNothing().when(expressionCalculateLogicService, "bulkCalculateWithExpression", any(IObjectDescribe.class), anyList(), anyList());
        BomFormulaModel.FormulaCheckResult result = null;
        ValidateException exception = null;
        try {
            result = bomConstraintServiceImplTest.formulaCheck(getServiceContext("", ""), arg);
        } catch (ValidateException e) {
            exception = e;
        }
        then:
        (result == null && exception != null) || (result != null && exception == null);
        where:
        selectedBomListEmpty | formulasEmpty
        true                 | true
        false                | true
        false                | false
    }

    def "test calculateByApl"() {
        given:
        BomFormulaModel.AplArg arg = buildBomFormulaModelAplArg(aplApiNameEmpty, selectBomListEmpty);
        when:
        PowerMockito.mockStatic(I18N);
        PowerMockito.when(I18N.text(any())).thenReturn("参数不正确");
        PowerMockito.when(I18N.text(any(), any())).thenReturn("参数不正确");
        PowerMockito.when(I18N.text(any(), any(), any())).thenReturn("参数不正确");
        PowerMockito.doReturn(functionLogicService).when(serviceFacade, "getFunctionLogicService");
        PowerMockito.doReturn(getIUdefFunction(functionExist)).when(functionLogicService, "findUDefFunction", any(), any(), any());
        PowerMockito.doReturn(getFunctionResult(executeFunctionControllerSuccess, dataListEmpty, formatError)).when(functionLogicService, "executeFunctionController", any(), any(), any());
        BomFormulaModel.AplResult result = null;
        ValidateException exception = null;
        try {
            result = bomConstraintServiceImplTest.calculateByApl(getServiceContext("", ""), arg);
        } catch (ValidateException e) {
            exception = e;
        }
        then:
        (result == null && exception != null) || (result != null && exception == null);
        where:
        aplApiNameEmpty | selectBomListEmpty | functionExist | executeFunctionControllerSuccess | dataListEmpty | formatError
        true            | true               | false         | false                            | true          | true
        false           | true               | false         | false                            | true          | true
        false           | false              | false         | false                            | true          | true
        false           | false              | true          | false                            | true          | true
        false           | false              | true          | true                             | true          | true
        false           | false              | true          | true                             | false         | true
        false           | false              | true          | true                             | false         | false
    }

    def "test buidlTriggerFormulaMap"() {
        given:
        List<IObjectData> bomConstraintLinesList = buildBomConstraintLinesList(bomConstraintLinesListEmpty);
        when:
        List<ObjectDataDocument> result = bomConstraintServiceImplTest.buidlTriggerFormulaMap(bomConstraintLinesList);
        then:
        result != null;
        where:
        bomConstraintLinesListEmpty | other
        true                        | false
        false                       | true
    }

    def "test matchAttr"() {
        given:
        def requireTable = HashBasedTable.create()
        def exclusionTable = HashBasedTable.create()
        def requireRangeTable = HashBasedTable.create()
        def r = new BomConstraintLineModel(
                product_name: "Product1",
                attribute: configAttributeList
        )
        def selectBomInfo = new CheckBomModel.BomInfo(
                attributes: selectAttributes
        )

        when:
        Whitebox.invokeMethod(bomConstraintServiceImplTest, "matchAttr", requireTable, exclusionTable, type, r, selectBomInfo, configAttributeList, requireRangeTable)

        then:
        if (type == EnumUtil.ConstraintType.must.getValue()) {
            assert requireTable.size() == expectedRequireSize
        } else if (type == EnumUtil.ConstraintType.unAllow.getValue()) {
            assert exclusionTable.size() == expectedExclusionSize
        } else if (type == EnumUtil.ConstraintType.range.getValue()) {
            assert requireRangeTable.size() == expectedRangeSize
        }

        where:
        type                                       | configAttributeList | selectAttributes | expectedRequireSize | expectedExclusionSize | expectedRangeSize | description
        // 测试空属性列表
        EnumUtil.ConstraintType.must.getValue()    | []                  | []               | 0                   | 0                     | 0                 | "Empty attribute lists"

        // 测试必选约束 - 属性值匹配
        EnumUtil.ConstraintType.must.getValue()    | [
                new BomConstraintLineModel.Attribute(
                        id: "attr1",
                        name: "Color",
                        attribute_values: [
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "red", name: "Red")
                        ]
                )
        ]                                                                | [
                new CheckBomModel.StandardAttribute(
                        id: "attr1",
                        attributeValues: new CheckBomModel.StandardAttributeValues(id: "red")
                )
        ]                                                                                   | 0                   | 0                     | 0                 | "Must constraint - matching attribute values"

        // 测试必选约束 - 属性值不匹配
        EnumUtil.ConstraintType.must.getValue()    | [
                new BomConstraintLineModel.Attribute(
                        id: "attr1",
                        name: "Color",
                        attribute_values: [
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "red", name: "Red")
                        ]
                )
        ]                                                                | [
                new CheckBomModel.StandardAttribute(
                        id: "attr1",
                        attributeValues: new CheckBomModel.StandardAttributeValues(id: "blue")
                )
        ]                                                                                   | 1                   | 0                     | 0                 | "Must constraint - non-matching attribute values"

        // 测试排除约束 - 属性值匹配
        EnumUtil.ConstraintType.unAllow.getValue() | [
                new BomConstraintLineModel.Attribute(
                        id: "attr1",
                        name: "Color",
                        attribute_values: [
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "red", name: "Red")
                        ]
                )
        ]                                                                | [
                new CheckBomModel.StandardAttribute(
                        id: "attr1",
                        attributeValues: new CheckBomModel.StandardAttributeValues(id: "red")
                )
        ]                                                                                   | 0                   | 1                     | 0                 | "Exclusion constraint - matching attribute values"

        // 测试范围约束 - 属性值匹配
        EnumUtil.ConstraintType.range.getValue()   | [
                new BomConstraintLineModel.Attribute(
                        id: "attr1",
                        name: "Color",
                        attribute_values: [
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "red", name: "Red"),
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "blue", name: "Blue")
                        ]
                )
        ]                                                                | [
                new CheckBomModel.StandardAttribute(
                        id: "attr1",
                        attributeValues: new CheckBomModel.StandardAttributeValues(id: "red")
                )
        ]                                                                                   | 0                   | 0                     | 0                 | "Range constraint - matching attribute values"

        // 测试范围约束 - 属性值不匹配
        EnumUtil.ConstraintType.range.getValue()   | [
                new BomConstraintLineModel.Attribute(
                        id: "attr1",
                        name: "Color",
                        attribute_values: [
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "red", name: "Red"),
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "blue", name: "Blue")
                        ]
                )
        ]                                                                | [
                new CheckBomModel.StandardAttribute(
                        id: "attr1",
                        attributeValues: new CheckBomModel.StandardAttributeValues(id: "green")
                )
        ]                                                                                   | 0                   | 0                     | 1                 | "Range constraint - non-matching attribute values"
        // 测试范围约束 - 属性值不匹配
        EnumUtil.ConstraintType.must.getValue()   | [
                new BomConstraintLineModel.Attribute(
                        id: "attr1",
                        name: "Color",
                        attribute_values: [
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "red", name: "Red"),
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "blue", name: "Blue")
                        ]
                )
        ]                                                                | []                                                                                   | 1                   | 0                     | 1                 | "Range constraint - non-matching attribute values"

        // 测试范围约束 - 属性值不匹配
        EnumUtil.ConstraintType.range.getValue()   | [
                new BomConstraintLineModel.Attribute(
                        id: "attr1",
                        name: "Color",
                        attribute_values: [
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "red", name: "Red"),
                                new BomConstraintLineModel.Attribute.AttributeValues(id: "blue", name: "Blue")
                        ]
                )
        ]                                                                | []                                                                                   | 1                   | 0                     | 1                 | "Range constraint - non-matching attribute values"

    }


    private List<IObjectData> buildBomConstraintLinesList(boolean bomConstraintLinesListEmpty) {
        if (bomConstraintLinesListEmpty) {
            return null;
        }
        String jsonStr = "[{\"condition_range\":[{\"product_path\":\"65bb47226d2fed00018eef9f.666ae4d77b88c000070c33f3\",\"product_name\":\"wsh组合\\u003e111\",\"bom_id\":\"667a96537584a20007493c79\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attribute_values\":[{\"id\":\"665f11b406153d00072218a2\",\"name\":\"铜\"}]},{\"id\":\"65e8759c82823000070742d9\",\"name\":\"回路数\",\"attribute_values\":[{\"id\":\"66028996678adb0007898a4a\",\"name\":\"10\"}]}]}],\"result_range\":[{\"product_path\":\"666ae4d77b88c000070c33f3.667e79c32faf7e000706fb46\",\"product_name\":\"111\\u003e漏电保护\",\"bom_id\":\"6699d50bfbb8a20007d64553\",\"formula\":[{\"expression\":\"\$667a96537584a20007493c79.amount\$*2+IF(\$667a96537584a20007493c7b#EXT#ATTR#65e8759c82823000070742d9\$\\u003d\\u003d\\u002766028996678adb0007898a4a\\u0027, 2, 3)\",\"field_name\":\"amount\",\"expression_label\":\"【数量】等于：\$111.数量\$*2+IF(\$智能摄像头.回路数.10\$,2,3)\"}]},{\"product_path\":\"65bb47226d2fed00018eef9f.665ec11c8ad1e00007a4c14a\",\"product_name\":\"wsh组合\\u003e智能摄像头\",\"bom_id\":\"667a96537584a20007493c7b\",\"attribute\":[],\"nonstandardAttribute\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attribute_values\":[{\"minValue\":\"1\",\"maxValue\":\"12\"}]}]}],\"constraint_type\":\"1\",\"root_id\":\"667a96537584a20007493c77\",\"_id\":\"66c5872de3cc1d00072e87c9\"}]";
        List<ObjectDataDocument> list = com.facishare.enterprise.common.util.JsonUtil.fromJsonByJackson(jsonStr, new TypeReference<List<ObjectDataDocument>>() {
        });
        return ObjectDataDocument.ofDataList(list);
    }

    private RunResult getFunctionResult(boolean executeFunctionControllerSuccess, boolean dataListEmpty, boolean formatError) {
        RunResult runResult = new RunResult();
        if (executeFunctionControllerSuccess) {
            runResult.setSuccess(true);
            Map functionMap = new HashMap();
            if (!dataListEmpty) {
                if (!formatError) {
                    String dataList = "[{\"bom_id\":\"66e3b7709a72e4000743e62e\",\"new_bom_path\":\"66e3b7709a72e4000743e62d.66e3b7709a72e4000743e62e\",\"product_id\":\"66e2ba3f43ccce0007f1f26f\",\"valueResultList\":[{\"type\":0,\"fieldName\":\"amount\",\"value\":{\"amount\":6}},{\"type\":1,\"fieldName\":\"attribute\",\"value\":{\"65d4635dedb9f50001015881\":\"65d4635dedb9f50001015883\",\"665f11b406153d000722189f\":\"665f11b406153d00072218a3\"}},{\"type\":2,\"fieldName\":\"nonAttribute\",\"value\":{\"65954eb112e1da0006468c12\":\"4\"}}]}]";
                    List<BomFormulaModel.FormulaResult> list = com.facishare.enterprise.common.util.JsonUtil.fromJsonByJackson(dataList, new TypeReference<List<BomFormulaModel.FormulaResult>>() {
                    })
                    functionMap.put("dataList", list);
                } else {
                    functionMap.put("dataList", "[]");
                }
            }
            runResult.setFunctionResult(functionMap);
        } else {
            runResult.setSuccess(false);
        }
        return runResult;
    }

    private IUdefFunction getIUdefFunction(boolean functionExist) {
        if (functionExist) {
            UdefFunction udefFunction = new UdefFunction();
            udefFunction.setIsActive(true);
            IUdefFunction function = UdefFunctionExt.of(udefFunction);
            return function;
        } else {
            return null
        }
    }

    private BomFormulaModel.AplArg buildBomFormulaModelAplArg(boolean aplApiNameEmpty, boolean selectBomListEmpty) {
        String jsonStr = "{\"aplApiName\":\"CstmCtrl_ME4hb__c\",\"triggerRowId\":\"1724916190116523\",\"selectBomList\":[{\"version\":\"6\",\"product_status\":\"1\",\"field_s27tu__c\":\"wsh组合\",\"new_bom_path\":\"667a96537584a20007493c77\",\"extend_obj_data_id\":\"65bb47236d2fed00018eefc2\",\"created_by\":[\"1000\"],\"object_describe_api_name\":\"ProductObj\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"pricing_attribute_ids\":[\"665f11b406153d000722189f\"],\"field_AhbB1__c\":\"0.00\",\"nonstandard_attribute_ids\":[\"65e6878b11714e00075612e5\"],\"tenant_id\":\"90242\",\"on_shelves_time\":1706772259066,\"core_id\":\"667a96537584a20007493c7d\",\"product_category_id__relation_ids\":\"6594c7c0cfd011000162fdde\",\"is_giveaway\":\"0\",\"data_own_department\":[\"1000\"],\"_id\":\"65bb47226d2fed00018eef9f\",\"nonstandardAttribute\":[{\"name\":\"件数\",\"default_value\":\"6\",\"id\":\"65e6878b11714e00075612e5\"}],\"is_saleable\":true,\"owner_department_id\":\"1000\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}],\"bom_id\":\"667a96537584a20007493c77\",\"product_category_id\":\"6594c7c0cfd011000162fdde\",\"owner_department\":\"研发部门\",\"create_time\":1706772259167,\"name\":\"wsh组合\",\"price\":\"100.00\",\"attribute_ids\":[\"665f11b406153d000722189f\"],\"is_package\":true,\"last_modified_time\":1724221842074,\"life_status\":\"normal\",\"lock_status\":\"0\",\"package\":\"CRM\",\"display_name\":\"wsh组合\",\"remark\":\"2\",\"is_deleted\":false,\"picture_path\":[],\"last_modified_by\":[\"1000\"],\"attribute76\":[\"665f11b406153d00072218a0\",\"665f11b406153d00072218a1\",\"665f11b406153d00072218a2\",\"665f11b406153d00072218a3\"],\"category\":\"1\",\"current_root_new_path\":\"667a96537584a20007493c77\",\"product_id\":\"65bb47226d2fed00018eef9f\",\"_isRoot\":true,\"isChecked\":true,\"product_id__r\":\"wsh组合\",\"rowId\":\"1724916190105509\",\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65e6878b11714e00075612e5\":{\"name\":\"件数\",\"value\":\"30\"}},\"nonstandard_attribute_json\":{\"65e6878b11714e00075612e5\":\"30\"},\"nonstandard_attribute\":\"件数:30\",\"bomId\":\"667a96537584a20007493c77\",\"nonAttributes\":[{\"id\":\"65e6878b11714e00075612e5\",\"name\":\"件数\",\"attributeValues\":{\"value\":\"30\"}}],\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}}],\"product_group_id\":\"\",\"isRoot\":true},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"0.00\",\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"product_group_id__r\":\"1\",\"product_id\":\"666ae4d77b88c000070c33f3\",\"owner_department_id\":\"1000\",\"searchAfterId\":[\"20240625004251\",\"667a96537584a20007493c79\"],\"modified_adjust_price\":150,\"price_mode\":\"1\",\"adjust_price__r\":\"CNY100.00\",\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c79\",\"product_bom_path\":[\"wsh组合\",\"111\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"field_74B23__c__r\":\"CNY0.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"increment\":\"1.000\",\"product_status\":\"已上架\",\"product_id__r\":\"111\",\"product_id__relation_ids\":\"666ae4d77b88c000070c33f3\",\"core_id\":\"667a96537584a20007493c7d\",\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},{\"id\":\"65e8759c82823000070742d9\",\"name\":\"回路数\",\"fieldNum\":72,\"attribute_values\":[{\"id\":\"66028996678adb0007898a4a\",\"code\":\"hl10\",\"name\":\"10\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4b\",\"code\":\"hl11\",\"name\":\"11\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4c\",\"code\":\"hl12\",\"name\":\"12\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4d\",\"code\":\"hl13\",\"name\":\"13\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4e\",\"code\":\"hl14\",\"name\":\"14\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4f\",\"code\":\"hl15\",\"name\":\"15\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a50\",\"code\":\"hl16\",\"name\":\"16\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a51\",\"code\":\"hl17\",\"name\":\"17\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a52\",\"code\":\"hl18\",\"name\":\"18\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a53\",\"code\":\"hl19\",\"name\":\"19\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742db\",\"code\":\"hl2\",\"name\":\"2\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a54\",\"code\":\"hl20\",\"name\":\"20\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a55\",\"code\":\"hl21\",\"name\":\"21\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a56\",\"code\":\"hl22\",\"name\":\"22\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a57\",\"code\":\"hl23\",\"name\":\"23\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a58\",\"code\":\"hl24\",\"name\":\"24\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a59\",\"code\":\"hl25\",\"name\":\"25\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5a\",\"code\":\"hl26\",\"name\":\"26\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5b\",\"code\":\"hl27\",\"name\":\"27\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5c\",\"code\":\"hl28\",\"name\":\"28\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5d\",\"code\":\"hl29\",\"name\":\"29\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742dc\",\"code\":\"hl3\",\"name\":\"3\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5e\",\"code\":\"hl30\",\"name\":\"30\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5f\",\"code\":\"hl31\",\"name\":\"31\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a60\",\"code\":\"hl32\",\"name\":\"32\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a61\",\"code\":\"hl33\",\"name\":\"33\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a62\",\"code\":\"hl34\",\"name\":\"34\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a63\",\"code\":\"hl35\",\"name\":\"35\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a64\",\"code\":\"hl36\",\"name\":\"36\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a65\",\"code\":\"hl37\",\"name\":\"37\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a66\",\"code\":\"hl38\",\"name\":\"38\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a67\",\"code\":\"hl39\",\"name\":\"39\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742dd\",\"code\":\"hl4\",\"name\":\"4\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a68\",\"code\":\"hl40\",\"name\":\"40\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a69\",\"code\":\"hl41\",\"name\":\"41\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6a\",\"code\":\"hl42\",\"name\":\"42\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6b\",\"code\":\"hl43\",\"name\":\"43\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6c\",\"code\":\"hl44\",\"name\":\"44\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6d\",\"code\":\"hl45\",\"name\":\"45\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6e\",\"code\":\"hl46\",\"name\":\"46\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6f\",\"code\":\"hl47\",\"name\":\"47\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a70\",\"code\":\"hl48\",\"name\":\"48\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a71\",\"code\":\"hl49\",\"name\":\"49\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742de\",\"code\":\"hl5\",\"name\":\"5\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a72\",\"code\":\"hl50\",\"name\":\"50\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a73\",\"code\":\"hl51\",\"name\":\"51\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a74\",\"code\":\"hl52\",\"name\":\"52\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a75\",\"code\":\"hl53\",\"name\":\"53\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a76\",\"code\":\"hl54\",\"name\":\"54\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a77\",\"code\":\"hl55\",\"name\":\"55\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a78\",\"code\":\"hl56\",\"name\":\"56\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a79\",\"code\":\"hl57\",\"name\":\"57\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a7a\",\"code\":\"hl58\",\"name\":\"58\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a7b\",\"code\":\"hl59\",\"name\":\"59\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742df\",\"code\":\"hl6\",\"name\":\"6\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a7c\",\"code\":\"hl60\",\"name\":\"60\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742e1\",\"code\":\"hl8\",\"name\":\"8\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a49\",\"code\":\"hl9\",\"name\":\"9\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742da\",\"code\":\"hl1\",\"name\":\"1\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742e0\",\"code\":\"hl7\",\"name\":\"7\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1721357580100,\"life_status\":\"normal\",\"enabled_status\":true,\"amount_editable\":true,\"product_group_id\":\"667a96537584a20007493c78\",\"current_root_new_path\":\"667a96537584a20007493c77\",\"order_by\":\"50\",\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c79\",\"product_id_path\":[\"65bb47226d2fed00018eef9f\",\"666ae4d77b88c000070c33f3\"],\"crumb_bread\":[\"667a96537584a20007493c77\",\"667a96537584a20007493c78\",\"667a96537584a20007493c79\"],\"extend_obj_data_id\":\"667a96547584a20007493d82\",\"amount_any\":false,\"node_type\":\"standard\",\"total_num\":11,\"owner_department\":\"研发部门\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1719309908852,\"created_by\":[\"1000\"],\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240625-001377\",\"bom_id__r\":\"20240625004251\",\"name\":\"20240625004251\",\"_id\":\"667a96537584a20007493c79\",\"selected_by_default\":true,\"core_id__relation_ids\":\"667a96537584a20007493c7d\",\"adjust_price\":150,\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"wsh组合\",\"1\",\"111\"],\"root_id\":\"667a96537584a20007493c77\",\"amount\":\"1.0\",\"mc_currency\":\"CNY\",\"product_group_id__relation_ids\":\"667a96537584a20007493c78\",\"record_type\":\"default__c\",\"parent_bom_id\":\"667a96537584a20007493c77\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"667a96537584a20007493c79\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"100.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"_attrConMark\":true,\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65e8759c82823000070742d9\":{\"name\":\"回路数\",\"value_ids\":[{\"id\":\"65e8759c82823000070742e0\",\"code\":\"hl7\",\"name\":\"7\",\"is_default\":\"1\"}]}},\"rowId\":\"1724916190116520\",\"pid\":\"1724916190116519\",\"isChecked\":true,\"isShow\":false,\"_level\":1,\"_index\":1,\"bomId\":\"667a96537584a20007493c79\",\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}},{\"id\":\"65e8759c82823000070742d9\",\"name\":\"回路数\",\"attributeValues\":{\"id\":\"65e8759c82823000070742e0\",\"name\":\"7\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"0.00\",\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c79.6699d50bfbb8a20007d64553\",\"product_id_path\":[\"65bb47226d2fed00018eef9f\",\"666ae4d77b88c000070c33f3\",\"667e79c32faf7e000706fb46\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"667a96537584a20007493c77\",\"667a96537584a20007493c78\",\"667a96537584a20007493c79\",\"6699d50bfbb8a20007d64553\"],\"extend_obj_data_id\":\"6699d50cfbb8a20007d6470b\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"667e79c32faf7e000706fb46\",\"owner_department_id\":\"1000\",\"total_num\":11,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240719004413\",\"6699d50bfbb8a20007d64553\"],\"modified_adjust_price\":\"10.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY10.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1721357580014,\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c79.6699d50bfbb8a20007d64553\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240625-001377\",\"bom_id__r\":\"20240719004413\",\"product_bom_path\":[\"wsh组合\",\"111\",\"漏电保护\"],\"name\":\"20240719004413\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"6699d50bfbb8a20007d64553\",\"field_74B23__c__r\":\"CNY0.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":true,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"667a96537584a20007493c7d\",\"product_id__r\":\"漏电保护\",\"product_id__relation_ids\":\"667e79c32faf7e000706fb46\",\"adjust_price\":\"10.00\",\"core_id\":\"667a96537584a20007493c7d\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"wsh组合\",\"1\",\"111\",\"漏电保护\"],\"product_life_status__v\":\"normal\",\"root_id\":\"667a96537584a20007493c77\",\"mc_functional_currency\":\"CNY\",\"amount\":\"5.0\",\"is_package\":false,\"last_modified_time\":1723186197430,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"667a96537584a20007493c79\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"667a96537584a20007493c77\",\"order_by\":\"10\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"6699d50bfbb8a20007d64553\",\"__amount\":\"5.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"10.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"rowId\":\"1724916190116521\",\"pid\":\"1724916190116520\",\"isChecked\":true,\"isShow\":false,\"_level\":2,\"_index\":2,\"bomId\":\"6699d50bfbb8a20007d64553\"},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"0.00\",\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c79.6699d50bfbb8a20007d64554\",\"product_id_path\":[\"65bb47226d2fed00018eef9f\",\"666ae4d77b88c000070c33f3\",\"6683a73f5d36590007c9c96e\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"667a96537584a20007493c77\",\"667a96537584a20007493c78\",\"667a96537584a20007493c79\",\"6699d50bfbb8a20007d64554\"],\"extend_obj_data_id\":\"6699d50cfbb8a20007d6470c\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"6683a73f5d36590007c9c96e\",\"owner_department_id\":\"1000\",\"total_num\":11,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240719004414\",\"6699d50bfbb8a20007d64554\"],\"modified_adjust_price\":\"20.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY20.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1721357580015,\"order_field\":\"2\",\"product_life_status\":\"正常\",\"version\":\"1\",\"created_by\":[\"1000\"],\"bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c79.6699d50bfbb8a20007d64554\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240625-001377\",\"bom_id__r\":\"20240719004414\",\"product_bom_path\":[\"wsh组合\",\"111\",\"测试产品属性\"],\"name\":\"20240719004414\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"6699d50bfbb8a20007d64554\",\"field_74B23__c__r\":\"CNY0.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"667a96537584a20007493c7d\",\"product_id__r\":\"测试产品属性\",\"product_id__relation_ids\":\"6683a73f5d36590007c9c96e\",\"adjust_price\":\"20.00\",\"core_id\":\"667a96537584a20007493c7d\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"product_life_status__v\":\"normal\",\"root_id\":\"667a96537584a20007493c77\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},{\"id\":\"65e8759c82823000070742d9\",\"name\":\"回路数\",\"fieldNum\":72,\"attribute_values\":[{\"id\":\"66028996678adb0007898a4a\",\"code\":\"hl10\",\"name\":\"10\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4b\",\"code\":\"hl11\",\"name\":\"11\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4c\",\"code\":\"hl12\",\"name\":\"12\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4d\",\"code\":\"hl13\",\"name\":\"13\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4e\",\"code\":\"hl14\",\"name\":\"14\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4f\",\"code\":\"hl15\",\"name\":\"15\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a50\",\"code\":\"hl16\",\"name\":\"16\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a51\",\"code\":\"hl17\",\"name\":\"17\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a52\",\"code\":\"hl18\",\"name\":\"18\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a53\",\"code\":\"hl19\",\"name\":\"19\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742db\",\"code\":\"hl2\",\"name\":\"2\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a54\",\"code\":\"hl20\",\"name\":\"20\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a55\",\"code\":\"hl21\",\"name\":\"21\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a56\",\"code\":\"hl22\",\"name\":\"22\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a57\",\"code\":\"hl23\",\"name\":\"23\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a58\",\"code\":\"hl24\",\"name\":\"24\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a59\",\"code\":\"hl25\",\"name\":\"25\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5a\",\"code\":\"hl26\",\"name\":\"26\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5b\",\"code\":\"hl27\",\"name\":\"27\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5c\",\"code\":\"hl28\",\"name\":\"28\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5d\",\"code\":\"hl29\",\"name\":\"29\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742dc\",\"code\":\"hl3\",\"name\":\"3\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5e\",\"code\":\"hl30\",\"name\":\"30\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5f\",\"code\":\"hl31\",\"name\":\"31\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a60\",\"code\":\"hl32\",\"name\":\"32\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a61\",\"code\":\"hl33\",\"name\":\"33\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a62\",\"code\":\"hl34\",\"name\":\"34\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a63\",\"code\":\"hl35\",\"name\":\"35\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a64\",\"code\":\"hl36\",\"name\":\"36\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a65\",\"code\":\"hl37\",\"name\":\"37\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a66\",\"code\":\"hl38\",\"name\":\"38\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a67\",\"code\":\"hl39\",\"name\":\"39\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742dd\",\"code\":\"hl4\",\"name\":\"4\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a68\",\"code\":\"hl40\",\"name\":\"40\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a69\",\"code\":\"hl41\",\"name\":\"41\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6a\",\"code\":\"hl42\",\"name\":\"42\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6b\",\"code\":\"hl43\",\"name\":\"43\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6c\",\"code\":\"hl44\",\"name\":\"44\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6d\",\"code\":\"hl45\",\"name\":\"45\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6e\",\"code\":\"hl46\",\"name\":\"46\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6f\",\"code\":\"hl47\",\"name\":\"47\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a70\",\"code\":\"hl48\",\"name\":\"48\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a71\",\"code\":\"hl49\",\"name\":\"49\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742de\",\"code\":\"hl5\",\"name\":\"5\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a72\",\"code\":\"hl50\",\"name\":\"50\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a73\",\"code\":\"hl51\",\"name\":\"51\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a74\",\"code\":\"hl52\",\"name\":\"52\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a75\",\"code\":\"hl53\",\"name\":\"53\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a76\",\"code\":\"hl54\",\"name\":\"54\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a77\",\"code\":\"hl55\",\"name\":\"55\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a78\",\"code\":\"hl56\",\"name\":\"56\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a79\",\"code\":\"hl57\",\"name\":\"57\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a7a\",\"code\":\"hl58\",\"name\":\"58\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a7b\",\"code\":\"hl59\",\"name\":\"59\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742df\",\"code\":\"hl6\",\"name\":\"6\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a7c\",\"code\":\"hl60\",\"name\":\"60\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742e0\",\"code\":\"hl7\",\"name\":\"7\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742e1\",\"code\":\"hl8\",\"name\":\"8\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a49\",\"code\":\"hl9\",\"name\":\"9\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742da\",\"code\":\"hl1\",\"name\":\"1\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"owner__r\":{\"picAddr\":\"\",\"description\":\"\",\"dept\":\"1000\",\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"email\":\"\",\"status\":0},\"owner\":[\"1000\"],\"amount\":\"1.0\",\"is_package\":false,\"last_modified_time\":1721357580015,\"life_status\":\"normal\",\"enabled_status\":true,\"last_modified_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"created_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"last_modified_by\":[\"1000\"],\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"last_modified_by__r\":{\"picAddr\":\"\",\"description\":\"\",\"dept\":\"1000\",\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"email\":\"\",\"status\":0},\"product_group_id\":\"\",\"parent_bom_id\":\"667a96537584a20007493c79\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"667a96537584a20007493c77\",\"order_by\":\"20\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"6699d50bfbb8a20007d64554\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"20.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"_defaultAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65e8759c82823000070742d9\":{\"name\":\"回路数\",\"value_ids\":[{\"id\":\"65e8759c82823000070742da\",\"code\":\"hl1\",\"name\":\"1\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65e8759c82823000070742d9\":{\"name\":\"回路数\",\"value_ids\":[{\"id\":\"65e8759c82823000070742da\",\"code\":\"hl1\",\"name\":\"1\",\"is_default\":\"1\"}]}},\"rowId\":\"1724916190116522\",\"pid\":\"1724916190116520\",\"isChecked\":false,\"isShow\":false,\"_level\":2,\"_index\":3,\"bomId\":\"6699d50bfbb8a20007d64554\",\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}},{\"id\":\"65e8759c82823000070742d9\",\"name\":\"回路数\",\"attributeValues\":{\"id\":\"65e8759c82823000070742da\",\"name\":\"1\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"0.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"product_group_id__r\":\"2\",\"product_id\":\"665ec11c8ad1e00007a4c14a\",\"owner_department_id\":\"1000\",\"searchAfterId\":[\"20240625004252\",\"667a96537584a20007493c7b\"],\"modified_adjust_price\":200,\"price_mode\":\"2\",\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"4\",\"bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c7b\",\"price_book_id\":\"6594c79bc715080007e4b05b\",\"product_bom_path\":[\"wsh组合\",\"智能摄像头\"],\"field_74B23__c__r\":\"CNY0.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"increment\":\"1.000\",\"product_status\":\"已上架\",\"product_id__r\":\"智能摄像头\",\"product_id__relation_ids\":\"665ec11c8ad1e00007a4c14a\",\"core_id\":\"667a96537584a20007493c7d\",\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"65e8759c82823000070742d9\",\"name\":\"回路数\",\"fieldNum\":72,\"attribute_values\":[{\"id\":\"66028996678adb0007898a4a\",\"code\":\"hl10\",\"name\":\"10\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4b\",\"code\":\"hl11\",\"name\":\"11\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4c\",\"code\":\"hl12\",\"name\":\"12\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4d\",\"code\":\"hl13\",\"name\":\"13\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4e\",\"code\":\"hl14\",\"name\":\"14\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a4f\",\"code\":\"hl15\",\"name\":\"15\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a50\",\"code\":\"hl16\",\"name\":\"16\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a51\",\"code\":\"hl17\",\"name\":\"17\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a52\",\"code\":\"hl18\",\"name\":\"18\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a53\",\"code\":\"hl19\",\"name\":\"19\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742db\",\"code\":\"hl2\",\"name\":\"2\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a54\",\"code\":\"hl20\",\"name\":\"20\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a55\",\"code\":\"hl21\",\"name\":\"21\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a56\",\"code\":\"hl22\",\"name\":\"22\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a57\",\"code\":\"hl23\",\"name\":\"23\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a58\",\"code\":\"hl24\",\"name\":\"24\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a59\",\"code\":\"hl25\",\"name\":\"25\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5a\",\"code\":\"hl26\",\"name\":\"26\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5b\",\"code\":\"hl27\",\"name\":\"27\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5c\",\"code\":\"hl28\",\"name\":\"28\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5d\",\"code\":\"hl29\",\"name\":\"29\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742dc\",\"code\":\"hl3\",\"name\":\"3\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5e\",\"code\":\"hl30\",\"name\":\"30\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a5f\",\"code\":\"hl31\",\"name\":\"31\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a60\",\"code\":\"hl32\",\"name\":\"32\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a61\",\"code\":\"hl33\",\"name\":\"33\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a62\",\"code\":\"hl34\",\"name\":\"34\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a63\",\"code\":\"hl35\",\"name\":\"35\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a64\",\"code\":\"hl36\",\"name\":\"36\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a65\",\"code\":\"hl37\",\"name\":\"37\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a66\",\"code\":\"hl38\",\"name\":\"38\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a67\",\"code\":\"hl39\",\"name\":\"39\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742dd\",\"code\":\"hl4\",\"name\":\"4\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a68\",\"code\":\"hl40\",\"name\":\"40\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a69\",\"code\":\"hl41\",\"name\":\"41\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6a\",\"code\":\"hl42\",\"name\":\"42\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6b\",\"code\":\"hl43\",\"name\":\"43\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6c\",\"code\":\"hl44\",\"name\":\"44\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6d\",\"code\":\"hl45\",\"name\":\"45\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6e\",\"code\":\"hl46\",\"name\":\"46\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a6f\",\"code\":\"hl47\",\"name\":\"47\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a70\",\"code\":\"hl48\",\"name\":\"48\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a71\",\"code\":\"hl49\",\"name\":\"49\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742de\",\"code\":\"hl5\",\"name\":\"5\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a72\",\"code\":\"hl50\",\"name\":\"50\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a73\",\"code\":\"hl51\",\"name\":\"51\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a74\",\"code\":\"hl52\",\"name\":\"52\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a75\",\"code\":\"hl53\",\"name\":\"53\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a76\",\"code\":\"hl54\",\"name\":\"54\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a77\",\"code\":\"hl55\",\"name\":\"55\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a78\",\"code\":\"hl56\",\"name\":\"56\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a79\",\"code\":\"hl57\",\"name\":\"57\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a7a\",\"code\":\"hl58\",\"name\":\"58\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a7b\",\"code\":\"hl59\",\"name\":\"59\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742df\",\"code\":\"hl6\",\"name\":\"6\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a7c\",\"code\":\"hl60\",\"name\":\"60\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742e0\",\"code\":\"hl7\",\"name\":\"7\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742e1\",\"code\":\"hl8\",\"name\":\"8\",\"is_default\":\"0\"},{\"id\":\"66028996678adb0007898a49\",\"code\":\"hl9\",\"name\":\"9\",\"is_default\":\"0\"},{\"id\":\"65e8759c82823000070742da\",\"code\":\"hl1\",\"name\":\"1\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1724397305268,\"life_status\":\"normal\",\"enabled_status\":true,\"amount_editable\":true,\"product_group_id\":\"667a96537584a20007493c7a\",\"current_root_new_path\":\"667a96537584a20007493c77\",\"order_by\":\"10\",\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"price_book_product_id__r\":\"PBProdCode20240604001703\",\"price_book_product_id\":\"665ec11c8ad1e00007a4c14a90242\",\"new_bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c7b\",\"product_id_path\":[\"65bb47226d2fed00018eef9f\",\"665ec11c8ad1e00007a4c14a\"],\"crumb_bread\":[\"667a96537584a20007493c77\",\"667a96537584a20007493c7a\",\"667a96537584a20007493c7b\"],\"extend_obj_data_id\":\"667a96547584a20007493d83\",\"amount_any\":false,\"node_type\":\"standard\",\"total_num\":11,\"owner_department\":\"研发部门\",\"price_book_id__r\":\"标准价目表\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1719309908853,\"created_by\":[\"1000\"],\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240625-001377\",\"bom_id__r\":\"20240625004252\",\"name\":\"20240625004252\",\"_id\":\"667a96537584a20007493c7b\",\"selected_by_default\":false,\"core_id__relation_ids\":\"667a96537584a20007493c7d\",\"adjust_price\":200,\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"wsh组合\",\"2\",\"智能摄像头\"],\"root_id\":\"667a96537584a20007493c77\",\"amount\":\"10.0\",\"mc_currency\":\"CNY\",\"product_group_id__relation_ids\":\"667a96537584a20007493c7a\",\"record_type\":\"default__c\",\"parent_bom_id\":\"667a96537584a20007493c77\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"667a96537584a20007493c7b\",\"__amount\":\"10.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":200,\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\",\"price_book_id\"],\"selectedAttr\":{\"65e8759c82823000070742d9\":{\"name\":\"回路数\",\"value_ids\":[{\"id\":\"66028996678adb0007898a7c\",\"name\":\"60\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"30\"}},\"rowId\":\"1724916190116515\",\"pid\":\"1724916190116514\",\"isChecked\":false,\"isShow\":false,\"_level\":1,\"_index\":5,\"bomId\":\"667a96537584a20007493c7b\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"30\"}}],\"attributes\":[{\"id\":\"65e8759c82823000070742d9\",\"name\":\"回路数\",\"attributeValues\":{\"id\":\"66028996678adb0007898a7c\",\"name\":\"60\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"0.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"},{\"name\":\"规格\",\"default_value\":\"10\",\"id\":\"6596a8d92d49e20007f1a234\"}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c7b.6699d50bfbb8a20007d64555\",\"product_id_path\":[\"65bb47226d2fed00018eef9f\",\"665ec11c8ad1e00007a4c14a\",\"663c714a35634d0007a7e2e2\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"667a96537584a20007493c77\",\"667a96537584a20007493c7a\",\"667a96537584a20007493c7b\",\"6699d50bfbb8a20007d64555\"],\"extend_obj_data_id\":\"6699d50cfbb8a20007d6470d\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"663c714a35634d0007a7e2e2\",\"owner_department_id\":\"1000\",\"total_num\":11,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240719004415\",\"6699d50bfbb8a20007d64555\"],\"modified_adjust_price\":\"30.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY30.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1721357580016,\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"1\",\"created_by\":[\"1000\"],\"bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c7b.6699d50bfbb8a20007d64555\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240625-001377\",\"bom_id__r\":\"20240719004415\",\"product_bom_path\":[\"wsh组合\",\"智能摄像头\",\"B1\"],\"name\":\"20240719004415\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"6699d50bfbb8a20007d64555\",\"field_74B23__c__r\":\"CNY0.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":true,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"667a96537584a20007493c7d\",\"product_id__r\":\"B1\",\"product_id__relation_ids\":\"663c714a35634d0007a7e2e2\",\"adjust_price\":\"30.00\",\"core_id\":\"667a96537584a20007493c7d\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"wsh组合\",\"2\",\"智能摄像头\",\"B1\"],\"product_life_status__v\":\"normal\",\"root_id\":\"667a96537584a20007493c77\",\"attribute\":[{\"id\":\"66b09e1fb46abe00081c67c7\",\"name\":\"精密配电柜型号\",\"fieldNum\":73,\"attribute_values\":[{\"id\":\"66b09e1fb46abe00081c67c9\",\"code\":\"A-250A\",\"name\":\"A-250A\",\"is_default\":\"0\"},{\"id\":\"66b09e1fb46abe00081c67ca\",\"code\":\"A-400A\",\"name\":\"A-400A\",\"is_default\":\"0\"},{\"id\":\"66b09e1fb46abe00081c67cb\",\"code\":\"定制\",\"name\":\"定制\",\"is_default\":\"0\"},{\"id\":\"66b09e1fb46abe00081c67c8\",\"code\":\"A-160A\",\"name\":\"A-160A\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"owner__r\":{\"picAddr\":\"\",\"description\":\"\",\"dept\":\"1000\",\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"email\":\"\",\"status\":0},\"owner\":[\"1000\"],\"amount\":\"2.0\",\"is_package\":false,\"last_modified_time\":1721357580016,\"life_status\":\"normal\",\"enabled_status\":true,\"last_modified_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"created_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"last_modified_by\":[\"1000\"],\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"last_modified_by__r\":{\"picAddr\":\"\",\"description\":\"\",\"dept\":\"1000\",\"empNum\":\"\",\"modifyTime\":1704250614802,\"post\":\"\",\"createTime\":1704249221204,\"phone\":\"\",\"name\":\"CRM管理员\",\"nickname\":\"CRM管理员\",\"tenantId\":\"90242\",\"id\":\"1000\",\"email\":\"\",\"status\":0},\"product_group_id\":\"\",\"parent_bom_id\":\"667a96537584a20007493c7b\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"667a96537584a20007493c77\",\"order_by\":\"30\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"6699d50bfbb8a20007d64555\",\"__amount\":\"2.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"30.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"_defaultAttr\":{\"66b09e1fb46abe00081c67c7\":{\"name\":\"精密配电柜型号\",\"value_ids\":[{\"id\":\"66b09e1fb46abe00081c67c8\",\"code\":\"A-160A\",\"name\":\"A-160A\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"66b09e1fb46abe00081c67c7\":{\"name\":\"精密配电柜型号\",\"value_ids\":[{\"id\":\"66b09e1fb46abe00081c67c8\",\"code\":\"A-160A\",\"name\":\"A-160A\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"},\"6596a8d92d49e20007f1a234\":{\"name\":\"规格\",\"value\":\"10\"}},\"rowId\":\"1724916190116516\",\"pid\":\"1724916190116515\",\"isShow\":false,\"_level\":2,\"_index\":6,\"bomId\":\"6699d50bfbb8a20007d64555\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}},{\"id\":\"6596a8d92d49e20007f1a234\",\"name\":\"规格\",\"attributeValues\":{\"value\":\"10\"}}],\"attributes\":[{\"id\":\"66b09e1fb46abe00081c67c7\",\"name\":\"精密配电柜型号\",\"attributeValues\":{\"id\":\"66b09e1fb46abe00081c67c8\",\"name\":\"A-160A\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"0.00\",\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c7b.6699d50bfbb8a20007d64556\",\"product_id_path\":[\"65bb47226d2fed00018eef9f\",\"665ec11c8ad1e00007a4c14a\",\"663c715a35634d0007a7e51a\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"667a96537584a20007493c77\",\"667a96537584a20007493c7a\",\"667a96537584a20007493c7b\",\"6699d50bfbb8a20007d64556\"],\"extend_obj_data_id\":\"6699d50cfbb8a20007d6470e\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"663c715a35634d0007a7e51a\",\"owner_department_id\":\"1000\",\"total_num\":11,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240719004416\",\"6699d50bfbb8a20007d64556\"],\"modified_adjust_price\":\"40.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY40.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1721357580017,\"order_field\":\"2\",\"product_life_status\":\"正常\",\"version\":\"1\",\"created_by\":[\"1000\"],\"bom_path\":\"667a96537584a20007493c77.667a96537584a20007493c7b.6699d50bfbb8a20007d64556\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240625-001377\",\"bom_id__r\":\"20240719004416\",\"product_bom_path\":[\"wsh组合\",\"智能摄像头\",\"B2\"],\"name\":\"20240719004416\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"6699d50bfbb8a20007d64556\",\"field_74B23__c__r\":\"CNY0.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"667a96537584a20007493c7d\",\"product_id__r\":\"B2\",\"product_id__relation_ids\":\"663c715a35634d0007a7e51a\",\"adjust_price\":\"40.00\",\"core_id\":\"667a96537584a20007493c7d\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"wsh组合\",\"2\",\"智能摄像头\",\"B2\"],\"product_life_status__v\":\"normal\",\"root_id\":\"667a96537584a20007493c77\",\"mc_functional_currency\":\"CNY\",\"amount\":\"3.0\",\"is_package\":false,\"last_modified_time\":1721357580017,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"667a96537584a20007493c7b\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"667a96537584a20007493c77\",\"order_by\":\"40\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"6699d50bfbb8a20007d64556\",\"__amount\":\"3.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"40.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"rowId\":\"1724916190116517\",\"pid\":\"1724916190116515\",\"isShow\":false,\"_level\":2,\"_index\":7,\"bomId\":\"6699d50bfbb8a20007d64556\"}]}";
        BomFormulaModel.AplArg arg = com.facishare.enterprise.common.util.JsonUtil.fromJson(jsonStr, BomFormulaModel.AplArg.class);
        if (aplApiNameEmpty) {
            arg.setAplApiName(null);
            return arg;
        }
        if (selectBomListEmpty) {
            arg.setSelectBomList(null);
            return arg;
        }
        return arg;
    }

    private BomFormulaModel.FormulaCheckArg buildBomFormulaModelFormulaCheckArg(boolean selectedBomListEmpty, boolean formulasEmpty) {
        String jsonStr = "{\"formulas\":[{\"expression\":\"\$666aa20c3e576b00075a3158#EXT#NON_ATTR#65954eb112e1da0006468c12\$*2\",\"bom_id\":\"666aa20c3e576b00075a3159\",\"product_id\":\"66586309d38f3f000740ff43\",\"field_name\":\"amount\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.666aa20c3e576b00075a3159\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\"},{\"expression\":\"\$66cc527b9c544c0007bf8273#EXT#NON_ATTR#65954eb112e1da0006468c12\$*3\",\"bom_id\":\"666aa20c3e576b00075a3158\",\"product_id\":\"665862f5d38f3f000740fb57\",\"field_name\":\"amount\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.666aa20c3e576b00075a3158\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\"}],\"selectBomList\":[{\"version\":\"6\",\"product_status\":\"1\",\"field_s27tu__c\":\"测试数量自动赋值\",\"attribute30\":[\"65d4635dedb9f50001015886\",\"65d4635dedb9f50001015883\",\"65d4635dedb9f50001015888\",\"65d4635dedb9f50001015884\",\"65d4635dedb9f50001015885\",\"65d4635dedb9f50001015882\",\"65d4635dedb9f50001015887\",\"65d4635dedb9f50001015889\"],\"new_bom_path\":\"66cc527b9c544c0007bf826d\",\"extend_obj_data_id\":\"66c461b8367a51000769a427\",\"created_by\":[\"1000\"],\"object_describe_api_name\":\"ProductObj\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"pricing_attribute_ids\":[\"65d4635dedb9f50001015881\",\"665f11b406153d000722189f\"],\"field_AhbB1__c\":\"0.00\",\"nonstandard_attribute_ids\":[\"65954ea312e1da0006466941\",\"65954eb112e1da0006468c12\"],\"tenant_id\":\"90242\",\"on_shelves_time\":1724146104163,\"core_id\":\"66cc527b9c544c0007bf8274\",\"product_category_id__relation_ids\":\"6594c7c0cfd011000162fddf\",\"is_giveaway\":\"0\",\"data_own_department\":[\"1000\"],\"_id\":\"66c461b8367a51000769a38a\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"is_saleable\":true,\"owner_department_id\":\"1000\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}],\"bom_id\":\"66cc527b9c544c0007bf826d\",\"product_category_id\":\"6594c7c0cfd011000162fddf\",\"owner_department\":\"研发部门\",\"create_time\":1724146104284,\"name\":\"测试数量自动赋值\",\"price\":\"100.00\",\"attribute_ids\":[\"65d4635dedb9f50001015881\",\"665f11b406153d000722189f\"],\"product_code\":\"测试数量自动赋值\",\"is_package\":true,\"last_modified_time\":1724299878688,\"life_status\":\"normal\",\"lock_status\":\"0\",\"package\":\"CRM\",\"display_name\":\"测试数量自动赋值\",\"is_deleted\":false,\"last_modified_by\":[\"1000\"],\"attribute76\":[\"665f11b406153d00072218a1\",\"665f11b406153d00072218a0\",\"665f11b406153d00072218a2\",\"665f11b406153d00072218a3\"],\"category\":\"2\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"product_id\":\"66c461b8367a51000769a38a\",\"_isRoot\":true,\"isChecked\":true,\"product_id__r\":\"测试数量自动赋值\",\"rowId\":\"1724919752025560\",\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"bomId\":\"66cc527b9c544c0007bf826d\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}}],\"product_group_id\":\"\",\"isRoot\":true},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"product_group_id__r\":\"分组一\",\"product_id\":\"65d2be34c87a1900015011f9\",\"owner_department_id\":\"1000\",\"searchAfterId\":[\"20240826004818\",\"66cc527b9c544c0007bf826f\"],\"modified_adjust_price\":135,\"price_mode\":\"1\",\"adjust_price__r\":\"CNY100.00\",\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"4\",\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f\",\"product_bom_path\":[\"测试数量自动赋值\",\"子1\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"increment\":\"1.000\",\"product_status\":\"已上架\",\"product_id__r\":\"子1\",\"product_id__relation_ids\":\"65d2be34c87a1900015011f9\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1724919163636,\"life_status\":\"normal\",\"enabled_status\":true,\"amount_editable\":false,\"product_group_id\":\"66cc527b9c544c0007bf826e\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"order_by\":\"10\",\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"65d2be34c87a1900015011f9\"],\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf826e\",\"66cc527b9c544c0007bf826f\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8393\",\"amount_any\":false,\"node_type\":\"standard\",\"total_num\":4,\"owner_department\":\"研发部门\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491544,\"created_by\":[\"1000\"],\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004818\",\"name\":\"20240826004818\",\"_id\":\"66cc527b9c544c0007bf826f\",\"selected_by_default\":false,\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"adjust_price\":135,\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试数量自动赋值\",\"分组一\",\"子1\"],\"root_id\":\"66cc527b9c544c0007bf826d\",\"amount\":\"1.0\",\"mc_currency\":\"CNY\",\"product_group_id__relation_ids\":\"66cc527b9c544c0007bf826e\",\"record_type\":\"default__c\",\"parent_bom_id\":\"66cc527b9c544c0007bf826d\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf826f\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"100.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"_attrConMark\":true,\"_defaultAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015888\",\"name\":\"黄色\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032567\",\"pid\":\"1724919752032566\",\"isChecked\":true,\"isShow\":true,\"_level\":1,\"_index\":1,\"__insertRowId\":\"1724919752032566\",\"_hasRender\":false,\"bomId\":\"66cc527b9c544c0007bf826f\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015888\",\"name\":\"黄色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"20.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\",\"valueRange\":{}},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\",\"valueRange\":{}}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8270\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"65d2be34c87a1900015011f9\",\"66c4877d234df50007169729\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf826e\",\"66cc527b9c544c0007bf826f\",\"66cc527b9c544c0007bf8270\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8391\",\"amount_any\":true,\"node_type\":\"standard\",\"product_id\":\"66c4877d234df50007169729\",\"owner_department_id\":\"1000\",\"total_num\":4,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240826004816\",\"66cc527b9c544c0007bf8270\"],\"modified_adjust_price\":\"10.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY10.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491542,\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8270\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004816\",\"product_bom_path\":[\"测试数量自动赋值\",\"子1\",\"子1-1\"],\"name\":\"20240826004816\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"66cc527b9c544c0007bf8270\",\"field_74B23__c__r\":\"CNY20.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"product_id__r\":\"子1-1\",\"product_id__relation_ids\":\"66c4877d234df50007169729\",\"adjust_price\":\"10.00\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试数量自动赋值\",\"分组一\",\"子1\",\"子1-1\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf826d\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]}],\"mc_functional_currency\":\"CNY\",\"amount\":\"2.0\",\"is_package\":false,\"last_modified_time\":1724668264657,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":false,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf826f\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"max_amount\":\"20\",\"order_by\":\"20\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf8270\",\"__amount\":\"2.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"10.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"_defaultAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\",\"isRequired\":true}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\",\"isRequired\":true}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"5\"}},\"rowId\":\"1724919752032568\",\"pid\":\"1724919752032567\",\"isShow\":true,\"_level\":2,\"_index\":2,\"__insertRowId\":\"1724919752032567\",\"isChecked\":true,\"__checkedByConstraint\":true,\"__isConstraintSetting\":false,\"_hasRender\":false,\"bomId\":\"66cc527b9c544c0007bf8270\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"5\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\",\"valueRange\":{}},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\",\"valueRange\":{}}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8271\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"65d2be34c87a1900015011f9\",\"66c48788234df50007169a82\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf826e\",\"66cc527b9c544c0007bf826f\",\"66cc527b9c544c0007bf8271\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8392\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"66c48788234df50007169a82\",\"owner_department_id\":\"1000\",\"total_num\":4,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240826004817\",\"66cc527b9c544c0007bf8271\"],\"modified_adjust_price\":\"15.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY15.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491543,\"order_field\":\"2\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8271\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004817\",\"product_bom_path\":[\"测试数量自动赋值\",\"子1\",\"子1-2\"],\"name\":\"20240826004817\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"66cc527b9c544c0007bf8271\",\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"product_id__r\":\"子1-2\",\"product_id__relation_ids\":\"66c48788234df50007169a82\",\"adjust_price\":\"15.00\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试数量自动赋值\",\"分组一\",\"子1\",\"子1-2\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf826d\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]}],\"mc_functional_currency\":\"CNY\",\"amount\":\"1.0\",\"is_package\":false,\"last_modified_time\":1724668264657,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":false,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf826f\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"max_amount\":\"20\",\"order_by\":\"30\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf8271\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"15.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"_defaultAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\",\"isRequired\":true}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\",\"isRequired\":true}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032569\",\"pid\":\"1724919752032567\",\"isShow\":true,\"_level\":2,\"_index\":3,\"__insertRowId\":\"1724919752032568\",\"isChecked\":true,\"__isConstraintSetting\":false,\"_hasRender\":false,\"bomId\":\"66cc527b9c544c0007bf8271\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"20.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"product_group_id__r\":\"分组二\",\"product_id\":\"665862d8d38f3f000740f6e3\",\"owner_department_id\":\"1000\",\"searchAfterId\":[\"20240826004819\",\"66cc527b9c544c0007bf8273\"],\"modified_adjust_price\":160,\"price_mode\":\"1\",\"adjust_price__r\":\"CNY100.00\",\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"product_bom_path\":[\"测试数量自动赋值\",\"产品C\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"related_core_id\":\"6658637bd38f3f0007411c39\",\"field_74B23__c__r\":\"CNY20.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"increment\":\"1.000\",\"product_status\":\"已上架\",\"product_id__r\":\"产品C\",\"product_id__relation_ids\":\"665862d8d38f3f000740f6e3\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":true,\"last_modified_time\":1724668264657,\"life_status\":\"normal\",\"enabled_status\":true,\"amount_editable\":false,\"product_group_id\":\"66cc527b9c544c0007bf8272\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"max_amount\":\"20\",\"order_by\":\"40\",\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"665862d8d38f3f000740f6e3\"],\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf8272\",\"66cc527b9c544c0007bf8273\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8394\",\"amount_any\":false,\"node_type\":\"standard\",\"total_num\":4,\"owner_department\":\"研发部门\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491545,\"created_by\":[\"1000\"],\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004819\",\"name\":\"20240826004819\",\"_id\":\"66cc527b9c544c0007bf8273\",\"selected_by_default\":false,\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"adjust_price\":160,\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试数量自动赋值\",\"分组二\",\"产品C\"],\"root_id\":\"66cc527b9c544c0007bf826d\",\"node_bom_core_version\":\"b20240530-v001360\",\"owner\":[\"1000\"],\"amount\":\"2.0\",\"mc_currency\":\"CNY\",\"product_group_id__relation_ids\":\"66cc527b9c544c0007bf8272\",\"record_type\":\"default__c\",\"parent_bom_id\":\"66cc527b9c544c0007bf826d\",\"node_bom_core_type\":\"configure\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf8273\",\"__amount\":\"2.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"100.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"_attrConMark\":true,\"_defaultAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"4\"}},\"rowId\":\"1724919752032562\",\"pid\":\"1724919752032561\",\"isChecked\":true,\"isShow\":true,\"_level\":1,\"_index\":5,\"__insertRowId\":\"1724919752032561\",\"_hasRender\":false,\"bomId\":\"66cc527b9c544c0007bf8273\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"4\"}}],\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"0.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.666aa20c3e576b00075a3158\",\"product_id_path\":[\"665862d8d38f3f000740f6e3\",\"665862f5d38f3f000740fb57\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"6658637bd38f3f0007411c36\",\"666aa20c3e576b00075a3158\"],\"extend_obj_data_id\":\"666aa20d3e576b00075a33e1\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"665862f5d38f3f000740fb57\",\"owner_department_id\":\"1000\",\"total_num\":3,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240613004234\",\"666aa20c3e576b00075a3158\"],\"modified_adjust_price\":\"10.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY10.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1718264333872,\"min_amount\":\"1\",\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"6658637bd38f3f0007411c36.666aa20c3e576b00075a3158\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240530-001360\",\"bom_id__r\":\"20240613004234\",\"product_bom_path\":[\"产品C\",\"产品C1\"],\"name\":\"20240613004234\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"666aa20c3e576b00075a3158\",\"field_74B23__c__r\":\"CNY0.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"6658637bd38f3f0007411c39\",\"product_id__r\":\"产品C1\",\"product_id__relation_ids\":\"665862f5d38f3f000740fb57\",\"adjust_price\":\"10.00\",\"core_id\":\"6658637bd38f3f0007411c39\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"产品C\",\"产品C1\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf8273\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"amount\":1,\"is_package\":false,\"last_modified_time\":1718264689702,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf8273\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"max_amount\":\"1\",\"order_by\":\"10\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"666aa20c3e576b00075a3158\",\"__amount\":1,\"__isNotFlowRootPricebook\":false,\"disabled\":true,\"__adjust_price\":\"10.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"_attrConMark\":true,\"_defaultAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032563\",\"pid\":\"1724919752032562\",\"__parentBomRowId\":\"1724919752032562\",\"isShow\":true,\"_level\":2,\"_index\":6,\"__insertRowId\":\"1724919752032562\",\"isChecked\":true,\"__checkedByConstraint\":true,\"__isConstraintSetting\":true,\"_hasRender\":false,\"bomId\":\"666aa20c3e576b00075a3158\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.666aa20c3e576b00075a3159\",\"product_id_path\":[\"665862d8d38f3f000740f6e3\",\"66586309d38f3f000740ff43\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"6658637bd38f3f0007411c36\",\"666aa20c3e576b00075a3159\"],\"extend_obj_data_id\":\"666aa20d3e576b00075a33e2\",\"amount_any\":true,\"node_type\":\"standard\",\"product_id\":\"66586309d38f3f000740ff43\",\"owner_department_id\":\"1000\",\"total_num\":3,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240613004235\",\"666aa20c3e576b00075a3159\"],\"modified_adjust_price\":\"20.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY20.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1718264333873,\"min_amount\":\"1\",\"order_field\":\"2\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"6658637bd38f3f0007411c36.666aa20c3e576b00075a3159\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240530-001360\",\"bom_id__r\":\"20240613004235\",\"product_bom_path\":[\"产品C\",\"产品C2\"],\"name\":\"20240613004235\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"666aa20c3e576b00075a3159\",\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":true,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"6658637bd38f3f0007411c39\",\"product_id__r\":\"产品C2\",\"product_id__relation_ids\":\"66586309d38f3f000740ff43\",\"adjust_price\":\"20.00\",\"core_id\":\"6658637bd38f3f0007411c39\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"产品C\",\"产品C2\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf8273\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"amount\":\"1\",\"is_package\":false,\"last_modified_time\":1724156167251,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf8273\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"max_amount\":\"2\",\"order_by\":\"10\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"666aa20c3e576b00075a3159\",\"__amount\":\"1\",\"__isNotFlowRootPricebook\":false,\"disabled\":true,\"__adjust_price\":\"20.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"_attrConMark\":true,\"_defaultAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032564\",\"pid\":\"1724919752032562\",\"__parentBomRowId\":\"1724919752032562\",\"isShow\":true,\"_level\":2,\"_index\":7,\"__insertRowId\":\"1724919752032563\",\"isChecked\":true,\"__isConstraintSetting\":true,\"_hasRender\":false,\"bomId\":\"666aa20c3e576b00075a3159\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.66cd864c4c06060007be429a\",\"product_id_path\":[\"665862d8d38f3f000740f6e3\",\"66cd85d54c06060007be2754\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"6658637bd38f3f0007411c36\",\"66cd864c4c06060007be429a\"],\"extend_obj_data_id\":\"66cd864d4c06060007be4444\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"66cd85d54c06060007be2754\",\"owner_department_id\":\"1000\",\"total_num\":3,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240827004878\",\"66cd864c4c06060007be429a\"],\"modified_adjust_price\":\"30.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY30.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724745293362,\"order_field\":\"3\",\"product_life_status\":\"正常\",\"version\":\"1\",\"created_by\":[\"1000\"],\"bom_path\":\"6658637bd38f3f0007411c36.66cd864c4c06060007be429a\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240530-001360\",\"bom_id__r\":\"20240827004878\",\"product_bom_path\":[\"产品C\",\"产品C3\"],\"name\":\"20240827004878\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"66cd864c4c06060007be429a\",\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":true,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"6658637bd38f3f0007411c39\",\"product_id__r\":\"产品C3\",\"product_id__relation_ids\":\"66cd85d54c06060007be2754\",\"adjust_price\":\"30.00\",\"core_id\":\"6658637bd38f3f0007411c39\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"产品C\",\"产品C3\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf8273\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"amount\":\"1.0\",\"is_package\":false,\"last_modified_time\":1724745293362,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf8273\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"order_by\":\"10\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cd864c4c06060007be429a\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":true,\"__adjust_price\":\"30.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"_defaultAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}},\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032565\",\"pid\":\"1724919752032562\",\"__parentBomRowId\":\"1724919752032562\",\"isShow\":true,\"_level\":2,\"_index\":8,\"__insertRowId\":\"1724919752032564\",\"isChecked\":true,\"__isConstraintSetting\":true,\"_hasRender\":false,\"bomId\":\"66cd864c4c06060007be429a\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}}]}]}";
        BomFormulaModel.FormulaCheckArg arg = com.facishare.enterprise.common.util.JsonUtil.fromJson(jsonStr, BomFormulaModel.FormulaCheckArg.class);
        if (selectedBomListEmpty) {
            arg.setSelectBomList(null);
            return arg;
        }
        if (formulasEmpty) {
            arg.setFormulas(null);
            return arg;
        }
        return arg;
    }

    private IObjectDescribe buildObjectDescribe() {
        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.setApiName(BomConstants.DESC_API_NAME);
        return objectDescribe;
    }

    private BomFormulaModel.CalculateArg buildBomFormulaModelCalculateArg(boolean formulaEmpty, boolean selectBomListEmpty, boolean bomIdEmpty, boolean bomIdNotExist, boolean expressEmpty, boolean isSkipInvalidExpression) {
        String jsonStr = null;
        if (bomIdNotExist) {
            jsonStr = "{\"formulas\":[{\"expression\":\"IF(\$66cc527b9c544c0007bf82ff#EXT#ATTR#65d4635dedb9f50001015881\$=='65d4635dedb9f50001015886',\$66cc527b9c544c0007bf826f#EXT#NON_ATTR#65954ea312e1da0006466941\$-\$66cc527b9c544c0007bf8271#EXT#NON_ATTR#65954eb112e1da0006468c12\$-\$66cc527b9c544c0007bf826f.max_amount\$,4)\",\"bom_id\":\"66cc527b9c544c0007bf8271\",\"product_id\":\"66c48788234df50007169a82\",\"field_name\":\"amount\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8271\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\"}],\"selectBomList\":[{\"version\":\"6\",\"product_status\":\"1\",\"field_s27tu__c\":\"测试数量自动赋值\",\"attribute30\":[\"65d4635dedb9f50001015886\",\"65d4635dedb9f50001015883\",\"65d4635dedb9f50001015888\",\"65d4635dedb9f50001015884\",\"65d4635dedb9f50001015885\",\"65d4635dedb9f50001015882\",\"65d4635dedb9f50001015887\",\"65d4635dedb9f50001015889\"],\"new_bom_path\":\"66cc527b9c544c0007bf826d\",\"extend_obj_data_id\":\"66c461b8367a51000769a427\",\"object_describe_api_name\":\"ProductObj\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"pricing_attribute_ids\":[\"65d4635dedb9f50001015881\",\"665f11b406153d000722189f\"],\"field_AhbB1__c\":\"0.00\",\"nonstandard_attribute_ids\":[\"65954ea312e1da0006466941\",\"65954eb112e1da0006468c12\"],\"tenant_id\":\"90242\",\"on_shelves_time\":1724146104163,\"core_id\":\"66cc527b9c544c0007bf8274\",\"product_category_id__relation_ids\":\"6594c7c0cfd011000162fddf\",\"is_giveaway\":\"0\",\"data_own_department\":[\"1000\"],\"_id\":\"66c461b8367a51000769a38a\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"is_saleable\":true,\"owner_department_id\":\"1000\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}],\"bom_id\":\"66cc527b9c544c0007bf826d\",\"product_category_id\":\"6594c7c0cfd011000162fddf\",\"owner_department\":\"研发部门\",\"create_time\":1724146104284,\"name\":\"测试数量自动赋值\",\"price\":\"100.00\",\"attribute_ids\":[\"65d4635dedb9f50001015881\",\"665f11b406153d000722189f\"],\"product_code\":\"测试数量自动赋值\",\"is_package\":true,\"last_modified_time\":1724299878688,\"life_status\":\"normal\",\"lock_status\":\"0\",\"package\":\"CRM\",\"display_name\":\"测试数量自动赋值\",\"is_deleted\":false,\"last_modified_by\":[\"1000\"],\"attribute76\":[\"665f11b406153d00072218a1\",\"665f11b406153d00072218a0\",\"665f11b406153d00072218a2\",\"665f11b406153d00072218a3\"],\"category\":\"2\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"product_id\":\"66c461b8367a51000769a38a\",\"_isRoot\":true,\"isChecked\":true,\"product_id__r\":\"测试数量自动赋值\",\"rowId\":\"1724919752025560\",\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"bomId\":\"66cc527b9c544c0007bf826d\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}}],\"product_group_id\":\"\",\"isRoot\":true},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"product_group_id__r\":\"分组一\",\"product_id\":\"65d2be34c87a1900015011f9\",\"owner_department_id\":\"1000\",\"searchAfterId\":[\"20240826004818\",\"66cc527b9c544c0007bf826f\"],\"modified_adjust_price\":135,\"price_mode\":\"1\",\"adjust_price__r\":\"CNY100.00\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"4\",\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f\",\"product_bom_path\":[\"测试数量自动赋值\",\"子1\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"increment\":\"1.000\",\"product_status\":\"已上架\",\"product_id__r\":\"子1\",\"product_id__relation_ids\":\"65d2be34c87a1900015011f9\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1724919163636,\"life_status\":\"normal\",\"enabled_status\":true,\"amount_editable\":false,\"product_group_id\":\"66cc527b9c544c0007bf826e\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"order_by\":\"10\",\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"65d2be34c87a1900015011f9\"],\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf826e\",\"66cc527b9c544c0007bf826f\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8393\",\"amount_any\":false,\"node_type\":\"standard\",\"total_num\":4,\"owner_department\":\"研发部门\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491544,\"created_by\":[\"1000\"],\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004818\",\"name\":\"20240826004818\",\"_id\":\"66cc527b9c544c0007bf826f\",\"max_amount\":\"20\",\"selected_by_default\":false,\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"adjust_price\":135,\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试数量自动赋值\",\"分组一\",\"子1\"],\"root_id\":\"66cc527b9c544c0007bf826d\",\"owner\":[\"1000\"],\"amount\":\"1.0\",\"last_modified_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"mc_currency\":\"CNY\",\"product_group_id__relation_ids\":\"66cc527b9c544c0007bf826e\",\"record_type\":\"default__c\",\"parent_bom_id\":\"66cc527b9c544c0007bf826d\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf826f\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"100.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"_attrConMark\":true,\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015888\",\"name\":\"黄色\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032567\",\"pid\":\"1724919752032566\",\"isChecked\":true,\"isShow\":true,\"_level\":1,\"_index\":1,\"__insertRowId\":\"1724919752032566\",\"_hasRender\":false,\"bomId\":\"66cc527b9c544c0007bf826f\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015888\",\"name\":\"黄色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"20.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\",\"valueRange\":{}},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\",\"valueRange\":{}}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8270\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"65d2be34c87a1900015011f9\",\"66c4877d234df50007169729\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf826e\",\"66cc527b9c544c0007bf826f\",\"66cc527b9c544c0007bf8270\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8391\",\"amount_any\":true,\"node_type\":\"standard\",\"product_id\":\"66c4877d234df50007169729\",\"owner_department_id\":\"1000\",\"total_num\":4,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240826004816\",\"66cc527b9c544c0007bf8270\"],\"modified_adjust_price\":\"10.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY10.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491542,\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8270\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004816\",\"product_bom_path\":[\"测试数量自动赋值\",\"子1\",\"子1-1\"],\"name\":\"20240826004816\",\"_id\":\"66cc527b9c544c0007bf8270\",\"field_74B23__c__r\":\"CNY20.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"product_id__r\":\"子1-1\",\"product_id__relation_ids\":\"66c4877d234df50007169729\",\"adjust_price\":\"10.00\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf826d\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]}],\"mc_functional_currency\":\"CNY\",\"amount\":\"2.0\",\"is_package\":false,\"last_modified_time\":1724668264657,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":false,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf826f\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"max_amount\":\"20\",\"order_by\":\"20\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf8270\",\"__amount\":\"2.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"10.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\",\"isRequired\":true}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\",\"isRequired\":true}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"5\"}},\"rowId\":\"1724919752032568\",\"pid\":\"1724919752032567\",\"isShow\":true,\"_level\":2,\"_index\":2,\"__insertRowId\":\"1724919752032567\",\"isChecked\":true,\"__checkedByConstraint\":true,\"__isConstraintSetting\":false,\"bomId\":\"66cc527b9c544c0007bf8270\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"5\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\",\"valueRange\":{}},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\",\"valueRange\":{}}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8271\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"65d2be34c87a1900015011f9\",\"66c48788234df50007169a82\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf826e\",\"66cc527b9c544c0007bf826f\",\"66cc527b9c544c0007bf8271\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8392\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"66c48788234df50007169a82\",\"owner_department_id\":\"1000\",\"total_num\":4,\"owner_department\":\"研发部门\",\"modified_adjust_price\":\"15.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY15.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491543,\"order_field\":\"2\",\"product_life_status\":\"正常\",\"version\":\"3\",\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8271\",\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004817\",\"name\":\"20240826004817\",\"_id\":\"66cc527b9c544c0007bf8271\",\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"product_id__r\":\"子1-2\",\"product_id__relation_ids\":\"66c48788234df50007169a82\",\"adjust_price\":\"15.00\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf826d\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]}],\"mc_functional_currency\":\"CNY\",\"amount\":\"1.0\",\"is_package\":false,\"last_modified_time\":1724668264657,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":false,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf826f\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"max_amount\":\"20\",\"order_by\":\"30\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf8271\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"15.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\",\"isRequired\":true}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\",\"isRequired\":true}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032569\",\"pid\":\"1724919752032567\",\"isShow\":true,\"_level\":2,\"_index\":3,\"__insertRowId\":\"1724919752032568\",\"isChecked\":true,\"__isConstraintSetting\":false,\"bomId\":\"66cc527b9c544c0007bf8271\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"20.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"product_group_id__r\":\"分组二\",\"product_id\":\"665862d8d38f3f000740f6e3\",\"owner_department_id\":\"1000\",\"searchAfterId\":[\"20240826004819\",\"66cc527b9c544c0007bf8273\"],\"modified_adjust_price\":330,\"price_mode\":\"1\",\"adjust_price__r\":\"CNY100.00\",\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"product_bom_path\":[\"测试数量自动赋值\",\"产品C\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"related_core_id\":\"6658637bd38f3f0007411c39\",\"field_74B23__c__r\":\"CNY20.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"increment\":\"1.000\",\"product_status\":\"已上架\",\"product_id__r\":\"产品C\",\"product_id__relation_ids\":\"665862d8d38f3f000740f6e3\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":true,\"last_modified_time\":1724668264657,\"life_status\":\"normal\",\"enabled_status\":true,\"amount_editable\":false,\"product_group_id\":\"66cc527b9c544c0007bf8272\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"max_amount\":\"20\",\"order_by\":\"40\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"665862d8d38f3f000740f6e3\"],\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf8272\",\"66cc527b9c544c0007bf8273\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8394\",\"amount_any\":false,\"node_type\":\"standard\",\"total_num\":4,\"owner_department\":\"研发部门\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491545,\"created_by\":[\"1000\"],\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004819\",\"name\":\"20240826004819\",\"_id\":\"66cc527b9c544c0007bf8273\",\"selected_by_default\":false,\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"adjust_price\":330,\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"root_id\":\"66cc527b9c544c0007bf826d\",\"node_bom_core_version\":\"b20240530-v001360\",\"amount\":\"2.0\",\"mc_currency\":\"CNY\",\"product_group_id__relation_ids\":\"66cc527b9c544c0007bf8272\",\"record_type\":\"default__c\",\"parent_bom_id\":\"66cc527b9c544c0007bf826d\",\"node_bom_core_type\":\"configure\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf8273\",\"__amount\":\"2.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"100.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"_attrConMark\":true,\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"4\"}},\"rowId\":\"1724919752032562\",\"pid\":\"1724919752032561\",\"isChecked\":true,\"isShow\":true,\"_level\":1,\"_index\":5,\"__insertRowId\":\"1724919752032561\",\"_hasRender\":false,\"bomId\":\"66cc527b9c544c0007bf8273\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"4\"}}],\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"0.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.666aa20c3e576b00075a3158\",\"product_id_path\":[\"665862d8d38f3f000740f6e3\",\"665862f5d38f3f000740fb57\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"6658637bd38f3f0007411c36\",\"666aa20c3e576b00075a3158\"],\"extend_obj_data_id\":\"666aa20d3e576b00075a33e1\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"665862f5d38f3f000740fb57\",\"owner_department_id\":\"1000\",\"total_num\":3,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240613004234\",\"666aa20c3e576b00075a3158\"],\"modified_adjust_price\":\"10.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY10.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1718264333872,\"min_amount\":\"1\",\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"6658637bd38f3f0007411c36.666aa20c3e576b00075a3158\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240530-001360\",\"bom_id__r\":\"20240613004234\",\"product_bom_path\":[\"产品C\",\"产品C1\"],\"name\":\"20240613004234\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"666aa20c3e576b00075a3158\",\"field_74B23__c__r\":\"CNY0.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"6658637bd38f3f0007411c39\",\"product_id__r\":\"产品C1\",\"product_id__relation_ids\":\"665862f5d38f3f000740fb57\",\"adjust_price\":\"10.00\",\"core_id\":\"6658637bd38f3f0007411c39\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"产品C\",\"产品C1\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf8273\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"owner\":[\"1000\"],\"amount\":\"12.0\",\"is_package\":false,\"last_modified_time\":1718264689702,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf8273\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"max_amount\":\"1\",\"order_by\":\"10\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"666aa20c3e576b00075a3158\",\"__amount\":\"12.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":true,\"__adjust_price\":\"10.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"_attrConMark\":true,\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032563\",\"pid\":\"1724919752032562\",\"__parentBomRowId\":\"1724919752032562\",\"isShow\":false,\"_level\":2,\"_index\":6,\"__insertRowId\":\"1724919752032562\",\"isChecked\":true,\"__checkedByConstraint\":true,\"__isConstraintSetting\":true,\"_hasRender\":false,\"bomId\":\"666aa20c3e576b00075a3158\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.666aa20c3e576b00075a3159\",\"product_id_path\":[\"665862d8d38f3f000740f6e3\",\"66586309d38f3f000740ff43\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"6658637bd38f3f0007411c36\",\"666aa20c3e576b00075a3159\"],\"extend_obj_data_id\":\"666aa20d3e576b00075a33e2\",\"amount_any\":true,\"node_type\":\"standard\",\"product_id\":\"66586309d38f3f000740ff43\",\"owner_department_id\":\"1000\",\"total_num\":3,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240613004235\",\"666aa20c3e576b00075a3159\"],\"modified_adjust_price\":\"20.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY20.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"create_time\":1718264333873,\"min_amount\":\"1\",\"order_field\":\"2\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"6658637bd38f3f0007411c36.666aa20c3e576b00075a3159\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240530-001360\",\"bom_id__r\":\"20240613004235\",\"product_bom_path\":[\"产品C\",\"产品C2\"],\"name\":\"20240613004235\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"666aa20c3e576b00075a3159\",\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":true,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"6658637bd38f3f0007411c39\",\"product_id__r\":\"产品C2\",\"product_id__relation_ids\":\"66586309d38f3f000740ff43\",\"adjust_price\":\"20.00\",\"core_id\":\"6658637bd38f3f0007411c39\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"产品C\",\"产品C2\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf8273\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"owner\":[\"1000\"],\"amount\":\"4.0\",\"is_package\":false,\"last_modified_time\":1724156167251,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf8273\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"max_amount\":\"2\",\"order_by\":\"10\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"666aa20c3e576b00075a3159\",\"__amount\":\"4.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":true,\"__adjust_price\":\"20.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"_attrConMark\":true,\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032564\",\"pid\":\"1724919752032562\",\"__parentBomRowId\":\"1724919752032562\",\"isShow\":false,\"_level\":2,\"_index\":7,\"__insertRowId\":\"1724919752032563\",\"isChecked\":true,\"__isConstraintSetting\":true,\"_hasRender\":false,\"bomId\":\"666aa20c3e576b00075a3159\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.66cd864c4c06060007be429a\",\"product_id_path\":[\"665862d8d38f3f000740f6e3\",\"66cd85d54c06060007be2754\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"6658637bd38f3f0007411c36\",\"66cd864c4c06060007be429a\"],\"extend_obj_data_id\":\"66cd864d4c06060007be4444\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"66cd85d54c06060007be2754\",\"owner_department_id\":\"1000\",\"total_num\":3,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240827004878\",\"66cd864c4c06060007be429a\"],\"modified_adjust_price\":\"30.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY30.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"create_time\":1724745293362,\"order_field\":\"3\",\"product_life_status\":\"正常\",\"version\":\"1\",\"created_by\":[\"1000\"],\"bom_path\":\"6658637bd38f3f0007411c36.66cd864c4c06060007be429a\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240530-001360\",\"bom_id__r\":\"20240827004878\",\"product_bom_path\":[\"产品C\",\"产品C3\"],\"name\":\"20240827004878\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"66cd864c4c06060007be429a\",\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":true,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"6658637bd38f3f0007411c39\",\"product_id__r\":\"产品C3\",\"product_id__relation_ids\":\"66cd85d54c06060007be2754\",\"adjust_price\":\"30.00\",\"core_id\":\"6658637bd38f3f0007411c39\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"产品C\",\"产品C3\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf8273\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"owner\":[\"1000\"],\"amount\":\"1.0\",\"is_package\":false,\"last_modified_time\":1724745293362,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf8273\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"order_by\":\"10\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cd864c4c06060007be429a\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":true,\"__adjust_price\":\"30.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032565\",\"pid\":\"1724919752032562\",\"__parentBomRowId\":\"1724919752032562\",\"isShow\":false,\"_level\":2,\"_index\":8,\"__insertRowId\":\"1724919752032564\",\"isChecked\":true,\"__isConstraintSetting\":true,\"_hasRender\":false,\"bomId\":\"66cd864c4c06060007be429a\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}}]}]}";
        } else {
            jsonStr = "{\"formulas\":[{\"expression\":\"IF(\$66cc527b9c544c0007bf826f#EXT#ATTR#65d4635dedb9f50001015881\$=='65d4635dedb9f50001015886',\$66cc527b9c544c0007bf826f#EXT#NON_ATTR#65954ea312e1da0006466941\$-\$66cc527b9c544c0007bf8271#EXT#NON_ATTR#65954eb112e1da0006468c12\$-\$66cc527b9c544c0007bf826f.max_amount\$,4)\",\"bom_id\":\"66cc527b9c544c0007bf8271\",\"product_id\":\"66c48788234df50007169a82\",\"field_name\":\"amount\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8271\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\"}],\"selectBomList\":[{\"version\":\"6\",\"product_status\":\"1\",\"field_s27tu__c\":\"测试数量自动赋值\",\"attribute30\":[\"65d4635dedb9f50001015886\",\"65d4635dedb9f50001015883\",\"65d4635dedb9f50001015888\",\"65d4635dedb9f50001015884\",\"65d4635dedb9f50001015885\",\"65d4635dedb9f50001015882\",\"65d4635dedb9f50001015887\",\"65d4635dedb9f50001015889\"],\"new_bom_path\":\"66cc527b9c544c0007bf826d\",\"extend_obj_data_id\":\"66c461b8367a51000769a427\",\"object_describe_api_name\":\"ProductObj\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"pricing_attribute_ids\":[\"65d4635dedb9f50001015881\",\"665f11b406153d000722189f\"],\"field_AhbB1__c\":\"0.00\",\"nonstandard_attribute_ids\":[\"65954ea312e1da0006466941\",\"65954eb112e1da0006468c12\"],\"tenant_id\":\"90242\",\"on_shelves_time\":1724146104163,\"core_id\":\"66cc527b9c544c0007bf8274\",\"product_category_id__relation_ids\":\"6594c7c0cfd011000162fddf\",\"is_giveaway\":\"0\",\"data_own_department\":[\"1000\"],\"_id\":\"66c461b8367a51000769a38a\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"is_saleable\":true,\"owner_department_id\":\"1000\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}],\"bom_id\":\"66cc527b9c544c0007bf826d\",\"product_category_id\":\"6594c7c0cfd011000162fddf\",\"owner_department\":\"研发部门\",\"create_time\":1724146104284,\"name\":\"测试数量自动赋值\",\"price\":\"100.00\",\"attribute_ids\":[\"65d4635dedb9f50001015881\",\"665f11b406153d000722189f\"],\"product_code\":\"测试数量自动赋值\",\"is_package\":true,\"last_modified_time\":1724299878688,\"life_status\":\"normal\",\"lock_status\":\"0\",\"package\":\"CRM\",\"display_name\":\"测试数量自动赋值\",\"is_deleted\":false,\"last_modified_by\":[\"1000\"],\"attribute76\":[\"665f11b406153d00072218a1\",\"665f11b406153d00072218a0\",\"665f11b406153d00072218a2\",\"665f11b406153d00072218a3\"],\"category\":\"2\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"product_id\":\"66c461b8367a51000769a38a\",\"_isRoot\":true,\"isChecked\":true,\"product_id__r\":\"测试数量自动赋值\",\"rowId\":\"1724919752025560\",\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"bomId\":\"66cc527b9c544c0007bf826d\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}}],\"product_group_id\":\"\",\"isRoot\":true},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"product_group_id__r\":\"分组一\",\"product_id\":\"65d2be34c87a1900015011f9\",\"owner_department_id\":\"1000\",\"searchAfterId\":[\"20240826004818\",\"66cc527b9c544c0007bf826f\"],\"modified_adjust_price\":135,\"price_mode\":\"1\",\"adjust_price__r\":\"CNY100.00\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"4\",\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f\",\"product_bom_path\":[\"测试数量自动赋值\",\"子1\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"increment\":\"1.000\",\"product_status\":\"已上架\",\"product_id__r\":\"子1\",\"product_id__relation_ids\":\"65d2be34c87a1900015011f9\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":false,\"last_modified_time\":1724919163636,\"life_status\":\"normal\",\"enabled_status\":true,\"amount_editable\":false,\"product_group_id\":\"66cc527b9c544c0007bf826e\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"order_by\":\"10\",\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"65d2be34c87a1900015011f9\"],\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf826e\",\"66cc527b9c544c0007bf826f\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8393\",\"amount_any\":false,\"node_type\":\"standard\",\"total_num\":4,\"owner_department\":\"研发部门\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491544,\"created_by\":[\"1000\"],\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004818\",\"name\":\"20240826004818\",\"_id\":\"66cc527b9c544c0007bf826f\",\"max_amount\":\"20\",\"selected_by_default\":false,\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"adjust_price\":135,\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"测试数量自动赋值\",\"分组一\",\"子1\"],\"root_id\":\"66cc527b9c544c0007bf826d\",\"owner\":[\"1000\"],\"amount\":\"1.0\",\"last_modified_by__l\":[{\"id\":\"1000\",\"tenantId\":\"90242\",\"name\":\"CRM管理员\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"CRM管理员\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1704249221204,\"modifyTime\":1704250614802,\"dept\":\"1000\",\"post\":\"\",\"empNum\":\"\"}],\"mc_currency\":\"CNY\",\"product_group_id__relation_ids\":\"66cc527b9c544c0007bf826e\",\"record_type\":\"default__c\",\"parent_bom_id\":\"66cc527b9c544c0007bf826d\",\"node_bom_core_type\":\"product\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf826f\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"100.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"_attrConMark\":true,\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015888\",\"name\":\"黄色\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032567\",\"pid\":\"1724919752032566\",\"isChecked\":true,\"isShow\":true,\"_level\":1,\"_index\":1,\"__insertRowId\":\"1724919752032566\",\"_hasRender\":false,\"bomId\":\"66cc527b9c544c0007bf826f\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015888\",\"name\":\"黄色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"20.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\",\"valueRange\":{}},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\",\"valueRange\":{}}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8270\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"65d2be34c87a1900015011f9\",\"66c4877d234df50007169729\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf826e\",\"66cc527b9c544c0007bf826f\",\"66cc527b9c544c0007bf8270\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8391\",\"amount_any\":true,\"node_type\":\"standard\",\"product_id\":\"66c4877d234df50007169729\",\"owner_department_id\":\"1000\",\"total_num\":4,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240826004816\",\"66cc527b9c544c0007bf8270\"],\"modified_adjust_price\":\"10.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY10.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491542,\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8270\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004816\",\"product_bom_path\":[\"测试数量自动赋值\",\"子1\",\"子1-1\"],\"name\":\"20240826004816\",\"_id\":\"66cc527b9c544c0007bf8270\",\"field_74B23__c__r\":\"CNY20.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"product_id__r\":\"子1-1\",\"product_id__relation_ids\":\"66c4877d234df50007169729\",\"adjust_price\":\"10.00\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf826d\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]}],\"mc_functional_currency\":\"CNY\",\"amount\":\"2.0\",\"is_package\":false,\"last_modified_time\":1724668264657,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":false,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf826f\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"max_amount\":\"20\",\"order_by\":\"20\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf8270\",\"__amount\":\"2.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"10.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\",\"isRequired\":true}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\",\"isRequired\":true}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"5\"}},\"rowId\":\"1724919752032568\",\"pid\":\"1724919752032567\",\"isShow\":true,\"_level\":2,\"_index\":2,\"__insertRowId\":\"1724919752032567\",\"isChecked\":true,\"__checkedByConstraint\":true,\"__isConstraintSetting\":false,\"bomId\":\"66cc527b9c544c0007bf8270\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"5\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\",\"valueRange\":{}},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\",\"valueRange\":{}}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8271\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"65d2be34c87a1900015011f9\",\"66c48788234df50007169a82\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf826e\",\"66cc527b9c544c0007bf826f\",\"66cc527b9c544c0007bf8271\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8392\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"66c48788234df50007169a82\",\"owner_department_id\":\"1000\",\"total_num\":4,\"owner_department\":\"研发部门\",\"modified_adjust_price\":\"15.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY15.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491543,\"order_field\":\"2\",\"product_life_status\":\"正常\",\"version\":\"3\",\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf826f.66cc527b9c544c0007bf8271\",\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004817\",\"name\":\"20240826004817\",\"_id\":\"66cc527b9c544c0007bf8271\",\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"product_id__r\":\"子1-2\",\"product_id__relation_ids\":\"66c48788234df50007169a82\",\"adjust_price\":\"15.00\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf826d\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\",\"isRequired\":false,\"disabled\":false},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\",\"isRequired\":false,\"disabled\":false}]}],\"mc_functional_currency\":\"CNY\",\"amount\":\"1.0\",\"is_package\":false,\"last_modified_time\":1724668264657,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":false,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf826f\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"max_amount\":\"20\",\"order_by\":\"30\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf8271\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"15.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\",\"isRequired\":true}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\",\"isRequired\":true}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032569\",\"pid\":\"1724919752032567\",\"isShow\":true,\"_level\":2,\"_index\":3,\"__insertRowId\":\"1724919752032568\",\"isChecked\":true,\"__isConstraintSetting\":false,\"bomId\":\"66cc527b9c544c0007bf8271\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a1\",\"name\":\"银\"}},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015886\",\"name\":\"白色\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"20.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"mc_exchange_rate\":\"1.000000\",\"product_group_id__r\":\"分组二\",\"product_id\":\"665862d8d38f3f000740f6e3\",\"owner_department_id\":\"1000\",\"searchAfterId\":[\"20240826004819\",\"66cc527b9c544c0007bf8273\"],\"modified_adjust_price\":330,\"price_mode\":\"1\",\"adjust_price__r\":\"CNY100.00\",\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"product_bom_path\":[\"测试数量自动赋值\",\"产品C\"],\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"related_core_id\":\"6658637bd38f3f0007411c39\",\"field_74B23__c__r\":\"CNY20.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"increment\":\"1.000\",\"product_status\":\"已上架\",\"product_id__r\":\"产品C\",\"product_id__relation_ids\":\"665862d8d38f3f000740f6e3\",\"core_id\":\"66cc527b9c544c0007bf8274\",\"product_life_status__v\":\"normal\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"is_package\":true,\"last_modified_time\":1724668264657,\"life_status\":\"normal\",\"enabled_status\":true,\"amount_editable\":false,\"product_group_id\":\"66cc527b9c544c0007bf8272\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d\",\"max_amount\":\"20\",\"order_by\":\"40\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"product_id_path\":[\"66c461b8367a51000769a38a\",\"665862d8d38f3f000740f6e3\"],\"crumb_bread\":[\"66cc527b9c544c0007bf826d\",\"66cc527b9c544c0007bf8272\",\"66cc527b9c544c0007bf8273\"],\"extend_obj_data_id\":\"66cc527b9c544c0007bf8394\",\"amount_any\":false,\"node_type\":\"standard\",\"total_num\":4,\"owner_department\":\"研发部门\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1724666491545,\"created_by\":[\"1000\"],\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240826-001445\",\"bom_id__r\":\"20240826004819\",\"name\":\"20240826004819\",\"_id\":\"66cc527b9c544c0007bf8273\",\"selected_by_default\":false,\"core_id__relation_ids\":\"66cc527b9c544c0007bf8274\",\"adjust_price\":330,\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"root_id\":\"66cc527b9c544c0007bf826d\",\"node_bom_core_version\":\"b20240530-v001360\",\"amount\":\"2.0\",\"mc_currency\":\"CNY\",\"product_group_id__relation_ids\":\"66cc527b9c544c0007bf8272\",\"record_type\":\"default__c\",\"parent_bom_id\":\"66cc527b9c544c0007bf826d\",\"node_bom_core_type\":\"configure\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cc527b9c544c0007bf8273\",\"__amount\":\"2.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":false,\"__adjust_price\":\"100.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"modified_adjust_price\"],\"_attrConMark\":true,\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"4\"}},\"rowId\":\"1724919752032562\",\"pid\":\"1724919752032561\",\"isChecked\":true,\"isShow\":true,\"_level\":1,\"_index\":5,\"__insertRowId\":\"1724919752032561\",\"_hasRender\":false,\"bomId\":\"66cc527b9c544c0007bf8273\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"4\"}}],\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"0.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.666aa20c3e576b00075a3158\",\"product_id_path\":[\"665862d8d38f3f000740f6e3\",\"665862f5d38f3f000740fb57\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"6658637bd38f3f0007411c36\",\"666aa20c3e576b00075a3158\"],\"extend_obj_data_id\":\"666aa20d3e576b00075a33e1\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"665862f5d38f3f000740fb57\",\"owner_department_id\":\"1000\",\"total_num\":3,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240613004234\",\"666aa20c3e576b00075a3158\"],\"modified_adjust_price\":\"10.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY10.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"create_time\":1718264333872,\"min_amount\":\"1\",\"order_field\":\"1\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"6658637bd38f3f0007411c36.666aa20c3e576b00075a3158\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240530-001360\",\"bom_id__r\":\"20240613004234\",\"product_bom_path\":[\"产品C\",\"产品C1\"],\"name\":\"20240613004234\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"666aa20c3e576b00075a3158\",\"field_74B23__c__r\":\"CNY0.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":false,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"6658637bd38f3f0007411c39\",\"product_id__r\":\"产品C1\",\"product_id__relation_ids\":\"665862f5d38f3f000740fb57\",\"adjust_price\":\"10.00\",\"core_id\":\"6658637bd38f3f0007411c39\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"产品C\",\"产品C1\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf8273\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"owner\":[\"1000\"],\"amount\":\"12.0\",\"is_package\":false,\"last_modified_time\":1718264689702,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf8273\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"max_amount\":\"1\",\"order_by\":\"10\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"666aa20c3e576b00075a3158\",\"__amount\":\"12.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":true,\"__adjust_price\":\"10.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"_attrConMark\":true,\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032563\",\"pid\":\"1724919752032562\",\"__parentBomRowId\":\"1724919752032562\",\"isShow\":false,\"_level\":2,\"_index\":6,\"__insertRowId\":\"1724919752032562\",\"isChecked\":true,\"__checkedByConstraint\":true,\"__isConstraintSetting\":true,\"_hasRender\":false,\"bomId\":\"666aa20c3e576b00075a3158\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.666aa20c3e576b00075a3159\",\"product_id_path\":[\"665862d8d38f3f000740f6e3\",\"66586309d38f3f000740ff43\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"6658637bd38f3f0007411c36\",\"666aa20c3e576b00075a3159\"],\"extend_obj_data_id\":\"666aa20d3e576b00075a33e2\",\"amount_any\":true,\"node_type\":\"standard\",\"product_id\":\"66586309d38f3f000740ff43\",\"owner_department_id\":\"1000\",\"total_num\":3,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240613004235\",\"666aa20c3e576b00075a3159\"],\"modified_adjust_price\":\"20.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY20.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"create_time\":1718264333873,\"min_amount\":\"1\",\"order_field\":\"2\",\"product_life_status\":\"正常\",\"version\":\"3\",\"created_by\":[\"1000\"],\"bom_path\":\"6658637bd38f3f0007411c36.666aa20c3e576b00075a3159\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240530-001360\",\"bom_id__r\":\"20240613004235\",\"product_bom_path\":[\"产品C\",\"产品C2\"],\"name\":\"20240613004235\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"666aa20c3e576b00075a3159\",\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":true,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"6658637bd38f3f0007411c39\",\"product_id__r\":\"产品C2\",\"product_id__relation_ids\":\"66586309d38f3f000740ff43\",\"adjust_price\":\"20.00\",\"core_id\":\"6658637bd38f3f0007411c39\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"产品C\",\"产品C2\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf8273\",\"attribute\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"owner\":[\"1000\"],\"amount\":\"4.0\",\"is_package\":false,\"last_modified_time\":1724156167251,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf8273\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"max_amount\":\"2\",\"order_by\":\"10\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"666aa20c3e576b00075a3159\",\"__amount\":\"4.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":true,\"__adjust_price\":\"20.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"_attrConMark\":true,\"selectedAttr\":{\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]},\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032564\",\"pid\":\"1724919752032562\",\"__parentBomRowId\":\"1724919752032562\",\"isShow\":false,\"_level\":2,\"_index\":7,\"__insertRowId\":\"1724919752032563\",\"isChecked\":true,\"__isConstraintSetting\":true,\"_hasRender\":false,\"bomId\":\"666aa20c3e576b00075a3159\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}},{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}}]},{\"lock_rule\":\"default_lock_rule\",\"field_74B23__c\":\"10.00\",\"nonstandardAttribute\":[{\"name\":\"直径\",\"default_value\":\"5\",\"id\":\"65954ea312e1da0006466941\"},{\"name\":\"半径\",\"default_value\":\"2\",\"id\":\"65954eb112e1da0006468c12\"}],\"currency_field_74B23__c\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_field_74B23__c\",\"objectApiName\":\"BOMObj\"},\"product_status__v\":\"1\",\"price_editable\":true,\"product_status__r\":\"已上架\",\"new_bom_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273.66cd864c4c06060007be429a\",\"product_id_path\":[\"665862d8d38f3f000740f6e3\",\"66cd85d54c06060007be2754\"],\"mc_exchange_rate\":\"1.000000\",\"crumb_bread\":[\"6658637bd38f3f0007411c36\",\"66cd864c4c06060007be429a\"],\"extend_obj_data_id\":\"66cd864d4c06060007be4444\",\"amount_any\":false,\"node_type\":\"standard\",\"product_id\":\"66cd85d54c06060007be2754\",\"owner_department_id\":\"1000\",\"total_num\":3,\"owner_department\":\"研发部门\",\"searchAfterId\":[\"20240827004878\",\"66cd864c4c06060007be429a\"],\"modified_adjust_price\":\"30.00\",\"price_mode\":\"1\",\"adjust_price__r\":\"CNY30.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"data_own_department__r\":{\"deptName\":\"研发部门\",\"deptId\":\"1000\",\"deptType\":\"dept\",\"parentId\":\"999999\",\"status\":0},\"create_time\":1724745293362,\"order_field\":\"3\",\"product_life_status\":\"正常\",\"version\":\"1\",\"created_by\":[\"1000\"],\"bom_path\":\"6658637bd38f3f0007411c36.66cd864c4c06060007be429a\",\"data_own_department\":[\"1000\"],\"core_id__r\":\"20240530-001360\",\"bom_id__r\":\"20240827004878\",\"product_bom_path\":[\"产品C\",\"产品C3\"],\"name\":\"20240827004878\",\"currency_adjust_price\":{\"prefix\":\"CNY\",\"suffix\":\"\",\"fieldApiName\":\"currency_adjust_price\",\"objectApiName\":\"BOMObj\"},\"_id\":\"66cd864c4c06060007be429a\",\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"1000\",\"deptName\":\"研发部门\",\"status\":0,\"deptType\":\"dept\"}],\"field_74B23__c__r\":\"CNY10.00\",\"tenant_id\":\"90242\",\"product_life_status__r\":\"正常\",\"selected_by_default\":true,\"increment\":\"1.000\",\"product_status\":\"已上架\",\"core_id__relation_ids\":\"6658637bd38f3f0007411c39\",\"product_id__r\":\"产品C3\",\"product_id__relation_ids\":\"66cd85d54c06060007be2754\",\"adjust_price\":\"30.00\",\"core_id\":\"6658637bd38f3f0007411c39\",\"is_deleted\":false,\"is_required\":false,\"object_describe_api_name\":\"BOMObj\",\"crumb_bread_name\":[\"产品C\",\"产品C3\"],\"product_life_status__v\":\"normal\",\"root_id\":\"66cc527b9c544c0007bf8273\",\"attribute\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"fieldNum\":30,\"attribute_values\":[{\"id\":\"65d4635dedb9f50001015889\",\"code\":\"1-8\",\"name\":\"橙色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015886\",\"code\":\"1-5\",\"name\":\"白色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015887\",\"code\":\"1-6\",\"name\":\"紫色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015882\",\"code\":\"1-1\",\"name\":\"红色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015885\",\"code\":\"1-4\",\"name\":\"绿色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015884\",\"code\":\"1-3\",\"name\":\"蓝色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015888\",\"code\":\"1-7\",\"name\":\"黄色\",\"is_default\":\"0\"},{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"fieldNum\":76,\"attribute_values\":[{\"id\":\"665f11b406153d00072218a3\",\"code\":\"04\",\"name\":\"铁\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a2\",\"code\":\"03\",\"name\":\"铜\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a1\",\"code\":\"02\",\"name\":\"银\",\"is_default\":\"0\"},{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}],\"mc_functional_currency\":\"CNY\",\"owner\":[\"1000\"],\"amount\":\"1.0\",\"is_package\":false,\"last_modified_time\":1724745293362,\"life_status\":\"normal\",\"enabled_status\":true,\"mc_currency\":\"CNY\",\"amount_editable\":true,\"record_type\":\"default__c\",\"product_group_id\":\"\",\"parent_bom_id\":\"66cc527b9c544c0007bf8273\",\"node_bom_core_type\":\"product\",\"current_root_new_path\":\"66cc527b9c544c0007bf826d.66cc527b9c544c0007bf8273\",\"order_by\":\"10\",\"mc_exchange_rate_version\":\"1710332605081\",\"bom_id\":\"66cd864c4c06060007be429a\",\"__amount\":\"1.0\",\"__isNotFlowRootPricebook\":false,\"disabled\":true,\"__adjust_price\":\"30.00\",\"a_node_bom_core_type\":\"configure\",\"isEditField\":[\"amount\",\"modified_adjust_price\"],\"selectedAttr\":{\"65d4635dedb9f50001015881\":{\"name\":\"颜色\",\"value_ids\":[{\"id\":\"65d4635dedb9f50001015883\",\"code\":\"1-2\",\"name\":\"黑色\",\"is_default\":\"1\"}]},\"665f11b406153d000722189f\":{\"name\":\"材质\",\"value_ids\":[{\"id\":\"665f11b406153d00072218a0\",\"code\":\"01\",\"name\":\"金\",\"is_default\":\"1\"}]}},\"nsAttr\":{\"65954ea312e1da0006466941\":{\"name\":\"直径\",\"value\":\"5\"},\"65954eb112e1da0006468c12\":{\"name\":\"半径\",\"value\":\"2\"}},\"rowId\":\"1724919752032565\",\"pid\":\"1724919752032562\",\"__parentBomRowId\":\"1724919752032562\",\"isShow\":false,\"_level\":2,\"_index\":8,\"__insertRowId\":\"1724919752032564\",\"isChecked\":true,\"__isConstraintSetting\":true,\"_hasRender\":false,\"bomId\":\"66cd864c4c06060007be429a\",\"nonAttributes\":[{\"id\":\"65954ea312e1da0006466941\",\"name\":\"直径\",\"attributeValues\":{\"value\":\"5\"}},{\"id\":\"65954eb112e1da0006468c12\",\"name\":\"半径\",\"attributeValues\":{\"value\":\"2\"}}],\"attributes\":[{\"id\":\"65d4635dedb9f50001015881\",\"name\":\"颜色\",\"attributeValues\":{\"id\":\"65d4635dedb9f50001015883\",\"name\":\"黑色\"}},{\"id\":\"665f11b406153d000722189f\",\"name\":\"材质\",\"attributeValues\":{\"id\":\"665f11b406153d00072218a0\",\"name\":\"金\"}}]}]}";
        }
        BomFormulaModel.CalculateArg arg = com.facishare.enterprise.common.util.JsonUtil.fromJson(jsonStr, BomFormulaModel.CalculateArg.class);
        if (formulaEmpty) {
            arg.setFormulas(null);
            return arg;
        }
        if (selectBomListEmpty) {
            arg.setSelectBomList(null);
            return arg;
        }
        if (bomIdEmpty) {
            arg.getSelectBomList().stream().forEach { x -> x.toObjectData().set("bom_id", null) };
        }
        if (expressEmpty) {
            arg.getFormulas().stream().forEach { x -> x.setExpression(null) };
        }
        arg.setSkipInvalidExpression(isSkipInvalidExpression);
        return arg;
    }

    private ExpressionCheck.Result getExpressionCheckResult(boolean isSuccess) {
        ExpressionCheck.Result result = new ExpressionCheck.Result();
        if (isSuccess) {
            result.setCode(0);
        } else {
            result.setCode(500);
        }
        return result;
    }

    private BomFormulaModel.CheckArg buildBomFormulaModelCheckArg(boolean expressEmpty) {
        BomFormulaModel.CheckArg arg = new BomFormulaModel.CheckArg();
        if (!expressEmpty) {
            arg.setExpression("\$discount\$*\$receivable_amount\$-IF(\$order_time\$=='22', 1, 2)");
        }
        return arg;
    }


    CheckBomModel.Args getCheckBomModelArgs(boolean isApl) {
        CheckBomModel.Args args = new CheckBomModel.Args();
        CheckBomModel.Arg arg = new CheckBomModel.Arg();
        if (isApl) {
            arg.setAplList(Lists.newArrayList("filter__c"));
        }
        arg.setSelectBomList(Lists.newArrayList(ObjectDataDocument.of(["_id": "1111"])));
        args.setTreeList(Lists.newArrayList(arg));
        return args;
    }

    CheckBomModel.Result getCheckBomModelResult() {
        return CheckBomModel.Result.builder().success(true).msg("ok").build();
    }

    IUdefFunction getFunction() {
        UdefFunction udefFunction = new UdefFunction();
        IUdefFunction function = UdefFunctionExt.of(udefFunction);
        return function;
    }

    RunResult getRunResult() {
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        Map functionMap = new HashMap();
        functionMap.put("success", Boolean.TRUE);
        functionMap.put("errorMessage", "ok");
        runResult.setFunctionResult(functionMap);
        return runResult;
    }

    RunResult getRunResultError() {
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        Map functionMap = new HashMap();
        functionMap.put("success", Boolean.FALSE);
        functionMap.put("errorMessage", "ok");
        runResult.setFunctionResult(functionMap);
        return runResult;
    }

    RunResult getRunResultFail() {
        RunResult runResult = new RunResult();
        runResult.setSuccess(false);
        Map functionMap = new HashMap();
        runResult.setFunctionResult(functionMap);
        return runResult;
    }

    def getBomList(String str) {
        def array = Lists.newArrayList()
        if (StringUtils.isNotBlank(str)) {
            array = JSON.parseArray(str, ObjectDataDocument.class)
        }
        array
    }

    def getBomAttributeConstraintLines(String str) {
        List<ObjectDataDocument> array = Lists.newArrayList();
        if (StringUtils.isNotBlank(str)) {
            array = JSON.parseArray(str, ObjectDataDocument.class)
        }
        array
    }


    def buildGroupData() {
        def map = ArrayListMultimap.create()
        map.put("1", "1.1")
        map.put("1", "1.1.2")
        map.put("2", "1.2.1")
        map.put("3", "1.3.1")
        map
    }

    def dataMap() {
        def map = Maps.newHashMap()
        Table<String, String, String> conditionTable = HashBasedTable.create();
        Table<String, String, String> resultTable = HashBasedTable.create();
        conditionTable.put("1.1.1", "", "");
        resultTable.put("1.2.1", "", "");
        map.put(conditionTable, resultTable);
        map
    }

    def groupMap(String key, String value) {
        def map = Maps.newHashMap()
        map.put(key, value)
        map
    }

    def selectBomMap(String bomId) {
        def map = Maps.newHashMap()
        def bomInfo = new CheckBomModel.BomInfo()
        bomInfo.setBomId(bomId)
        map.put(bomId, bomInfo)
        map
    }

    def bomMap() {
        def map = Maps.newHashMap()
        map.put("1", "1.1")
        map.put("2", "2.2")
        map.put("3", "3.3")
        map
    }

    def conditionList(String dataId) {
        def list = Lists.newArrayList()
        BomConstraintLineModel mode = new BomConstraintLineModel()
        mode.setGroup_id(dataId)
        list.add(mode)
        list
    }

    def resultList(String dataId) {
        def list = Lists.newArrayList()
        BomConstraintLineModel mode = new BomConstraintLineModel()
        mode.setGroup_id(dataId)
        mode.setGroup_name("分组")
        list.add(mode)
        list
    }

    ServiceContext getServiceContext(String serviceName, String serviceMethod) {
        RequestContext.RequestContextBuilder requestContextBuilder = RequestContext.builder()
        requestContextBuilder.user(user)
        requestContextBuilder.tenantId(user.getTenantId())
        return new ServiceContext(requestContextBuilder.build(), serviceName, serviceMethod)
    }

}
