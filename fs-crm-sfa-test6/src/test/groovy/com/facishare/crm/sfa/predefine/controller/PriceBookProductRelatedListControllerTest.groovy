//package com.facishare.crm.sfa.predefine.controller
//
//import com.facishare.crm.sfa.RemoveUseless
//import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
//import com.facishare.crm.sfa.utilities.util.*
//import com.facishare.paas.I18N
//import com.facishare.paas.appframework.core.model.*
//import com.facishare.paas.appframework.core.predef.controller.BaseListController
//import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController
//import com.facishare.paas.appframework.core.util.RequestUtil
//import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam
//import com.facishare.paas.metadata.api.IObjectData
//import com.facishare.paas.metadata.api.search.IFilter
//import com.facishare.paas.metadata.impl.ObjectData
//import com.facishare.paas.metadata.impl.search.Filter
//import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
//import com.facishare.paas.metadata.util.SpringContextUtil
//import com.google.common.collect.Lists
//import com.google.common.collect.Maps
//import org.junit.runner.RunWith
//import org.powermock.api.mockito.PowerMockito
//import org.powermock.api.support.membermodification.MemberMatcher
//import org.powermock.core.classloader.annotations.PowerMockIgnore
//import org.powermock.core.classloader.annotations.PrepareForTest
//import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
//import org.powermock.modules.junit4.PowerMockRunner
//import org.powermock.modules.junit4.PowerMockRunnerDelegate
//import org.powermock.reflect.Whitebox
//import org.spockframework.runtime.Sputnik
//import org.springframework.context.ApplicationContext
//import spock.lang.Shared
//
//import static org.mockito.ArgumentMatchers.any
//import static org.mockito.ArgumentMatchers.anyString
//
///**
// * <AUTHOR>
// * @since 2024/9/9
// */
//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(Sputnik)
//@PowerMockIgnore(["javax.management.*"])
//@PrepareForTest([I18N, ValidDateUtils.class, MultiKeyWordSearchUtil.class, SFAConfigUtil.class, GrayUtil.class, RequestUtil.class, PromotionUtil.class, DhtUtil.class, PriceBookProductRelatedListController.class, BaseListController.class])
//@SuppressStaticInitializationFor(["com.facishare.paas.I18N", "com.facishare.crm.sfa.predefine.controller.PriceBookProductRelatedListController"])
//class PriceBookProductRelatedListControllerTest extends RemoveUseless {
//    @Shared
//    def bizConfigThreadLocalCacheService = PowerMockito.mock(BizConfigThreadLocalCacheService)
//
//    def tenantId = '123'
//
//    @Shared
//    protected ControllerContext controllerContext
//
//    def setupSpec() {
//        removeConfigFactory()
//        removeI18N()
//        initSpringContext()
//        PowerMockito.mockStatic(I18N)
//        PowerMockito.doNothing().when(I18N, "setContext", anyString(), anyString())
//        RequestContextManager.setContext(RequestContext.builder().build())
//    }
//
//    def "customSearchTemplate"() {
//        given:
//        PowerMockito.mockStatic(ValidDateUtils.class)
//        PowerMockito.mockStatic(MultiKeyWordSearchUtil.class)
//        PowerMockito.mockStatic(SFAConfigUtil.class)
//        PowerMockito.mockStatic(GrayUtil.class)
//        PowerMockito.mockStatic(RequestUtil.class)
//        PowerMockito.mockStatic(PromotionUtil.class)
//        PowerMockito.mockStatic(DhtUtil.class)
//        def priceBookProductRelatedListController = PowerMockito.spy(new PriceBookProductRelatedListController())
//        controllerContext = Mock(ControllerContext)
//        Whitebox.setInternalState(priceBookProductRelatedListController, "controllerContext", controllerContext)
//        def searchTemplateQuery = new SearchTemplateQuery()
//        List<IFilter> list = Lists.newArrayList()
//        IFilter filter = new Filter()
//        list.add(filter)
//        searchTemplateQuery.setFilters(list)
//        StandardRelatedListController.Arg arg = new StandardRelatedListController.Arg()
//        IObjectData objectData = getObjectData(Lists.newArrayList("_id"))
//        objectData.set("object_describe_api_name", "SalesOrderObj")
//        objectData.set("account_id", "xxx")
//        objectData.set("partner_id", "xxx")
//        objectData.set("pricebook_id", "xxx")
//        objectData.set("tab_price_policy_id", "xxx")
//        arg.setObjectData(ObjectDataDocument.of(objectData))
//        arg.setMasterData(ObjectDataDocument.of(objectData))
//        arg.setRelatedListName("pricebookproduct_salesorderproduct_list")
//        arg.setFilterProductIds(Lists.newArrayList("xxx"))
//        Whitebox.setInternalState(priceBookProductRelatedListController, "arg", arg)
//        Whitebox.setInternalState(priceBookProductRelatedListController, "isPriceBookSelect", true)
//        Whitebox.setInternalState(priceBookProductRelatedListController, "accountId", "xxx")
//        InfraServiceFacade infraServiceFacade = PowerMockito.mock(InfraServiceFacade)
//        Whitebox.setInternalState(priceBookProductRelatedListController, "infraServiceFacade", infraServiceFacade)
//        DomainPluginParam domainPluginParam = new DomainPluginParam();
//        Map<String, String> fieldMapping = Maps.newHashMap();
//        fieldMapping.put("form_account_id", "account_id");
//        fieldMapping.put("form_partner_id", "partner_id");
//        domainPluginParam.setFieldMapping(fieldMapping);
//        PowerMockito.doReturn(domainPluginParam).when(infraServiceFacade, "findPluginParam", any(), any(), any())
//        Whitebox.setInternalState(priceBookProductRelatedListController, "bizConfigThreadLocalCacheService", bizConfigThreadLocalCacheService)
//        PowerMockito.doReturn(true).when(bizConfigThreadLocalCacheService, "isOpenPricePolicy", any(), any())
//        PowerMockito.suppress(MemberMatcher.method(StandardRelatedListController, "handleFilters", SearchTemplateQuery.class))
//        PowerMockito.when(SFAConfigUtil.isCPQ(anyString())).thenReturn(true)
//        PowerMockito.when(GrayUtil.isGrayMobileProductPackage(anyString())).thenReturn(false)
//        PowerMockito.when(RequestUtil.isMobileOrH5Request()).thenReturn(true)
//        PowerMockito.when(GrayUtil.isGrayOrderPromotion(anyString())).thenReturn(true)
//        PowerMockito.when(PromotionUtil.getIsPromotionEnable(any(), any())).thenReturn(true)
//        when:
//        SearchTemplateQuery result = priceBookProductRelatedListController.customSearchTemplate(searchTemplateQuery)
//        then:
//        result != null
//    }
//
//    void initSpringContext() {
//        def stubApplicationContext = Stub(ApplicationContext)
//        def util = new SpringContextUtil()
//        util.setApplicationContext(stubApplicationContext)
//        Map<Class, Object> map = new HashMap<>()
//        stubApplicationContext.getBean(_ as Class) >> { Class requiredType ->
//            map.computeIfAbsent(requiredType, { key ->
//                Stub(requiredType)
//            })
//        }
//
//        Map<String, Object> map2 = new HashMap<>()
//        stubApplicationContext.getBean(_ as String, _ as Class) >> { String name, Class requiredType ->
//            map2.computeIfAbsent(name, { key ->
//                Stub(requiredType)
//            })
//        }
//    }
//
//    def getObjectData(List<String> fieldList) {
//        def objectData = new ObjectData();
//        fieldList.each {
//            objectData.set(it, "xxxx")
//        }
//        objectData.setTenantId(tenantId)
//        objectData
//    }
//}
