package com.facishare.crm.prm.constant;

/**
 * <AUTHOR>
 * @time 2024-07-09 10:51
 * @Description
 */
public interface PrmI18nConstant {
    /**
     * 协议关联的打印模版不存在，请先关联具体的打印模版
     */
    String PRM_PRINT_TEMPLATE_NOT_FOUND = "prm.print.template.not.found";

    /**
     * 签约方案不存在
     */
    String PRM_SIGN_SCHEME_NOT_FOUND = "prm.sign.scheme.not.found";
    /**
     * 请上传注册协议后再提交
     */
    String PRM_UPLOAD_REGISTER_AGREEMENT = "prm.upload.register.agreement";

    /**
     * 对象:{0} 描述不存在
     */
    String PRM_OBJECT_DESCRIBE_NOT_EXISTS = "prm.object.describe.not.exists";
    /**
     * 字段:{0} 描述不存在
     */
    String PRM_FIELD_DESCRIBE_NOT_EXISTS = "prm.field.describe.not.exists";

    /**
     * 字段:{0} 非查找关联合作伙伴协议字段
     */
    String PRM_FIELD_NOT_RELATED_AGREEMENT_FIELD = "prm.field.not.related.agreement.field";


    /**
     * 伙伴协议{0}{1}后到期，需要重新续签。
     */
    String PRM_AGREEMENT_INITIATE_RENEWAL_ALERT = "prm.agreement.initiate.renewal.alert";

    /**
     * {0}{1}后到期
     */
    String PRM_AGREEMENT_INITIATE_RENEWAL_ALERT_V2 = "prm.agreement.initiate.renewal.alert.v2";
    /**
     * 协议已逾期{0}天，请尽快完成续约，{1}日后，原协议将终止，并停用互联账号
     */
    String PRM_AGREEMENT_EXPIRED_ALERT = "prm.agreement.expired.alert";


    /**
     * 去续约
     */
    String PRM_RENEWAL_AGREEMENT_BUTTON_LABEL = "prm.renewal.agreement.button.label";
    /**
     * 您的「会员忠诚度」产品已过期，请续费后使用伙伴忠诚度服务。
     */
    String PRM_MEMBERSHIP_EXPIRED_ALERT = "prm.membership.expired.alert";
    /**
     * 您未开启「会员」开关，请开启开关后，再次使用伙伴忠诚度服务。
     */
    String PRM_MEMBERSHIP_SWITCH_OFF_ALERT = "prm.membership.switch.off.alert";
    /**
     * 伙伴忠诚度组件只能在下游应用中使用，请调整组件位置后重新使用。
     */
    String PRM_MEMBERSHIP_COMPONENT_NOT_ALLOW_USE = "prm.membership.component.not.allow.use";
    /**
     * 协议不可签约提交，该应用仅配置用 PartnerObj 对象，请联系管理员调整渠道准入配置后再提交。
     */
    String PRM_AGREEMENT_NOT_SIGN_SUBMIT_ALERT = "prm.agreement.not.sign.submit.alert";
    /**
     * 协议不可签约提交，该应用仅配置用 AccountObj 对象，请联系管理员调整渠道准入配置后再提交。
     */
    String PRM_AGREEMENT_NOT_SIGN_SUBMIT_ALERT_ACCOUNT = "prm.agreement.not.sign.submit.alert.account";
    /**
     * 您未开启渠道准入功能，无法使用渠道准入组件，请联系上游人员调整组建配置
     */
    String PRM_CHANNEL_NOT_OPEN_ALERT = "prm.channel.not.open.alert";
    /**
     * 根据渠道准入配置，无法找到对应的关系数据
     */
    String PRM_CHANNEL_CNA_T_FIND_ADMISSION_DATA = "prm.channel.cna.t.find.admission.data";
}
