package com.facishare.crm.prm.service.model;

import com.facishare.crm.platform.annotation.Convertible;
import com.facishare.crm.platform.annotation.Mappable;
import lombok.Data;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-08
 * ============================================================
 */
@Data
@Convertible
public class ChannelStageRecord {
    @Mappable(fieldApiName = "_id")
    private String id;
    @Mappable(from = "task_progress")
    private String stage;
    @Mappable(from = "enterprise_id")
    private String enterpriseId;
    @Mappable(from = "biz_scope")
    private String bizScope;

}
