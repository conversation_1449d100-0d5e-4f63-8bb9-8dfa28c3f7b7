package com.facishare.crm.prm.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.enums.SignModel;
import com.facishare.crm.model.ChannelManagementDTO;
import com.facishare.crm.model.ChannelRpcModel;
import com.facishare.crm.model.PartnerChannelManage;
import com.facishare.crm.model.SignSchemeModel;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.prm.service.model.ChannelStageRecord;
import com.facishare.crm.prm.service.model.PrmChannelModel;
import com.facishare.crm.sfa.model.ChannelServiceModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.ChannelAgreementService;
import com.facishare.crm.sfa.predefine.service.ChannelCacheService;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.channel.ChannelAdmissionService;
import com.facishare.crm.sfa.prm.api.client.EnterpriseRelationServiceAdapter;
import com.facishare.crm.sfa.prm.api.dto.AdmissionConfigDTO;
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer;
import com.facishare.crm.sfa.prm.api.enums.AgreementStatus;
import com.facishare.crm.sfa.prm.api.enums.ChannelStage;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.core.service.TimeComputeService;
import com.facishare.crm.sfa.prm.platform.enums.TimeUnit;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.PartnerConfigGrayUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.PrintTemplate;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.prm.PartnerCoreService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.prm.constant.PrmI18nConstant.*;
import static com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel.AGREEMENT_ID;
import static com.facishare.crm.sfa.prm.core.constants.ChannelConstants.ACCOUNT_OBJ;
import static com.facishare.crm.sfa.prm.core.constants.ChannelConstants.PARTNER_OBJ;

/**
 * <AUTHOR>
 * @time 2024-06-28 15:07
 * @Description
 */
@Service
@Slf4j
public class PrmChannelService {
    @Resource
    private PartnerCoreService partnerCoreService;
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource(name = "channelServiceProvider")
    private ChannelService channelService;
    @Resource
    private PrintTemplateExportService printTemplateExportService;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ConfigService configService;
    @Resource(name = "metaDataService")
    private MetaDataService metaDataService;
    @Resource
    private TimeComputeService timeComputeService;
    @Resource
    private ChannelCacheService channelCacheService;
    @Resource
    private DescribeEnhancer describeEnhancer;
    @Resource(name = "enterpriseRelationServiceAdapterImpl")
    private EnterpriseRelationServiceAdapter enterpriseRelationServiceAdapter;
    @Resource
    private ChannelAgreementService channelAgreementService;
    @Resource
    private ChannelAdmissionService channelAdmissionService;
    @Resource
    private ActivationTaskDataProxy activationTaskDataProxy;


    private static final String AGREEMENT_ATTACHMENT = "agreement_attachment";
    private static final String SIGNING_STATUS = "signing_status";
    private static final String AGREEMENT_STASH_KEY = "agreement_stash_key";
    private static final String VAR_MESSAGE_SYMBOL = "${var_message}";
    private static final String VAR_OVERDUE_MESSAGE_SYMBOL = "${var_overdue_message}";

    public String queryMatchLayoutApiName(User user, String admissionApiName) {
        if (channelService.notExistsModule(user)) {
            log.info("PrmChannelService#queryMatchLayoutApiName 不存在 license, tenant:{}", user.getTenantId());
            return null;
        }
        String mapperDataId = enterpriseRelationServiceAdapter.fetchCrmMapperObjectDataId(user, admissionApiName);
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, mapperDataId, admissionApiName);
        if (admissionData == null) {
            return null;
        }
        ChannelServiceModel.MatchScheme matchScheme = channelService.matchPriorityLayoutApiName(user, admissionApiName, admissionData);
        return matchScheme.getMatch();
    }


    public ChannelServiceModel.MatchScheme matchProvisionData(User user) {
        String admissionObject = channelService.fetchChannelAdmissionObject(user);
        String mapperDataId = enterpriseRelationServiceAdapter.fetchCrmMapperObjectDataId(user, admissionObject);
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, mapperDataId, admissionObject);
        if (admissionData == null) {
            log.warn("PrmChannelService#matchProvisionData admissionData is null, user:{}, admissionObject:{}", user.getTenantId(), admissionObject);
            return ChannelServiceModel.MatchScheme.builder().build();
        }
        ChannelServiceModel.MatchScheme matchScheme = channelService.matchProvisionScheme(user, admissionData, admissionObject);
        if (!matchScheme.matchSchemeSuccess()) {
            return matchScheme;
        }
        List<IObjectData> objectDataList = metaDataFindServiceExt.findObjectByIdsWithHtml(user, matchScheme.getMatchList(), SFAPreDefineObject.PartnerProvision.getApiName());
        List<IObjectData> sortedDataList = sortMatchDataList(objectDataList, matchScheme.getMatchList());
        matchScheme.setMatchDataList(sortedDataList);
        return matchScheme;
    }

    private List<IObjectData> sortMatchDataList(List<IObjectData> objectDataList, List<String> matchList) {
        List<IObjectData> sortedDataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(objectDataList) || CollectionUtils.isEmpty(matchList)) {
            return sortedDataList;
        }
        Map<String, IObjectData> dataMapping = objectDataList
                .stream()
                .collect(Collectors.toMap(DBRecord::getId, d -> d,
                        (k1, k2) -> k1));
        for (String id : matchList) {
            IObjectData data = dataMapping.get(id);
            if (data != null) {
                sortedDataList.add(data);
            }
        }
        return sortedDataList;
    }

    public ChannelServiceModel.MatchScheme matchAgreementData(User user, PrmChannelModel.MatchAgreementArg matchAgreementArg) {
        String admissionObject = channelService.fetchChannelAdmissionObject(user);
        String mapperDataId = enterpriseRelationServiceAdapter.fetchCrmMapperObjectDataId(user, admissionObject);
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, mapperDataId, admissionObject);
        if (admissionData == null) {
            log.warn("PrmChannelService#matchAgreementData admissionData is null, user:{}, admissionObject:{}", user.getTenantId(), admissionObject);
            return ChannelServiceModel.MatchScheme.builder().build();
        }
        User systemUser = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        ChannelServiceModel.MatchScheme matchScheme = channelService.matchSignScheme(systemUser, admissionObject, admissionData);
        if (!matchScheme.matchSchemeSuccess()) {
            return matchScheme;
        }
        IObjectData objectByIdWithHtml = metaDataFindServiceExt.findObjectByIdWithHtml(systemUser, matchScheme.getMatch(), SFAPreDefineObject.PartnerAgreement.getApiName());
        List<IObjectData> slaveObjectDataList = findSlaveDataList(systemUser, matchAgreementArg, objectByIdWithHtml);
        matchScheme.setMatchData(objectByIdWithHtml);
        matchScheme.setMatchSlaveDataList(slaveObjectDataList);
        return matchScheme;
    }

    private List<IObjectData> findSlaveDataList(User user, PrmChannelModel.MatchAgreementArg matchAgreementArg, IObjectData objectByIdWithHtml) {
        if (objectByIdWithHtml == null || matchAgreementArg == null) {
            return Lists.newArrayList();
        }
        if (StringUtils.isAnyBlank(matchAgreementArg.getRelatedFieldApiName(), matchAgreementArg.getObjectApiName())) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1000);
        SearchUtil.fillFilterEq(query.getFilters(), matchAgreementArg.getRelatedFieldApiName(), objectByIdWithHtml.getId());
        return metaDataFindServiceExt.findObjectByQueryWithHtml(user, matchAgreementArg.getObjectApiName(), query);
    }

    public Boolean submitAgreement(User user, PrmChannelModel.SubmitAgreementArg arg) {
        String appId = RequestContextManager.getContext().getAppId();
        String admissionObject = channelService.fetchChannelAdmissionObject(user);
        // 代理通绑定合作伙伴
        if (AppIdMapping.isPRM(appId) && !SFAPreDefineObject.Partner.getApiName().equals(admissionObject)) {
            throw new ValidateException(I18N.text(PRM_AGREEMENT_NOT_SIGN_SUBMIT_ALERT));
        }
        // 订货通绑定客户
        if (AppIdMapping.isTargetApp(appId, AppIdMapping.DHT_APP_ID) && !SFAPreDefineObject.Account.getApiName().equals(admissionObject)) {
            throw new ValidateException(I18N.text(PRM_AGREEMENT_NOT_SIGN_SUBMIT_ALERT_ACCOUNT));
        }
        String partnerAgreementId = arg.getPartnerAgreementId();
        PartnerChannelManage.SignScheme signScheme = channelService.querySignSchemeById(user, arg.getSignSchemeId());
        if (signScheme == null) {
            throw new ValidateException(I18N.text(PRM_SIGN_SCHEME_NOT_FOUND));
        }
        if (arg.getValueMapping() == null || arg.getValueMapping().isEmpty()) {
            throw new ValidateException(I18N.text(PRM_UPLOAD_REGISTER_AGREEMENT));
        }
        SignModel signModel = signScheme.getSignModelEnum();
        String crmMapperId = enterpriseRelationServiceAdapter.fetchCrmMapperObjectDataId(user, admissionObject);
        String partnerAgreementDetailDataId = channelAgreementService.upsertPartnerAgreementDetailData(user, partnerAgreementId, crmMapperId, admissionObject);
        boolean success = updateAdmissionDataWhenSubmitAgreement(user, arg, signModel, partnerAgreementId, crmMapperId, admissionObject);
        if (success) {
            agreementStashDel(user);
        } else {
            deletePartnerAgreementDetailData(user, partnerAgreementDetailDataId);
        }
        return success;
    }

    public void agreementStashDel(User user) {
        configService.deleteUserConfig(user, AGREEMENT_STASH_KEY);
        channelCacheService.delCache(user);
    }

    private boolean updateAdmissionDataWhenSubmitAgreement(User user, PrmChannelModel.SubmitAgreementArg arg, SignModel signModel, String partnerAgreementId, String crmMapperId, String admissionObject) {
        if (signModel == SignModel.MANUAL) {
            return updateAdmissionObjectData(user, admissionObject, crmMapperId, arg.getValueMapping());
        } else if (signModel == SignModel.APL) {
            IObjectData data = channelService.queryAgreementDataById(user, partnerAgreementId);
            String aplApiName = data.get(SignSchemeModel.APL_API_NAME, String.class);
            channelService.executeLayoutFunction(user, aplApiName, admissionObject, crmMapperId);
        }
        return true;
    }

    private void deletePartnerAgreementDetailData(User user, String partnerAgreementDetailDataId) {
        if (StringUtils.isBlank(partnerAgreementDetailDataId)) {
            return;
        }
        IObjectData partnerAgreementDetailData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, partnerAgreementDetailDataId, SFAPreDefineObject.PartnerAgreement.getApiName());
        if (partnerAgreementDetailData != null) {
            serviceFacade.bulkDeleteDirect(Lists.newArrayList(partnerAgreementDetailData), user);
        }
    }

    private boolean updateAdmissionObjectData(User user, String admissionObject, String crmMapperId, Map<String, Object> valueMapping) {
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, crmMapperId, admissionObject);
        if (admissionData == null) {
            log.warn("根据 crmMapperId 未找到数据, tenantId:{}, admissionObject:{}, crmMapperId:{}", user.getTenantId(), admissionObject, crmMapperId);
            return false;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(admissionData);
        if (objectDataExt.isLock() || objectDataExt.isInChange()) {
            ObjectLifeStatus lifeStatus = objectDataExt.getLifeStatus();
            log.warn("PrmChannelService#updateAdmissionObjectData is Lock or is In Change tenant:{}, life status:{}", user.getTenantId(), lifeStatus);
            return false;
        }
        IObjectDescribe admissionDescribe = describeEnhancer.fetchObject(user, admissionObject);
        if (admissionDescribe == null) {
            log.warn("未找到描述, tenantId:{}, admissionObject:{}", user.getTenantId(), admissionObject);
            return false;
        }
        editAdmissionDataAndTriggerFlow(valueMapping, admissionDescribe, admissionData);
        return true;
    }

    private void editAdmissionDataAndTriggerFlow(Map<String, Object> valueMapping, IObjectDescribe admissionDescribe, IObjectData admissionData) {
        Optional.ofNullable(valueMapping).orElse(Maps.newHashMap()).forEach((fieldApiName, fieldValue) -> setObjectValue(admissionDescribe, admissionData, fieldApiName, fieldValue));
        admissionData.set(SIGNING_STATUS, SignStatus.SIGNED.getStatus());
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setSkipApprovalFlow(false);
        arg.setObjectData(ObjectDataDocument.of(admissionData));
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setIsDuplicateSearch(true);
        optionInfo.setUseValidationRule(true);
        optionInfo.setSkipFuncValidate(false);
        arg.setOptionInfo(optionInfo);
        ActionContext actionContext = new ActionContext(RequestContextManager.getContext(), admissionDescribe.getApiName(), ObjectAction.UPDATE.getActionCode());
        log.info("PrmChannelService#editAdmissionDataAndTriggerFlow, triggerAction:{}", arg);
        serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
    }

    private boolean nonFileTransferGray(User user) {
        String fileTransferGray = configService.findTenantConfig(user, "non_file_transfer_gray");
        return "1".equals(fileTransferGray);
    }

    private void setObjectValue(IObjectDescribe describe, IObjectData admissionData, String fieldApiName, Object fieldValue) {
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(fieldApiName);
        if (fieldDescribe == null) {
            return;
        }
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (fieldDescribeExt.isSystemField()) {
            return;
        }
        admissionData.set(fieldApiName, fieldValue);
    }

    public PrintTemplate.Result printAgreement(User user, PrmChannelModel.SubmitAgreementArg arg) {
        String templateId = channelService.queryAgreementTemplateById(user, arg.getPartnerAgreementId());
        if (StringUtils.isBlank(templateId)) {
            throw new ValidateException(I18N.text(PRM_PRINT_TEMPLATE_NOT_FOUND));
        }
        PrintTemplate.Result printResult = print(user, SFAPreDefineObject.PartnerAgreement.getApiName(), arg.getPartnerAgreementId(), templateId);
        updatePartnerAgreementAttachment(user, printResult);
        return printResult;
    }

    private @Nullable PrintTemplate.Result print(User user, String objectApiName, String dataId, String templateId) {
        if (StringUtils.isAnyBlank(objectApiName, dataId, templateId)) {
            return null;
        }
        PrintTemplate.Arg printArg = new PrintTemplate.Arg();
        printArg.setObjectId(dataId);
        printArg.setTemplateId(templateId);
        printArg.setObjectAPIName(objectApiName);
        return printTemplateExportService.print(user, printArg);
    }

    private void updatePartnerAgreementAttachment(User user, PrintTemplate.Result printResult) {
        if (printResult == null || StringUtils.isBlank(printResult.getPath())) {
            return;
        }
        String partnerId = partnerCoreService.getPartnerId(user);
        IObjectData partnerData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, partnerId, SFAPreDefineObject.Partner.getApiName());
        if (partnerData == null) {
            return;
        }
        Map<String, Object> fileAttachmentMap = new HashMap<>();
        fileAttachmentMap.put("create_time", System.currentTimeMillis());
        fileAttachmentMap.put("size", printResult.getFileSize());
        fileAttachmentMap.put("filename", printResult.getName());
        fileAttachmentMap.put("ext", printResult.getFileType());
        fileAttachmentMap.put("path", printResult.getPath());
        partnerData.set(AGREEMENT_ATTACHMENT, Lists.newArrayList(fileAttachmentMap));
        partnerData.set(SIGNING_STATUS, "pending_signature");
        metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(partnerData), Lists.newArrayList("agreement_attachment", "signing_status"));
    }

    public String queryCurrentPartner(User user) {
        if (!user.isOutUser()) {
            return null;
        }
        return partnerCoreService.getPartnerId(user);
    }

    public void checkSubmitAgreementArg(User systemUser, PrmChannelModel.MatchAgreementArg matchAgreementArg) {
        if (matchAgreementArg == null || StringUtils.isAnyBlank(matchAgreementArg.getObjectApiName(), matchAgreementArg.getRelatedFieldApiName())) {
            return;
        }
        IObjectDescribe describe = serviceFacade.findObject(systemUser.getTenantId(), matchAgreementArg.getObjectApiName());
        if (describe == null) {
            throw new ValidateException(I18N.text(PRM_OBJECT_DESCRIBE_NOT_EXISTS, matchAgreementArg.getObjectApiName()));
        }
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(matchAgreementArg.getRelatedFieldApiName());
        if (fieldDescribe == null) {
            throw new ValidateException(I18N.text(PRM_FIELD_DESCRIBE_NOT_EXISTS, matchAgreementArg.getRelatedFieldApiName()));
        }
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (!fieldDescribeExt.isRefObjectField()) {
            throw new ValidateException(I18N.text(PRM_FIELD_NOT_RELATED_AGREEMENT_FIELD, matchAgreementArg.getRelatedFieldApiName()));
        }
        if (!SFAPreDefineObject.PartnerAgreement.getApiName().equals(fieldDescribeExt.getRefObjTargetApiName())) {
            throw new ValidateException(I18N.text(PRM_FIELD_NOT_RELATED_AGREEMENT_FIELD, matchAgreementArg.getRelatedFieldApiName()));
        }
    }

    public Map<String, Object> agreementStash(User user, Map<String, Object> stashMap) {
        if (nonRedisCache(user)) {
            return configStash(user, stashMap);
        }
        return channelCacheService.stash(user, stashMap);

    }

    private Map<String, Object> configStash(User user, Map<String, Object> stashMap) {
        if (stashMap == null || stashMap.isEmpty()) {
            return Maps.newHashMap();
        }
        configService.upsertUserConfig(user, AGREEMENT_STASH_KEY, JsonUtil.toJsonWithNull(stashMap), ConfigValueType.JSON);
        return stashMap;
    }

    public Map<String, Object> agreementStashPop(User user) {
        return channelCacheService.stashPop(user);
    }

    public boolean nonRedisCache(User user) {
        String value = configService.findTenantConfig(user, "non_channel_redis_cache");
        return "1".equals(value);
    }

    public Map<String, Object> agreementStashPeek(User user) {
        if (nonRedisCache(user)) {
            return configCacheStashPeek(user);
        }
        Map<String, Object> cacheValue = channelCacheService.stashPeek(user);
        if (cacheValue == null || cacheValue.isEmpty()) {
            return configCacheStashPeek(user);
        }
        return cacheValue;
    }

    private Map<String, Object> configCacheStashPeek(User user) {
        String userConfig = configService.findUserConfig(user, AGREEMENT_STASH_KEY);
        if (StringUtils.isBlank(userConfig)) {
            return Maps.newHashMap();
        }
        try {
            return JSON.parseObject(userConfig, Map.class);
        } catch (Exception e) {
            log.error("agreementStashPeek#configCacheStashPeek error, userConfig:{}", userConfig, e);
        }
        return Maps.newHashMap();
    }

    public List<PrintTemplate.Result> print(User user, List<PrmChannelModel.PrintItem> printItemList) {
        if (CollectionUtils.isEmpty(printItemList)) {
            return Lists.newArrayList();
        }
        List<PrintTemplate.Result> rst = Lists.newArrayList();
        for (PrmChannelModel.PrintItem printItem : printItemList) {
            if (PartnerConfigGrayUtils.isSupportPrint(printItem.getObjectApiName())) {
                PrintTemplate.Result print = print(user, printItem.getObjectApiName(), printItem.getDataId(), printItem.getTemplateId());
                if (print != null) {
                    rst.add(print);
                }
            }
        }
        return rst;
    }

    private boolean nonChannelApp(User user, String appId) {
        if (StringUtils.isBlank(appId)) {
            log.warn("nonChannelApp appId is empty, tenant:{}", user.getTenantId());
            return true;
        }
        AdmissionConfigDTO admissionConfigDTO = channelAdmissionService.fetchChannelAdmissionConfig(user);
        if (admissionConfigDTO == null) {
            return true;
        }
        String applyToApp = admissionConfigDTO.getApplyToApp();
        if (StringUtils.isBlank(applyToApp)) {
            return true;
        }
        String appIdByName = AppIdMapping.getAppIdByName(applyToApp);
        return !appId.equals(appIdByName);
    }

    public PrmChannelModel.RenewalStatus renewalCycleStatus(User user, String appId) {
        PrmChannelModel.RenewalStatus renewalStatus = PrmChannelModel.RenewalStatus.builder().build();
        if (nonChannelApp(user, appId)) {
            return renewalStatus;
        }
        Boolean alertHasRead = channelCacheService.prmAlertHasRead(user);
        if (alertHasRead == null) {
            return renewalStatus;
        }
        String admissionObject = channelService.fetchChannelAdmissionObject(user);
        String crmMapperId = enterpriseRelationServiceAdapter.fetchCrmMapperObjectDataId(user, admissionObject);
        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, crmMapperId, admissionObject);
        IObjectData partnerAgreementDetailData = channelAgreementService.fetchPartnerAgreementDetailData(user, crmMapperId, admissionObject, AgreementStatus.ACTIVE);
        Long endDateTimestamp = ObjectDataUtils.getValue(partnerAgreementDetailData, PartnerAgreementDetailModel.END_DATE, Long.class, null);
        if (partnerAgreementDetailData == null || endDateTimestamp == null) {
            return renewalStatus;
        }
        ChannelManagementDTO.MatchSignSchemeWithPrmReminder matchSignSchemeWithPrmReminder = channelService.matchSignSchemeWithReminderInfo(user, admissionData, admissionObject);
        if (matchSignSchemeWithPrmReminder == null ||
                matchSignSchemeWithPrmReminder.getPrmAlertWindowReminder() == null ||
                !Boolean.TRUE.equals(matchSignSchemeWithPrmReminder.getPrmAlertWindowReminder().getActivated())) {
            return renewalStatus;
        }
        PartnerChannelManage.SignScheme matchSignSchemeDate = channelService.matchSignSchemeDate(user, admissionObject, admissionData);
        if (matchSignSchemeDate == null) {
            return renewalStatus;
        }
        long currentTimeMillis = System.currentTimeMillis();
        String alertText;
        if (currentTimeMillis <= endDateTimestamp) {
            alertText = generateReminderObject(user, endDateTimestamp, partnerAgreementDetailData, matchSignSchemeWithPrmReminder);
        } else {
            long maxEndDateTimestamp = currentTimeMillis;
            Integer renewalWindowEnd = matchSignSchemeDate.getRenewalWindowEnd();
            String renewalWindowUnit = matchSignSchemeDate.getRenewalWindowUnit();
            if (StringUtils.isNotBlank(renewalWindowUnit) && renewalWindowEnd != null) {
                maxEndDateTimestamp = timeComputeService.calculateTimestampByOffset(endDateTimestamp, renewalWindowEnd, TimeUnit.find(renewalWindowUnit));
            }
            alertText = generateOverdueReminderObject(user, currentTimeMillis, endDateTimestamp, maxEndDateTimestamp, partnerAgreementDetailData, matchSignSchemeWithPrmReminder);
        }
        renewalStatus.setAlertText(alertText);
        renewalStatus.setMatchSignSchemeId(matchSignSchemeDate.getSignSchemeId());
        renewalStatus.setRenewable(Boolean.TRUE);
        renewalStatus.setHasRead(alertHasRead);
        return renewalStatus;
    }

    private String generateOverdueReminderObject(User user, long currentTimeMillis, Long endDateTimestamp, long maxEndDateTimestamp, IObjectData partnerAgreementDetailData, ChannelManagementDTO.MatchSignSchemeWithPrmReminder matchSignSchemeWithPrmReminder) {
        int overdueDayTime = timeComputeService.calculateDifference(endDateTimestamp, TimeUnit.DAY);
        overdueDayTime = Math.abs(overdueDayTime);

        if (overdueDayTime <= 0) {
            overdueDayTime = 0;
        }
        LocalDate endDate = Instant.ofEpochMilli(endDateTimestamp)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        LocalDate maxEndDate = Instant.ofEpochMilli(maxEndDateTimestamp)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        LocalDate localDate = Instant.ofEpochMilli(currentTimeMillis)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        int stopDayTime = (int) timeComputeService.calculateDifference(localDate, maxEndDate, TimeUnit.DAY);
        String timeText = getOverdueTimeText(overdueDayTime, stopDayTime);
        String agreementTitle = fetchAgreementTitle(user, partnerAgreementDetailData);
        String varMessage = agreementTitle + timeText;
        return replaceAlertText(varMessage, matchSignSchemeWithPrmReminder.getPrmAlertWindowReminder().getMessage(), VAR_OVERDUE_MESSAGE_SYMBOL);
    }

    private String getOverdueTimeText(int overdueDayTime, int stopDayTime) {

        return I18N.text(PRM_AGREEMENT_EXPIRED_ALERT, overdueDayTime, stopDayTime);
    }

    private String generateReminderObject(User user, Long endDateTimestamp, IObjectData partnerAgreementDetailData, ChannelManagementDTO.MatchSignSchemeWithPrmReminder matchSignSchemeWithPrmReminder) {
        int weekTime = timeComputeService.calculateDifference(endDateTimestamp, TimeUnit.WEEK);
        String timeText = getTimeText(weekTime, endDateTimestamp);
        String agreementTitle = fetchAgreementTitle(user, partnerAgreementDetailData);
        String varMessage = agreementTitle + timeText;
        return replaceAlertText(varMessage, matchSignSchemeWithPrmReminder.getPrmAlertWindowReminder().getMessage(), VAR_MESSAGE_SYMBOL);
    }

    private String getTimeText(int weekTime, Long endDate) {
        String timeText;
        if (weekTime == 0) {
            int dayTime = timeComputeService.calculateDifference(endDate, TimeUnit.DAY);
            timeText = I18N.text(PRM_AGREEMENT_INITIATE_RENEWAL_ALERT_V2, dayTime, I18N.text(TimeUnit.DAY.getI18n()));
        } else {
            timeText = I18N.text(PRM_AGREEMENT_INITIATE_RENEWAL_ALERT_V2, weekTime, I18N.text(TimeUnit.WEEK.getI18n()));
        }
        return timeText;
    }

    private String replaceAlertText(String varMessage, String message, String varMessageSymbol) {
        if (StringUtils.isBlank(message)) {
            return varMessage;
        }
        return message.replace(VAR_MESSAGE_SYMBOL, varMessage);
    }

    private String fetchAgreementTitle(User user, IObjectData partnerAgreementDetailData) {
        String agreementId = partnerAgreementDetailData.get(AGREEMENT_ID, String.class);
        if (StringUtils.isBlank(agreementId)) {
            return "";
        }
        List<IObjectData> dataList = metaDataFindServiceExt.findObjectByIdsWithFieldsIgnoreAll(user,
                Lists.newArrayList(),
                SFAPreDefineObject.PartnerAgreement.getApiName(),
                Lists.newArrayList(IObjectData.NAME));
        if (CollectionUtils.isEmpty(dataList)) {
            return "";
        }
        IObjectData objectData = dataList.get(0);
        return ObjectDataUtils.getValueOrDefault(objectData, "agreement_title", "");
    }

    public void alertRead(User user) {
        // 时间：1年
        int expireTime = 60 * 60 * 24 * 365;
        channelCacheService.prmAlertRead(user, expireTime);
    }

    public List<PrmChannelModel.UpsertAgreementDetailResult> upsertAgreementDetails(User user, List<String> dataIds) {
        List<PrmChannelModel.UpsertAgreementDetailResult> results = Lists.newArrayList();
        String admissionObject = channelService.fetchChannelAdmissionObject(user);
        for (String dataId : dataIds) {
            IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, dataId, admissionObject);
            if (admissionData == null) {
                log.warn("admissionData is null, dataId:{}", dataId);
                continue;
            }
            User systemUser = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            ChannelServiceModel.MatchScheme matchScheme = channelService.matchSignScheme(systemUser, admissionObject, admissionData);
            if (!matchScheme.matchSchemeSuccess()) {
                log.warn("matchScheme is null, dataId:{}", dataId);
                continue;
            }
            String partnerAgreementDetailId = channelAgreementService.upsertPartnerAgreementDetailData(user, matchScheme.getMatch(), dataId, admissionObject);
            PrmChannelModel.UpsertAgreementDetailResult build = PrmChannelModel.UpsertAgreementDetailResult.builder()
                    .partnerId(dataId)
                    .signSchemeId(matchScheme.getSchemeId())
                    .partnerAgreementDetailId(partnerAgreementDetailId)
                    .build();
            results.add(build);
        }
        log.warn("upsertAgreementDetails:{}", results);
        return results;
    }

    public PartnerChannelManage.CustomText fetchSigningText(User user) {
        return channelService.fetchCustomText(user, ChannelService.SIGN_CUSTOM_TEXT);
    }

    public PartnerChannelManage.CustomText fetchRenewalText(User user) {
        return channelService.fetchCustomText(user, ChannelService.RENEWAL_CUSTOM_TEXT);
    }

    public PartnerChannelManage.CustomText fetchRegisterText(User user) {
        return channelService.fetchCustomText(user, ChannelService.REGISTER_CUSTOM_TEXT);
    }

    public String fetchCurrentMapperId(User user) {
        String admissionObject = channelService.fetchChannelAdmissionObject(user);
        return enterpriseRelationServiceAdapter.fetchCrmMapperObjectDataId(user, admissionObject);
    }

    public Boolean createAgreementDetail(User user, ChannelRpcModel.CreateAgreementDetailArg arg) {
        IObjectData data = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, arg.getDataId(), arg.getObjectApiName());
        IObjectData oldAgreementDetailData = fetchChannelAgreementDetailData(user, arg.getObjectApiName(), arg.getDataId(), AgreementStatus.ACTIVE);
        channelAgreementService.createAgreementDetailWhenInitiateRenewal(user, arg.getObjectApiName(), data, oldAgreementDetailData);
        return Boolean.TRUE;
    }

    public IObjectData fetchChannelAgreementDetailData(User user, String objectApiName, String dataId, AgreementStatus agreementStatus) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        if (agreementStatus != null) {
            SearchUtil.fillFilterEq(query.getFilters(), "agreement_status", agreementStatus.getStatus());
        } else {
            SearchUtil.fillFilterIsNull(query.getFilters(), "agreement_status");
        }
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.CREATE_TIME, false)));
        if (ACCOUNT_OBJ.equals(objectApiName)) {
            SearchUtil.fillFilterEq(query.getFilters(), "belong_account_id", dataId);
        } else if (PARTNER_OBJ.equals(objectApiName)) {
            SearchUtil.fillFilterEq(query.getFilters(), "belong _partner_id", dataId);
        } else {
            return null;
        }
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, "PartnerAgreementDetailObj", query).getData();
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        } else {
            return dataList.get(0);
        }
    }

    /**
     * 需是下游用户的请求
     *
     * @param user
     * @param appId
     * @return
     */
    public PartnerChannelManage.AdmissionData queryAdmissionData(User user, String appId) {
        AdmissionConfigDTO admissionConfigDTO = channelAdmissionService.fetchChannelAdmissionConfig(user);
        if (admissionConfigDTO == null) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_NOT_OPEN_ALERT));
        }
        String applyToApp = admissionConfigDTO.getApplyToApp();
        if (!AppIdMapping.isTargetApp(appId, applyToApp)) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_NOT_OPEN_ALERT));
        }
        String mapperObjectDataId = enterpriseRelationServiceAdapter.fetchCrmMapperObjectDataId(user, admissionConfigDTO.getRelatedObjectApiName());
        if (StringUtils.isEmpty(mapperObjectDataId)) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_NOT_OPEN_ALERT));
        }
        return PartnerChannelManage.AdmissionData
                .builder()
                .objectDescribeApiName(admissionConfigDTO.getRelatedObjectApiName())
                .dataId(mapperObjectDataId)
                .build();
    }

    public boolean isSignedThrough(User user, String appId) {
        if (nonChannelApp(user, appId)) {
            return true;
        }
        AdmissionConfigDTO admissionConfigDTO = channelAdmissionService.fetchChannelAdmissionConfig(user);
        String relatedObjectApiName = admissionConfigDTO.getRelatedObjectApiName();
        String mapperObjectDataId = enterpriseRelationServiceAdapter.fetchCrmMapperObjectDataId(user, relatedObjectApiName);
        List<ChannelStageRecord> channelStageRecords = activationTaskDataProxy.fetchStageRecords(user, relatedObjectApiName, mapperObjectDataId, user.getOutTenantId());
        if (CollectionUtils.isEmpty(channelStageRecords)) {
            return true;
        }
        return !channelStageRecords.stream()
                .map(record -> ChannelStage.find(record.getStage()))
                .filter(Objects::nonNull)
                .allMatch(stage -> stage == ChannelStage.REGISTERED);
    }

    public List<String> fetchRenewPages(User user) {
        AdmissionConfigDTO admissionConfigDTO = channelAdmissionService.fetchChannelAdmissionConfig(user);
        String relatedObjectApiName = admissionConfigDTO.getRelatedObjectApiName();
        String mapperObjectDataId = enterpriseRelationServiceAdapter.fetchCrmMapperObjectDataId(user, relatedObjectApiName);
        IObjectData mapperData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, mapperObjectDataId, relatedObjectApiName);
        if (mapperData == null) {
            return Lists.newArrayList();
        }
        ChannelServiceModel.MatchScheme matchScheme = channelService.matchSignScheme(user, relatedObjectApiName, mapperData);
        if (StringUtils.isBlank(matchScheme.getSchemeId())) {
            return Lists.newArrayList();
        }
        IObjectData signSchemeData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, matchScheme.getSchemeId(), SignSchemeModel.SIGN_SCHEME_OBJ);
        if (signSchemeData == null) {
            return Lists.newArrayList();
        }
        return DataUtils.getValue(signSchemeData, "renewal_page", List.class, Lists.newArrayList());
    }
}
