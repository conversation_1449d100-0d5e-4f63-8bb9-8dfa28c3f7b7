package com.facishare.crm.prm.resource;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.enums.ReminderTrigger;
import com.facishare.crm.model.ChannelManagementDTO;
import com.facishare.crm.model.ChannelRpcModel;
import com.facishare.crm.model.PartnerChannelManage;
import com.facishare.crm.platform.converter.ObjectConverter;
import com.facishare.crm.prm.service.PrmChannelService;
import com.facishare.crm.prm.service.model.PrmChannelModel;
import com.facishare.crm.sfa.model.ChannelServiceModel;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.prm.api.enums.ScheduleType;
import com.facishare.crm.sfa.prm.core.service.TimeComputeService;
import com.facishare.crm.sfa.prm.platform.enums.TimeUnit;
import com.facishare.paas.appframework.common.service.dto.PrintTemplate;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.Year;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2024-06-28 11:48
 * @IgnoreI18nFile
 * @Description
 */
@Service
@Slf4j
@ServiceModule("prm_channel")
public class PrmChannelResource {
    @Resource
    private PrmChannelService prmChannelService;
    @Resource
    private TimeComputeService timeComputeService;
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private ObjectConverter converter;

    @ServiceMethod("match_provision_data")
    public RestResult<PrmChannelModel.MatchProvisionResult> matchProvisionData(ServiceContext serviceContext) {
        ChannelServiceModel.MatchScheme matchScheme = prmChannelService.matchProvisionData(serviceContext.getUser());
        PrmChannelModel.MatchProvisionResult build = PrmChannelModel.MatchProvisionResult.builder()
                .provisionSchemeId(matchScheme.getSchemeId())
                .provisionDataList(matchScheme.getMatchDataList())
                .build();
        RestResult<PrmChannelModel.MatchProvisionResult> objectRestResult = new RestResult<>();
        objectRestResult.setData(build);
        return objectRestResult;
    }

    @ServiceMethod("match_agreement_data")
    public RestResult<PrmChannelModel.MatchAgreementResult> matchAgreementData(ServiceContext serviceContext, PrmChannelModel.MatchAgreementArg matchAgreementArg) {
        User systemUser = new User(serviceContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        prmChannelService.checkSubmitAgreementArg(systemUser, matchAgreementArg);
        ChannelServiceModel.MatchScheme matchScheme = prmChannelService.matchAgreementData(serviceContext.getUser(), matchAgreementArg);
        PrmChannelModel.MatchAgreementResult build = PrmChannelModel.MatchAgreementResult.builder()
                .signSchemeId(matchScheme.getSchemeId())
                .agreementData(matchScheme.getMatchData())
                .slaveAgreementDataList(matchScheme.getMatchSlaveDataList())
                .build();
        RestResult<PrmChannelModel.MatchAgreementResult> objectRestResult = new RestResult<>();
        objectRestResult.setData(build);
        return objectRestResult;
    }

    @ServiceMethod("submit_agreement")
    public RestResult<Boolean> submitAgreement(ServiceContext serviceContext, PrmChannelModel.SubmitAgreementArg arg) {
        Boolean submitted = prmChannelService.submitAgreement(serviceContext.getUser(), arg);
        RestResult<Boolean> objectRestResult = new RestResult<>();
        objectRestResult.setData(submitted);
        return objectRestResult;
    }

    @ServiceMethod("print_agreement")
    public RestResult<PrintTemplate.Result> printAgreement(ServiceContext serviceContext, PrmChannelModel.SubmitAgreementArg arg) {
        PrintTemplate.Result result = prmChannelService.printAgreement(serviceContext.getUser(), arg);
        RestResult<PrintTemplate.Result> objectRestResult = new RestResult<>();
        objectRestResult.setData(result);
        return objectRestResult;
    }

    @ServiceMethod("query_admission_data")
    public RestResult<PartnerChannelManage.AdmissionData> queryAdmissionData(ServiceContext serviceContext) {
        PartnerChannelManage.AdmissionData admissionData = prmChannelService.queryAdmissionData(serviceContext.getUser(), serviceContext.getAppId());
        RestResult<PartnerChannelManage.AdmissionData> objectRestResult = new RestResult<>();
        objectRestResult.setData(admissionData);
        return objectRestResult;
    }

    @ServiceMethod("query_current_partner")
    public RestResult<String> queryCurrentPartner(ServiceContext serviceContext) {
        String currentPartner = prmChannelService.queryCurrentPartner(serviceContext.getUser());
        RestResult<String> objectRestResult = new RestResult<>();
        objectRestResult.setData(currentPartner);
        return objectRestResult;
    }

    @ServiceMethod("fetch_current_mapper_id")
    public RestResult<String> fetchCurrentMapperId(ServiceContext serviceContext) {
        String crmMapperId = prmChannelService.fetchCurrentMapperId(serviceContext.getUser());
        RestResult<String> objectRestResult = new RestResult<>();
        objectRestResult.setData(crmMapperId);
        return objectRestResult;
    }

    @ServiceMethod("agreement_stash")
    public RestResult<Map<String, Object>> agreementStash(ServiceContext serviceContext, Map<String, Object> stashMap) {
        Map<String, Object> objectMap = prmChannelService.agreementStash(serviceContext.getUser(), stashMap);
        RestResult<Map<String, Object>> objectRestResult = new RestResult<>();
        objectRestResult.setData(objectMap);
        return objectRestResult;
    }

    @ServiceMethod("agreement_stash_peek")
    public RestResult<Map<String, Object>> agreementStashPeek(ServiceContext serviceContext) {
        Map<String, Object> objectMap = prmChannelService.agreementStashPeek(serviceContext.getUser());
        RestResult<Map<String, Object>> objectRestResult = new RestResult<>();
        objectRestResult.setData(objectMap);
        return objectRestResult;
    }

    @ServiceMethod("agreement_stash_pop")
    public RestResult<Map<String, Object>> agreementStashPop(ServiceContext serviceContext) {
        Map<String, Object> objectMap = prmChannelService.agreementStashPop(serviceContext.getUser());
        RestResult<Map<String, Object>> objectRestResult = new RestResult<>();
        objectRestResult.setData(objectMap);
        return objectRestResult;
    }

    @ServiceMethod("print")
    public RestResult<List<PrintTemplate.Result>> print(ServiceContext serviceContext, PrmChannelModel.PrintArg arg) {
        List<PrintTemplate.Result> printRstList = prmChannelService.print(serviceContext.getUser(), arg.getPrintItemList());
        RestResult<List<PrintTemplate.Result>> objectRestResult = new RestResult<>();
        objectRestResult.setData(printRstList);
        return objectRestResult;
    }

    @ServiceMethod("renewal_cycle_status")
    public PrmChannelModel.RenewalStatus renewalCycleStatus(ServiceContext serviceContext) {
        try {
            return prmChannelService.renewalCycleStatus(serviceContext.getUser(), serviceContext.getAppId());
        } catch (Exception e) {
            log.warn("renewalCycleStatus error", e);
        }
        return PrmChannelModel.RenewalStatus.builder().build();
    }

    @ServiceMethod("alert_read")
    public RestResult<Boolean> alertRead(ServiceContext serviceContext) {
        prmChannelService.alertRead(serviceContext.getUser());
        RestResult<Boolean> objectRestResult = new RestResult<>();
        objectRestResult.setData(Boolean.TRUE);
        return objectRestResult;
    }

    @ServiceMethod("upsert_agreement_details")
    public RestResult<List<PrmChannelModel.UpsertAgreementDetailResult>> upsertAgreementDetails(ServiceContext serviceContext, PrmChannelModel.UpsertAgreementDetail arg) {
        List<PrmChannelModel.UpsertAgreementDetailResult> upsertAgreementDetailResults = prmChannelService.upsertAgreementDetails(serviceContext.getUser(), arg.getDataIds());
        RestResult<List<PrmChannelModel.UpsertAgreementDetailResult>> objectRestResult = new RestResult<>();
        objectRestResult.setData(upsertAgreementDetailResults);
        return objectRestResult;
    }

    @ServiceMethod("fetch_signing_text")
    public RestResult<PartnerChannelManage.CustomText> fetchSigningText(ServiceContext serviceContext) {
        PartnerChannelManage.CustomText customText = prmChannelService.fetchSigningText(serviceContext.getUser());
        RestResult<PartnerChannelManage.CustomText> objectRestResult = new RestResult<>();
        objectRestResult.setData(customText);
        return objectRestResult;
    }

    @ServiceMethod("fetch_renewal_text")
    public RestResult<PartnerChannelManage.CustomText> fetchRenewalText(ServiceContext serviceContext) {
        PartnerChannelManage.CustomText customText = prmChannelService.fetchRenewalText(serviceContext.getUser());
        RestResult<PartnerChannelManage.CustomText> objectRestResult = new RestResult<>();
        objectRestResult.setData(customText);
        return objectRestResult;
    }

    @ServiceMethod("fetch_register_text")
    public RestResult<PartnerChannelManage.CustomText> fetchRegisterText(ServiceContext serviceContext) {
        PartnerChannelManage.CustomText customText = prmChannelService.fetchRegisterText(serviceContext.getUser());
        RestResult<PartnerChannelManage.CustomText> objectRestResult = new RestResult<>();
        objectRestResult.setData(customText);
        return objectRestResult;
    }

    @ServiceMethod("create_agreement_detail")
    public RestResult<Boolean> createAgreementDetail(ServiceContext context, ChannelRpcModel.CreateAgreementDetailArg arg) {
        Boolean success = prmChannelService.createAgreementDetail(context.getUser(), arg);
        return RestResult.newSuccess(success);
    }

    @ServiceMethod("verify_signed_through")
    public RestResult<Boolean> verifySignedThrough(ServiceContext context) {
        try {
            Boolean success = prmChannelService.isSignedThrough(context.getUser(), context.getAppId());
            return RestResult.newSuccess(success);
        } catch (Exception e) {
            log.error("verifySignedThrough error", e);
        }
        return RestResult.newSuccess(Boolean.TRUE);
    }

    @ServiceMethod("fetch_renew_pages")
    public RestResult<List<String>> fetchRenewPages(ServiceContext context) {
        List<String> pages = prmChannelService.fetchRenewPages(context.getUser());
        return RestResult.newSuccess(pages);
    }
}
