package com.facishare.crm.prm.service;

import com.facishare.crm.platform.converter.ObjectConverter;
import com.facishare.crm.prm.service.model.ChannelStageRecord;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.lto.utils.StringUtil;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-08
 * ============================================================
 */
@Service
@Slf4j
public class ActivationTaskDataProxy {
    private static final String APPROVAL_ACTIVATION_TASK_OBJ = "ApprovalActivationTaskObj";
    private static final String ASSOCIATION_OBJECT = "association_object";
    private static final String ASSOCIATION_DATA_ID = "association_data_id";
    private static final String ENTERPRISE_ID = "enterprise_id";

    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource(name = "objectConverter")
    private ObjectConverter converter;


    @NotNull
    public List<ChannelStageRecord> fetchStageRecords(User user, String associationObject, String associationDataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        return fetchStageRecords(user, associationObject, associationDataId, query);
    }

    @NotNull
    public List<ChannelStageRecord> fetchStageRecords(User user, String associationObject, String associationDataId, String outTenantId) {
        if (StringUtils.isAnyBlank(associationObject, associationDataId, outTenantId)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), ENTERPRISE_ID, outTenantId);
        return fetchStageRecords(user, associationObject, associationDataId, query);
    }

    private List<ChannelStageRecord> fetchStageRecords(User user, String associationObject, String associationDataId, SearchTemplateQuery query) {
        SearchUtil.fillFilterEq(query.getFilters(), ASSOCIATION_OBJECT, associationObject);
        SearchUtil.fillFilterEq(query.getFilters(), ASSOCIATION_DATA_ID, associationDataId);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, APPROVAL_ACTIVATION_TASK_OBJ,
                query, Lists.newArrayList("_id", "biz_scope", "enterprise_id", "task_progress"));
        return converter.convertInPutDataList(dataList, ChannelStageRecord.class);
    }
}
