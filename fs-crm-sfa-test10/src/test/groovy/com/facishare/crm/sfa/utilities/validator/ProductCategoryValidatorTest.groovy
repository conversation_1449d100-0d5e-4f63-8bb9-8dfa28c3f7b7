package com.facishare.crm.sfa.utilities.validator

import com.facishare.crm.platform.tree.TreeManager
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.model.CategoryImportValidateModel
import com.facishare.crm.sfa.model.ProductCategoryTree
import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt
import com.facishare.crm.sfa.predefine.service.ProductCategoryImportBizService
import com.facishare.crm.sfa.predefine.service.model.CategoryObject
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.action.BaseImportAction
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.fxiaoke.common.StopWatch
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.junit.runner.RunWith
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification
import com.facishare.crm.sfa.predefine.service.CategoryStaticUtilService

import java.util.concurrent.atomic.AtomicBoolean

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.*
/**
 * <AUTHOR>
 * @time 2024-05-28 20:06
 * @Description
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
class ProductCategoryValidatorTest extends RemoveUseless {

    ProductCategoryValidator productCategoryValidatorTest
    @Shared
    User user
    @Shared
    RequestContext requestContext
    @Shared
    ServiceContext serviceContext

    def setupSpec() {
        user = Mock(User) {
            getTenantId() >> "71568"
            getUserId() >> "-10000"
        }

        requestContext = Mock(RequestContext) {
            getUser() >> user
            getTenantId() >> "71568"
        }

        serviceContext = Mock(ServiceContext) {
            getRequestContext() >> requestContext
            getUser() >> user
            getTenantId() >> "71568"
        }
    }

    def setup() {
        productCategoryValidatorTest = Spy(ProductCategoryValidator)
    }

    def "buildCategoryTree"() {
        given:
        def queryResult = new QueryResult();
        def data = new ObjectData()
        queryResult.setData([data])

        def data1 = new ObjectData()
        data1.setId("1")
        data1.setName("1-name")
        data1.set(PID, "")
        data1.set(ORDER_FIELD, 1)

        def data2 = new ObjectData()
        data2.setId("2")
        data2.setName("2-name")
        data2.set(PID, "1")
        data2.set(ORDER_FIELD, 2)

        List<IObjectData> allProductCategory = [data1, data2]

        and:
        productCategoryValidatorTest.isInnerServiceRequest() >> false

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.rollbackCategoryReturn(_) >> true
        categoryStaticUtilService.getContext() >> requestContext
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.buildCategoryTree(allProductCategory)
        then:
        notThrown(Exception)
    }

    def "getCategoryTreeList v2"() {
        given:
        def queryResult = new QueryResult();
        def data = new ObjectData()
        queryResult.setData([data])


        def data1 = new ObjectData()
        data1.setId("1")
        data1.setName("1-name")
        data1.set(PID, "")
        data1.set(ORDER_FIELD, 1)

        def data2 = new ObjectData()
        data2.setId("2")
        data2.setName("2-name")
        data2.set(PID, "1")
        data2.set(ORDER_FIELD, 2)

        List<IObjectData> allProductCategory = [data1, data2]

        and:
        def categoryObject = new CategoryObject()
        productCategoryValidatorTest.buildCategoryTree(queryResult.getData()) >> categoryObject

        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.rollbackCategoryReturn(_) >> true
        categoryStaticUtilService.getContext() >> requestContext
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.getCategoryTreeList(allProductCategory)
        then:
        notThrown(Exception)
    }


    def "getAddErrorData"() {
        given:

        def d1 = new ObjectData()
        d1.setName("d1")
        d1.set("category_code", "d1")
        List<IObjectData> productCategoryDataFromDB = [d1]


        and:


        def id1 = new ObjectData()
        id1.setId("id1")
        id1.setName("id1")
        def i1 = new BaseImportDataAction.ImportData()
        i1.setData(id1)

        def id2 = new ObjectData()
        id2.setId("id1")
        id2.setName("id1")
        id2.set("parent_code", "222")
        def i2 = new BaseImportDataAction.ImportData()
        i2.setData(id1)

        def id3 = new ObjectData()
        id3.setId("id1")
        id3.setName("id1")
        id3.set("parent_code", "d1")
        def i3 = new BaseImportDataAction.ImportData()
        i3.setData(id1)
        List<BaseImportDataAction.ImportData> partnerNodeIsRelatedData = [i1, i2, i3]


        and:
        productCategoryValidatorTest.getCategoryCode(_) >> "d1"
        productCategoryValidatorTest.getDataI18nName(_) >> "id1"


        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_, _) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        def validateModel = Mock(CategoryImportValidateModel)
        validateModel.addErrorData(_ as List<BaseImportDataAction.ImportData>, "errorMessage") >> {}

        when:
        productCategoryValidatorTest.getAddErrorData(partnerNodeIsRelatedData,
                validateModel, productCategoryDataFromDB, "分类", "产品")

        then:
        notThrown(Exception)
    }

    def "getCategoryTreeList"() {
        given:
        def queryResult = new QueryResult();
        def data = new ObjectData()
        queryResult.setData([data])


        and:
        def categoryObject = new CategoryObject()
        productCategoryValidatorTest.buildCategoryTree(queryResult.getData()) >> categoryObject

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllSortedCategory(_, _, _, _) >> queryResult
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.getContext() >> requestContext
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.getCategoryTreeList(requestContext.getUser())
        then:
        notThrown(Exception)
    }


    def "checkAllDepths throw ex"() {
        given:
        def object = new CategoryObject();
        productCategoryValidatorTest.getCategoryTreeList(_) >> object
        productCategoryValidatorTest.getDepth(object, 0, _) >> {}

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        categoryStaticUtilService.getLevelDepthLimit() >> -1
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.checkAllDepths([])
        then:
        thrown(Exception)
    }

    def "checkAllDepths not throw ex"() {
        given:
        def object = new CategoryObject();
        productCategoryValidatorTest.getCategoryTreeList(_) >> object
        productCategoryValidatorTest.getDepth(object, 0, _) >> {}

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        categoryStaticUtilService.getContext() >> requestContext
        categoryStaticUtilService.getLevelDepthLimit() >> 100
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)


        when:
        productCategoryValidatorTest.checkAllDepths([])
        then:
        notThrown(Exception)
    }

    def "UpdateImportValidate updateDataFromStore is null"() {
        given:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getErrorList(_, _) >> returnList
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def metaDataFindServiceExtMock = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExtMock.findObjectByIdsWithFieldsIgnoreAll(requestContext.getUser(), _ as List, SFAPreDefineObject.ProductCategory.getApiName(), Lists.newArrayList(_ID, NAME, PID)) >> findDataList
        productCategoryValidatorTest.setMetaDataFindServiceExt(metaDataFindServiceExtMock)
        when:
        productCategoryValidatorTest.updateImportValidate(requestContext.getUser(), [], new AtomicBoolean(false))
        then:
        notThrown(Exception)
        where:
        dataList                                | findDataList       | returnList
        [new BaseImportDataAction.ImportData()] | [new ObjectData()] | [new BaseImportAction.ImportError()]
        [new BaseImportDataAction.ImportData()] | []                 | []
    }

    def "UpdateImportValidate updateDataFromStore name a"() {
        given:
        def importData1 = new BaseImportDataAction.ImportData()
        def data1 = new ObjectData()
        data1.setName("data1")
        data1.setId("1")
        data1.set(PID, "")
        data1.set(ORDER_FIELD, 1)
        importData1.setData(data1)
        importData1.setRowNo(2)
        def importData2 = new BaseImportDataAction.ImportData()
        def data2 = new ObjectData()
        data2.setName("data2")
        data2.setId("2")
        data2.set(PID, "1")
        data2.set(ORDER_FIELD, 2)
        importData2.setData(data2)
        importData2.setRowNo(3)
        def dataList = [importData1, importData2]

        and:
        def data22 = new ObjectData()
        data22.setName("data3")
        data22.setId("2")
        data22.set(PID, "1")
        data22.set(ORDER_FIELD, 2)
        def findDataList = [data1, data22]

        and:
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)

        metaDataFindServiceExt.findObjectByIdsWithFieldsIgnoreAll(requestContext.getUser(), _ as List, SFAPreDefineObject.ProductCategory.getApiName(), Lists.newArrayList(_ID, NAME, PID)) >> findDataList

        metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(requestContext.getUser(), SFAPreDefineObject.ProductCategory.getApiName(), _ as SearchTemplateQuery, Lists.newArrayList(_ID, NAME, PID)) >> Lists.newArrayList(data1)
        productCategoryValidatorTest.setMetaDataFindServiceExt(metaDataFindServiceExt)
        and:

        productCategoryValidatorTest.findSameLayerCategoryData(requestContext.getUser(), _ as Set<String>, Lists.newArrayList(_ID, NAME, PID))
        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getErrorList(_, _) >> []
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryValidatorTest.updateImportValidate(requestContext.getUser(), dataList, new AtomicBoolean(false))
        then:
        notThrown(Exception)
    }

    def "UpdateImportValidate updateDataFromStore"() {
        given:
        def importData1 = new BaseImportDataAction.ImportData()
        def data1 = new ObjectData()
        data1.setName("data1")
        data1.setId("1")
        data1.set(PID, "")
        data1.set(ORDER_FIELD, 1)
        importData1.setData(data1)
        importData1.setRowNo(2)
        def importData2 = new BaseImportDataAction.ImportData()
        def data2 = new ObjectData()
//        data2.setName("data2")
        data2.setId("2")
        data2.set(PID, "1")
        data2.set(ORDER_FIELD, 2)
        importData2.setData(data2)
        importData2.setRowNo(3)
        def dataList = [importData1, importData2]

        and:
        def data22 = new ObjectData()
        data22.setName("data2")
        data22.setId("2")
        data22.set(PID, "1")
        data22.set(ORDER_FIELD, 2)
        def findDataList = [data1, data22]

        and:
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)

        metaDataFindServiceExt.findObjectByIdsWithFieldsIgnoreAll(requestContext.getUser(), _ as List, SFAPreDefineObject.ProductCategory.getApiName(), Lists.newArrayList(_ID, NAME, PID)) >> findDataList

        metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(requestContext.getUser(), SFAPreDefineObject.ProductCategory.getApiName(), _ as SearchTemplateQuery, Lists.newArrayList(_ID, NAME, PID)) >> Lists.newArrayList(data1)
        productCategoryValidatorTest.setMetaDataFindServiceExt(metaDataFindServiceExt)
        and:

        productCategoryValidatorTest.findSameLayerCategoryData(requestContext.getUser(), _ as Set<String>, Lists.newArrayList(_ID, NAME, PID))
        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getErrorList(_, _) >> []
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryValidatorTest.updateImportValidate(requestContext.getUser(), dataList, new AtomicBoolean(false))
        then:
        notThrown(Exception)
    }

    def "UpdateImportValidate updateDataFromStore neq size"() {
        given:
        def importData1 = new BaseImportDataAction.ImportData()
        def data1 = new ObjectData()
        data1.setName("data1")
        data1.setId("1")
        data1.set(PID, "")
        data1.set(ORDER_FIELD, 1)
        importData1.setData(data1)
        importData1.setRowNo(2)
        def importData2 = new BaseImportDataAction.ImportData()
        def data2 = new ObjectData()
        data2.setName("data2")
        data2.setId("2")
        data2.set(PID, "1")
        data2.set(ORDER_FIELD, 2)
        importData2.setData(data2)
        importData2.setRowNo(3)
        def dataList = [importData1, importData2]

        and:
        def findDataList = [new ObjectData()]
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExt.findObjectByIdsWithFieldsIgnoreAll(requestContext.getUser(), _ as List, SFAPreDefineObject.ProductCategory.getApiName(), Lists.newArrayList(_ID, NAME, PID)) >> findDataList
        productCategoryValidatorTest.setMetaDataFindServiceExt(metaDataFindServiceExt)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getErrorList(_, _) >> []
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryValidatorTest.updateImportValidate(requestContext.getUser(), dataList, new AtomicBoolean(false))
        then:
        notThrown(Exception)
    }

    def "checkLevelAboutBulk test"() {
        given:
        def root = new ProductCategoryTree()
        Map<ProductCategoryTree, Integer> mapResult = new HashMap<>()

        mapResult.put(root, 30)
        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getDepthMap(_) >> mapResult
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.checkLevelAboutBulk(root)
        then:
        thrown(ValidateException.class)
    }

    def "test checkPartnerCategoryId pid is null"() {
        when:
        productCategoryValidatorTest.checkPartnerCategoryId([], "")
        then:
        notThrown(Exception.class)
    }

    def "test checkPartnerCategoryId pid is not null and allCategory not exists dataId == pid"() {
        given:
        def data = new ObjectData()
        data.setId("2")
        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)


        when:
        productCategoryValidatorTest.checkPartnerCategoryId([data], "1")
        then:
        thrown(ValidateException.class)
    }

    def "test checkPartnerCategoryId pid is not null and allCategory exists dataId == pid"() {
        given:
        def data = new ObjectData()
        data.setId("1")
        when:
        productCategoryValidatorTest.checkPartnerCategoryId([data], "1")
        then:
        notThrown(ValidateException.class)
    }


    def "test getCategoryTreeList2 "() {
        given:
        def queryResult = new QueryResult();
        def data = new ObjectData()
        data.set("name__r", "name__r")
        data.set("category_code__r", "category_code__r")
        queryResult.setData([data])

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.rollbackCategoryReturn(_) >> false
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.buildCategory("testTenantId", true, data)
        then:
        notThrown(Exception.class)
    }


    def "test checkCodeUnique categoryCode is not null and id is null"() {
        given:
        def map = Maps.newHashMap()
        map.put("category_code", "11")
        def document = ObjectDataDocument.of(map as Map<String, Object>)
        def map2 = Maps.newHashMap()
        map2.put("category_code", "11")
        def objectData = ObjectDataExt.of(map2 as Map<String, Object>)
        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.checkCodeUnique([objectData], document)
        then:
        thrown(ValidateException)
    }

    def "test insertImportValidate propertyErrorData is not null"() {
        given:
        def validatingData = []
        and:
        def queryResult = new QueryResult<IObjectData>()
        queryResult.setData([])

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllCategoryWithFields(_, _) >> queryResult
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)


        and:
        def categoryGlobalVariable = Mock(CategoryImportValidateModel.CategoryGlobalVariable)
        categoryGlobalVariable.getProductCategoryDataFromDB() >> []


        and:
        def validateModel = Mock(CategoryImportValidateModel)
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        categoryStaticUtilService.getCategoryImportValidateModel(_, _) >> validateModel
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryImportBizService = Mock(ProductCategoryImportBizService)
        productCategoryImportBizService.getPropertyErrorData(_, _) >> [new BaseImportAction.ImportError()]
        productCategoryValidatorTest.setProductCategoryImportBizService(productCategoryImportBizService)

        when:
        productCategoryValidatorTest.insertImportValidate(requestContext.getUser(), validatingData, categoryGlobalVariable, Mock(StopWatch))
        then:
        notThrown(Exception)
    }

    def "test insertImportValidate propertyErrorData is null"() {
        given:
        def queryResult = new QueryResult<IObjectData>()
        queryResult.setData([])

        and:
        def categoryGlobalVariable = Mock(CategoryImportValidateModel.CategoryGlobalVariable)
        categoryGlobalVariable.getProductCategoryDataFromDB() >> []

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllCategoryWithFields(_, _) >> queryResult
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def validateModel = Mock(CategoryImportValidateModel)
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        categoryStaticUtilService.getLevelDepthLimit() >> 0
        categoryStaticUtilService.getCategoryImportValidateModel(_, _) >> validateModel
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)


        and:
        def treeManager = TreeManager.builder().build()
        and:
        def productCategoryImportBizService = Mock(ProductCategoryImportBizService)
        productCategoryImportBizService.getPropertyErrorData(_, _) >> []
        productCategoryImportBizService.getPartnerCodeNotInDB(_, _) >> []
        productCategoryImportBizService.getDuplicateNameBrotherNode(_, _, _) >> []
        productCategoryImportBizService.buildCategoryTreeManager(_, _, _) >> treeManager
        productCategoryImportBizService.getBeyondSpecifiedDepthData(_, _, _) >> []
        productCategoryImportBizService.findAllExceptionChildrenData(_, _) >> []
        productCategoryValidatorTest.setProductCategoryImportBizService(productCategoryImportBizService)

        and:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(user, "ProductCategoryObj") >> null
        productCategoryValidatorTest.setDescribeEnhancer(describeEnhancer)

        when:
        productCategoryValidatorTest.insertImportValidate(requestContext.getUser(), [], categoryGlobalVariable, Mock(StopWatch))
        then:
        notThrown(Exception)
    }

    def "test checkNameUnique name is not null"() {
        given:
        def map = Maps.newHashMap()
        map.put("category_code", "11")
        map.put("pid", "11")
        map.put("name", "11")
        def document = ObjectDataDocument.of(map as Map<String, Object>)
        def map2 = Maps.newHashMap()
        map2.put("category_code", "11")
        map2.put("pid", "11")
        map2.put("name", "11")
        def objectData = ObjectDataExt.of(map2 as Map<String, Object>)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.checkNameUnique(document, [objectData])
        then:
        thrown(ValidateException)
    }

    def "test checkBulkAddLimit "() {
        given:
        def dataList = []
        for (i in 0..<1100) {
            dataList.add(new ObjectData())
        }
        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)


        when:
        productCategoryValidatorTest.checkBulkAddLimit(dataList)
        then:
        thrown(ValidateException)
    }

    def "test checkImportCategoryLimit "() {
        given:
        def queryResult = new QueryResult<IObjectData>()
        queryResult.setData([])
        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllCategoryWithFields(_, _) >> queryResult
        productCategoryUtils.isCloseOldProductCategory(_) >> true
        productCategoryUtils.getTenantCategoryLimit(_) >> 1L
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def globalVariable = Mock(CategoryImportValidateModel.CategoryGlobalVariable)
        globalVariable.getProductCategoryDataFromDB() >> []

        when:
        productCategoryValidatorTest.checkImportCategoryLimit(requestContext.getUser(), [], globalVariable)
        then:
        notThrown(Exception)
    }

    def "test checkRootName rootNameSet.size != categoryList.size"() {
        given:
        Set<String> rootNameSet = ["1", "2"]
        List<ProductCategoryTree> categoryList = [new ProductCategoryTree()]
        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)


        when:
        productCategoryValidatorTest.checkRootName(serviceContext, rootNameSet, categoryList)
        then:
        thrown(ValidateException)
    }

    def "test checkRootName rootNameSet.size == categoryList.size"() {
        given:
        Set<String> rootNameSet = ["1"]
        List<ProductCategoryTree> categoryList = [new ProductCategoryTree()]

        def queryResult = new QueryResult()
        queryResult.setData([new ObjectData()])

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findRootByName(_, _) >> queryResult
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.checkRootName(serviceContext, rootNameSet, categoryList)
        then:
        thrown(ValidateException)
    }

    def "test checkNameUnique name is null"() {
        given:
        def map = Maps.newHashMap()
        map.put("category_code", "11")
        def document = ObjectDataDocument.of(map as Map<String, Object>)
        def map2 = Maps.newHashMap()
        map2.put("category_code", "11")
        def objectData = ObjectDataExt.of(map2 as Map<String, Object>)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.checkNameUnique(document, [objectData])
        then:
        thrown(ValidateException)
    }

    def "test checkDepth "() {
        given:
        def map = Maps.newHashMap()
        map.put("category_code", "1")
        map.put("pid", "3")
        map.put("_id", "1")
        def document = ObjectDataDocument.of(map as Map<String, Object>)
        def map2 = Maps.newHashMap()
        map2.put("category_code", "2")
        map2.put("pid", "")
        map2.put("_id", "2")
        def objectData = ObjectDataExt.of(map2 as Map<String, Object>)

        def map3 = Maps.newHashMap()
        map3.put("category_code", "3")
        map3.put("pid", "2")
        map3.put("_id", "3")
        def objectData2 = ObjectDataExt.of(map3 as Map<String, Object>)


        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        categoryStaticUtilService.getLevelDepthLimit() >> 0
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.checkDepth(document, [objectData, objectData2])
        then:
        notThrown(Exception)
    }

    def "test checkCodeUnique categoryCode is null"() {
        given:
        def map = Maps.newHashMap()
        def document = ObjectDataDocument.of(map as Map<String, Object>)
        def map2 = Maps.newHashMap()
        def objectData = ObjectDataExt.of(map2 as Map<String, Object>)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.checkCodeUnique([objectData], document)
        then:
        thrown(ValidateException)
    }

    def "test getRootCategoryData"() {
        given:
        def queryResult = new QueryResult<IObjectData>()
        queryResult.setData([])
        and:
        def metaDataFindServiceExtMock = Mock(MetaDataFindServiceExt)
        def test = productCategoryValidatorTest = new ProductCategoryValidator(metaDataFindServiceExt: metaDataFindServiceExtMock)
        metaDataFindServiceExtMock.findBySearchQueryWithFields(_ as User, SFAPreDefineObject.ProductCategory.getApiName(), _ as SearchTemplateQuery, Lists.newArrayList(_ID, NAME, PID), true) >> queryResult

        when:
        test.getRootCategoryData(requestContext.getUser())
        then:
        notThrown(ValidateException)
    }

    def "test checkCodeUnique categoryCode is not null and id is not null"() {
        given:
        def map = Maps.newHashMap()
        map.put("category_code", "11")
        map.put("_id", "11")

        def document = ObjectDataDocument.of(map as Map<String, Object>)
        def objectData = new ObjectData();
        objectData.setId("112")
        objectData.set("category_code", "11")

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryValidatorTest.checkCodeUnique([objectData], document)
        then:
        thrown(ValidateException)
    }

    def "UpdateImportValidate not continueCheck"() {
        given:
        def importData = new BaseImportDataAction.ImportData();
        def data = new ObjectData();
        data.setName("name")
        data.setId("123")
        importData.setData(data)

        def data2 = new ObjectData();
        data2.setName("name1")
        data2.setId("12312")

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getErrorList(_, _) >> []
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def metaDataFindServiceExtMock = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExtMock.findObjectByIdsWithFieldsIgnoreAll(requestContext.getUser(), _ as List, SFAPreDefineObject.ProductCategory.getApiName(), Lists.newArrayList(_ID, NAME, PID)) >> [data, data2]
        productCategoryValidatorTest.setMetaDataFindServiceExt(metaDataFindServiceExtMock)
        when:
        productCategoryValidatorTest.updateImportValidate(requestContext.getUser(), [importData], new AtomicBoolean(false))
        then:
        notThrown(Exception)
    }

    def "updateImportValidate"() {
        given:

        def data = new ObjectData();
        data.setId("123")

        def data2 = new ObjectData();
        data2.setName("name")
        data2.setId("12312")

        def data3 = new ObjectData();
        data3.setId("123123")
        data3.setName("sss")

        def data4 = new ObjectData();
        data4.setId("123123-1")
        data4.setName("sss")

        def importData = new BaseImportDataAction.ImportData();
        importData.setData(data)
        def importData2 = new BaseImportDataAction.ImportData();
        importData2.setData(data2)
        def importData3 = new BaseImportDataAction.ImportData();
        importData3.setData(data3)
        def importData4 = new BaseImportDataAction.ImportData();
        importData4.setData(data4)

        def importDataList = [importData, importData2, importData3, importData4]


        def db = new ObjectData();
        db.setName("name")
        db.setId("123")


        def db2 = new ObjectData();
        db2.setName("name1231")
        db2.setId("12312")

        def db3 = new ObjectData();
        db3.setName("name123123")
        db3.setId("123123")
        db3.set("pid", "12312")

        def db4 = new ObjectData();
        db4.setName("name123123-1")
        db4.setId("123123-1")
        db4.set("pid", "12312")


        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_) >> ""
        productCategoryValidatorTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.getErrorList(_, _) >> []
        productCategoryValidatorTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def queryResult = new QueryResult();
        queryResult.setData([db])
        def metaDataFindServiceExtMock = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExtMock.findObjectByIdsWithFieldsIgnoreAll(requestContext.getUser(), _ as List, SFAPreDefineObject.ProductCategory.getApiName(), Lists.newArrayList(_ID, NAME, PID)) >> [db, db2, db3, db4]
        metaDataFindServiceExtMock.findBySearchQueryWithFields(requestContext.getUser(), SFAPreDefineObject.ProductCategory.getApiName(), _ as SearchTemplateQuery, Lists.newArrayList(_ID, NAME, PID), true) >> queryResult

        metaDataFindServiceExtMock.findBySearchQueryWithFieldsIgnoreAll(requestContext.getUser(), SFAPreDefineObject.ProductCategory.getApiName(), _ as SearchTemplateQuery, _ as List) >> [db2];
        productCategoryValidatorTest.setMetaDataFindServiceExt(metaDataFindServiceExtMock)


        expect:
        productCategoryValidatorTest.updateImportValidate(requestContext.getUser(), importDataList, new AtomicBoolean(false)).size() == 0
    }
}
