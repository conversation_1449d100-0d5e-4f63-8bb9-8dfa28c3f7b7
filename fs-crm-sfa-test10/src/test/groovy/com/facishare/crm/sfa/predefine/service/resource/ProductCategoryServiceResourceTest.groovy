package com.facishare.crm.sfa.predefine.service.resource

import com.alibaba.fastjson.JSON
import com.facishare.crm.platform.async.executor.AsyncBootstrap
import com.facishare.crm.sfa.RemoveUseless
import com.facishare.crm.sfa.model.ProductCategoryTree
import com.facishare.crm.sfa.predefine.action.ProductCategoryInsertImportDataAction
import com.facishare.crm.sfa.predefine.enums.CategoryFilterEnum
import com.facishare.crm.sfa.predefine.service.*
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService
import com.facishare.crm.sfa.predefine.service.model.CategoryObject
import com.facishare.crm.sfa.predefine.service.model.CheckDeleteCategoryModel
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer
import com.facishare.crm.sfa.utilities.proxy.CRMMetaDataServiceProxy
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator
import com.facishare.crm.sfa.utilities.validator.ProductCategoryValidator
import com.facishare.paas.appframework.common.util.Tuple
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction
import com.facishare.paas.appframework.metadata.ProductCategoryServiceImpl
import com.facishare.paas.appframework.metadata.dto.ProductAllCategoriesModel
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.apache.commons.collections.CollectionUtils
import org.junit.runner.RunWith
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification

import java.util.concurrent.CompletableFuture
import java.util.concurrent.ThreadPoolExecutor

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.*
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_CHECK_CATEGORY_DELETE
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.TENANT_NOT_GRAY_THIS_ABILITY

/**
 * <AUTHOR>
 *
 * @time 2024-04-01 17:16
 * @Description
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik)
class ProductCategoryServiceResourceTest extends RemoveUseless {
    ProductCategoryServiceResource productCategoryServiceResourceTest
    @Shared
    User user
    @Shared
    RequestContext requestContext
    @Shared
    ServiceContext serviceContext

    def setup() {
        productCategoryServiceResourceTest = Spy(ProductCategoryServiceResource)
    }

    def setupSpec() {
        user = Mock(User) {
            getTenantId() >> "71568"
            getUserId() >> "-10000"
        }

        requestContext = Mock(RequestContext) {
            getUser() >> user
            getTenantId() >> "71568"
            getPeerName() >> ""
            getAppId() >> ""
        }

        serviceContext = Mock(ServiceContext) {
            getRequestContext() >> requestContext
            getUser() >> user
            getTenantId() >> "71568"
            getPeerName() >> ""
            getAppId() >> ""
        }
    }

    def "initPool"() {
        given:
        when:
        productCategoryServiceResourceTest.initPool()
        then:
        notThrown(Exception)
    }

    def "add"() {
        given:
        def result = Mock(BaseObjectSaveAction.Result)
        ServiceFacadeProxy serviceFacadeProxy = Mock(ServiceFacadeProxy)
        serviceFacadeProxy.triggerAction(_ as ActionContext, _ as BaseObjectSaveAction.Arg, _ as Class<BaseObjectSaveAction.Result>) >> result
        productCategoryServiceResourceTest.setServiceFacadeProxy(serviceFacadeProxy)

        and:
        productCategoryServiceResourceTest.getDocument(result) >> Mock(ObjectDataDocument)
        when:
        productCategoryServiceResourceTest.add(serviceContext, Mock(ObjectDataDocument))
        then:
        notThrown(Exception)
    }

    def "filter_by_price_book isNullOrEmpty(arg.getAccountId()"() {
        given:
        def arg = new GetCategoryByPriceBookArg()

        and:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.supportFilterCategory(_ as String) >> true
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isAvailableRangeEnabled(_ as String) >> true
        productCategoryServiceResourceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)

        when:
        productCategoryServiceResourceTest.filterByPriceBook(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "filter_by_price_book shop"() {
        given:
        def arg = new GetCategoryByPriceBookArg()
        arg.setAccountId("accountId")
        arg.setPriceBookId("priceBookId")
        arg.setAvailableRangeId("availableRangeId")
        arg.setDetailDataList(Maps.newHashMap())
        arg.setFilterByShopCategory(true)

        and:
        QueryResult<IObjectData> queryResult = Mock(QueryResult)
        def data1 = new ObjectData()
        queryResult.getData() >> Lists.newArrayList(data1)

        and:
        def dhtPriceBookService = Mock(DhtPriceBookService)
        dhtPriceBookService.detailDataDocument2detailData(_ as HashMap<String, List<ObjectDataDocument>>, _ as Map<String, List<IObjectData>>) >> {}
        Tuple<Boolean, Set<String>> tuple = Tuple.of(true, [] as Set<String>)
        dhtPriceBookService.matchAvailableProduct(serviceContext,
                "accountId", "priceBookId", "availableRangeId", null, arg.getDetailDataList()) >> tuple

        productCategoryServiceResourceTest.setDhtPriceBookService(dhtPriceBookService)

        and:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.supportFilterCategory(_ as String) >> true
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllSortedCategory(requestContext.getUser(),
                _ as List<String>,
                false,
                CategoryFilterEnum.SHOP) >> queryResult
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)


        and:
        // 正确创建 mock
        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isAvailableRangeEnabled(requestContext.getTenantId()) >> true
        productCategoryServiceResourceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)

        and:
        // 正确创建 mock
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.getCategoryCodeSQL(requestContext.getUser(),
                _ as Set<String>, arg.getPriceBookId(), _ as Boolean, _ as boolean) >> "sql"
        productCategoryBizService.handleCategoryForCPQ(serviceContext,
                _ as List<IObjectData>,
                _ as CategoryObject,
                _ as String,
                _ as Boolean) >> {}

        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)

        and:
        def categoryObject = new CategoryObject()

        // 正确创建 mock
        def productCategoryValidator = Mock(ProductCategoryValidator)
        productCategoryValidator.buildCategoryTree(_ as List<IObjectData>) >> categoryObject
        productCategoryValidator.getCategoryTreeList(requestContext.getUser()) >> categoryObject
        productCategoryServiceResourceTest.setProductCategoryValidator(productCategoryValidator)

        when:
        productCategoryServiceResourceTest.filterByPriceBook(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "filter_by_price_book not shop"() {
        given:
        def arg = new GetCategoryByPriceBookArg()
        arg.setAccountId("accountId")
        arg.setPriceBookId("priceBookId")
        arg.setAvailableRangeId("availableRangeId")
        arg.setDetailDataList(Maps.newHashMap())
        arg.setFilterByShopCategory(false)

        and:
        def dhtPriceBookService = Mock(DhtPriceBookService)
        dhtPriceBookService.detailDataDocument2detailData(_ as HashMap<String, List<ObjectDataDocument>>, _ as Map<String, List<IObjectData>>) >> {}
        Tuple<Boolean, Set<String>> tuple = Tuple.of(true, [] as Set<String>)
        dhtPriceBookService.matchAvailableProduct(serviceContext, "accountId", "priceBookId", "availableRangeId", null, arg.getDetailDataList()) >> tuple

        productCategoryServiceResourceTest.setDhtPriceBookService(dhtPriceBookService)

        and:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.supportFilterCategory(requestContext.getTenantId()) >> true
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        // 正确创建 mock
        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isAvailableRangeEnabled(requestContext.getTenantId()) >> true
        productCategoryServiceResourceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)

        and:
        // 正确创建 mock
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.getCategoryCodeSQL(requestContext.getUser(),
                _ as Set<String>, arg.getPriceBookId(), _ as Boolean, _ as boolean) >> "sql"
        productCategoryBizService.handleCategoryForCPQ(serviceContext, _ as List<IObjectData>, _ as CategoryObject, _ as String, _ as Boolean) >> {}

        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)

        and:
        def categoryObject = new CategoryObject()
        // 正确创建 mock
        def productCategoryValidator = Mock(ProductCategoryValidator)
        productCategoryValidator.buildCategoryTree(_ as List<IObjectData>) >> categoryObject
        productCategoryValidator.getCategoryTreeList(requestContext.getUser()) >> categoryObject
        productCategoryServiceResourceTest.setProductCategoryValidator(productCategoryValidator)

        when:
        productCategoryServiceResourceTest.filterByPriceBook(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "filter_by_price_book sql is null"() {
        given:
        def arg = new GetCategoryByPriceBookArg()
        arg.setAccountId("accountId")
        arg.setPriceBookId("priceBookId")
        arg.setAvailableRangeId("availableRangeId")
        arg.setDetailDataList(Maps.newHashMap())

        and:
        def dhtPriceBookService = Mock(DhtPriceBookService)
        dhtPriceBookService.detailDataDocument2detailData(_ as HashMap<String, List<ObjectDataDocument>>, _ as Map<String, List<IObjectData>>) >> {}
        Tuple<Boolean, Set<String>> tuple = Tuple.of(true, [] as Set<String>)
        dhtPriceBookService.matchAvailableProduct(serviceContext,
                "accountId", "priceBookId", "availableRangeId", null, arg.getDetailDataList()) >> tuple

        productCategoryServiceResourceTest.setDhtPriceBookService(dhtPriceBookService)

        and:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.supportFilterCategory(requestContext.getTenantId()) >> true
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        // 正确创建 mock
        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isAvailableRangeEnabled(requestContext.getTenantId()) >> true
        productCategoryServiceResourceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)

        and:
        // 正确创建 mock
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.getCategoryCodeSQL(requestContext.getUser(),
                _ as Set<String>, arg.getPriceBookId(), _ as Boolean, _ as boolean) >> null
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)

        when:
        productCategoryServiceResourceTest.filterByPriceBook(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "filter_by_price_book availableTuple false"() {
        given:
        def arg = new GetCategoryByPriceBookArg()
        arg.setAccountId("accountId")
        arg.setPriceBookId("priceBookId")
        arg.setAvailableRangeId("availableRangeId")
        arg.setDetailDataList(Maps.newHashMap())

        and:
        def dhtPriceBookService = Mock(DhtPriceBookService)
        dhtPriceBookService.detailDataDocument2detailData(_ as HashMap<String, List<ObjectDataDocument>>, _ as Map<String, List<IObjectData>>) >> {}
        Tuple<Boolean, Set<String>> tuple = Tuple.of(false, [] as Set<String>)
        dhtPriceBookService.matchAvailableProduct(serviceContext,
                "accountId", "priceBookId", "availableRangeId", null, arg.getDetailDataList()) >> tuple

        productCategoryServiceResourceTest.setDhtPriceBookService(dhtPriceBookService)

        and:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.supportFilterCategory(requestContext.getTenantId()) >> true
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        // 正确创建 mock
        def bizConfigThreadLocalCacheService = Mock(BizConfigThreadLocalCacheService)
        bizConfigThreadLocalCacheService.isAvailableRangeEnabled(requestContext.getTenantId()) >> true
        productCategoryServiceResourceTest.setBizConfigThreadLocalCacheService(bizConfigThreadLocalCacheService)


        when:
        productCategoryServiceResourceTest.filterByPriceBook(serviceContext, arg)
        then:
        notThrown(Exception)
    }


    def "get_tree4function null"() {
        given:
        ChildCategoryArg arg = new ChildCategoryArg()

        and:
        // 正确创建 mock
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.getCategoryTree(requestContext.getUser(), arg) >> null

        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)


        when:
        productCategoryServiceResourceTest.getTree4Function(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "get_tree4function not null"() {
        given:
        ChildCategoryArg arg = new ChildCategoryArg()

        and:
        // 正确创建 mock
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.getCategoryTree(requestContext.getUser(), arg) >> new ProductCategoryTree()

        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)


        when:
        productCategoryServiceResourceTest.getTree4Function(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "updateOldCategory not any.isPresent()"() {
        given:
        def data = new ObjectData()
        data.setId("12")
        data.setName("1")
        data.set("category_code", "1")
        data.set("pid", "2")
        def mockDoc = Mock(ObjectDataDocument)
        mockDoc.toObjectData() >> data
        mockDoc.getId() >> "12"

        and:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(user, "ProductCategoryObj") >> null
        productCategoryServiceResourceTest.setDescribeEnhancer(describeEnhancer)

        and:
        def asyncBootstrap = Mock(AsyncBootstrap)
        asyncBootstrap.composed(user, _ as long, _ as CompletableFuture<?>) >> ""
        productCategoryServiceResourceTest.setAsyncBootstrap(asyncBootstrap)

        and:
        def pool = Mock(ThreadPoolExecutor)
        productCategoryServiceResourceTest.setExecutorPool(pool)

        // 正确创建 mock
        def productCategoryValidator = Mock(ProductCategoryValidator)

        // 使用 Spock 风格设置 mock 行为
        productCategoryValidator.checkPartnerCategoryId([], "pid") >> {}
        productCategoryValidator.checkNameUnique(mockDoc, []) >> {}
        productCategoryValidator.checkDepth(mockDoc, []) >> {}
        productCategoryValidator.checkAllDepths([]) >> {}
        productCategoryValidator.checkCodeUnique([], mockDoc) >> {}
        productCategoryServiceResourceTest.setProductCategoryValidator(productCategoryValidator)

        and:
        // 正确创建 mock
        def productCategoryV2Validator = Mock(ProductCategoryV2Validator)
        productCategoryV2Validator.checkCategoryTotalLimit(requestContext.getUser(), _ as long) >> {}
        productCategoryV2Validator.checkCategoryIsRelated(requestContext.getUser(), "pid", null) >> {}
        productCategoryServiceResourceTest.setProductCategoryV2Validator(productCategoryV2Validator)

        and:
        def result = new QueryResult<>()
        def data1 = new ObjectData()
        data1.setId("1")
        data1.setName("1")
        data1.set("category_code", "1")
        data1.set("order_field", 1)
        data1.set("pid", "2")
        result.setData([data1])
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllCategory(serviceContext.getUser()) >> result
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        ServiceFacadeProxy serviceFacadeProxy = Mock(ServiceFacadeProxy)
        serviceFacadeProxy.updateObjectData(serviceContext.getUser(), mockDoc.toObjectData()) >> {}
        productCategoryServiceResourceTest.setServiceFacadeProxy(serviceFacadeProxy)

        and:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.bulkUpdateOrderField(serviceContext.getUser(), _ as String, _, _ as String) >> {}
        productCategoryBizService.sendSynchronizeDescribeMqByGray(serviceContext.getUser(), "update") >> {}
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)

        and:
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        def data2 = new ObjectData()
        data2.set("order_field", 6)
        metaDataFindServiceExt.findObjectData(user, _ as String, "ProductCategoryObj") >> data2
        productCategoryServiceResourceTest.setMetaDataFindServiceExt(metaDataFindServiceExt)

        and:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(_ as String) >> ""
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        productCategoryServiceResourceTest.savaCategoryCheckAsync(serviceContext.getUser(),
                "Update", mockDoc, _ as List<IObjectData>) >> {}

        when:
        productCategoryServiceResourceTest.updateOldCategory(serviceContext, mockDoc)
        then:
        notThrown(Exception)
    }


    def "updateOldCategory any.isPresent()"() {
        given:
        def data = new ObjectData()
        data.setId("1")
        data.setName("1")
        data.set(CATEGORY_CODE, "1")
        data.set(ORDER_FIELD, 1)
        data.set(PID, "1")

        def dataDocument = Mock(ObjectDataDocument)
        dataDocument.toObjectData() >> data
        dataDocument.getId() >> "1"

        and:
        def result = new QueryResult<>()
        def data1 = new ObjectData()
        data1.setId("1")
        data1.setName("1")
        data1.set(CATEGORY_CODE, "1")
        data1.set(ORDER_FIELD, 1)
        data1.set(PID, "1")
        result.setData([data1])
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllCategory(serviceContext.getUser()) >> result
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryServiceResourceTest.updateOldCategory(serviceContext, dataDocument)
        then:
        notThrown(Exception)
    }


    def "getCategoryForCPQ"() {
        given:

        def categoryFilter = CategoryFilterEnum.PRODUCT

        and:
        def result = Mock(QueryResult)
        def data1 = new ObjectData()
        result.getData() >> [data1]
        result.setData([data1])

        and:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.getUsedShopCategoryCode(requestContext.getTenantId()) >> "getUsedShopCategoryCode"
        categoryStaticUtilService.getUsedCategoryCode(requestContext.getTenantId()) >> "getUsedCategoryCode"
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllSortedCategory(requestContext.getUser(),
                [], false, categoryFilter) >> result
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)


        and:
        def productCategoryValidator = Mock(ProductCategoryValidator)
        // 使用 Spock 风格设置 mock 行为
        def object = new CategoryObject()
        productCategoryValidator.buildCategoryTree(result.getData()) >> object
        productCategoryServiceResourceTest.setProductCategoryValidator(productCategoryValidator)

        and:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.handleCategoryForCPQ(requestContext as ServiceContext, result.getData(),
                object, _ as String, _ as Boolean) >> {}
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)

        when:
        productCategoryServiceResourceTest.getCategoryForCPQ(serviceContext, categoryFilter, filterByShopCategory)

        then:
        notThrown(Exception)

        where:
        filterByShopCategory || _
        true                 || _
        false                || _
    }

    def "list_children arg is null"() {
        given:

        when:
        productCategoryServiceResourceTest.listChildren(serviceContext, null)
        then:
        notThrown(Exception)
    }

    def "list_children arg filter == true"() {
        given:

        def arg = new ListChildrenArg()
        arg.setFilterByShopCategory(true)

        and:
        productCategoryServiceResourceTest.queryShopCategoryChildMapping(requestContext.getUser(), arg) >> Maps.newHashMap()

        when:
        productCategoryServiceResourceTest.listChildren(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "list_children arg filter == false"() {
        given:

        def arg = new ListChildrenArg()
        arg.setFilterByShopCategory(false)

        and:
        productCategoryServiceResourceTest.queryProductCategoryMapping(requestContext.getUser(), arg) >> Maps.newHashMap()

        when:
        productCategoryServiceResourceTest.listChildren(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "queryProductCategoryMapping arg not null"() {
        given:

        def arg = new ListChildrenArg()
        arg.setFilterByShopCategory(false)
        arg.setCodes(["1"])

        and:
        def categoryService = Mock(ProductCategoryServiceImpl)
        categoryService.getProductAllCategories(requestContext.getTenantId(), requestContext.getUser().getUserId()) >> []
        categoryService.getCategoryChildrenCategoryCodesContainSelf(_ as String, []) >> []
        productCategoryServiceResourceTest.setCategoryService(categoryService)

        when:
        productCategoryServiceResourceTest.queryProductCategoryMapping(requestContext.getUser(), arg)
        then:
        notThrown(Exception)
    }

    def "getIdsByCodes categoryChildrenCategoryCodes is null"() {
        when:
        productCategoryServiceResourceTest.getIdsByCodes(Maps.newHashMap(), Sets.newHashSet())
        then:
        notThrown(Exception)
    }

    def "getIdsByCodes categoryChildrenCategoryCodes is not null"() {
        given:

        Map<String, String> map = Maps.newHashMap()
        map.put("code", "id")
        Set<String> set = Sets.newHashSet()
        set.add("code")

        when:
        productCategoryServiceResourceTest.getIdsByCodes(map, set)
        then:
        notThrown(Exception)
    }

    def "queryShopCategoryChildMapping shopCategoryIds is null"() {
        given:
        def arg = new ListChildrenArg()
        when:
        productCategoryServiceResourceTest.queryShopCategoryChildMapping(requestContext.getUser(), arg)
        then:
        notThrown(Exception)
    }

    def "queryShopCategoryChildMapping findAllSortedCategory is null"() {
        given:
        ListChildrenArg arg = new ListChildrenArg()
        arg.setCodes(["shopCategoryId"])

        and:
        def result = Mock(QueryResult)
        result.getData() >> []

        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllSortedCategory(requestContext.getUser(),
                [], false, CategoryFilterEnum.SHOP) >> result
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryServiceResourceTest.queryShopCategoryChildMapping(requestContext.getUser(), arg)
        then:
        notThrown(Exception)
    }

    def "queryShopCategoryChildMapping findAllSortedCategory is not null"() {
        given:

        ListChildrenArg arg = new ListChildrenArg()
        arg.setCodes(["shopCategoryId"])

        and:
        def result = Mock(QueryResult)
        def data = new ObjectData()
        data.set("code", "code")
        result.getData() >> [data]

        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllSortedCategory(requestContext.getUser(),
                [], false, CategoryFilterEnum.SHOP) >> result
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.queryProductAllCategories(result.getData()) >> []

        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)

        when:
        productCategoryServiceResourceTest.queryShopCategoryChildMapping(requestContext.getUser(), arg)
        then:
        notThrown(Exception)
    }

    def "queryShopCategoryChildMapping"() {
        given:

        ListChildrenArg arg = new ListChildrenArg()
        arg.setCodes(["shopCategoryId", "code", "id"])

        and:
        def result = Mock(QueryResult)
        def data = new ObjectData()
        data.set("code", "code")
        data.setId("id")
        result.getData() >> [data]

        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllSortedCategory(requestContext.getUser(),
                [], false, CategoryFilterEnum.SHOP) >> result
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        ProductAllCategoriesModel.CategoryPojo pojo = new ProductAllCategoriesModel.CategoryPojo()
        productCategoryBizService.queryProductAllCategories(result.getData()) >> [pojo]

        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)


        and:
        def categoryService = Mock(ProductCategoryServiceImpl)
        categoryService.getCategoryChildrenCategoryCodesContainSelf(_ as String, _ as List<ProductAllCategoriesModel.CategoryPojo>) >> []

        productCategoryServiceResourceTest.setCategoryService(categoryService)

        when:
        productCategoryServiceResourceTest.queryShopCategoryChildMapping(requestContext.getUser(), arg)
        then:
        notThrown(Exception)
    }

    def "queryProductCategoryMapping arg is null"() {
        given:

        def arg = new ListChildrenArg()
        arg.setFilterByShopCategory(false)
        arg.setCodes(["1"])

        and:
        def categoryService = Mock(ProductCategoryServiceImpl)
        categoryService.getProductAllCategories(requestContext.getTenantId(), requestContext.getUser().getUserId()) >> []
        categoryService.getCategoryChildrenCategoryCodesContainSelf(_ as String, []) >> []
        productCategoryServiceResourceTest.setCategoryService(categoryService)

        when:
        productCategoryServiceResourceTest.queryProductCategoryMapping(requestContext.getUser(), null)
        then:
        notThrown(Exception)
    }

    def "bulk_add"() {
        given:

        def arg = new BulkAddArg()
        def tree = new ProductCategoryTree()
        tree.setName("name")
        arg.setCategoryList([tree])
        and:
        // 正确创建 mock
        def productCategoryValidator = Mock(ProductCategoryValidator)

        // 使用 Spock 风格设置 mock 行为
        productCategoryValidator.checkLevelAboutBulk(tree) >> {}
        productCategoryValidator.checkRootName(serviceContext, _ as Set<String>, _ as List<ProductCategoryTree>) >> {}
        productCategoryValidator.checkBulkAddLimit(_ as List<IObjectData>) >> {}

        productCategoryServiceResourceTest.setProductCategoryValidator(productCategoryValidator)

        and:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        // 使用 Spock 风格设置 mock 行为
        productCategoryBizService.bulkAddTreeHandler(tree, requestContext.getUser(), _ as ProductCategoryInsertImportDataAction.Recorder) >> []
        productCategoryBizService.bulkAddDataFill(requestContext.getUser(), _ as List<ProductCategoryTree>, _ as Map<String, String>) >> {}
        productCategoryBizService.fillPidAndPath(requestContext.getUser(), _ as Set<String>, _ as Map<String, String>) >> {}
        productCategoryBizService.sendSynchronizeDescribeMqByGray(requestContext.getUser()) >> {}

        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)

        and:
        ServiceFacadeProxy serviceFacadeProxy = Mock(ServiceFacadeProxy)
        // 使用 Spock 风格设置 mock 行为
        serviceFacadeProxy.bulkSaveObjectData(_ as List<IObjectData>, requestContext.getUser()) >> {}

        productCategoryServiceResourceTest.setServiceFacadeProxy(serviceFacadeProxy)

        when:
        productCategoryServiceResourceTest.bulkAdd(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "savaCategoryCheckAsync"() {
        given:

        def data = new ObjectData()
        data.set("pid", "pid")
        def dataDocument = Mock(ObjectDataDocument)
        dataDocument.toObjectData() >> data


        and:
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(requestContext.getUser(), "ProductCategoryObj") >> null
        productCategoryServiceResourceTest.setDescribeEnhancer(describeEnhancer)

        and:
        def asyncBootstrap = Mock(AsyncBootstrap)
        asyncBootstrap.composed(_ as User, _ as long, _ as CompletableFuture<?>) >> ""
        productCategoryServiceResourceTest.setAsyncBootstrap(asyncBootstrap)

        and:
        def pool = Mock(ThreadPoolExecutor)
        productCategoryServiceResourceTest.setExecutorPool(pool)

        // 正确创建 mock
        def productCategoryValidator = Mock(ProductCategoryValidator)

        // 使用 Spock 风格设置 mock 行为
        productCategoryValidator.checkPartnerCategoryId([], "pid") >> {}
        productCategoryValidator.checkNameUnique(dataDocument, []) >> {}
        productCategoryValidator.checkDepth(dataDocument, []) >> {}
        productCategoryValidator.checkAllDepths([]) >> {}
        productCategoryValidator.checkCodeUnique([], dataDocument) >> {}
        productCategoryServiceResourceTest.setProductCategoryValidator(productCategoryValidator)

        and:
        // 正确创建 mock
        def productCategoryV2Validator = Mock(ProductCategoryV2Validator)
        productCategoryV2Validator.checkCategoryTotalLimit(requestContext.getUser(), _ as long) >> {}
        productCategoryV2Validator.checkCategoryIsRelated(requestContext.getUser(), "pid", null) >> {}
        productCategoryServiceResourceTest.setProductCategoryV2Validator(productCategoryV2Validator)

        when:
        productCategoryServiceResourceTest.savaCategoryCheckAsync(serviceContext.getUser(), "Add", dataDocument, [])
        then:
        notThrown(Exception)
    }

    def "updateDragData"() {
        given:
        def originalData = new ObjectData()
        def newData = new ObjectData()
        // 正确创建 mock
        ServiceFacadeProxy serviceFacadeProxy = Mock(ServiceFacadeProxy)
        // 使用 Spock 风格设置 mock 行为
        serviceFacadeProxy.batchUpdateByFields(requestContext.getUser(), [originalData], Lists.newArrayList(PID, ORDER_FIELD)) >> {}
        productCategoryServiceResourceTest.setServiceFacadeProxy(serviceFacadeProxy)
        and:
        productCategoryServiceResourceTest.getString(_ as IObjectData, _ as String, _ as String) >> ""

        and:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        // 使用 Spock 风格设置 mock 行为
        productCategoryBizService.bulkUpdateOrderField(requestContext.getUser(), _ as String, _, _ as String) >> {}
        productCategoryBizService.changePathByParentId(requestContext.getUser(), _ as String, _ as String) >> {}
        productCategoryBizService.sendSynchronizeDescribeMq(requestContext.getUser()) >> {}
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)

        when:
        productCategoryServiceResourceTest.updateDragData(serviceContext, originalData,
                "new", "newOrder", newData)
        then:
        notThrown(Exception)
    }

    def "add_to_db"() {
        given:
        // 正确创建 mock
        ServiceFacadeProxy serviceFacadeProxy = Mock(ServiceFacadeProxy)

        // 使用 Spock 风格设置 mock 行为
        serviceFacadeProxy.triggerAction(_ as ActionContext, _ as BaseObjectSaveAction.Arg, _ as Class<BaseObjectSaveAction.Result>) >> new BaseObjectSaveAction.Result()

        productCategoryServiceResourceTest.setServiceFacadeProxy(serviceFacadeProxy)
        productCategoryServiceResourceTest.getDocument(_ as BaseObjectSaveAction.Result) >> Mock(ObjectDataDocument)

        def mockDoc = Mock(ObjectDataDocument)
        mockDoc.toObjectData() >> new ObjectData()

        when:
        productCategoryServiceResourceTest.add2db(serviceContext, mockDoc)
        then:
        notThrown(Exception)
    }

    def "category_nodeList"() {
        given:

        def arg = new CategoryArg()

        and:
        // 正确创建 mock
        def productCategoryBizService = Mock(ProductCategoryBizService)

        // 使用 Spock 风格设置 mock 行为
        1 * productCategoryBizService.categoryNodeList(requestContext.getUser(), _)

        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)
        when:
        productCategoryServiceResourceTest.categoryNodeList(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "send_gray_category_object_mq tenant is null"() {
        given:
        def mock = Mock(ServiceContext)
        mock.getTenantId() >> null

        when:
        productCategoryServiceResourceTest.sendGrayCategoryObjectMq(mock)
        then:
        notThrown(Exception)
    }


    def "send_gray_category_object_mq tenant is mot null"() {
        given:
        def productCategoryBizService = Mock(ProductCategoryBizService)

        productCategoryBizService.sendGrayCategoryObjectMq(requestContext.getUser()) >> {}
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)
        when:
        productCategoryServiceResourceTest.sendGrayCategoryObjectMq(serviceContext)
        then:
        notThrown(Exception)
    }

//    def "send_gray_category_object_mq tenant is mot null throw ex"() {
//        given:
//        def productCategoryBizService = Mock(ProductCategoryBizService)
//
//        productCategoryBizService.sendGrayCategoryObjectMq(requestContext.getUser()) >> { throw new Exception() }
//        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)
//        when:
//        productCategoryServiceResourceTest.sendGrayCategoryObjectMq(serviceContext)
//        then:
//        notThrown(Exception)
//    }


    def "gray_product_category tenant is null"() {
        when:
        productCategoryServiceResourceTest.categoryProductCategory(serviceContext, [])
        then:
        notThrown(Exception)
    }

    def "gray_product_category tenant is >5"() {
        when:
        productCategoryServiceResourceTest.categoryProductCategory(serviceContext, ["1", "2", "3", "4", "5", "6"])
        then:
        notThrown(Exception)
    }

    def "gray_product_category !ConfigConstant.allowGrayProductCategory()"() {
        given:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.allowGrayProductCategory() >> false
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)
        when:
        productCategoryServiceResourceTest.categoryProductCategory(serviceContext, ["1", "2", "3"])
        then:
        notThrown(Exception)
    }

    def "gray_product_category ConfigConstant.allowGrayProductCategory()"() {
        given:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.allowGrayProductCategory() >> true
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def crmMetaDataServiceProxy = Mock(CRMMetaDataServiceProxy)
        crmMetaDataServiceProxy.grayProductCategory(_ as List<String>)
        productCategoryServiceResourceTest.setCrmMetaDataServiceProxy(crmMetaDataServiceProxy)
        when:
        productCategoryServiceResourceTest.categoryProductCategory(serviceContext, ["1", "2", "3"])
        then:
        notThrown(Exception)
    }

    def "update not isCloseOldProductCategory"() {
        given:
        def data = new ObjectData()
        def dataDocument = Mock(ObjectDataDocument)
        dataDocument.toObjectData() >> data

        and:
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryUtils.findAllSortedCategory(user) >> queryResult
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        productCategoryServiceResourceTest.updateOldCategory(serviceContext, dataDocument) >> {}
        productCategoryServiceResourceTest.getObjectDataDocumentList(_ as List<IObjectData>) >> []

        when:
        productCategoryServiceResourceTest.update(serviceContext, dataDocument)

        then:
        notThrown(Exception)
    }

    def "update isCloseOldProductCategory"() {
        given:
        def data = new ObjectData()
        def dataDocument = Mock(ObjectDataDocument)
        dataDocument.toObjectData() >> data

        and:
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryUtils.findAllSortedCategory(user) >> queryResult
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        productCategoryServiceResourceTest.updateOldCategory(serviceContext, dataDocument) >> {}
        productCategoryServiceResourceTest.getObjectDataDocumentList(_ as List<IObjectData>) >> []

        and:
        // 正确创建 mock
        ServiceFacadeProxy serviceFacadeProxy = Mock(ServiceFacadeProxy)
        // 使用 Spock 风格设置 mock 行为
        serviceFacadeProxy.triggerAction(_ as ActionContext, _ as BaseObjectSaveAction.Arg, _ as Class<BaseObjectSaveAction.Result>) >> {}
        productCategoryServiceResourceTest.setServiceFacadeProxy(serviceFacadeProxy)

        when:
        productCategoryServiceResourceTest.update(serviceContext, dataDocument)

        then:
        notThrown(Exception)
    }

    def "delete"() {
        given:
        def arg = new Arg()
        arg.setId("id-111")

        and:
        def data = new ObjectData()
        data.set("code", "code")
        def mockResult = Mock(BaseObjectSaveAction.Result)

        ServiceFacadeProxy serviceFacadeProxy = Mock(ServiceFacadeProxy)
        serviceFacadeProxy.triggerAction(_ as ActionContext, _ as BaseObjectSaveAction.Arg, _ as Class<BaseObjectSaveAction.Result>) >> mockResult
        serviceFacadeProxy.findObjectDataByIds(requestContext.getTenantId(), ["id-111"], "ProductCategoryObj") >> [data]
        serviceFacadeProxy.bulkInvalidAndDeleteWithSuperPrivilege(_ as List<IObjectData>, user) >> {}
        productCategoryServiceResourceTest.setServiceFacadeProxy(serviceFacadeProxy)

        and:
        def result = new CheckDeleteCategoryModel.Result(true)
        productCategoryServiceResourceTest.checkDeleteCategory(_ as CheckDeleteCategoryModel.Arg, serviceContext) >> result
        productCategoryServiceResourceTest.getSearchTemplateQuery([] as Set<String>) >> Mock(SearchTemplateQuery)

        and:
        def queryResult1 = Mock(QueryResult)
        queryResult1.getData() >> []

        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExt.findBySearchQueryIgnoreAll(requestContext.getUser(), API_NAME, _ as SearchTemplateQuery) >> queryResult1
        productCategoryServiceResourceTest.setMetaDataFindServiceExt(metaDataFindServiceExt)

        and:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.sendSynchronizeDescribeMqByGray(requestContext.getUser(), "delete") >> {}
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)


        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllSortedCategory(user) >> queryResult
        productCategoryUtils.findCategoryChildren(requestContext.getUser(), _ as String) >> []
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(SO_CHECK_CATEGORY_DELETE) >> "ex"
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryServiceResourceTest.delete(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "List method"() {
        given:
        def dataList = [new ObjectData("name": "分类1"), new ObjectData("name": "分类2")]
        def queryResult = Mock(QueryResult)
        def arg = new ProductCategoryListArg()
        queryResult.getData() >> dataList


        and: "mock method"
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.ignoreAvailableRangeCategory(user.getTenantId()) >> isIgnore
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and: "getCategoryFilterEnums && getExtendFields && filterByAvailableRange"
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.getCategoryFilterEnums(arg) >> CategoryFilterEnum.SHOP
        productCategoryBizService.getExtendFields(serviceContext.getUser(), null) >> []
        productCategoryBizService.filterByAvailableRange(serviceContext, arg, _ as List) >> filterByAvailableRangeDataList
        productCategoryBizService.isPreCategoryField(arg.getRelatedFieldApiName()) >> isPreCategoryField
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.findAllSortedCategory(_ as User, _ as List<String>, _ as boolean, _ as CategoryFilterEnum) >> queryResult
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        when: "test method"
        productCategoryServiceResourceTest.list(serviceContext, arg)
        then: "verify result contains"
        notThrown(Exception)
        where:
        isIgnore | filterByAvailableRangeDataList                                       | isPreCategoryField
        true     | [new ObjectData("name": "分类1"), new ObjectData("name": "分类2")]   | false
        false    | [new ObjectData("name": "分类1"), new ObjectData("name": "分类2")]   | false
        false    | [new ObjectData("name": "分类13"), new ObjectData("name": "分类23")] | false
        false    | [new ObjectData("name": "分类1")]                                    | false
        false    | new ObjectData("name": "分类1")                                      | true
    }

    def "searchCategory method result"() {
        given:
        def searchArg = new SearchArg("fieldApiName": fieldApiName, "fieldValues": values)

        def metaDataFindServiceExtMock = Mock(MetaDataFindServiceExt)
        productCategoryServiceResourceTest.setMetaDataFindServiceExt(metaDataFindServiceExtMock)

        and:
        def queryResult = Mock(QueryResult)
        def map = Maps.newHashMap()
        map.put("tenant_id", "71568")
        def ofData = new ObjectData(map)
        queryResult.getData() >> Lists.newArrayList(ofData)
        metaDataFindServiceExtMock.findBySearchQueryIgnoreAll(_ as User, _ as String, _ as SearchTemplateQuery) >> queryResult
        and:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.includeField(_ as String) >> true
        categoryStaticUtilService.getSearchCategoryValueSize(_ as String) >> 4
        categoryStaticUtilService.text(_) >> ""
        categoryStaticUtilService.text(_, _) >> ""
        categoryStaticUtilService.text(_, _, _) >> ""
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryServiceResourceTest.searchCategory(serviceContext, searchArg)
        then:
        notThrown(Exception)
        where:
        fieldApiName | values          || emptyResult
        null         | []              || true
        ""           | []              || true
        ""           | null            || true
        null         | null            || true
        "id"         | ["1", "2", "3"] || false
        "id"         | []              || true
    }

    def "searchCategory method throw validateException"() {
        given:
        def searchArg = new SearchArg("fieldApiName": fieldApiName, "fieldValues": values)

        def metaDataFindServiceExtMock = Mock(MetaDataFindServiceExt)
        productCategoryServiceResourceTest.setMetaDataFindServiceExt(metaDataFindServiceExtMock)

        and: "mock meta data find data result"
        def queryResult = Mock(QueryResult)
        def map = Maps.newHashMap()
        map.put("tenant_id", "71568")
        def ofData = new ObjectData(map)
        queryResult.getData() >> Lists.newArrayList(ofData)
        metaDataFindServiceExtMock.findBySearchQueryIgnoreAll(_ as User, _ as String, _ as SearchTemplateQuery) >> queryResult
        and: "mock static method"
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.includeField(_ as String) >> include
        categoryStaticUtilService.getSearchCategoryValueSize(_ as String) >> 2
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        when:
        productCategoryServiceResourceTest.searchCategory(serviceContext, searchArg)
        then:
        thrown(Exception)
        where:
        fieldApiName | values                | include
        "id"         | ["1"]                || false
        "id"         | ["1", "2", "3"]      || true
        "id"         | ["1", "2", "3", "4"] || true
        "name"       | ["1", "2", "3", "4"] || true
    }

    def "getTree method test and return empty"() {
        given: "context"
        def arg = new ChildCategoryArg()

        and:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.getCategoryTree(user, arg) >> null
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)
        when:
        productCategoryServiceResourceTest.getTree(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "getTree method test and return not empty"() {
        given: "context"
        def arg = new ChildCategoryArg()
        def tree = buildMockCategoryTree()

        and:
        def categoryBizService = Mock(ProductCategoryBizService)
        categoryBizService.getCategoryTree(user, arg) >> tree
        productCategoryServiceResourceTest.setProductCategoryBizService(categoryBizService)

        when:
        productCategoryServiceResourceTest.getTree(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    private static ProductCategoryTree buildMockCategoryTree() {
        def json = "{\"categoryCode\":\"01\",\"children\":[{\"categoryCode\":\"07\",\"children\":[],\"code\":\"853868\",\"id\":\"60546af577ee0b0007f539ec\",\"name\":\"生活\",\"orderField\":3,\"pid\":\"605466f977ee0b0007f4f4c3\"},{\"categoryCode\":\"03\",\"children\":[{\"categoryCode\":\"05\",\"children\":[],\"code\":\"853866\",\"id\":\"605468c70d620e00071f6105\",\"name\":\"洗碗机\",\"orderField\":1,\"pid\":\"605467210d620e00071f466d\"}],\"code\":\"853864\",\"id\":\"605467210d620e00071f466d\",\"name\":\"厨房用品\",\"orderField\":2,\"pid\":\"605466f977ee0b0007f4f4c3\"},{\"categoryCode\":\"02\",\"children\":[{\"categoryCode\":\"06\",\"children\":[],\"code\":\"853867\",\"id\":\"605469590d620e00071f65d6\",\"name\":\"扫地机器人\",\"orderField\":2,\"pid\":\"6054670f77ee0b0007f4f643\"},{\"categoryCode\":\"04\",\"children\":[],\"code\":\"853865\",\"id\":\"6054676e77ee0b0007f4fcef\",\"name\":\"电热毯\",\"orderField\":1,\"pid\":\"6054670f77ee0b0007f4f643\"}],\"code\":\"853863\",\"id\":\"6054670f77ee0b0007f4f643\",\"name\":\"家居用品\",\"orderField\":1,\"pid\":\"605466f977ee0b0007f4f4c3\"},{\"categoryCode\":\"qdasdsawffffe\",\"children\":[{\"categoryCode\":\"737\",\"children\":[{\"categoryCode\":\"741\",\"children\":[{\"categoryCode\":\"示例文本dsadsad\",\"children\":[],\"code\":\"853861\",\"id\":\"6052e46c1c5ce30001159765\",\"name\":\"示例文本dddaa\",\"orderField\":37,\"pid\":\"5fb25007a9684032f0b16484\"},{\"categoryCode\":\"dddddd\",\"children\":[],\"code\":\"853859\",\"id\":\"6052e382a6c90100011a8345\",\"name\":\"ddd\",\"orderField\":36,\"pid\":\"5fb25007a9684032f0b16484\"},{\"categoryCode\":\"5632\",\"children\":[],\"code\":\"853825\",\"id\":\"5fcdfa5d8c6d5100013dfbf2\",\"name\":\"哈啦\",\"orderField\":35,\"pid\":\"5fb25007a9684032f0b16484\"},{\"categoryCode\":\"sdsadsa\",\"children\":[{\"categoryCode\":\"hjh2321321\",\"children\":[],\"code\":\"853883\",\"id\":\"6140135c216f330001c3b3b0\",\"name\":\"产品分类hh加\",\"orderField\":2,\"pid\":\"5fc45237844fd4000185fb25\"}],\"code\":\"853801\",\"id\":\"5fc45237844fd4000185fb25\",\"name\":\"4.2\",\"orderField\":33,\"pid\":\"5fb25007a9684032f0b16484\"},{\"categoryCode\":\"7sdsd41\",\"children\":[],\"code\":\"853799\",\"id\":\"5fc451dd844fd4000185fa5d\",\"name\":\"4.1\",\"orderField\":34,\"pid\":\"5fb25007a9684032f0b16484\"}],\"code\":\"741\",\"id\":\"5fb25007a9684032f0b16484\",\"name\":\"333333a\",\"orderField\":4,\"pid\":\"5fb25007a9684032f0b16480\"},{\"categoryCode\":\"739\",\"children\":[{\"categoryCode\":\"740\",\"children\":[],\"code\":\"740\",\"id\":\"5fb25007a9684032f0b16483\",\"name\":\"2.1\",\"orderField\":1,\"pid\":\"5fb25007a9684032f0b16482\"}],\"code\":\"739\",\"id\":\"5fb25007a9684032f0b16482\",\"name\":\"2.2\",\"orderField\":2,\"pid\":\"5fb25007a9684032f0b16480\"},{\"categoryCode\":\"738\",\"children\":[{\"categoryCode\":\"hjh23213241\",\"children\":[{\"categoryCode\":\"78963\",\"children\":[],\"code\":\"852752\",\"id\":\"5fbb688c2358f600017cc438\",\"name\":\"测试新建子级\",\"orderField\":3,\"pid\":\"5fb3355bdb6ca21a60af21b9\"},{\"categoryCode\":\"56899\",\"children\":[{\"categoryCode\":\"1852\",\"children\":[],\"code\":\"852745\",\"id\":\"5fbb6313432d7b000127a083\",\"name\":\"测\",\"orderField\":1,\"pid\":\"5fbb62d8432d7b0001279f6b\"}],\"code\":\"852744\",\"id\":\"5fbb62d8432d7b0001279f6b\",\"name\":\"测试编码\",\"orderField\":2,\"pid\":\"5fb3355bdb6ca21a60af21b9\"}],\"code\":\"756\",\"id\":\"5fb3355bdb6ca21a60af21b9\",\"name\":\"产品分类添加\",\"orderField\":2,\"pid\":\"5fb25007a9684032f0b16481\"}],\"code\":\"738\",\"id\":\"5fb25007a9684032f0b16481\",\"name\":\"2.1\",\"orderField\":1,\"pid\":\"5fb25007a9684032f0b16480\"}],\"code\":\"737\",\"id\":\"5fb25007a9684032f0b16480\",\"name\":\"222222\",\"orderField\":2,\"pid\":\"5fc46bdfd0a2b541d8103b3f\"},{\"categoryCode\":\"673\",\"children\":[{\"categoryCode\":\"668\",\"children\":[{\"categoryCode\":\"78134821\",\"children\":[],\"code\":\"853847\",\"id\":\"603f754c512ea30001d2cd2d\",\"name\":\"二级分类D4\",\"orderField\":11,\"pid\":\"5fa3750512a3cc3ae073a936\"},{\"categoryCode\":\"666761\",\"children\":[],\"code\":\"853846\",\"id\":\"603f753d512ea30001d2cc5c\",\"name\":\"二级分类D3\",\"orderField\":10,\"pid\":\"5fa3750512a3cc3ae073a936\"},{\"categoryCode\":\"829515\",\"children\":[],\"code\":\"853845\",\"id\":\"603f752c512ea30001d2cb5b\",\"name\":\"二级分类D防守打法\",\"orderField\":9,\"pid\":\"5fa3750512a3cc3ae073a936\"},{\"categoryCode\":\"456794\",\"children\":[],\"code\":\"853844\",\"id\":\"603f751320f33900014db4e0\",\"name\":\"二级分类D1\",\"orderField\":8,\"pid\":\"5fa3750512a3cc3ae073a936\"},{\"categoryCode\":\"78945\",\"children\":[],\"code\":\"853843\",\"id\":\"603f7252512ea30001d2bf13\",\"name\":\"二级分类1\",\"orderField\":7,\"pid\":\"5fa3750512a3cc3ae073a936\"},{\"categoryCode\":\"74185\",\"children\":[],\"code\":\"853842\",\"id\":\"603f7181512ea30001d2bc04\",\"name\":\"二级分类D\",\"orderField\":6,\"pid\":\"5fa3750512a3cc3ae073a936\"},{\"categoryCode\":\"671\",\"children\":[],\"code\":\"671\",\"id\":\"5fa3750512a3cc3ae073a939\",\"name\":\"二级分类C\",\"orderField\":5,\"pid\":\"5fa3750512a3cc3ae073a936\"},{\"categoryCode\":\"670\",\"children\":[],\"code\":\"670\",\"id\":\"5fa3750512a3cc3ae073a938\",\"name\":\"二级分类B\",\"orderField\":4,\"pid\":\"5fa3750512a3cc3ae073a936\"},{\"categoryCode\":\"669\",\"children\":[{\"categoryCode\":\"4561\",\"children\":[{\"categoryCode\":\"45679\",\"children\":[],\"code\":\"853840\",\"id\":\"6036120bf3f3ee0001decc15\",\"name\":\"四级分类e1\",\"orderField\":1,\"pid\":\"603611165ca3500001e09a85\"}],\"code\":\"853839\",\"id\":\"603611165ca3500001e09a85\",\"name\":\"3级别分类\",\"orderField\":4,\"pid\":\"5fa3750512a3cc3ae073a937\"},{\"categoryCode\":\"456132\",\"children\":[],\"code\":\"853838\",\"id\":\"603610e5f3f3ee0001deace7\",\"name\":\"三级分类e\",\"orderField\":5,\"pid\":\"5fa3750512a3cc3ae073a937\"},{\"categoryCode\":\"963\",\"children\":[{\"categoryCode\":\"56\",\"children\":[],\"code\":\"853837\",\"id\":\"6034a57c2a04a100015d16e1\",\"name\":\"四级12\",\"orderField\":2,\"pid\":\"6034729f12ce8d000129d2f3\"},{\"categoryCode\":\"4325\",\"children\":[{\"categoryCode\":\"54657\",\"children\":[],\"code\":\"853858\",\"id\":\"603f763e20f33900014dc273\",\"name\":\"5及分类gsdfgfdh\",\"orderField\":10,\"pid\":\"603472c712ce8d000129d6a2\"},{\"categoryCode\":\"26589\",\"children\":[],\"code\":\"853857\",\"id\":\"603f762f20f33900014dc1df\",\"name\":\"5及分类r\",\"orderField\":9,\"pid\":\"603472c712ce8d000129d6a2\"},{\"categoryCode\":\"4645654\",\"children\":[],\"code\":\"853856\",\"id\":\"603f761f20f33900014dc11e\",\"name\":\"5及分类y\",\"orderField\":8,\"pid\":\"603472c712ce8d000129d6a2\"},{\"categoryCode\":\"546456\",\"children\":[{\"categoryCode\":\"436546\",\"children\":[],\"code\":\"853855\",\"id\":\"603f7611512ea30001d2d3ae\",\"name\":\"5及分类i\",\"orderField\":1,\"pid\":\"603f75fc512ea30001d2d2b4\"}],\"code\":\"853854\",\"id\":\"603f75fc512ea30001d2d2b4\",\"name\":\"5及分类8\",\"orderField\":7,\"pid\":\"603472c712ce8d000129d6a2\"},{\"categoryCode\":\"657567\",\"children\":[],\"code\":\"853853\",\"id\":\"603f75ee20f33900014dc07a\",\"name\":\"5及分类f\",\"orderField\":6,\"pid\":\"603472c712ce8d000129d6a2\"},{\"categoryCode\":\"855677543\",\"children\":[],\"code\":\"853852\",\"id\":\"603f75db512ea30001d2d220\",\"name\":\"5及分类57\",\"orderField\":5,\"pid\":\"603472c712ce8d000129d6a2\"},{\"categoryCode\":\"567867\",\"children\":[],\"code\":\"853851\",\"id\":\"603f75c920f33900014dbfd6\",\"name\":\"5及分类7\",\"orderField\":4,\"pid\":\"603472c712ce8d000129d6a2\"},{\"categoryCode\":\"235423\",\"children\":[],\"code\":\"853850\",\"id\":\"603f75ba20f33900014dbf24\",\"name\":\"5及分类6\",\"orderField\":3,\"pid\":\"603472c712ce8d000129d6a2\"},{\"categoryCode\":\"7122\",\"children\":[],\"code\":\"853849\",\"id\":\"603f75a820f33900014dbe18\",\"name\":\"5及分类\",\"orderField\":2,\"pid\":\"603472c712ce8d000129d6a2\"},{\"categoryCode\":\"453\",\"children\":[],\"code\":\"853835\",\"id\":\"6034a4882a04a100015d0d70\",\"name\":\"5级分类\",\"orderField\":1,\"pid\":\"603472c712ce8d000129d6a2\"}],\"code\":\"853834\",\"id\":\"603472c712ce8d000129d6a2\",\"name\":\"四级分类1\",\"orderField\":1,\"pid\":\"6034729f12ce8d000129d2f3\"}],\"code\":\"853832\",\"id\":\"6034729f12ce8d000129d2f3\",\"name\":\"三级分类d\",\"orderField\":6,\"pid\":\"5fa3750512a3cc3ae073a937\"},{\"categoryCode\":\"852\",\"children\":[],\"code\":\"853831\",\"id\":\"60347279d93676000195d05c\",\"name\":\"三级分类c\",\"orderField\":3,\"pid\":\"5fa3750512a3cc3ae073a937\"},{\"categoryCode\":\"789\",\"children\":[],\"code\":\"853830\",\"id\":\"6034725dd93676000195cfb8\",\"name\":\"三级分类B\",\"orderField\":2,\"pid\":\"5fa3750512a3cc3ae073a937\"},{\"categoryCode\":\"672\",\"children\":[],\"code\":\"672\",\"id\":\"5fa3750512a3cc3ae073a93a\",\"name\":\"三级分类A\",\"orderField\":1,\"pid\":\"5fa3750512a3cc3ae073a937\"}],\"code\":\"669\",\"id\":\"5fa3750512a3cc3ae073a937\",\"name\":\"二级分类A\",\"orderField\":1,\"pid\":\"5fa3750512a3cc3ae073a936\"}],\"code\":\"668\",\"id\":\"5fa3750512a3cc3ae073a936\",\"name\":\"一级分类A\",\"orderField\":2,\"pid\":\"5fa3750512a3cc3ae073a93b\"}],\"code\":\"673\",\"id\":\"5fa3750512a3cc3ae073a93b\",\"name\":\"一级分类B\",\"orderField\":3,\"pid\":\"5fc46bdfd0a2b541d8103b3f\"}],\"code\":\"853812\",\"id\":\"5fc46bdfd0a2b541d8103b3f\",\"name\":\"z一级分类\",\"orderField\":5,\"pid\":\"605466f977ee0b0007f4f4c3\"}],\"code\":\"853862\",\"id\":\"605466f977ee0b0007f4f4c3\",\"name\":\"电器\",\"orderField\":15}"
        return JSON.parseObject(json, ProductCategoryTree)
    }

    def "tree_list is FilterByShopCategory categoryStaticUtilService is false"() {
        given:
        def treeArg = new TreeArg()
        treeArg.setFilterByShopCategory(true)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.isDhtOrAccessoriesMallRequest("", "") >> false
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryValidator = Mock(ProductCategoryValidator)
        productCategoryValidator.getCategoryTreeList(user, false, _ as CategoryFilterEnum) >> Mock(CategoryObject)
        productCategoryServiceResourceTest.setProductCategoryValidator(productCategoryValidator)

        when:
        productCategoryServiceResourceTest.treeList(serviceContext, treeArg)
        then:
        notThrown(Exception)
    }

    def "tree_list not is FilterByShopCategory categoryStaticUtilService is true"() {
        given:
        def treeArg = new TreeArg()
        treeArg.setFilterByShopCategory(false)

        and:
        def categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.isDhtOrAccessoriesMallRequest("", "") >> true
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        productCategoryServiceResourceTest.getCategoryForCPQ(serviceContext, _ as CategoryFilterEnum, _ as Boolean) >> Mock(CategoryObject)

        when:
        productCategoryServiceResourceTest.treeList(serviceContext, treeArg)
        then:
        notThrown(Exception)
    }


    def "not gray product category of dragOrder"() {
        given:
        CategoryStaticUtilService categoryStaticUtilService = Mock(CategoryStaticUtilService)
        categoryStaticUtilService.text(TENANT_NOT_GRAY_THIS_ABILITY) >> "该企业未灰度该功能"
        productCategoryServiceResourceTest.setCategoryStaticUtilService(categoryStaticUtilService)

        and:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> false
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        when:
        productCategoryServiceResourceTest.dragOrder(serviceContext, null)
        then:
        thrown(ValidateException)
    }

    def "gray product category of dragOrder"() {
        given:
        def productCategoryUtils = Mock(ProductCategoryUtils)
        productCategoryUtils.isCloseOldProductCategory(user.getTenantId()) >> true
        productCategoryServiceResourceTest.setProductCategoryUtils(productCategoryUtils)

        and:
        def originalData = new ObjectData()
        def queryResult = Mock(QueryResult)
        queryResult.getData() >> []
        def metaDataFindServiceExt = Mock(MetaDataFindServiceExt)
        metaDataFindServiceExt.findObjectData(serviceContext.getUser(), _ as String, ProductCategoryModel.Metadata.API_NAME) >> originalData
        metaDataFindServiceExt.findBySearchQueryWithFields(serviceContext.getUser(), ProductCategoryModel.Metadata.API_NAME, _ as SearchTemplateQuery, _ as List<String>, true) >> queryResult
        productCategoryServiceResourceTest.setMetaDataFindServiceExt(metaDataFindServiceExt)

        and:
        def temp = Mock(ProductCategoryServiceResource)
        productCategoryServiceResourceTest.getProxyProductCategoryServiceResource() >> temp
        temp.updateDragData(serviceContext, _ as IObjectData, _ as String, _ as String, _ as IObjectData) >> {}

        and:
        def productCategoryV2Validator = Mock(ProductCategoryV2Validator)
        productCategoryV2Validator.passParamCheck(serviceContext.getUser(), _ as IObjectData) >> true
        productCategoryV2Validator.checkCategoryIsRelated(serviceContext.getUser(), _ as String, _ as IObjectDescribe) >> {}
        productCategoryServiceResourceTest.setProductCategoryV2Validator(productCategoryV2Validator)


        and:
        def categoryDescribe = Mock(IObjectDescribe)
        def describeEnhancer = Mock(DescribeEnhancer)
        describeEnhancer.fetchObject(serviceContext.getUser(), _ as String) >> categoryDescribe
        productCategoryServiceResourceTest.setDescribeEnhancer(describeEnhancer)


        and:
        productCategoryServiceResourceTest.getString(_ as IObjectData, _ as String, _ as String) >> ""
        productCategoryServiceResourceTest.needUpdateDragData(_ as String, _ as String, _ as String, _ as String) >> true

        when:
        def objectDataDocument = Mock(ObjectDataDocument)
        def newData = Mock(ObjectData)
        objectDataDocument.toObjectData() >> newData
        productCategoryServiceResourceTest.dragOrder(serviceContext, objectDataDocument)

        then:
        notThrown(Exception)
    }

    def "havaChildNode objectIdList is null"() {
        given:
        def arg = new CheckChildArg()
        arg.setObjectIdList([])
        when:
        productCategoryServiceResourceTest.havaChildNode(serviceContext, arg)
        then:
        notThrown(Exception)
    }

    def "havaChildNode objectIdList not null"() {
        given:
        def arg = new CheckChildArg()
        arg.setObjectIdList(["1"])

        and:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.havaChildNode(serviceContext.getUser(), arg.getObjectIdList()) >> true
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)
        when:
        productCategoryServiceResourceTest.havaChildNode(serviceContext, arg)
        then:
        notThrown(Exception)
    }


    def "sync_describe"() {
        given:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.asyncSyncDescribe(user, 5) >> {}
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)

        when:
        productCategoryServiceResourceTest.syncDescribe(serviceContext)
        then:
        notThrown(Exception)
    }

    def "sync_describe throw exception"() {
        given:
        def productCategoryBizService = Mock(ProductCategoryBizService)
        productCategoryBizService.asyncSyncDescribe(serviceContext.getUser(), 5) >> { throw new Exception() }
        productCategoryServiceResourceTest.setProductCategoryBizService(productCategoryBizService)
        when:
        productCategoryServiceResourceTest.syncDescribe(serviceContext)
        then:
        notThrown(Exception)
    }

    def "synchronize"() {
        when:
        productCategoryServiceResourceTest.synchronizeCategory(["1"])
        then:
        notThrown(Exception)
    }
}
