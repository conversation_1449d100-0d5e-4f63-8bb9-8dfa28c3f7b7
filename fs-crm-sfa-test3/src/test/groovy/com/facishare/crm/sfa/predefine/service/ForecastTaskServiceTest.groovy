package com.facishare.crm.sfa.predefine.service

import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.service.model.ForecastTaskModel
import com.facishare.crm.sfa.task.ForecastTaskProducer
import com.facishare.crm.sfa.utilities.proxy.BiCrmRestProxy
import com.facishare.crm.sfa.utilities.proxy.SFARecyclingProxy
import com.facishare.crm.sfa.utilities.proxy.model.ForecastTaskProxy
import com.facishare.crm.sfa.utilities.proxy.model.bi.StatViewDataQuery
import com.facishare.crm.sfa.utilities.proxy.model.bi.StatViewDataQuery.ResultData
import com.facishare.crm.sfa.utilities.util.DepartmentBasedAccessControl
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.service.dto.QueryAllSuperDeptsByDeptIds
import com.facishare.paas.appframework.common.service.dto.QueryResponsibleDeptsByUserIds
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.LoggerFactory
import org.spockframework.runtime.Sputnik
import spock.lang.Shared
import spock.lang.Specification

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PrepareForTest([I18N])
@PowerMockIgnore(["javax.management.*"])
@SuppressStaticInitializationFor([
        "com.facishare.paas.I18N",
        "com.facishare.paas.appframework.common.util.AppFrameworkConfig",
        "com.facishare.paas.appframework.metadata.relation.EditCalculateParam",
])
class ForecastTaskServiceTest extends Specification {
    @Shared
    def context = Mock(ServiceContext)
    @Shared
    def user = Mock(User)

    def setupSpec() {
        Whitebox.setInternalState(I18N, "log", LoggerFactory.getLogger("test"))
        Whitebox.setInternalState(I18N, "I18N_RESOURCES", Collections.emptyList())
        Whitebox.setInternalState(I18N, "THREAD_LOCAL", new InheritableThreadLocal<>())
        def tenantId = "1"
        user.getTenantId() >> tenantId
        user.getUserId() >> "1000"
        user.getIsCrmAdmin() >> Optional.empty()
        context.getTenantId() >> tenantId
        context.getUser() >> user
    }

    def "queryGroup - normal"() {
        def biCrmRestProxy = Mock(BiCrmRestProxy)
        biCrmRestProxy.queryViewData(_ as StatViewDataQuery.Arg, _ as Map<String, String>) >> new StatViewDataQuery.Result(
                data: data,
                code: code
        )
        def service = new ForecastTaskService(biCrmRestProxy: biCrmRestProxy)
        def arg = new ForecastTaskModel.Arg(
                biParam: new StatViewDataQuery.Arg()
        )
        def res = service.queryGroup(context, arg)
        expect:
        res.isPresent() == expect
        where:
        data             | code | expect
        new ResultData() | 200  | true
        null             | 500  | false
    }

    def "queryGroup - catch exception"() {
        def biCrmRestProxy = Mock(BiCrmRestProxy)
        biCrmRestProxy.queryViewData(_ as StatViewDataQuery.Arg, _ as Map<String, String>) >> { throw new RuntimeException("test") }
        def service = new ForecastTaskService(biCrmRestProxy: biCrmRestProxy)
        def arg = new ForecastTaskModel.Arg(biParam: new StatViewDataQuery.Arg())
        def res = service.queryGroup(context, arg)
        expect:
        !res.isPresent()
    }

    def "commit"() {
        def serviceFacade = Mock(ServiceFacade)
        serviceFacade.findObjectData(_ as User, _ as String, SFAPreDefineObject.ForecastTask.getApiName()) >>> [
                null,
                new ObjectData("biz_status": 0),
                new ObjectData("biz_status": 1)
        ]
        def service = new ForecastTaskService(serviceFacade: serviceFacade)
        def arg = new ForecastTaskModel.Arg(id: "1")
        when:
        service.commit(context, arg)
        then:
        noExceptionThrown()
        when:
        service.commit(context, arg)
        then:
        noExceptionThrown()
        when:
        service.commit(context, arg)
        then:
        noExceptionThrown()
    }

    def "queryUserIdentity"() {
        def service = new ForecastTaskService()
        when:
        def res = service.queryUserIdentity(context, new ForecastTaskModel.Arg())
        then:
        res.getData()["code"] == "normal"
        when:
        def serviceFacade = Mock(ServiceFacade)
        serviceFacade.findObjectData(_ as User, _ as String, SFAPreDefineObject.ForecastRule.getApiName()) >> new ObjectData(
                "forecast_apply_range": applyRange
        )
        serviceFacade.getResponsibleDeptsByUserIds(_ as String, _ as String, _ as List<String>, 0) >> deptInfoList
        serviceFacade.isAdmin(_ as User) >> isAdmin
        service = new ForecastTaskService(serviceFacade: serviceFacade)
        res = service.queryUserIdentity(context, new ForecastTaskModel.Arg(ruleId: "1"))
        then:
        res.getData()["code"] == expect
        where:
        ruleId | applyRange | deptInfoList                                              | isAdmin | expect
        "1"    | []         | []                                                        | false   | "normal"    // 普通员工不在适用范围
        "1"    | []         | []                                                        | true    | "manager"   // 管理员不在适用范围
        "1"    | ["1001"]   | [new QueryResponsibleDeptsByUserIds.DeptInfo(id: "1001")] | false   | "manager"   // 普通员工在适用范围
    }

    def "queryResponsibleDept"() {
        def service = new ForecastTaskService()
        when:
        def res = service.queryResponsibleDept()
        then:
        (res.getData() as DepartmentBasedAccessControl.TreeNode).getId() == "999999"
    }

    def "querySubDept"() {
        def service = new ForecastTaskService()
        when:
        def res = service.querySubDept(context, new ForecastTaskModel.Arg())
        then:
        res.getData() == []
        when:
        res = service.querySubDept(context, new ForecastTaskModel.Arg(id: "1"))
        then:
        res.getData() == []
        when:
        def serviceFacade = Mock(ServiceFacade)
        serviceFacade.findObjectData(_ as User, _ as String, SFAPreDefineObject.ForecastRule.getApiName()) >> new ObjectData(
                "forecast_apply_range": applyRange
        )
        serviceFacade.getAllSuperDeptsByDeptIds(_ as String, _ as String, _ as List<String>) >> ["1111": superDepts]
        serviceFacade.getResponsibleDeptsByUserIds(_ as String, _ as String, _ as List<String>, 0) >> responsibleDepts
        serviceFacade.isAdmin(_ as User) >> isAdmin
        serviceFacade.getSubDeptByDeptId(_ as String, _ as String, _ as String, false) >> ["1001"]
        serviceFacade.batchGetDeptInfosByDeptIds(_ as User, _ as List<String>) >> []
        serviceFacade.getMemberInfoMapByDeptIds(_ as User, _ as List<String>, false, 0, 1) >> ["1111": []]
        serviceFacade.getAllSuperDeptIdsByDeptIds(_ as String, _ as String, _ as List<String>) >> ["1111": []]
        service = new ForecastTaskService(serviceFacade: serviceFacade)
        res = service.querySubDept(context, new ForecastTaskModel.Arg(id: "1111", ruleId: "1"))
        then:
        res.getData() != null
        where:
        applyRange | superDepts                                                             | responsibleDepts | subDepts | isAdmin
        []         | []                                                                     | []               | []       | false   // all false
        []         | []                                                                     | []               | []       | true    // responsibleConfirm
        ["1111"]   | [new QueryAllSuperDeptsByDeptIds.DeptInfo(id: "111", name: "dept111")] | []               | []       | true    // applyRangeContains && responsibleConfirm
        ["111"]    | [new QueryAllSuperDeptsByDeptIds.DeptInfo(id: "111", name: "dept111")] | []               | []       | true    // applyRangeConfirm && responsibleConfirm && !applyRangeContains
        ["111"]    | [new QueryAllSuperDeptsByDeptIds.DeptInfo(id: "111", name: "dept111")] | []               | []       | false   // applyRangeConfirm
    }

    def "queryForecastPeriod"() {
        def sfaRecyclingProxy = Mock(SFARecyclingProxy)
        sfaRecyclingProxy.getForecastPeriod(_ as ForecastTaskProxy.Arg, _ as Map<String, String>) >> new ForecastTaskProxy.Result()
        def service = new ForecastTaskService(sfaRecyclingProxy: sfaRecyclingProxy)
        expect:
        service.queryForecastPeriod(context, new ForecastTaskModel.Arg(ruleId: "1")) != null
    }

    def "queryLastModifiedTime"() {
        def serviceFacade = Mock(ServiceFacade)
        def time = System.currentTimeMillis()
        serviceFacade.findBySearchQueryWithFieldsIgnoreAll(_ as User, SFAPreDefineObject.ForecastTask.getApiName(), _ as SearchTemplateQuery, _ as List<String>) >> new QueryResult<IObjectData>(
                data: [new ObjectData("last_modified_time": time)]
        )
        def service = new ForecastTaskService(serviceFacade: serviceFacade)
        when:
        def res = service.queryLastModifiedTime(context, new ForecastTaskModel.Arg(ruleId: "1"))
        then:
        res.getData() == time
    }

    def "adjust"() {
        def service = new ForecastTaskService()
        when:
        service.adjust(context, new ForecastTaskModel.Arg())
        then:
        thrown(ValidateException)
        when:
        service.adjust(context, new ForecastTaskModel.Arg(ruleId: "1"))
        then:
        thrown(ValidateException)
        when:
        def serviceFacade = Mock(ServiceFacade)
        serviceFacade.findObjectData(_ as User, _ as String, SFAPreDefineObject.ForecastRule.getApiName()) >>> [
                new ObjectData("_id": "rule1"),
                new ObjectData("_id": "rule1", "adjust_forecast_task": "1")
        ]
        serviceFacade.isAdmin(_ as User) >>> [false, true]
        def arg = new ForecastTaskModel.Arg(ruleId: "rule1", id: "1", adjustData: new ForecastTaskModel.AdjustData(apiName: "", amount: BigDecimal.TEN, original: BigDecimal.ONE))
        service = new ForecastTaskService(serviceFacade: serviceFacade)
        service.adjust(context, arg)
        then: "参数错误"
        thrown(ValidateException)
        when:
        service.adjust(context, arg)
        then: "无权限"
        thrown(ValidateException)
        when:
        serviceFacade.findObjectData(_ as User, _ as String, SFAPreDefineObject.ForecastTask.getApiName()) >> new ObjectData("_id": "task1")
        serviceFacade.findBySearchQueryWithFieldsIgnoreAll(_ as User, SFAPreDefineObject.ForecastTask.getApiName(), _ as SearchTemplateQuery, _ as List<String>) >> new QueryResult<IObjectData>(data: [new ObjectData("_id": "task1")])
        arg = new ForecastTaskModel.Arg(ruleId: "rule1", id: id, adjustData: new ForecastTaskModel.AdjustData(apiName: adjustApiName, amount: BigDecimal.TEN, original: BigDecimal.ONE))
        service.adjust(context, arg)
        then:
        noExceptionThrown()
        where:
        id << ["1.1.1", "1", "null.null.null"]
        adjustApiName << ["666", "best_practices_forecast_model1", "best_practices_forecast_model2"]
    }

    def "clearRuleAdjustedData"() {
        def serviceFacade = Mock(ServiceFacade)
        serviceFacade.findBySearchQueryWithFieldsIgnoreAll(_ as User, SFAPreDefineObject.ForecastTask.getApiName(), _ as SearchTemplateQuery, _ as List<String>) >> new QueryResult<IObjectData>(data: [new ObjectData("_id": "task1")])
        def service = new ForecastTaskService(serviceFacade: serviceFacade)
        when:
        service.clearRuleAdjustedData("1", "1")
        then:
        noExceptionThrown()
    }

    def "isSyncDisable"() {
        def forecastTaskProducer = Mock(ForecastTaskProducer)
        def service = new ForecastTaskService(forecastTaskProducer: forecastTaskProducer)
        when:
        service.isSyncDisable(context)
        then:
        noExceptionThrown()
    }

    def "removeSyncEnableCache"() {
        def forecastTaskProducer = Mock(ForecastTaskProducer)
        def service = new ForecastTaskService(forecastTaskProducer: forecastTaskProducer)
        when:
        service.removeSyncEnableCache(context)
        then:
        noExceptionThrown()
    }
}
