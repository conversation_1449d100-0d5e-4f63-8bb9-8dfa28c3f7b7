<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>fs-crm-sfa</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.0-SNAPSHOT</version>
    </parent>

    <artifactId>fs-crm-paas-ext</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>
        <!--    <dependency>-->
        <!--        <groupId>com.github.colin-lee</groupId>-->
        <!--        <artifactId>mybatis-spring-support</artifactId>-->
        <!--        <version>5.0.7-SNAPSH0T</version>-->
        <!--    </dependency>-->
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>0.3.2-patch11</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.athaydes</groupId>
                    <artifactId>spock-reports</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
    </dependencies>
</project>
