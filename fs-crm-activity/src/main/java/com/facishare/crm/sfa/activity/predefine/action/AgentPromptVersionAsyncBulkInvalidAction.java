package com.facishare.crm.sfa.activity.predefine.action;

import com.alibaba.fastjson2.JSONObject;
import com.facishare.crm.sfa.activity.predefine.service.AgentPromptVersionService;
import com.facishare.paas.appframework.core.predef.action.StandardAsyncBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

@Slf4j
public class AgentPromptVersionAsyncBulkInvalidAction extends StandardAsyncBulkInvalidAction {

    protected AgentPromptVersionService agentPromptVersionService = SpringUtil.getContext().getBean(AgentPromptVersionService.class);

    @Override
    protected Result doAct(StandardBulkInvalidAction.Arg arg) {
        dataList.forEach(o -> {
            if (!ObjectUtils.isEmpty(o.get("rule"))) {
                String aplApiName = "";
                try {
                    JSONObject jsonObject = JSONObject.parseObject(o.get("rule").toString());
                    aplApiName = jsonObject.getJSONObject("APL").getString("function_api_name");
                } catch (Exception e) {
                    log.warn("json error");
                }
                if (!ObjectUtils.isEmpty(aplApiName)) {
                    agentPromptVersionService.delete(actionContext.getTenantId(), o.getId(), aplApiName);
                }
            }
        });
        return super.doAct(arg);
    }

}
