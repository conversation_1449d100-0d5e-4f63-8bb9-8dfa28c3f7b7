package com.facishare.crm.sfa.activity.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpClientConfig;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.rest.StoneAuthProxy;
import com.facishare.crm.rest.dto.StoneAuthModels;
import com.facishare.crm.sfa.activity.predefine.ActivityPredefineObject;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityTaskMessage;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.crm.sfa.activity.predefine.service.model.TaskAIProxyModel;
import com.facishare.crm.sfa.activity.proxy.TaskAIProxy;
import com.facishare.crm.sfa.lto.activity.enums.ActivityResourceType;
import com.facishare.crm.sfa.lto.activity.enums.TaskStatusEnum;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoDao;
import com.facishare.crm.sfa.lto.activity.mongo.ActivityMongoParamDao;
import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.crm.sfa.lto.activity.producer.ActivityMessage;
import com.facishare.crm.sfa.lto.activity.producer.ActivityRocketProducer;
import com.facishare.crm.sfa.lto.activity.service.ActivityResourceUsageService;
import com.facishare.crm.sfa.lto.activity.service.ActivityTaskStateService;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.HttpHeaderUtil;
import com.facishare.crm.sfa.lto.utils.ProxyConfigUtil;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.social.personnel.PersonnelObjService;
import com.facishare.social.personnel.model.FindByUserId;
import com.facishare.social.personnel.model.PersonnelDto;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/5 17:22
 * @description: 阿里云听悟服务接口
 */
@ServiceModule("activity_text")
@Component
@Slf4j
public class Activity2TextService {

    @Resource
    private ActivityMongoDao activityMongoDao;

    @Resource
    private ActivityTaskStateService activityTaskStateService;

    @Resource
    private StoneAuthProxy stoneAuthProxy;

    @Resource
    private NomonProducer nomonProducer;

    @Resource
    protected GDSHandler gdsHandler;

    @Resource
    private IObjectDataService objectDataService;

    @Resource
    private ActivityRocketProducer activityRocketProducer;

    @Resource
    private PersonnelObjService personnelObjService;

    @Resource
    private TaskAIProxy taskAIProxy;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    ActivityRelatedObjectService activityRelatedObjectService;

    @Resource
    ActivityUserService activityUserService;

    @Resource
    InteractiveDocumentService interactiveDocumentService;

    @Resource
    private ActivityResourceUsageService activityResourceUsageService;

    @Resource
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    @Resource
    private ActivityGeneralService activityGeneralService;


    @ServiceMethod("full_text_list")
    public ActivityText.ActivityTextResult getFullTextList(ServiceContext context, ActivityText.ActivityTextQueryArg arg) {
        ActivityText.ActivityTextResult result = ActivityText.ActivityTextResult.
                builder().code(200).build();
        List<Map<String, Object>> fullInteractiveDocumentList = interactiveDocumentService.getFullInteractiveDocumentList(context, arg);
        result.setDataList(fullInteractiveDocumentList);
        result.setAvatarColorList(ActivityUserService.AVATAR_BG_COLORS);
        return result;
    }


    /**
     * 给函数使用的，填充更多信息的互动语料
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("get_interactive_content")
    public ActivityText.ActivityTextResult getInteractiveContent(ServiceContext context, ActivityText.GetInteractiveContentArg arg) {
        ActivityText.ActivityTextResult result = ActivityText.ActivityTextResult.
                builder().code(200).build();
        List<Map<String, Object>> filteredInteractiveContentList = interactiveDocumentService.getInteractiveContent(context, arg);
        result.setDataList(filteredInteractiveContentList);
        result.setAvatarColorList(ActivityUserService.AVATAR_BG_COLORS);
        return result;
    }


    /**
     * 开始实时转写任务
     *
     * @return 任务信息
     * https://help.aliyun.com/zh/tingwu/api-tingwu-2023-09-30-createtask?spm=a2c4g.11186623.help-menu-454189.d_2_2_3_0.1bc85041B1GiJO
     */
    @ServiceMethod("realtime_start")
    public TaskResult startRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStartArg arg) {
        TaskResult result = new TaskResult();
        StoneAuthModels.TingWuCredentialData credentialData = getTingWuCredential(context);
        // 创建请求
        CommonRequest request = createCommonRequest(credentialData);
        request.putQueryParameter("type", "realtime");

        // 构建请求体
        JSONObject root = new JSONObject();
        root.put("AppKey", credentialData.getAppKey());

        // 设置输入参数
        JSONObject input = new JSONObject();
        input.put("SourceLanguage", arg.getSourceLanguage());
        input.put("Format", "pcm");
        input.put("SampleRate", 16000);
        input.put("TaskKey", "task" + System.currentTimeMillis());


        // 设置转写参数
        JSONObject parameters = new JSONObject();
        String ossPath = "";

        // input.put("MultipleStreamsEnabled", true);
        // oss://${transResultOssBucket}/tingwu/${ea}/${yyyyMM}/${dd}/${employeeId}/${customPath}
        String dontSaveRealTimeRecordAudio = bizConfigThreadLocalCacheService.getBizConfig(context.getTenantId(), ConfigType.DONT_SAVE_REAL_TIME_RECORD_AUDIO.getKey());
        // 如果dontSaveRealTimeRecordAudio为0，用户没有勾选配置，则保存录音到oss
        if("0".equals(dontSaveRealTimeRecordAudio)) {
            ossPath = buildOssPath(context, credentialData);
            input.put("OutputPath", ossPath);
            JSONObject transcoding = new JSONObject();
            transcoding.put("TargetAudioFormat", "mp3");
            transcoding.put("SpectrumEnabled", false);
            parameters.put("Transcoding", transcoding);
        }
        root.put("Input", input);


        JSONObject transcription = new JSONObject();
        transcription.put("OutputLevel", 2); // 返回中间结果
        transcription.put("RealtimeDiarizationEnabled", arg.isDiarizationEnabled());
        transcription.put("AdditionalStreamOutputLevel",2);
        parameters.put("Transcription", transcription);

        parameters.put("TranslationEnabled", arg.isTranslationEnabled());

        JSONObject translation = new JSONObject();
        translation.put("TargetLanguages", Lists.newArrayList(arg.getTargetLanguages()));
        /*
            设置实时语言翻译识别结果返回等级。默认值是 1。
            1：识别出完整句子时返回识别结果；
            2：识别出中间结果及完整句子时返回识别结果。
            仅在实时记录场景下按需设置，离线转写场景无须设置
            */
        translation.put("OutputLevel",2);
        /*
            设置实时记录场景下活跃说话人对应的语言翻译识别结果返回等级。
            1：识别出完整句子时返回识别结果；
            2：识别出中间结果及完整句子时返回识别结果；
            仅在实时记录场景下且 MultipleStreamsEnabled=true 时按需设置，离线转写场景无须设置。
            */
        translation.put("AdditionalStreamOutputLevel",2);

        parameters.put("Translation", translation);
        root.put("Parameters", parameters);

        // 设置请求内容
        request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);

        // 发送请求
        CommonResponse response = null;
        try {
            response = getCommonResponse(request, credentialData);
        } catch (Exception e) {
            log.error("Failed to start realtime transcription", e);
        }

        // 解析响应
        JSONObject responseBody = JSONObject.parseObject(response.getData());
        JSONObject data = responseBody.getJSONObject("Data");
        String taskId = data.getString("TaskId");
        result.setWsUrl(data.getString("MeetingJoinUrl"));
        result.setTaskId(taskId);
        sendTaskId(context, arg, taskId, ossPath);
        saveResourceUsage(context.getTenantId(), arg.getObjectId(), StringUtils.isNotEmpty(arg.getTargetLanguages()));
        return result;
    }


    /**
     * 保存会议转写结果
     *
     * @param context
     * @param meetingDocResult
     */
    @ServiceMethod("save_meeting_doc_result")
    public ActivityText.Result saveMeetingDocResult(ServiceContext context, ActivityText.SaveMeetingDocResult meetingDocResult) {
        try {
            String language = context.getLang().getValue();
            if ("fs-crm-meeting".equals(context.getPeerName())) {
                language = getLanguage(context);
            }
            correct(context, meetingDocResult);
            List<InteractiveDocument> interactiveDocuments = convertToInteractiveDocument(context.getTenantId(), meetingDocResult);
            activityUserService.upsertActivityUser(context, meetingDocResult,interactiveDocuments);
            activityMongoDao.batchInsert(context.getTenantId(), interactiveDocuments);
            String existContent = getExistContent(context, meetingDocResult.getObjectId());
            StringBuilder sb = new StringBuilder(existContent + "\n");
            interactiveDocuments.forEach(document -> {
                sb.append(document.getContent());
            });
            updateActivityText(context.getTenantId(), meetingDocResult.getObjectId(), meetingDocResult.getObjectApiName(), sb.toString());
            // 仅当 Content 列表中有 id 能被 5 整除时才发送 MQ
            boolean shouldSendMq = meetingDocResult.getContent().stream()
                    .anyMatch(content -> content.getId() % 5 == 0);
            if (shouldSendMq) {
                sendMQ(meetingDocResult.getObjectId(), context.getTenantId(), context.getUser().getUserId(), "realtime2text", "realtime2text", language);
            }
            List<Map<String, Object>> fullInteractiveDocumentList = interactiveDocumentService.getFullInteractiveDocumentForSave(context, meetingDocResult);
            return ActivityText.Result.builder()
                    .code(0)
                    .msg("success")
                    .success(true)
                    .dataList(fullInteractiveDocumentList)
                    .build();
        } catch (IllegalArgumentException e) {
            log.error("Invalid argument when saving meeting doc result. context: {}, meetingDocResult: {}", context, meetingDocResult, e);
            return ActivityText.Result.builder()
                    .code(400)
                    .msg("Invalid argument" + e.getMessage())
                    .success(false)
                    .build();
        } catch (Exception e) {
            log.error("Failed to save meeting doc result. context: {}, meetingDocResult: {}", context, meetingDocResult, e);
            return ActivityText.Result.builder()
                    .code(500)
                    .msg("system error")
                    .success(false)
                    .build();
        }
    }

    /**
     * 获取发言人换绑的候选数据列表
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("get_object_data_list")
    public ActivityText.SpeakerResult getObjectDataList(ServiceContext context, ActivityText.SpeakerDataListArg arg) {
        Map<String, List<ObjectDataDocument>> speakerObjectData = activityRelatedObjectService.getSpeakerObjectData(context, arg.getObjectId());
        ActivityText.SpeakerResult result = ActivityText.SpeakerResult.builder()
                .speakerObjectData(speakerObjectData)
                .build();
        return result;
    }


    /**
     * 批量更换发言人接口
     */
    @ServiceMethod("batch_change_speaker")
    public ActivityText.Result batchChangeSpeaker(ServiceContext context, ActivityText.BatchChangeSpeaker batchChangeSpeaker) {

        ActivityText.Result result = ActivityText.Result.builder()
                    .code(0)
                    .msg("success")
                    .success(true)
                    .build();
        try {
            log.info("batchChangeSpeaker, context:{}, batchChangeSpeaker:{}", context, batchChangeSpeaker);

            if (batchChangeSpeaker == null || batchChangeSpeaker.getReplaceInfos() == null
                    || batchChangeSpeaker.getReplaceInfos().isEmpty()) {
                throw new IllegalArgumentException("Replace parameters cannot be empty");
            }
            activityUserService.batchChangeSpeaker(context, batchChangeSpeaker);
            IObjectData objectData = findById(context, batchChangeSpeaker.getObjectId());
            String process = objectData.get("interactive_processes", String.class);
            if (StringUtils.isNotBlank(process) && !"1".equals(process)) {
                sendChangeSpeakerTask(context, batchChangeSpeaker.getObjectId());
            }
            sendChangeSpeakerDetailMsg(context,batchChangeSpeaker);
            log.info("batchChangeSpeaker completed, context:{}, batchChangeSpeaker:{}", context, batchChangeSpeaker);
            return result;
        } catch (IllegalArgumentException e) {
            log.error("Invalid argument when batch changing speaker. context: {}, batchChangeSpeaker: {}", context, batchChangeSpeaker, e);
            return ActivityText.Result.builder()
                    .code(400)
                    .msg("Invalid argument: " + e.getMessage())
                    .success(false)
                    .build();
        } catch (Exception e) {
            log.error("Failed to batch change speaker. context: {}, batchChangeSpeaker: {}", context, batchChangeSpeaker, e);
            return ActivityText.Result.builder()
                    .code(500)
                    .msg("system error")
                    .success(false)
                    .build();
        }
    }


    /**
     * 录音中更新发言人 userName
     * @param context
     * @param changeRecordingSpeaker
     * @return
     */
    @ServiceMethod("change_recording_speaker")
    public ActivityText.Result changeRecordingSpeaker(ServiceContext context, ActivityText.ChangeRecordingSpeaker changeRecordingSpeaker){
        ActivityMongoParamDao.RecordingReplaceParam param = new ActivityMongoParamDao.RecordingReplaceParam();
        param.setObjectId(changeRecordingSpeaker.getObjectId());
        param.setUserName(changeRecordingSpeaker.getUserName());
        param.setSeqIds(changeRecordingSpeaker.getSeqIds());
        activityMongoDao.recordingReplaceSpeaker(context.getTenantId(),changeRecordingSpeaker.getObjectId(), param);
        ActivityText.Result result = ActivityText.Result.builder()
                .code(0)
                .msg("success")
                .success(true)
                .build();
        return result;
    }


    /**
     * 修改语料内容
     * @param context
     * @param body
     * @return
     */
    @ServiceMethod("update_content")
    public ActivityText.UpdateContentResult updateContent(ServiceContext context, ActivityText.UpdateContentParam body){
        List<ActivityText.UpdateContentArg> arg = body.getArg();
        List<String> ids = arg.stream().map(ActivityText.UpdateContentArg::getId).collect(Collectors.toList());
        List<InteractiveDocument> interactiveDocumentList = activityMongoDao.queryByIds(context.getTenantId(), ids);

        Map<String,InteractiveDocument> interactiveDocumentMap = interactiveDocumentList.stream().collect(Collectors.toMap(x->x.getId().toString(), Function.identity()));
        List<ActivityMongoParamDao.UpdateContentParam> paramList = new ArrayList<>();
        List<String> needDeleteIds = new ArrayList<>();
        List<InteractiveDocument> returnList = new ArrayList<>();
        for(ActivityText.UpdateContentArg argItem :arg ){
            if(ObjectUtils.isEmpty(argItem.getContent()) && ObjectUtils.isEmpty(argItem.getTranslateContent()) ){
                needDeleteIds.add(argItem.getId());
                continue;
            }
            ActivityMongoParamDao.UpdateContentParam param = new ActivityMongoParamDao.UpdateContentParam();
            InteractiveDocument interactiveDocument = interactiveDocumentMap.get(argItem.getId());
            param.setId(argItem.getId());
            if(argItem.getContent()!=null){
                param.setContent(argItem.getContent());
                interactiveDocument.setContent(argItem.getContent());
                if(ObjectUtils.isEmpty(interactiveDocument.getOldContent())){
                    param.setOldContent(interactiveDocument.getContent());
                    interactiveDocument.setOldContent(interactiveDocument.getContent());
                }
            } else if(argItem.getTranslateContent()!=null){
                param.setTranslateContent(argItem.getTranslateContent());
                interactiveDocument.setTranslateContent(argItem.getTranslateContent());
                if(ObjectUtils.isEmpty(interactiveDocument.getOldTranslateContent())){
                    param.setOldTranslateContent(interactiveDocument.getTranslateContent());
                    interactiveDocument.setOldTranslateContent(interactiveDocument.getTranslateContent());
                }
            }else{
                throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
            }
            paramList.add(param);
            returnList.add(interactiveDocument);
        }

        //判断是修改的是什么
        if(CollectionUtil.isNotEmpty(paramList)){
            activityMongoDao.batchUpdateContent(context.getTenantId(),paramList);
        }
        if(CollectionUtil.isNotEmpty(needDeleteIds)){

            activityMongoDao.deleteByIds(context.getTenantId(),needDeleteIds);
        }
        ActivityText.UpdateContentResult result = new ActivityText.UpdateContentResult();
        result.setDocumentList(returnList.stream()
                .map(this::interactiveDocumentTransfMap)
                .collect(Collectors.toList()));
        return result;
    }


    @ServiceMethod("rollback_content")
    public ActivityText.UpdateContentResult rollBackContent(ServiceContext context, ActivityText.UpdateContentArg arg){
        InteractiveDocument interactiveDocument = activityMongoDao.queryById(context.getTenantId(), arg.getId());
        ActivityMongoParamDao.UpdateContentParam param = new ActivityMongoParamDao.UpdateContentParam();
        if("content".equals(arg.getRollBackField())){
            param.setContent(interactiveDocument.getOldContent());
            interactiveDocument.setContent(interactiveDocument.getOldContent());
        }else if("translateContent".equals(arg.getRollBackField())){
            param.setOldTranslateContent(interactiveDocument.getOldTranslateContent());
            interactiveDocument.setTranslateContent(interactiveDocument.getOldTranslateContent());
        }else {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }

        activityMongoDao.updateContent(context.getTenantId(),arg.getId(),param);
        ActivityText.UpdateContentResult result = new ActivityText.UpdateContentResult();
        result.setDocument(interactiveDocument);
        return result;
    }


    /**
     * 生成发言人唯一标识
     */
    private String generateSpeakerKey(String userName, String userId, String userApiName) {
        return String.format("%s_%s_%s", userName, userId, userApiName);
    }


    private List<InteractiveDocument> convertToInteractiveDocument(String tenantId, ActivityText.SaveMeetingDocResult meetingDocResult) {
        return meetingDocResult.getContent().stream().map(content -> {
            InteractiveDocument document = new InteractiveDocument();
            document.setObjectId(meetingDocResult.getObjectId());
            document.setObjectApiName(meetingDocResult.getObjectApiName());
            document.setOriginalUserName(StringUtils.isEmpty(content.getUname()) ? "user_"+content.getUi() : content.getUname());
            document.setUserName(StringUtils.isEmpty(content.getUname()) ? "user_"+content.getUi() : content.getUname());
            document.setUserId(content.getUi());
            document.setSeq(content.getSeq() != null ? content.getSeq(): content.getId());
            document.setTenantId(tenantId);
            document.setCreateTime(System.currentTimeMillis());
            document.setStartTime(content.getSt());
            document.setEndTime(content.getEt());
            document.setContent(content.getContent());
            document.setTranslateContent(content.getTranslateContent());
            document.setSpeakTime(content.getSpeakTime());
            document.setNameAvaId(content.getNameAvaId());
            document.setType(meetingDocResult.getSourceType());
            document.setDeleted(false);
            return document;
        }).collect(Collectors.toList());
    }

    /**
     * 停止实时转写任务
     *
     * @param arg 任务ID 等相关参数
     * @return 停止结果
     */
    @ServiceMethod("realtime_stop")
    public Map<String, String> stopRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStopArg arg) {
        Map<String, String> result = new HashMap<>();
        if (StringUtils.isBlank(arg.getTaskId())) {
            updateInteractiveProcesses(context.getUser(), arg.getObjectId());
            result.put("status", "success");
            return result;
        }
        result.put("status", "success");
        try {
            // 重置任务状态
            try {
                activityGeneralService.initEndRecordingState(context.getTenantId(), context.getUser().getUserId(), arg.getObjectId());
            } catch (Exception e) {
                log.error("initEndRecordingState error", e);
            }

            StoneAuthModels.TingWuCredentialData credentialData = getTingWuCredential(context);

            // 创建请求
            CommonRequest request = createCommonRequest(credentialData);
            request.putQueryParameter("type", "realtime");
            request.putQueryParameter("operation", "stop");

            // 构建请求体
            JSONObject root = new JSONObject();
            JSONObject input = new JSONObject();
            input.put("TaskId", arg.getTaskId());
            root.put("Input", input);
            // 设置请求内容
            request.setHttpContent(root.toJSONString().getBytes(), "utf-8", FormatType.JSON);
            // 发送请求
            CommonResponse commonResponse = getCommonResponse(request, credentialData);
            String queryUrl = String.format("/openapi/tingwu/v2/tasks/%s", arg.getTaskId());
            // 停止任务后，再次获取任务结果
            CommonRequest getRequest = createCommonRequest(credentialData,queryUrl,MethodType.GET);
            CommonResponse resultResponse = getCommonResponse(getRequest,credentialData);
            log.info("Get task result after stop: taskId={}, response={}", arg.getTaskId(), resultResponse.getData());
            sendMQ(arg.getObjectId(), context.getTenantId(), context.getUser().getUserId(), "realtime2textDone", "realtime2textDone", context.getLang().getValue());
            // sendTaskId(context, null, arg.getTaskId(), queryUrl);
            updateInteractiveProcesses(context.getUser(), arg.getObjectId());
            result.put("status", "success");
        } catch (Exception e) {
            log.error("停止实时转写任务失败", e);
        }
        return result;
    }

    private CommonRequest createCommonRequest(StoneAuthModels.TingWuCredentialData credentialData) {
        return createCommonRequest(credentialData,"/openapi/tingwu/v2/tasks",MethodType.PUT);
    }

    private CommonRequest createCommonRequest(StoneAuthModels.TingWuCredentialData credentialData, String uri,MethodType method) {
        CommonRequest request = new CommonRequest();
        request.setSysDomain(credentialData.getEndPoint());
        request.setSysVersion("2023-09-30");
        request.setSysProtocol(ProtocolType.HTTPS);
        request.setSysMethod(method);
        request.setSysUriPattern(uri);
        request.setHttpContentType(FormatType.JSON);
        return request;
    }

    private CommonResponse getCommonResponse(CommonRequest request, StoneAuthModels.TingWuCredentialData credentialData) throws Exception {
        DefaultProfile profile = DefaultProfile.getProfile(
                "cn-beijing",
                credentialData.getAccessKey(),
                credentialData.getSecretKey(), credentialData.getStsToken()
        );
        HttpClientConfig clientConfig = HttpClientConfig.getDefault();
        clientConfig.setHttpProxy("http://" + ProxyConfigUtil.getProxyHost()+":"+ProxyConfigUtil.getProxyPort());
        clientConfig.setHttpsProxy("http://" + ProxyConfigUtil.getProxyHost()+":"+ProxyConfigUtil.getProxyPort());
        profile.setHttpClientConfig(clientConfig);
        IAcsClient client = new DefaultAcsClient(profile);
        return client.getCommonResponse(request);
    }

    private StoneAuthModels.TingWuCredentialData getTingWuCredential(ServiceContext context) {
        Map<String, String> pathParams = Maps.newHashMap();
        pathParams.put("ea", gdsHandler.getEAByEI(context.getTenantId()));

        StoneAuthModels.GetTingWuCredentialResponse response = stoneAuthProxy.getTingWuCredential(HttpHeaderUtil.getHeaders(context.getTenantId()), pathParams);
        return response.getData();
    }

    private void upsertTaskState(String tenantId, String targetApiName, String dataId, String taskId) {
        IObjectData objectData = activityTaskStateService.getObjectData(tenantId, targetApiName, dataId, taskId);
        if (objectData == null) {
            activityTaskStateService.insOrUpdate(tenantId, targetApiName, dataId, TaskStatusEnum.ONGOING, taskId);
        } else {
            activityTaskStateService.insOrUpdate(tenantId, targetApiName, dataId, TaskStatusEnum.ONGOING, taskId);
        }
    }


    private void sendChangeSpeakerTask(ServiceContext context, String objectId) {
        ActivityTaskMessage.Rec2TextTask rec2TextTask = ActivityTaskMessage.Rec2TextTask.builder()
                .tenantId(context.getTenantId())
                .objectId(objectId)
                .objectApiName("ActiveRecordObj")
                .actionCode("changeSpeaker")
                .stage("changeSpeaker")
                .createTime(System.currentTimeMillis())
                .opId(context.getUser() == null ? "" : context.getUser().getUpstreamOwnerIdOrUserId())
                .language(context.getLang().getValue())
                .build();
        Date executeTime = new Date(System.currentTimeMillis() + 2 * 60 * 1000); // 2分钟后执行
        NomonMessage message = NomonMessage.builder()
                .biz("activity-text-task")
                .tenantId(rec2TextTask.getTenantId())
                .dataId(rec2TextTask.getObjectId()+"_changeSpeaker")
                .taskId(objectId)
                .executeTime(executeTime)
                .callArg(JSON.toJSONString(rec2TextTask))
                .build();
        nomonProducer.send(message);
    }

    private void sendChangeSpeakerDetailMsg(ServiceContext context, ActivityText.BatchChangeSpeaker batchChangeSpeaker) {
        try {
            ActivityTaskMessage.ChangeSpeakerMsg msg = new ActivityTaskMessage.ChangeSpeakerMsg();
            msg.setObjectId(batchChangeSpeaker.getObjectId());
            msg.setObjectApiName("ActiveRecordObj");
            msg.setTenantId(context.getTenantId());
            List<ActivityTaskMessage.ChangeSpeakerDetail> detailList = new ArrayList<>();
            batchChangeSpeaker.getReplaceInfos().stream().forEach(info -> {
                ActivityTaskMessage.ChangeSpeakerDetail detail = new  ActivityTaskMessage.ChangeSpeakerDetail();
                detail.setDocId(info.getDocId());
                detail.setOldActivityUserId(info.getActivityUserId());
                detail.setTargetUserId(info.getTargetUserId());
                detail.setTargetUserApiName(info.getTargetUserApiName());
                detailList.add(detail);
            });
            msg.setDetails(detailList);
            activityRocketProducer.sendMessage("sfa-change-speaker-tag",(JSONObject) com.alibaba.fastjson.JSON.toJSON(msg),context.getTenantId());
        }catch (Exception e){
            log.error("sendChangeSpeakerDetailMsg error:",e);
        }
    }
    private void sendTaskId(ServiceContext context, ActivityText.ActivityRealtimeStartArg arg, String taskId, String ossPath) {
        ActivityTaskMessage.Rec2TextTask rec2TextTask = ActivityTaskMessage.Rec2TextTask.builder()
                .tenantId(context.getTenantId())
                .objectId(arg.getObjectId())
                .objectApiName("ActiveRecordObj")
                .actionCode("recording")
                .stage("recording")
                .ossPath(ossPath)
                .createTime(System.currentTimeMillis())
                .opId(context.getUser() == null ? "" : context.getUser().getUpstreamOwnerIdOrUserId())
                .taskId(taskId)
                .language(context.getLang().getValue())
                .build();
        Date executeTime = new Date(System.currentTimeMillis() + 2 * 60 * 1000); // 2分钟后执行
        NomonMessage message = NomonMessage.builder()
                .biz("activity-text-task")
                .tenantId(rec2TextTask.getTenantId())
                .dataId(rec2TextTask.getObjectId())
                .executeTime(executeTime)
                .callArg(JSON.toJSONString(rec2TextTask))
                .build();
        nomonProducer.send(message);
    }

    private String buildOssPath(ServiceContext context, StoneAuthModels.TingWuCredentialData credentialData) {
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        String yyyyMM = new SimpleDateFormat("yyyyMM").format(calendar.getTime());
        String dd = new SimpleDateFormat("dd").format(calendar.getTime());

        // 获取 EA
        String ea = gdsHandler.getEAByEI(context.getTenantId());

        // 获取员工ID
        String employeeId = context.getUser() != null ? context.getUser().getUserId() : "system";

        // 构建自定义路径（使用时间戳作为唯一标识）
        String customPath = String.valueOf(System.currentTimeMillis());

        // 按照规则拼接路径
        return String.format("oss://%s/tingwu/%s/%s/%s/%s/%s",
                credentialData.getTransResultOssBucket(),
                ea,
                yyyyMM,
                dd,
                employeeId,
                customPath);
    }

    @Data
    public static class TaskResult {
        private String wsUrl;
        private String taskId;
    }


    private String getExistContent(ServiceContext context, String objectId){
        IObjectData objectData = findById(context, objectId);
        Object content = objectData.get("interactive_content");
        if (content == null) {
            return "";
        }
        return content.toString();
    }

    public IObjectData findById(ServiceContext context, String objectId) {
        IActionContext actionContext = ActionContextExt.of(context.getUser()).getContext();
        actionContext.put(ActionContextKey.SEARCH_RICH_TEXT_EXTRA, true);
        IObjectData accountObj;
        try {
            accountObj = objectDataService.findById(objectId, context.getTenantId(), actionContext, "ActiveRecordObj");
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
        return accountObj;
    }


    public void updateActivityText(String tenantId, String objectId, String objectApiName, String fullText) {
        IActionContext context = new ActionContext();
        context.setEnterpriseId(tenantId);
        context.setUserId("-10000");
        IObjectData data = new ObjectData();
        data.setTenantId(tenantId);
        data.setId(objectId);
        data.setDescribeApiName(objectApiName);
        data.set("interactive_content__e", fullText);
        data.set("interactive_content", fullText);
        List<String> updateFieldList = new ArrayList<>();
        updateFieldList.add("interactive_content");
        updateFieldList.add("interactive_content__e");
        try {
            objectDataService.batchUpdateIgnoreOther(Lists.newArrayList(data), updateFieldList, context);
        } catch (MetadataServiceException e) {
            log.error("更新活动文本失败, tenantId:{}, objectId:{}, objectApiName:{}", tenantId, objectId, objectApiName, e);
        }
    }

    /**
     * 更新活动路径接口
     * @param context 服务上下文
     * @param arg 更新活动路径参数
     * @return 更新结果
     */
    @ServiceMethod("update_activity_path")
    public ActivityText.UpdateActivityPathResult updateActivityPath(ServiceContext context, ActivityText.UpdateActivityPathArg arg) {
        try {
            updateActivityPathInternal(context.getTenantId(), arg.getObjectId(), 
                                     arg.getPath(), arg.getFileName(), arg.getFileSize());
            
            return ActivityText.UpdateActivityPathResult.builder()
                    .code(200)
                    .msg("success")
                    .success(true)
                    .build();
        } catch (Exception e) {
            log.error("更新活动路径失败, context:{}, arg:{}", context, arg, e);
            return ActivityText.UpdateActivityPathResult.builder()
                    .code(500)
                    .msg("更新活动路径失败: " + e.getMessage())
                    .success(false)
                    .build();
        }
    }

    /**
     * 内部方法：更新活动路径
     */
    private void updateActivityPathInternal(String tenantId, String objectId, 
                                          String path, String fileName, Long fileSize) {
        IActionContext context = new ActionContext();
        context.setEnterpriseId(tenantId);
        context.setUserId("-10000");
        IObjectData data = new ObjectData();
        data.setTenantId(tenantId);
        data.setId(objectId);
        data.setDescribeApiName(ActivityPredefineObject.ActiveRecord.getApiName());
        List<Map<String, Object>> interactionRecords = new ArrayList<>();
        Map<String, Object> record = new HashMap<>();
        record.put("ext", "mp3");
        record.put("path", path);
        record.put("filename", fileName);
        record.put("create_time", System.currentTimeMillis());
        record.put("size", fileSize);
        interactionRecords.add(record);
        data.set("interaction_records", JSON.toJSONString(interactionRecords));
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("interaction_records", interactionRecords);
        try {
            serviceFacade.batchUpdateWithMap(new User(tenantId, "-10000"), Lists.newArrayList(data), updateMap);
        } catch (Exception e) {
            log.error("更新活动文本失败，tenantId:{}, objectId:{}:{}", tenantId, objectId, e);
            throw e; // 重新抛出异常以便上层处理
        }
    }

    private void sendMQ(String objectId, String tenantId, String opId,String actionCode, String stage,String language){
        ActivityMessage activityMessage = ActivityMessage.builder()
                .tenantId(tenantId)
                .objectId(objectId)
                .sourceId("")
                .sourceApiName("")
                .objectApiName("ActiveRecordObj")
                .actionCode(actionCode)
                .stage(stage)
                .interactiveTypes("")
                .language(language)
                .opId(opId)
                .build();
        activityRocketProducer.sendActivityToTextMessage(activityMessage);
    }

    private String getLanguage(ServiceContext context) {
        FindByUserId.Argument argument = new FindByUserId.Argument();
        argument.setTenantId(context.getTenantId());
        argument.setUserId(context.getUser().getUserId());
        Optional<PersonnelDto> ret = personnelObjService.findByUserIdUseCacheChain(argument);
        return ret.map(PersonnelDto::getLanguage)
                .filter(StringUtils::isNotBlank)
                .orElse(null);
    }

    private void correct(ServiceContext context, ActivityText.SaveMeetingDocResult meetingDocResult) {
        try {
            serviceFacade.findObject(context.getTenantId(), "TermBankObj");
        } catch (Exception e) {
            return;
        }
        List<ActivityText.Content> content = meetingDocResult.getContent();
        List<String> text = content.stream().map(ActivityText.Content::getContent).collect(Collectors.toList());
        try {
            List<String> corrected = taskAIProxy.correct(new TaskAIProxyModel.CorrectArg(text), TaskAIProxy.getHeaders(context.getUser()));
            for (int i = 0; i < content.size(); i++) {
                content.get(i).setContent(corrected.get(i));
            }
        } catch (Exception e) {
            log.error("correct error", e);
        }
    }

    private void updateInteractiveProcesses(User user, String objectId) {
        try {
            List<IFilter> filters = new ArrayList<>();
            SearchUtil.fillFilterEq(filters, DBRecord.ID, objectId);
            SearchUtil.fillFilterNotIn(filters, "interactive_processes", Lists.newArrayList("2", "4"));
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setFilters(filters);
            query.setLimit(1);
            query.setPermissionType(0);
            List<IObjectData> dataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, Utils.ACTIVE_RECORD_API_NAME, query, Lists.newArrayList(DBRecord.ID, Tenantable.TENANT_ID, IObjectData.DESCRIBE_API_NAME, "interactive_processes")).getData();
            if (dataList.isEmpty()) {
                return;
            }
            IObjectData data = dataList.get(0);
            data.set("interactive_processes", "2");
            serviceFacade.batchUpdateByFields(user, Collections.singletonList(data), Collections.singletonList("interactive_processes"));
        } catch (Exception e) {
            log.error("updateInteractiveProcesses error", e);
        }
    }

    public Map<String, Object> interactiveDocumentTransfMap(InteractiveDocument doc){
        Map<String, Object> map = new HashMap<>();
        map.put("id", doc.getId().toString());
        map.put("objectId", doc.getObjectId());
        map.put("objectApiName", doc.getObjectApiName());
        map.put("originalUserName", doc.getOriginalUserName());
        map.put("userName", doc.getUserName());
        map.put("userApiName", doc.getUserApiName());
        map.put("nameAvaId", doc.getNameAvaId());
        map.put("userId", doc.getUserId());
        map.put("seq", doc.getSeq());
        map.put("tenantId", doc.getTenantId());
        map.put("createTime", doc.getCreateTime());
        map.put("startTime", doc.getStartTime());
        map.put("endTime", doc.getEndTime());
        map.put("content", doc.getContent());
        map.put("translateContent", doc.getTranslateContent());
        map.put("speakTime", doc.getSpeakTime());
        return map;
    }

    private void saveResourceUsage(String tenantId, String objectId, boolean translate) {
        List<ActivityResourceType> types = new ArrayList<>();
        types.add(ActivityResourceType.RECORDING);
        if (translate) {
            types.add(ActivityResourceType.RECORDING_TRANSLATION);
        }
        activityResourceUsageService.add(tenantId, objectId, Utils.ACTIVE_RECORD_API_NAME, types, 0);
    }
}
