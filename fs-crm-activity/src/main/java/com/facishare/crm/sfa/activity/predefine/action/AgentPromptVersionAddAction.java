package com.facishare.crm.sfa.activity.predefine.action;

import com.alibaba.fastjson2.JSONObject;
import com.facishare.crm.sfa.activity.predefine.ActivityPredefineObject;
import com.facishare.crm.sfa.activity.predefine.service.AgentPromptVersionService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_CONTACT_PARAM_EXCEPTION;

@Slf4j
public class AgentPromptVersionAddAction extends StandardAddAction {

    protected AgentPromptVersionService agentPromptVersionService = SpringUtil.getContext().getBean(AgentPromptVersionService.class);

    private static Integer COUNT = 50;

    static {
        ConfigFactory.getInstance().getConfig("fs-sfa-ai", config -> {
            COUNT = config.getInt("maxAgentPromptVersionObjCount", 50);
        });
    }

    @Override
    protected void before(Arg arg) {
        // 检查提示词
        Object templateApiName = arg.getObjectData().get("subtemplate_api_name");
        if (ObjectUtils.isEmpty(templateApiName) || "[]".equals(templateApiName)) {
            throw new ValidateException(I18N.text(SFA_CONTACT_PARAM_EXCEPTION));
        }
        JSONObject templateObject = JSONObject.parseObject(templateApiName.toString());
        if (ObjectUtils.isEmpty(templateObject.get("promptApiName"))) {
            throw new ValidateException(I18N.text(SFA_CONTACT_PARAM_EXCEPTION));
        }
        // 检查个数
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFindExplicitTotalNum(true);
        searchTemplateQuery.setNeedReturnCountNum(true);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(
                User.systemUser(actionContext.getTenantId()),
                ActivityPredefineObject.AgentPromptVersion.getApiName(),
                searchTemplateQuery
        );
        if (COUNT <= queryResult.getTotalNumber()) {
            throw new ValidateException(I18N.text("sfa.activity.agent.max.count.msg", COUNT));
        }
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = super.doAct(arg);
        if (!ObjectUtils.isEmpty(arg.getObjectData().get("rule"))){
            String aplApiName = "";
            try {
                JSONObject jsonObject = JSONObject.parseObject(arg.getObjectData().get("rule").toString());
                aplApiName = jsonObject.getJSONObject("APL").getString("function_api_name");
            }catch (Exception e){
                log.warn("json error");
            }
            if (!ObjectUtils.isEmpty(aplApiName)){
                agentPromptVersionService.create(actionContext.getTenantId(), result.getObjectData().getId(), aplApiName);
            }
        }
        return result;
    }

}
