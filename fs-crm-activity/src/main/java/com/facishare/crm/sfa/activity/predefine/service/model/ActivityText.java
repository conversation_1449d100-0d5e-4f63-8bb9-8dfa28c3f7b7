package com.facishare.crm.sfa.activity.predefine.service.model;

import com.facishare.crm.sfa.lto.activity.mongo.InteractiveDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/12/12 19:12
 * @description:
 */
public interface ActivityText {



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ActivityRealtimeStartArg{

        /**
         * 销售记录 ID
         */
        private String  objectId;

        /**
         * 必选。根据音视频中的语种类别来配置对应的参数。
         * 若语音的语种是单语种，音视频文件对应的语言种类；支持cn（中文）、en（英文）、粤语（yue）、日语（ja）、韩语（ko）。
         * 若语音中的语种非单语种，涉及多个语种，可传入（multilingual），识别出对应语种的文字。结合Input.LanguageHints一起使用。
         */
        private String sourceLanguage;
        /**
         * 是否开启翻译功能。sourceLanguage
         */
        private boolean translationEnabled;
        /**
         * 如果开启翻译，需要设置目标翻译语言。支持设置 中文（cn）、英语（en）、日语（ja）、韩语（ko）、德语（de）、法语（fr）、俄语（ru）。
         */
        private String targetLanguages;
        /**
         * 区分发言人 是否在语音识别过程中开启说话人分离功能。
         */
        private boolean diarizationEnabled;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ActivityRealtimeStopArg{
        /**
         * 销售记录 ID
         */
        private String  objectId;

        /**
         *
         */
        private String taskId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ActivityTextQueryArg{
        private String objectId;
        private Integer offset;
        private Integer limit;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ActivityTextResult{
        private Integer code;
        private String msg;
        private List<Map<String, Object>> dataList;
        private List<String> avatarColorList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class RealtimeStopArg{
        private String taskId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class SaveMeetingDocResult{
        private String objectId;
        private String objectApiName;
        private String taskId;
        private List<Content> content;
        private String sourceType;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Content{
        private Long seq;
        //order id
        private Long id;
        private String pi;
        //open id
        private String ui;
        //用户名称
        private String uname;
        //开始时间
        private String st;
        private String et;
        private String content;
        // 翻译后内容
        private String translateContent;
        // 会议发言时间
        private Long speakTime;
        // nameAvaId
        private Integer nameAvaId;

        // 是否是系统识别的默认发言人
        private Boolean isDefaultSpeaker;

        private String userName;

        private String userApiName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ChangeRecordingSpeaker {
        private String objectId;
        /*
         seqId
         */
        private List<Long> seqIds;

        private String userName;
    }


    enum SpeakerOpType{
        BIND("bind"),
        MERGE("merge"),
        EDIT("edit");

        String value;

        SpeakerOpType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class BatchChangeSpeaker {
        private String objectId;
        // 绑定，合并，修改
        // bind, merge, edit
        private String opType;
        private Boolean isGlobal;
        // 替换规则列表
        private List<SpeakerReplaceInfo> replaceInfos;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class SpeakerReplaceInfo {
        private String docId;
        private String UserName;
        private String UserId;
        private String UserApiName;
        private String targetUserName;
        private String targetUserId;
        private String targetUserApiName;
        private Integer targetNameAvaId;
        private String activityUserId;
        private String targetActivityUserId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class BatchReplaceParam {
        private String objectId;
        private String userName;
        private String userId;
        private String userApiName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result{
        private Integer code;
        private String msg;
        private boolean success;
        private List<Map<String, Object>> dataList;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class UpdateContentArg {
        private String id;
        /**content,translateContent **/
        private String rollBackField;
        private String content;
        private String translateContent;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class UpdateContentParam {
        private List<UpdateContentArg> arg;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class UpdateContentResult {
        private InteractiveDocument document;
        private List<Map<String,Object>> documentList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class SpeakerDataListArg{
        private String objectId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class SpeakerResult{
        private Map<String, List<ObjectDataDocument>> speakerObjectData;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class SpeakerObjectData{
        private List<IObjectData> dataList;
        private String objectApiName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class GetInteractiveContentArg{
        private String objectId;
        private List<String> personnelIds;
        private List<String> contactIds;
        private List<String> publicEmployeeIds;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class UpdateActivityPathArg {
        /**
         * 对象ID
         */
        private String objectId;
        
        /**
         * 文件路径
         */
        private String path;
        
        /**
         * 文件大小
         */
        private Long fileSize;
        
        /**
         * 文件名
         */
        private String fileName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class UpdateActivityPathResult {
        /**
         * 返回码
         */
        private Integer code;
        
        /**
         * 返回消息
         */
        private String msg;
        
        /**
         * 是否成功
         */
        private boolean success;
    }
}


