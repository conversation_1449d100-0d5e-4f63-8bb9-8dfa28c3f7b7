package com.facishare.crm.sfa.activity.predefine.action;

import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.util.List;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

public class AgentPromptVersionChangeStatusAction extends AbstractStandardAction<AgentPromptVersionChangeStatusAction.Arg, AgentPromptVersionChangeStatusAction.Result> {

    /** 状态字段 */
    public static String PROMPT_STATUS = "prompt_status";

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.CHANGE_STATUS.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getObjectIds();
    }

    @Override
    protected void before(Arg arg) {
        if (ObjectUtils.isEmpty(arg.getObjectIds()) || ObjectUtils.isEmpty(arg.getIsEnabled())) {
            throw new ValidateException(I18N.text(SFA_CONTACT_PARAM_EXCEPTION));
        }
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        if (ObjectUtils.isEmpty(dataList)) {
            return AgentPromptVersionChangeStatusAction.Result.builder().success(false).message(I18N.text(SFA_SYS_ERROR_MSG)).build();
        }
        dataList.forEach(o -> o.set(PROMPT_STATUS, Boolean.TRUE.equals(arg.getIsEnabled()) ? "enable" : "disable"));
        try {
            serviceFacade.batchUpdateByFields(actionContext.getUser(), dataList, Lists.newArrayList(PROMPT_STATUS));
        } catch (Exception e) {
            log.error("update error:",e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
        return AgentPromptVersionChangeStatusAction.Result.builder().success(true).message(I18N.text(SFA_SUCESSFUL)).build();
    }

    @Data
    public static class Arg {
        private List<String> objectIds;
        private Boolean isEnabled;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String message;
        private boolean success;
    }

}
