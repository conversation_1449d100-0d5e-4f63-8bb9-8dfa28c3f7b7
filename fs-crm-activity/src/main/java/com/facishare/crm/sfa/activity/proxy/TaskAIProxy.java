package com.facishare.crm.sfa.activity.proxy;

import com.facishare.crm.sfa.activity.predefine.service.model.TaskAIProxyModel;
import com.facishare.crm.util.RestUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.List;
import java.util.Map;

@RestResource(value = "FS-CRM-TASK-SFA-AI", desc = "SFA-TASK-AI服务", contentType = "application/json")
public interface TaskAIProxy {

    @POST(value = "/term_bank/correct", desc = "术语库纠正")
    List<String> correct(@Body TaskAIProxyModel.CorrectArg arg, @HeaderMap Map<String, String> headers);

    static Map<String, String> getHeaders(User user) {
        return RestUtils.getDDSHeaders(user);
    }
}
