package com.facishare.crm.sfa.activity.predefine.service.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.Data;

import java.util.List;

public interface ContactInsightModel {
    @Data
    class Arg{
        private String objectApiName;
        private String objectDataId;
    }
    @Data
    class Result{
        private ObjectDataDocument objectData;
        private boolean updatePrivilege;
        private ObjectDescribeDocument objectDescribe;
    }
    @Data
    class SetSwitchArg{
        private String objectApiName;
        private String objectDataId;
        private String fieldApiName;
        private boolean value;
    }@Data
    class SetSwitchResult{
        private String objectApiName;
        private String objectDataId;
        private String fieldApiName;
        private boolean value;
    }
    @Data
    class UpdateArg{
        private ObjectDataDocument objectData;
        private List<String> updateFieldList;
    }
}
