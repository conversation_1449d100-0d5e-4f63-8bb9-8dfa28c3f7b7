package com.facishare.crm.sfa.activity.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.ActivityQuestionConstants;
import com.facishare.crm.constants.CRMFeedConstants;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.sfa.activity.predefine.service.model.InteractQuestionModel;
import com.facishare.crm.sfa.activity.predefine.service.model.InteractionStrategyTask;
import com.facishare.crm.sfa.cache.RedisDataAccessor;
import com.facishare.crm.sfa.lto.activity.producer.ActivityRocketProducer;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lik
 * @date : 2024/12/4 17:03
 */
@ServiceModule("interact_question")
@Component
@Slf4j
public class InteractQuestionService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private LicenseClient licenseClient;
    @Autowired
    private ConfigService configService;
    @Autowired
    private RedisDataAccessor sfaRedisService;
    @Autowired
    private ActivityRocketProducer activityRocketProducer;
    @Autowired
    private CreateStrategyTaskService createStrategyTaskService;

    public static final String AI_SUGGESTION_KEY = "get_ai_suggestion";
    public static final String ACCOUNT_ID = "account_id";
    public static final String NEW_OPPORTUNITY_ID = "new_opportunity_id";
    public static final String ACCOUNT_OBJ = "AccountObj";
    public static final String NEWOPPORTUNITY_OBJ = "NewOpportunityObj";
    public static final String ACTIVITY_QUESTION_OBJ = "ActivityQuestionObj";
    public static final String LEADS_OBJ = "LeadsObj";
    public static final String LEADS_ID = "leads_id";
    public static final String QUESTION_USER = "question_user";
    public static final String ANSWER_USER = "answer_user";
    public static final String ADVICE_STATUS = "advice_status";
    public static final String PROPOSER_ATTITUDE = "proposer_attitude";
    public static final String TAGS = "tags";


    public static final List<String> COUNT_TYPE = Lists.newArrayList("all","satisfied","general","dissatisfied","not_responded");
    @ServiceMethod("get_interact_count")
    public InteractQuestionModel.InteractCountResult getInteractCount(ServiceContext context, InteractQuestionModel.Arg arg) {
        checkParam(arg);
        InteractQuestionModel.InteractCountResult result = new InteractQuestionModel.InteractCountResult();
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), ACTIVITY_QUESTION_OBJ);
        result.setAiSuggestionSwitch(getAiAutoSwitch(context.getUser()));
        SearchTemplateQuery searchTemplateQuery = buildSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        if(ACCOUNT_OBJ.equals(arg.getObjectApiName())){
            SearchUtil.fillFilterEq(filters, ACCOUNT_ID, arg.getObjectDataId());
        }else if(CRMFeedConstants.ACTIVE_RECORD_API_NAME.equals(arg.getObjectApiName())){
            SearchUtil.fillFilterEq(filters, "active_record_id", arg.getObjectDataId());
        }else if(NEWOPPORTUNITY_OBJ.equals(arg.getObjectApiName())){
            SearchUtil.fillFilterEq(filters, NEW_OPPORTUNITY_ID, arg.getObjectDataId());
        }else if(LEADS_OBJ.equals(arg.getObjectApiName())){
            SearchUtil.fillFilterEq(filters, LEADS_ID, arg.getObjectDataId());
        }else {
            log.warn("InteractQuestionServicee getInteractCount arg:{}", JSONObject.toJSONString(arg));
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(context.getUser(), ACTIVITY_QUESTION_OBJ, searchTemplateQuery,
                Lists.newArrayList("_id",ADVICE_STATUS,PROPOSER_ATTITUDE,QUESTION_USER,ANSWER_USER,TAGS));
        // tags选项与数据状况关联
        Set<String> allTags = queryResult.getData().stream().map(data -> (List<String>) data.get(TAGS)).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toSet());
        IFieldDescribe tagsFieldDescribe = describe.getFieldDescribe(TAGS);
        if (tagsFieldDescribe != null) {
            SelectManyFieldDescribe selectMany = (SelectManyFieldDescribe) tagsFieldDescribe;
            List<ISelectOption> options = selectMany.getSelectOptions();
            options.removeIf(o -> !allTags.contains(o.getValue()));
            selectMany.setSelectOptions(options);
        }
        result.setObjectDescribe(ObjectDescribeDocument.of(describe));
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtils.empty(queryResult.getData())){
            result.setDataList(buddleInteractCountResult());
            return result;
        }
        List<ObjectDataDocument> list = new ArrayList<>();
        COUNT_TYPE.stream().forEach(x->{
            ObjectDataDocument dataDocument = new ObjectDataDocument();
            dataDocument.put("type",x);
            if("all".equals(x)){
                dataDocument.put("num",queryResult.getData().size());
            }else if("not_responded".equals(x)){
                dataDocument.put("num",queryResult.getData().stream().filter(y->ObjectUtils.isNotEmpty(y.get(ADVICE_STATUS)) && "1".equals(y.get(ADVICE_STATUS).toString())).count());
            }else {
                dataDocument.put("num",queryResult.getData().stream().filter(y->ObjectUtils.isNotEmpty(y.get(PROPOSER_ATTITUDE)) && x.equals(y.get(PROPOSER_ATTITUDE).toString())).count());
            }
            list.add(dataDocument);
        });
        result.setDataList(list);

        //获取人员id
        Set<String> activityUserId = new HashSet<>();
        for(IObjectData objectData:queryResult.getData()){
            if(ObjectUtils.isNotEmpty(objectData.get(QUESTION_USER))){
                List<String> userIds = objectData.get(QUESTION_USER,List.class);
                activityUserId.addAll(userIds);
            }
            if(ObjectUtils.isNotEmpty(objectData.get(ANSWER_USER))){
                List<String> userIds = objectData.get(ANSWER_USER,List.class);
                activityUserId.addAll(userIds);
            }
        }
        if(CollectionUtils.notEmpty(activityUserId)){
            List<IObjectData> activityUserList = serviceFacade.findObjectDataByIdsIgnoreAll(context.getTenantId(),Lists.newArrayList(activityUserId),"ActivityUserObj");
            if(CollectionUtils.notEmpty(activityUserList)){
                //获取头像
                List<String> personnelIds =activityUserList.stream().filter(x->ObjectUtils.isNotEmpty(x.get("personnel_id")))
                        .map(x->x.get("personnel_id").toString()).collect(Collectors.toList());
                if(CollectionUtils.notEmpty(personnelIds)){
                    List<IObjectData> personnelList = serviceFacade.findObjectDataByIdsIgnoreAll(context.getTenantId(),Lists.newArrayList(personnelIds),"PersonnelObj");
                    Map<String, Object> profileImageMap = personnelList.stream()
                            .filter(x -> ObjectUtils.isNotEmpty(x.get("profile_image")))
                            .collect(Collectors.toMap(IObjectData::getId, x -> x.get("profile_image")));
                    activityUserList.stream().forEach(x->{
                        if(ObjectUtils.isNotEmpty(x.get("personnel_id"))){
                            String personnelId = x.get("personnel_id").toString();
                            if(profileImageMap.containsKey(personnelId)){
                                x.set("profile_image",profileImageMap.get(personnelId));
                            }
                        }
                    });
                }
            }
            result.setActivityUserList(ObjectDataDocument.ofList(activityUserList));
        }
        return result;
    }

    public List<ObjectDataDocument> buddleInteractCountResult(){
        List<ObjectDataDocument> list =new ArrayList<>();
        COUNT_TYPE.stream().forEach(x->{
            ObjectDataDocument dataDocument = new ObjectDataDocument();
            dataDocument.put("num",0);
            dataDocument.put("type",x);
            list.add(dataDocument);
        });
        return list;
    }


    @ServiceMethod("get_ai_suggestion")
    public InteractQuestionModel.AiSuggestionResult getAiSuggestion(ServiceContext context, InteractQuestionModel.Arg arg) {
        checkParam(arg);
        checkKnowledgelicense(context.getUser());

        IActionContext actionContext = ActionContextExt.of(context.getUser()).getContext();
        actionContext.put(ActionContextKey.SEARCH_RICH_TEXT_EXTRA, true);
        List<IObjectData> list = new ArrayList<>();

        if(ObjectUtils.isNotEmpty(arg.getAiQuestionId())){
            IObjectData data = serviceFacade.findObjectData(actionContext, arg.getAiQuestionId(),ACTIVITY_QUESTION_OBJ);
            if(ObjectUtils.isEmpty(data)){
                throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
            }
            list.add(data);
        }else{
            if(ACCOUNT_OBJ.equals(arg.getObjectApiName())){
                list = getQustionListByLinkField(actionContext, ACCOUNT_ID, arg.getObjectDataId());
            } else if(CRMFeedConstants.ACTIVE_RECORD_API_NAME.equals(arg.getObjectApiName())){
                list = getQustionListByLinkField(actionContext, "active_record_id", arg.getObjectDataId());
            }else if(NEWOPPORTUNITY_OBJ.equals(arg.getObjectApiName())){
                list = getQustionListByLinkField(actionContext, NEW_OPPORTUNITY_ID, arg.getObjectDataId());
            }else if(LEADS_OBJ.equals(arg.getObjectApiName())){
                list = getQustionListByLinkField(actionContext, LEADS_ID, arg.getObjectDataId());
            }
        }
        if(CollectionUtils.empty(list)){
            log.warn("InteractQuestionServicee getAiSuggestion list is null");
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        String key = String.format("getAiSuggestion:%s:%s:%s",context.getTenantId(),arg.getObjectDataId(),arg.getAiQuestionId());
        String redisRequestId = sfaRedisService.tryLockExpireSecondTime(context.getTenantId(), ACTIVITY_QUESTION_OBJ,AI_SUGGESTION_KEY,key
                , 10*60);
        if (StringUtils.isEmpty(redisRequestId)) {
            throw new ValidateException(I18N.text(SOI18NKeyUtils.SFA_ACTIVITY_GET_AI_SUGGESTION_TITLE), AppFrameworkErrorCode.VALIDATION_ERROR);
        }
        //如果是单条的获取，就实时返回
        List<String> ids = list.stream().map(IObjectData::getId).collect(Collectors.toList());
        InteractQuestionModel.AiSuggestionMsg msg = new InteractQuestionModel.AiSuggestionMsg();
        msg.setQuestionIds(ids);
        msg.setTenantId(context.getTenantId());
        msg.setUserId(context.getUser().getUpstreamOwnerIdOrUserId());
        msg.setKey(key);
        msg.setRedisRequestId(redisRequestId);
        msg.setObjectApiName(ACTIVITY_QUESTION_OBJ);
        msg.setObjectActionCode(AI_SUGGESTION_KEY);
        activityRocketProducer.sendMessage("sfa-ai-answer",(JSONObject) JSON.toJSON(msg),context.getTenantId());

        return new InteractQuestionModel.AiSuggestionResult();
    }

    @ServiceMethod("set_ai_auto_switch")
    public InteractQuestionModel.InteractCountResult setAiAutoSwitc(ServiceContext context, InteractQuestionModel.Arg arg) {
        configService.upsertUserConfig(context.getUser(), ConfigType.SFA_ACTIVITY_SET_AI_SWITCH.getKey(), String.valueOf(arg.isState()), ConfigValueType.STRING);
        InteractQuestionModel.InteractCountResult result = new InteractQuestionModel.InteractCountResult();
        result.setAiSuggestionSwitch(arg.isState());
        return result;
    }
    @ServiceMethod("getById")
    public InteractQuestionModel.InteractCountResult getById(ServiceContext context, InteractQuestionModel.Arg arg) {
        if(ObjectUtils.isEmpty(arg.getAiQuestionId())){
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        IActionContext actionContext = ActionContextExt.of(context.getUser()).getContext();
        actionContext.put(ActionContextKey.SEARCH_RICH_TEXT_EXTRA, true);
        IObjectData data = serviceFacade.findObjectData(actionContext, arg.getAiQuestionId(),ACTIVITY_QUESTION_OBJ);
        InteractQuestionModel.InteractCountResult result = new InteractQuestionModel.InteractCountResult();
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), ACTIVITY_QUESTION_OBJ);
        serviceFacade.fillObjectDataWithRefObject(describe,Lists.newArrayList(data),context.getUser());
        result.setData(ObjectDataDocument.of(data));
        result.setObjectDescribe(ObjectDescribeDocument.of(describe));
        return result;
    }

    public boolean getAiAutoSwitch(User user){
        String value = configService.findUserConfig(user, ConfigType.SFA_ACTIVITY_SET_AI_SWITCH.getKey());
        if(ObjectUtils.isEmpty(value)){
            return false;
        }
        return "true".equals(value);
    }

    public void checkParam(InteractQuestionModel.Arg arg){
        if(ObjectUtils.isEmpty(arg.getObjectDataId()) || ObjectUtils.isEmpty(arg.getObjectApiName())){
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
    }

    public List<IObjectData> getQustionListByLinkField(IActionContext context, String linkField ,String dataId){
        SearchTemplateQuery searchTemplateQuery = buildSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, linkField, dataId);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(context, ACTIVITY_QUESTION_OBJ, searchTemplateQuery);
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtils.empty(queryResult.getData())){
            return Lists.newArrayList();
        }
        List<IObjectData> resultList = new ArrayList<>();
        Map<String,List<IObjectData>> groupByActiveRecordMap = queryResult.getData().stream().collect(Collectors.groupingBy(x->x.get("active_record_id").toString()));
        groupByActiveRecordMap.values().stream().forEach(list->{
            List<IObjectData> sortedPeople = list.stream()
                    .filter(x -> x.get("answer_version") != null)
                    .sorted(Comparator.comparing(InteractQuestionService::getAnswerVersion).reversed())
                    .collect(Collectors.toList());
            Integer newVersion = getAnswerVersion(sortedPeople.get(0));
            resultList.addAll(sortedPeople.stream().filter(x->newVersion.equals(getAnswerVersion(x))).collect(Collectors.toList()));
        });
        return resultList;
    }

    public SearchTemplateQuery buildSearchTemplateQuery(){
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(2000);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "question_type", "1");
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }

    private static int getAnswerVersion(IObjectData data) {
        if(ObjectUtils.isNotEmpty(data.get("answer_version"))){
            return Integer.parseInt(data.get("answer_version").toString());
        }
        return 0;
    }

    public void checkKnowledgelicense(User user){
        QueryModuleArg arg = new QueryModuleArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(user.getTenantId());
        licenseContext.setUserId(user.getUpstreamOwnerIdOrUserId());
        arg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(arg);
        if (result == null) {
            throw new ValidateException("query license error");
        }
        List<ModuleInfoPojo> modules = result.getResult();
        boolean containsBoth = modules.stream().filter(e -> e.getModuleCode().equals("customerservice_ai_plugin_app") || e.getModuleCode().equals("knowledgemanagement_app")).distinct().count() == 2;
        if(containsBoth){
            return;
        }
        throw new ValidateException(I18N.text("accountsreceivablenoteobj.license.validate.purchase_license_first"));
    }

    @ServiceMethod("set_mark")
    public InteractQuestionModel.AiSuggestionResult setMark(ServiceContext context, InteractQuestionModel.Arg arg) {
        IObjectData data = serviceFacade.findObjectData(context.getUser(), arg.getAiQuestionId(),ACTIVITY_QUESTION_OBJ);
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), ACTIVITY_QUESTION_OBJ);
        IObjectData  oblData = ObjectDataExt.of(data).copy();
        data.set(ActivityQuestionConstants.Field.MARK, arg.getMark());
        serviceFacade.batchUpdateByFields(context.getUser(), Lists.newArrayList(data), Lists.newArrayList(ActivityQuestionConstants.Field.MARK));
        String buttonApiName = I18N.text(SFAI18NKeyUtil.SFA_INTERACT_QUESTION_SET_MARK);
        InternationalItem internationalPeerDisplayName = new InternationalItem();
        internationalPeerDisplayName.setDefaultInternationalValue(buttonApiName);
        internationalPeerDisplayName.setInternationalKey(SFAI18NKeyUtil.SFA_INTERACT_QUESTION_SET_MARK);
        Map<String, Object> diffMap = Maps.newHashMap();
        diffMap.put(ActivityQuestionConstants.Field.MARK, data.get(ActivityQuestionConstants.Field.MARK));
        serviceFacade.log(context.getUser(), EventType.MODIFY, ActionType.Modify, describe, data, diffMap, oblData, buttonApiName, buttonApiName,internationalPeerDisplayName,null,Maps.newHashMap());
        InteractQuestionModel.AiSuggestionResult result = new InteractQuestionModel.AiSuggestionResult();
        result.setData(ObjectDataDocument.of(data));
        return result;
    }
    @ServiceMethod("create_strategy_task")
    public InteractionStrategyTask.Result createStrategyTask(ServiceContext context, InteractionStrategyTask.Arg arg){
        return createStrategyTaskService.createStrategyTask(context,arg);
    }

}
