package com.facishare.crm.sfa.activity.predefine.service;


import com.facishare.crm.sfa.activity.predefine.service.model.ContactInsightModel;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.DateUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@ServiceModule("contact_insight")
@Component
@Slf4j
public class ActivityContactInsightService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private IObjectDescribeService objectDescribeService;

    public static final String CONTACT_ID = "contact_id";

    public static final String OPPORTUNITY_CONTACTS_ID = "opportunity_contacts_id";

    public static final String CHARACTER_CONTENT= "character_content";
    public static final String MEETING_CONTENT= "meeting_content";
    public static final String INVISIBLE_CONTENT= "invisible_content";

    @ServiceMethod("query_details")
    public ContactInsightModel.Result queryDetails(ServiceContext context, ContactInsightModel.Arg arg) {
        ContactInsightModel.Result result = new ContactInsightModel.Result();
        String contactId = arg.getObjectDataId();
        String opportunityContactId = "";
        if (SFAPreDefine.NewOpportunityContacts.getApiName().equals(arg.getObjectApiName())) {
            IObjectData iObjectData = serviceFacade.findObjectData(context.getUser(), arg.getObjectDataId(), arg.getObjectApiName());
            if (ObjectUtils.isEmpty(iObjectData.get(CONTACT_ID))) {
                return result;
            }
            contactId = iObjectData.get(CONTACT_ID).toString();
            opportunityContactId = arg.getObjectDataId();
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, CONTACT_ID, contactId);
        if(ObjectUtils.isNotEmpty(opportunityContactId)){
            SearchUtil.fillFilterEq(filters, OPPORTUNITY_CONTACTS_ID, opportunityContactId);
        }else{
            SearchUtil.fillFilterIsNull(filters, OPPORTUNITY_CONTACTS_ID);
        }
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(), "ActivityContactInsightObj", searchTemplateQuery);
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            return result;
        }
        IObjectData iObjectData= queryResult.getData().get(0);
        if(ObjectUtils.isNotEmpty(iObjectData.get("character_content"))){
            List<Map<String, String>> characterContentList = iObjectData.get("character_content", List.class);
            characterContentList.stream().forEach(characterContent -> {
                String key ="sfa.activity.contact.insight.character."+characterContent.get("insightType")+"."+characterContent.get("insightText");
                characterContent.put("label", I18N.text(key));
            });
            iObjectData.set("character_content",characterContentList);
        }
        boolean funPrivilege = functionPrivilegeService.funPrivilegeCheck(context.getUser(),
                arg.getObjectApiName(), ObjectAction.UPDATE.getActionCode());
        result.setUpdatePrivilege(funPrivilege);
        IObjectDescribe iObjectDescribe = null;
        try {
            iObjectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(context.getTenantId(), "ActivityContactInsightObj");
            result.setObjectDescribe(ObjectDescribeDocument.of(iObjectDescribe));
        }catch (Exception e){
            log.warn("ActivityContactInsightService findByTenantIdAndDescribeApiName ",e);
        }
        serviceFacade.fillObjectDataWithRefObject(iObjectDescribe,Lists.newArrayList(iObjectData),context.getUser());
        handleLinkActiveRecord(context.getUser(),iObjectData);
        if(ObjectUtils.isEmpty(iObjectData.get("attendeeinsight_record_id"))){
            result.setObjectData(ObjectDataDocument.of(iObjectData));
            return result;
        }
        List<String> attendeeInsightRecordId = iObjectData.get("attendeeinsight_record_id",List.class);
        List<IObjectData> insightRecordList = serviceFacade.findObjectDataByIds(context.getTenantId(),attendeeInsightRecordId,"AttendeeInsightRecordObj");
        insightRecordList = insightRecordList.stream().sorted(Comparator.comparing(IObjectData::getCreateTime)).collect(Collectors.toList());
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(),"AttendeeInsightRecordObj");
        serviceFacade.fillObjectDataWithRefObject(describe,insightRecordList,context.getUser());
        IFieldDescribe fieldDescribe = describe.getFieldDescribe("attitude");
        List<Map<String, String>> limitObjTypeOptions = fieldDescribe.get("options", List.class);
        Map<String,String> optionMap = limitObjTypeOptions.stream().collect(Collectors.toMap(option -> option.get("value"), option -> option.get("label")));
        List<Map<String, String>>  showDataList = Lists.newArrayList();
        insightRecordList.stream().forEach(insightRecord -> {
            String value = insightRecord.get("attitude").toString();
            Map<String, String> map = new HashMap<>();
            map.put("time", DateUtil.getDateString(insightRecord.getCreateTime()));
            map.put("value", value);
            map.put("label", optionMap.get(value) );
            map.put("id", insightRecord.getId());
            map.put("active_record_id", insightRecord.get("active_record_id").toString());
            map.put("active_record_id__r", insightRecord.get("active_record_id__r").toString());
            showDataList.add(map);
        });
        iObjectData.set("five_records",showDataList);
        result.setObjectData(ObjectDataDocument.of(iObjectData));
        return result;
    }

    @ServiceMethod("set_switch")
    public ContactInsightModel.SetSwitchResult setSwitch(ServiceContext context,ContactInsightModel.SetSwitchArg setSwitchArg ) {
        String contactId = setSwitchArg.getObjectDataId();
        String opportunityContactId = "";
        IObjectData iObjectData = serviceFacade.findObjectData(context.getUser(), setSwitchArg.getObjectDataId(), setSwitchArg.getObjectApiName());

        if (SFAPreDefine.NewOpportunityContacts.getApiName().equals(setSwitchArg.getObjectApiName())) {
             if (ObjectUtils.isEmpty(iObjectData.get(CONTACT_ID))) {
                return null;
            }
            contactId = iObjectData.get(CONTACT_ID).toString();
            opportunityContactId = setSwitchArg.getObjectDataId();
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, CONTACT_ID, contactId);
        if(ObjectUtils.isNotEmpty(opportunityContactId)){
            SearchUtil.fillFilterEq(filters, OPPORTUNITY_CONTACTS_ID, opportunityContactId);
        }else{
            SearchUtil.fillFilterIsNull(filters, OPPORTUNITY_CONTACTS_ID);
        }
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(), "ActivityContactInsightObj", searchTemplateQuery);
        if(ObjectUtils.isEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            return null;
        }
        IObjectData contactInsight = queryResult.getData().get(0);
        contactInsight.set(setSwitchArg.getFieldApiName(),setSwitchArg.isValue());
        serviceFacade.batchUpdateWithData(context.getUser(),Lists.newArrayList(contactInsight),Lists.newArrayList(setSwitchArg.getFieldApiName()));
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(),setSwitchArg.getObjectApiName());
        String msg ="";
        String internationalKey = "sfa.activity.contact."+setSwitchArg.getFieldApiName();
        String switchKey = "sfa.activity.contact.set.switch."+setSwitchArg.isValue();
        List<String> internationalParameters = new ArrayList<>();
        if("attitude_upd_field".equals(setSwitchArg.getFieldApiName())){
            internationalKey = "sfa.activity.contact.insight_attitude_upd_field";
            IFieldDescribe fieldDescribe = null;
            if(SFAPreDefine.NewOpportunityContacts.getApiName().equals(setSwitchArg.getObjectApiName())){
                 fieldDescribe = describe.getFieldDescribe("position");
                internationalParameters.add("NewOpportunityContactsObj.field.position.label");
            }else{
                fieldDescribe = describe.getFieldDescribe("contact_our_strength");
                internationalParameters.add("ContactObj.field.contact_our_strength.label");
            }
            msg =I18N.text(internationalKey  , fieldDescribe.getLabel(),I18N.text(switchKey));

        }else{
            msg =I18N.text(internationalKey  , I18N.text(switchKey));
        }
        internationalParameters.add(switchKey);

        InternationalItem nternationalItem = InternationalItem.builder()
                .defaultInternationalValue(msg)
                .internationalKey(internationalKey)
                .internationalParameters(internationalParameters)
                .build();
        serviceFacade.logDataWithInternationalCustomMessage(context.getUser(), EventType.MODIFY, ActionType.MODIFY, describe, iObjectData, msg,nternationalItem);
        ContactInsightModel.SetSwitchResult setSwitchResult = new ContactInsightModel.SetSwitchResult();
        setSwitchResult.setValue(setSwitchArg.isValue());
        return setSwitchResult;
    }

    /**
     * 处理关联得销售记录
     * @param user
     * @param iObjectData
     */
    public void handleLinkActiveRecord(User user,IObjectData  iObjectData){
        List<String> activeRecordIds = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(iObjectData.get(CHARACTER_CONTENT))){
            List<Map<String,Object>> list  = (List<Map<String,Object>>)iObjectData.get(CHARACTER_CONTENT);
            list.stream().forEach(item->{
                List<String>  ids = (List<String>)item.get("quotaSeqList");
                activeRecordIds.addAll(ids);
            });
        }

        if(ObjectUtils.isNotEmpty(iObjectData.get(MEETING_CONTENT))){
            List<Map<String,Object>> list  = (List<Map<String,Object>>)iObjectData.get(MEETING_CONTENT);
            list.stream().forEach(item->{
                List<String>  ids = (List<String>)item.get("topics_source");
                activeRecordIds.addAll(ids);
            });
        }

        if(ObjectUtils.isNotEmpty(iObjectData.get(INVISIBLE_CONTENT))){
            List<Map<String,Object>> list  = (List<Map<String,Object>>)iObjectData.get(INVISIBLE_CONTENT);
            list.stream().forEach(item->{
                List<String>  ids = (List<String>)item.get("topics_source");
                activeRecordIds.addAll(ids);
            });
        }
        if(CollectionUtil.isEmpty(activeRecordIds)){
            return;
        }
        List<IObjectData> list = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(),activeRecordIds,"ActiveRecordObj");
        Map<String,String> map = list.stream().collect(Collectors.toMap(x->x.getId(),y->y.getName()));
        if(ObjectUtils.isNotEmpty(iObjectData.get(CHARACTER_CONTENT))){
            List<Map<String,Object>> tempList  = (List<Map<String,Object>>)iObjectData.get(CHARACTER_CONTENT);
            tempList.stream().forEach(item->{
                List<String>  ids = (List<String>)item.get("quotaSeqList");
                List<Map<String,String>> activeRecordTempList = new ArrayList<>();
                ids.stream().forEach(id->{
                    Map<String,String> mapTemp = new HashMap<>();
                    mapTemp.put("id",id);
                    if(map.containsKey(id)){
                        mapTemp.put("name",map.get(id));
                    }else{
                        mapTemp.put("name","已删除");
                    }
                    activeRecordTempList.add(mapTemp);
                });
                item.put("link_data",activeRecordTempList);
            });
        }
        if(ObjectUtils.isNotEmpty(iObjectData.get(MEETING_CONTENT))){
            List<Map<String,Object>> tempList  = (List<Map<String,Object>>)iObjectData.get(MEETING_CONTENT);
            tempList.stream().forEach(item->{
                List<String>  ids = (List<String>)item.get("topics_source");
                List<Map<String,String>> activeRecordTempList = new ArrayList<>();
                ids.stream().forEach(id->{
                    Map<String,String> mapTemp = new HashMap<>();
                    mapTemp.put("id",id);
                    if(map.containsKey(id)){
                        mapTemp.put("name",map.get(id));
                    }else{
                        mapTemp.put("name","已删除");
                    }
                    activeRecordTempList.add(mapTemp);
                });
                item.put("link_data",activeRecordTempList);
            });
        }
        if(ObjectUtils.isNotEmpty(iObjectData.get(INVISIBLE_CONTENT))){
            List<Map<String,Object>> tempList  = (List<Map<String,Object>>)iObjectData.get(INVISIBLE_CONTENT);
            tempList.stream().forEach(item->{
                List<String>  ids = (List<String>)item.get("topics_source");
                List<Map<String,String>> activeRecordTempList = new ArrayList<>();
                ids.stream().forEach(id->{
                    Map<String,String> mapTemp = new HashMap<>();
                    mapTemp.put("id",id);
                    if(map.containsKey(id)){
                        mapTemp.put("name",map.get(id));
                    }else{
                        mapTemp.put("name","已删除");
                    }
                    activeRecordTempList.add(mapTemp);
                });
                item.put("link_data",activeRecordTempList);
            });
        }
    }

    @ServiceMethod("update_details")
    public ContactInsightModel.Result updateDetails(ServiceContext context, ContactInsightModel.UpdateArg arg) {
        IObjectData iObjectData = serviceFacade.findObjectData(context.getUser(),arg.getObjectData().getId(),"ActivityContactInsightObj");
        arg.getUpdateFieldList().stream().forEach(item->{
            iObjectData.set(item,arg.getObjectData().get(item));
        });
        serviceFacade.batchUpdateWithData(context.getUser(),Lists.newArrayList(iObjectData),arg.getUpdateFieldList());
        return new ContactInsightModel.Result();
    }
}
