package com.facishare.crm.sfa.activity.predefine.controller;

import com.facishare.crm.sfa.activity.predefine.ActivityPredefineObject;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;

import java.util.List;

public class AgentPromptVersionListHeaderController extends StandardListHeaderController {

    public static List<String> buttonBlacklist = Lists.newArrayList(
            "ChangeOwner_button_default", "AddTeamMember_button_default", "DeleteTeamMember_button_default",
            "ExportFile_button_default", "Print_button_default", "IntelligentForm_button_default", "Abolish_button_default"
    );

    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        res.getButtons().removeIf(o -> buttonBlacklist.contains(o.toButton().getApiName()));
        // 获取销售记录互动场景的选项
        IObjectDescribe activeRecordDescribe = serviceFacade.findObject(controllerContext.getTenantId(), ActivityPredefineObject.ActiveRecord.getApiName());
        IObjectDescribe agentPromptVersionDescribe = serviceFacade.findObject(controllerContext.getTenantId(), ActivityPredefineObject.AgentPromptVersion.getApiName());
        IFieldDescribe sourceInteractiveScenario = activeRecordDescribe.getFieldDescribe("interactive_scenario");
        IFieldDescribe targetInteractiveScenario = agentPromptVersionDescribe.getFieldDescribe("interactive_scenario");
        targetInteractiveScenario.set("options",sourceInteractiveScenario.get("options"));
        ObjectDescribeDocument objectDescribeDocument = ObjectDescribeDocument.of(agentPromptVersionDescribe);
        res.setObjectDescribe(objectDescribeDocument);
        res.setObjectDescribeExt(objectDescribeDocument);
        return res;
    }

    @Override
    protected List<IButton> getButtons() {
        List<IButton> buttons = super.getButtons();
        buttons.removeIf(o -> buttonBlacklist.contains(o.getName()));
        return buttons;
    }

//    @Override
//    protected IObjectDescribe findObject() {
//        //相关列表页需要多对多字段来获取按钮
//        if (isRelatedList()) {
//            return serviceFacade.findObjectIncludeMultiField(controllerContext.getTenantId(), controllerContext.getObjectApiName());
//        }
//        return serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
//    }

}
