package com.facishare.crm.sfa.activity.predefine.action;

import com.alibaba.fastjson2.JSONObject;
import com.facishare.crm.sfa.activity.predefine.ActivityPredefineObject;
import com.facishare.crm.sfa.activity.predefine.service.AgentPromptVersionService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_CONTACT_PARAM_EXCEPTION;

@Slf4j
public class AgentPromptVersionEditAction extends StandardEditAction {

    protected AgentPromptVersionService agentPromptVersionService = SpringUtil.getContext().getBean(AgentPromptVersionService.class);
    protected IObjectData oldObjectData;

    @Override
    protected Result doAct(Arg arg) {
        Result result = super.doAct(arg);
        String oldAplApiName = "";
        String newAplApiName = "";
        try {
            if (!ObjectUtils.isEmpty(oldObjectData.get("rule"))) {
                JSONObject jsonObject = JSONObject.parseObject(oldObjectData.get("rule").toString());
                oldAplApiName = jsonObject.getJSONObject("APL").getString("function_api_name");
                if (oldAplApiName == null) {
                    oldAplApiName = "";
                }
            }
            if (!ObjectUtils.isEmpty(arg.getObjectData().get("rule"))) {
                JSONObject jsonObject = JSONObject.parseObject(arg.getObjectData().get("rule").toString());
                newAplApiName = jsonObject.getJSONObject("APL").getString("function_api_name");
                if (newAplApiName == null) {
                    newAplApiName = "";
                }
            }
        } catch (Exception e) {
            log.warn("json error");
        }
        if (!oldAplApiName.equals(newAplApiName)) {
            if (!ObjectUtils.isEmpty(oldAplApiName)) {
                agentPromptVersionService.delete(actionContext.getTenantId(), result.getObjectData().getId(), oldAplApiName);
            }
            if (!ObjectUtils.isEmpty(newAplApiName)) {
                agentPromptVersionService.create(actionContext.getTenantId(), result.getObjectData().getId(), newAplApiName);
            }
        }
        return result;
    }

    @Override
    protected void before(Arg arg) {
        // 检查提示词
        Object templateApiName = arg.getObjectData().get("subtemplate_api_name");
        if (ObjectUtils.isEmpty(templateApiName) || "[]".equals(templateApiName)) {
            throw new ValidateException(I18N.text(SFA_CONTACT_PARAM_EXCEPTION));
        }
        JSONObject templateObject = JSONObject.parseObject(templateApiName.toString());
        if (ObjectUtils.isEmpty(templateObject.get("promptApiName"))) {
            throw new ValidateException(I18N.text(SFA_CONTACT_PARAM_EXCEPTION));
        }
        oldObjectData = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),arg.getObjectData().getId(), ActivityPredefineObject.AgentPromptVersion.getApiName());
        super.before(arg);
    }

}
