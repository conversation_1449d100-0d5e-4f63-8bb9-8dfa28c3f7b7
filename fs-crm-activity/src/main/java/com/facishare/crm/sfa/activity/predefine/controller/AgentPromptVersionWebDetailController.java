package com.facishare.crm.sfa.activity.predefine.controller;

import com.facishare.crm.sfa.activity.predefine.ActivityPredefineObject;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

public class AgentPromptVersionWebDetailController extends StandardWebDetailController {

    @Override
    protected AbstractStandardDetailController.Result after(AbstractStandardDetailController.Arg arg, AbstractStandardDetailController.Result result) {
        Result res = super.after(arg, result);
        // 获取销售记录互动场景的选项
        IObjectDescribe activeRecordDescribe = serviceFacade.findObject(controllerContext.getTenantId(), ActivityPredefineObject.ActiveRecord.getApiName());
        IObjectDescribe agentPromptVersionDescribe = serviceFacade.findObject(controllerContext.getTenantId(), ActivityPredefineObject.AgentPromptVersion.getApiName());
        IFieldDescribe sourceInteractiveScenario = activeRecordDescribe.getFieldDescribe("interactive_scenario");
        IFieldDescribe targetInteractiveScenario = agentPromptVersionDescribe.getFieldDescribe("interactive_scenario");
        targetInteractiveScenario.set("options",sourceInteractiveScenario.get("options"));
        ObjectDescribeDocument objectDescribeDocument = ObjectDescribeDocument.of(agentPromptVersionDescribe);
        res.setDescribe(objectDescribeDocument);
        res.setObjectDescribeExt(objectDescribeDocument);
        return res;
    }

}
