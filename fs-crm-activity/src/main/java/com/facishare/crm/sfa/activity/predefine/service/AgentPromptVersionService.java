package com.facishare.crm.sfa.activity.predefine.service;

import com.facishare.crm.sfa.activity.predefine.ActivityPredefineObject;
import com.facishare.crm.util.DescribeI18NUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.reference.data.DeleteArg;
import com.facishare.paas.reference.data.EntityReferenceArg;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class AgentPromptVersionService {

    @Resource
    private EntityReferenceService entityReferenceService;

    /**
     * @param tenantId   tenantId
     * @param objectId   数据ID
     * @param aplApiName APL函数ApiName
     */
    public void create(String tenantId, String objectId, String aplApiName) {
        EntityReferenceArg entityReferenceArg = EntityReferenceArg.builder()
                .tenantId(tenantId)
                .sourceType(ActivityPredefineObject.AgentPromptVersion.getApiName())
                .sourceLabel(I18N.text(DescribeI18NUtils.getObjectNameKey(ActivityPredefineObject.AgentPromptVersion.getApiName())))
                .sourceValue(objectId)
                .targetType(EntityReferenceService.FUNCTION)
                .targetValue(aplApiName)
                .build();
        entityReferenceService.create(entityReferenceArg);
    }

    /**
     * @param tenantId   tenantId
     * @param objectId   数据ID
     * @param aplApiName APL函数ApiName
     */
    public void delete(String tenantId, String objectId, String aplApiName) {
        List<DeleteArg> deleteArgList = Lists.newArrayList();
        DeleteArg deleteArg = DeleteArg.builder()
                .sourceType(ActivityPredefineObject.AgentPromptVersion.getApiName())
                .sourceValue(objectId)
                .targetType(EntityReferenceService.FUNCTION)
                .targetValue(aplApiName)
                .build();
        deleteArgList.add(deleteArg);
        entityReferenceService.delete(tenantId, deleteArgList);
    }

}