package com.facishare.crm.sfa.activity.predefine.controller;

import com.facishare.crm.sfa.lto.utils.LicenseCheckUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutController;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.social.ability.layout.XTLayoutFormComponentMaintainer;
import com.google.common.collect.Sets;

public class ActiveRecordDesigner<PERSON>ayoutController extends StandardDesignerLayoutController implements XTLayoutFormComponentMaintainer {
    @Override
    protected void processLayout(ILayout layout) {
        boolean headInfoPresent = LayoutExt.of(layout).getHeadInfoComponent().isPresent();
        String tenantId = controllerContext.getTenantId();
        boolean allowActiveRecordObjectification = GrayUtil.allowActiveRecordObjectification(tenantId);
        if (!allowActiveRecordObjectification && !LicenseCheckUtil.checkAIExist(tenantId) && !headInfoPresent) {
            removeAllComponentsExcept(layout, Sets.newHashSet(ComponentExt.FORM_COMPONENT, ComponentExt.HEAD_INFO_COMPONENT_NAME));
        }
    }
}