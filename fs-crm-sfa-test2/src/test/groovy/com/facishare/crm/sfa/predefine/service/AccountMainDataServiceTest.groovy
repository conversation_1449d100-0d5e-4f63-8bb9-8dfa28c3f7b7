//package com.facishare.crm.sfa.predefine.service
//
//import com.alibaba.fastjson.JSON
//import com.beust.jcommander.internal.Maps
//import com.facishare.crm.describebuilder.QuoteFieldDescribeBuilder
//import com.facishare.crm.openapi.Utils
//import com.facishare.crm.sfa.EnhancedBaseGroovyTest
//import com.facishare.crm.sfa.model.AccountMainDataModel
//import com.facishare.crm.sfa.predefine.SFAPreDefineObject
//import com.facishare.crm.sfa.predefine.service.model.Duplicate.SFADuplicateSearch
//import com.facishare.crm.sfa.utilities.constant.AccountConstants
//import com.facishare.crm.sfa.utilities.util.AccountAddrUtil
//import com.facishare.crm.sfa.utilities.util.LogUtil
//import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds
//import com.facishare.paas.appframework.core.model.RequestContext
//import com.facishare.paas.appframework.core.model.ServiceFacade
//import com.facishare.paas.appframework.core.model.ServiceFacadeImpl
//import com.facishare.paas.appframework.core.model.User
//import com.facishare.paas.appframework.core.util.UdobjGrayConfig
//import com.facishare.paas.metadata.api.IObjectData
//import com.facishare.paas.metadata.api.QueryResult
//import com.facishare.paas.metadata.api.describe.IFieldDescribe
//import com.facishare.paas.metadata.api.describe.IObjectDescribe
//import com.facishare.paas.metadata.api.search.Wheres
//import com.facishare.paas.metadata.api.service.IObjectDataService
//import com.facishare.paas.metadata.impl.describe.QuoteFieldDescribe
//import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe
//import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
//import com.google.common.collect.Lists
//import org.junit.runner.RunWith
//import org.powermock.api.mockito.PowerMockito
//import org.powermock.core.classloader.annotations.PowerMockIgnore
//import org.powermock.core.classloader.annotations.PrepareForTest
//import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
//import org.powermock.modules.junit4.PowerMockRunner
//import org.powermock.modules.junit4.PowerMockRunnerDelegate
//import org.powermock.reflect.Whitebox
//import org.slf4j.Logger
//import org.spockframework.runtime.Sputnik
//import spock.lang.Shared
//
//import static org.mockito.ArgumentMatchers.any
//import static org.mockito.ArgumentMatchers.anyList
//
///**
// * <AUTHOR> lik
// * @date : 2024/6/4 10:32
// */
//
//@RunWith(PowerMockRunner)
//@PowerMockRunnerDelegate(Sputnik)
//@PowerMockIgnore(["javax.management.*"])
//@PrepareForTest([UdobjGrayConfig.class, ServiceFacadeImpl.class, AccountAddrUtil.class, LogUtil.class, AccountMainDataService.class, IObjectDataService.class])
//// 限制 RequestUtil, AccountAddrService 类里的静态代码块初始化, 这里会加载一些静态资源，可以禁止初始化。
//@SuppressStaticInitializationFor([
//        "com.facishare.paas.appframework.core.util.RequestUtil",
//        "com.facishare.crm.sfa.predefine.service.AccountMainDataService",
//        "com.facishare.paas.appframework.core.model.ServiceFacadeImpl",
//        "com.facishare.paas.appframework.core.util.UdobjGrayConfig"
//])
//class AccountMainDataServiceTest extends EnhancedBaseGroovyTest {
//    @Shared
//    RequestContext requestContext
//    @Shared
//    AccountMainDataService accountMainDataService
//    @Shared
//    ServiceFacade serviceFacade
//    @Shared
//    User user
//
//    ObjectDataServiceImpl iObjectDataService
//
//    def setupSpec() {
//
//        removeConfigFactory()
//        removeI18N()
//        initSpringContext()
//        requestContext = createRequestContext()
//        user = PowerMockito.spy(new User("71568", "-10000"));
//        accountMainDataService = PowerMockito.spy(new AccountMainDataService())
//        serviceFacade = PowerMockito.mock(ServiceFacade)
//
//        Whitebox.setInternalState(AccountMainDataService, "log", Mock(Logger))
//    }
//
//    def setup() {
//        iObjectDataService = PowerMockito.mock(ObjectDataServiceImpl.class)
//    }
//
//    def "check_account_main_data_distribute"() {
//        given:
//
//
//        AccountMainDataModel.CheckAccountMainDataDistributeArg arg = new AccountMainDataModel.CheckAccountMainDataDistributeArg()
//
//        Whitebox.setInternalState(accountMainDataService, "serviceFacade", serviceFacade)
//        PowerMockito.doReturn(objectDataList).when(serviceFacade, "findObjectDataByIdsIncludeDeleted", any(), any(), any())
//        when:
//        def result = Whitebox.invokeMethod(accountMainDataService, "checkAccountMainData", getServiceContext(requestContext, "AccountMainDataService", "checkAccountMainData"), arg)
//        then:
//        notThrown(Exception)
//        where:
//        objectDataList                        | needFieldMap
//        getObjectDataList(["name", "_id"], 3) | new HashMap<>();
//    }
//
//
//    def "getOrganizationAccountMainData method"() {
//        given:
//
//        accountMainDataService = PowerMockito.spy(new AccountMainDataService())
//
//        Whitebox.setInternalState(accountMainDataService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(AccountMainDataService, "objectDataService", iObjectDataService)
//        PowerMockito.doReturn(queryResult).when(iObjectDataService, "findBySql", any(), any())
//        when:
//        def result = Whitebox.invokeMethod(accountMainDataService, "getOrganizationAccountMainData", this.user, mainDataIds, organizationIds)
//        then:
//        true
//        where:
//        mainDataIds                          | organizationIds                      | queryResult
//        Lists.newArrayList("dedw", "dewdew") | Lists.newArrayList("dedw", "dewdew") | Lists.newArrayList(Maps.newHashMap("account_main_data_id", "dewde", "data_own_organization", "dewdew"));
//    }
//
//    def "getAccountMainDataByAccountName method"() {
//        given:
//        def arg = new AccountMainDataModel.Arg();
//        arg.setAccountName(AccountName)
//
//        Whitebox.setInternalState(accountMainDataService, "serviceFacade", serviceFacade)
//        PowerMockito.doReturn(describeMap).when(serviceFacade, "findObjects", any(), any())
//        PowerMockito.doReturn(dataList).when(accountMainDataService, "getAccountMainDataBySearchTemplate", any(), any(), any())
//        when:
//        def result = Whitebox.invokeMethod(accountMainDataService, "getAccountMainDataByAccountName", arg, getServiceContext(requestContext, "AccountMainDataService", "getAccountMainDataByAccountName"))
//        then:
//        true
//        where:
//        AccountName | describeMap                                                             | dataList                                                                                                | resulty
////        null       |Maps.newHashMap(SFAPreDefineObject.Account.getApiName(),getDescribe())| Lists.newArrayList(getObjectData(Lists.newArrayList("life_status"),Lists.newArrayList("normal")))      |null
//        "xx"        | Maps.newHashMap(SFAPreDefineObject.Account.getApiName(), getDescribe()) | Lists.newArrayList(getObjectData(Lists.newArrayList("life_status"), Lists.newArrayList("normal")))      | null
//        "xx"        | Maps.newHashMap(SFAPreDefineObject.Account.getApiName(), getDescribe()) | Lists.newArrayList(getObjectData(Lists.newArrayList("life_status"), Lists.newArrayList("ineffective"))) | null
//        "xx"        | Maps.newHashMap(SFAPreDefineObject.Account.getApiName(), getDescribe()) | null                                                                                                    | null
//        "xx"        | Maps.newHashMap(SFAPreDefineObject.Account.getApiName(), getDescribe()) | Lists.newArrayList(getObjectData(Lists.newArrayList("life_status"), Lists.newArrayList("ineffective"))) | null;
//    }
//
//    def "getMainDataUsed method"() {
//        given:
//
//        Whitebox.setInternalState(accountMainDataService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(AccountMainDataService, "objectDataService", iObjectDataService)
//        PowerMockito.doReturn(queryResult).when(iObjectDataService, "findBySql", any(), any())
//        when:
//        def result = Whitebox.invokeMethod(accountMainDataService, "getMainDataUsed", this.user, mainDataIds)
//        then:
//        true
//        where:
//        mainDataIds                          | queryResult
//        Lists.newArrayList("dedw", "dewdew") | Lists.newArrayList(Maps.newHashMap("account_main_data_id", "dewde", "data_own_organization", "dewdew"));
//    }
//
//    def "createAccountData method"() {
//        given:
//
//        Whitebox.setInternalState(accountMainDataService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(AccountMainDataService, "objectDataService", iObjectDataService)
//        PowerMockito.doReturn(queryResult).when(iObjectDataService, "findBySql", any(), any())
//        PowerMockito.doReturn(accountDescribe).when(serviceFacade, "findObject", any(), any())
//        when:
//        def result = Whitebox.invokeMethod(accountMainDataService, "createAccountData", getServiceContext(requestContext, "AccountMainDataService", "checkAccountMainData").getRequestContext(), dataList)
//        then:
//        true
//        where:
//        dataList                                                                           | queryResult                                                                                             | accountDescribe
//        Maps.newHashMap(getObjectData(Lists.newArrayList("name")), getOrganizationDatas()) | Lists.newArrayList(Maps.newHashMap("account_main_data_id", "dewde", "data_own_organization", "dewdew")) | getDescribe(SFAPreDefineObject.Account.getApiName());
//    }
//
//    def "checkDataExist method"() {
//        given:
//
//        Whitebox.setInternalState(accountMainDataService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(AccountMainDataService, "objectDataService", iObjectDataService)
//        PowerMockito.doReturn(queryResult).when(iObjectDataService, "findBySql", any(), any())
////        PowerMockito.doReturn(accountDescribe).when(serviceFacade, "findObject", any(), any())
//        when:
//        def result = Whitebox.invokeMethod(accountMainDataService, "checkDataExist", this.user, name, dataId)
//        then:
//        true
//        where:
//        name      | dataId   | queryResult
//        "dewdewe" | "dewdew" | Lists.newArrayList(Maps.newHashMap("account_main_data_id", "dewde", "data_own_organization", "dewdew"));
//    }
//
//    def "checkAccountMainData method"() {
//        given:
//
//
//        accountMainDataService = PowerMockito.spy(new AccountMainDataService())
//
//        def duplicateSearchService = PowerMockito.mock(SFADuplicateSearchService)
//        Whitebox.setInternalState(accountMainDataService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(accountMainDataService, "duplicateSearchService", duplicateSearchService)
//        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
//                .toReturn(true)
//        PowerMockito.doReturn(deptList).when(serviceFacade, "getDeptInfoNameByIds", any(String.class) as String, any(String.class) as String, any(List.class) as List)
//        PowerMockito.doReturn(accountDescribe).when(serviceFacade, "findObject", any(), any())
//        PowerMockito.doReturn(searchResult).when(duplicateSearchService, "query", any(), any())
//        PowerMockito.doReturn(organizationMainDataList).when(accountMainDataService, "getOrganizationAccountMainData", any(User) as User, anyList(), anyList())
////        PowerMockito.doNothing().when(accountAddrService, "handleInvisibleFields",  any(), any())
//        when:
//        def result = Whitebox.invokeMethod(accountMainDataService, "checkAccountMainData", getServiceContext(requestContext, "AccountMainDataService", "checkAccountMainData").getRequestContext(), objectDataList, orgList)
//        then:
//        true
//        where:
//        objectDataList                        | orgList                | deptList       | accountDescribe                                      | searchResult      | organizationMainDataList
//        getObjectDataList(["name", "_id"], 3) | getOrganizationDatas() | getdeptLists() | getDescribe(SFAPreDefineObject.Account.getApiName()) | getsearchResult() | Maps.newHashMap(Utils.ACCOUNT_API_NAME, Lists.newArrayList("name"));
//    }
//
//    List<AccountMainDataModel.OrganizationData> getOrganizationDatas() {
//        AccountMainDataModel.OrganizationData data = new AccountMainDataModel.OrganizationData();
//        data.setForm_data_own_department(Lists.newArrayList("32323"))
//        data.setForm_data_own_organization(Lists.newArrayList("32323"))
//        data.setForm_owner(Lists.newArrayList("32323"))
//        data.setForm_record_type("dewdew")
//        return Lists.newArrayList(data);
//    }
//
//    List<QueryDeptInfoByDeptIds.DeptInfo> getdeptLists() {
//        QueryDeptInfoByDeptIds.DeptInfo data = new QueryDeptInfoByDeptIds.DeptInfo();
//        data.setDeptId("32323")
//        return Lists.newArrayList(data);
//    }
//
//    SFADuplicateSearch.Result getsearchResult() {
//        SFADuplicateSearch.Result result = SFADuplicateSearch.Result.builder().duplicateMode(1).build();
//        return result
//    }
//
//    IObjectDescribe getDescribe() {
//        removeConfigFactory()
//
//        IObjectDescribe objectDescribe = getDescribe(Utils.ACCOUNT_API_NAME)
//        IFieldDescribe fieldDescribe = new SelectManyFieldDescribe()
//        fieldDescribe.setApiName(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID)
//        fieldDescribe.set("wheres", JSON.parseArray("[{\"connector\":\"OR\",\"filters\":[{\"value_type\":13,\"operator\":\"EQ\",\"field_name\":\"name\",\"field_values\":[\"ccc\"]}]}]", Wheres.class))
//        QuoteFieldDescribe quoteFieldDescribe = QuoteFieldDescribeBuilder.builder().quoteField("high_seas_name").quoteFieldType("object_reference").apiName("high_seas_name").unique(false).isIndex(false).required(false).label("分类").build();
//        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe, quoteFieldDescribe));
//        return objectDescribe;
//    }
//
//    QueryResult<IObjectData> getQueryResultByWhereaaa() {
//        QueryResult<IObjectData> querySpecResult = new QueryResult<>();
//        querySpecResult.setData(Lists.newArrayList(getObjectData(Lists.newArrayList("life_status"), Lists.newArrayList("normal"))))
//        querySpecResult.setTotalNumber(10)
//        return querySpecResult
//    }
//}
