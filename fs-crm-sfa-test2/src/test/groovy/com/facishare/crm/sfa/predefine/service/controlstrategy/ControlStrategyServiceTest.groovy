package com.facishare.crm.sfa.predefine.service.controlstrategy

import com.alibaba.fastjson.JSONObject
import com.beust.jcommander.internal.Maps

import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.service.model.ControlStrategyModel
import com.facishare.crm.util.CommonSqlUtils
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import com.facishare.paas.metadata.support.GDSHandler
import com.facishare.paas.metadata.util.SpringUtil
import com.fxiaoke.enterpriserelation2.common.RestResult
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared
import com.facishare.crm.sfa.utilities.util.LogUtil
import java.lang.reflect.Method

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([LogUtil.class,SFAPreDefineObject.class,UdobjGrayConfig.class,I18N.class, ObjectDataServiceImpl.class, SpringUtil.class, CommonSqlUtils.class, CommonSqlServiceImpl.class, ObjectDataServiceImpl.class])
// 限制 RequestUtil, AccountAddrService 类里的静态代码块初始化, 这里会加载一些静态资源，可以禁止初始化。
@SuppressStaticInitializationFor([
        "com.facishare.paas.appframework.core.util.RequestUtil",
        "com.facishare.crm.util.CommonSqlUtils",
        "com.facishare.crm.sfa.predefine.SFAPreDefineObject",
        "com.facishare.crm.sfa.utilities.util.LogUtil"
])

class ControlStrategyServiceTest extends EnhancedBaseGroovyTest {
    @Shared
    ControlStrategyService controlStrategyService
    @Shared
    ServiceFacade serviceFacade
    @Shared
    IObjectDescribeService objectDescribeService

    @Shared
    User user
    @Shared
    ApplicationContext applicationContext
    def setupSpec() {

        removeConfigFactory()
        removeI18N()
        initSpringContext()
        user = PowerMockito.spy(new User("71568", "-10000"));
        PowerMockito.mockStatic(SpringUtil.class)
        applicationContext = PowerMockito.mock(ApplicationContext.class)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)
        objectDescribeService = PowerMockito.mock(IObjectDescribeService)
        serviceFacade = PowerMockito.mock(ServiceFacade)
        controlStrategyService = PowerMockito.spy(new ControlStrategyService())
        Whitebox.setInternalState(ControlStrategyService, "log", Mock(Logger))

        // 设置静态变量的值
        Whitebox.setInternalState(ControlStrategyService.class, "SUPPORT_SELECT_TYPE_MAP", Maps.newHashMap("name", "1"));

        SFAPreDefineObject AccountObj = PowerMockito.mock(SFAPreDefineObject);
        PowerMockito.when(AccountObj.getApiName()).thenReturn("AccountObj");
        Whitebox.setInternalState(SFAPreDefineObject.class, "Account", AccountObj);

    }
    def setup() {
        PowerMockito.stub(PowerMockito.method(I18N.class, "text", String.class))
                .toReturn("xx")
        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
                .toReturn(true)
    }

/**
 *
 * @return
 */
    def "save"() {
        given:

        def arg = new ControlStrategyModel.Arg()
        arg.setOrganizationIds(organizationIds)
        arg.setObjectApiName(objectApiName)
        arg.setFieldSettings(fieldSettings)


        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
        PowerMockito.doReturn("xxxxx").when(serviceFacade, "generateId")
        PowerMockito.doReturn(getDescribe("xxx")).when(objectDescribeService, "findByTenantIdAndDescribeApiName", any(), any())
        PowerMockito.stub(PowerMockito.method(CommonSqlUtils.class, "queryData", String.class, String.class,List.class))
                .toReturn(Lists.newArrayList())
        PowerMockito.stub(PowerMockito.method(CommonSqlUtils.class, "updateData", String.class, String.class,Map.class,List.class))
                .toReturn(0)
        PowerMockito.stub(PowerMockito.method(CommonSqlUtils.class, "insertData", String.class, String.class,List.class))
                .toReturn(0)
        PowerMockito.stub(PowerMockito.method(LogUtil.class, "addSettingLog", ServiceContext.class, String.class,String.class,String.class,String.class)).toReturn(null)

        when:
        controlStrategyService.save(getServiceContext(user,"ControlStrategyService", "save"), arg)
        then:
        true

        where:
        queryResult                                                    | organizationIds            | objectApiName | fieldSettings
        Lists.newArrayList(Maps.newHashMap("organization_id", "xxxx")) | Lists.newArrayList("xxxx") | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"));
    }


    def "queryEnterpriseData"() {
        given:

        def arg = new ControlStrategyModel.Arg();
        arg.setOrganizationIds(Lists.newArrayList("xxxxx"))
        PowerMockito.stub(PowerMockito.method(CommonSqlUtils.class, "queryData", String.class, String.class,List.class))
                .toReturn(mappingOrgInfoList)
        PowerMockito.stub(PowerMockito.method(CommonSqlUtils.class, "updateData", String.class, String.class,Map.class,List.class))
                .toReturn(0)
        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
        PowerMockito.doReturn("xxxxx").when(serviceFacade, "generateId")
        PowerMockito.stub(PowerMockito.method(CommonSqlUtils.class, "insertData", String.class, String.class,List.class))
                .toReturn(0)
        PowerMockito.doReturn(getDescribe("xxx")).when(objectDescribeService, "findByTenantIdAndDescribeApiName", any(), any())
        PowerMockito.stub(PowerMockito.method(LogUtil.class, "addSettingLog", ServiceContext.class, String.class,String.class,String.class,String.class)).toReturn(null)

        when:
        // 使用反射调用私有方法
        Method method = ControlStrategyService.class.getDeclaredMethod("handleDownstreamEnterprise", User.class, ControlStrategyModel.Arg.class)
        method.setAccessible(true)
        method.invoke(controlStrategyService, this.user, arg)
        then:
        true
        where:
        mappingOrgInfoList                                                                                                                                                                                 | queryResult                                                    | isBlank
        null                                                                                                                                                                                               | null                                                           | true
        Lists.newArrayList(Maps.newHashMap("upstream_org_id", "xxx"))                                                                                                                                      | null                                                           | true
        Lists.newArrayList(Maps.newHashMap("upstream_org_id", "xxxxx", "downstream_tenant_id", "xxxx", "downstream_org_id", "xxxx","organization_id", "xxxx", "field_settings", [],"settings_type", "xx")) | Lists.newArrayList(Maps.newHashMap("organization_id", "xxxx")) | true;
    }

/**
 *
 * @return
 */
    def "updateOrgMappingLeadToUpdateControlStrategy"() {
        given:

        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        def commonSqlService = PowerMockito.mock(CommonSqlServiceImpl)
        Whitebox.setInternalState(CommonSqlUtils, "commonSqlService", commonSqlService)
        Whitebox.setInternalState(CommonSqlUtils, "objectDataService", objectDataService)

        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
        PowerMockito.doReturn(getDescribe("xxx")).when(objectDescribeService, "findByTenantIdAndDescribeApiName", any(), any())
        PowerMockito.doReturn("xxxxx").when(serviceFacade, "generateId")
//        PowerMockito.when(CommonSqlUtils.insertData(any(), any(), any())).thenReturn(0)
        PowerMockito.stub(PowerMockito.method(CommonSqlUtils.class, "queryData", String.class, String.class,List.class))
                .toReturn(downStreamControlStrategyList)
        PowerMockito.stub(PowerMockito.method(CommonSqlUtils.class, "updateData", String.class, String.class,Map.class,List.class))
                .toReturn(0)
        PowerMockito.stub(PowerMockito.method(LogUtil.class, "addSettingLog", ServiceContext.class, String.class,String.class,String.class,String.class)).toReturn(null)

        when:
        controlStrategyService.updateOrgMappingLeadToUpdateControlStrategy("xxxxx", "xxxxxx")
        then:
        true

        where:
        orgMappingsList                                                                                | downStreamControlStrategyList                                                                                                                                                                              | objectApiName | fieldSettings
        null                                                                                           | null                                                                                                                                                                                                       | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"))
        null                                                                                           | Lists.newArrayList(Maps.newHashMap("organization_id", "xxxxx","downstream_org_id", "xxxxxx", "upstream_org_id", "xxxxx"))                                                                                  | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"))
        Lists.newArrayList(Maps.newHashMap("downstream_org_id", "xxxxxx", "upstream_org_id", "xxxxx")) | Lists.newArrayList(Maps.newHashMap("organization_id", "xxxxx", "downstream_org_id", "xxxxx", "upstream_org_id", "xxxxx", "settings_type", "xx","downstream_org_id", "xxxxxx", "upstream_org_id", "xxxxx")) | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"))
        Lists.newArrayList(Maps.newHashMap("downstream_org_id", "xxxxxx", "upstream_org_id", "xxxxx")) | Lists.newArrayList()                                                                                                                                                                                       | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"));
    }


    def "handleUpdateRecordDetail"() {
        given:


        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
        when:
        // 使用反射调用私有方法
        Method method = ControlStrategyService.class.getDeclaredMethod("handleUpdateRecordDetail", StringBuilder.class, List.class, IObjectDescribe.class, String.class)
        method.setAccessible(true)
        method.invoke(controlStrategyService, sb, fieldMapList, iObjectDescribe, settingsType)
        then:
        true
        where:
        sb                  | fieldMapList                                                                                                                     | iObjectDescribe                                                 | settingsType
        new StringBuilder() | Lists.newArrayList(Maps.newHashMap("api_name", "xxxxx", "update_type", "cover", "splice_symbol", ";", "is_allow_updates", true)) | getDescribeWithDefaultFields("xx", Lists.newArrayList("xxxx"))  | "field_name"
        new StringBuilder() | Lists.newArrayList(Maps.newHashMap("api_name", "xxxxx", "update_type", "cover", "splice_symbol", ";", "is_allow_updates", true)) | getDescribeWithDefaultFields("xx", Lists.newArrayList("xxxxx")) | "field_name"
        new StringBuilder() | Lists.newArrayList(Maps.newHashMap("api_name", "xxxxx", "update_type", "cover", "splice_symbol", ";", "is_allow_updates", true)) | getDescribeWithDefaultFields("xx", Lists.newArrayList("xxxxx")) | "field_type";
    }


/**
 *
 * @return
 */
    def "get"() {
        given:

        def arg = new ControlStrategyModel.Arg()
        arg.setObjectApiName("xxxx");
        arg.setOrganizationIds(Lists.newArrayList("xxxx"))


        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
        PowerMockito.doReturn(iObjectDescribe).when(objectDescribeService, "findByTenantIdAndDescribeApiName", any(), any())
        PowerMockito.stub(PowerMockito.method(CommonSqlUtils.class, "queryData", String.class, String.class,List.class))
                .toReturn(queryResult)
        when:
        controlStrategyService.get(getServiceContext(user,"ControlStrategyService", "get"), arg)
        then:
        true

        where:
        iObjectDescribe    | queryResult                                                                              | objectApiName | fieldSettings
        null               | null                                                                                     | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"))
        getDescribe("xxx") | null                                                                                     | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"))
        getDescribe("xxx") | Lists.newArrayList(Maps.newHashMap("settings_type", "field_name", "field_settings", [])) | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"))
        getDescribe("xxx") | Lists.newArrayList(Maps.newHashMap("settings_type", "field_type", "field_settings", [])) | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"));
    }

/**
 *
 * @return
 */
    def "fillNewAddInheritField"() {
        given:

        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)

        when:
        controlStrategyService.fillNewAddInheritField(result, iObjectDescribe)
        then:
        true

        where:
        iObjectDescribe           | result      | objectApiName | fieldSettings
        getDescribe("xxxx")       | getResult() | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"))
        getIObjectDescribe(false) | getResult() | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"))
        getIObjectDescribe(true)  | getResult() | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"));
    }
/**
 *
 * @return
 */
    def "fillIsHaveSplice"() {
        given:

        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)

        when:
        controlStrategyService.fillIsHaveSplice(result, iObjectDescribe)
        then:
        true

        where:
        iObjectDescribe                                                        | result      | objectApiName | fieldSettings
        getDescribe("xxxx")                                                    | getResult() | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"))
        getDescribeWithDefaultFields("xxxxxxx", Lists.newArrayList("xxxxxxx")) | getResult() | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"));
    }
///**
// *
// * @return
// */
//    def "getControlStrategyByApiNameAndOrganizationId"() {
//        given:
//
//        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
//
//        when:
//        controlStrategyService.getControlStrategyByApiNameAndOrganizationId("xxxx", "xxxx", Lists.newArrayList("xxxxx"))
//        then:
//        true
//
//        where:
//        iObjectDescribe     | result      | objectApiName | fieldSettings
//        getDescribe("xxxx") | getResult() | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"));
//    }
///**
// *
// * @return
// */
//    def "saveMainControlStrategy"() {
//        given:
//
//        def arg = new ControlStrategyModel.Arg();
//        arg.setOrganizationIds(Lists.newArrayList("xxxx"))
//
//
//        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
//
//        PowerMockito.doReturn("iObjectDescribe").when(serviceFacade, "generateId")
//
//        when:
//        controlStrategyService.saveMainControlStrategy(this.user, arg)
//        then:
//        true
//
//        where:
//        iObjectDescribe     | result      | objectApiName | fieldSettings
//        getDescribe("xxxx") | getResult() | "xxx"         | Lists.newArrayList(Maps.newHashMap("name", "name"));
//    }
/**
 *
 * @return
 */
    def "fillNameFieldSettings"() {
        given:

        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)

        when:
        controlStrategyService.fillNameFieldSettings(iObjectDescribe)
        then:
        true

        where:
        iObjectDescribe           | fieldSettings
        getDescribe("xxxx")       | Lists.newArrayList(Maps.newHashMap("name", "name"))
        getIObjectDescribe(false) | Lists.newArrayList(Maps.newHashMap("name", "name"))
        getIObjectDescribe(true)  | Lists.newArrayList(Maps.newHashMap("name", "name"));
    }
///**
// *
// * @return
// */
//    def "fillTypeFieldSettings"() {
//        given:
//
//
//
//        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
//
//        when:
//        controlStrategyService.fillTypeFieldSettings()
//        then:
//        true
//
//        where:
//        iObjectDescribe     | fieldSettings
//        getDescribe("xxxx") | Lists.newArrayList(Maps.newHashMap("name", "name"));
//    }
///**
// *
// * @return
// */
//    def "getOrgMappingsOrgInfo"() {
//        given:
//
//
//
//        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
//
//        when:
//        controlStrategyService.getOrgMappingsOrgInfo("xxxx")
//        then:
//        true
//
//        where:
//        iObjectDescribe     | fieldSettings
//        getDescribe("xxxx") | Lists.newArrayList(Maps.newHashMap("name", "name"));
//    }
///**
// *
// * @return
// */
//    def "getOrgMappingsOrgInfoByUpIdAndDownId"() {
//        given:
//
//
//
//        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
//
//        when:
//        controlStrategyService.getOrgMappingsOrgInfoByUpIdAndDownId("xxxx", "xxxx")
//        then:
//        true
//
//        where:
//        iObjectDescribe     | fieldSettings
//        getDescribe("xxxx") | Lists.newArrayList(Maps.newHashMap("name", "name"));
//    }
///**
// *
// * @return
// */
//    def "deleteMainControlStrategy"() {
//        given:
//
//
//
//        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
//
//        when:
//        controlStrategyService.deleteMainControlStrategy(this.user, organizationIds, objectApiName)
//        then:
//        true
//
//        where:
//        organizationIds              | objectApiName
//        null                         | "xxxxx"
//        Lists.newArrayList("xxxxxx") | "xxxxx";
//    }
///**
// *
// * @return
// */
//    def "handleUpdateRecord"() {
//        given:
//        controlStrategyService = PowerMockito.spy(new ControlStrategyService())
//        def arg = new ControlStrategyModel.Arg();
//        arg.setOrganizationIds(Lists.newArrayList("xxxx"))
//        arg.setFieldSettings(fieldSettings)
//
//
//        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
//        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
//
//        PowerMockito.doReturn(deptList).when(serviceFacade, "getDeptInfoNameByIdsAndStatus", any(), any(), any(), any())
//        PowerMockito.doReturn(getDescribe("xxxx")).when(objectDescribeService, "findByTenantIdAndDescribeApiName", any(), any())
//        when:
//        controlStrategyService.handleUpdateRecord(getServiceContext(user,"ControlStrategyService", "handleUpdateRecord"), arg, oldDataMap)
//        then:
//        true
//
//        where:
//        fieldSettings                                    | oldDataMap                                                                                                                                                     | deptList
//        null                                             | Maps.newHashMap("xxx", Maps.newHashMap("xxx", "xxx"))                                                                                                          | Lists.newArrayList(getQueryDeptInfoByDeptIdsDeptInfo())
//        null                                             | Maps.newHashMap("xxxx", Maps.newHashMap("field_settings", JSONObject.toJSONString(Lists.newArrayList(Maps.newHashMap("xxx", "xxxx"))), "settings_type", "xx")) | Lists.newArrayList(getQueryDeptInfoByDeptIdsDeptInfo())
//        Lists.newArrayList(Maps.newHashMap("xxx", "xx")) | Maps.newHashMap("xxxx", Maps.newHashMap("field_settings", JSONObject.toJSONString(Lists.newArrayList(Maps.newHashMap("xxx", "xxxx"))), "settings_type", "xx")) | Lists.newArrayList(getQueryDeptInfoByDeptIdsDeptInfo());
//    }
/**
 *
 * @return
 */
    def "getTenantInfo"() {
        given:
        controlStrategyService = PowerMockito.spy(new ControlStrategyService())
        def arg = new ControlStrategyModel.Arg();

        def gdsHandler = PowerMockito.mock(GDSHandler)
        def fxiaokeAccountService = PowerMockito.mock(FxiaokeAccountService)

        def enterpriseRelationService = PowerMockito.mock(EnterpriseRelationService)
        def objectDataService = PowerMockito.mock(ObjectDataServiceImpl)
        Whitebox.setInternalState(controlStrategyService, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(controlStrategyService, "objectDescribeService", objectDescribeService)
        Whitebox.setInternalState(controlStrategyService, "gdsHandler", gdsHandler)
        Whitebox.setInternalState(controlStrategyService, "fxiaokeAccountService", fxiaokeAccountService)
        Whitebox.setInternalState(controlStrategyService, "enterpriseRelationService", enterpriseRelationService)
//        Whitebox.setInternalState(controlStrategyService, "objectDataService", objectDataService)

        PowerMockito.mockStatic(SpringUtil)
        def applicationContext = PowerMockito.mock(ApplicationContext.class)
        PowerMockito.when(applicationContext.getBean(FunctionPrivilegeService.class)).thenReturn(objectDataService)
        Whitebox.setInternalState(ControlStrategyService, "objectDataService", objectDataService)

        PowerMockito.doReturn("xxxxx").when(gdsHandler, "getEAByEI", anyString())
        PowerMockito.doReturn(outTenantResult).when(fxiaokeAccountService, "getOuterTenantIdByEa", any(), any())
        PowerMockito.doReturn(restResult).when(enterpriseRelationService, "listUpstreamEas", any(), any())
//        PowerMockito.doReturn(findResult).when(objectDataService, "findBySql", any(), any())
        when:
        controlStrategyService.getTenantInfo(getServiceContext(user,"ControlStrategyService", "getTenantInfo"), arg)
        then:
        true

        where:
        outTenantResult | restResult       | findResult
        getRestResult() | null             | null
        getRestResult() | getRestResult1() | Lists.newArrayList(Maps.newHashMap("organization_id", "xxx"));
    }


    IObjectDescribe getIObjectDescribe(boolean flag) {
        IObjectDescribe iObjectDescribe = getDescribe("xxxx");
        iObjectDescribe.setPrimaryApiName("xxxxxx")
        if (flag) {
            iObjectDescribe.setInheritFields(Maps.newHashMap("xxxxxx", Lists.newArrayList("xxxx")))
        } else {
            iObjectDescribe.setInheritFields(Maps.newHashMap("xxxxxx", Lists.newArrayList()))
        }
        return iObjectDescribe
    }

    ControlStrategyModel.Result getResult() {
        ControlStrategyModel.Result result = ControlStrategyModel.Result.builder().build();
        result.setNameFieldSettings(Lists.newArrayList(Maps.newHashMap("api_name", "xxxxxxx")))
        result.setTypeFieldSettings(Lists.newArrayList(Maps.newHashMap("api_name", "text")))
        return result;
    }

    QueryDeptInfoByDeptIds.DeptInfo getQueryDeptInfoByDeptIdsDeptInfo() {
        QueryDeptInfoByDeptIds.DeptInfo deptInfo = new QueryDeptInfoByDeptIds.DeptInfo();
        deptInfo.setDeptId("xxxx")
        deptInfo.setDeptName("xxxxx")
        return deptInfo;
    }

    RestResult<Long> getRestResult() {
        RestResult<Long> result = new RestResult<>();
        result.setData(Long.valueOf(9999))
        return result;
    }

    RestResult<List<String>> getRestResult1() {
        RestResult<List<String>> result = new RestResult<>();
        result.setData(Lists.newArrayList("xxxx"))
        return result;
    }
}
