package com.facishare.crm.sfa.predefine.service.mcr

import com.beust.jcommander.internal.Maps
import com.facishare.crm.openapi.Utils

import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.predefine.SFAPreDefineObject
import com.facishare.crm.sfa.predefine.service.model.MCRServiceModel
import com.facishare.crm.sfa.utilities.util.AccountUtil
import com.facishare.paas.appframework.core.model.InfraServiceFacade
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.metadata.RecordTypeLogicService
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo
import com.facishare.paas.metadata.api.ISelectOption
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectMappingRuleDetailInfo
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe
import com.facishare.paas.metadata.impl.describe.SelectOption
import com.facishare.paas.metadata.util.SpringUtil
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyList
import static org.mockito.ArgumentMatchers.anyString

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([ SFAPreDefineObject.class,UdobjGrayConfig.class,AccountUtil.class,SpringUtil.class])
// 限制 RequestUtil, AccountAddrService 类里的静态代码块初始化, 这里会加载一些静态资源，可以禁止初始化。
@SuppressStaticInitializationFor([
        "com.facishare.crm.sfa.predefine.SFAPreDefineObject",
        "com.facishare.paas.appframework.core.util.RequestUtil"
])
class MCRServiceTest extends EnhancedBaseGroovyTest {
    @Shared
    MCRService mcrService
    @Shared
    ServiceFacade serviceFacade
    @Shared
    RecordTypeLogicService recordTypeLogicService
    @Shared
    InfraServiceFacade infraServiceFacade
    @Shared
    User user
    def setupSpec() {

        removeConfigFactory()
        removeI18N()
        initSpringContext()
        user = PowerMockito.spy(new User("71568", "-10000"));
        infraServiceFacade = PowerMockito.mock(InfraServiceFacade)
        mcrService = PowerMockito.spy(new MCRService())
        serviceFacade = PowerMockito.mock(ServiceFacade)
        recordTypeLogicService = PowerMockito.mock(RecordTypeLogicService)
        Whitebox.setInternalState(MCRService, "log", Mock(Logger))
    }
    def setup() {
        def applicationContext = PowerMockito.mock(ApplicationContext)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)
        PowerMockito.stub(PowerMockito.method(UdobjGrayConfig.class, "isAllow", String.class, String.class))
                .toReturn(true)

        SFAPreDefineObject Contact = PowerMockito.mock(SFAPreDefineObject);
        PowerMockito.when(Contact.getApiName()).thenReturn("ContactObj");
        Whitebox.setInternalState(SFAPreDefineObject.class, "Contact", Contact);
        SFAPreDefineObject ContactEvaluation = PowerMockito.mock(SFAPreDefineObject);
        PowerMockito.when(ContactEvaluation.getApiName()).thenReturn("ContactEvaluationObj");
        Whitebox.setInternalState(SFAPreDefineObject.class, "ContactEvaluation", ContactEvaluation);

        SFAPreDefineObject CustomerRetentionStrategy = PowerMockito.mock(SFAPreDefineObject);
        PowerMockito.when(CustomerRetentionStrategy.getApiName()).thenReturn("CustomerRetentionStrategyObj");
        Whitebox.setInternalState(SFAPreDefineObject.class, "CustomerRetentionStrategy", CustomerRetentionStrategy);

        SFAPreDefineObject CustomerRetentionObjectives = PowerMockito.mock(SFAPreDefineObject);
        PowerMockito.when(CustomerRetentionObjectives.getApiName()).thenReturn("CustomerRetentionObjectivesObj");
        Whitebox.setInternalState(SFAPreDefineObject.class, "CustomerRetentionObjectives", CustomerRetentionObjectives);

        SFAPreDefineObject MCRExpansionPlan = PowerMockito.mock(SFAPreDefineObject);
        PowerMockito.when(MCRExpansionPlan.getApiName()).thenReturn("MCRExpansionPlanObj");
        Whitebox.setInternalState(SFAPreDefineObject.class, "MCRExpansionPlan", MCRExpansionPlan);

        SFAPreDefineObject Position = PowerMockito.mock(SFAPreDefineObject);
        PowerMockito.when(Position.getApiName()).thenReturn("PositionObj");
        Whitebox.setInternalState(SFAPreDefineObject.class, "Position", Position);
    }

/**
 *
 * @return
 */
    def "bulkFindValidRecordType"() {
        given:

        def arg = new MCRServiceModel.FindRecordTypeArg()
        arg.setObjectApiNameList(objectApiNameList)
        def functionPrivilegeService = PowerMockito.mock(FunctionPrivilegeService)


        Whitebox.setInternalState(mcrService, "functionPrivilegeService", functionPrivilegeService)
        Whitebox.setInternalState(mcrService, "recordTypeLogicService", recordTypeLogicService)
        Whitebox.setInternalState(mcrService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(funPrivilegeCheckResult).when(functionPrivilegeService, "batchFunPrivilegeCheck", any(), any(), any())
        PowerMockito.doReturn(objectDescribeMap).when(serviceFacade, "findObjects", any(), any())
        PowerMockito.doReturn(recordTypeResult).when(recordTypeLogicService, "findValidRecordTypesByFunctionCode", any(), any(), any())

        when:
        mcrService.bulkFindValidRecordType(getServiceContext(user,"MCRService", "bulkFindValidRecordType"), arg)
        then:
        true

        where:
        funPrivilegeCheckResult                               | objectApiNameList          | objectDescribeMap                          | recordTypeResult
        null                                                  | null                       | null                                       | null
        null                                                  | Lists.newArrayList("xxxx") | null                                       | null
        Maps.newHashMap("xxxx", Maps.newHashMap("Add", true)) | Lists.newArrayList("2222") | null                                       | null
        Maps.newHashMap("xxxx", Maps.newHashMap("Add", true)) | Lists.newArrayList("xxxx") | null                                       | null
        Maps.newHashMap("xxxx", Maps.newHashMap("Add", true)) | Lists.newArrayList("xxxx") | Maps.newHashMap("xxx", getDescribe("xxx")) | null;
    }
/**
 *
 * @return
 */
    def "contactTransferContactEvaluation"() {
        given:

        def arg = new MCRServiceModel.TransferArg()
        arg.setContactIdList(contactList)

        Whitebox.setInternalState(mcrService, "infraServiceFacade", infraServiceFacade)
        Whitebox.setInternalState(mcrService, "recordTypeLogicService", recordTypeLogicService)
        Whitebox.setInternalState(mcrService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(ruleList).when(infraServiceFacade, "findByApiName", any(), any())
        PowerMockito.doReturn(contactDesc).when(serviceFacade, "findObject", anyString(), anyString())
        PowerMockito.doReturn(describeMap).when(serviceFacade, "findObjects", anyString(), anyList())

        PowerMockito.stub(PowerMockito.method(AccountUtil.class, "transferDataOfCreateAccountBeforeCreateMainData", List.class, IObjectData.class,IObjectDescribe.class))
                .toReturn(newData)
        PowerMockito.doReturn(contactList).when(serviceFacade, "findObjectDataByIds", anyString(), anyList(), anyString())

        when:
        mcrService.contactTransferContactEvaluation(getServiceContext(user,"SfaCrmRFMService", "batchUpdatePriority"), arg)
        then:
        true

        where:
        contactList                                                                 | ruleList                                        | contactDesc   | newData                                                 |describeMap
        null                                                                        | null                                            | null          | null                                                    |null
        null                                                                        | null                                            | null          | null                                                    |null
        Lists.newArrayList(getObjectData(Lists.newArrayList("name","position_id"))) | Lists.newArrayList(getIObjectMappingRuleInfo()) | getDescribe() | getObjectData(Lists.newArrayList("name","position_id")) |Maps.newHashMap("ContactObj",getDescribe());
    }
/**
 *
 * @return
 */
    def "save"() {
        given:

        def arg = new MCRServiceModel.SaveArg()
        arg.setEvaluationList(evaluationList)
        arg.setPlanDetailList(planDetailList)

        Whitebox.setInternalState(mcrService, "infraServiceFacade", infraServiceFacade)
        Whitebox.setInternalState(mcrService, "recordTypeLogicService", recordTypeLogicService)
        Whitebox.setInternalState(mcrService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(ruleList).when(infraServiceFacade, "findByApiName", any(), any())
        PowerMockito.doReturn(getObjectDataList(Lists.newArrayList("_id", "name"), 1)).when(serviceFacade, "findObjectDataByIds", any(), any(), any())
        PowerMockito.doReturn(BaseObjectSaveAction.Result.builder().objectData(ObjectDataDocument.of(getObjectData(Lists.newArrayList("contact_id")))).build()).when(serviceFacade, "triggerAction", any(), any(), any())
        PowerMockito.doReturn("xxxxxxx").when(serviceFacade, "generateId")

        PowerMockito.stub(PowerMockito.method(AccountUtil.class, "targetTransferSource", List.class, IObjectData.class))
                .toReturn(Maps.newHashMap("name", "xxxxxx"))
        when:
        mcrService.save(getServiceContext(user,"SfaCrmRFMService", "batchUpdatePriority"), arg)
        then:
        true

        where:
        evaluationList                                                                             | ruleList | newData | planDetailList
        Lists.newArrayList(ObjectDataDocument.of(getObjectData(Lists.newArrayList("contact_id")))) | null     | null    | Lists.newArrayList(getRetentionStrategy());
    }
    def "edit"() {
        given:

        def arg = new MCRServiceModel.SaveArg()
        arg.setEvaluationList(evaluationList)
        arg.setPlanDetailList(planDetailList)

        Whitebox.setInternalState(mcrService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(BaseObjectSaveAction.Result.builder().objectData(ObjectDataDocument.of(getObjectData(Lists.newArrayList("contact_id")))).build()).when(serviceFacade, "triggerAction", any(), any(), any())

        when:
        mcrService.edit(getServiceContext(user,"SfaCrmRFMService", "batchUpdatePriority"), arg)
        then:
        true

        where:
        evaluationList                                                                             | ruleList | newData | planDetailList
        Lists.newArrayList(ObjectDataDocument.of(getObjectData(Lists.newArrayList("contact_id")))) | null     | null    | Lists.newArrayList(getRetentionStrategy());
    }

    def "fillContactInfo"() {
        given:

        when:
        mcrService.fillContactInfo(newData,newData,positionDataMap,"",null)
        then:
        true

        where:
        newData                                           | positionDataMap
        getObjectData(Lists.newArrayList("position_id"))  | Maps.newHashMap("xxxx",getObjectData(Lists.newArrayList("position_id")))
        getObjectData(Lists.newArrayList("position_ic"))  | Maps.newHashMap("xxxx",getObjectData(Lists.newArrayList("position_ix"))) ;
    }

    def "checkLimitNum"() {
        given:
        Whitebox.setInternalState(mcrService, "serviceFacade", serviceFacade)
        PowerMockito.doReturn(99).when(serviceFacade, "countObjectDataFromDB", any(), any(), any())

        when:
        mcrService.checkLimitNum(user,objectApiName,accountId,businessPlanTaskId,customerRelationshipPlanningId)
        then:
        true

        where:
        objectApiName                       | accountId  | businessPlanTaskId  | customerRelationshipPlanningId
        "objectApiName"                     | null       | "businessPlanTaskId"| "customerRelationshipPlanningId"
        "MCRExpansionPlanObj"               | "accountId"| "businessPlanTaskId"| null
        "MCRExpansionPlanObj"               | "accountId"| "businessPlanTaskId"| "customerRelationshipPlanningId"
        "CustomerRetentionObjectivesObj"    | "accountId"| "businessPlanTaskId"| "customerRelationshipPlanningId"   ;
    }

    IObjectDescribe getDescribe() {
        IObjectDescribe objectDescribe = getDescribe(Utils.ACCOUNT_API_NAME)
        IFieldDescribe fieldDescribe = new SelectOneFieldDescribe();
        fieldDescribe.setApiName("influence_level")
        ISelectOption selectOption = new SelectOption()
        selectOption.setLabel("xxxx")
        selectOption.setValue("xxxx")
        fieldDescribe.setSelectOptions(Lists.newArrayList(selectOption))

        IFieldDescribe fieldDescribe1 = new SelectOneFieldDescribe();
        fieldDescribe1.setApiName("contact_competitor_strength")
        fieldDescribe1.setSelectOptions(Lists.newArrayList(selectOption))

        IFieldDescribe fieldDescribe2 = new SelectOneFieldDescribe();
        fieldDescribe2.setApiName("contact_our_strength")
        fieldDescribe2.setSelectOptions(Lists.newArrayList(selectOption))

        IFieldDescribe fieldDescribe3 = new SelectOneFieldDescribe();
        fieldDescribe3.setApiName("change_adaptability")
        fieldDescribe3.setSelectOptions(Lists.newArrayList(selectOption))

        IFieldDescribe fieldDescribe4 = new SelectOneFieldDescribe();
        fieldDescribe4.setApiName("customer_relationship_hierarchy")
        fieldDescribe4.setSelectOptions(Lists.newArrayList(selectOption))

        objectDescribe.setFieldDescribes(Lists.newArrayList(fieldDescribe, fieldDescribe1, fieldDescribe2, fieldDescribe3, fieldDescribe4));
        return objectDescribe;
    }

    IObjectMappingRuleInfo getIObjectMappingRuleInfo() {
        ObjectMappingRuleInfo objectMappingRuleInfo = new ObjectMappingRuleInfo();
        ObjectMappingRuleDetailInfo detailInfo = new ObjectMappingRuleDetailInfo();
        detailInfo.setSourceFieldName("xxxxx")
        objectMappingRuleInfo.setFieldMapping(Lists.newArrayList(detailInfo))
        return objectMappingRuleInfo;
    }

    MCRServiceModel.RetentionStrategy getRetentionStrategy() {
        MCRServiceModel.RetentionStrategy detail = new MCRServiceModel.RetentionStrategy();
        detail.setRetentionStrategy(ObjectDataDocument.of(getObjectData(Lists.newArrayList("name"))))
        detail.setRetentionObjectivesList(ObjectDataDocument.ofList(getObjectDataList(Lists.newArrayList("name"), 1)))
        detail.setExpansionPlanList(ObjectDataDocument.ofList(getObjectDataList(Lists.newArrayList("name"), 1)))
        return detail;
    }

}
