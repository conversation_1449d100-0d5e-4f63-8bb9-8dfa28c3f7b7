package com.facishare.crm.newpayment.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.manager.NewPaymentManger;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.Objects;

public class NewOrderPaymentRelatedListController extends StandardRelatedListController {
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        LayoutDocument layoutDocument = result.getLayout();
        layoutDocument = newPaymentManger.addPaymentButton(controllerContext.getUser(), layoutDocument);
        boolean newCustomerAccountEnable = newPaymentManger.isCustomerAccountEnable(controllerContext.getUser());
        String targetObjectApiName = arg.getTargetObjectApiName();
        if (Objects.nonNull(layoutDocument) && newCustomerAccountEnable && Utils.CUSTOMER_PAYMENT_API_NAME.equals(targetObjectApiName)) {
            //开启新版客户账户时，如果回款是已入账，移除回款明细相关tab页下新建回款明细按钮
            String targetObjectDataId = arg.getTargetObjectDataId();
            IObjectData paymentData = serviceFacade.findObjectData(controllerContext.getUser(), targetObjectDataId, arg.getTargetObjectApiName());
            boolean enterIntoAccount = paymentData.get(NewPaymentConst.ENTER_INTO_ACCOUNT, Boolean.class, Boolean.FALSE);
            if (enterIntoAccount) {
                ILayout layout = layoutDocument.toLayout();
                List<IButton> buttonList = layout.getButtons();
                buttonList.removeIf(x -> x.getAction().equals(ObjectAction.CREATE.getActionCode()) || "AddPayment".equals(x.getAction()));
                layout.setButtons(buttonList);
                layoutDocument = LayoutDocument.of(layout);
            }
        }
        result.setLayout(layoutDocument);
        newPaymentManger.handPaymentMultiField(controllerContext.getUser(), result.getDataList());
        return result;
    }
}
