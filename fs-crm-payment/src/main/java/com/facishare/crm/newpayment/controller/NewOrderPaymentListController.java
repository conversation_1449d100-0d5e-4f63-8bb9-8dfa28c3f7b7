package com.facishare.crm.newpayment.controller;

import com.facishare.crm.newpayment.manager.NewPaymentManger;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.util.SpringUtil;

public class NewOrderPaymentListController extends StandardListController {
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        LayoutDocument layoutDocument = result.getLayout();
        layoutDocument = newPaymentManger.addPaymentButton(controllerContext.getUser(), layoutDocument);
        result.setLayout(layoutDocument);

        newPaymentManger.handPaymentMultiField(controllerContext.getUser(), result.getDataList());
        return result;
    }
}
