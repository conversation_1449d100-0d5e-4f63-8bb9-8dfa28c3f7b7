package com.facishare.crm.newpayment.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.newpayment.constants.OrderPaymentConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.action.NewPaymentChangeCustomerAction;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.constants.NewPaymentI18N;
import com.facishare.crm.newpayment.enums.PaymentCollectionTypeEnum;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.crm.sfa.model.MultiSourceServiceModel;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.core.predef.service.CalculateService;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.CalculateAndUpdateFormulaFields;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.QuoteFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class NewPaymentManger {
    @Autowired
    private CalculateService calculateService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @ReloadableProperty("openPayQrCodeUrl")
    private String openPayQrCodeUrl = "https://www.fxiaoke.com/open/fe-pay/pay-qrcode-apply/index.html?busiNo=%s&busiCode=1009&orderName=%s&amount=%.2f&payerEnterpriseName=%s&alone=1&apiName=PaymentObj";
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;


    @Transactional
    public NewPaymentChangeCustomerAction.Result changePaymentCustomer(User user, String toUpdateAccountId, IObjectData objectData, List<IObjectData> toUpdateOrderPaymentList) {
        NewPaymentChangeCustomerAction.Result updateResult = new NewPaymentChangeCustomerAction.Result();
        String dbCustomerId = objectData.get(NewPaymentConst.ACCOUNT_ID, String.class);
        if (!StringUtils.equals(dbCustomerId, toUpdateAccountId)) {
            IObjectData toUpdateData = ObjectDataExt.of(objectData).copy();
            toUpdateData.set(NewPaymentConst.ACCOUNT_ID, toUpdateAccountId);
            toUpdateData = serviceFacade.updateObjectData(user, toUpdateData);
            updateResult.setObjectData(ObjectDataDocument.of(toUpdateData));
        }
        List<IObjectData> updateOrderPayments = serviceFacade.batchUpdateByFields(user, toUpdateOrderPaymentList, Lists.newArrayList(NewPaymentConst.ACCOUNT_ID));
        Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
        details.put(NewPaymentConst.ORDER_PAYMENT_API_NAME, ObjectDataDocument.ofList(updateOrderPayments));
        updateResult.setDetails(details);
        return updateResult;
    }

    public LayoutDocument addPaymentButton(User user, LayoutDocument layoutDocument) {
        if (Objects.isNull(layoutDocument)) {
            return null;
        }
        ILayout layout = layoutDocument.toLayout();
        List<IButton> buttonList = layout.getButtons();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(buttonList)) {
            return layoutDocument;
        }
        String value = serviceFacade.findTenantConfig(user,NewPaymentConst.ACCOUNT_RECEIVABLE_CONFIG);
        boolean receiveEnable = "2".equals(value);

        if (!receiveEnable && buttonList.stream().anyMatch(x -> x.getAction().equals(ObjectAction.CREATE.getActionCode()))) {
            List<IButton> newButtonList = Lists.newArrayList();
            buttonList.forEach(x -> {
                if (x.getAction().equals(ObjectAction.CREATE.getActionCode())) {
                    //默认下发回款明细新建按钮和回款新建按钮，灰度企业只下发 回款新建按钮
                    if (NewPaymentUtil.onlyPaymentAddButton(user.getTenantId())) {
                        newButtonList.add(NewPaymentUtil.getAddPaymentButton());
                    } else {
                        newButtonList.add(x);
                        newButtonList.add(NewPaymentUtil.getAddPaymentButton());
                    }
                } else {
                    newButtonList.add(x);
                }
            });
            layout.setButtons(newButtonList);
            layoutDocument = LayoutDocument.of(layout);
        }
        return layoutDocument;
    }

    public boolean isCustomerAccountEnable(User user) {
        String value = serviceFacade.findTenantConfig(user, NewPaymentConst.CUSTOMER_ACCOUNT_CONFIG);
        return "2".equals(value);
    }

    public String generateOpenPayQrCodeUrl(User user, String customerPaymentId) {
        IObjectData customerPayment = serviceFacade.findObjectData(user, customerPaymentId, Utils.CUSTOMER_PAYMENT_API_NAME);
        if (customerPayment == null) {
            return "";
        }
        //升级新回款后，单独新建回款，无明细时，payment_amount为空，这里修改为取amount字段
        BigDecimal amount = customerPayment.get(NewPaymentConst.AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        amount = amount.multiply(BigDecimal.valueOf(100));
        String accountID = customerPayment.get(NewPaymentConst.ACCOUNT_ID, String.class);
        String accountName = "客户";// ignoreI18n
        if (!Strings.isNullOrEmpty(accountID)) {
            Map<String, String> accountNameCacheMap = serviceFacade.findNameByIds(user, Utils.ACCOUNT_API_NAME, Lists.newArrayList(accountID));
            if (accountNameCacheMap != null && accountNameCacheMap.containsKey(accountID)) {
                accountName= accountNameCacheMap.get(accountID);
            }
        }
        return String.format(openPayQrCodeUrl, customerPayment.getId(), customerPayment.getName(), amount.doubleValue(), accountName);
    }

    public List<IObjectData> queryOrderPaymentByPaymentId(User user, String paymentId) {
        List<IFilter> filters = Lists.newArrayList();
        NewPaymentUtil.fillFilterEq(filters, NewPaymentConst.PAYMENT_ID, paymentId);
        return search(user, Utils.ORDER_PAYMENT_API_NAME, filters, 0, 1000).getData();
    }

    public boolean isHasOrderPayment(User user, String paymentId) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1);

        Filter filter = new Filter();
        filter.setOperator(Operator.IN);
        filter.setFieldName(OrderPaymentConstants.Field.PaymentId.apiName);
        filter.setFieldValues(Lists.newArrayList(paymentId));
        searchTemplateQuery.setFilters(Lists.newArrayList(filter));

        QueryResult<IObjectData> orderPaymentResult = serviceFacade.findBySearchQuery(user, Utils.ORDER_PAYMENT_API_NAME, searchTemplateQuery);
        if (orderPaymentResult.getTotalNumber() > 0) {
            return true;
        }
        return false;
    }

    public boolean hasLinkMatchNote(User user, String paymentId) {
        String value = serviceFacade.findTenantConfig(user, NewPaymentConst.ACCOUNT_RECEIVABLE_CONFIG);
        if (!"2".equals(value)) {
            return false;
        }
        List<IFilter> filters = Lists.newArrayList();
        NewPaymentUtil.fillFilterEq(filters, NewPaymentConst.PAYMENT_ID, paymentId);

        List<IObjectData> matchNoteDatas = search(user, NewPaymentConst.MATCH_NOTE_API_NAME, filters, 0, 1).getData();
        return !CollectionUtils.empty(matchNoteDatas);
    }

    public void calculateAndUpdateFormulaFields(RequestContext requestContext, String objectApiName, List<String> dataIds, List<String> fieldApiNames) {
        if (CollectionUtils.empty(dataIds) || CollectionUtils.empty(fieldApiNames)) {
            return;
        }
        CalculateAndUpdateFormulaFields.Arg calculateArg = new CalculateAndUpdateFormulaFields.Arg();
        calculateArg.setCalcRelateField(false);
        calculateArg.setDataIds(dataIds);
        calculateArg.setObjectApiName(objectApiName);
        calculateArg.setFieldApiNames(fieldApiNames);
        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
        calculateService.calculateAndUpdateFormulaFields(calculateArg, serviceContext);
    }

    public void handPaymentMultiField(User user, List<ObjectDataDocument> paymentDataList) {
        String configMappingRule = bizConfigThreadLocalCacheService.getOrderPaymentMappingRule(user.getTenantId());
        if (Strings.isNullOrEmpty(configMappingRule)) {
            return;
        }
        if (!bizConfigThreadLocalCacheService.isOpenOrderPaymentMultiSource(user.getTenantId())) {
            return;
        }
        if (!bizConfigThreadLocalCacheService.isOpenPaymentMultiSourceField(user.getTenantId())) {
            return;
        }

        // 解析多源映射规则
        List<MultiSourceServiceModel.MultiSourceMappingRuleModel> multiSourceMappingRuleModels
                = JSON.parseArray(configMappingRule, MultiSourceServiceModel.MultiSourceMappingRuleModel.class);

        // 收集所有需要查询的字段值
        Map<String, Set<String>> objectApiNameToFieldValuesMap = new LinkedHashMap<>();
        for (MultiSourceServiceModel.MultiSourceMappingRuleModel multiSourceMappingRuleModel : multiSourceMappingRuleModels) {
            String objectApiName = multiSourceMappingRuleModel.getObjectApiName();
            String fieldApiName = multiSourceMappingRuleModel.getFieldApiName();

            Set<String> fieldValues = paymentDataList.stream()
                    .map(paymentData -> paymentData.toObjectData().get(fieldApiName, String.class))
                    .filter(value -> !Strings.isNullOrEmpty(value))
                    .collect(Collectors.toSet());

            if (!fieldValues.isEmpty()) {
                objectApiNameToFieldValuesMap.computeIfAbsent(objectApiName, k -> new HashSet<>()).addAll(fieldValues);
            }
        }

        // 批量查询名称
        Map<String, Map<String, String>> objectApiNameToNameCacheMap = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : objectApiNameToFieldValuesMap.entrySet()) {
            String objectApiName = entry.getKey();
            Set<String> fieldValues = entry.getValue();

            Map<String, String> nameCacheMap = serviceFacade.findNameByIds(user, objectApiName, new ArrayList<>(fieldValues));
            objectApiNameToNameCacheMap.put(objectApiName, nameCacheMap);
        }

        // 更新 paymentData
        for (ObjectDataDocument paymentData : paymentDataList) {
            StringBuilder fieldNameBuilder = new StringBuilder();
            for (MultiSourceServiceModel.MultiSourceMappingRuleModel multiSourceMappingRuleModel : multiSourceMappingRuleModels) {
                String objectApiName = multiSourceMappingRuleModel.getObjectApiName();
                String fieldApiName = multiSourceMappingRuleModel.getFieldApiName();

                String fieldValue = paymentData.toObjectData().get(fieldApiName, String.class);
                if (Strings.isNullOrEmpty(fieldValue)) {
                    continue;
                }

                Map<String, String> nameCacheMap = objectApiNameToNameCacheMap.get(objectApiName);
                if (nameCacheMap != null && nameCacheMap.containsKey(fieldValue)) {
                    fieldNameBuilder.append(I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(objectApiName)))
                            .append(":")
                            .append(nameCacheMap.get(fieldValue));
                    break;
                }
            }
            paymentData.toObjectData().set("object_source_abstract", fieldNameBuilder.toString());
        }
    }

    public List<ObjectDataDocument> parseOrderName(User user, List<ObjectDataDocument> paymentDataList) {
        if (org.springframework.util.CollectionUtils.isEmpty(paymentDataList)) {
            return paymentDataList;
        }
        try {
            Set<String> orderIds = Sets.newHashSet();
            paymentDataList.forEach(paymentData -> {
                String orderIdText = paymentData.toObjectData().get(NewPaymentConst.ORDER_ID, String.class, "");
                orderIds.addAll(Arrays.asList(orderIdText.split(",")));
            });
            orderIds.removeIf(StringUtils::isEmpty);
            List<INameCache> nameCaches = serviceFacade.findRecordName(ActionContextExt.of(user).getContext(), Utils.SALES_ORDER_API_NAME, Lists.newArrayList(orderIds));
            Map<String, String> orderIdNameMap = Maps.newHashMap();
            nameCaches.forEach(x -> {
                if (StringUtils.isNotEmpty(x.getId()) && StringUtils.isNotEmpty(x.getName())) {
                    orderIdNameMap.put(x.getId(), x.getName());
                }
            });
            paymentDataList.forEach(x -> {
                String orderIdText = x.toObjectData().get(NewPaymentConst.ORDER_ID, String.class, "");
                String orderNameText = Arrays.stream(orderIdText.split(",")).map(orderId ->
                        orderIdNameMap.getOrDefault(orderId, orderId)
                ).collect(Collectors.joining(","));
                x.put(NewPaymentConst.ORDER_ID, orderNameText);
                x.put("order_data_id", orderIdText);
            });
        } catch (Exception e) {
            log.warn("fillOrderName error,user:{}", user, e);
        }
        return paymentDataList;
    }

    public List<ObjectDataDocument> parseDateTime(User user, IObjectDescribe objectDescribe, List<ObjectDataDocument> documents) {
        log.info("CustomerPaymentService parseDateTime start,tenantId {},apiName {}", user.getTenantId(), objectDescribe.getApiName());
        String timeStr = "946656000000";
        Map<String, IFieldDescribe> fieldDescribeMap = ObjectDescribeExt.of(objectDescribe).getFieldDescribeMap();
        for (ObjectDataDocument dataDocument : documents) {
            dataDocument.forEach((f, v) -> {
                IFieldDescribe fieldDescribe = fieldDescribeMap.get(f);
                if (fieldDescribe != null) {
                    if (fieldDescribe.getType().equals(IFieldType.DATE) || fieldDescribe.getType().equals(IFieldType.DATE_TIME) || fieldDescribe.getType().equals(IFieldType.TIME)) {
                        if (v != null && v.toString().equals(timeStr)) {
                            dataDocument.put(f, null);
                        }
                    }
                    if (fieldDescribe.getType().equals(IFieldType.QUOTE)) {
                        QuoteFieldDescribe describe = (QuoteFieldDescribe) fieldDescribe;
                        if (describe.getQuoteFieldType().equals(IFieldType.DATE) || describe.getQuoteFieldType().equals(IFieldType.DATE_TIME) || describe.getQuoteFieldType().equals(IFieldType.TIME)) {
                            if (v != null && v.toString().equals(timeStr)) {
                                dataDocument.put(f, null);
                            }
                        }
                    }
                }
            });
        }
        log.info("CustomerPaymentService parseDateTime end,tenantId {},apiName {}", user.getTenantId(), objectDescribe.getApiName());
        return documents;
    }

    public List<BaseImportAction.ImportError> customPaymentValidate(User user, IObjectDescribe paymentDescribe, List<BaseImportDataAction.ImportData> dataList, boolean isUncheck) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        ArrayList<String> options = Lists.newArrayList("10000", "10001", "10002");
        boolean containsAmountField = paymentDescribe.containsField(NewPaymentConst.AMOUNT);
        long openingBalanceDate = 0L;
        String date = serviceFacade.findTenantConfig(user, "opening_balance_date");
        if (!org.apache.commons.lang.StringUtils.isBlank(date)) {
            openingBalanceDate = Long.parseLong(date);
        }
        String isOpeningBalanceForceCheckStr = serviceFacade.findTenantConfig(user, "opening_balance_force_check");
        boolean isOpeningBalanceForceCheck = Objects.equals("1", isOpeningBalanceForceCheckStr);
        long finalOpeningBalanceDate = openingBalanceDate;
        dataList.forEach(x -> {
            String method = (String) x.getData().get("payment_term");
            BaseImportAction.ImportError importError = null;
            if (StringUtils.isNotBlank(method) && options.contains(method)) {
                importError = new BaseImportAction.ImportError(x.getRowNo(), I18N.text(NewPaymentI18N.SO_PAYMENT_PAYMENTTERMERROR));
            } else if (containsAmountField) {
                String amountString = x.getData().get(NewPaymentConst.AMOUNT, String.class);
                String collectionType = x.getData().get(NewPaymentConst.COLLECTION_TYPE, String.class);
                if (NumberUtils.isNumber(amountString)) {
                    BigDecimal amount = x.getData().get(NewPaymentConst.AMOUNT, BigDecimal.class, BigDecimal.ZERO);
                    if (NewPaymentUtil.isSupportRedPayment(x.getData())) {
                        if (collectionType.equals(PaymentCollectionTypeEnum.Red.value)
                                && amount.compareTo(BigDecimal.ZERO) >= 0) {
                            importError = new BaseImportAction.ImportError(x.getRowNo(), I18N.text(NewPaymentI18N.PAYMENT_RED_AMOUNT_NOT_GT_ZERO));
                        }
                        if (collectionType.equals(PaymentCollectionTypeEnum.Blue.value)
                                && amount.compareTo(BigDecimal.ZERO) <= 0) {
                            importError = new BaseImportAction.ImportError(x.getRowNo(), I18N.text(NewPaymentI18N.PAYMENT_BLUE_AMOUNT_NOT_GT_ZERO));
                        }
                        if (finalOpeningBalanceDate > 0L && isOpeningBalanceForceCheck) {
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTimeInMillis(finalOpeningBalanceDate);
                            calendar.add(Calendar.DAY_OF_MONTH, 1);
                            long updatedTimestamp = calendar.getTimeInMillis();

                            for (BaseImportDataAction.ImportData data : dataList) {
                                //设置了期初日期，需要校验
                                Long paymentTime = data.getData().get(NewPaymentConst.PAYMENT_TIME, Long.class);
                                boolean isOpeningBalanceData = data.getData().get(NewPaymentConst.OPENING_BALANCE, Boolean.class);
                                if (isOpeningBalanceData) {
                                    if (paymentTime >= updatedTimestamp) {
                                        importError = new BaseImportAction.ImportError(data.getRowNo(), I18N.text("payment.validate.opening.balance.check.error.0"));
                                        //throw new ValidateException("单据日期不可晚于期初日期");
                                    }
                                } else {
                                    if (paymentTime < updatedTimestamp) {
                                        importError = new BaseImportAction.ImportError(data.getRowNo(), I18N.text("payment.validate.opening.balance.check.error.1"));
                                        //throw new ValidateException("单据日期不可早于期初日期");
                                    }
                                }
                            }
                        }
                    } else {
                        if (!isUncheck && amount.compareTo(BigDecimal.ZERO) <= 0) {
                            importError = new BaseImportAction.ImportError(x.getRowNo(), I18N.text(NewPaymentI18N.PAYMENT_AMOUNT_MUST_GT_ZERO));
                        }
                    }
                }
            }
            if (Objects.nonNull(importError)) {
                errorList.add(importError);
            }
        });
        return errorList;
    }

    public List<BaseImportAction.ImportError> customOrderPaymentValidate(RequestContext requestContext, IObjectDescribe objectDescribe, List<BaseImportDataAction.ImportData> dataList) {
        String customerAccountConfigValue = serviceFacade.findTenantConfig(requestContext.getUser(), NewPaymentConst.CUSTOMER_ACCOUNT_CONFIG);
        if (!"2".equals(customerAccountConfigValue)) {
            return Lists.newArrayList();
        }
        boolean paymentNegative = NewPaymentUtil.supportPaymentNegative(requestContext.getTenantId());
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();

        Map<String, BigDecimal> paymentIdAmountMap = Maps.newHashMap();
        dataList.forEach(x -> {
            String paymentId = x.getData().get(NewPaymentConst.PAYMENT_ID, String.class);
            BigDecimal amount = x.getData().get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            if (amount.compareTo(BigDecimal.ZERO) < 0 && !paymentNegative) {
                errorList.add(new BaseImportAction.ImportError(x.getRowNo(), I18N.text(NewPaymentI18N.PAYMENT_AMOUNT_MUST_GT_ZERO)));
            } else {
                BigDecimal amountBefore = paymentIdAmountMap.computeIfAbsent(paymentId, key -> BigDecimal.ZERO);
                paymentIdAmountMap.put(paymentId, amountBefore.add(amount));
            }
        });
        Map<String, BigDecimal> availableAmountMap = queryPaymentAvailableAmountByPaymentId(requestContext.getUser(), Lists.newArrayList(paymentIdAmountMap.keySet()));
        Set<String> errorPaymentIds = Sets.newHashSet();
        paymentIdAmountMap.forEach((k, v) -> {
            BigDecimal availableAmount = availableAmountMap.getOrDefault(k, BigDecimal.ZERO);
            if (availableAmount.compareTo(v) < 0) {
                errorPaymentIds.add(k);
            }
        });

        dataList.forEach(x -> {
            String paymentId = x.getData().get(NewPaymentConst.PAYMENT_ID, String.class);
            if (errorPaymentIds.contains(paymentId)) {
                errorList.add(new BaseImportAction.ImportError(x.getRowNo(), I18N.text(NewPaymentI18N.PAYMENT_AMOUNT_LT_USED_AMOUNT)));
            }
        });

        //主对象客户字段不能为空
        Set<String> paymentIds = dataList.stream().map(x -> x.getData().get(NewPaymentConst.PAYMENT_ID, String.class)).collect(Collectors.toSet());
        List<IObjectData> paymentDatas = serviceFacade.findObjectDataByIds(requestContext.getTenantId(), Lists.newArrayList(paymentIds), NewPaymentConst.PAYMENT_API_NAME);
        Map<String, IObjectData> paymentId2PaymentDataMap = paymentDatas.stream().collect(Collectors.toMap(
                DBRecord::getId,
                data -> data)
        );
        dataList.forEach(x -> {
            String paymentId = x.getData().get(NewPaymentConst.PAYMENT_ID, String.class);
            IObjectData paymentData = paymentId2PaymentDataMap.get(paymentId);
            if (Objects.isNull(paymentData)) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(NewPaymentConst.PAYMENT_ID);
                String fieldLabel = Objects.nonNull(fieldDescribe) ? fieldDescribe.getLabel() : NewPaymentConst.PAYMENT_ID;
                errorList.add(new BaseImportAction.ImportError(x.getRowNo(), I18N.text(NewPaymentI18N.SPECIFIED_PARAM_ERROR, fieldLabel)));
            } else {
                String customerId = paymentData.get(NewPaymentConst.ACCOUNT_ID, String.class);
                if (Strings.isNullOrEmpty(customerId)) {
                    errorList.add(new BaseImportAction.ImportError(x.getRowNo(), I18N.text(NewPaymentI18N.PAYMENT_CUSTOMER_IS_EMPTY)));
                }
                if (NewPaymentUtil.isSupportRedPayment(paymentData)) {
                    checkOrderPaymentImportCollectionType(paymentData, x, errorList);
                }
            }
        });

        return errorList;
    }

    public void customOrderPaymentAfterImport(RequestContext requestContext, IObjectDescribe paymentOrderDescribe, List<IObjectData> actualList) {
        log.debug("OrderPaymentInsertImportDataAction customAfterImport actualList:{}", actualList);
        Map<String, List<String>> customerPaymentIdWithOrderIds = Maps.newHashMap();
        actualList.forEach(x -> {
            String paymentId = x.get(NewPaymentConst.PAYMENT_ID, String.class);
            String orderId = x.get(NewPaymentConst.ORDER_ID, String.class);
            if (StringUtils.isNotEmpty(paymentId) && StringUtils.isNotEmpty(orderId)) {
                customerPaymentIdWithOrderIds.computeIfAbsent(paymentId, k -> Lists.newArrayList()).add(orderId);
            }
        });
        String tenantId = requestContext.getTenantId();
        List<IObjectData> customerPaymentList = serviceFacade.findObjectDataByIdsIgnoreFormula(tenantId,
                Lists.newArrayList(customerPaymentIdWithOrderIds.keySet()), Utils.CUSTOMER_PAYMENT_API_NAME);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        customerPaymentList.forEach(customerPaymentObjectData -> {
            if (customerPaymentObjectData != null) {
                parallelTask.submit(() -> {
                    String paymentId = customerPaymentObjectData.getId();
                    List<String> orderIds = customerPaymentIdWithOrderIds.get(paymentId);
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderIds)) {
                        Set<String> oldOrderIds = getOrderList(requestContext.getUser(), paymentOrderDescribe, customerPaymentObjectData);
                        oldOrderIds.addAll(orderIds);
                        Map<String, Object> paramMap = Maps.newHashMap();
                        paramMap.put(NewPaymentConst.ORDER_ID, Joiner.on(",").join(oldOrderIds));
                        //update by quzf,父类有批量插入异步操作，所以此处更新时不关注版本号，防止和父类同时保存主数据版本号不一致报错
                        serviceFacade.updateWithMap(requestContext.getUser(), customerPaymentObjectData, paramMap);
                    }
                });
            }
        });
        try {
            parallelTask.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    protected Set<String> getOrderList(User user, IObjectDescribe orderPaymentDescribe, IObjectData paymentData) {
        List<IObjectData> dataList =
                serviceFacade.findDetailObjectDataListIgnoreFormula(orderPaymentDescribe, paymentData, user);
        Set<String> orderIdList = Sets.newHashSet();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dataList)) {
            orderIdList.addAll(dataList.stream().map(x -> x.get(NewPaymentConst.ORDER_ID, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toSet()));
        }
        return orderIdList;
    }


    public Map<String, BigDecimal> queryPaymentAvailableAmountByPaymentId(User user, List<String> paymentIds) {
        if (CollectionUtils.empty(paymentIds)) {
            return Maps.newHashMap();
        }
        String tenantId = user.getTenantId();
        String paymentIdSql = Joiner.on(",").join(paymentIds.stream().map(x -> String.format("'%s'", x)).collect(Collectors.toList()));
        String orderPaymentSqlFormat = "select sum(payment_amount) as payment_amount,payment_id from payment_order where tenant_id='%s' and is_deleted=0 and life_status <> 'invalid' and payment_id in(%s) group by payment_id";
        String paymentSqlFormat = "select id,amount from payment_customer where tenant_id = '%s' and id in(%s)";
        try {
            List<Map> paymentMapList = objectDataService.findBySql(tenantId, String.format(paymentSqlFormat, tenantId, paymentIdSql));
            List<Map> orderPaymentMapList = objectDataService.findBySql(tenantId, String.format(orderPaymentSqlFormat, tenantId, paymentIdSql));
            Map<String, BigDecimal> paymentIdAmountMap = Maps.newHashMap();
            if (!CollectionUtils.empty(paymentMapList)) {
                paymentMapList.forEach(x -> {
                    String paymentId = String.valueOf(x.get("id"));
                    BigDecimal amount = new BigDecimal(String.valueOf(x.get("amount")));
                    paymentIdAmountMap.put(paymentId, amount);
                });
            }
            Map<String, BigDecimal> paymentIdUsedAmountMap = Maps.newHashMap();
            if (!org.springframework.util.CollectionUtils.isEmpty(orderPaymentMapList)) {
                orderPaymentMapList.forEach(x -> {
                    String paymentId = String.valueOf(x.get("payment_id"));
                    BigDecimal usedAmount = new BigDecimal(String.valueOf(x.get("payment_amount")));
                    paymentIdUsedAmountMap.put(paymentId, usedAmount);
                });
            }
            Map<String, BigDecimal> availableAmountMap = Maps.newHashMap();
            paymentIds.forEach(x -> {
                BigDecimal amount = paymentIdAmountMap.getOrDefault(x, BigDecimal.ZERO);
                BigDecimal usedAmount = paymentIdUsedAmountMap.getOrDefault(x, BigDecimal.ZERO);
                availableAmountMap.put(x, amount.subtract(usedAmount));
            });
            return availableAmountMap;
        } catch (MetadataServiceException e) {
            log.warn("queryPaymentAvailableAmount error user:{},paymentIds:{} ", user, paymentIds, e);
            throw new ValidateException(e.getMessage());
        }
    }

    public void fillDetails(User user, String objectApiName, ObjectDataDocument objectData) {
        if (Objects.isNull(objectData)) {
            return;
        }
        List<IObjectDescribe> detailDescribeList = serviceFacade
                .findDetailDescribes(user.getTenantId(), objectApiName);
        Map<String, List<IObjectData>> details = serviceFacade
                .findDetailObjectDataList(detailDescribeList, objectData.toObjectData(), user);
        objectData.put("details", details);
    }

    public void checkOrderPaymentImportCollectionType(IObjectData objectData, BaseImportDataAction.ImportData detailData
            , List<BaseImportAction.ImportError> errorList) {
        String collectionType = objectData.get(NewPaymentConst.COLLECTION_TYPE, String.class);
        BigDecimal paymentAmount = detailData.getData().get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        String orderId = detailData.getData().get(NewPaymentConst.ORDER_ID, String.class);
        String returnedGoodsInvoiceId = detailData.getData().get(NewPaymentConst.RETURNED_GOODS_INVOICE_ID, String.class);

        if (Strings.isNullOrEmpty(returnedGoodsInvoiceId) == Strings.isNullOrEmpty(orderId)) {
            errorList.add(new BaseImportAction.ImportError(detailData.getRowNo(), I18N.text(NewPaymentI18N.ORDER_PAYMENT_ORDER_RETURN_ONLY_ONE)));
        }
        if (collectionType.equals(PaymentCollectionTypeEnum.Red.value)) {
            if (Strings.isNullOrEmpty(returnedGoodsInvoiceId)) {
                errorList.add(new BaseImportAction.ImportError(detailData.getRowNo(), I18N.text(NewPaymentI18N.ORDER_PAYMENT_RED_RETURN_ID_NOT_EMPTY)));
            }
            if (paymentAmount.compareTo(BigDecimal.ZERO) > 0) {
                errorList.add(new BaseImportAction.ImportError(detailData.getRowNo(), I18N.text(NewPaymentI18N.ORDER_PAYMENT_RED_AMOUNT_NOT_GT_ZERO)));
            }
        }
        if (collectionType.equals(PaymentCollectionTypeEnum.Blue.value)) {
            if (Strings.isNullOrEmpty(orderId)) {
                errorList.add(new BaseImportAction.ImportError(detailData.getRowNo(), I18N.text(NewPaymentI18N.ORDER_PAYMENT_BLUE_ORDER_ID_NOT_EMPTY)));
            }
            if (paymentAmount.compareTo(BigDecimal.ZERO) < 0) {
                errorList.add(new BaseImportAction.ImportError(detailData.getRowNo(), I18N.text(NewPaymentI18N.ORDER_PAYMENT_BLUE_AMOUNT_NOT_GT_ZERO)));
            }
        }
    }

    private static final List<String> PAYMENT_UN_SUPPORT_IMPORT_FIELDS = Lists.newArrayList(
            NewPaymentConst.ORDER_ID,
            NewPaymentConst.PAYMENT_AMOUNT,
            "extend_obj_data_id",
            "submit_time",
            "approve_employee_id",
            "approved_employee_id",
            "finance_employee_id",
            "finance_confirm_time",
            "origin_object",
            "origin_object_data_id",
            "origin_object_api_name"
    );

    private static final List<String> PAYMENT_VALIDATE_IGNORE_FIELDS = Lists.newArrayList(
            NewPaymentConst.ORDER_ID,
            NewPaymentConst.PAYMENT_AMOUNT,
            "extend_obj_data_id",
            "submit_time",
            "approve_employee_id",
            "approved_employee_id",
            "finance_employee_id",
            "finance_confirm_time"
    );

    private static final List<String> ORDER_PAYMENT_UN_SUPPORT_IMPORT_FIELDS = Lists.newArrayList(
            "extend_obj_data_id", "approve_employee_id"
    );

    private static final List<String> ORDER_PAYMENT_VALIDATE_IGNORE_FIELDS = Lists.newArrayList(
            "extend_obj_data_id", "approve_employee_id"
    );

    public void customPaymentHeader(List<IFieldDescribe> headerFieldList) {
        headerFieldList.removeIf(f -> PAYMENT_UN_SUPPORT_IMPORT_FIELDS.contains(f.getApiName()));
        headerFieldList.stream().filter(f -> "select_one".equals(f.getType())).forEach(k -> {
            String fieldApiName = k.getApiName();
            SelectOneFieldDescribe selectOneField = (SelectOneFieldDescribe) k;
            List<ISelectOption> selectOptions = selectOneField.getSelectOptions();
            Set<String> optionsToRemove = Sets.newHashSet();
            if (NewPaymentConst.PAYMENT_TERM.equals(fieldApiName)) {
                optionsToRemove.addAll(Lists.newArrayList("10000", "10001", "10002"));
            }
            selectOptions.removeIf(x -> optionsToRemove.contains(x.getValue()));
            selectOneField.setSelectOptions(selectOptions);
        });
    }

    public void customOrderPaymentHeader(List<IFieldDescribe> headerFieldList) {
        headerFieldList.removeIf(f -> ORDER_PAYMENT_UN_SUPPORT_IMPORT_FIELDS.contains(f.getApiName()));
    }

    public List<IFieldDescribe> getPaymentValidImportFields(List<IFieldDescribe> fieldDescribes) {
        fieldDescribes.removeIf(x -> PAYMENT_VALIDATE_IGNORE_FIELDS.contains(x.getApiName()));
        return fieldDescribes;
    }

    public List<IFieldDescribe> getOrderPaymentValidImportFields(List<IFieldDescribe> fieldDescribes) {
        fieldDescribes.removeIf(x -> ORDER_PAYMENT_VALIDATE_IGNORE_FIELDS.contains(x.getApiName()));
        return fieldDescribes;
    }


    private QueryResult<IObjectData> search(User user, String objectApiName, List<IFilter> filters, int offset, int limit) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setFilters(filters);
        return serviceFacade.findBySearchQuery(user, Utils.ORDER_PAYMENT_API_NAME, searchTemplateQuery);
    }

    public void updatePaymentOrderIdText(RequestContext requestContext, String paymentId) {
        if (StringUtils.isEmpty(paymentId)) {
            return;
        }
        IObjectData paymentData = serviceFacade.findObjectDataIncludeDeleted(requestContext.getUser(), paymentId, Utils.CUSTOMER_PAYMENT_API_NAME);
        if (ObjectDataExt.of(paymentData).isInvalid()) {
            return;
        }
        List<IObjectData> orderPaymentList = queryOrderPaymentByPaymentId(requestContext.getUser(), paymentId);
        updatePaymentOrderIdText(requestContext, paymentData, orderPaymentList);
    }
    public void updatePaymentOrderIdText(RequestContext requestContext, IObjectData paymentData, List<IObjectData> orderPaymentList) {
        Set<String> orderIds = Sets.newHashSet();
        orderIds.addAll(orderPaymentList.stream().map(x -> x.get(OrderPaymentConstants.Field.SalesOrder.apiName, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toSet()));
        paymentData.set(NewPaymentConst.ORDER_ID, Joiner.on(",").join(orderIds));
        serviceFacade.batchUpdateByFields(requestContext.getUser(), Lists.newArrayList(paymentData), Lists.newArrayList(NewPaymentConst.ORDER_ID));
    }
}
