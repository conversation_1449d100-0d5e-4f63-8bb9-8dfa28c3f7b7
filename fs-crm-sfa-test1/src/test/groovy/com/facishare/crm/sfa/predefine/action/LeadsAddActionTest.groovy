package com.facishare.crm.sfa.predefine.action

import com.beust.jcommander.internal.Maps
import com.facishare.crm.sfa.EnhancedBaseGroovyTest
import com.facishare.crm.sfa.lto.duplicated.DuplicatedProcessingService
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitRuleService
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitService
import com.facishare.crm.sfa.predefine.action.model.LeadsSaveArg
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum
import com.facishare.crm.sfa.predefine.service.*
import com.facishare.crm.sfa.predefine.service.bizquery.BizQuerySearchService
import com.facishare.crm.sfa.predefine.service.qywx.EnterpriseWechatService
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateOverTimeTaskService
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateTaskService
import com.facishare.crm.sfa.predefine.service.task.LeadsOverTimeTaskService
import com.facishare.crm.sfa.predefine.service.task.RecalculateTaskService
import com.facishare.crm.sfa.utilities.util.*
import com.facishare.paas.appframework.common.util.AppIdMapping
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAddAction
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction
import com.facishare.paas.appframework.metadata.AutoNumberLogicService
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl
import com.facishare.paas.metadata.util.SpringUtil
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.api.support.membermodification.MemberMatcher
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import org.spockframework.runtime.Sputnik
import org.springframework.context.ApplicationContext
import spock.lang.Shared
import spock.lang.Unroll

import static com.google.common.collect.HashBiMap.create
import static org.mockito.ArgumentMatchers.*

@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([
        SpringUtil.class,
        AccountAddAction.class,
        ProcurementAnalysisService.class,
        AppIdMapping.class,
        SFAConfigUtil.class,
        PoolOwnerRuleUtil.class,
        AccountUtil.class,
        LeadsUtils.class,
        DuplicatedProcessingUtils.class,
        CommonBizUtils.class
])
@SuppressStaticInitializationFor([
        "com.facishare.paas.appframework.core.model.PreDefineAction",
        "com.facishare.crm.sfa.predefine.action.LeadsAddAction",
        "com.facishare.paas.appframework.core.predef.action.AbstractStandardAddAction",
        "com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction",
        "com.facishare.crm.sfa.utilities.util.AccountUtil",
        "com.facishare.crm.sfa.utilities.util.LeadsUtils",
        "com.facishare.crm.sfa.utilities.util.SFAConfigUtil",
        "com.facishare.crm.sfa.utilities.util.JsonUtil",
        "com.facishare.crm.sfa.utilities.util.PoolOwnerRuleUtil",
        "com.facishare.crm.sfa.predefine.service.task.RecalculateTaskService",
        "com.facishare.crm.sfa.utilities.util.DuplicatedProcessingUtils",
        "com.facishare.crm.sfa.utilities.util.CampaignMembersUtil",
])
class LeadsAddActionTest extends EnhancedBaseGroovyTest {

    @Shared
    def tenantId = "79337"
    @Shared
    def userId = "1000"

    @Shared
    def user = new User(tenantId, userId)

    @Shared
    def serviceFacade

    @Shared
    def leadsAddAction

    @Shared
    def leadsPoolServiceImpl

    @Shared
    def objectLimitService

    @Shared
    def leadsOverTimeTaskService

    @Shared
    def leadsAllocateTaskService

    @Shared
    def leadsAllocateOverTimeTaskService

    @Shared
    def bizQuerySearchService

    @Shared
    def recalculateTaskService

    def setupSpec() {
        removeConfigFactory()
        removeI18N()

        // Mock Spring context and dependencies
        def applicationContext = PowerMockito.mock(ApplicationContext)
        PowerMockito.mockStatic(SpringUtil)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)

        leadsAddAction = PowerMockito.spy(new LeadsAddAction())
        PowerMockito.suppress(MemberMatcher.methods(ServiceFacade.class, "logWithCustomMessage"))
        PowerMockito.suppress(MemberMatcher.methodsDeclaredIn(ProcurementAnalysisService.class))
        Whitebox.setInternalState(leadsAddAction, "objectData", getObjectData(["id"], ["1"]))
        objectLimitService = PowerMockito.mock(ObjectLimitService)
        PowerMockito.when(objectLimitService.isGrayObjectLimit(anyString())).thenReturn(true)
        def procurementAnalysisService = PowerMockito.mock(ProcurementAnalysisService)
        def commonSqlService = PowerMockito.mock(CommonSqlServiceImpl)
        def autoNumberLogicService = PowerMockito.mock(AutoNumberLogicService)
        def sfaOpenApiMqService = PowerMockito.mock(SFAOpenApiMqService)
        def duplicatedProcessingService = PowerMockito.mock(DuplicatedProcessingService)
        def leadsUtil = PowerMockito.mock(LeadsUtil)
        def objectLimitRuleService = PowerMockito.mock(ObjectLimitRuleService)
        def sfaRedisService = PowerMockito.mock(SFARedisService)
        bizQuerySearchService = PowerMockito.mock(BizQuerySearchService)
        def enterpriseInfoService = PowerMockito.mock(SFAEnterpriseInfoService)
        def enterpriseWechatService = PowerMockito.mock(EnterpriseWechatService)
        leadsAllocateTaskService = PowerMockito.mock(LeadsAllocateTaskService)
        leadsAllocateOverTimeTaskService = PowerMockito.mock(LeadsAllocateOverTimeTaskService)
        leadsOverTimeTaskService = PowerMockito.mock(LeadsOverTimeTaskService)
        recalculateTaskService = PowerMockito.mock(RecalculateTaskService)

        leadsPoolServiceImpl = PowerMockito.mock(LeadsPoolServiceImpl)
        serviceFacade = PowerMockito.mock(ServiceFacade)
        def logger = PowerMockito.mock(Logger)
        Whitebox.setInternalState(leadsAddAction, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(LeadsAddAction.class, "PROCUREMENT_ANALYSIS_SERVICE", procurementAnalysisService)
        Whitebox.setInternalState(LeadsAddAction.class, "commonSqlService", commonSqlService)
        Whitebox.setInternalState(LeadsAddAction.class, "autoNumberLogicService", autoNumberLogicService)
        Whitebox.setInternalState(LeadsAddAction.class, "sfaOpenApiMqService", sfaOpenApiMqService)
        Whitebox.setInternalState(LeadsAddAction.class, "duplicatedProcessingService", duplicatedProcessingService)
        Whitebox.setInternalState(LeadsAddAction.class, "leadsUtil", leadsUtil)
        Whitebox.setInternalState(LeadsAddAction.class, "objectLimitService", objectLimitService)
        Whitebox.setInternalState(LeadsAddAction.class, "objectLimitRuleService", objectLimitRuleService)
        Whitebox.setInternalState(LeadsAddAction.class, "sfaRedisService", sfaRedisService)
        Whitebox.setInternalState(LeadsAddAction.class, "bizQuerySearchService", bizQuerySearchService)
        Whitebox.setInternalState(LeadsAddAction.class, "enterpriseInfoService", enterpriseInfoService)
        Whitebox.setInternalState(LeadsAddAction.class, "log", logger)
        Whitebox.setInternalState(leadsAddAction, "enterpriseWechatService", enterpriseWechatService)
        Whitebox.setInternalState(leadsAddAction, "leadsPoolServiceImpl", leadsPoolServiceImpl)
        Whitebox.setInternalState(leadsAddAction, "leadsAllocateTaskService", leadsAllocateTaskService)
        Whitebox.setInternalState(leadsAddAction, "leadsAllocateOverTimeTaskService", leadsAllocateOverTimeTaskService)
        Whitebox.setInternalState(leadsAddAction, "leadsOverTimeTaskService", leadsOverTimeTaskService)
        Whitebox.setInternalState(leadsAddAction, "recalculateTaskService", recalculateTaskService)
    }

    def setup() {

        def localUser = Spy(user)
        localUser.getUpstreamOwnerIdOrUserId() >> ""
        localUser.isGrayTenant() >> false
        def actionContext = createActionContext([
                requestContext: Mock(RequestContext),
                user          : localUser,
                tenantId      : tenantId,
                appId         : "crm",
        ])
        Whitebox.setInternalState(leadsAddAction, "actionContext", actionContext)

        PowerMockito.suppress(MemberMatcher.methodsDeclaredIn(AbstractStandardAddAction.class))
        PowerMockito.suppress(MemberMatcher.methodsDeclaredIn(BaseObjectSaveAction.class))
        PowerMockito.suppress(MemberMatcher.methodsDeclaredIn(DuplicatedProcessingService.class))
        PowerMockito.suppress(MemberMatcher.methodsDeclaredIn(JsonUtil.class))
        PowerMockito.suppress(MemberMatcher.methods(AccountUtil.class, "getPhoneNumberInfo"))
        PowerMockito.suppress(MemberMatcher.methods(LeadsUtils.class, "checkPoolLeadsLimit","getUserMainDepartId"))
        PowerMockito.suppress(MemberMatcher.methods(DuplicatedProcessingUtils.class, "createLeadsDuplicatedTask"))
        PowerMockito.suppress(MemberMatcher.methods(CampaignMembersUtil.class, "asyncInsertCampaignMembers"))
    }

    @Unroll
    def "test leads add with various data sources"() {
        given: "Prepare leads save argument"
        def arg = new LeadsSaveArg()
        def args = new ObjectDataDocument()
        args.put("from_marketing", fromMarketing)
        args.put("is_from_procurement", fromProcurement)
        args.put("name", "Test Lead")
        args.put("mobile", "***********")
        args.put("From", dataFrom)
        arg.setObjectData(args)
        arg.setSkipCheckCleanOwner(skipCheckCleanOwner)


        def localUser = Spy(user)
        localUser.getUpstreamOwnerIdOrUserId() >> ""
        localUser.isGrayTenant() >> false

        def actionContext = createActionContext([
                requestContext : Mock(RequestContext),
                user           : localUser,
                tenantId       : tenantId,
                appId          : appId,
                isFromOpenAPI  : false,
                isFromSmartForm: true
        ])
        Whitebox.setInternalState(leadsAddAction, "actionContext", actionContext)
        Whitebox.setInternalState(AppIdMapping.class, "appIdMapping", create(Maps.newHashMap("prm", prm)));
        Whitebox.setInternalState(leadsAddAction, "objectData", objectData)
        Whitebox.setInternalState(leadsAddAction, "objectData", objectData)
        Whitebox.setInternalState(leadsAddAction, "arg", arg)
        Whitebox.setInternalState(leadsAddAction, "dataFrom", "1")
        PowerMockito.doReturn(poolData).when(leadsPoolServiceImpl).getObjectPoolById(any(), any())

        def checkOutLimitResult = ObjectLimitService.CheckLimitResult.builder()
                .successIds(failureIds ? [] : ["1"])
                .failureIds(failureIds)
                .failureIdAndRuleName(["": ""])
                .build()
        PowerMockito.when(objectLimitService.checkOutUserObjectLimit(any(User), nullable(String), nullable(String), nullable(String), anyList(), nullable(IObjectDescribe), eq(true))).thenReturn(checkOutLimitResult)

        def checkLimitResult = ObjectLimitService.CheckLimitResult.builder().failureIds(failureIds).failureIdAndRuleName(["": ""]).build()
        PowerMockito.when(objectLimitService.checkObjectLimit(any(User), nullable(String), anyString(), nullable(List), eq(null), nullable(IObjectDescribe))).thenReturn(checkLimitResult)

        PowerMockito.mockStatic(PoolOwnerRuleUtil.class)
        PowerMockito.mockStatic(AccountUtil.class)
//        Map<String, Boolean> cleanOwnerResult = PoolOwnerRuleUtil.cleanOwner(actionContext.getUser(), Utils.LEADS_API_NAME, leadsPoolId, checkCleanOwnerDataList, objectDescribe);
        PowerMockito.doReturn(cleanOwnerResult).when(PoolOwnerRuleUtil, "cleanOwner", any(), any(), any(), any(), any())
        PowerMockito.doReturn("owner_department").when(AccountUtil, "getUserMainDepartName", anyString(), anyString())
        PowerMockito.doReturn(dataFrom).when(AccountUtil, "getStringValue", any(ObjectDataDocument), eq("From"), eq("1"))


        when: "Executing before method"
        leadsAddAction.before(arg)
        throw new OK()

        then: "Verify processing based on data source"
        thrown(exception2)

        where:
        fromMarketing | prm   | appId | skipCheckCleanOwner | isFromOpenAPI | failureIds | dataFrom | isGrayObjectLimit | fromProcurement | exception2        | poolData                     | cleanOwnerResult | objectData
        true          | "prm" | "prm" | true                | false         | ["1"]      | "1"      | true              | false           | OK                | getObjectData(["id"], ["1"]) | ["1": true]      | getObjectData(["id", "From"], ["1", "1"])
        true          | "prm" | "prm" | true                | false         | ["1"]      | "1"      | true              | false           | ValidateException | getObjectData(["id"], ["1"]) | ["1": true]      | getObjectData(["id", "From", "owner"], ["1", "1", ["111"]])
        true          | "prm" | "crm" | true                | false         | ["1"]      | "1"      | true              | false           | ValidateException | getObjectData(["id"], ["1"]) | ["1": true]      | getObjectData(["id", "From", "owner"], ["1", "1", ["111"]])
        true          | "prm" | "prm" | true                | false         | []         | "1"      | true              | false           | OK                | getObjectData(["id"], ["1"]) | ["1": false]     | getObjectData(["id", "From", "owner"], ["1", "1", ["111"]])
        true          | "prm" | "prm" | true                | false         | []         | "1"      | true              | false           | OK                | getObjectData(["id"], ["1"]) | ["1": true]      | getObjectData(["id", "From", "owner"], ["1", "1", ["111"]])
        true          | "prm" | "prm" | true                | false         | []         | "1"      | true              | false           | OK                | getObjectData(["id"], ["1"]) | ["1": true]      | getObjectData(["id", "From", "owner", "leads_pool_id"], ["1", "1", ["111"], "pool123"])
        true          | "prm" | "crm" | true                | false         | []         | "1"      | true              | false           | OK                | getObjectData(["id"], ["1"]) | ["1": true]      | getObjectData(["id", "From", "owner", "leads_pool_id"], ["1", "1", ["111"], "pool123"])
        false         | "prm" | "prm" | true                | false         | []         | "1"      | true              | true            | OK                | null                         | ["1": true]      | getObjectData(["id", "From", "leads_pool_id"], ["1", "1", "pool123"])
        false         | "prm" | "prm" | true                | false         | []         | "1"      | true              | false           | OK                | null                         | ["1": true]      | getObjectData(["id", "From"], ["1", "1"])
    }


//    @Unroll
//    def "test leads pool approval flow"() {
//        given: "Prepare leads save argument"
//        def arg = new LeadsSaveArg()
//        def args = new ObjectDataDocument()
//        args.put("leads_pool_id", leadsPoolId)
//        arg.setObjectData(args)
//        def objectData = getObjectData(["id", "leads_pool_id"], ["1", leadsPoolId])
//        PowerMockito.mockStatic(SFAConfigUtil.class)
//        Whitebox.setInternalState(leadsAddAction, "objectData", objectData)
//        Whitebox.setInternalState(leadsAddAction, "dataFrom", dataFrom)
//
//        PowerMockito.doReturn(configValue).when(SFAConfigUtil, "getConfigValue", anyString(), anyString(), anyString())
//
//        when: "Check if approval flow is needed"
//        leadsAddAction.needTriggerApprovalFlow()
//
//        then: "Verify approval flow condition"
//        noExceptionThrown()
//
//        where:
//        leadsPoolId | configValue | leads_pool_id | dataFrom
//        ""          | "1"         | null          | AccountAddAction.AccountFrom.HIGH_SEAS.getValue()
//        null        | ""          | "poolId"      | AccountAddAction.AccountFrom.CUSTOMER.getValue()
//        "pool123"   | ""          | "pool123"     | AccountAddAction.AccountFrom.HIGH_SEAS.getValue()
//    }

    @Unroll
    def "test getFuncPrivilegeCodes"() {
        given: "Set dataFrom to a specific value"
        Whitebox.setInternalState(leadsAddAction, "dataFrom", dataFrom)

        when: "Get function privilege codes"
        def privilegeCodes = leadsAddAction.getFuncPrivilegeCodes()

        then: "Verify privilege codes"
        noExceptionThrown()

        where:
        dataFrom                                                                | expectedSize
        "1"                                                                     | 1
        AccountAddAction.AccountFrom.PROCUREMENT_ENTERPRISE_TRANSFER.getValue() | 0
    }

    @Unroll
    def "test validateTransfer with different transfer counts"() {
        given: "Mock bizQuerySearchService"
        PowerMockito.doReturn(transferCount).when(bizQuerySearchService, "getUserTransferCount", anyString(), nullable(String))
        // long memberRuleLimit = bizQuerySearchService.getMemberRuleLimit(actionContext.getTenantId(), userId);
        PowerMockito.doReturn(memberRuleLimit).when(bizQuerySearchService, "getMemberRuleLimit", anyString(), anyString())


        when: "Call validateTransfer"
        def objectData = new ObjectDataDocument()
        objectData.put("from_marketing", false)
        Whitebox.invokeMethod(leadsAddAction, "validateTransfer", objectData)
        throw new OK()

        then:
        thrown(exception2)

        where:
        transferCount | memberRuleLimit | exception2
        9L            | 10L             | OK
        10L           | 10L             | ValidateException
        11L           | 10L             | ValidateException
    }

    @Unroll
    def "test getTriggerAction with different scenarios"() {
        given: "Set up test scenario"
        def actionContext = createActionContext([
                requestContext : Mock(RequestContext),
                user           : user,
                tenantId       : tenantId,
                isFromOpenAPI  : isFromOpenAPI,
                isFromSmartForm: isFromSmartForm
        ])
        Whitebox.setInternalState(leadsAddAction, "actionContext", actionContext)
        Whitebox.setInternalState(leadsAddAction, "isFromMarketing", isFromMarketing)

        when: "Call getTriggerAction"
        def result = Whitebox.invokeMethod(leadsAddAction, "getTriggerAction")

        then: "Verify the result"
        result == expectedAction

        where:
        isFromMarketing | isFromOpenAPI | isFromSmartForm | expectedAction
        true            | false         | false           | DuplicatedModels.TriggerAction.MARKETING_ADD
        false           | true          | false           | DuplicatedModels.TriggerAction.OPEN_API_ADD
        false           | false         | true            | DuplicatedModels.TriggerAction.SMART_FORM
        false           | false         | false           | DuplicatedModels.TriggerAction.ADD
    }

    @Unroll
    def "test modifyOwnerOrTeamMemberBeforeCreate with different pool configurations"() {
        given: "Setup test data"
        def objectData = getObjectData(["id", "leads_pool_id", "owner"], ["1", poolId, owner])
        def describe = Mock(IObjectDescribe)

        def arg = new LeadsSaveArg()
        arg.setSkipCheckCleanOwner(skipCheckCleanOwner)
        Whitebox.setInternalState(leadsAddAction, "arg", arg)

        PowerMockito.doReturn(poolData).when(leadsPoolServiceImpl).getObjectPoolById(anyString(), eq(poolId))
        PowerMockito.mockStatic(PoolOwnerRuleUtil.class)
        PowerMockito.doReturn(cleanOwnerResult).when(PoolOwnerRuleUtil, "cleanOwner", any(), any(), any(), any(), any())

        when: "Call modifyOwnerOrTeamMemberBeforeCreate"
        leadsAddAction.modifyOwnerOrTeamMemberBeforeCreate(objectData, describe)

        then: "Verify the result"
        noExceptionThrown()
//        if (poolId != null && cleanOwnerResult.containsKey("1") && cleanOwnerResult.get("1")) {
//            assert objectData.get("biz_status") == LeadsBizStatusEnum.UN_ASSIGNED.getCode()
//        }

        where:
        poolId  | owner     | poolData                     | cleanOwnerResult | skipCheckCleanOwner
        "pool1" | null      | getObjectData(["id"], ["1"]) | [:]              | true
        "pool1" | ["user1"] | getObjectData(["id"], ["1"]) | ["1": true]      | true
        "pool1" | ["user1"] | getObjectData(["id"], ["1"]) | ["1": false]     | true
        null    | null      | null                         | [:]              | true
        null    | ["user1"] | null                         | [:]              | false
    }


    @Unroll
    def "test needTriggerApprovalFlow with different configurations"() {
        given: "Setup test data and mocks"
        def objectData = getObjectData(["leads_pool_id", "From"], [poolId, dataFrom])
        Whitebox.setInternalState(leadsAddAction, "objectData", objectData)

        PowerMockito.mockStatic(SFAConfigUtil.class)
        PowerMockito.doReturn(configValue).when(SFAConfigUtil, "getConfigValue",
            eq(tenantId), eq("LEADS_APPROVAL_FLOW_POOL_ID"), anyString())

        when: "Call needTriggerApprovalFlow"
        def result = leadsAddAction.needTriggerApprovalFlow()

        then: "Verify the result"
        noExceptionThrown()

        where:
        poolId    | configValue | dataFrom                                    | expected
        ""        | "1"         | AccountAddAction.AccountFrom.HIGH_SEAS.getValue() | false
        null      | ""          | AccountAddAction.AccountFrom.CUSTOMER.getValue()  | false
        "pool123" | ""          | AccountAddAction.AccountFrom.HIGH_SEAS.getValue() | false
        "pool123" | "pool123"   | AccountAddAction.AccountFrom.HIGH_SEAS.getValue() | true
    }

    @Unroll
    def "test after method with different scenarios"() {
        given: "Setup test data"

        def arg = new LeadsSaveArg()
        def objectData = getObjectData(["id", "leads_pool_id", "owner"], ["1", poolId, owner])
        arg.setObjectData(ObjectDataDocument.of(objectData))
        def result = BaseObjectSaveAction.Result.builder().objectData(ObjectDataDocument.of(objectData)).build()

//        PowerMockito.when(leadsPoolServiceImpl.getObjectPoolById(any(), any())).thenReturn(poolData)
//        PowerMockito.when(serviceFacade.bulkSaveObjectData(anyList(), any(User) as User, anyBoolean(), anyBoolean(), any())).thenReturn(null)
        PowerMockito.doNothing().when(recalculateTaskService).send(anyString(), anyString(), eq("LeadsObj"), any(ActionCodeEnum.class));




        Whitebox.setInternalState(leadsAddAction, "arg", arg)
        Whitebox.setInternalState(leadsAddAction, "result", result)

        when: "Call after method"
        leadsAddAction.after(arg, result)

        then: "Verify the result"
        noExceptionThrown()

        where:
        poolId    | owner      | poolData
        "pool1"   | null       | getObjectData(["id"], ["pool1"])
        "pool1"   | ["user1"]  | getObjectData(["id"], ["pool1"])
        null      | ["user1"]  | null
        null      | null       | null
    }

}
