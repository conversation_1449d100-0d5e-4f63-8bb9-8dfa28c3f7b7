package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.LeadsPoolServiceImpl;
import com.facishare.crm.sfa.predefine.service.SFALicenseService;
import com.facishare.crm.sfa.predefine.service.SFALogService;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.predefine.service.real.SFARecyclingService;
import com.facishare.crm.sfa.predefine.service.real.SFARecyclingServiceImpl;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.enums.LifeStatusEnum;
import com.facishare.crm.util.Safes;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.GroupComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.impl.ui.layout.component.RelatedObjectList;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IGroupComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.joda.time.DateTime;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Slf4j
public class LeadsUtils {
    private static final LeadsPoolServiceImpl poolService = SpringUtil.getContext().getBean(LeadsPoolServiceImpl.class);
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private static final SpecialTableMapper SPECIAL_TABLE_MAPPER = SpringUtil.getContext().getBean(SpecialTableMapper.class);
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
	private static final InfraServiceFacade INFRA_SERVICE_FACADE = SpringUtil.getContext().getBean(InfraServiceFacadeImpl.class);
    private static final SFARecyclingService sfaRecyclingService = SpringUtil.getContext().getBean(SFARecyclingServiceImpl.class);
    private static final CRMNotificationServiceImpl crmNotificationService = SpringUtil.getContext().getBean("crmNotificationService", CRMNotificationServiceImpl.class);
    protected static final SFALogService sfaLogService = SpringUtil.getContext().getBean(SFALogService.class);
    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    private static final SFALicenseService sfaLicenseService = SpringUtil.getContext().getBean(SFALicenseService.class);

    // 810 需求，线索转换支持客户联系人从对象，灰度企业校验从对象
    public static final String TRANSFER_SUPPORT_DETAILS_VALIDATE = "transfer_support_details_validate";
    /**
     * 更新的时候跳过更新字段
     */
    public static List<String> exceptFields = Lists.newArrayList(AccountConstants.Field.EXPIRE_TIME
            , AccountConstants.Field.LAST_FOLLOWED_TIME
            , LeadsConstants.LAST_FOLLOW_TIME
            , AccountConstants.Field.OWNER_MODIFIED_TIME, AccountConstants.Field.REMIND_DAYS
            , AccountConstants.Field.DEAL_STATUS, AccountConstants.Field.LAST_DEAL_TIME);

    public static List<Map> getLeadsPoolCountByOwnerIdListAndPoolId(String tenantId, List<String> ownerIds, String poolId) {
        List<Map> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(ownerIds)) {
            return result;
        }
        //String ownerSql = getListQuerySql(ownerIds);
        String querySql = String.format("select  leads_pool_id,owner,count(1) as count from  biz_leads " +
                "where tenant_id='%s' and owner = %s  and leads_pool_id='%s' and  biz_status!='%s' and life_status!='%s' " +
                "GROUP BY owner,leads_pool_id", SqlEscaper.pg_escape(tenantId), SqlEscaper.any_clause(ownerIds), SqlEscaper.pg_escape(poolId), LeadsBizStatusEnum.TRANSFORMED.getCode(), LifeStatusEnum.INVALID.value);
        try {
            result = objectDataService.findBySql(tenantId, querySql);
        } catch (Exception e) {
            log.error("getLeadsPoolCountByOwnerIdListAndPoolId error {}", tenantId, e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        return result;
    }

    public static List<Map> getLeadsPoolCountByOwnerIdList(String tenantId, List<String> ownerIds) {
        List<Map> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(ownerIds)) {
            return result;
        }
       // String ownerSql = getListQuerySql(ownerIds);
        String querySql = String.format("select  leads_pool_id,owner,count(1) as count from  biz_leads " +
                "where tenant_id='%s' and owner = %s  and length(leads_pool_id)>=1 and biz_status = ANY ('{\"un_assigned\", \"un_processed\", \"processed\", \"closed\"}') and life_status = ANY ('{\"ineffective\", \"under_review\", \"in_change\", \"normal\"}') " +
                "GROUP BY owner,leads_pool_id", SqlEscaper.pg_escape(tenantId), SqlEscaper.any_clause(ownerIds));
        try {
            result = objectDataService.findBySql(tenantId, querySql);
        } catch (Exception e) {
            log.error("getLeadsPoolCountByOwnerIdList error {}", tenantId, e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        return result;
    }

    public static List<Map> getLeadsPoolCountByDataList(String tenantId, List<IObjectData> dataList) {
        List<String> ids = Lists.newArrayList();
        dataList.stream().forEach(l -> ids.addAll(l.getOwner()));
        List<String> ownerIds = ids.stream().distinct().collect(Collectors.toList());
        List<Map> result = getLeadsPoolCountByOwnerIdList(tenantId, ownerIds);
        return result;
    }

    public static String getListQuerySql(List<String> list) {
        StringBuilder builder = new StringBuilder();
        for (String id : list) {
            if (builder.length() == 0) {
                String str = String.format("'%s'", id);
                builder.append(str);
            } else {
                String str = String.format(",'%s'", id);
                builder.append(str);
            }
        }
        return builder.toString();
    }

    public static String getObjectIdFromLeadsTransferLog(String tenantId, String apiName, String leadsId) {
        String result = null;
        String keyName = "";
        if (apiName.equals(SFAPreDefineObject.Account.getApiName())) {
            keyName = "account_id";
        } else if (apiName.equals(SFAPreDefineObject.Contact.getApiName())) {
            keyName = "contact_id";
        } else if (SFAPreDefineObject.Partner.getApiName().equals(apiName)) {
            keyName = "partner_id";
        }
        if (!StringUtils.isEmpty(keyName)) {
            String querySql = String.format("select  %s as object_id from  biz_leads_transfer_log " +
                    "where tenant_id='%s'  and leads_id='%s'", SqlEscaper.pg_escape(keyName), SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(leadsId));
            try {
                List<Map> mapList = objectDataService.findBySql(tenantId, querySql);
                if (mapList.size() > 0) {
                    Map map = mapList.get(0);
                    if (!CollectionUtils.isEmpty(map) && map.containsKey("object_id")) {
                        result = map.get("object_id").toString();
                    }
                }
            } catch (Exception e) {
                log.error("getObjectIdFromLeadsTransferLog error {}", tenantId, e);
                throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
            }
        }
        return result;
    }

    public static List<Map> getRelatedObjectDataById(String tenantId, String apiName, String id) {
        String tableName = "";
        String keyName = "";
        if (apiName.equals(SFAPreDefineObject.Account.getApiName())) {
            tableName = "biz_account";
            keyName = "account_id";
        }
        if (apiName.equals(SFAPreDefineObject.Contact.getApiName())) {
            tableName = "biz_contact";
            keyName = "contact_id";
        }
        if (apiName.equals(SFAPreDefineObject.Opportunity.getApiName())) {
            tableName = "biz_opportunity";
        }
        if (apiName.equals(SFAPreDefineObject.NewOpportunity.getApiName())) {
            tableName = "new_opportunity";
        }
        if (apiName.equals(SFAPreDefineObject.Partner.getApiName())) {
            tableName = "partner";
            keyName = "partner_id";
        }
        List<Map> result = Lists.newArrayList();
        if (StringUtils.isEmpty(tableName)) {
            return result;
        }
        String querySql;
        if (!StringUtils.isEmpty(tableName)) {
            if ((apiName.equals(SFAPreDefineObject.Account.getApiName()) || apiName.equals(SFAPreDefineObject.Contact.getApiName())) || apiName.equals(SFAPreDefineObject.Partner.getApiName()) && !StringUtils.isEmpty(keyName)) {
                querySql = String.format("select  b.%s as _id,a.name from  %s a join biz_leads_transfer_log b on a.tenant_id=b.tenant_id and a.id =b.%s" +
                        "  where a.tenant_id='%s'  and b.leads_id='%s'", SqlEscaper.pg_escape(keyName), SqlEscaper.pg_escape(tableName), SqlEscaper.pg_escape(keyName), SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(id));
            } else {
                querySql = String.format("select  id as _id,name from  %s " +
                        "where tenant_id='%s'  and leads_id='%s'", SqlEscaper.pg_escape(tableName), SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(id));
            }
            try {
                result = objectDataService.findBySql(tenantId, querySql);
            } catch (Exception e) {
                log.error("getRelatedObjectDataById error {}", tenantId, e);
                throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
            }
        }
        return result;
    }

    public static List<Map> getOpportunityDataById(String tenantId, String apiName, String id) {
        List<Map> opportunityDataByLeadsId = getOpportunityDataByLeadsId(tenantId, apiName, id);
        if (!ObjectUtils.allIsEmpty(opportunityDataByLeadsId)){
            return opportunityDataByLeadsId;
        }
        String tableName = "";
        if (apiName.equals(SFAPreDefineObject.Opportunity.getApiName())) {
            tableName = "biz_opportunity";
        }
        if (apiName.equals(SFAPreDefineObject.NewOpportunity.getApiName())) {
            tableName = "new_opportunity";
        }
        List<Map> result = Lists.newArrayList();
        if (StringUtils.isEmpty(tableName)) {
            return result;
        }
        String sql;
        if (!StringUtils.isEmpty(tableName)) {
            if (apiName.equals(SFAPreDefineObject.Opportunity.getApiName())) {
                sql = String.format("select  id as _id,name,status,biz_status from  %s " +
                        "where tenant_id='%s'  and leads_id='%s'", SqlEscaper.pg_escape(tableName), SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(id));
            } else {
                sql = String.format("select  id as _id,name,sales_status,sales_stage from  %s " +
                        "where tenant_id='%s'  and leads_id='%s'", SqlEscaper.pg_escape(tableName), SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(id));
            }
            try {
                result = objectDataService.findBySql(tenantId, sql);
            } catch (Exception e) {
                log.error("getRelatedObjectDataById error {}", tenantId, e);
                throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
            }
        }
        return result;
    }

    public static List<Map> getOpportunityDataByLeadsId(String tenantId,String apiName, String leadsId) {
        List<Map> result = Lists.newArrayList();
        try {
            String selectLeadsLogSql = String.format("select tenant_id,opportunity_id,new_opportunity_id from biz_leads_transfer_log " +
                    "where tenant_id='%s' and leads_id='%s'", SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(leadsId));
            List<Map> selectLeadsLogResult = objectDataService.findBySql(tenantId, selectLeadsLogSql);
            if (ObjectUtils.allIsEmpty(selectLeadsLogResult)) {
                return result;
            }
            Object opportunityId = null;
            String sql = null;
            if (apiName.equals(SFAPreDefineObject.Opportunity.getApiName())) {
                opportunityId = selectLeadsLogResult.get(0).get("opportunity_id");
                sql = String.format("select id as _id,name,status,biz_status from biz_opportunity where id='%s'",
                        SqlEscaper.pg_escape(String.valueOf(opportunityId)));
            }
            if (apiName.equals(SFAPreDefineObject.NewOpportunity.getApiName())) {
                opportunityId = selectLeadsLogResult.get(0).get("new_opportunity_id");
                sql = String.format("select id as _id,name,sales_status,sales_stage from new_opportunity where id='%s'",
                        SqlEscaper.pg_escape(String.valueOf(opportunityId)));
            }
            if (ObjectUtils.allNotEmpty(opportunityId, sql)) {
                return objectDataService.findBySql(tenantId, sql);
            }
        } catch (Exception e) {
            log.error("getOpportunityDataByLeadsId error {}", tenantId, e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        return result;
    }

    public static void insertLeadsOwnerHistory(String tenantId, String userId, List<String> leadIds, String ownerId) {
        for (String leadId : leadIds) {
            String ownerHistoryId = UUID.randomUUID().toString().replace("-", "");
            String sql = String.format("INSERT INTO " +
                            " biz_data_owner_history (id,name,tenant_id,data_id,owner_id,created_by,create_time,last_modified_by,data_object_describe_api_name)" +
                            " VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','LeadsObj')",
                    ownerHistoryId, ownerHistoryId, tenantId, leadId, ownerId, userId, System.currentTimeMillis(), userId);
            try {
                SPECIAL_TABLE_MAPPER.setTenantId(tenantId).insertBySql(sql);
                //objectDataService.findBySql(tenantId, sql); //更新用
            } catch (Exception e) {
                log.error("insertLeadsOwnerHistory error {}", tenantId, e);
                throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
            }
        }
    }

    public static void setClaimLogList(IObjectData objectData, String poolId, List<Map<String, String>> claimLogList,
                                       boolean isOutUser, boolean isTakeBack) {
        if (claimLogList == null) claimLogList = Lists.newArrayList();
        Map<String, String> maps = Maps.newHashMap();
        if (isOutUser) {
            if (objectData.getOutOwner().size() > 0) {
                maps.put("owner", String.valueOf(objectData.getOutOwner().get(0)));
            }
        } else {
            if (objectData.getOwner().size() > 0) {
                maps.put("owner", objectData.getOwner().get(0));
            }
        }
        maps.put("leads_pool_id", poolId);
        maps.put("leads_id", objectData.getId());
        claimLogList.add(maps);
        if (isTakeBack) {
            Map<String, String> outerMap = Maps.newHashMap();
            if (objectData.getOutOwner().size() > 0) {
                outerMap.put("owner", String.valueOf(objectData.getOutOwner().get(0)));
            }
            outerMap.put("leads_pool_id", poolId);
            outerMap.put("leads_id", objectData.getId());
            claimLogList.add(outerMap);
        }
    }

    public static void insertLeadsClaimLog(String tenantId, String userId, List<Map<String, String>> dataList, String actionCode) {
        for (Map<String, String> data : dataList) { //退回，收回 操作数据量不会太大，暂时采用循环处理方式
            String logId = UUID.randomUUID().toString().replace("-", "");
            String leadsId = data.get("leads_id");
            String ownerId = data.get("owner");
            String poolId = data.get("leads_pool_id");
            if (!StringUtils.isEmpty(leadsId) && !StringUtils.isEmpty(ownerId) && !StringUtils.isEmpty(poolId)) {
                String delSql = String.format("DELETE FROM biz_data_claim_log WHERE tenant_id='%s' AND api_name='LeadsObj' " +
                        " AND pool_id='%s' AND object_id='%s'" +
                        " AND employee_id='%s' ", tenantId, poolId, leadsId, ownerId);
                String sql = String.format("INSERT INTO " +
                                " biz_data_claim_log (id,name,tenant_id,pool_id,object_id,employee_id,operation_time,api_name," +
                                " action_code, created_by,create_time,last_modified_by,last_modified_time)" +
                                " VALUES ('%s','%s','%s','%s','%s','%s','%s','LeadsObj','%s','%s','%s','%s','%s')",
                        logId, logId, tenantId, poolId, leadsId, ownerId, System.currentTimeMillis(), actionCode, userId,
                        System.currentTimeMillis(), userId, System.currentTimeMillis());
                try {
                    SPECIAL_TABLE_MAPPER.setTenantId(tenantId).deleteBySql(delSql);
                    SPECIAL_TABLE_MAPPER.setTenantId(tenantId).insertBySql(sql);
                } catch (Exception e) {
                    throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
                }
            }
        }
    }

    public static List<String> checkClaimRuleLimit(User user, List<String> objectIds, String poolId) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(objectIds) || StringUtils.isEmpty(poolId)) {
            return result;
        }
        String key = "claim_interval_days";
        int claimIntervalDays = 0;
        IObjectData poolData = poolService.getObjectPoolById(user.getTenantId(), poolId);
        if (poolData == null) {
            return result;
        }
        Object value = poolData.get(key);
        if (value != null) {
            try {
                claimIntervalDays = Integer.parseInt(value.toString());
            } catch (Exception ignored) {
            }
        }
        if (claimIntervalDays <= 0) {
            return result;
        }
       // String idString = getListQuerySql(objectIds);
        List<Map> maps;
        String userId = user.isOutUser() ? user.getOutUserId() : user.getUpstreamOwnerIdOrUserId();
        String querySql = String.format("SELECT object_id, operation_time" +
                        "  FROM biz_data_claim_log WHERE tenant_id='%s' " +
                        " AND pool_id='%s' AND api_name='LeadsObj' AND object_id = %s AND employee_id='%s' "
                , user.getTenantId(), SqlEscaper.pg_escape(poolId), SqlEscaper.any_clause(objectIds), SqlEscaper.pg_escape(userId));
        try {
            maps = objectDataService.findBySql(user.getTenantId(), querySql);

        } catch (Exception e) {
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        if (!CollectionUtils.isEmpty(maps)) {
            long actualIntervalDays = 0;
            for (Map m : maps) {
                actualIntervalDays = (System.currentTimeMillis() - Long.parseLong(m.get("operation_time").toString())) / (60 * 60 * 24 * 1000);
                if (actualIntervalDays < claimIntervalDays) {
                    result.add(m.get("object_id").toString());
                }
            }
            if (objectIds.size() > 1) {
                //暂时不需要做什么操作
            } else if (result.size() > 0) {
                throw new ValidateException(String.format(I18N.text(SFA_CANT_RECEIVE_SAME_IN_DAYS),
                        claimIntervalDays, I18N.text("LeadsObj.attribute.self.display_name")));
            }
        }
        return result;
    }

    public static List<String> getLeadsPoolIds(List<IObjectData> dataList) {
        return dataList.stream().filter(x ->
                        org.apache.commons.lang3.StringUtils.isNotEmpty(x.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), String.class)))
                .map(x -> x.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName()).toString()).distinct().collect(Collectors.toList());
    }

    public static boolean hasOwner(IObjectData objectData) {
        ObjectDataExt dataExt = ObjectDataExt.of(objectData);
        List<String> ownerList = dataExt.getOwner();
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(ownerList)) {
            return false;
        }
        String owner = ownerList.get(0);
        return !org.apache.commons.lang3.StringUtils.isEmpty(owner) && !"0".equals(owner);
    }

    public static boolean isPoolIdEmpty(String poolId) {
        return "null".equals(poolId) || StringUtils.isEmpty(poolId);
    }

    public static Map<String, Object> getApprovalFlowTriggerMap(IObjectDescribe objectDescribe,
                                                                String leadsPoolId, Map<String, Object> fieldMap) {
        SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe("leads_pool_id");
        fieldMap.put("leadsPoolId", leadsPoolId);
        String leadsPoolName = "";
        if (fieldDescribe.getOption(leadsPoolId).isPresent()) {
            leadsPoolName = fieldDescribe.getOption(leadsPoolId).get().getLabel();
        }
        fieldMap.put("leadsPoolName", leadsPoolName);
        return fieldMap;
    }

    // 线索联系人支持从对象添加的方法
    public static boolean transferObjIsNull(Boolean supportDetailObj, Map<String, ObjectDataDocument> dataList, String apiName) {
        ObjectDataDocument document = getTransferMasterObjByApiName(supportDetailObj, dataList, apiName);
        return Safes.isEmpty(document) || document == null || document.toObjectData() == null;
    }

    // 线索联系人支持从对象添加的方法
    @SuppressWarnings("rawtypes")
    public static ObjectDataDocument getTransferMasterObjByApiName(Boolean supportDetailObj, Map<String, ObjectDataDocument> dataList, String apiName) {

        ObjectDataDocument objectDataDocument = Safes.of(dataList).get(apiName);
        if (Objects.equals(apiName, SFAPreDefineObject.NewOpportunity.getApiName())
                || isSupportDetailAccountOrContact(supportDetailObj, apiName)) {
            // noinspection unchecked
            return Safes.isEmpty(objectDataDocument) ? null : ObjectDataDocument.of((Map) objectDataDocument.get("object_data"));
        } else {
            return objectDataDocument;
        }
    }

    // 线索联系人支持从对象添加的方法
    public static boolean isSupportDetailAccountOrContact(Boolean supportDetailObj, String apiName) {
        if (supportDetailObj) {
            return Objects.equals(apiName, SFAPreDefineObject.Account.getApiName()) ||
                    Objects.equals(apiName, SFAPreDefineObject.Contact.getApiName());
        } else {
            return false;
        }
    }


    public static boolean isCrmAdmin(User user) {
        if (!user.getIsCrmAdmin().isPresent()) {
            user.setIsCrmAdmin(Optional.of(serviceFacade.isAdmin(user)));
        }
        return user.getIsCrmAdmin().get();
    }

    public static void sendCRMNotification(User user, String remindContent, Integer remindRecordType, String title
            , String dataId, List<String> receiverIds) {
//        List<Integer> realReceiverIds = Lists.newArrayList();
//        for (String receiverId : receiverIds) {
//            try {
//                Integer realId = Integer.valueOf(receiverId);
//                realReceiverIds.add(realId);
//            } catch (Exception e) {
//                log.error("LeadsUtils sendCRMNotification error", e);
//            }
//        }
//        CRMNotification crmNotification = CRMNotification.builder().sender(user.getUpstreamOwnerIdOrUserId())
//                .remindRecordType(remindRecordType).title(title).content(remindContent).dataId(dataId).content2Id(user.getUpstreamOwnerIdOrUserId())
//                .receiverIds(new HashSet<>(realReceiverIds))
//                .objectApiName(Utils.LEADS_API_NAME)
//                .build();
//        crmNotificationService.sendCRMNotification(user, crmNotification);
    }

    public static void setImportFields(String objectCode, List<IFieldDescribe> fieldDescribes) {
        Optional<IFieldDescribe> poolDescribeOptional = fieldDescribes.stream().filter(h -> "leads_pool_id".equals(h.getApiName())).findFirst();
        Optional<IFieldDescribe> ownerDescribeOptional = fieldDescribes.stream().filter(h -> "owner".equals(h.getApiName())).findFirst();
        if ("LeadsPoolObj".equals(objectCode) || "salescluepool".equals(objectCode)) {
            poolDescribeOptional.ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(true));
            ownerDescribeOptional.ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(false));
        } else {
            poolDescribeOptional.ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(false));
            ownerDescribeOptional.ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(true));
        }
    }

    public static String getUserMainDepartId(String tenantId, String userId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(userId)) {
            return "";
        }
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = serviceFacade.getMainDeptInfo(tenantId, userId, Lists.newArrayList(userId));
        if (mainDeptInfoMap.containsKey(userId)) {
            return mainDeptInfoMap.get(userId).getDeptId();
        }
        return "";
    }

    public static Map<String, QueryDeptInfoByUserIds.MainDeptInfo> getUserMainDepartId(String tenantId, String userId, List<String> userIds) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(userId)) {
            return Maps.newHashMap();
        }
        return serviceFacade.getMainDeptInfo(tenantId, userId, userIds);
    }

    public static void handleLeadsRelatedComponents(ILayout layout, String relatedComponentApiName) {
        LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
            try {
                List<IComponent> childComponents = x.getChildComponents();
                Optional<IComponent> componentOp = childComponents.stream().filter(c -> relatedComponentApiName.equals(c.getName())).findFirst();
                componentOp.ifPresent(childComponents::remove);
                x.setChildComponents(childComponents);
            } catch (MetadataServiceException e) {
                log.error("Set LeadsObj RelatedComponent error", e);
            }
        });
    }

    public static void handleLeadsTransferLogRelatedComponents(ILayout layout, String relatedComponentApiName) {
        LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
            try {
                List<IComponent> childComponents = x.getChildComponents();
                Optional<IComponent> componentOp = childComponents.stream().filter(c -> relatedComponentApiName.equals(c.getName())).findFirst();
                if (componentOp.isPresent()) {
                    componentOp.get().setButtons(Lists.newArrayList());
                    childComponents.remove(componentOp.get());
                }
                RelatedObjectList component = new RelatedObjectList();
                component.setName(relatedComponentApiName);
                component.setHeader(I18N.text("sfa.leads.transfer_log.related.name"));
                component.setRefObjectApiName("LeadsTransferLogObj");
                if ("LeadsTransferLogObj_account_id_related_list".equals(relatedComponentApiName)) {
                    component.setRelatedListName("account_leads_transfer_log_list");
                    component.setFieldApiName("account_id");
                } else if ("LeadsTransferLogObj_contact_id_related_list".equals(relatedComponentApiName)) {
                    component.setRelatedListName("contact_leads_transfer_log_list");
                    component.setFieldApiName("contact_id");
                }
                component.set("relationType", 2);
                component.setButtons(Lists.newArrayList());
                ITableColumn column = getTableColumn(I18N.text("VocalizeObj.field.name.label")/*编码*/, "name", "auto_number");
                component.addField(column);
                column = getTableColumn(I18N.text("VocalizeObj.field.owner.label")/*负责人*/, "owner", "employee");
                component.addField(column);
                column = getTableColumn(I18N.text("VocalizeObj.field.create_time.label")/*创建时间*/, "create_time", "date_time");
                component.addField(column);
                component.setIsHidden(false);
                childComponents.add(component);
                x.setChildComponents(childComponents);
            } catch (MetadataServiceException e) {
                log.error("Set LeadsTransferLogObj RelatedComponent error", e);
            }
        });
    }

    public static void handleLeadsFlowRecordRelatedComponents(ILayout layout) {
        String relatedComponentApiName = "LeadsFlowRecordObj_account_id_related_list";
        LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
            try {
                List<IComponent> childComponents = x.getChildComponents();
                Optional<IComponent> componentOp = childComponents.stream().filter(c -> relatedComponentApiName.equals(c.getName())).findFirst();
                if (componentOp.isPresent()) {
                    componentOp.get().setButtons(Lists.newArrayList());
                    childComponents.remove(componentOp.get());
                }
                RelatedObjectList component = new RelatedObjectList();
                component.setName(relatedComponentApiName);
                component.setHeader(I18N.text("sfa.leads.flow_record.related.name"));
                component.setRefObjectApiName("LeadsFlowRecordObj");
                component.setRelatedListName("leads_leads_flow_record_list");
                component.setFieldApiName("leads_id");
                component.set("relationType", 2);
                component.setButtons(Lists.newArrayList());
                component.setIsHidden(false);
                childComponents.add(component);
                x.setChildComponents(childComponents);
            } catch (MetadataServiceException e) {
                log.error("Set LeadsTransferLogObj RelatedComponent error", e);
            }
        });
    }

    private static TableColumn getTableColumn(String lableName, String name, String renderType) {
        TableColumn tableColumn = new TableColumn();
        tableColumn.setLabelName(lableName);
        tableColumn.setName(name);
        tableColumn.setRenderType(renderType);
        return tableColumn;
    }

    public static void insertCrmDealDataRelation(String tenantId, String dataId, int type, String employeeId) {
        String updateSql = String.format("with upsert as (update crm_deal_data_relation set update_time='%s' where ei=%s and data_id='%s' and type=%s and employee_id=%s returning *) \n" +
                        "insert into crm_deal_data_relation select %s,'%s',%s,%s,'%s' \n" +
                        "where not exists (select 1 from upsert where ei=%s and  data_id='%s' and type=%s and employee_id=%s); ",
                DateTime.now(), SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(dataId), type, SqlEscaper.pg_escape(employeeId), SqlEscaper.pg_escape(tenantId),
                SqlEscaper.pg_escape(dataId), type, SqlEscaper.pg_escape(employeeId), DateTime.now(), SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(dataId),
                type, SqlEscaper.pg_escape(employeeId));
        try {
            SPECIAL_TABLE_MAPPER.setTenantId(tenantId).batchUpdateBySql(updateSql);
        } catch (Exception e) {
            log.error("insertCrmDealDataRelation error {}", tenantId, e);
        }

    }

    public static List<String> getImportRemovedFields() {
        return Lists.newArrayList(
                "life_status", "owner_department", "assigned_time", "expire_time", "last_follower", "last_follow_time",
                "leads_status", "out_resources", "owner_change_time", "picture_path", "resale_count", "biz_status",
                "transform_time", "assigner_id", "account_id", "contact_id", "opportunity_id", "new_opportunity_id", "returned_time", "remind_days",
                "is_duplicated", "is_collected", "collected_to", "refresh_duplicated_version", "enterprise_wechat_user_id", "conversion_probability",
                "leads_stage", "biz_reg_name", "behavior_integral", "phone_number_attribution_country",
                "phone_number_attribution_location", "phone_number_attribution_address", "phone_number_attribution_city",
                "phone_number_attribution_province", "phone_number_attribution_district", "leads_stage_changed_time",
                "extend_days", "changed_to_mql_time", "industry_ext", "extend_reason"
        );
    }

    public static List<IObjectData> getLeadsPoolList(ActionContext actionContext, List<IObjectData> dataList) {
        List<IObjectData> result = Lists.newArrayList();
        List<String> leadsPoolIds = dataList.stream()
                .filter(l -> !StringUtils.isEmpty(l.get("leads_pool_id", String.class)))
                .map(l -> l.get("leads_pool_id", String.class)).distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(leadsPoolIds)) {
            result = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),
                    leadsPoolIds, Utils.LEADS_POOL_API_NAME);
        }
        return result;
    }

    public static List<Map> getPoolAllocateRules(String tenantId, List<String> poolIds) {
        List<Map> result;
        try {
            //String poolIdQuerySql = getListQuerySql(poolIds);
            String querySql = String.format("select tenant_id,pool_id from biz_pool_allocate_rule " +
                    "where tenant_id='%s' and object_api_name='%s' and is_deleted=0 and pool_id =  %s", SqlEscaper.pg_escape(tenantId), Utils.LEADS_POOL_API_NAME, SqlEscaper.any_clause(poolIds));

            result = objectDataService.findBySql(tenantId, querySql);
        } catch (Exception e) {
            log.error("getPoolAllocateRules error {}", tenantId, e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        return result;
    }

    public static void addPoolReturnLog(User user, IObjectData objectData, IObjectData pool, SFALogModels.LogOperationType logOperationType) {
        String ownerName = "--";
        if (objectData.getOwner().size() > 0) {
            String ownerId = objectData.getOwner().get(0);
            User owner = serviceFacade.getUser(user.getTenantId(), ownerId);
            if (owner != null) {
                ownerName = owner.getUserName();
            }
        }
        String msg = String.format("%s%s，%s%s，%s%s",
                I18N.text("LeadsObj.attribute.self.display_name"),
                objectData.getName(),
                I18N.text("sfa.original.manager"),
                ownerName,
                I18N.text("LeadsPoolObj.attribute.self.display_name"),
                pool.getName()
        );

        SFALogModels.SFALogEntity logEntity = sfaLogService.buildLogEntity(pool,
                msg,
                false);
        List<SFALogModels.SFALogTextMessage> textMessageList = Lists.newArrayList();
        sfaLogService.appendNameLinkLogTextMessage(SFAPreDefineObject.Leads.getApiName(), objectData, textMessageList);
        msg = String.format(" ， %s%s，%s%s", I18N.text("sfa.original.manager"),
                ownerName, I18N.text("LeadsPoolObj.attribute.self.display_name"), pool.getName());
        sfaLogService.appendLogTextMessage(SFAPreDefineObject.Leads.getApiName(), objectData,
                SFALogModels.LogLinkType.NO_LINK, msg, textMessageList);
        logEntity.setLogTextMessageList(textMessageList);
        InternationalItem internationalItem = InternationalItem.builder()
                .defaultInternationalValue(msg)
                .internationalKey(SFA_LEADS_RETURN_POOL_LOG)
                .internationalParameters(Lists.newArrayList(objectData.getName(), ownerName, pool.getName()))
                .build();
        sfaLogService.addLog(user, logEntity, "SalesCluePoolLog", logOperationType, internationalItem);
    }

    public static void handleIsRemindRecycling(List<IObjectData> objectDataList) {
        try {
            if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(objectDataList)) {
                sfaRecyclingService.getRecyclingRule(Utils.LEADS_API_NAME, objectDataList);
            }
        } catch (Exception e) {
            log.error("handleIsRemindRecycling error", e);
        }
    }

    public static void removeLeadsId(ObjectDataDocument document) {
        //防止自定义函数，openApi调用会传该字段。
        document.remove("leads_id");
    }

    //region NewDetail
    public static void handleLeadsRelatedComponentsForNewDetail(ILayout layout, String relatedComponentApiName) {
        LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(x -> {
            try {
                GroupComponentExt groupComponent = GroupComponentExt.of((IGroupComponent) x);
                List<IComponent> childComponents = groupComponent.getChildComponents();
                Optional<IComponent> componentOp = childComponents.stream().filter(c -> relatedComponentApiName.equals(c.getName())).findFirst();
                componentOp.ifPresent(childComponents::remove);
                groupComponent.setChildComponents(childComponents);
            } catch (MetadataServiceException e) {
                log.error("Set LeadsObj RelatedComponent error", e);
            }
        });
    }

    public static void handleLeadsTransferLogRelatedComponentsForNewDetail(ILayout layout, String relatedComponentApiName) {
        LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(x -> {
            try {
                GroupComponentExt groupComponent = GroupComponentExt.of((IGroupComponent) x);
                List<IComponent> childComponents = groupComponent.getChildComponents();
                Optional<IComponent> componentOp = childComponents.stream().filter(c -> relatedComponentApiName.equals(c.getName())).findFirst();
                if (componentOp.isPresent()) {
                    componentOp.get().setButtons(Lists.newArrayList());
                    childComponents.remove(componentOp.get());
                }
                RelatedObjectList component = new RelatedObjectList();
                component.setName(relatedComponentApiName);
                component.setHeader(I18N.text("sfa.leads.transfer_log.related.name"));
                component.setRefObjectApiName("LeadsTransferLogObj");
                if ("LeadsTransferLogObj_account_id_related_list".equals(relatedComponentApiName)) {
                    component.setRelatedListName("account_leads_transfer_log_list");
                    component.setFieldApiName("account_id");
                } else if ("LeadsTransferLogObj_contact_id_related_list".equals(relatedComponentApiName)) {
                    component.setRelatedListName("contact_leads_transfer_log_list");
                    component.setFieldApiName("contact_id");
                }
                component.set("relationType", 2);
                component.setButtons(Lists.newArrayList());
                ITableColumn column = getTableColumn(I18N.text("VocalizeObj.field.name.label")/*编码*/, "name", "auto_number");
                component.addField(column);
                column = getTableColumn(I18N.text("VocalizeObj.field.owner.label")/*负责人*/, "owner", "employee");
                component.addField(column);
                column = getTableColumn(I18N.text("VocalizeObj.field.create_time.label")/*创建时间*/, "create_time", "date_time");
                component.addField(column);
                component.setIsHidden(false);
                childComponents.add(component);
                groupComponent.setChildComponents(childComponents);
            } catch (MetadataServiceException e) {
                log.error("Set LeadsTransferLogObj RelatedComponent error", e);
            }
        });
    }

    public static ObjectDataDocument getLeadsInteractiveRecordObjDocument(User user, String leadsId, String collectedToLeadsId, String interactiveMode) {
        String interactiveRecordObjApiName = "InteractiveRecordObj";
        Map<String, Object> map = getCommonObjectMap(user, interactiveRecordObjApiName);
        map.put("interactive_time", System.currentTimeMillis());
        map.put("related_object_api_name", SFAPreDefineObject.Leads.getApiName());
        map.put("related_object_id", leadsId);
        map.put("interactive_mode", interactiveMode);
        map.put("leads_id", collectedToLeadsId);
        return ObjectDataDocument.of(map);
    }

    public static Map<String, Object> getCommonObjectMap(User user, String apiName) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), apiName);
        Map<String, Object> map = Maps.newHashMap();
        if (!org.apache.commons.lang3.StringUtils.isEmpty(user.getOutTenantId())) {
            map.put("out_tenant_id", user.getOutTenantId());
        }
        List<String> outOwner = Lists.newArrayList();
        if (!StringUtils.isEmpty(user.getOutUserId()) && NumberUtils.isDigits(user.getOutUserId())) {
            outOwner.add(user.getOutUserId());
        }
        if (!CollectionUtils.isEmpty(outOwner)) {
            map.put("out_owner", outOwner);
        }
        map.put(Tenantable.TENANT_ID, user.getTenantId());
        map.put("record_type", "default__c");
        map.put("owner", Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
        map.put("object_describe_api_name", objectDescribe.getApiName());
        map.put("object_describe_id", objectDescribe.getId());
        return map;
    }

    public static IObjectData constructObjectData(String tenantId, String apiName, String id) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(Tenantable.TENANT_ID, tenantId);
        map.put("object_describe_api_name", apiName);
        map.put(DBRecord.ID, id);
        ObjectDataDocument document = ObjectDataDocument.of(map);
        return document.toObjectData();
    }

    public static List<Map> getLeadsPoolCountByPoolIdAndOwner(String tenantId, String ownerId, String poolId,
                                                              String limitType, ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                                              Long outerTenantId, Long outerOwnerId) {
        // 灰度控制：如果开启了灰度，走ES查询，否则走DB查询
        if (isGrayLeadsPoolCountES(tenantId)) {
            return getLeadsPoolCountByPoolIdAndOwnerES(tenantId, ownerId, poolId, limitType, objectPoolMemberType, outerTenantId, outerOwnerId);
        } else {
            return getLeadsPoolCountByPoolIdAndOwnerDB(tenantId, ownerId, poolId, limitType, objectPoolMemberType, outerTenantId, outerOwnerId);
        }
    }

    /**
     * 灰度控制：线索池统计是否走ES查询
     */
    public static boolean isGrayLeadsPoolCountES(String tenantId) {
        return gray.isAllow("leads_pool_count_es_query", tenantId);
    }

    /**
     * 走DB查询的原有逻辑
     */
    private static List<Map> getLeadsPoolCountByPoolIdAndOwnerDB(String tenantId, String ownerId, String poolId,
                                                                 String limitType, ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                                                 Long outerTenantId, Long outerOwnerId) {
        int cnt = 0;
        Count countFieldDescribe = getCountField();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "leads_pool_id", poolId);
        SearchUtil.fillFilterNotEq(filters, "biz_status", LeadsBizStatusEnum.TRANSFORMED.getCode());
        SearchUtil.fillFilterNotEq(filters, "life_status", LifeStatusEnum.INVALID.value);
        if (objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE ||
                objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE) {
            SearchUtil.fillFilterEq(filters, "out_tenant_id", String.valueOf(outerTenantId));
            if (!ObjectPoolPermission.ObjectPoolLimitType.ENTERPRISE.getValue().equals(limitType)) {
                SearchUtil.fillFilterEq(filters, "out_owner", String.valueOf(outerOwnerId));
            }
        } else {
            SearchUtil.fillFilterEq(filters, "owner", ownerId);
        }
        query.setFilters(filters);
        query.setSearchSource("db");

        Object objResult = serviceFacade.getCountValue(tenantId, countFieldDescribe, query);
        if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
            cnt = Integer.parseInt(objResult.toString());
        }
        Map map = Maps.newHashMap();
        map.put("count", cnt);
        List<Map> list = Lists.newArrayList();
        list.add(map);
        return list;
    }

    /**
     * 走ES查询的新逻辑
     */
    private static List<Map> getLeadsPoolCountByPoolIdAndOwnerES(String tenantId, String ownerId, String poolId,
                                                                 String limitType, ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                                                 Long outerTenantId, Long outerOwnerId) {
        int cnt = 0;
        Count countFieldDescribe = getCountField();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "leads_pool_id", poolId);

        // 使用IN查询替代NotEq，包含除了transformed之外的所有状态
        List<String> validBizStatuses = Lists.newArrayList(
                LeadsBizStatusEnum.UN_ASSIGNED.getCode(),
                LeadsBizStatusEnum.UN_PROCESSED.getCode(),
                LeadsBizStatusEnum.PROCESSED.getCode(),
                LeadsBizStatusEnum.CLOSED.getCode()
        );
        SearchUtil.fillFilterIn(filters, "biz_status", validBizStatuses);

        // 使用IN查询替代NotEq，包含除了invalid之外的所有状态
        List<String> validLifeStatuses = Lists.newArrayList(
                LifeStatusEnum.INEFFECTIVE.value,
                LifeStatusEnum.UNDER_REVIEW.value,
                LifeStatusEnum.NORMAL.value,
                LifeStatusEnum.IN_CHANGE.value
        );
        SearchUtil.fillFilterIn(filters, "life_status", validLifeStatuses);

        if (objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE ||
                objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE) {
            SearchUtil.fillFilterEq(filters, "out_tenant_id", String.valueOf(outerTenantId));
            if (!ObjectPoolPermission.ObjectPoolLimitType.ENTERPRISE.getValue().equals(limitType)) {
                SearchUtil.fillFilterEq(filters, "out_owner", String.valueOf(outerOwnerId));
            }
        } else {
            SearchUtil.fillFilterEq(filters, "owner", ownerId);
        }
        query.setFilters(filters);
        // 不设置searchSource，让其走ES查询

        Object objResult = serviceFacade.getCountValue(tenantId, countFieldDescribe, query);
        if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
            cnt = Integer.parseInt(objResult.toString());
        }
        Map map = Maps.newHashMap();
        map.put("count", cnt);
        List<Map> list = Lists.newArrayList();
        list.add(map);
        return list;
    }

    public static Count getCountField() {
        Count countFieldDescribe = new CountFieldDescribe();
        countFieldDescribe.setApiName(Utils.LEADS_API_NAME);
        countFieldDescribe.setFieldApiName("totalcount");
        countFieldDescribe.setSubObjectDescribeApiName(Utils.LEADS_API_NAME);
        countFieldDescribe.setDescribeApiName(Utils.LEADS_API_NAME);
        countFieldDescribe.setCountFieldApiName("id");
        countFieldDescribe.setCountType(Count.TYPE_COUNT);
        countFieldDescribe.setReturnType("number");
        countFieldDescribe.setDecimalPlaces(0);
        return countFieldDescribe;
    }

    public static void checkOuterLeadsLimit(User user, String owner, String outTenantId, List<IObjectData> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        if (StringUtils.isEmpty(outTenantId)) {
            throw new ValidateException(I18N.text(SFA_CONFIG_PARAMETER_ERROR1));
        }
        List<String> poolIds = getPoolIds(objectDataList);
        if (!CollectionUtils.isEmpty(poolIds)) {
            poolIds.forEach(x -> {
                List<IObjectData> tempDataList = objectDataList.stream().filter(data ->
                                x.equals(data.get("leads_pool_id", String.class)))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(tempDataList)) {
                    long newOutTenantId = Long.parseLong(outTenantId);
                    checkPoolLeadsLimit(user, owner, x, tempDataList, ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE, newOutTenantId
                            , Long.valueOf(owner));
                }
            });
        }
    }

    public static void checkPoolLeadsLimit(User user, String owner, String poolId, List<IObjectData> objectDataList,
                                           ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                           Long outerTenantId, Long outerOwnerId) {
        if (CollectionUtils.isEmpty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        IObjectData poolData = poolService.getObjectPoolById(user.getTenantId(), poolId);
        if (poolData == null) {
            return;
        }

        Integer claimLimitNum = poolData.get("limit_count", Integer.class);
        String limitType = poolData.get("limit_type", String.class);

        Integer totalCount = getOwnerLeadsCount(user.getTenantId(), owner, poolId, limitType,
                objectPoolMemberType, outerTenantId, outerOwnerId);
        if (totalCount + objectDataList.size() > claimLimitNum) {
            throw new ValidateException(I18N.text(SFA_OVER_ALLOCATE_LIMIT_NOT_CHANGE, objectDataList.get(0).getName()));
        }
    }

    public static Integer getOwnerLeadsCount(String tenantId, String employeeId, String poolId,
                                             String limitType, ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                             Long outerTenantId, Long outerOwnerId) {
        int totalCount = 0;
        List<Map> queryResult = getLeadsPoolCountByPoolIdAndOwner(tenantId, employeeId, poolId, limitType,
                objectPoolMemberType, outerTenantId, outerOwnerId);
        if (!CollectionUtils.isEmpty(queryResult)) {
            totalCount = Integer.parseInt(queryResult.get(0).get("count").toString());
        }
        return totalCount;
    }

    public static List<String> getPoolIds(List<IObjectData> objectDataList) {
        return objectDataList.stream().filter(x -> !StringUtils.isEmpty(x.get("leads_pool_id", String.class)))
                .map(x -> x.get("leads_pool_id").toString()).distinct().collect(Collectors.toList());
    }

    public static String getPoolId(IObjectData objectData) {
        return AccountUtil.getStringValue(objectData, LeadsConstants.Field.LEADS_POOL_ID.getApiName(), "");
    }

    public static void handleSearchQuery(SearchTemplateQuery query, User user, Integer sessionBOCItemKey) {
        boolean isCrmAdmin = LeadsUtils.isCrmAdmin(user);
        if (sessionBOCItemKey == 401) {
            List<IObjectData> poolDataList = poolService.getObjectPoolAdminByUser(user);
            List<IObjectData> needNotifyPoolList = poolDataList.stream()
                    .filter(x -> AccountUtil.getBooleanValue(x, "is_new_to_notify_admin", false)).collect(Collectors.toList());

            List<IFilter> filters = Lists.newArrayList();
            if (CollectionUtils.isEmpty(needNotifyPoolList)) {
                SearchUtil.fillFilterEq(filters, "name", String.format("%s%s", "LeadsObj-", serviceFacade.generateId()));
                query.setFilters(filters);
                return;
            }
            List<String> poolIds = needNotifyPoolList.stream().map(x -> x.getId()).collect(Collectors.toList());
            SearchUtil.fillFilterEq(filters, "biz_status", LeadsBizStatusEnum.UN_ASSIGNED.getCode());
            SearchUtil.fillFilterIn(filters, "leads_pool_id", poolIds);
            query.setFilters(filters);
        }
        if (sessionBOCItemKey == 402) {
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "biz_status", LeadsBizStatusEnum.UN_PROCESSED.getCode());
            if (GrayUtil.isGrayLeadsTodoListFilter(user.getTenantId())) {
                SearchUtil.fillFilterNotEq(filters, "out_resources", "partner");
            }
            AccountUtil.getOwnerFilter(filters, user);
            query.setFilters(filters);
        }
        query.setDataRightsParameter(null);
        if (!user.isSupperAdmin() && !isCrmAdmin) {
            List<IFilter> filters = query.getFilters();
            SearchUtil.fillFilterNEq(filters, "life_status", SystemConstants.LifeStatus.Invalid.value);
        }
    }

    public static List<IFilter> getToDoListFilters(User user, Integer sessionBOCItemKey) {
        List<IFilter> filters = Lists.newArrayList();
        boolean isCrmAdmin = LeadsUtils.isCrmAdmin(user);
        if (sessionBOCItemKey == 401) {
            List<IObjectData> poolDataList = poolService.getObjectPoolAdminByUser(user);
            List<IObjectData> needNotifyPoolList = poolDataList.stream()
                    .filter(x -> AccountUtil.getBooleanValue(x, "is_new_to_notify_admin", false)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(needNotifyPoolList)) {
                SearchUtil.fillFilterEq(filters, "name", String.format("%s%s", "LeadsObj-", serviceFacade.generateId()));
                return filters;
            }
            List<String> poolIds = needNotifyPoolList.stream().map(DBRecord::getId).collect(Collectors.toList());
            SearchUtil.fillFilterEq(filters, "biz_status", LeadsBizStatusEnum.UN_ASSIGNED.getCode());
            SearchUtil.fillFilterIn(filters, "leads_pool_id", poolIds);
        }
        if (sessionBOCItemKey == 402) {
            SearchUtil.fillFilterEq(filters, "biz_status", LeadsBizStatusEnum.UN_PROCESSED.getCode());
            AccountUtil.getOwnerFilter(filters, user);
        }
        if (!user.isSupperAdmin() && !isCrmAdmin) {
            SearchUtil.fillFilterNEq(filters, "life_status", SystemConstants.LifeStatus.Invalid.value);
        }
        return filters;
    }

    public static boolean isGrayLeadsDuplicated(String tenantId) {
        return sfaLicenseService.checkModuleLicenseExist(tenantId, "leads_deduplication_app");
    }

    public static void asyncFillFieldInfo(User user, IObjectDescribe describe, List<IObjectData> dataList) {
        List<IObjectData> synchronizedDataList = ObjectDataExt.synchronize(dataList);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            fillQuoteFieldValue(user, describe, synchronizedDataList, null);
        });
        parallelTask.submit(() -> {
            fillRefObjectName(user, describe, synchronizedDataList, null);
        });
        parallelTask.submit(() -> {
            fillInfo(user, describe, synchronizedDataList);
        });

        try {
            parallelTask.await(5L, TimeUnit.SECONDS);
        } catch (Exception var6) {
            log.error("Error in fill info of listController, ei:{}, object:{}", user.getTenantId(), describe.getApiName(), var6);
        }

    }

    private static void fillQuoteFieldValue(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList, Map<String, List<IObjectData>> refObjectDataMap) {
        INFRA_SERVICE_FACADE.fillQuoteFieldValue(user, dataList, objectDescribe, refObjectDataMap, false);
    }

    private static void fillRefObjectName(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList, Map<String, List<IObjectData>> refObjectDataMap) {
        serviceFacade.fillObjectDataWithRefObject(objectDescribe, dataList, user, refObjectDataMap);
    }

    private static void fillInfo(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        serviceFacade.fillUserInfo(objectDescribe, dataList, user);
        serviceFacade.fillDepartmentInfo(objectDescribe, dataList, user);
    }

    public static void updateObjectDataByFields(User user, List<IObjectData> objectDataList, List<String> updateFieldList) {
        if (CollectionUtils.isEmpty(objectDataList)) return;
        objectDataList = objectDataList.stream().filter(d -> !StringUtils.isEmpty(d.getId()))
                .collect(Collectors.toList());
        serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
    }

    public static boolean isGrayTransferNewOpportunityDetails(String tenantId) {
        return gray.isAllow("leads_transfer_new_opportunity_details_enable", tenantId);
    }

    public static boolean isGrayTransferSupportDetails(String tenantId) {
        return gray.isAllow("leads_transfer_support_details_enable", tenantId);
    }

    public static String getSelectOneLabel(IObjectDescribe describe, String fieldApiName, String fieldValue) {
        String result = "";
        if (describe == null || StringUtils.isEmpty(fieldApiName) || StringUtils.isEmpty(fieldValue)) {
            return result;
        }

        IFieldDescribe fieldDescribe = describe.getFieldDescribe(fieldApiName);
        if (fieldDescribe == null) {
            return result;
        }
        if (fieldDescribe instanceof SelectOneFieldDescribe) {
            SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) fieldDescribe;
            Optional<ISelectOption> selectOption = selectOneFieldDescribe.getOption(fieldValue);
            if (selectOption.isPresent()) {
                result = selectOption.get().getLabel();
            }
        }

        return result;
    }

    public static boolean isGrayLeadsInvalidTask(String tenantId) {
        return gray.isAllow("enable_leads_invalid_task", tenantId);
    }

    public static boolean isGrayLeadsWorkPlatform(String tenantId) {
        return gray.isAllow("leads_work_platform_enable", tenantId);
    }

    public static boolean isGrayLeadsInvalidLimit(String tenantId) {
        return gray.isAllow("leads_invalid_limit_enable", tenantId);
    }

    /**
     * 灰度了不发下游的线索待处理待办，屏蔽相关的代码
     * @param actionContext
     * @param data
     * @return true 不发送待办; false 发送待办
     */
    public static boolean isGraySendTodo(ActionContext actionContext, IObjectData data) {
        // 灰度了不发下游的线索待处理待办，屏蔽相关的代码
        if (GrayUtil.isGrayLeadsTodoListFilter(actionContext.getTenantId())) {
            if (data.get("out_resources") != null && "partner".equals(data.get("out_resources", String.class))) {
                log.warn("QiXinTodoService>sendInAndOutTodo> grayLeadsTodoListFilter, not send todo ,tenantId");
                return true;
            }
        }
        return false;
    }
}
