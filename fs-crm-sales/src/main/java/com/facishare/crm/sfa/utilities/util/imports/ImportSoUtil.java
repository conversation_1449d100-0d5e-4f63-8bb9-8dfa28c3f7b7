package com.facishare.crm.sfa.utilities.util.imports;

import com.facishare.crm.describebuilder.CurrencyFieldDescribeBuilder;
import com.facishare.crm.describebuilder.TextFieldDescribeBuilder;
import com.facishare.crm.sfa.predefine.service.model.ImportSoModel;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.describe.AbstractFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_CUSTOM_FIELD_IMPORR_PRICE;

/**
 * <AUTHOR>
 * @date 2019/7/16 14:32
 * @instruction
 */
public class ImportSoUtil {


    public static String PRINT_HIERARCHY = "print_hierarchy";

    public static String MULTI_UNIT_RELATED_PRICE = "price";
    public static String MULTI_UNIT_RELATED_PRICE_TYPE = "currency";
    public static List<String> PERIODIC_PRODUCT_FILTER_FIELDS = Lists.newArrayList("pricing_mode","pricing_rate","pricing_cycle","settlement_mode","whole_period_sale","detail_type_changed");

    public static List<String> SALES_ORDER_PRODUCT_FILTER_FIELDS = Lists.newArrayList("base_unit_count", "stat_unit_count", "conversion_ratio","other_unit_quantity","other_unit");
    public static List<String> SALES_ORDER_PRODUCT_OPEN_MULTI_UNIT_FILTER_FIELDS = Lists.newArrayList("node_price","parent_prod_package_id",
            "parent_prod_pkg_key", "prod_pkg_key","bom_id","root_prod_pkg_key", "print_hierarchy","node_type",
            "bom_instance_tree_id","node_no","temp_node_bom_id","temp_node_group_id","nonstandard_attribute","nonstandard_attribute_json","node_subtotal","node_discount","share_rate");
    public static List<String> ATTRIBUTE_FILTER_FIELDS = Lists.newArrayList("attribute","attribute_json","attribute_price_book_id");
    public static List<String> CLOSE_FIELD_FILTER_FIELDS = Lists.newArrayList("close_amount","close_status","close_reason","close_quantity");

    public static List<String> SALES_ORDER_UPDATE_IMPORT_FILTER_FIELD = Lists.newArrayList("settle_type",
            "logistics_status", "ship_to_id", "ship_to_add", "confirmed_receive_date", "price_book_id", "resource",
            "submit_time", "partner_id", "out_resources", "ship_to_tel", "confirmed_delivery_date", "delivery_comment",
            "confirm_time", "unit_quantity_amount", "amount_quantity", "price_policy_id", "price_policy_rule_ids",
            "order_status", "policy_total", "policy_discount", "price_book_amount", "dynamic_amount",
            "policy_dynamic_amount", "price_policy_rule","rebate_amount","coupon_amount","rebate_rule_id",
            "product_rebate_rule_id","misc_content","policy_amount_total","policy_after_total","paid_amount","range_rebate_rule","range_rebate_rule_ids","ship_to_add_control");
    public static List<String> SALES_ORDER_PRODUCT_UPDATE_IMPORT_FILTER_FIELD = Lists.newArrayList("parent_prod_package_id",
            "parent_prod_pkg_key", "prod_pkg_key", "sub_product_id_name", "sub_product_id", "bom_id", "root_prod_pkg_key",
            "price_book_product_id", "attribute", "attribute_json", "attribute_price_book_id", "price_policy_id",
            "price_policy_rule_ids", "parent_gift_key", "policy_dynamic_amount", "policy_discount",
            "policy_price", "amortize_subtotal", "group_key", "policy_subtotal", "dynamic_amount",
            "amortize_amount", "price_policy_rule", "gift_amortize_price", "gift_amortize_subtotal",
            "rebate_amortize_amount","rebate_dynamic_amount","coupon_amortize_amount","coupon_dynamic_amount",
            "misc_content","rebate_coupon_id","rebate_coupon_object_api_name","detail_type","detail_type_changed");


    /**
     * 销售合同明细，去掉价目表折扣，价目表价格
     */
    public static List<String> SALE_CONTRACT_LINE_DETAIL_REMOVE_FIELD = Lists.newArrayList("price_book_discount","price_book_price");

//    /**
//     * 产品移除的字段，针对没商品对象的情情况
//     */
//    public static List<String> INSERT_IMPORT_REMOVE_FIELDS_SKU_NO_SPU = Lists.newArrayList( "spu_id", "off_shelves_time", "on_shelves_time", "is_package", "is_multiple_unit");
    public static List<String> UPDATE_IMPORT_REMOVE_FIELDS_SKU_NO_SPU = Lists.newArrayList( "spu_id", "is_package","bom_type", "is_multiple_unit",
            "owner_department","off_shelves_time", "on_shelves_time", ProductConstants.PRODUCT_TYPE);

//    public static List<String> DETAIL_REMOVE_FIELD_CPQ = Lists.newArrayList(QuoteConstants.QuoteLinesField.PROD_PKG.getApiName()
//            , QuoteConstants.QuoteLinesField.PARENT_PROD_PKG.getApiName()
//            , QuoteConstants.QuoteLinesField.ROOT_PROD_PKG.getApiName()
//            , QuoteConstants.QuoteLinesField.BOM_ID.getApiName()
//            , PRINT_HIERARCHY);

    public static List<String> DETAIL_REMOVE_FIELD_BASE = Lists.newArrayList(ObjectDataExt.EXTEND_OBJ_DATA_ID,
            QuoteConstants.QuoteLinesField.PRICEBOOKPRODUCTID.getApiName(),
            QuoteConstants.QuoteLinesField.SUB_PRODUCT_ID.getApiName(),
            QuoteConstants.QuoteLinesField.PARENT_PROD_PACKAGE_ID.getApiName(),
            QuoteConstants.QuoteLinesField.PROD_PKG.getApiName()
            , QuoteConstants.QuoteLinesField.PARENT_PROD_PKG.getApiName()
            , QuoteConstants.QuoteLinesField.ROOT_PROD_PKG.getApiName()
            , QuoteConstants.QuoteLinesField.BOM_ID.getApiName()
            , PRINT_HIERARCHY,
            "price_policy_id", "price_policy_rule_ids", "prod_pkg_key", "parent_gift_key", "price_policy_rule");

    public static List<String> NON_STANDARD_PRODUCT_FIELD = Lists.newArrayList( "detail_type","detail_type_changed");

    public static List<String> get_import_remove_fields_attribute()
    {
        List<String> result = new ArrayList<>();
        for (int i = 1; i <= 30; i++) {
            result.add(AttributeConstants.ATTRIBUTE + String.valueOf(i));
        }
        result.add(AttributeConstants.ProductField.ATTRIBUTE_IDS);
        result.add(AttributeConstants.ProductField.PRICING_ATTRIBUTES);
        return result;
    }


    public static List<String> SALES_ORDER_PRODUCT_FILTER_DELIVERY_FIELD = Lists.newArrayList("delivery_amount",
            "delivered_count", "order_product_amount", "invoiced_quantity", "invoiced_amount", "no_invoice_quantity",
            "no_invoice_amount", "invoice_status");

    public static List<String> SALES_ORDER_PRODUCT_UNSUPPORT_STOCK_FIELDS = Lists.newArrayList("quantity_returned",
            "current_available_stock", "product_id", "quantity", "sales_price");

    public static List<String> SALES_ORDER_FILTER_DELIVERY_FIELD = Lists.newArrayList("delivered_amount_sum",
            "delivery_comment", "shipping_warehouse_id", "confirmed_receive_date", "confirmed_delivery_date",
            "logistics_status", "actual_refunded_amount");

    private static  Map<String, BuilderTypes> fieleTypeMaps = Maps.newHashMap();

    static {
        fieleTypeMaps.put("Text", BuilderTypes.Text);
        fieleTypeMaps.put("currency", BuilderTypes.Currency);
        fieleTypeMaps.put("SelectOne", BuilderTypes.Text);
    }

    /**
     * 构建字段
     *
     * @param args
     * @return
     */
    public static List<AbstractFieldDescribe> addCustomFields(List<ImportSoModel.AddFieldArg> args) {
        List<AbstractFieldDescribe> fields = Lists.newArrayList();
        args.forEach(arg -> {
            String type = arg.getType();
            if (fieleTypeMaps.containsKey(type)) {
                if ("currency".equals(type)) {
                    fields.add(CurrencyFieldDescribeBuilder.builder()
                            .apiName(arg.getApiName())
                            .label(arg.getLabel())
                            .decimalPlaces(8)
                            .length(20)
                            .maxLength(256).build());
                }
                if ("Text".equals(type)) {
                    fields.add(TextFieldDescribeBuilder.builder()
                            .apiName(arg.getApiName())
                            .label(arg.getLabel())
                            .maxLength(256).build());
                }
                //fields.add(DescribeBuilderFactory.getBuilder(fieleTypeMaps.get(type)).createFieldDescribe(arg));
            }
        });
        return fields;
    }


    /**
     * 屏蔽相关团队 自定义字段
     *
     * @param fieldDescribes
     * @return
     */
    public static List<IFieldDescribe> remvoeTeamMember(List<IFieldDescribe> fieldDescribes) {
        fieldDescribes.removeIf(field -> {
            if (!Objects.equals(field.get("extend_info"), null)) {
                Map extendInfo = field.get("extend_info", Map.class);
                String type = extendInfo.get("importType") == null ? "" : extendInfo.get("importType").toString();
                return Objects.equals(type, "TEAM_MEMBER");
            }
            return false;
        });
        return fieldDescribes;
    }


    /**
     * 屏蔽字段
     *
     * @param fieldDescribes
     * @return
     */
    public static List<IFieldDescribe> removeFields(List<IFieldDescribe> fieldDescribes, List<String> filterFields) {
        fieldDescribes.removeIf(o -> filterFields.contains(o.getApiName()));
        return fieldDescribes;
    }

    /**
     * 排序excel的title
     *
     * @param fieldDescribes
     * @return
     */
    public static List<IFieldDescribe> sortOrderFields(List<IFieldDescribe> fieldDescribes, List<String> sortFields) {
        List<IFieldDescribe> sortResult = Lists.newArrayList();
        sortFields.forEach(o -> {
            fieldDescribes.forEach(field -> {
                if (Objects.equals(o, field.getApiName())) {
                    sortResult.add(field);
                }
            });
        });
        return sortResult;
    }

    /**
     * 多单位导入所有过滤条件，all filter
     * 添加自定义的价格字段
     *
     * @param fieldDescribes
     * @return
     */
    public static List<IFieldDescribe> multiUnitRelatedAllFilter(List<IFieldDescribe> fieldDescribes, List<String> filterFields,List<String> customFilterFields, boolean flag) {
        ImportSoModel.AddFieldArg param = new ImportSoModel.AddFieldArg(ImportSoUtil.MULTI_UNIT_RELATED_PRICE,
                I18N.text(SO_CUSTOM_FIELD_IMPORR_PRICE), ImportSoUtil.MULTI_UNIT_RELATED_PRICE_TYPE);
        fieldDescribes.addAll(ImportSoUtil.addCustomFields(Lists.newArrayList(param)));
        // 屏蔽相关团队
        fieldDescribes = ImportSoUtil.remvoeTeamMember(fieldDescribes);
        // 屏蔽业务类型和负责人
        fieldDescribes = ImportSoUtil.removeFields(fieldDescribes, filterFields);
        if(!flag){
            fieldDescribes = ImportSoUtil.removeFields(fieldDescribes, customFilterFields);
        }
        return fieldDescribes;
    }


    public static String convertLabelToString(List<String> labels) {
        if (CollectionUtils.empty(labels)) {
            return "";
        }
        // 不用Arrays.toString，是为了防止单选中有"," 选项导致解析出错误的选项
        StringBuilder result = new StringBuilder();
        for (String label : labels) {
            result.append(label + "||");
        }
        return result.delete(result.length() - 2, result.length()).toString();
    }

    public static SearchTemplateQueryExt queryExt(String objectApiName, String tenantId, int size) {
        ISearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.setLimit(size);
        queryExt.setOffset(0);
        queryExt.addFilter(Operator.EQ, IObjectData.DESCRIBE_API_NAME, objectApiName);
        queryExt.addFilter(Operator.EQ, IObjectData.TENANT_ID, tenantId);
        return queryExt;
    }


}
