package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.PartnerConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.InfraServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.dto.GetVersion;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectCluster;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.invisibleReferenceMap;
import static com.facishare.paas.appframework.common.util.ObjectAction.*;

@Slf4j
public class AccountWebDetailController extends SFAWebDetailController {
    protected static final String HEAD_INFO = "head_info";
    private static final String ACCOUNT_ADDR_MD_GROUP_COMPONENT = Utils.ACCOUNT_ADDR_API_NAME + "_md_group_component";
    private static final String ACCOUNT_FIN_INFO_MD_GROUP_COMPONENT = Utils.ACCOUNT_FIN_INFO_API_NAME + "_md_group_component";
    private final static MetaDataService metaDataService = SpringUtil.getContext().getBean(MetaDataServiceImpl.class);
    private static final InfraServiceFacade INFRA_SERVICE_FACADE = SpringUtil.getContext().getBean(InfraServiceFacadeImpl.class);

    private static final String MULTI_CURRENCY_KEY = "multi_currency_config";

    private void specialLogicForLayout(ILayout layout, Result result) {
        try {

            List<IComponent> componentList = layout.getComponents();
            if (!componentList.isEmpty()) {
                List<String> apiNameList = new ArrayList<>();
                componentList.stream().forEach(x->{
                    if(ATTACH_COMPONENT.equals(x.getName())){
                        apiNameList.add(Utils.ACCOUNT_API_NAME);
                    }else if(ACCOUNT_ADDR_MD_GROUP_COMPONENT.equals(x.getName())){
                        apiNameList.add(Utils.ACCOUNT_ADDR_API_NAME);
                    }else if(ACCOUNT_FIN_INFO_MD_GROUP_COMPONENT.equals(x.getName())){
                        apiNameList.add(Utils.ACCOUNT_FIN_INFO_API_NAME);
                    }
                    if("equity_relationship_list_target_account".equals(x.getName())){
                        x.setType("relatedlist");
                        x.set("field_api_name","account_main_data_id");
                        x.set("relationType",2);
                        x.set("related_list_name","target_related_account_main_data_id");
                    }

                });
                if(CollectionUtils.notEmpty(apiNameList)){
                    // ********优化-如果标签中没有这个三个组件就不去查询，如果有，则是有那个查询那个
                    Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = functionPrivilegeService.batchFunPrivilegeCheck(controllerContext.getUser(),
                            apiNameList,
                            Lists.newArrayList(UPDATE.getActionCode(), INVALID.getActionCode(), CREATE.getActionCode(),
                                    ObjectAction.VIEW_ATTACH.getActionCode(), ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));

                    Optional<IComponent> attachComponent = layout.getComponents().stream().filter(x -> ATTACH_COMPONENT.equals(x.getName())).findFirst();
                    if (attachComponent.isPresent()) {
                        if (objApiNameAndActionCodePrivilegeMapping.get(Utils.ACCOUNT_API_NAME).get(ObjectAction.VIEW_ATTACH.getActionCode())) {
                            if (result != null && result.getData() != null && !result.getData().toObjectData().isDeleted()) {
                                if (objApiNameAndActionCodePrivilegeMapping.get(Utils.ACCOUNT_API_NAME).get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
                                    List<IButton> buttons = getButtons();
                                    WebDetailLayout.of(layout).addButtons(buttons, ATTACH_COMPONENT);
                                }
                            }
                        } else {
                            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList(ATTACH_COMPONENT));
                        }
                    }
                    if (result != null && result.getData() != null && !result.getData().toObjectData().isDeleted()) {
                        Optional<IComponent> componentAccountAddr = componentList.stream().filter(s -> s.getName().equals(ACCOUNT_ADDR_MD_GROUP_COMPONENT)).findFirst();
                        if (componentAccountAddr.isPresent() && result.getData() != null) {
                            WebDetailLayout.of(layout).addButtons(AccountAddrUtil.getButtons(objApiNameAndActionCodePrivilegeMapping,
                                    controllerContext.getUser(), Utils.ACCOUNT_ADDR_API_NAME, result.getData()), ACCOUNT_ADDR_MD_GROUP_COMPONENT);
                        }
                        Optional<IComponent> componentAccountFinInfo = componentList.stream().filter(s -> s.getName().equals(ACCOUNT_FIN_INFO_MD_GROUP_COMPONENT)).findFirst();
                        if (componentAccountFinInfo.isPresent()) {
                            WebDetailLayout.of(layout).addButtons(AccountAddrUtil.getButtons(objApiNameAndActionCodePrivilegeMapping,
                                    controllerContext.getUser(), Utils.ACCOUNT_FIN_INFO_API_NAME, result.getData()), ACCOUNT_FIN_INFO_MD_GROUP_COMPONENT);
                        }
                    }
                }

                if(!AccountUtil.isShowCostComponent(controllerContext.getTenantId())) {
                    WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("AccountCostObj_related_list"));
                }
                if (!SFAConfigUtil.isSaleContractOpen(controllerContext.getTenantId())) {
                    WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("SaleContractObj_account_id_related_list"));
                }
            }
        } catch (MetadataServiceException ex) {
            log.warn("AccountWebDetailController->ChangeComponentOrder  error", ex);
        }

        //移动端移除ViewFeedCard按钮
        if (RequestUtil.isMobileOrH5Request() || RequestUtil.isMobileOrMobileDeviceRequest() || RequestUtil.isWXMiniProgram()) {
            RemoveButtons(layout, Lists.newArrayList(ObjectAction.VIEW_FEED_CARD.getActionCode()));
            //移动端去掉商情洞察
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("business_insights"));
        } else if (RequestUtil.isWebRequest()) {
            RemoveButtons(layout, Lists.newArrayList(ObjectAction.JOINROUT.getActionCode()));
        }

        //移动端移除rfm 或者联系人关系图谱
        if (RequestUtil.isMobileOrH5Request() || RequestUtil.isWXMiniProgram() || AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(controllerContext.getAppId())) {
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("contact_member_relationship",
                    "sfa_business_in_follow","sfa_business_information_module"));
        }
        if (result != null) {
            ObjectDataDocument dataDocument = result.getData();
            if (!Objects.equals(dataDocument.get("enable_risk_portrait"), Boolean.TRUE)) {//没开启风险画像 , 移除tab
                WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("biz_risk_brain","biz_enterprise_credit"));
            }
        }
        if (RequestUtil.isH5Request() || AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(controllerContext.getAppId())) {
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("rfm_analysis"));
        }

        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("qywx_conversion"));

        List<String> invisibleReference = invisibleReferenceMap.get(arg.getObjectDescribeApiName());
        List<String> componentApiNameList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(invisibleReference)) {
            invisibleReference.forEach(m -> componentApiNameList.add(m + "_account_id_related_list"));
        }
        componentApiNameList.add("LeadsObj_account_id_related_list");
        WebDetailLayout.of(layout).removeComponents(componentApiNameList);
        //region 开启多组织逻辑
        if (AccountUtil.isOpenManyOrganizations(controllerContext.getUser(), describe)) {
            //开启多组织，一期暂时屏蔽扫名片和智能表单
            List<IButton> buttons = layout.getButtons();
            if (CollectionUtils.notEmpty(buttons)) {
                buttons.removeIf(x -> x.getAction().equals(ObjectAction.SCAN_CARD.getActionCode()));
                buttons.removeIf(x -> x.getAction().equals(ObjectAction.INTELLIGENTFORM.getActionCode()));
                layout.setButtons(buttons);
            }
        }
        if(!(RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest())){
            List<IButton> buttons = layout.getButtons();
            if (CollectionUtils.notEmpty(buttons)) {
                buttons.removeIf(x -> x.getAction().equals(ObjectAction.SCAN_CARD.getActionCode()));
                layout.setButtons(buttons);
            }
        }
         //endregion
    }

    private void RemoveButtons(ILayout layout, List<String> objectActions) {
        List<IButton> buttons = layout.getButtons();
        buttons.removeIf(x -> objectActions.contains(x.getAction()));
        layout.setButtons(buttons);
        LayoutExt layoutExt = LayoutExt.of(layout);
        Optional<IComponent> headInfoComponentOp = layoutExt.getHeadInfoComponent();
        if (headInfoComponentOp.isPresent()) {
            IComponent headInfoComponent = headInfoComponentOp.get();
            List<IButton> headInfoButtons = headInfoComponent.getButtons();
            headInfoButtons.removeIf(x -> objectActions.contains(x.getAction()));
            WebDetailLayout.of(layout).setButtons(headInfoButtons, HEAD_INFO);
        }
    }

    @Override
    protected StandardDetailController.Result after(StandardDetailController.Arg arg, StandardDetailController.Result result) {
        this.stopWatch.lap("after start");
        StandardDetailController.Result newResult = super.after(arg, result);
        IObjectData objectData = newResult.getData().toObjectData();
        //拷贝对象描述
        IObjectDescribe copyDescribe = ObjectDescribeExt.of(describe).copyOnWrite();
        AccountUtil.handleRemainingTimeDesc(copyDescribe, Lists.newArrayList(objectData));
        this.stopWatch.lap("handleRemainingTimeDesc end");
        AccountUtil.handleIsRemindRecycling(Lists.newArrayList(objectData));
        this.stopWatch.lap("handleIsRemindRecycling end");
        AccountUtil.calculateCompletionRate(copyDescribe, Lists.newArrayList(objectData));
        this.stopWatch.lap("calculateCompletionRate end");
        newResult.setDescribe(ObjectDescribeDocument.of(copyDescribe));
        boolean isShowCompanyLyricalAll = AccountUtil.isShowCompanyLyricalAll(controllerContext.getTenantId(), objectData.getName());
        this.stopWatch.lap("isShowCompanyLyricalAll end");
        objectData.set("isShowCompanyLyricalAll", isShowCompanyLyricalAll);
        newResult.setData(ObjectDataDocument.of(objectData));
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());

        specialLogicForLayout(layout, newResult);
        LayoutUtils.removeProfileComponent(controllerContext.getTenantId(), layout);
        this.stopWatch.lap("specialLogicForLayout end");

        //开启多币种，移除摘要卡片
        String configValue = SFAConfigUtil.getConfigValue(controllerContext.getTenantId(), MULTI_CURRENCY_KEY,
                controllerContext.getUser().getUpstreamOwnerIdOrUserId());
        if (Objects.equals("1", configValue)) {
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList(ComponentExt.SUMMARY_CARD_COMPONENT_NAME));
        }

        String owner = CommonBizUtils.getOwner(objectData);
        log.info("AccountWebDetailController1 owner={}",owner);
        if (StringUtils.isEmpty(owner)) {
            handleRelatedListByHighSeaSetting(layout);
            this.stopWatch.lap("handleRelatedListByHighSeaSetting end");
        }

        GetVersion.VersionInfo versionInfo = serviceFacade.getVersionInfo(controllerContext.getTenantId());
        if (versionInfo != null) {
            LayoutUtils.handleRelatedCasesObj(versionInfo.getCurrentVersion(), "CasesObj_account_id_related_list", layout);
            this.stopWatch.lap("handleRelatedCasesObj end");

        }
        this.stopWatch.lap("after end");

        return newResult;
    }

    private final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);

    private List<IButton> getButtons() {
        Map<String, Map<String, Permissions>> privilege = metaDataService.checkDataPrivilege(
                controllerContext.getUser(), Lists.newArrayList(data), ObjectDescribeExt.of(describe),
                Lists.newArrayList(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));

        List<IButton> buttons = Lists.newArrayList();
        if (privilege.containsKey(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
            Map<String, Permissions> permissions = privilege.get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode());
            if (permissions.containsKey(arg.getObjectDataId())
                    && permissions.get(arg.getObjectDataId()).equals(Permissions.READ_WRITE)) {
                IButton addButton = new Button();
                addButton.setAction("Add");
                addButton.setActionType("default");
                addButton.setLabel(I18N.text(I18NKey.action_upload));
                addButton.setName("AccountAttObj_Add_button_default");
                buttons.add(addButton);
                IButton deleteButton = new Button();
                deleteButton.setAction("Delete");
                deleteButton.setActionType("default");
                deleteButton.setLabel(I18N.text(I18NKey.action_delete));
                deleteButton.setName("AccountAttObj_Delete_button_default");
                buttons.add(deleteButton);
            }
        }
        return buttons;
    }

    /**
     * 根据公海的设置动态隐藏相关列表对象及隐藏字段
     */
    protected void handleRelatedListByHighSeaSetting(ILayout layout) {
        //移除外部企业字段
        SFADetailController.CheckNeedShowRelatedObjsResult checkNeedShowRelatedObjsResult = checkNeedShowRelatedObjs();
        if (!checkNeedShowRelatedObjsResult.isAllowMemberRelation()) {
            List<String> removeComponentNameList = Lists.newArrayList("CustomerAccountObj_md_group_component",
                    "AccountFinInfoObj_md_group_component", "AccountAddrObj_md_group_component", "operation_log",
                    "relevant_team_component", "account_hierarchy_component", "contact_relation_component", "contact_member_relationship", "rfm_analysis","common_relationship_tree",
                    "account_org_distribution","account_relationship_tree","equity_relationship_view");
            List<String> removeComponentTypeList = Lists.newArrayList("relatedlist", "multi_table", "group");
            if (checkNeedShowRelatedObjsResult.isAllowMemberViewFeed()) {
                try {
                    List<IComponent> ch_comps = layout.getComponents();
                    List<String> removeComponentList = ch_comps.stream().filter(g -> !"sale_log".equals(g.getName()) &&
                            (removeComponentTypeList.contains(g.getType()) || removeComponentNameList.contains(g.getName())))
                            .map(g -> g.getName()).collect(Collectors.toList());
                    WebDetailLayout.of(layout).removeComponents(removeComponentList);
                } catch (MetadataServiceException e) {
                    log.error("getComponents error", e);
                }
            } else {
                try {
                    List<IComponent> ch_comps = layout.getComponents();
                    List<String> removeComponentList = ch_comps.stream().filter(g -> "sale_log".equals(g.getName()) ||
                            removeComponentTypeList.contains(g.getType()) || removeComponentNameList.contains(g.getName()))
                            .map(g -> g.getName()).collect(Collectors.toList());
                    WebDetailLayout.of(layout).removeComponents(removeComponentList);
                } catch (MetadataServiceException e) {
                    log.error("getComponents error", e);
                }
            }
        }
        if (!checkNeedShowRelatedObjsResult.isAllowMemberViewFeed()) {
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("sale_log"));
        }

        if (!checkNeedShowRelatedObjsResult.isAllowMemberSendFeed()) {
            List<IButton> layoutButtonsbuttons = layout.getButtons();
            layoutButtonsbuttons.removeIf(x -> x.getAction().equals(ObjectAction.ADD_EVENT.getActionCode()));
            layout.setButtons(layoutButtonsbuttons);
            IComponent component = getComponent(layout, "related_record", "sale_log");
            if (component != null) {
                List<IButton> buttons = component.getButtons();
                buttons.removeIf(b -> ObjectAction.ADD_EVENT.getActionCode().equals(b.getAction()));
                component.setButtons(buttons);
            }
        }

        //隐藏详细信息和顶部信息中的字段
        handleHideFieldsBySetting(layout, checkNeedShowRelatedObjsResult.getNeedHideFields());
    }

    @Override
    protected boolean defaultEnableQixinGroup() {
        IObjectCluster cluster = INFRA_SERVICE_FACADE.find(controllerContext.getUser(), arg.getObjectDescribeApiName());
        if (Objects.isNull(cluster) || QixinGroupConfig.of(cluster).isObjectEnabled()) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }
}
