package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.platform.utils.ResourcesIOUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LayoutServiceExt {
    @Autowired
    private ServiceFacade serviceFacade;

    public void createLayoutByLocalURI(User user, String objectApiName, String type) {
        if (StringUtils.isBlank(objectApiName)) {
            return;
        }
        String path = "layoutjson/init_crm_%s_%s_layout.json";
        String uri = String.format(path, objectApiName, type);
        String layoutJson = ResourcesIOUtils.getJsonStrByURI(uri);
        if (layoutJson == null) {
            return;
        }
        ILayout layout = new Layout(Document.parse(layoutJson));
        if (getLayoutByApiName(user, objectApiName, layout.getName()) != null) {
            log.warn("布局已经存在，无法新建, tenant:{}, objectApiName:{}, layoutApiName:{}", user.getTenantId(), objectApiName, layout.getName());
            return;
        }
        serviceFacade.getLayoutLogicService().createLayout(user, layout);
    }

    public ILayout getLayoutByApiName(User user, String objectApiName, String type) {
        return serviceFacade.getLayoutLogicService().findLayoutByApiName(user, type, objectApiName);
    }

    public void insertFieldsToLayout(User user, IObjectDescribe objectDescribe, List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList) {
        List<ILayout> layoutList = serviceFacade.getLayoutLogicService().getDetailLayouts(user.getTenantId(), objectDescribe);
        layoutList.forEach(m -> removeRepeatField(m, addFieldTupleList.stream().map(n -> n.getKey().getApiName()).distinct()
                .collect(Collectors.toList())));
        layoutList.forEach(m -> {
            addFieldTupleList.forEach(tuple -> LayoutExt.of(m).addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.getLayoutLogicService().updateLayout(user, m);
        });
    }

    public void removeRepeatField(ILayout layout, List<String> fieldNames) {
        try {
            for (IComponent component : layout.getComponents()) {
                if (!(component instanceof FormComponent)) {
                    continue;
                }
                FormComponent formComponent = (FormComponent) component;
                if (formComponent.getFieldSections() == null) {
                    continue;
                }
                removeField(fieldNames, formComponent.getFieldSections());
            }
        } catch (MetadataServiceException e) {
            log.warn("updateFieldRenderType error occur,for exception", e);
        }
    }

    private void removeField(List<String> fieldNames, List<IFieldSection> fieldSections) {
        if (CollectionUtils.empty(fieldSections)) {
            return;
        }
        for (IFieldSection fieldSection : fieldSections) {
            List<IFormField> removeFormFields = Lists.newArrayList();
            List<IFormField> formFields = fieldSection.getFields();
            formFields.forEach(formFiled -> {
                if (fieldNames.contains(formFiled.getFieldName())) {
                    removeFormFields.add(formFiled);
                }
            });
            formFields.removeAll(removeFormFields);
            fieldSection.setFields(formFields);
        }
    }
}
