package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.aggregatevalue.AggregateRuleService;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

/**
 * 聚合值详细信息
 */
public class AggregateRuleWebDetailController extends StandardWebDetailController {

    AggregateRuleService aggregateRuleService = SpringUtil.getContext().getBean(AggregateRuleService.class);

    @Override
    protected IObjectData findObjectData(Arg arg) {
        IObjectData iObjectData = super.findObjectData(arg);
        aggregateRuleService.specialTreatmentData(controllerContext.getUser(), Lists.newArrayList(iObjectData), true);
        return iObjectData;
    }
    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (RequestUtil.isMobileOrH5Request() || RequestUtil.isMobileDeviceRequest()) {
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(
                ObjectAction.CREATE.getActionCode(),
                ObjectAction.UPDATE.getActionCode(),
                ObjectAction.DELETE.getActionCode(),
                ObjectAction.CLONE.getActionCode(),
                ObjectAction.INVALID.getActionCode())
            );
        }
        //根据配置隐藏关联对象列表的新建按钮
        PreDefLayoutUtil.invisibleNewWebRefObjectListAddButton(arg.getObjectDescribeApiName(), layout);
        return layout;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        IFieldDescribe field = result.copyFieldToDescribeExt(AggregateRuleConstants.Field.AGGREGATE_OBJECT);
        if(field != null) {
            aggregateRuleService.buildAggregateObjectDescribe(controllerContext.getUser(), field);
            field.setIndex(false);
        }
        return super.after(arg, result);
    }
}
