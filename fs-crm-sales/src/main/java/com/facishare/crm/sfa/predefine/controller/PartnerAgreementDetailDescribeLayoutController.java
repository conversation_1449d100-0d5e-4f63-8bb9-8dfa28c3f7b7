package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.platform.utils.RequestSourceResolver;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by Sundy on 2024/11/14 15:27
 */
public class PartnerAgreementDetailDescribeLayoutController extends StandardDescribeLayoutController {
    private final RequestSourceResolver requestSourceResolver = SpringUtil.getContext().getBean("requestSourceResolver", RequestSourceResolver.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (!Boolean.TRUE.equals(arg.getInclude_layout()) || result.getLayout() == null) {
            return result;
        }
        LayoutExt layoutExt = LayoutExt.of(result.getLayout());
        for (String onlyReadField : PartnerAgreementDetailModel.ONLY_READ_FIELDS) {
            layoutExt.getField(onlyReadField).ifPresent(field -> field.setReadOnly(true));
        }
        if (requestSourceResolver.isErAppRequest()) {
            layoutExt.removeFields(Sets.newHashSet(PartnerAgreementDetailModel.ABLE_RENEWAL));
            return result;
        }
        return result;
    }
}
