package com.facishare.crm.sfa.utilities.util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.NewOpportunityInitService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.proxy.model.GetStageSimpleDefinitionByEntityIdAndObjectId;
import com.facishare.crm.sfa.utilities.validator.NewOpportunityValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.MtCurrency;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class NewOpportunityUtil {
    private static final ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private static final NewOpportunityInitService newOpportunityInitService = SpringUtil.getContext().getBean(NewOpportunityInitService.class);
    private static final ConfigService configService = SpringUtil.getContext().getBean(ConfigService.class);
    public static final String STG_CHANGED_TIME = "stg_changed_time";

    public static boolean getIsChangeToNewOpportunity(User user) {
        return GrayUtil.isChangeToNewOpportunity(user.getTenantId());
    }

    public static boolean getIsOpenCloseDateIfWin(User user) {
        ConfigCtrlModule.Arg configCtrlModuleArg = new ConfigCtrlModule.Arg();
        configCtrlModuleArg.setModuleCode(IModuleInitService.CONFIG_CHANGE_CLOSE_DATE_IF_WIN);
        configCtrlModuleArg.setTenantId(user.getTenantId());

        ConfigCtrlModule.Result checkModuleStatus = moduleCtrlConfigService.checkModuleStatus(configCtrlModuleArg, user);
        ConfigCtrlModule.Value moduleStatusValue = checkModuleStatus.getValue();
        String openStatus = moduleStatusValue.getOpenStatus();
        return "1".equals(openStatus);
    }

    public static void fillModelByChangeSalesStage(ActionContext actionContext, IObjectData newData, IObjectData dbData) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), SFAPreDefineObject.NewOpportunity.getApiName());
        //保证流程不变的情况下，阶段变更
        String salesStage = String.valueOf(newData.get("sales_stage"));

        Map map = NewOpportunityValidator.salesStageToProbability(salesStage, objectDescribe);

        if (map != null && map.size() > 0) {
            String salesStatus = map.get("sales_status").toString();
            newData.set("sales_status", salesStatus);
            if (getIsOpenCloseDateIfWin(actionContext.getUser()) && "2".equals(salesStatus)) {
                SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
                String str = dateformat.format(System.currentTimeMillis());
                try {
                    newData.set("close_date", String.valueOf(dateformat.parse(str).getTime()));
                } catch (ParseException e) {
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NEWOPPORTUNITY_VALIDATOR_CLOSE_DATE_PARSE_FAILED));
                }
            }
            if (map.get("forecast_type") != null) {
                newData.set("forecast_type", map.get("forecast_type"));
            }
        }
        Map<String, GetStageSimpleDefinitionByEntityIdAndObjectId.StagesValue> stagesProbabilityMap = newOpportunityInitService.getStagesProbability(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), newData.getId());
        if (stagesProbabilityMap == null) {
            //log.error("NewOpportunityEditAction  error 调用阶段推进器接口解析异常");
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NEWOPPORTUNITY_VALIDATOR_STAGES_PROCESSOR_PARSE_FAILED));
        }
        GetStageSimpleDefinitionByEntityIdAndObjectId.StagesValue stagesValue = stagesProbabilityMap.get(salesStage);
        if (stagesValue == null) {
            //log.error("NewOpportunityEditAction  error 该阶段不在所在商机流程中");
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NEWOPPORTUNITY_VALIDATOR_STAGES_IS_EMPTY));
        }
        GetStageSimpleDefinitionByEntityIdAndObjectId.ExtraDataModel extraDataModel = stagesValue.getExtraData();
        if (extraDataModel == null) {
            //log.error("NewOpportunityEditAction  error 阶段推进器解析extraDataModel异常");
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NEWOPPORTUNITY_VALIDATOR_STAGES_PROCESSOR_EXTRA_DATA_PARSE_FAILED));
        }
        String probability = "";
        if (newData.get("probability") == null) {
            probability = extraDataModel.getProbability();
            if (probability == null || probability.isEmpty()) {
                //log.error("NewOpportunityEditAction  error 阶段推进器获取赢率异常");
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NEWOPPORTUNITY_VALIDATOR_STAGES_PROCESSOR_PROBABILITY_PARSE_FAILED));
            }
        } else {
            probability = String.valueOf(newData.get("probability"));
        }

        newData.set(STG_CHANGED_TIME, System.currentTimeMillis());
        newData.set("last_modified_time", System.currentTimeMillis());
        newData.set("last_modified_by", Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId()));
        newData.set("probability", probability);
    }

    public static void setLeadsToNewOpportunity(User user, List<IObjectData> validList) {
        String queryRst = configService.findTenantConfig(user, ConfigType.NEW_OPPORTUNTIY_LEADS_SETTING.getKey());
        List<IObjectData> dataList = validList.stream()
                .filter(data -> data.get("leads_id") == null)
                .collect(Collectors.toList());
        //商机归因设置0:不自动关联；1：自动关联第一个；2：自动关联最近一个
        if (queryRst != null && !Strings.isNullOrEmpty(queryRst) && (queryRst.equals("1") || queryRst.equals("2"))) {
            List<String> accountIds = new ArrayList<>();
            dataList.forEach(data -> {
                if (data.get("account_id") != null) {
                    String accountId = data.get("account_id").toString();
                    if (!accountIds.contains(accountId)) {
                        accountIds.add(accountId);
                    }
                }
            });
            if (!accountIds.isEmpty()) {
                SearchTemplateQuery query = new SearchTemplateQuery();
                Filter filter = new Filter();
                filter.setFieldValues(Lists.newArrayList(accountIds));
                if (accountIds.size() > 1) {
                    filter.setOperator(Operator.IN);
                    filter.setFieldName("account_id");
                } else {
                    filter.setOperator(Operator.EQ);
                    filter.setFieldName("account_id");
                }
                query.addFilters(Lists.newArrayList(filter));
                query.setLimit(2000);
                query.setOffset(0);

                query.setPermissionType(0);
                if (queryRst.equals("1")) {
                    query.setOrders(Lists.newArrayList(new OrderBy("create_time", true)));
                }
                if (queryRst.equals("2")) {
                    query.setOrders(Lists.newArrayList(new OrderBy("create_time", false)));
                }
                QueryResult<IObjectData> queryResultLeadsLog = serviceFacade
                        .findBySearchQuery(user, SFAPreDefineObject.LeadsTransferLog.getApiName(), query);
                List<String> leadsIds = new ArrayList<>();
                if (queryResultLeadsLog != null && queryResultLeadsLog.getData() != null && !queryResultLeadsLog.getData().isEmpty()) {
                    queryResultLeadsLog.getData().forEach(data -> {
                        if (data.get("leads_id") != null) {
                            leadsIds.add(data.get("leads_id").toString());
                        }
                    });
                }
                if (!leadsIds.isEmpty()) {
                    SearchTemplateQuery query1 = new SearchTemplateQuery();
                    List<IFilter> filters = new ArrayList<>();
                    Filter filter1 = new Filter();
                    filter1.setFieldValues(Lists.newArrayList(leadsIds));
                    if (leadsIds.size() > 1) {
                        filter1.setOperator(Operator.IN);
                        filter1.setFieldName("leads_id");
                    } else {
                        filter1.setOperator(Operator.EQ);
                        filter1.setFieldName("leads_id");
                    }
                    filters.add(SearchUtil.filter("is_deleted", Operator.GT, -1));
                    filters.add(filter1);
                    query1.addFilters(Lists.newArrayList(filters));
                    query1.setLimit(2000);
                    query1.setOffset(0);

                    query1.setPermissionType(0);

                    QueryResult<IObjectData> queryResultOppo = serviceFacade
                            .findBySearchQuery(user, SFAPreDefineObject.NewOpportunity.getApiName(), query1);

                    List<IObjectData> leadsDataList = serviceFacade.findObjectDataByIdsIgnoreFormula(user.getTenantId(), leadsIds, SFAPreDefineObject.Leads.getApiName());

                    if (queryResultOppo != null && queryResultOppo.getData() != null && !queryResultOppo.getData().isEmpty()) {
                        dataList.forEach(data -> {
                            for (IObjectData result : queryResultLeadsLog.getData()) {
                                if (queryResultOppo.getData().stream().anyMatch(r -> r.get("leads_id").equals(result.get("leads_id")))) {
                                    continue;
                                }
                                if (leadsDataList.stream().noneMatch(r -> r.getId().equals(result.get("leads_id")))) {
                                    continue;
                                }
                                if (data.get("account_id").equals(result.get("account_id"))) {
                                    data.set("leads_id", result.get("leads_id"));
                                    break;
                                }
                            }
                        });
                    } else {
                        dataList.forEach(data -> {
                            for (IObjectData result : queryResultLeadsLog.getData()) {
                                if (data.get("account_id").equals(result.get("account_id"))) {
                                    if (leadsDataList.stream().anyMatch(r -> r.getId().equals(result.get("leads_id")))) {
                                        data.set("leads_id", result.get("leads_id"));
                                        break;
                                    }
                                }
                            }
                        });
                    }
                }
            }
        }
    }

    public static Optional<MtCurrency> preferCurrencyFromLayout(User user, ObjectDescribeExt objectDescribeExt, ServiceFacade serviceFacade) {
        if (objectDescribeExt.containsMultiCurrencyField()) {
            ILayout layout = serviceFacade.getLayoutLogicService().getListLayoutWitchComponents(user, objectDescribeExt.getObjectDescribe(), PageType.ListLayout, LayoutTypes.LIST);
            return findListComponent(layout).flatMap(c -> findCurrency(c, serviceFacade, user));
        } else {
            return Optional.empty();
        }
    }

    public static BigDecimal recalculateByCurrency(BigDecimal amount, MtCurrency currency) {
        if (amount.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal exchangeRate = new BigDecimal(currency.getExchangeRate());
            return amount.divide(exchangeRate, 2, RoundingMode.HALF_UP);
        }
        return amount;
    }

    // 因layout是一个黑盒，故用JSONPath找列表组件
    public static Optional<IComponent> findListComponent(ILayout layout) {
        try {
            String path = "$.." + ILayout.COMPONENTS + "[?(@." + ILayout.NAME + "='" + ComponentExt.LIST_COMPONENT_NAME + "')][0]";
            JSONObject obj = JSONPath.compile(path).eval(LayoutExt.of(layout).toMap(), JSONObject.class);
            return obj == null ? Optional.empty() : Optional.of(ComponentFactory.newInstance(obj));
        } catch (Exception e) {
            log.error("findListComponent error", e);
            return Optional.empty();
        }
    }

    private static Optional<MtCurrency> findCurrency(IComponent component, ServiceFacade serviceFacade, User user) {
        Object obj = component.get("amount_info");
        if (obj instanceof Map) {
            @SuppressWarnings("unchecked")
            JSONObject amountInfo = new JSONObject((Map<String, Object>) obj);
            return Optional.of(amountInfo)
                    .map(o -> o.getJSONObject("multi_currency"))
                    .map(o -> o.getString("value"))
                    .map(o -> serviceFacade.getMultiCurrencyLogicService().findCurrencyByCode(o, user));
        }
        return Optional.empty();
    }
}
