package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.enums.ReminderTrigger;
import com.facishare.crm.model.*;
import com.facishare.crm.platform.async.executor.AsyncBootstrap;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.sfa.model.ChannelFlowInitVO;
import com.facishare.crm.sfa.model.ChannelServiceModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.enums.ChannelEventTag;
import com.facishare.crm.sfa.predefine.service.model.ChannelEventModel;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.channel.ChannelAdmissionService;
import com.facishare.crm.sfa.prm.api.client.EnterpriseRelationServiceAdapter;
import com.facishare.crm.sfa.prm.api.dto.AdmissionConfigDTO;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.platform.utils.I18NUtils;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.constants.PrmI18NConstants.*;

/**
 * <AUTHOR>
 * @time 2023-11-07 14:39
 * @Description
 */
@Service
@Slf4j
public class ChannelTaskService {
    @Resource
    private AsyncTaskProducer asyncTaskProducer;
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource(name = "channelServiceProvider")
    private ChannelService channelService;
    @Resource(name = "enterpriseRelationServiceAdapterImpl")
    private EnterpriseRelationServiceAdapter enterpriseRelationServiceAdapter;
    @Resource(name = "objectConverter")
    private Converter converter;
    @Resource
    private ChannelAdmissionService channelAdmissionService;

    private static final String RENEW_EXPIRATION_BUTTON = "renew_expiration_button";
    private static final String MANUAL_INITIATE_RENEWAL_BUTTON = "manual_initiate_renewal_button";


    private void sendRegisterMq(User user, ChannelEventModel.RegisterMessage registerMessage) {
        String messageBody = JSON.toJSONString(registerMessage);
        String messageTemp = "Register@%s_%s_%s_%s";
        String messageKey = String.format(messageTemp, user.getTenantId(),
                registerMessage.getObjectApiName(),
                registerMessage.getDataId(),
                registerMessage.getApprovalStatus());
        try {
            asyncTaskProducer.create(ChannelEventTag.REGISTER.getTag(), messageBody, messageKey);
            log.info("ChannelTaskService#sendCreateLinkRelationMsg by send rocketmq message {}", messageBody);
        } catch (Exception e) {
            log.error("ChannelTaskService#sendCreateLinkRelationMsg by send rocketmq message error, body:{}", messageBody, e);
        }
    }

    public void renewExpiration(User user, String objectApiName, String partnerId, Long renewTimestamp) {
        User adminUser = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        PartnerMsgModel.RenewExpiration renewExpiration = new PartnerMsgModel.RenewExpiration();
        renewExpiration.setTenantId(user.getTenantId());
        renewExpiration.setDataId(partnerId);
        renewExpiration.setObjectApiName(objectApiName);
        renewExpiration.setRenewTimestamp(renewTimestamp);
        AsyncBootstrap.runAsyncTask(() -> asyncSendRenewExpiration(adminUser, renewExpiration));
    }

    private void asyncSendRenewExpiration(User user, PartnerMsgModel.RenewExpiration renewExpiration) {
        String messageKey = user.getTenantId()
                .concat("@")
                .concat("RenewExpiration_")
                .concat(renewExpiration.getDataId());
        String messageBody = JSON.toJSONString(renewExpiration);
        log.warn("PartnerTaskService#asyncSendRenewExpiration by send rocketmq message {}", messageBody);
        asyncTaskProducer.create(RENEW_EXPIRATION_BUTTON, messageBody, messageKey, Integer.valueOf(user.getTenantId()), null);
    }

    private boolean notFromSelfRegistration(User user, String approvalStatus, IObjectData partnerData) {
        if (partnerData == null) {
            log.warn("PartnerTaskService#isChannelPartner, tenant:{}, approvalStatus:{}", user.getTenantId(), approvalStatus);
            return true;
        }
        // 来源不是自注册
        if (!"self_registration".equals(partnerData.get("out_resources", String.class))) {
            log.warn("PartnerTaskService#isChannelPartner is not self_registration, tenant:{}, status:{}",
                    user.getTenantId(), approvalStatus);
            return true;
        }
        return false;
    }

    private void sendSignMq(User user, ChannelEventModel.@NotNull SignMessage registerMessage) {
        String messageBody = JSON.toJSONString(registerMessage);
        String messageTemp = "Sign@%s_%s_%s_%s";
        String messageKey = String.format(messageTemp,
                user.getTenantId(),
                registerMessage.getObjectApiName(),
                registerMessage.getDataId(),
                registerMessage.getApprovalStatus());
        try {
            asyncTaskProducer.create(ChannelEventTag.SIGN.getTag(), messageBody, messageKey);
            log.info("ChannelTaskService#sendSignMq by send rocketmq message {}", messageBody);
        } catch (Exception e) {
            log.error("ChannelTaskService#sendSignMq by send rocketmq message error, body:{}", messageBody, e);
        }
    }

    public void manualInitiateRenewal(User user, String admissionDataId, String signSchemeId, String outTenantId, String admissionObject) {
        User adminUser = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        AsyncBootstrap.runAsyncTask(() -> asyncManualInitiateRenewal(adminUser, admissionDataId, signSchemeId, outTenantId, admissionObject));
    }

    private void asyncManualInitiateRenewal(User user, String admissionDataId, String signSchemeId, String outTenantId, String admissionObject) {
        if (StringUtils.isAnyBlank(admissionDataId, signSchemeId, outTenantId, admissionObject)) {
            log.warn("PartnerTaskService#manualInitiateRenewal, tenant:{}, admissionDataId:{}, signSchemeId:{}, outTenantId:{}, admissionObject:{}",
                    user.getTenantId(), admissionDataId, signSchemeId, outTenantId, admissionObject);
            return;
        }
        // 查询审批激活
        ChannelRpcModel.SignReminderInfoResult signReminderInfoResult = channelService.querySignReminderInfo(user, signSchemeId, ReminderTrigger.MANUAL);
        if (!signReminderInfoResult.getEffective()) {
            return;
        }
        List<PartnerChannelManage.ReminderType> reminderTypes = signReminderInfoResult.getReminderTypes();
        if (CollectionUtils.isEmpty(reminderTypes)) {
            return;
        }
        reminderTypes.forEach(reminderType -> sendManualInitiateRenewalMQ(user, admissionDataId, signSchemeId, outTenantId, reminderType, admissionObject));
    }

    private void sendManualInitiateRenewalMQ(User user, String admissionDataId, String signSchemeId, String outTenantId, PartnerChannelManage.ReminderType reminderType, String admissionObject) {
        if (!Boolean.TRUE.equals(reminderType.getActivated())) {
            return;
        }
        PartnerChannelManage.ExpireReminderTask reminderTask = PartnerChannelManage.ExpireReminderTask.builder()
                .tenantId(user.getTenantId())
                .signSchemeId(signSchemeId)
                .dataId(admissionDataId)
                .objectApiName(admissionObject)
                .outTenantId(Long.valueOf(outTenantId))
                .reminderMethod(reminderType.getReminderMethod())
                .build();
        String messageBody = JSON.toJSONString(reminderTask);
        String messageKey = user.getTenantId()
                .concat("@")
                .concat("manualInitiateRenewalEvent")
                .concat("@")
                .concat(reminderType.getReminderMethod());
        log.warn("PartnerTaskService#manualInitiateRenewalEvent by send rocketmq message {}", messageBody);
        asyncTaskProducer.create(MANUAL_INITIATE_RENEWAL_BUTTON, messageBody, messageKey);
    }

    public void renewalSignApproval(User user, String sourceWorkFlowId, String partnerAgreementDetailId, String userId, boolean pass) {
        AdmissionConfigDTO admissionConfigDTO = channelAdmissionService.fetchChannelAdmissionConfig(user);
        String admissionObject = admissionConfigDTO.getRelatedObjectApiName();

        String renewalSignFlowId = ChannelFlowInitVO.getWorkFlowIdByScene(SFAPreDefineObject.PartnerAgreementDetail.getApiName(), ChannelFlowInitVO.RENEWAL_SIGN);
        if (!sourceWorkFlowId.equals(renewalSignFlowId)) {
            return;
        }
        IObjectData partnerAgreementDetailData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, partnerAgreementDetailId, PartnerAgreementDetailModel.OBJ_API_NAME);
        if (partnerAgreementDetailData == null) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_DATA_NOT_EXISTS, "partnerAgreementDetailId"));
        }
        String belongDataId = channelService.getAdmissionObjectDataId(partnerAgreementDetailData, admissionObject);
        if (StringUtils.isBlank(belongDataId)) {
            //todo 抛异常提示是不是更好？
            log.warn("ChannelTaskService#renewalSignApproval, partnerAgreementDetailData:{} belongDataId is null", partnerAgreementDetailData);
            return;
        }
        String outTenantId = enterpriseRelationServiceAdapter.fetchOutTenantId(user, admissionObject, belongDataId);
        if (StringUtils.isBlank(outTenantId)) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_NO_DOWNSTREAM_COMPANY));
        }

        IObjectData admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, belongDataId, admissionObject);
        if (admissionData == null) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_DATA_NOT_EXISTS, "belongDataId"));
        }
        ChannelServiceModel.MatchScheme matchScheme = channelService.matchSignScheme(user, admissionObject, admissionData);
        if (StringUtils.isBlank(matchScheme.getSchemeId())) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_NOT_MATCH, I18NUtils.getDataI18nName(admissionData)));
        }
        updateAbleRenewalFalse(user, partnerAgreementDetailData);
        ChannelEventModel.RenewMessage renewMessage = ChannelEventModel.RenewMessage.builder()
                .operator(userId)
                .app(admissionConfigDTO.getApplyToApp())
                .tenantId(user.getTenantId())
                .partnerAgreementDetailId(partnerAgreementDetailId)
                .objectDataId(belongDataId)
                .objectApiName(admissionObject)
                .outTenantId(Long.valueOf(outTenantId))
                .signSchemeId(matchScheme.getSchemeId())
                .language(I18NUtils.getLanguage())
                .currentStatus(SignStatus.PENDING_RENEWAL.getStatus())
                .pass(pass)
                .build();
        String messageBody = JSON.toJSONString(renewMessage);
        String messageTemp = "Sign@%s_%s_%s_%s";
        String messageKey = String.format(messageTemp, user.getTenantId(), admissionObject, belongDataId, pass);
        log.warn("ChannelTaskService#renewalSignApproval by send rocketmq message {}", messageBody);
        asyncTaskProducer.create(ChannelEventTag.RENEW.getTag(), messageBody, messageKey);

    }

    private void updateAbleRenewalFalse(User user, IObjectData partnerAgreementDetailData) {
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put("able_renewal", false);
        log.info("续签提醒:更新 able_renewal = false");
        serviceFacade.updateWithMap(user, partnerAgreementDetailData, updateMap);
    }

    public void registerEvent(User user, ChannelEventModel.RegisterMessage registerMessage) {
        if (registerMessage == null) {
            log.warn("ChannelTaskService#registerEvent, registerMessage is null");
            return;
        }
        String channelAdmissionObject = channelService.fetchChannelAdmissionObject(user);
        if (StringUtils.isBlank(channelAdmissionObject)) {
            log.warn("ChannelTaskService#registerEvent, channelAdmissionObject is null");
            return;
        }
        if (!channelAdmissionObject.equals(registerMessage.getObjectApiName())) {
            log.warn("ChannelTaskService#registerEvent, registerApprovalEvent.getObjectApiName:{} is not equals channelAdmissionObject:{}",
                    registerMessage.getObjectApiName(), channelAdmissionObject);
            return;
        }
        sendRegisterMq(user, registerMessage);
    }

    public void signEvent(User user, @NotNull ChannelEventModel.SignMessage signMessage) {
        String channelAdmissionObject = channelService.fetchChannelAdmissionObject(user);
        if (StringUtils.isBlank(channelAdmissionObject)) {
            log.warn("ChannelTaskService#signEvent, channelAdmissionObject is null");
            return;
        }
        if (!channelAdmissionObject.equals(signMessage.getObjectApiName())) {
            log.warn("ChannelTaskService#signEvent, signApprovalEvent.getObjectApiName:{} is not equals channelAdmissionObject:{}",
                    signMessage.getObjectApiName(), channelAdmissionObject);
            return;
        }
        sendSignMq(user, signMessage);
    }
}
