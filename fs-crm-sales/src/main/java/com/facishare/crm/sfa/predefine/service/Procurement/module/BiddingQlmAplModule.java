package com.facishare.crm.sfa.predefine.service.Procurement.module;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface BiddingQlmAplModule {

    // 网址校验
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class UrlCheckArg {
        private List<String> dataList;
        private List<String> urlList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class UrlCheckResult {
        private String code;
        private String msg;
        private Map<String,String> body;

        public static UrlCheckResult error(String errorMsg){
            return UrlCheckResult.builder().code("500").msg(errorMsg).build();
        }

    }

    // 获取token
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class GetTokenArg {
        private String dataId;
        private String bidId;
        private Boolean onlyToken;
        private Boolean refreshToken;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class GetTokenResult {
        private String code;
        private String errMsg;
        private String url;
        private String token;
        public static GetTokenResult error(String errMsg){
            return GetTokenResult.builder().code("500").errMsg(errMsg).build();
        }
    }

    // 获取医院信息
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class GetHospitalArg{
        private List<String> hospitalList;
        private List<String> classificationList;
        private List<Integer> taskList;
        private Integer taskId;
    }

    // 获取医院信息
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class GetHospitalResult{
        private String code;
        private String errMsg;
        private Object body;
    }

}
