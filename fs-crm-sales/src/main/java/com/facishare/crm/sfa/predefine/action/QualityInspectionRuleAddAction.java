package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleService;
import com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel;
import com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.QIUser;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Idempotent(serializer = Serializer.Type.java)
public class QualityInspectionRuleAddAction extends StandardAddAction {
    private static final QualityInspectionRuleService qualityInspectionRuleService = SpringUtil.getContext().getBean(QualityInspectionRuleService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        IObjectData data = arg.getObjectData().toObjectData();
        String record_type = data.getRecordType();
        if (QIGetWechatConversionModel.record_dirtyword_layout.equals(record_type)) {
            data.set("type", 1);
            data.set("session_feature", "1");
            data.set("session_action", "1");
            //data.put("layout_api_name", dirtyword_layout_api);
        } else if (record_type.contains("action")) {
            data.set("type", 2);
            data.set("dirty_words", "{}");
            data.set("session_feature", "1");
            data.set("session_action", String.valueOf(data.get("session_action")));
            //data.put("record_type", record_action_layout);
            //data.put("layout_api_name", action_layout_api);
        } else if (record_type.contains("feature")) {
            data.set("type", 3);
            data.set("dirty_words", "{}");
            data.set("session_feature", String.valueOf(data.get("session_feature")));
            data.set("session_action", "1");
            //data.put("record_type", record_feature_layout);
            //data.put("layout_api_name", feature_layout_api);
        }
        data.setOwner(Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId()));
        log.warn("QualityInspectionRuleAddAction before {}", data);
    }

    @Override
    protected Result doAct(Arg arg) {
        ObjectDataDocument data = arg.getObjectData();
        QIUser monitors = JSON.parseObject(String.valueOf(data.get("monitors")), QIUser.class);
        QIUser msgUser = JSON.parseObject(String.valueOf(data.get("msg_user")), QIUser.class);

        Result doAct = super.doAct(arg);
        String ruleId = doAct.getObjectData().getId();

        log.warn("QualityInspectionRuleAddAction doAct {}, {}, {}, {}", ruleId, monitors, msgUser, doAct.getObjectData());

        qualityInspectionRuleService.updateRule(actionContext, ruleId, data);
        qualityInspectionRuleService.addRuleMember(actionContext, monitors, msgUser, ruleId);
        qualityInspectionRuleService.putRecordLayout((Integer) data.get("type"), data);
        return doAct;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        IObjectData data = result.getObjectData().toObjectData();
        qualityInspectionRuleService.syncRule(actionContext.getUser(), data);
        return result;
    }
}

