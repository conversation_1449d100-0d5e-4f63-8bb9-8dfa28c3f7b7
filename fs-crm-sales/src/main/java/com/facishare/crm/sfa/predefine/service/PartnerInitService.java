package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.constants.CommonConstants;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.describebuilder.ObjectReferenceFieldDescribeBuilder;
import com.facishare.crm.describebuilder.SelectOneFieldDescribeBuilder;
import com.facishare.crm.describebuilder.SelectOptionBuilder;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.rest.ApprovalInitProxy;
import com.facishare.crm.rest.CrmRestApi;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.AssignRecordAndLayoutModel;
import com.facishare.crm.sfa.predefine.service.model.ExceptionInfo;
import com.facishare.crm.sfa.predefine.service.model.SaveObjectToPrmModel;
import com.facishare.crm.rest.InitObjectsPermissionsAndLayoutProxy;
import com.facishare.crm.sfa.utilities.util.PartnerConfigGrayUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.AddRoleRecordTypeModel;
import com.facishare.paas.appframework.metadata.dto.auth.AddRoleViewModel;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypePojo;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypeRoleViewPojo;
import com.facishare.paas.appframework.privilege.RoleService;
import com.facishare.paas.appframework.privilege.dto.SupportEnterpriseRelationResult;
import com.facishare.paas.appframework.privilege.model.role.ChannelManagerRoleProvider;
import com.facishare.paas.appframework.privilege.model.role.Role;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.auth.model.RoleViewPojo;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.service.impl.LayoutServiceImpl;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.constants.LayoutConstants.BASE_FIELD_SECTION_API_NAME;
import static com.facishare.crm.sfa.utilities.constant.PartnerConstants.OUT_RESOURCES;
import static com.facishare.crm.sfa.utilities.constant.PartnerConstants.PARTNER_ID;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;
import static com.facishare.paas.appframework.core.model.User.SUPPER_ADMIN_USER_ID;
import static com.facishare.paas.appframework.metadata.ObjectDescribeExt.PREDEFINE_CUSTOM_OBJECTS;
import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;

@ServiceModule("partner_init")
@Service
@Slf4j
public class
PartnerInitService {
    @Autowired
    CRMNotificationServiceImpl crmNotificationService;
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    RecordTypeLogicServiceImpl recordTypeLogicService;
    @Autowired
    EnterpriseInitService enterpriseInitService;
    @Autowired
    ConfigService configService;
    @Autowired
    CrmRestApi crmRestApi;
    @Autowired
    ApprovalInitProxy approvalInitProxy;
    @Autowired
    RoleService roleService;
    @Autowired
    ChannelManagerRoleProvider channelManagerRoleProvider;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    LayoutServiceImpl layoutService;
    @Autowired
    InitObjectsPermissionsAndLayoutProxy initObjectsPermissionsAndLayoutProxy;
    @Autowired
    private BizConfigThreadLocalCacheService configCacheService;
    @Autowired
    private RecordTypeAuthProxy recordTypeAuthProxy;// 分配业务类型和默认布局
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private DescribeServiceExt describeServiceExt;
    @Autowired
    private LayoutServiceExt layoutServiceExt;
    @Autowired
    private FunctionPrivilegeServiceExt functionPrivilegeServiceExt;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Resource(name = "describeWithSimplifiedChineseService")
    private DescribeWithSimplifiedChineseService simplifiedChineseService;

    private static final String configPartnerIsOpenKey = "config_partner_open";
    private static final String statusClose = "close";
    private static final List<String> grayObject = Lists.newArrayList("ScheduleRepeatObj", "ScheduleObj");

    private static final Map<String, String> outResources = ImmutableMap.<String, String>builder()
            .put("partner", "代理通")//ignoreI18n
            .put("kx_fxt", "访销通")//ignoreI18n
            .put("self_registration", "自注册")//ignoreI18n
            .build();

    public static final List<String> PrmUnSupportApiName = Lists.newArrayList(
            "AccountFinInfoObj", "AccountAddrObj", "SalesOrderProductObj", "ReturnedGoodsInvoiceProductObj");

    /**
     * 合作伙伴开启
     */
    @ServiceMethod("status_open")
    public Map openStatus(ServiceContext context) {
        String configPartnerIsOpen = configService.findTenantConfig(context.getUser(), configPartnerIsOpenKey);
        Map map = Maps.newHashMap();
        map.put("result", StringUtils.isEmpty(configPartnerIsOpen) ? statusClose : configPartnerIsOpen);
        return map;
    }

    /**
     * 自定义对象绑定合作伙伴
     */
    @ServiceMethod("openObject")
    public boolean openServiceForObject(ServiceContext context, List<String> apiNames) {
        String openApiNamesString = configService.findTenantConfig(context.getUser(), PrmConstant.CONFIG_PARTNER_KEY);
        if (!StringUtils.isEmpty(openApiNamesString)) {
            List<String> openApiNames = JSON.parseArray(openApiNamesString, String.class);
            apiNames.removeAll(openApiNames);
        }
        if (!apiNames.isEmpty()) {
            openObject(context.getUser(), apiNames, Maps.newHashMap());
        }
        return true;
    }

    /**
     * 用于互联添加对象到prm
     */
    @ServiceMethod("addObjectToPrm")
    public List<String> addObject2Prm(ServiceContext context, List<String> apiNames) {
        List<String> invalidObject = Lists.newArrayList();
        if (CollectionUtils.empty(apiNames)) {
            return invalidObject;
        }
        // 自定义对象可以被添加到 prm
        // prm支持的预设对象不需要在这里处理
        for (String apiName : apiNames) {
            if (apiName.endsWith("__c")) {
                openServiceForObject(context, Lists.newArrayList(apiName));
            } else if (!PrmConstant.menuApiNames.contains(apiName)) {
                log.warn("该对象暂不支持被添加到代理通应用中,tenant:{}, apiName:{}", context.getTenantId(), apiName);
                invalidObject.add(apiName);
            }
        }
        return invalidObject;
    }

    @ServiceMethod("checkLinkAppAssociateObjectApiEnable")
    public SaveObjectToPrmModel.Result checkOrSaveObjectToPrm(ServiceContext context, SaveObjectToPrmModel.Arg arg) {
        List<String> objectApiNames = arg.getObjectApiName();
        if ((!"add".equals(arg.getAction()) && !"remove".equals(arg.getAction())) || CollectionUtils.empty(objectApiNames)) {
            return SaveObjectToPrmModel.Result.builder().message(I18N.text(SFA_CONFIG_PARAMETER_ERROR1)).enable(false).build();
        }
        List<String> preObjects = objectApiNames.stream().filter(apiName -> !apiName.endsWith("__c")).collect(Collectors.toList());
        List<String> cusObjects = objectApiNames.stream().filter(apiName -> apiName.endsWith("__c")).collect(Collectors.toList());
        if (!"add".equals(arg.getAction())) {
            return SaveObjectToPrmModel.Result.builder().enable(true).build();
        }
        if (unOpenPartnerSwitch(context.getUser())) {
            return SaveObjectToPrmModel.Result.builder().message(I18N.text(SFA_PARTNER_SWITCH_NOT_OPEN)).enable(false).build();
        }
        Set<String> menuApiNames = Sets.newHashSet();
        menuApiNames.addAll(PrmConstant.menuApiNames);
        menuApiNames.addAll(PrmConstant.newMenuApiNames);
        menuApiNames.addAll(PartnerConfigGrayUtils.getOnlyAccessObjectsOfPRM());
        List<String> grayMenuApiNames = PartnerConfigGrayUtils.getGrayMenuApiNames();
        if (CollectionUtils.notEmpty(grayMenuApiNames)) {
            menuApiNames.addAll(grayMenuApiNames);
        }
        if (PrmConstant.isAllowByBusiness("SUPPORT_CROSS_SCHEDULE", context.getUser().getTenantIdInt())) {
            menuApiNames.addAll(grayObject);
        }
        if (CollectionUtils.notEmpty(preObjects) && !menuApiNames.containsAll(preObjects)) {
            List<String> unSupportedPreObjects = preObjects.stream().filter(x -> !menuApiNames.contains(x)).collect(Collectors.toList());
            RequestContextManager.getContext().setAttribute(RequestContext.DIRECT_KEY, true);
            List<String> objectNames = describeLogicService.findObjectList(context.getTenantId(), unSupportedPreObjects)
                    .stream().map(IObjectDescribe::getDisplayName).collect(Collectors.toList());
            return SaveObjectToPrmModel.Result.builder().message(I18N.text(PRM_NOT_SUPPORTED_PRE_OBJETS, objectNames)).enable(false).build();
        }
        Map<String, ExceptionInfo> failedObjectExceptionMap = Maps.newHashMap();
        try {
            //处理自定义对象
            openObject(context.getUser(), cusObjects, failedObjectExceptionMap);
            //处理预设对象
            openPresetObject(context, preObjects, failedObjectExceptionMap);
        } catch (Exception e) {
            return SaveObjectToPrmModel.Result.builder().message("inner error").enable(false).build();
        }
        String errorMsg = sendNotification(context.getUser(), failedObjectExceptionMap, objectApiNames, "saveObjectToPrm");
        if (StringUtils.isBlank(errorMsg)) {
            errorMsg = "add failed";
        }
        if (CollectionUtils.notEmpty(failedObjectExceptionMap)) {
            log.warn("add object to prm failed, tenant:{}, errorMsg:{}", context.getTenantId(), errorMsg);
            return SaveObjectToPrmModel.Result.builder().message(errorMsg).enable(false).build();
        }
        return SaveObjectToPrmModel.Result.builder().enable(true).build();
    }

    private boolean unOpenPartnerSwitch(User user) {
        IObjectDescribe partnerDescribe = serviceFacade.findObject(user.getTenantId(), SFAPreDefineObject.Partner.getApiName());
        return partnerDescribe == null;
    }

    /**
     * 获取未绑定合作伙伴的自定义对象
     */
    @ServiceMethod("getDescribeListNoPartner")
    public List<ObjectDescribeDocument> getDescribeListNoPartner(ServiceContext context) {
        //用crm管理员权限获取自定义对象
        User user = new User(context.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        List<String> users = serviceFacade.getUsersByRole(user, CommonConstants.CRM_MANAGER_ROLE);
        if (!users.isEmpty()) {
            user = new User(context.getTenantId(), users.get(0));
        }
        List<IObjectDescribe> describeList = serviceFacade.findDescribeByPrivilegeAndModule(user,
                ObjectAction.VIEW_LIST.getActionCode(), false, true, false, true);
//        describeList.removeIf(f -> PrmConstant.predefineCustomObjects.contains(f.getApiName()));
        describeList.removeIf(r -> !IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(r.getDefineType()));
        describeList.removeIf(f -> PREDEFINE_CUSTOM_OBJECTS.contains(f.getApiName()));
        describeList.removeIf(f -> PrmConstant.menuApiNames.contains(f.getApiName()));
        describeList.removeIf(f -> PrmUnSupportApiName.contains(f.getApiName()));
        String openApiNamesString = configService.findTenantConfig(context.getUser(), PrmConstant.CONFIG_PARTNER_KEY);
        if (!StringUtils.isEmpty(openApiNamesString)) {
            List<String> openApiNames = JSON.parseArray(openApiNamesString, String.class);
            describeList.removeIf(f -> openApiNames.contains(f.getApiName()));
        }
        for (IObjectDescribe describe : describeList) {
            describe.setFieldDescribes(Lists.newArrayList());
            describe.setActions(Lists.newArrayList());
            describe.setConfig(Maps.newHashMap());
        }
        return ObjectDescribeDocument.ofList(describeList);
    }

    /**
     * 获取绑定合作伙伴的所有对象
     */
    @ServiceMethod("getDescribeListHasPartner")
    public List<ObjectDescribeDocument> getDescribeListHasPartner(ServiceContext context) {
        List<IObjectDescribe> describeList = Lists.newArrayList();
        String openApiNamesString = configService.findTenantConfig(context.getUser(), PrmConstant.CONFIG_PARTNER_KEY);
        List<String> openApiNames = Lists.newArrayList();
        openApiNames.addAll(PrmConstant.menuApiNames);
        if (!StringUtils.isEmpty(openApiNamesString)) {
            openApiNames.addAll(JSON.parseArray(openApiNamesString, String.class));
        }
        Map<String, IObjectDescribe> objects = serviceFacade.findObjects(context.getTenantId(), openApiNames);
        describeList.addAll(objects.values());
        //处理对象排序 老对象在上面
        List<IObjectDescribe> retdescribeList = Lists.newArrayList();
        for (String apiName : openApiNames) {
            for (IObjectDescribe iObjectDescribe : describeList) {
                if (iObjectDescribe.getApiName().equals(apiName)) {
                    retdescribeList.add(iObjectDescribe);
                }
            }
        }
        for (IObjectDescribe describe : retdescribeList) {
            describe.setFieldDescribes(Lists.newArrayList());
            describe.setActions(Lists.newArrayList());
            describe.setConfig(Maps.newHashMap());
        }
        return ObjectDescribeDocument.ofList(retdescribeList);
    }

    public void openObject(User user, List<String> apiNames, Map<String, ExceptionInfo> failedObjectExceptionMap) {
        if (CollectionUtils.empty(apiNames)) {
            return;
        }
        String openApiNamesString = configService.findTenantConfig(user, PrmConstant.CONFIG_PARTNER_KEY);
        if (openApiNamesString != null) {
            List<String> openApiNames = JSON.parseArray(openApiNamesString, String.class);
            apiNames.removeAll(openApiNames);
        }
        //自定义对象新增字段
        Map<String, IObjectDescribe> describeMap = getPreObjectDescribeMap(user, apiNames);
        try {
            for (String apiName : apiNames) {
                addFieldByApiName(user, apiName, describeMap.get(apiName), failedObjectExceptionMap);
            }
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(), e);
            throw new ValidateException(e.getMessage());
        }
        //移除成环的对象
        if (CollectionUtils.notEmpty(failedObjectExceptionMap)) {
            apiNames.removeAll(failedObjectExceptionMap.values().stream().map(o -> o.getApiName()).collect(Collectors.toList()));
        }
        if (CollectionUtils.empty(apiNames)) {
            return;
        }
        //自定义对象分配权限
        List<String> actionCodeList = Lists.newArrayList(ObjectAction.CHANGE_PARTNER.getActionCode(),
                ObjectAction.DELETE_PARTNER.getActionCode());
        for (String needInitApiName : apiNames) {
            try {
                serviceFacade.batchCreateFunc(user, needInitApiName, actionCodeList);
                serviceFacade.updateUserDefinedFuncAccess(user,
                        PrivilegeConstants.ADMIN_ROLE_CODE, needInitApiName, actionCodeList, Lists.newArrayList());
                serviceFacade.updateUserDefinedFuncAccess(user,
                        Role.CHANNEL_MANAGER.getRoleCode(), needInitApiName, actionCodeList, Lists.newArrayList());
            } catch (Exception e) {
                log.error("batchInitPartnerRelateAction addRoleFunc error,apiName {}", needInitApiName, e);
            }
        }
        if (StringUtils.isEmpty(openApiNamesString)) {
            configService.createTenantConfig(user, PrmConstant.CONFIG_PARTNER_KEY, JSON.toJSONString(apiNames), ConfigValueType.STRING);
        } else {
            List<String> openApiNames = JSON.parseArray(openApiNamesString, String.class);
            openApiNames.addAll(apiNames);
            configService.updateTenantConfig(user, PrmConstant.CONFIG_PARTNER_KEY, JSON.toJSONString(Sets.newHashSet(openApiNames)), ConfigValueType.STRING);
        }
    }

    public void openPresetObject(ServiceContext context, List<String> apiNames, Map<String, ExceptionInfo> failedObjectExceptionMap) throws MetadataServiceException {
        if (CollectionUtils.empty(apiNames)) {
            return;
        }
        Map<String, IObjectDescribe> preObjectDescribeMap = getPreObjectDescribeMap(context.getUser(), apiNames);
        for (Map.Entry<String, IObjectDescribe> data : preObjectDescribeMap.entrySet()) {
            String objectApiName = data.getKey();
            IObjectDescribe describe = data.getValue();
            if (PartnerConfigGrayUtils.isOnlyAccessObject(objectApiName)) {
                continue;
            }
            if (SFAPreDefineObject.ProductCategory.getApiName().equals(objectApiName) && !configCacheService.isCloseOldCategory(context.getTenantId())) {
                // 产品分类未对象化的企业不允许添加
                String message = I18N.text(SFA_PRM_ADD_CATEGORY_OBJECT_ERROR);
                failedObjectExceptionMap.put(describe.getDisplayName(), ExceptionInfo.builder().errorCode(201111039).errorMessage(message).build());
                continue;
            }
            if (grayObject.contains(objectApiName) && !PrmConstant.isAllowByBusiness("SUPPORT_CROSS_SCHEDULE", context.getUser().getTenantIdInt())) {
                String message = I18N.text(PRM_NOT_SUPPORTED_PRE_OBJETS);
                failedObjectExceptionMap.put(describe.getDisplayName(), ExceptionInfo.builder().errorCode(201111039).errorMessage(message).build());
                continue;
            }
            try {
                addPartnerField2Describe(context.getUser(), describe);
            } catch (MetadataServiceException mtEx) {
                log.warn("openPresetObject MetadataServiceException, tenant:{}, objectApiName:{}", context.getTenantId(), objectApiName, mtEx);
                if (mtEx.getErrorCode() != null && mtEx.getErrorCode().getCode() == 201111039) {
                    failedObjectExceptionMap.put(describe.getDisplayName(), ExceptionInfo.builder().errorCode(mtEx.getErrorCode().getCode()).errorMessage(mtEx.getMessage()).build());
                    continue;
                } else {
                    throw mtEx;
                }
            }
            try {
                addPartnerField2Layout(context.getUser(), describe);
                createPartnerSpecialFunc(context.getUser(), describe);
            } catch (Exception ex) {
                log.warn("sfa.add.object.to.prm.error", ex);
                throw new ValidateException(I18N.text("sfa.add.object.to.prm.error"));
            }
        }
    }

    public void addFieldByApiName(User user, String apiName, IObjectDescribe objectDescribe, Map<String, ExceptionInfo> failedObjectExceptionMap) throws MetadataServiceException {
        if (objectDescribe == null) {
            return;
        }
        List<IFieldDescribe> addFields = Lists.newArrayList();
        List<String> addFieldName = Lists.newArrayList();
        //合作伙伴field
        IFieldDescribe partnerField = new ObjectReferenceFieldDescribe();
        partnerField.fromJsonString(enterpriseInitService.getPartnerJsonFromFieldName("partnerId"));
        partnerField.setDescribeApiName(apiName);
        partnerField.set("target_related_list_label", objectDescribe.getDisplayName());
        partnerField.set("target_related_list_name", "partner_" + apiName.toLowerCase() + "_list");
        // 灰度关联外部负责人权限
        if (!ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
            List<IFieldDescribe> fieldDescribeList = fieldDescribes.stream().filter(x -> "object_reference".equals(x.getType()) && "PartnerObj".equals(x.get("target_api_name"))).collect(Collectors.toList());
            Optional<IFieldDescribe> any = fieldDescribeList.stream().filter(x -> "outer_owner".equals(x.get("relation_outer_data_privilege"))).findAny();
            if (!any.isPresent()) {
                partnerField.set("relation_outer_data_privilege", "outer_owner");
            }
        }
        //外部来源
        IFieldDescribe outResourcesField = new SelectOneFieldDescribe();
        outResourcesField.fromJsonString(enterpriseInitService.getPartnerJsonFromFieldName("outResources"));
        outResourcesField.setDescribeApiName(apiName);

        if (!objectDescribe.getFieldDescribeMap().containsKey("partner_id")) {
            addFields.add(partnerField);
            addFieldName.add("partner_id");
        }
        if (!objectDescribe.getFieldDescribeMap().containsKey("out_resources")) {
            addFields.add(outResourcesField);
            addFieldName.add("out_resources");
        }
        if (addFields.isEmpty()) {
            return;
        }
        try {
            objectDescribeService.addCustomFieldDescribe(objectDescribe, addFields);
        } catch (MetadataServiceException mtEx) {
            log.warn("SaveObjectToPrm MetadataServiceException, tenant:{}, objectApiName:{}", user.getTenantId(), apiName, mtEx);
            if (mtEx.getErrorCode() != null && mtEx.getErrorCode().getCode() == 201111039) {
                failedObjectExceptionMap.put(objectDescribe.getDisplayName(), ExceptionInfo.builder().apiName(apiName).errorCode(mtEx.getErrorCode().getCode()).errorMessage(mtEx.getMessage()).build());
                return;
            } else {
                throw mtEx;
            }
        }
        //字段放到布局里
        FieldLayoutPojo partnerLayoutPojo = new FieldLayoutPojo();
        partnerLayoutPojo.setShow(true);
        partnerLayoutPojo.setReadonly(false);
        partnerLayoutPojo.setRequired(false);
        partnerLayoutPojo.setApiName(partnerField.getApiName());
        partnerLayoutPojo.setRenderType(partnerField.getType());
        FieldLayoutPojo outResourcesLayoutPojo = new FieldLayoutPojo();
        outResourcesLayoutPojo.setShow(true);
        outResourcesLayoutPojo.setReadonly(false);
        outResourcesLayoutPojo.setRequired(false);
        outResourcesLayoutPojo.setApiName(outResourcesField.getApiName());
        outResourcesLayoutPojo.setRenderType(outResourcesField.getType());
        List<ILayout> layouts = serviceFacade.getLayoutLogicService().findLayoutByObjectApiName(user.getTenantId(), apiName);
        if (!layouts.isEmpty()) {
            for (ILayout layout : layouts) {
                LayoutExt layoutExt = LayoutExt.of(layout);
                if (layoutExt.isListLayout()) {
                    continue;
                }
                if (addFieldName.contains("partner_id")) {
                    layoutExt.addField(partnerField, partnerLayoutPojo);
                }
                if (addFieldName.contains("out_resources")) {
                    layoutExt.addField(outResourcesField, outResourcesLayoutPojo);
                }
                Optional<IFormField> formField = layoutExt.getField("out_owner");
                if (!formField.isPresent()) {
                    //添加外部负责人
                    IFieldDescribe iFieldDescribe = objectDescribe.getFieldDescribe("out_owner");
                    if (iFieldDescribe != null) {
                        FieldLayoutPojo outOwnerPojo = new FieldLayoutPojo();
                        outOwnerPojo.setShow(true);
                        outOwnerPojo.setReadonly(true);
                        outOwnerPojo.setRequired(false);
                        outOwnerPojo.setApiName(iFieldDescribe.getApiName());
                        outOwnerPojo.setRenderType(iFieldDescribe.getType());
                        layoutExt.addField(iFieldDescribe, outOwnerPojo);
                    }
                }
                try {
                    serviceFacade.getLayoutLogicService().updateLayout(user, layout);
                } catch (Exception e) {
                    log.warn("add custom object partner_id field to layout failed, tenant:{}", user.getTenantId(), e);
                }
            }
        }
    }

    @ServiceMethod("batchInitPartnerTransferAction")
    public Boolean batchInitPartnerTransferAction(ServiceContext context, List<String> tenants) {
        tenants.parallelStream().forEach(tenant -> {
            User user = new User(tenant, SUPPER_ADMIN_USER_ID);
            if (!configCacheService.isPartnerEnabled(user.getTenantId())) {
                return;
            }
            List<String> actionCodeList = Lists.newArrayList(ObjectAction.TRANSFER_PARTNER.getActionCode());
            try {
                log.info("batchInitPartnerTransferAction doing..., tenant:{}", tenant);
                serviceFacade.batchCreateFunc(user, "LeadsObj", actionCodeList);
                serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, "LeadsObj", actionCodeList, Lists.newArrayList());
                // 转换新建
                serviceFacade.batchCreateFunc(user, SFAPreDefineObject.Partner.getApiName(), Lists.newArrayList(ObjectAction.TRANSFER_ADD.getActionCode()));
                serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, SFAPreDefineObject.Partner.getApiName(), Lists.newArrayList(ObjectAction.TRANSFER_ADD.getActionCode()), Lists.newArrayList());
            } catch (Throwable e) {
                log.warn("batchInitPartnerTransferAction failed, tenant:{}", tenant, e);
            }
        });
        return true;
    }

    /**
     * 初始化更换合作伙伴三个操作
     */
    public String batchInitPartnerRelateAction(User user, List<String> apiNames) {
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.empty(apiNames)) {
            return stringBuilder.toString();
        }
        apiNames.remove(Utils.CONTACT_API_NAME);
        apiNames.remove("ActiveRecordObj");
        List<String> actionCodeList = Lists.newArrayList(ObjectAction.CHANGE_PARTNER.getActionCode(),
                ObjectAction.DELETE_PARTNER.getActionCode());
        for (String needInitApiName : apiNames) {
            try {
                serviceFacade.batchCreateFunc(user, needInitApiName, actionCodeList);
                serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, needInitApiName, actionCodeList, Lists.newArrayList());
            } catch (Exception e) {
                stringBuilder.append(String.format("batchInitPartnerRelateAction error,apiName %s,failMsg %s", needInitApiName, e.getMessage()));
                log.error("batchInitPartnerRelateAction error,apiName {} {}", needInitApiName, e.toString());
            }
        }
        return StringUtils.isNotEmpty(stringBuilder.toString()) ? " \n batchInitPartnerRelateAction success" : stringBuilder.toString();
    }

    public String createPrmLayout(User user, String partnerApiName) {
        StringBuilder rst = new StringBuilder();
        try {
            List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(partnerApiName, user.getTenantId());
            if (layouts == null || layouts.isEmpty()) {
                rst.append(enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(partnerApiName), user.getTenantId()));
            }
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }

        List<Tuple<String, String>> layoutList = Lists.newArrayList();

        layoutList.add(Tuple.of(partnerApiName, "detailprmzzc"));//初始化合伙伙伴自注册布局
        layoutList.add(Tuple.of(Utils.CONTACT_API_NAME, "partnerdetail"));//初始化合作伙伴员工布局
        layoutList.add(Tuple.of(Utils.CONTACT_API_NAME, "partnerdetailprmzzc"));//初始化自注册合作伙伴员工布局
        rst.append(batchCreateLayout(user, layoutList));
        return rst.toString();
    }

    public String createPrmContactLayout(User user) {
        StringBuilder rst = new StringBuilder();
        List<Tuple<String, String>> layoutList = Lists.newArrayList();
        layoutList.add(Tuple.of(Utils.CONTACT_API_NAME, "partnerdetail"));//初始化合作伙伴员工布局
        layoutList.add(Tuple.of(Utils.CONTACT_API_NAME, "partnerdetailprmzzc"));//初始化自注册合作伙伴员工布局
        rst.append(batchCreateLayout(user, layoutList));
        return rst.toString();
    }

    private String batchCreateLayout(User user, List<Tuple<String, String>> layoutKeyList) {
        if (CollectionUtils.empty(layoutKeyList)) {
            return "";
        }
        StringBuilder rst = new StringBuilder();
        for (Tuple<String, String> entry : layoutKeyList) {
            String apiName = entry.getKey();
            String layoutType = entry.getValue();
            String layoutJson = enterpriseInitService.getLayoutJsonFromResourceByApiName(apiName, layoutType);
            ILayout layout = new Layout(Document.parse(layoutJson));
            try {
                serviceFacade.getLayoutLogicService().createLayout(user, layout);
                rst.append(String.format(" \n %s_%s createPrmLayout success", apiName, layoutType));
            } catch (Exception e) {
                rst.append(" \n createPrmLayout error:" + e.getMessage());
                log.error("partner_init open createPrmLayout error,tenantId {}", user.getTenantId(), e);
            }
        }
        return rst.toString();
    }

    public String createPrmContactRecordType(User user) {
        List<String> jsonList = Lists.newArrayList();
        //初始化合作伙伴联系人业务类型
        jsonList.add("{\"label\":\"合作伙伴联系人\",\"api_name\":\"default_contact_partner__c\",\"description\":\"合作伙伴联系人1\",\"config\":{\"remove\":0},\"is_active\":true,\"roles\":[{\"role_code\":\"00000000000000000000000000000025\",\"is_default\":true,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetail_layout_by_UDObjectServer__c\"},{\"role_code\":\"00000000000000000000000000000006\",\"is_default\":false,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetail_layout_by_UDObjectServer__c\"}]}");//ignoreI18n
        // 初始化合作伙伴自注册业务类型
        jsonList.add("{\"label\":\"合作伙伴自注册联系人\",\"api_name\":\"default_contact_partner_zzc__c\",\"description\":\"合作伙伴联系人\",\"config\":{\"remove\":0},\"is_active\":true,\"roles\":[{\"role_code\":\"00000000000000000000000000000025\",\"is_default\":true,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetailprmzzc_layout_by_UDObjectServer__c\"},{\"role_code\":\"00000000000000000000000000000006\",\"is_default\":false,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetailprmzzc_layout_by_UDObjectServer__c\"}]}");//ignoreI18n

        StringBuilder rst = new StringBuilder();
        for (String json : jsonList) {
            try {
                RecordTypeRoleViewPojo pojo = JSON.parseObject(json, RecordTypeRoleViewPojo.class);
                RecordTypeResult result = recordTypeLogicService.createRecordType(user.getTenantId(), Utils.CONTACT_API_NAME, pojo, user);
                if (!result.isSuccess()) {
                    rst.append("createPrmContactRecordType:" + json.substring(0, 65) + " error:" + result.getFailMessage());
                    log.warn("partner_init createPrmContactRecordType:" + json.substring(0, 65) + " error,tenantId {}", user.getTenantId(), result.getFailMessage());
                } else {
                    rst.append(" \n createPrmContactRecordType:" + json.substring(0, 65) + " success");
                }
            } catch (Exception ex) {
                rst.append(" \n createPrmContactRecordType:" + json.substring(0, 65) + " error:" + ex.getMessage());
                log.warn("partner_init createPrmContactRecordType:" + json.substring(0, 65) + " error,tenantId {}", user.getTenantId(), ex);
            }
        }
        return rst.toString();
    }

    public void addPartner2AvailableRange(String tenantId, boolean needAddApplyToPartner) {
        IObjectDescribe objectDescribe = simplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId), SFAPreDefineObject.AvailableRange.getApiName());
        if (null == objectDescribe) {
            return;
        }
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        if (null == objectDescribe.getFieldDescribe("partner_range")) {
            IFieldDescribe rangeFieldDescribe = new UseRangeFieldDescribe();
            rangeFieldDescribe.fromJsonString(enterpriseInitService.getAvailableJsonFromFieldName("partnerrange"));
            rangeFieldDescribe.setDescribeApiName(SFAPreDefineObject.AvailableRange.getApiName());
            fieldDescribeList.add(rangeFieldDescribe);
        }
        if (needAddApplyToPartner && null == objectDescribe.getFieldDescribe("apply_to_partner")) {
            IFieldDescribe booleanFieldDescribe = new BooleanFieldDescribe();
            booleanFieldDescribe.fromJsonString(enterpriseInitService.getAvailableJsonFromFieldName("applytopartner"));
            booleanFieldDescribe.setDescribeApiName(SFAPreDefineObject.AvailableRange.getApiName());
            fieldDescribeList.add(booleanFieldDescribe);
        }
        addCustomFieldDescribe(objectDescribe, fieldDescribeList);
    }

    public void addChannel2AvailableRange(String tenantId) {
        IObjectDescribe objectDescribe = simplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId), SFAPreDefineObject.AvailableRange.getApiName());
        if (null == objectDescribe || null != objectDescribe.getFieldDescribe("apply_to_channel_customer")) {
            return;
        }
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        IFieldDescribe booleanFieldDescribe = new BooleanFieldDescribe();
        booleanFieldDescribe.fromJsonString(enterpriseInitService.getAvailableJsonFromFieldName("applytochannel"));
        booleanFieldDescribe.setDescribeApiName(SFAPreDefineObject.AvailableRange.getApiName());
        fieldDescribeList.add(booleanFieldDescribe);
        addCustomFieldDescribe(objectDescribe, fieldDescribeList);
    }

    public void addPartner2AvailableRangeLayout(String tenantId, boolean needAddApplyToPartner) throws MetadataServiceException {
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayListWithCapacity(4);
        IFieldDescribe fieldDescribe = new UseRangeFieldDescribe();
        fieldDescribe.setApiName("partner_range");
        fieldDescribe.setRequired(Boolean.FALSE);
        fieldDescribeList.add(fieldDescribe);
        if (needAddApplyToPartner) {
            IFieldDescribe booleanFieldDescribe = new BooleanFieldDescribe();
            booleanFieldDescribe.setApiName("apply_to_partner");
            booleanFieldDescribe.setRequired(Boolean.FALSE);
            fieldDescribeList.add(booleanFieldDescribe);
        }
        addField2DetailLayout(tenantId, fieldDescribeList, SFAPreDefineObject.AvailableRange.getApiName());
    }

    public void addChannel2AvailableRangeLayout(String tenantId) throws MetadataServiceException {
        IFieldDescribe fieldDescribe = new BooleanFieldDescribe();
        fieldDescribe.setApiName("apply_to_channel_customer");
        fieldDescribe.setRequired(Boolean.FALSE);
        addField2DetailLayout(tenantId, Lists.newArrayList(fieldDescribe), SFAPreDefineObject.AvailableRange.getApiName());
    }

    private void addField2DetailLayout(String tenantId, List<IFieldDescribe> fieldDescribeList, String objectDescribeAPIName) throws MetadataServiceException {
        if (CollectionUtils.empty(fieldDescribeList)) {
            return;
        }
        List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(objectDescribeAPIName, tenantId);
        if (CollectionUtils.empty(layouts)) {
            return;
        }
        layouts = layouts.stream()
                .filter(x -> ILayout.DETAIL_LAYOUT_TYPE.equals(x.getLayoutType()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(layouts)) {
            return;
        }
        for (ILayout iLayout : layouts) {
            addField2DetailLayout(iLayout, fieldDescribeList);
            layoutService.replace(iLayout);
        }
    }

    private void addField2DetailLayout(ILayout layout, List<IFieldDescribe> toAddFieldList) throws MetadataServiceException {
        List<IFieldSection> refreshedFieldSections = Lists.newArrayList();
        List<IComponent> oldComponents = layout.getComponents();
        List<IComponent> newComponents = Lists.newArrayList();
        oldComponents.forEach(iComponent -> {
            if (iComponent.getName().startsWith("form_component")) {
                FormComponent formComponent = (FormComponent) iComponent;
                for (IFieldSection iFieldSection : formComponent.getFieldSections()) {
                    if (iFieldSection.getName().equals(BASE_FIELD_SECTION_API_NAME)) {
                        List<IFormField> formFields = iFieldSection.getFields();
                        if (CollectionUtils.empty(formFields)) {
                            continue;
                        }
                        List<String> existFieldNames = iFieldSection.getFields().stream()
                                .map(x -> x.getFieldName()).collect(Collectors.toList());
                        for (IFieldDescribe fieldDescribe : toAddFieldList) {
                            String fieldName = fieldDescribe.getApiName();
                            if (existFieldNames.contains(fieldName)) {
                                continue;
                            }
                            IFormField formField = new FormField();
                            formField.setFieldName(fieldDescribe.getApiName());
                            formField.setRenderType(fieldDescribe.getType());
                            formField.setRequired(fieldDescribe.isRequired());
                            formField.setReadOnly(false);
                            formFields.add(formField);
                        }
                        iFieldSection.setFields(formFields);
                    }
                    refreshedFieldSections.add(iFieldSection);
                }
                formComponent.setFieldSections(refreshedFieldSections);
                newComponents.add(formComponent);
            } else {
                newComponents.add(iComponent);
            }
        });
        layout.setComponents(newComponents);
    }


    private void addCustomFieldDescribe(IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribeList) {
        try {
            objectDescribeService.addCustomFieldDescribe(objectDescribe, fieldDescribeList);
        } catch (MetadataServiceException e) {
            log.error("addCustomFieldDescribe error,tenantId {} ", objectDescribe.getTenantId(), e);
        }
    }


    /**
     * copy 自定义对象的分配布局方法
     * reason 下层方法 appId 写死成了 CRM，不能覆写
     */
    public RecordTypeResult saveLayoutAssign(String describeApiName, String json, User user) {
        log.debug("Entering RecordTypeService saveLayoutAssign(tenantId = {}, objectDescribeApiName = {}, json={})",
                user.getTenantId(), describeApiName, json);
        RecordTypeResult result = new RecordTypeResult();
        JSONArray jsonArray = JSONObject.parseArray(json);
        if (null == jsonArray || jsonArray.isEmpty()) {
            result.setSuccess(false);
            return result;
        }

        AddRoleViewModel.Arg arg = new AddRoleViewModel.Arg();
        arg.setAuthContext(user);
        String tenantId = user.getTenantId();
        List<RoleViewPojo> roleViewPojoList = Lists.newArrayList();
        RoleViewPojo roleViewPojo;
        JSONObject object;
        for (int index = 0; index < jsonArray.size(); index++) {
            object = jsonArray.getJSONObject(index);
            String roleCode = object.getString("roleCode");

            JSONArray recordLayout = object.getJSONArray("record_layout");
            String recordTypeApiName;
            String layoutApiName;
            if (null == recordLayout || recordLayout.isEmpty()) {
                continue;
            }

            for (Object obj : recordLayout) {
                JSONObject jsonObject = (JSONObject) obj;
                recordTypeApiName = jsonObject.getString("record_api_name");
                layoutApiName = jsonObject.getString("layout_api_name");
                roleViewPojo = new RoleViewPojo();
                // appId 要读取配置
                roleViewPojo.setAppId(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID));
                roleViewPojo.setEntityId(describeApiName);
                roleViewPojo.setRecordTypeId(recordTypeApiName);
                roleViewPojo.setRoleCode(roleCode);
                roleViewPojo.setTenantId(tenantId);
                roleViewPojo.setViewId(layoutApiName);
                roleViewPojoList.add(roleViewPojo);
            }
        }
        recordTypeLogicService.upsertRoleViewList(user, roleViewPojoList);
        result.setSuccess(true);
        return result;
    }


    /**
     * copy 自定义对象的分配业务类型方法
     * reason 下层方法 appId 写死成了 CRM，不能覆写
     */
    public RecordTypeResult assignRecord(String tenantId, String describeApiName, String json, User user) {
        log.debug("Entering RecordTypeService assignRecord(tenantId = {}, objectDescribeApiName = {}, " +
                "roleListJson={})", tenantId, describeApiName, json);
        RecordTypeResult result = new RecordTypeResult();
        JSONArray roleListJsonArray = JSONObject.parseArray(json);
        if (null == roleListJsonArray || roleListJsonArray.isEmpty()) {
            log.warn("role list is empty, json={}", json);
            return result;
        }

        JSONObject object;
        AddRoleRecordTypeModel.Arg arg = new AddRoleRecordTypeModel.Arg();
        List<RecordTypePojo> recordTypePojoList = Lists.newArrayList();
        for (int index = 0; index < roleListJsonArray.size(); index++) {
            object = roleListJsonArray.getJSONObject(index);
            Object recordTypeObject = object.get("records");
            if (!(recordTypeObject instanceof List)) {
                continue;
            }
            List<String> list = (List) recordTypeObject;
            String roleCode = object.getString("roleCode");
            String defaultRecord = object.getString("default_record");
            for (String apiName : list) {
                RecordTypePojo recordTypePojo = new RecordTypePojo();
                recordTypePojo.setAppId(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID));
                recordTypePojo.setEntityId(describeApiName);
                recordTypePojo.setRecordTypeId(apiName);
                recordTypePojo.setRoleCode(roleCode);
                recordTypePojo.setTenantId(tenantId);
                recordTypePojo.setDefaultType(apiName.equals(defaultRecord));
                recordTypePojoList.add(recordTypePojo);
            }
        }


        arg.setRecordTypePojos(recordTypePojoList);
        arg.setEntityId(describeApiName);
        arg.setAuthContext(user);
        arg.setRecordTypeId("default__c");
        AddRoleRecordTypeModel.Result addRoleRecordTypeResult = recordTypeAuthProxy.addRoleRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        result.setSuccess(addRoleRecordTypeResult.isSuccess());
        return result;

    }

    @ServiceMethod("assignRecordAndLayout")
    public AssignRecordAndLayoutModel.Result assignRecordAndLayout(ServiceContext context, AssignRecordAndLayoutModel.Arg arg) {
        log.warn("PartnerInitService assignRecordAndLayout tenantId->{}", context.getTenantId());
        String layoutName = arg.getApiName() + "_layout_generate_by_UDObjectServer__c";
        String layoutJson = "[{\"roleCode\":\"%s\",\"label\":\"地址管理\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"%s\"}]}]";//ignoreI18n
        layoutJson = String.format(layoutJson, arg.getRoleCode(), layoutName);
        String recordJson = "[{\"default_record\":\"default__c\",\"roleCode\":\"%s\",\"records\":[\"default__c\"]}]";
        recordJson = String.format(recordJson, arg.getRoleCode());
        User user = context.getUser();
        AssignRecordAndLayoutModel.Result result = new AssignRecordAndLayoutModel.Result();
        try {
            assignRecord(context.getTenantId(), arg.getApiName(), recordJson, user);
            saveLayoutAssign(arg.getApiName(), layoutJson, user);
        } catch (Exception e) {
            log.warn("PartnerInitService assignRecordAndLayout error tenantId->{}", context.getTenantId(), e);
            result.setResult(Boolean.FALSE);
            return result;
        }
        result.setResult(Boolean.TRUE);
        return result;
    }

    public void addPartner2PriceBook(String tenantId) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, SFAPreDefineObject.PriceBook.getApiName());
        if (null == objectDescribe) {
            return;
        }
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        if (null == objectDescribe.getFieldDescribe("apply_partner_range")) {
            IFieldDescribe rangeFieldDescribe = new UseRangeFieldDescribe();
            rangeFieldDescribe.fromJsonString(enterpriseInitService.getPriceBookJsonFromFieldName("applypartnerrange"));
            rangeFieldDescribe.setDescribeApiName(SFAPreDefineObject.PriceBook.getApiName());
            fieldDescribeList.add(rangeFieldDescribe);
        }
        addCustomFieldDescribe(objectDescribe, fieldDescribeList);
    }

    public void addPartner2PriceBookLayout(String tenantId) throws MetadataServiceException {
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayListWithCapacity(4);
        IFieldDescribe fieldDescribe = new UseRangeFieldDescribe();
        fieldDescribe.setApiName("apply_partner_range");
        fieldDescribe.setRequired(Boolean.FALSE);
        fieldDescribeList.add(fieldDescribe);
        addField2DetailLayout(tenantId, fieldDescribeList, SFAPreDefineObject.PriceBook.getApiName());
    }

    private Map<String, IObjectDescribe> getPreObjectDescribeMap(User user, List<String> apiNames) {
        Map<String, IObjectDescribe> preDescribeMap = Maps.newHashMap();
        Lists.partition(apiNames, 6).forEach(partitionList -> {
            Map<String, IObjectDescribe> tempDescribeMap = simplifiedChineseService.findByDescribeApiNameList(user, partitionList);
            tempDescribeMap.forEach((objectApiName, describe) -> {
                if (describe != null && !ObjectDescribeExt.of(describe).isSlaveObject()) {
                    preDescribeMap.put(objectApiName, describe);
                }
            });
        });
        return preDescribeMap;
    }

    private void addPartnerField2Describe(User user, IObjectDescribe describe) throws MetadataServiceException {
        String targetRelatedListLabel = describe.getDisplayName();
        String targetRelatedListName = "partner_" + describe.getApiName().toLowerCase().replace("obj", "") + "_list";
        if (SFAPreDefineObject.Contact.getApiName().equals(describe.getApiName())) {
            targetRelatedListName = "partner_" + describe.getApiName().toLowerCase() + "_list";
        }
        ObjectReferenceFieldDescribe partnerFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder()
                .apiName(PARTNER_ID)
                .label("合作伙伴")
                .targetApiName(SFAPreDefine.Partner.getApiName())
                .targetRelatedListLabel(targetRelatedListLabel)
                .targetRelatedListName(targetRelatedListName)
                .unique(false)
                .required(false)
                .build();
        if (!ObjectDescribeExt.of(describe).isSlaveObject()) {
            SupportEnterpriseRelationResult supportEnterpriseRelation = infraServiceFacade.isSupportEnterpriseRelation(user.getTenantId());
            if (supportEnterpriseRelation.isSupportInterconnectBaseAppLicense()) {
                partnerFieldDescribe.set("relation_outer_data_privilege", "outer_owner");
            }
        }
        List<ISelectOption> outResourcesSelectOptions = Lists.newArrayList();
        for (Map.Entry<String, String> map : outResources.entrySet()) {
            outResourcesSelectOptions.add(SelectOptionBuilder.builder().value(map.getKey()).label(map.getValue()).build());
        }
        SelectOneFieldDescribe outResourcesField = SelectOneFieldDescribeBuilder.builder()
                .apiName("out_resources")
                .label("外部来源")
                .required(false)
                .selectOptions(outResourcesSelectOptions)
                .build();
        describeServiceExt.addCustomField(describe, Lists.newArrayList(partnerFieldDescribe, outResourcesField));
    }

    private void addPartnerField2Layout(User user, IObjectDescribe describe) {
        List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList = Lists.newArrayList();
        IFieldDescribe partnerIdField = describe.getFieldDescribe(PARTNER_ID);
        IFieldDescribe outResourcesField = describe.getFieldDescribe(OUT_RESOURCES);
        if (partnerIdField != null) {
            FieldLayoutPojo reference = getFieldLayoutPojo(SystemConstants.RenderType.ObjectReference.renderType, false, false);
            addFieldTupleList.add(Tuple.of(partnerIdField, reference));
        }
        if (outResourcesField != null) {
            FieldLayoutPojo selectOne = getFieldLayoutPojo(SystemConstants.RenderType.SelectOne.renderType, false, false);
            addFieldTupleList.add(Tuple.of(outResourcesField, selectOne));
        }
        log.info("addPartnerField2Layout begin, tenant:{}, objectApiName:{}", user.getTenantId(), describe.getApiName());
        layoutServiceExt.insertFieldsToLayout(user, describe, addFieldTupleList);
        log.info("addPartnerField2Layout end success, tenant:{}, objectApiName:{}", user.getTenantId(), describe.getApiName());
    }

    private FieldLayoutPojo getFieldLayoutPojo(String renderType, boolean readOnly, boolean required) {
        FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
        fieldLayoutPojo.setReadonly(readOnly);
        fieldLayoutPojo.setRequired(required);
        fieldLayoutPojo.setRenderType(renderType);
        return fieldLayoutPojo;
    }

    public void createPartnerSpecialFunc(User user, IObjectDescribe describe) {
        //预置对象刷功能权限，更换合作伙伴、移除合作伙伴
        List<String> actionCodeList = Lists.newArrayList(ObjectAction.CHANGE_PARTNER.getActionCode(), ObjectAction.DELETE_PARTNER.getActionCode(), ObjectAction.CHANGE_PARTNER_OWNER.getActionCode());
        if ("ActiveRecordObj".equals(describe.getApiName())) {
            return;
        }
        functionPrivilegeServiceExt.createFunctionPrivilege(user, describe.getApiName(), actionCodeList);
        Set<String> havePermissionFuncCodes = channelManagerRoleProvider.getHavePermissFuncCodes();
        List<String> objectFuncCodes = havePermissionFuncCodes.stream().filter(o -> o.startsWith(describe.getApiName())).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(objectFuncCodes)) {
            serviceFacade.updatePreDefinedFuncAccess(user, Role.CHANNEL_MANAGER.getRoleCode(), objectFuncCodes, null);
        }
        //  7.2.0 预置线索对象功能权限：转换合作伙伴、转换新建
        if (SFAPreDefineObject.Leads.getApiName().equals(describe.getApiName())) {
            functionPrivilegeServiceExt.createFunctionPrivilege(user, SFAPreDefineObject.Leads.getApiName(), Lists.newArrayList(ObjectAction.TRANSFER_PARTNER.getActionCode()));
            functionPrivilegeServiceExt.createFunctionPrivilege(user, SFAPreDefineObject.Partner.getApiName(), Lists.newArrayList(ObjectAction.TRANSFER_ADD.getActionCode()));
        }
    }

    public String sendNotification(User user, Map<String, ExceptionInfo> failedObjectExceptionMap, List<String> objectApiNames, String source) {
        if (CollectionUtils.empty(failedObjectExceptionMap)) {
            return null;
        }
        List<String> objectList = Lists.newArrayList();
        List<String> objectErrorMsgList = Lists.newArrayList();
        for (Map.Entry<String, ExceptionInfo> entry : failedObjectExceptionMap.entrySet()) {
            String objectDisplayName = entry.getKey();
            ExceptionInfo exceptionInfo = entry.getValue();
            if (exceptionInfo != null && exceptionInfo.getErrorCode() == 201111039) {
                objectList.add(objectDisplayName);
                objectErrorMsgList.add("【" + exceptionInfo.getErrorMessage() + "】");
            }
        }
        if (CollectionUtils.empty(objectList) || CollectionUtils.empty(objectErrorMsgList)) {
            return null;
        }
        Set<Integer> receiverIds = Sets.newHashSet();
        if (NumberUtils.isNumber(user.getUserId())) {
            receiverIds.add(Integer.parseInt(user.getUserId()));
        }
        if (CollectionUtils.empty(receiverIds)) {
            return null;
        }
        NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                .receiverIDs(receiverIds)
                .sourceId(UUID.randomUUID().toString().replace("-", ""))
                .type(92)
                .remindSender(false)
                .build();
        if ("saveObjectToPrm".equals(source)) {
            newCrmNotification.setTitle(I18N.text("sfa.add.object.prm.success.some.field.process.error"));
        } else {
            newCrmNotification.setTitle(I18N.text("sfa.open.partner.success.some.field.process.error"));
        }
        newCrmNotification.setFullContent(String.format(I18N.text("sfa.object.looping.brush.partner.field.error"), Joiner.on("、").join(objectList)) + "，" + Joiner.on("、").join(objectErrorMsgList));
        try {
            crmNotificationService.sendNewCrmNotification(user, newCrmNotification);
        } catch (Exception e) {
            log.error("发送CRM通知失败", e);
        }
        return newCrmNotification.getFullContent();
    }
}
