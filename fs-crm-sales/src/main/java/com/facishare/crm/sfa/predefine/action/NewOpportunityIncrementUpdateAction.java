package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitService;
import com.facishare.crm.sfa.predefine.service.NewOpportunityInitService;
import com.facishare.crm.sfa.task.ForecastTaskProducer;
import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.crm.sfa.utilities.proxy.NewOpportunityProxy;
import com.facishare.crm.sfa.utilities.proxy.model.GetStagesBySourceWorkflowIdModel;
import com.facishare.crm.sfa.utilities.proxy.model.StagesWithProbability;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.rest.core.exception.RestProxyInvokeException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Slf4j
public class NewOpportunityIncrementUpdateAction extends StandardIncrementUpdateAction {
    private final NewOpportunityInitService newOpportunityInitService = SpringUtil.getContext().getBean(NewOpportunityInitService.class);
    private final ForecastTaskProducer forecastTaskProducer = SpringUtil.getContext().getBean(ForecastTaskProducer.class);
    NewOpportunityProxy newOpportunityProxy = SpringUtil.getContext().getBean(NewOpportunityProxy.class);
    private static final ObjectLimitService objectLimitService = SpringUtil.getContext().getBean(ObjectLimitService.class);

    private static final String SALES_STAGE = NewOppportunityConstants.NewOpportunityField.SALES_STAGE.getApiName();
    private static final String SALES_PROCESS_ID = NewOppportunityConstants.NewOpportunityField.SALES_PROCESS_ID.getApiName();
    private boolean isChangeSalesProcess = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        ObjectDataDocument objectDataDocument = arg.getData();
        // 不支持更新这个字段，移除
        objectDataDocument.remove("parent_id");
        RequestContext requestContext = actionContext.getRequestContext();
        String modelName = requestContext.getModelName();
        if (objectDataDocument.containsKey(SALES_PROCESS_ID) && (objectDataDocument.get(SALES_PROCESS_ID) == null || "".equals(objectDataDocument.get(SALES_PROCESS_ID))) && "stage".equals(modelName)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_SALES_PROCESS_CANNOT_BE_EMPTY));
        }
        boolean isFromApprovalFlow = "approvalflow".equals(modelName) || "fs-crm-workflow".equals(requestContext.getPeerName());
        boolean isInChange = ObjectLifeStatus.IN_CHANGE.getCode().equals(dbObjectData.get("life_status"));
        boolean isFromApprovalInChange = isFromApprovalFlow && isInChange;
        // 换流程
        if (!isFromApprovalInChange && dbObjectData.get(SALES_PROCESS_ID) != null && objectData.get(SALES_PROCESS_ID) != null
                && !dbObjectData.get(SALES_PROCESS_ID).equals(objectData.get(SALES_PROCESS_ID))) {
            validate();
            isChangeSalesProcess = true;
        }
        //保证流程不变的情况下，阶段变更
        //阶段变更时修改数据
        if (!isChangeSalesProcess
                && dbObjectData.get(SALES_STAGE) != null && objectData.get(SALES_STAGE) != null
                && !dbObjectData.get(SALES_STAGE).equals(objectData.get(SALES_STAGE))) {
            NewOpportunityUtil.fillModelByChangeSalesStage(actionContext, objectData, dbObjectData);
        }

        checkOwnerShip();
    }

    /**
     * 保有量校验
     */
    private void checkOwnerShip() {
        if (!actionContext.isFromOpenAPI() && objectLimitService.isGrayObjectLimit(actionContext.getTenantId())) {
            IObjectData copyData = ObjectDataExt.of(this.objectData).copy();
            ObjectDataExt.of(copyData).merge(this.dbObjectData);
            List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(Lists.newArrayList(copyData));
            checkLimitDataList.forEach(x -> x.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value));
            List<IObjectData> oldDataList = ObjectDataExt.copyList(Lists.newArrayList(this.dbObjectData));
            String ownerId = CommonBizUtils.getOwner(copyData);
            ObjectLimitService.CheckLimitResult checkLimitResult = objectLimitService.checkObjectLimitForEdit(actionContext.getUser(),
                    actionContext.getObjectApiName(), ownerId, oldDataList, checkLimitDataList, objectDescribe);
            if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                for (String failureId : checkLimitResult.getFailureIds()) {
                    if (!checkLimitResult.getFailureIdAndRuleName().isEmpty() && checkLimitResult.getFailureIdAndRuleName().get(failureId) != null) {
                        throw new ValidateException(String.format(I18N.text(SFA_REACH_RULE_LIMIT_OBJ), checkLimitResult.getFailureIdAndRuleName().get(failureId),
                                I18N.text("NewOpportunityObj.attribute.self.display_name")));
                    }
                }
                throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                        I18N.text("NewOpportunityObj.attribute.self.display_name")));
            }
            String outOwnerId = AccountUtil.getOutOwner(copyData);
            String outTenantId = copyData.getOutTenantId();
            if (StringUtils.isNotBlank(outOwnerId) && StringUtils.isNotBlank(outTenantId)) {
                checkLimitResult = objectLimitService.checkOutUserObjectLimitForEdit(actionContext.getUser(),
                        actionContext.getObjectApiName(), outTenantId, outOwnerId, oldDataList, checkLimitDataList, objectDescribe, true);
                if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                    throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                            I18N.text("NewOpportunityObj.attribute.self.display_name")));
                }
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        //判断是是否更改了销售流程

        if (isChangeSalesProcess) {
            newOpportunityInitService.changeSalesProcess(actionContext.getUser(), objectData);
        }
        forecastTaskProducer.sendMessageWhenNewOpportunityEdit(actionContext.getTenantId(), objectData.getId());
        return result;
    }

    private void validate() {
        //校验当前流程是否适用于当前人
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        headers.put("x-tenant-id",actionContext.getTenantId());
        headers.put("x-user-id", actionContext.getUser().getUpstreamOwnerIdOrUserId());

        GetStagesBySourceWorkflowIdModel.Arg arg = new GetStagesBySourceWorkflowIdModel.Arg();
        arg.setSourceWorkflowId((String) objectData.get(SALES_PROCESS_ID));
        try {
            GetStagesBySourceWorkflowIdModel.Result result = newOpportunityProxy.getStagesBySourceWorkflowId(arg, headers);
            if (!Objects.isNull(objectData.get(SALES_STAGE, String.class))) {
                String stageId = objectData.get(SALES_STAGE, String.class);
                boolean flag = false;
                if (result != null && result.getData() != null && result.getData().getStages() != null) {
                    for (StagesWithProbability data : result.getData().getStages()) {
                        if (stageId.equals(data.getStageId())) {
                            flag = true;
                            break;
                        }
                    }
                }
                if(result != null){
                    log.info("NewOpportunityTriggerModel.Result {}", result.getData());
                    if (!flag) {
                        log.warn("NewOpportunityTriggerModel.Result {}", result);
                        throw new ValidateException(I18N.text(SFA_SALES_PROCESS_AND_STAGE_DO_NOT_MATCH));
                    }
                }
            } else {
                // 商机阶段为空时，置为新流程的初始阶段
                StagesWithProbability firstStage = result.getData().getStages().get(0);
                objectData.set(SALES_STAGE, firstStage.getStageId());
            }
        } catch (RestProxyInvokeException e) {
            log.error("RestProxyInvokeException error {}", arg);
            throw new RestProxyInvokeException(e);
        }
    }
}
