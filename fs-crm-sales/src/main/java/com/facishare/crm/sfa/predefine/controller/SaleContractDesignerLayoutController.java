package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutController;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Objects;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2025-05-20
 */
public class SaleContractDesignerLayoutController extends StandardDesignerLayoutController {
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void processLayout(ILayout layout) {
        super.processLayout(layout);
        if (Objects.isNull(layout)) {
            return;
        }
    }
}
