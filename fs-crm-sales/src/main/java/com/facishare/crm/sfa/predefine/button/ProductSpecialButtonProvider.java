package com.facishare.crm.sfa.predefine.button;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by luxin on 2019-04-09.
 */
@Component
public class ProductSpecialButtonProvider extends AbstractSfaSpecialButtonProvider {
    {
        IButton button = new Button();
        button.setAction(ObjectAction.CONFIGURE_PRODUCT.getActionCode());
        button.setLabel(ObjectAction.CONFIGURE_PRODUCT.getActionLabel());
        button.set("isActive", true);
        button.setName(ObjectAction.CONFIGURE_PRODUCT.getActionCode() + "_button_" + IButton.ACTION_TYPE_DEFAULT);
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
    }

    @Override
    public String getApiName() {
        return Utils.PRODUCT_API_NAME;
    }

    @Override
    public List<IButton> getSpecialButtons() {

        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.AssociateAttribute));
        buttons.add(ButtonUtils.buildButton(ObjectAction.DisAssociateAttribute));
        buttons.add(ButtonUtils.buildButton(ObjectAction.AssociateNonstandardAttribute));
        buttons.add(ButtonUtils.buildButton(ObjectAction.DisAssociateNonstandardAttribute));
        RequestContext context = RequestContextManager.getContext();
        if (context!=null&& StringUtils.isNotBlank(context.getTenantId())) {
            if(!GrayUtil.bomMasterSlaveMode(context.getTenantId())){
                buttons.add(ButtonUtils.buildButton(ObjectAction.CONFIGURE_PRODUCT));
            }
            if (SFAConfigUtil.isSimpleBom(context.getTenantId())) {
                buttons.add(ButtonUtils.buildButton(ObjectAction.CONFIGURE_COLLOCATION));
            }
        }
        buttons.add(ButtonUtils.buildButton(ObjectAction.SET_ATTR_RANGE));
        return buttons;
    }
}
