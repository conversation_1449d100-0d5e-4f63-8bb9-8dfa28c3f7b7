package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.TeamMemberUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.facishare.paas.metadata.support.GrayHelper;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public class AccountUpdateImportVerifyAction extends StandardUpdateImportVerifyAction {

    private List<String> removeFields;

    @Override
    protected void before(BaseImportAction.Arg arg) {
        super.before(arg);
        removeFields = AccountUtil.getImportTemplateRemoveFields(actionContext.getUser(), objectDescribe,AccountConstants.ImportType.UPDATE_IMPORT.getCode());
    }

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fieldDescribes = super.getValidImportFields();
//        removeFields.add(AccountConstants.Field.HIGH_SEAS_ID);
        removeFields.add(AccountConstants.Field.RECORD_TYPE);
        removeFields.add(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID);
        fieldDescribes.removeIf(f -> removeFields.contains(f.getApiName()));
        TeamMemberUtil.addRelevantTeam(fieldDescribes,actionContext);
        if (!CollectionUtils.isEmpty(arg.getRows())) {
            ObjectDataDocument rowData = arg.getRows().get(0);
            if (rowData.containsKey("经度")// ignoreI18n
                    && rowData.containsKey("纬度")// ignoreI18n
                    && (rowData.containsKey("定位（必填）") || rowData.containsKey("定位"))) {// ignoreI18n
                TextFieldDescribe textFieldDescribe = new TextFieldDescribe();
                textFieldDescribe.setApiName("longitude");
                textFieldDescribe.setLabel("经度");// ignoreI18n
                textFieldDescribe.setDescribeApiName("AccountObj");
                textFieldDescribe.setActive(true);
                textFieldDescribe.setStatus("released");
                fieldDescribes.add(textFieldDescribe);
                textFieldDescribe = new TextFieldDescribe();
                textFieldDescribe.setApiName("latitude");
                textFieldDescribe.setLabel("纬度");// ignoreI18n
                textFieldDescribe.setDescribeApiName("AccountObj");
                textFieldDescribe.setActive(true);
                textFieldDescribe.setStatus("released");
                fieldDescribes.add(textFieldDescribe);
            }
        }
        return fieldDescribes;
    }

    @Override
    public List<String> getValidTitles(Set<Map.Entry<String, Object>> entrySet, List<IFieldDescribe> fieldDescribes) {
        AccountUtil.setImportFields(actionContext.getTenantId(), arg.getObjectCode(), fieldDescribes, true);
        return super.getValidTitles(entrySet, fieldDescribes);
    }

}
