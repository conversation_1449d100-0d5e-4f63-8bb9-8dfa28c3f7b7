package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Optional;

public class CouponInstanceWebDetailController extends SFAWebDetailController {
    private static final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    private static final String COUPON_TO_REBATE_DETAIL_JSON = "{\n" +
            "                    \"field_section\": [],\n" +
            "                    \"buttons\": [],\n" +
            "                    \"api_name\": \"related_what_detail\",\n" +
            "                    \"related_list_name\": \"\",\n" +
            "                    \"ref_object_api_name\": \"RebateDetailObj\",\n" +
            "                    \"nameI18nKey\": \"sfa.rebate.RebateDetailObj.display.name\",\n" +
            "                    \"header\": \"优惠券实例使用明细\",\n" +
            "                    \"type\": \"related_what\",\n" +
            "                    \"field_api_name\": \"sourceRelatedObject\",\n" +
            "                    \"order\": 999,\n" +
            "                    \"is_hidden\": false,\n" +
            "                    \"_id\": \"related_what_detail\"" +
            "                }";
    /**
     * 删除action
     */
    private final ImmutableSet<String> removeAction = ImmutableSet.of(ObjectAction.UPDATE.getActionCode(),
            ObjectAction.INVALID.getActionCode());

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        Optional.ofNullable(result).map(Result::getLayout)
                .ifPresent(x -> {
                    ILayout layout = new Layout(x);
                    WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.PRINT.getActionCode()));
                });
        usedInstanceCannotEdit(result);
        issueWhatTab(result);
        return result;
    }

    private void issueWhatTab(Result result) {
        TabSection tabSection = new TabSection();
        tabSection.setApiName("tab_related_what_detail");
        String i18nKey = "CouponInstanceObj.tab_related_what_detail.header";
        tabSection.setHeader(I18N.text(i18nKey));
        tabSection.set("nameI18nKey", i18nKey);
        List<String> whatComponents = Lists.newArrayList("related_what_detail");
        CouponUtils.issueWhatTab(result, tabSection, whatComponents, COUPON_TO_REBATE_DETAIL_JSON);
    }

    /**
     * 使用后的实例无法编辑
     *
     * @param result 结果
     */
    private void usedInstanceCannotEdit(Result result) {
        //已经使用了的优惠券实例
        if (CouponConstants.UseStatus.UNUSED.getValue().equals(data.get(CouponConstants.CouponInstanceField.USE_STATUS.getApiName()))) {
            return;
        }
        Optional.ofNullable(result).map(Result::getLayout)
                .ifPresent(x -> {
                    ILayout layout = new Layout(x);
                    WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(removeAction));
                });
    }

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (RequestUtil.isMobileRequest() ||RequestUtil.isMobileDeviceRequest()|| RequestUtil.isH5Request()) {
            availableRangeUtils.removeMobileButton(layout);
        }
        return layout;
    }
}
