package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.attribute.dao.AttributeDao;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeValue;
import com.facishare.crm.sfa.predefine.service.task.AttributeTaskService;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants.ValueField;
import com.facishare.crm.sfa.utilities.validator.ProductAssociateAttributeValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2017/12/7.
 */
@Slf4j
public class ProductAssociateAttributeAction extends PreDefineAction<ProductAssociateAttributeAction.Arg, ProductAssociateAttributeAction.Result> {
    private static final AttributeDao attributeDao = SpringUtil.getContext().getBean(AttributeDao.class);
    private static final AttributeTaskService attributeTaskService = SpringUtil.getContext().getBean(AttributeTaskService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("AssociateAttribute");
    }

    @Override
    protected List<String> getDataPrivilegeIds(ProductAssociateAttributeAction.Arg arg) {
        return arg.getProductIds();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        ProductAssociateAttributeValidator.validateProductAssociateAttribute(actionContext.getUser(), arg);
    }
    @Override
    protected ProductAssociateAttributeAction.Result doAct(ProductAssociateAttributeAction.Arg arg) {
        List<Model> attributeInfos = arg.getAttributeInfos();
        List<String> attributeIds = attributeInfos.stream().map(Model::getAttributeId).filter(Objects::nonNull).collect(Collectors.toList());
        List<IObjectData> attributes = attributeDao.getAttributesByIds(actionContext.getUser(), attributeIds);
        List<IObjectData> attributeValues = attributeDao.getAttributeValuesByAttributeIds(actionContext.getUser(), attributeIds);
        Map<String, AttributeValue> attributeIdValuesMap = new HashMap<>();

        List<String> finalAttributeIds = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(attributes) && CollectionUtils.isNotEmpty(attributeIds)) {
            List<String> dbAttributeIds = attributes.stream().map(IObjectData::getId).collect(Collectors.toList());
            attributeIds.stream().forEach(attributeId -> {
                if (dbAttributeIds.contains(attributeId)) {
                    finalAttributeIds.add(attributeId);
                }
            });
        }
        Set<String> productIds = Sets.newHashSet();

        List<String> updateFieldList = new ArrayList<>();
        for (IObjectData product : dataList) {
            List<String> relatedAttributeIds = product.get(AttributeConstants.ProductField.ATTRIBUTE_IDS, ArrayList.class);
            // 已经定价关联属性
            List<String> pricingAttributeIds = product.get(AttributeConstants.ProductField.PRICING_ATTRIBUTES, ArrayList.class);
            for (Model attributeInfo : arg.getAttributeInfos()) {
                String attributeId = attributeInfo.getAttributeId();
                Optional<IObjectData> attribute = attributes.stream().filter(x ->
                        x.getId().equals(attributeId)).findFirst();
                Boolean isPricing = attributeInfo.getIsPricing();
                if (attribute.isPresent()) {
                    String fieldNum = attribute.get().get(AttributeConstants.Field.FIELD_NUM, String.class);
                    String attributeFieldNum = AttributeConstants.ProductAttributeField.ATTRIBUTE + fieldNum;
                    List<String> valueIds = getCodeOrderByDefault(attributeInfo.getValueIds(), attributeInfo.getDefaultValueId());
                    // 当前属性参与定价
                    if (isPricing != null && isPricing) {
                        // 已经关联的商品属性值
                        List<String> oldValueCodes = new ArrayList<>();
                        List<String> productValueCodes = product.get(attributeFieldNum, ArrayList.class);
                        if(CollectionUtils.isNotEmpty(productValueCodes)) {
                            oldValueCodes.addAll(product.get(attributeFieldNum, ArrayList.class));
                        }
                        // 当前需要关联属性值
                        List<String> nowValueCodes = attributeInfo.getValueIds();
                        // 取差集
                        oldValueCodes.removeAll(nowValueCodes);
                        if(oldValueCodes.size() > 0) {
                            // 取消属性值的商品
                            productIds.add(product.getId());
                            List<IObjectData> list = attributeValues.stream().filter(x -> x.get(ValueField.ATTRIBUTE_ID,String.class).equals(attributeId)
                                && oldValueCodes.contains(x.getId())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(list)) {
                                for (IObjectData objectData : list) {
                                    String id = objectData.getId();
                                    if (!attributeIdValuesMap.containsKey(id)) {
                                        AttributeValue attributeValue = AttributeValue.builder()
                                                .id(id)
                                                .name(objectData.getName())
                                                .code(objectData.get(ValueField.CODE, String.class))
                                                .attributeId(objectData.get(ValueField.ATTRIBUTE_ID, String.class))
                                                .build();
                                        attributeIdValuesMap.put(id, attributeValue);
                                    }
                                }
                            }
                        }
                    }
                    product.set(attributeFieldNum, valueIds);
                    if (!updateFieldList.contains(attributeFieldNum)) {
                        updateFieldList.add(attributeFieldNum);
                    }
                }
                if (relatedAttributeIds == null) {
                    relatedAttributeIds = new ArrayList<>();
                }
                if (!relatedAttributeIds.contains(attributeId)) {
                    relatedAttributeIds.add(attributeId);
                }
                if (pricingAttributeIds == null) {
                    pricingAttributeIds = new ArrayList<>();
                }
                if (isPricing != null && isPricing) {
                    if (!pricingAttributeIds.contains(attributeId)) {
                        pricingAttributeIds.add(attributeId);
                    }
                } else {
                    pricingAttributeIds.remove(attributeId);
                }
            }
            List<String> finalPricingAttributeIds = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(finalAttributeIds) && CollectionUtils.isNotEmpty(pricingAttributeIds)) {
                for (String attributeId : finalAttributeIds) {
                    if(pricingAttributeIds.contains(attributeId)) {
                        finalPricingAttributeIds.add(attributeId);
                    }
                }
            }

            if (!updateFieldList.contains(AttributeConstants.ProductField.PRICING_ATTRIBUTES)) {
                updateFieldList.add(AttributeConstants.ProductField.PRICING_ATTRIBUTES);
            }
            product.set(AttributeConstants.ProductField.PRICING_ATTRIBUTES, finalPricingAttributeIds);

            if (!updateFieldList.contains(AttributeConstants.ProductField.ATTRIBUTE_IDS)) {
                updateFieldList.add(AttributeConstants.ProductField.ATTRIBUTE_IDS);
            }
            //attribute_id 每次都以前端传过来的为准，直接更新，确保顺序正确
            product.set(AttributeConstants.ProductField.ATTRIBUTE_IDS, finalAttributeIds);
        }
        if (!dataList.isEmpty()) {
            serviceFacade.batchUpdateByFields(actionContext.getUser(), dataList, updateFieldList);
        }
        List<AttributeValue> attributeIdValues = new ArrayList<>(attributeIdValuesMap.values());
        if(CollectionUtils.isNotEmpty(attributeIdValues)) {
            attributeTaskService.delAttributeValueAttributePriceBookTask(actionContext.getTenantId(), attributeIdValues,Lists.newArrayList(productIds));
        }
        return ProductAssociateAttributeAction.Result.builder().errorCode("0").value(true).build();
    }
    @Override
    protected ProductAssociateAttributeAction.Result after(ProductAssociateAttributeAction.Arg arg, ProductAssociateAttributeAction.Result result){
        /*String logContent = String.format("状态，原状态: 禁用 ,被更改为: 启用 状态");
        serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.MODIFY, objectDescribe, dataList, logContent);
        */return super.after(arg, result);
    }

    private List<String> getCodeOrderByDefault(List<String> valueIds, String defaultValueId) {
        if (!valueIds.contains(defaultValueId)) {
//            默认值不在属性值中
            throw new ValidateException(I18N.text("sfa.attributevalue.not.attbutevalue"));
        }
        List<String> result = new ArrayList<>();
        result.add(defaultValueId);
        for (String code : valueIds) {
            if (!code.equals(defaultValueId)) {
                result.add(code);
            }
        }
        return result;
    }
    @Data
    public static class Arg {
        @JsonProperty("product_ids")
        private List<String> productIds;

        @JsonProperty("attribute_infos")
        private List<Model> attributeInfos;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String errorDetail;
        private String errorCode;
        private String message;
        private Boolean value;

        public boolean isSuccess() {
            return "0".equals(errorCode) && value;
        }
    }
    @Data
    public static class Model {
        @JsonProperty("attribute_Id")
        private String attributeId;

        @JsonProperty("value_Ids")
        private List<String> valueIds;

        @JsonProperty("is_pricing")
        private Boolean isPricing;

        @JsonProperty("default_value_id")
        private String defaultValueId;
    }
}
