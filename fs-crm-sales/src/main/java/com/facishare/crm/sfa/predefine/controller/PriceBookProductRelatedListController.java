package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.*;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeRangeService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyCoreService;
import com.facishare.crm.sfa.predefine.service.real.BlljmyGrayService;
import com.facishare.crm.sfa.predefine.service.real.HandleSearchQueryService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants.Field;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants.ProductField;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.DmDefineConstants;
import com.facishare.crm.sfa.utilities.constant.dmConstants.RealPriceConstants;
import com.facishare.crm.sfa.utilities.proxy.model.GetPromotionProductsByAccountIdModel;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISpecifiedTableParameter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.support.GrayHelper;
import com.facishare.paas.metadata.support.SchemaHelper;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.common.SqlEscaper;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.*;
import static com.facishare.paas.appframework.core.model.RequestContext.Android_CLIENT_INFO_PREFIX;
import static com.facishare.paas.appframework.core.model.RequestContext.IOS_CLIENT_INFO_PREFIX;

public class PriceBookProductRelatedListController extends StandardRelatedListController {
    private static Set<String> tenantIdsOrderByName = Sets.newHashSet();
    private final HandleSearchQueryService handleSearchQueryService = SpringUtil.getContext().getBean(HandleSearchQueryService.class);
    private final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    protected final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    private final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private final BlljmyGrayService blljmyGrayService = SpringUtil.getContext().getBean(BlljmyGrayService.class);
    private final SchemaHelper schemaHelper = SpringUtil.getContext().getBean(SchemaHelper.class);
    private final PricePolicyCoreService pricePolicyCoreService = SpringUtil.getContext().getBean(PricePolicyCoreService.class);
    private final ProductCoreService productCoreService = SpringUtil.getContext().getBean(ProductCoreService.class);
    private final UnitCoreService unitCoreService = SpringUtil.getContext().getBean(UnitCoreService.class);
    private final PromotionService promotionService = SpringUtil.getContext().getBean(PromotionService.class);
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitService.class);
    private final ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);
    private final NonstandardAttributeService nonstandardAttributeService = SpringUtil.getContext().getBean(NonstandardAttributeService.class);
    private final GrayHelper grayHelper = SpringUtil.getContext().getBean(GrayHelper.class);
    private final AttributeRangeService attributeRangeService = SpringUtil.getContext().getBean(AttributeRangeService.class);
    private final QuoterCommonController quoterCommonController = SpringUtil.getContext().getBean(QuoterCommonController.class);

    private final AtomicBoolean flag = new AtomicBoolean(false);
    private final AtomicBoolean saleableFlag = new AtomicBoolean(false);
    private boolean isPriceBookSelect = false;
    private boolean isRealLookUp = true;
    private String priceBookId = "";
    protected String accountId = "";
    protected String partnerId = "";
    private String spuId;
    List<GetPromotionProductsByAccountIdModel.WrapProductPromotion> productPromotionList = null;
    Map<String, List<String>> pricePolicyProductListMap = null;
    String masterObjectApiName = StringUtils.EMPTY;

    private boolean canUseQuoter = false;

    private final AttributeCoreService attributeCoreService = SpringUtil.getContext().getBean(AttributeCoreService.class);

    static {
        ConfigFactory.getConfig("fs-crm-java-config", config -> {
            String tenantIdsOrderByNameString = config.get("tenantIdsOrderByName");
            if (!Strings.isNullOrEmpty(tenantIdsOrderByNameString)) {
                tenantIdsOrderByName = Sets.newHashSet(tenantIdsOrderByNameString.split(","));
            }
        });
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery searchTemplateQuery = super.customSearchTemplate(searchQuery);
        customHandleFilters(searchTemplateQuery);
        MultiKeyWordSearchUtil.handleSearchQuery(controllerContext.getUser(), searchTemplateQuery);
        if (CollectionUtils.isNotEmpty(arg.getFilterProductIds())) {
            SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.NIN, Field.PRODUCT_ID.getApiName(), arg.getFilterProductIds());
        }
        if (bizConfigThreadLocalCacheService.isOpenNonStandardProduct(controllerContext.getTenantId()) && !Objects.equals("target_related_list_pricebookproduct",arg.getRelatedListName())) {
            Boolean flag = Optional.ofNullable(arg.getObjectData()).map(x -> MapUtils.getBoolean(x, "not_filter_non_product_flag", false)).orElse(false);
            if(!flag){
                SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.N, Field.PRODUCT_ID.getApiName() + "."+ProductConstants.PRODUCT_TYPE, ProductConstants.ProductTypeEnum.NON_STANDARD.getValue());
            }
        }
        if (StringUtils.isNotBlank(accountId) && (!"--".equals(accountId) || bizConfigThreadLocalCacheService.isLeadsTransferUseAllPriceBook(controllerContext.getTenantId()))) {
            DomainPluginParam pluginParam = infraServiceFacade.findPluginParam(controllerContext.getTenantId(), masterObjectApiName, DmDefineConstants.REAL_PRICE);
            if (Objects.nonNull(pluginParam)) {
                IObjectData sourceData = Objects.isNull(arg.getMasterData()) ? null : arg.getMasterData().toObjectData();
                availableRangeUtils.buildRangeSearchQueryForProduct(controllerContext.getUser(), searchTemplateQuery,
                        arg.getRelatedListName(), accountId, partnerId, "", Field.PRODUCT_ID.getApiName(),
                        stopWatch, sourceData, availableRangeUtils.domainQueryAvailableRangeEnable(arg, true, pluginParam, controllerContext.getTenantId(), RealPriceConstants.DetailField.PRICE_BOOK_PRODUCT_ID));
                availableRangeUtils.buildValidPeriodTieredPriceSearchQuery(controllerContext.getTenantId(), searchTemplateQuery,
                        sourceData, arg.getObjectData());
            }
        }
        if (GrayUtil.isGrayOrderPromotion(controllerContext.getTenantId())) {
            if (PromotionUtil.getIsPromotionEnable(controllerContext.getUser(), controllerContext.getClientInfo())) {
                if (arg.getObjectData() != null && arg.getObjectData().get("account_id") != null) {
                    //叠加促销信息
                    String accountId = arg.getObjectData().get("account_id").toString();
                    productPromotionList = promotionService.getProductPromotionList(controllerContext.getTenantId(), controllerContext.getUser(), accountId);
                    if (Objects.equals(arg.getObjectData().get("is_promotion_list"), true)) {
                        promotionService.handPromotionQuery(productPromotionList, searchTemplateQuery, "product_id");
                    }
                }
            }
        }
        //叠加价格政策信息
        String masterDescribeApiName = Optional.ofNullable(arg)
                .map(Arg::getMasterData)
                .map(data -> data.get("object_describe_api_name"))
                .map(Object::toString).orElse("");
        if (bizConfigThreadLocalCacheService.isOpenPricePolicy(controllerContext.getTenantId(), masterDescribeApiName)) {
            if (arg.getMasterData() != null && !Objects.isNull(arg.getMasterData().get("object_describe_api_name"))) {
                masterObjectApiName = arg.getMasterData().get("object_describe_api_name").toString();
                pricePolicyProductListMap = pricePolicyCoreService.GetPricePolicyProductsByAccountId(controllerContext.getUser(), masterObjectApiName, accountId);
                if (arg.getObjectData().get("tab_price_policy_id") != null) {
                    String pricePolicyId = arg.getObjectData().get("tab_price_policy_id").toString();
                    List<String> pricePolicyProductIds = new ArrayList<>();
                    if (!Objects.isNull(pricePolicyProductListMap)) {
                        pricePolicyProductIds = pricePolicyProductListMap.get(pricePolicyId);
                    }
                    if (CollectionUtils.isNotEmpty(pricePolicyProductIds)) {
                        if (!pricePolicyProductIds.contains(PricePolicyConstants.ALL_PRODUCT)) {
                            com.facishare.crm.sfa.utilities.common.convert
                                    .SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), "product_id", pricePolicyProductIds);
                        }
                    } else {
                        com.facishare.crm.sfa.utilities.common.convert
                                .SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), "product_id", Lists.newArrayList("null"));
                    }
                }
            }
        }

        return searchTemplateQuery;
    }

    @Override
    protected void before(Arg arg) {
        log.warn("arg for test: {}", arg);
        isRealLookUp = arg.getObjectData() == null || !Objects.equals(arg.getObjectData().get("is_real_lookup"), false);
        super.before(arg);
        if (arg.getMasterData() != null && arg.getMasterData().get("object_describe_api_name") != null) {
            masterObjectApiName = arg.getMasterData().get("object_describe_api_name").toString();
        }
        //是否灰度了报价器功能
        canUseQuoter = Boolean.TRUE.equals(GrayUtil.isGrayEnable(controllerContext.getTenantId(), GrayUtil.QUOTER_OPEN_TENANT_ID));
        if(canUseQuoter && quoterCommonController.isFromQuoter(arg.getExtraData())) {
            //处理从报价器来的请求参数
            SearchTemplateQuery tempQuery = quoterCommonController.processAttrFilter(arg.getExtraData(), Field.PRODUCT_ID.getApiName() + ".", Field.PRODUCT_ID.getApiName() + "." + ProductConstants.NONSTANDARD_ATTRIBUTE_IDS, Boolean.TRUE);
            SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery)SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());

            List<Wheres> wheres = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(tempQuery.getFilters())) {
                Wheres where2 = new Wheres();
                where2.setConnector(Where.CONN.OR.toString());
                where2.setFilters(tempQuery.getFilters());
                wheres.add(where2);
            }

            List<String> productIds = quoterCommonController.findProductsByAttr(controllerContext.getUser(), arg.getExtraData());
            if(CollectionUtils.isNotEmpty(productIds)) {
                List<IFilter> filters = Lists.newArrayList();
                IFilter productIdFilter = new Filter();
                productIdFilter.setFieldName(Field.PRODUCT_ID.getApiName());
                productIdFilter.setOperator(Operator.IN);
                productIdFilter.setFieldValues(productIds);
                filters.add(productIdFilter);

                Wheres where = new Wheres();
                where.setConnector(Where.CONN.OR.toString());
                where.setFilters(filters);
                wheres.add(where);
            }
            searchTemplateQuery.setWheres(wheres);
            arg.setSearchQueryInfo(JsonUtil.toJson(searchTemplateQuery));
        }
    }

    @Override
    protected void init() {
        ObjectDataDocument objectData = arg.getObjectData();
        if (objectData != null && objectData.get("pricebook_id") != null) {
            priceBookId = objectData.get("pricebook_id").toString();
        }
        if (arg.getObjectData() != null && arg.getObjectData().containsKey("account_id")) {
            accountId = Optional.ofNullable(arg.getObjectData().get("account_id")).orElse("").toString();
            partnerId = Optional.ofNullable(arg.getObjectData().get("partner_id")).orElse("").toString();
        }

        if (DhtUtil.isDhtOrAccessoriesMallRequest(controllerContext.getPeerName(), controllerContext.getAppId())) {
            accountId = AccountUtil.getAccountIdByOutTenantId(controllerContext.getTenantId(), controllerContext.getUser().getOutTenantId(), controllerContext.getUser().getOutUserId());
            if (arg.getObjectData() != null) {
                arg.getObjectData().put("account_id", accountId);
                partnerId = Optional.ofNullable(arg.getObjectData().get("partner_id")).orElse("").toString();
            }
        }
        if (!isRealLookUp) {
            // 对数据做特殊处理
            arg.setTargetObjectApiName(Utils.PRICE_BOOK_API_NAME);
            spuId = arg.getTargetObjectDataId();

            if (StringUtils.isBlank(priceBookId) || spuId == null) {
                arg.setSearchTemplateId(null);
            } else {
                arg.setTargetObjectDataId(priceBookId);
            }
        }
        super.init();
    }

    @Override
    protected void doFunPrivilegeCheck() {
        verifyIsPriceBookSelect();
        if (isPriceBookSelect || !isRealLookUp) {
            return;
        }
        super.doFunPrivilegeCheck();
    }


    /**
     * 1、支持产品分类树的特殊查询
     * 2、如果是价目表关联价目表产品时，过滤数据权限
     */
    protected void customHandleFilters(SearchTemplateQuery query) {
        stopWatch.lap("pricebook refObjEach handleFilters start");
        log.debug("PPPTV-PriceBookProductRelatedListController.handleFilters start");
        super.handleFilters(query);
        if (!Objects.equals(arg.getRelatedListName(), "target_related_list_pricebookproduct")
                && !GrayUtil.supportScene4RelatedList(controllerContext.getTenantId()) && (isPriceBookSelect || !isRealLookUp)) {
            query.setPermissionType(0);
        }
        //订单选择产品，开启价目表的情况下，查询价目表明细列表要过滤掉产品生命状态为作废的数据。
        if (isRealLookUp && isPriceBookSelect) {
            addProductLifeStatusFilter(query);
        }
        addPriceBookIdFilter(query);
        productCategoryBizService.handleNewCategoryListFilters(controllerContext.getUser(), arg.getSearchQueryInfo(), query, PRODUCT_CATEGORY, SHOP_CATEGORY);
        productCategoryBizService.oldCategoryTransferNewCategoryListFilters(controllerContext.getUser(), query, PRODUCT_CATEGORY);
        log.debug("PPPTV-PriceBookProductRelatedListController.handleFilters end");
    }

    @Override
    protected ILayout findLayout() {
        //产品详情页下删掉价目表页签的的新建关联页签
        return ButtonUtils.removeButtons(Lists.newArrayList(Utils.PRODUCT_API_NAME), arg.getTargetObjectApiName(), super.findLayout());
    }

    private void addProductLifeStatusFilter(SearchTemplateQuery query) {
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterEq(filters, "product_life_status", "normal");
    }

    private void addPriceBookIdFilter(SearchTemplateQuery query) {
        if (arg.getObjectData() != null && arg.getObjectData().get("pricebook_id") != null && StringUtils.isNotBlank(arg.getObjectData().get("pricebook_id").toString())) {
            List<IFilter> filters = query.getFilters();
            SearchUtil.fillFilterEq(filters, "pricebook_id", arg.getObjectData().get("pricebook_id"));
        }
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        //Start 价目表明细列表 安卓与ios默认按照产品名称排序
        String tenantId = controllerContext.getTenantId();
        String clientInfo = controllerContext.getRequestContext().getClientInfo();
        List<OrderBy> orderBys = query.getOrders();
        if (clientInfo != null && tenantIdsOrderByName.contains(tenantId) &&
                (clientInfo.startsWith(Android_CLIENT_INFO_PREFIX) || clientInfo.startsWith(IOS_CLIENT_INFO_PREFIX))) {
            orderBys.clear();

            OrderBy orderBy = new OrderBy("product_name", true);
            orderBy.setIndexName(null);
            orderBy.setReference(false);
            orderBys.add(orderBy);
        }
        // web端中的产品排序是按照id排序的 现在改成根据name排序
        orderBys.forEach(o -> {
            if ("product_id".equals(o.getFieldName())) {
                o.setFieldName("product_name");
            }
        });

        QueryResult<IObjectData> queryResult = super.getQueryResult(query);

        // 如果为选择spu，字段不转换
        log.debug("PPPTV-PriceBookProductRelatedListController.getQueryResult start");
        if (isRealLookUp) {
            //特殊给订单选择价目表产品时，填充产品数据
            if (isPriceBookSelect) {
                productCoreService.fillDataWithProduct(controllerContext.getUser(), queryResult.getData(), isRealLookUp, masterObjectApiName);
                log.debug("PPPTV-PriceBookProductRelatedListController.getQueryResult 1");
            }
        } else {
            productCoreService.fillDataWithProduct(controllerContext.getUser(), queryResult.getData(), isRealLookUp, masterObjectApiName);
            log.debug("PPPTV-PriceBookProductRelatedListController.getQueryResult 2");
        }
        //super.asyncFillFieldInfo(objectDescribe, queryResult.getData());
        log.debug("PPPTV-PriceBookProductRelatedListController.getQueryResult end");
        //叠加价格政策信息
        if (bizConfigThreadLocalCacheService.isOpenPricePolicy(controllerContext.getTenantId(), masterObjectApiName)) {
            pricePolicyCoreService.HasPricePolicyForProduct(controllerContext.getUser(), queryResult.getData(), pricePolicyProductListMap);
        }
        return queryResult;
    }


    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        int time = 0;
        QueryResult<IObjectData> finalResult;

        do {
            time++;
            finalResult = getObjectDataQueryResult(query);
        } while (blljmyGrayService.checkNeedSearchAgainAndHandleFieldValues(controllerContext.getTenantId(), query, finalResult) && time <= 1);
        //todo plugin 需检查
        attributeCoreService.attachAttributeData(controllerContext.getUser()
                , finalResult.getData()
                , ProductField.PRODUCTID.getApiName());
        nonstandardAttributeService.attachNonstandardAttributeData(controllerContext.getUser()
                , Lists.newArrayList(finalResult.getData()), true);

        return finalResult;
    }

    private QueryResult<IObjectData> getObjectDataQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> finalResult;
        log.debug("PPPTV-PriceBookProductRelatedListController.getObjectDataQueryResult start");
        if (isRealLookUp) {
            finalResult = super.findData(query);
            log.debug("PPPTV-PriceBookProductRelatedListController.getObjectDataQueryResult 1");
        } else {
            boolean isSpecialSchema = schemaHelper.isSpecialSchema(controllerContext.getTenantId());
            String priceBookFilterSqlPart = getFiltersAndWheresSqlPart(objectDescribe, query);
            String orgDepStringPart = getOrgDepartmentSqlPart(priceBookFilterSqlPart);
            String validPeriodTieredPriceSqlPart = getValidPeriodTieredPriceSqlPart(query);
            String replaceValue = getValue2Replace();
            Optional<Map> totalCountInfo = getTotalCount(priceBookFilterSqlPart, orgDepStringPart, validPeriodTieredPriceSqlPart, replaceValue, isSpecialSchema);
            log.debug("PPPTV-PriceBookProductRelatedListController.getObjectDataQueryResult 2");
            if (totalCountInfo.isPresent() && totalCountInfo.get().get("count") != null) {
                List<IObjectData> priceBookProductDataList = getPriceBookProductDataList(query, priceBookFilterSqlPart,
                        orgDepStringPart, validPeriodTieredPriceSqlPart, replaceValue, isSpecialSchema);
                log.debug("PPPTV-PriceBookProductRelatedListController.getObjectDataQueryResult 3");
                QueryResult<IObjectData> result = new QueryResult<>();
                result.setData(priceBookProductDataList);
                result.setTotalNumber(Integer.valueOf(totalCountInfo.get().get("count").toString()));
                finalResult = result;
            } else {
                QueryResult<IObjectData> result = new QueryResult<>();
                result.setData(Lists.newArrayListWithCapacity(0));
                finalResult = result;
            }
        }
        return finalResult;
    }

    /**
     * 获取人员部门筛选条件部分的 sql
     *
     * @param priceBookFilterSqlPart
     * @return 如果没有人员部门的筛选条件, 返回空串
     */
    private String getOrgDepartmentSqlPart(String priceBookFilterSqlPart) {
        boolean haveOrgDetSqlPart = ConcatenateSqlUtils.isHaveOrgDetSqlPart(priceBookFilterSqlPart);

        String orgDepStringPart;
        if (haveOrgDetSqlPart) {
            String orgTableName = ConcatenateSqlUtils.getOrgTableName(priceBookFilterSqlPart);
            orgDepStringPart = String.format("left join org_dept_user as %s on %s.tenant_id = PriceBookProductObj.tenant_id", orgTableName, orgTableName);
        } else {
            orgDepStringPart = "";
        }
        return orgDepStringPart;
    }

    @Nullable
    private String getFiltersAndWheresSqlPart(ObjectDescribeExt describeExt, SearchTemplateQuery query) {
        List<List<IFilter>> whereFilters = query.getWheres().stream()
                .filter(w -> com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(w.getFilters()))
                .filter(w -> {
                            if (w.getFilters().size() == 1 && "product_status".equals(w.getFilters().get(0).getFieldName())) {
                                flag.set(true);
                                return false;
                            } else {
                                w.getFilters().removeIf(f -> {
                                    if ("product_status".equals(f.getFieldName())) {
                                        flag.set(true);
                                        return true;
                                    } else if ("is_saleable".equals(f.getFieldName())) {
                                        saleableFlag.set(true);
                                        return true;
                                    } else {
                                        return false;
                                    }
                                });
                                return true;
                            }
                        }
                ).map(Wheres::getFilters).collect(Collectors.toList());

        // TODO 价目表产品特殊逻辑
        whereFilters.removeIf(o -> {

            for (IFilter filter : o) {
                if (o.size() == 1 && "product_status".equals(filter.getFieldName())) {
                    query.getFilters().add(filter);
                    return true;
                }
            }
            return false;
        });

        String wheresSqlPart = handleSearchQueryService.getWheresSqlPart(describeExt, "PriceBookProductObj", whereFilters);

        String priceBookFilterSqlPart = handleSearchQueryService.getFilterSqlPart(describeExt,
                query.getFilters().stream().filter(o -> !"object_describe_api_name".equals(o.getFieldName())).collect(Collectors.toList()), "PriceBookProductObj");

        if (StringUtils.isNotBlank(priceBookFilterSqlPart) && StringUtils.isNotBlank(wheresSqlPart)) {
            priceBookFilterSqlPart = priceBookFilterSqlPart + wheresSqlPart;
        }

        if (!StringUtils.isNotBlank(priceBookFilterSqlPart) && StringUtils.isNotBlank(wheresSqlPart)) {
            priceBookFilterSqlPart = wheresSqlPart;
        }
        return priceBookFilterSqlPart;
    }


    @Override
    protected Result after(Arg arg, Result result) {
        Result result1 = super.after(arg, result);
        nonstandardAttributeService.fillNonstandardAttributeDefaultValue(controllerContext.getTenantId(), result1.getDataList(), true);
        //将非标属性的值重新赋值到产品行上、重置默认选中的属性值
        if (canUseQuoter && quoterCommonController.isFromQuoter(arg.getExtraData()) && CollectionUtils.isNotEmpty(result1.getDataList())) {
            quoterCommonController.resetDefaultAttributeValue(result1.getDataList(), arg.getExtraData());
        }
        LayoutUtils.removeListLayoutUpdateTypeButtons4MobileOrH5(result1.getListLayouts());
        if (multiUnitService.isOpenMultiUnit(controllerContext.getTenantId())) {
            unitCoreService.fillMultipleUnit(controllerContext.getUser(), result1.getDataList());
            unitCoreService.handCommonUnitInfo(controllerContext.getTenantId()
                    , ObjectDataDocument.ofDataList(result1.getDataList())
                    , Utils.PRICE_BOOK_PRODUCT_API_NAME, masterObjectApiName);
        }
        // 叠加促销信息
        if (GrayUtil.isGrayOrderPromotion(controllerContext.getTenantId())) {
            if (productPromotionList != null && !productPromotionList.isEmpty()) {
                promotionService.fillPromotionInfo(controllerContext.getUser(), result1.getDataList(), productPromotionList);
            }
        }

        //属性范围
        attributeRangeService.queryAttrRange(controllerContext.getUser(),ObjectDataDocument.ofDataList(result1.getDataList()),Utils.PRICE_BOOK_PRODUCT_API_NAME);

//        fillVirtualField();
        productCategoryBizService.handleCategoryName(controllerContext.getUser(), result1, PRODUCT_CATEGORY__V, PRODUCT_CATEGORY);
        return result1;
    }


    /**
     * 补充虚拟字段的值
     */
    private void fillVirtualField() {
        if (Strings.isNullOrEmpty(accountId) || CollectionUtils.isEmpty(result.getDataList())) {
            return;
        }

        //虚拟字段开关
        boolean isVirtualExtensionEnabled = bizConfigThreadLocalCacheService.isVirtualExtensionEnabled(controllerContext.getTenantId());
        log.info("isListPage[{}], isVirtualExtensionEnabled[{}]", isListPage(), isVirtualExtensionEnabled);
        if (!isListPage() || !isVirtualExtensionEnabled) {
            return;
        }

        //不需要返回库存信息
        if (!Objects.equals(arg.getRelatedListName(), "pricebookproduct_salesorderproduct_list")) {
            return;
        }
        if (arg.getMasterData() == null || arg.getMasterData().get("object_describe_api_name") == null) {
            return;
        }
        String masterObjectApiName = arg.getMasterData().get("object_describe_api_name").toString();
        if (!Objects.equals(masterObjectApiName, "SalesOrderObj")) {
            return;
        }

        String shippingWarehouseId = arg.getObjectData() == null ? null : (String) arg.getObjectData().get("shipping_warehouse_id");
        boolean isDhtMultiLevelOrder = bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(controllerContext.getTenantId());
        //开启渠道多级订货且record_type=distribution__c，取库存才按partnerId查
        String recordType = "";
        if (arg.getMasterData() != null) {
            recordType = Optional.ofNullable(arg.getMasterData().get(SystemConstants.Field.RecordType.apiName)).orElse("").toString();
        }
        StockUtil.fillVirtualStockField(controllerContext.getUser(), result.getDataList(), shippingWarehouseId, accountId, partnerId, recordType);
    }

    private String getValue2Replace() {
        return grayHelper.fixTargetTableAlias(controllerContext.getTenantId())
                ? "PriceBookProductObj_ProductObj_product_id"
                : "ProductObj_product_id";
    }

    @NotNull
    private Optional<Map> getTotalCount(String priceBookFilterSqlPart, String orgDepStringPart, String validPeriodTieredPriceSqlPart,
                                        String value2Replace, boolean isSpecialSchema) {
        //language=GenericSQL
        String countSql = "select count(distinct id) from (select PriceBookProductObj.id\n" +
                "from price_book_product as PriceBookProductObj  %s\n" +
                "left join mt_data as ext on (PriceBookProductObj.extend_obj_data_id = ext.id and PriceBookProductObj.tenant_id = ext.tenant_id)\n" +
                " left join biz_product as k on (k.tenant_id = PriceBookProductObj.tenant_id and k.id = PriceBookProductObj.product_id)\n" +
                "where  k.spu_id = '%s' \n" +
                "and k.tenant_id = '%s'\n" +
                "and k.id = PriceBookProductObj.product_id \n" +
                "and k.is_deleted=0 \n" +
                "and PriceBookProductObj.is_deleted=0 \n" +
                "and k.life_status='normal'\n" +
                (flag.get() ? " and k.product_status='1'" : "\n") +
                (saleableFlag.get() ? " and k.is_saleable=true " : "\n") +
                "and PriceBookProductObj.life_status='normal' and PriceBookProductObj.tenant_id = '%s' %s %s) as count_result;";
        String schemaCountSql = "select count(distinct id) from (select PriceBookProductObj.id\n" +
                "from price_book_product as PriceBookProductObj  %s\n" +
                " inner join biz_product as k on (k.tenant_id = PriceBookProductObj.tenant_id and k.id = PriceBookProductObj.product_id)\n" +
                "where  k.spu_id = '%s' \n" +
                "and k.tenant_id = '%s'\n" +
                "and k.id = PriceBookProductObj.product_id \n" +
                "and k.is_deleted=0 \n" +
                "and PriceBookProductObj.is_deleted=0 \n" +
                "and k.life_status='normal'\n" +
                (flag.get() ? "and k.product_status='1' " : "\n") +
                (saleableFlag.get() ? " and k.is_saleable=true " : "\n") +
                "and PriceBookProductObj.life_status='normal' and PriceBookProductObj.tenant_id = '%s' %s %s) as count_result;";

        String querySql = String.format(
                isSpecialSchema ? schemaCountSql : countSql,
                orgDepStringPart,
                SqlEscaper.pg_escape(spuId),
                SqlEscaper.pg_escape(controllerContext.getTenantId()),
                SqlEscaper.pg_escape(controllerContext.getTenantId()),
                priceBookFilterSqlPart,
                validPeriodTieredPriceSqlPart
        );
        try {
            querySql = querySql.replaceAll(value2Replace, "k");
            log.debug("PPPTV-PriceBookProductRelatedListController.getTotalCount sql:{}", querySql);
            List<Map> countInfo = objectDataService.findBySql(controllerContext.getTenantId(), querySql);
            return countInfo.stream().findFirst();
        } catch (MetadataServiceException e) {
            throw new RuntimeException();
        }
    }

    private List<IObjectData> getPriceBookProductDataList(SearchTemplateQuery query, String priceBookFilterSqlPart,
                                                          String orgDepStringPart, String validPeriodTieredPriceSqlPart,
                                                          String value2Replace, boolean isSpecialSchema) {
        // TODO 优化 sql
        String dataSql = "select PriceBookProductObj.id as _id\n" +
                "from price_book_product as PriceBookProductObj %s\n" +
                "left join mt_data as ext on (PriceBookProductObj.extend_obj_data_id = ext.id and PriceBookProductObj.tenant_id = ext.tenant_id),biz_product as k  \n" +
                "where k.spu_id = '%s' \n" +
                "and k.tenant_id = '%s'\n" +
                "and k.id = PriceBookProductObj.product_id \n" +
                "and k.is_deleted=0 \n" +
                "and PriceBookProductObj.is_deleted=0 \n" +
                "and k.life_status='normal'\n" +
                (flag.get() ? "and k.product_status='1' \n" : "") +
                (saleableFlag.get() ? "and k.is_saleable=true \n" : "") +
                "and PriceBookProductObj.life_status='normal' and PriceBookProductObj.tenant_id = '%s' %s %s %s %s;";
        String schemaDataSql = "select PriceBookProductObj.id as _id\n" +
                "from price_book_product as PriceBookProductObj %s\n" +
                "inner join biz_product as k  \n" +
                "on k.tenant_id = PriceBookProductObj.tenant_id and k.id = PriceBookProductObj.product_id \n" +
                "where k.spu_id = '%s' \n" +
                "and k.tenant_id = '%s'\n" +
                "and k.is_deleted=0 \n" +
                "and PriceBookProductObj.is_deleted=0 \n" +
                "and k.life_status='normal'\n" +
                (flag.get() ? "and k.product_status='1' \n" : "") +
                (saleableFlag.get() ? "and k.is_saleable=true \n" : "") +
                "and PriceBookProductObj.life_status='normal' and PriceBookProductObj.tenant_id = '%s' %s %s %s %s;";
        String querySql = String.format(isSpecialSchema ? schemaDataSql : dataSql, orgDepStringPart, SqlEscaper.pg_escape(spuId), SqlEscaper.pg_escape(controllerContext.getTenantId()),
                SqlEscaper.pg_escape(controllerContext.getTenantId()), priceBookFilterSqlPart, validPeriodTieredPriceSqlPart,
                ConcatenateSqlUtils.getOrderPart(query.getOrders(), "PriceBookProductObj", isSpecialSchema ? "PriceBookProductObj" : "ext", objectDescribe),
                ConcatenateSqlUtils.getPagePart(query));
        try {
            log.debug("PPPTV-PriceBookProductRelatedListController.getPriceBookProductDataList start");
            // TODO 可以单独打日志到目标文件
            querySql = querySql.replaceAll(value2Replace, "k");
            log.debug("PPPTV-PriceBookProductRelatedListController.getPriceBookProductDataList sql:{}", querySql);
            List<Map> priceBookProductDataList = objectDataService.findBySql(controllerContext.getTenantId(), querySql);
            log.debug("PPPTV-PriceBookProductRelatedListController.getPriceBookProductDataList 1");
            List<String> priceBookProductIds = priceBookProductDataList.stream().map(o -> o.get(DBRecord.ID).toString()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(priceBookProductIds)) {
                return Lists.newArrayList();
            }
            SearchTemplateQuery priceBookProductQuery = new SearchTemplateQuery();
            priceBookProductQuery.setPermissionType(0);
            priceBookProductQuery.setLimit(2000);
            SearchTemplateQueryExt.of(priceBookProductQuery).addFilter(Operator.IN, DBRecord.ID, priceBookProductIds);
            if (CollectionUtils.isEmpty(query.getOrders())) {
                query.getOrders().add(SearchUtil.orderByLastModifiedTime());
            }
            SearchTemplateQueryExt.of(priceBookProductQuery).setOrders(query.getOrders());
            return serviceFacade.findBySearchQuery(controllerContext.getUser(), Utils.PRICE_BOOK_PRODUCT_API_NAME, priceBookProductQuery).getData();
        } catch (MetadataServiceException e) {
            throw new RuntimeException();
        }
    }

    /**
     * 判断入口是价目表时
     * 目的是跳过数据权限，只能销售订单选择价目表产品的时候选择，在其他地方不让看到
     */
    private void verifyIsPriceBookSelect() {
        //终端会传递价目表的apiname，web端会在object_data中添加pricebook_id字段标识是从价目表选择
        if (isRealLookUp && (Utils.PRICE_BOOK_API_NAME.equals(arg.getTargetObjectApiName()) || (arg.getObjectData() != null && arg.getObjectData().get("pricebook_id") != null))) {
            isPriceBookSelect = true;
        }
    }

    private String getValidPeriodTieredPriceSqlPart(SearchTemplateQuery query) {
        ISpecifiedTableParameter specifiedTableParameter = query.getSpecifiedTableParameter();
        if (specifiedTableParameter == null) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        String parameterTableName = specifiedTableParameter.getTableName();
        if (StringUtils.isNotEmpty(parameterTableName)) {
            stringBuilder.append(" AND EXISTS ").append(parameterTableName.replace(") AS newTable", "")).append(" AND PriceBookProductObj.id = A.id) ");
        }
        return stringBuilder.toString();
    }
}
