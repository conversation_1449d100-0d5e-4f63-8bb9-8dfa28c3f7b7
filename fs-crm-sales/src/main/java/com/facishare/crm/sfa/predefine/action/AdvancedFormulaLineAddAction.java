package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.AdvancedFormulaService;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class AdvancedFormulaLineAddAction extends StandardAddAction {
    private final AdvancedFormulaService advancedFormulaService = SpringUtil.getContext().getBean(AdvancedFormulaService.class);
    private static final String REF_OBJECT_API_NAME = "ref_object_api_name";
    private static final String REF_FIELD_NAME = "ref_field_name";
    private static final String ADVANCED_FORMULA_ID = "advanced_formula_id";


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String masterId = objectData.get(ADVANCED_FORMULA_ID, String.class);
        if (StringUtils.isBlank(masterId)) {
            return;
        }
        IObjectData masterData = serviceFacade.findObjectDataIgnoreAll(actionContext.getUser(), masterId, Utils.ADVANCED_FORMULA_API_NAME);
        if (Objects.isNull(masterData)) {
            return;
        }
        String bomId = objectData.get(BomConstants.FIELD_BOM_ID, String.class);
        if (StringUtils.isNotBlank(bomId)) {
            IObjectData bomData = serviceFacade.findObjectDataIgnoreAll(actionContext.getUser(), bomId, Utils.BOM_API_NAME);
            if (Objects.nonNull(bomData)) {
                objectData.set(BomConstants.FIELD_PRODUCT_ID, bomData.get(BomConstants.FIELD_PRODUCT_ID));
            }
        }
        objectData.set(REF_OBJECT_API_NAME, masterData.get(REF_OBJECT_API_NAME));
        objectData.set(REF_FIELD_NAME, masterData.get(REF_FIELD_NAME));
        Map<String, List<IObjectData>> detailMap = Maps.newHashMap();
        detailMap.put(Utils.ADVANCED_FORMULA_LINE_API_NAME, Lists.newArrayList(objectData));
        advancedFormulaService.checkRepeat(masterData, detailMap, actionContext.getUser());
    }
}
