package com.facishare.crm.sfa.predefine.button;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BomCoreSpecialButtonProvider extends AbstractSfaSpecialButtonProvider {
    @Override
    public String getApiName() {
        return Utils.BOM_CORE_API_NAME;
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.SET_ATTR_RANGE));
        buttons.add(ButtonUtils.buildButton(ObjectAction.BOM_CORE_CONFIG_OCR));

        return buttons;
    }

}

