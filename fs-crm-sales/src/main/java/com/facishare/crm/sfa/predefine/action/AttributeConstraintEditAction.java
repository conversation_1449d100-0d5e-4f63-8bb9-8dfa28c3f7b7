package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.quoter.AttributeConstraintService;
import com.facishare.crm.sfa.predefine.service.quoter.model.AttributeConstraintModel;
import com.facishare.crm.sfa.utilities.constant.AttributeConstaintConstants;
import com.facishare.crm.sfa.utilities.constant.AttributeConstaintLinesConstants;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2023-12-17
 */
public class AttributeConstraintEditAction extends StandardEditAction {

    private final AttributeConstraintService attributeConstraintService = SpringUtil.getContext().getBean(AttributeConstraintService.class);


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //将attribute_json数据转成明细列表
        String attributeJson = objectData.get(AttributeConstaintConstants.FIELD_ATTRIBUTE_JSON, String.class);

        if(StringUtils.isNotBlank(attributeJson)) {
            List<AttributeConstraintModel.AttributeConstraintNode> detailList = JsonUtil.fromJsonByJackson(attributeJson, new TypeReference<List<AttributeConstraintModel.AttributeConstraintNode>>(){});
            List<IObjectData> detailDatas = attributeConstraintService.tree2List(detailList, objectData.getId());
            detailDatas.parallelStream().forEach(e -> {
                e.setCreatedBy(objectData.getCreatedBy());
                e.setTenantId(actionContext.getTenantId());
                e.setOwner(objectData.getOwner());
                e.setDataOwnDepartment(objectData.getDataOwnDepartment());
            });
            if(detailObjectData == null) {
                detailObjectData = Maps.newHashMap();
            }
            detailObjectData.put(AttributeConstaintLinesConstants.DESC_API_NAME, detailDatas);
            detailsToAdd = detailDatas;
            attributeConstraintService.check(detailDatas, actionContext.getUser());
            List<IObjectData> oldDetails = attributeConstraintService.getAttributeConstraintLinesByAttributeConstraintId(actionContext.getUser(), objectData.getId());
            detailsToDelete = oldDetails;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        List<IObjectData> detailDatas = attributeConstraintService.getAttributeConstraintLinesByAttributeConstraintId(actionContext.getUser(), arg.getObjectData().getId());
        List<AttributeConstraintModel.AttributeConstraintNode> nodes = attributeConstraintService.list2MultiTree(actionContext.getUser(), detailDatas);
        if(CollectionUtils.isNotEmpty(nodes) && newResult.getObjectData() != null) {
            String attributeJson = JsonUtil.toJson(nodes);
            newResult.getObjectData().put(AttributeConstaintConstants.FIELD_ATTRIBUTE_JSON, attributeJson);
        }
        return newResult;
    }

}
