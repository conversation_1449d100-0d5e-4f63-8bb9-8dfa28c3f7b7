package com.facishare.crm.sfa.utilities.util;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.LocationCalculationMessage;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AccountAddrConstants;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.constant.AccountFinInfoConstants;
import com.facishare.crm.util.DescribeI18NUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.RocketMQMessageSender;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.AutoNumberLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_NOT_ALLOW_MANY_MAIN_ADDR;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_NOT_ALLOW_MANY_SHIP_ADDR;
import static com.facishare.paas.appframework.common.util.ObjectAction.*;

/**
 * 客户位置公共类 class
 *
 * <AUTHOR>
 * @date 2019/1/16
 */
@Slf4j
public class AccountAddrUtil {
    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);
    private static final ObjectDataProxy DATA_PROXY = SpringUtil.getContext().getBean(ObjectDataProxy.class);
    private static final AutoNumberLogicService AUTO_NUMBER_LOGIC_SERVICE = SpringUtil.getContext().getBean(AutoNumberLogicService.class);
    private static final FunctionPrivilegeService FUNCTION_PRIVILEGE_SERVICE = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);
    private static final RocketMQMessageSender LOCATION_CALCULATION_MESSAGE_SENDER = SpringUtil.getContext().getBean("locationCalculationMessageSender", RocketMQMessageSender.class);

    private static final Set<ObjectAction> ADD_FUNC_LIST = Sets.newHashSet(ObjectAction.UPDATE, ObjectAction.INVALID);
    private static final Set<ObjectAction> INEFFECTIVE_WHITE_LIST_ACTIONS =
            Sets.newHashSet(VIEW_DETAIL, UPDATE, RECOVER, EDIT_TEAM_MEMBER, ADD_TEAM_MEMBER, DELETE_TEAM_MEMBER, INVALID,
                    LOCK, UNLOCK, PRINT, SALE_RECORD, DISCUSS, DIAL, SEND_MAIL, SCHEDULE, REMIND, START_BPM, STOP_BPM,
                    VIEW_ENTIRE_BPM, CHANGE_BPM_APPROVER, CHANGE_OWNER, CLONE, VIEW_BEFORE_SALE_ACTION, VIEW_AFTER_SALE_ACTION, VIEW_FEED_CARD);

    private static final Set<ObjectAction> LOCK_BLACK_LIST_ACTIONS =
            Sets.newHashSet(CREATE, LOCK, UPDATE, INVALID, BULK_INVALID, CHANGE_OWNER, START_STAGE_PROPELLOR, RETURN, MOVE, DEAL,
                    TAKE_BACK, CLOSE, ALLOCATE, FOLLOW_UP, CHOOSE);
    public static final String CHILD_COMPONENTS = "child_components";
    public static final String BUTTONS = "buttons";

    public static final String LIST_TYPE = "list";
    public static final String isMainAddr = "isMainAddr";
    public static final String isShipAddr = "isShipAddr";

    /**
     * 根据权限给财务和地址下发编辑作废按钮
     */
    public static void getButtons(User user, String objectApiName, IComponent component, ObjectDataDocument
            dataDocument) {
        Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = FUNCTION_PRIVILEGE_SERVICE.batchFunPrivilegeCheck(user,
                Lists.newArrayList(objectApiName),
                Lists.newArrayList(ObjectAction.UPDATE.getActionCode(), ObjectAction.INVALID.getActionCode()));
        Set<ObjectAction> tempHasPrivilege = ADD_FUNC_LIST;
        if (Utils.ACCOUNT_ADDR_API_NAME.equals(objectApiName)) {
            tempHasPrivilege = ADD_FUNC_LIST.stream().filter((x) -> {
                return checkLifeStatus(x, ObjectDataExt.of(dataDocument)) && checkLockStatus(x, ObjectDataExt.of(dataDocument), user);
            }).collect(Collectors.toSet());
        }
        Set<ObjectAction> hasPrivilege = tempHasPrivilege;
        List<Map> childComponents = (List) component.get(CHILD_COMPONENTS, ArrayList.class);
        if (!CollectionUtils.isEmpty(childComponents)) {
            childComponents.forEach(childComponent -> {
                List<Map> childComponentsButtons = (List) childComponent.get(BUTTONS);
                hasPrivilege.forEach(x -> {
                    if (objApiNameAndActionCodePrivilegeMapping.get(objectApiName).get(x.getActionCode())) {
                        childComponentsButtons.add(((Button) PreDefLayoutUtil.createButton(x)).getContainerDocument());
                    }
                });
                childComponent.put(BUTTONS, childComponentsButtons);
                List<Map> subChildComponents = (List) childComponent.get(CHILD_COMPONENTS);
                if (!CollectionUtils.isEmpty(subChildComponents)) {
                    subChildComponents.forEach(relatedListChildComponent -> {
                        List<Map> subChildComponentsButtons = (List) relatedListChildComponent.get(BUTTONS);
                        hasPrivilege.forEach(x -> {
                            if (objApiNameAndActionCodePrivilegeMapping.get(objectApiName).get(x.getActionCode())) {
                                subChildComponentsButtons.add(((Button) PreDefLayoutUtil.createButton(x)).getContainerDocument());
                            }
                        });
                        relatedListChildComponent.put(BUTTONS, subChildComponentsButtons);
                    });
                }
            });
        }
    }

    /**
     * 根据权限给财务和地址下发编辑作废按钮
     */
    public static List<IButton> getButtons(Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping,
                                           User user, String objectApiName, ObjectDataDocument dataDocument) {
        List<IButton> buttons = Lists.newArrayList();
        if (Utils.ACCOUNT_ADDR_API_NAME.equals(objectApiName)) {
            if (checkLifeStatus(CREATE, ObjectDataExt.of(dataDocument)) && checkLockStatus(CREATE, ObjectDataExt.of(dataDocument), user)) {
                if (objApiNameAndActionCodePrivilegeMapping.get(objectApiName).containsKey(CREATE.getActionCode()) && objApiNameAndActionCodePrivilegeMapping.get(objectApiName).get(CREATE.getActionCode())) {
                    buttons.add(PreDefLayoutUtil.createButton(CREATE));
                }
            }
        } else {
            if (objApiNameAndActionCodePrivilegeMapping.get(objectApiName).containsKey(CREATE.getActionCode()) && objApiNameAndActionCodePrivilegeMapping.get(objectApiName).get(CREATE.getActionCode())) {
                buttons.add(PreDefLayoutUtil.createButton(CREATE));
            }
        }
        Set<ObjectAction> hasPrivilege = ADD_FUNC_LIST;
        if (Utils.ACCOUNT_ADDR_API_NAME.equals(objectApiName)) {
            hasPrivilege = ADD_FUNC_LIST.stream().filter((x) -> {
                return checkLifeStatus(x, ObjectDataExt.of(dataDocument)) && checkLockStatus(x, ObjectDataExt.of(dataDocument), user);
            }).collect(Collectors.toSet());
        }
        hasPrivilege.forEach(x -> {
            if (objApiNameAndActionCodePrivilegeMapping.get(objectApiName).get(x.getActionCode())) {
                buttons.add(PreDefLayoutUtil.createButton(x));
            }
        });
        return buttons;
    }

    /**
     * 修改地址的展示字段
     *
     * @param component
     */
    public static void changeIncludeFields(User user, IComponent component) throws MetadataServiceException {
        List<Map> childComponents = (List) component.get(CHILD_COMPONENTS, ArrayList.class);
        Optional<Map> accountAddrComponent = childComponents.stream().filter(s -> s.get("ref_object_api_name") != null
                && s.get("ref_object_api_name").equals(Utils.ACCOUNT_ADDR_API_NAME)).findFirst();
        if (accountAddrComponent.isPresent()) {
            ILayout layout = SERVICE_FACADE.getLayoutLogicService().findDefaultLayout(user, "list", SFAPreDefineObject.AccountAddr.getApiName());
            Object includeFields = layout.getComponents().get(0).get("include_fields");
            accountAddrComponent.get().put("include_fields", includeFields);
        }
    }

    public static boolean checkLifeStatus(ObjectAction action, ObjectDataExt data) {
        if (data.isIneffective()) {
            return INEFFECTIVE_WHITE_LIST_ACTIONS.contains(action);
        } else if (data.isInChange() || data.isUnderReview()) {
            //变更中的数据屏蔽解锁按钮
            if (data.isInChange() && UNLOCK.equals(action)) {
                return false;
            }
            return !START_STAGE_PROPELLOR.equals(action);
        } else {
            return true;
        }
    }

    public static boolean checkLockStatus(ObjectAction action, ObjectDataExt data, User user) {
        if (user.isSupperAdmin()) {
            return true;
        }
        if (data.isLock()) {
            if (action == ObjectAction.LOCK) {
                return false;
            }
            return !LOCK_BLACK_LIST_ACTIONS.contains(action);
        } else {
            return action != UNLOCK;
        }
    }

    /**
     * 地址定位确保没有以#%$结尾的
     *
     * @param objectData
     */
    public static void handleLocationField(IObjectData objectData) {
        if (objectData != null) {
            String location = objectData.get("location", String.class);
            if (!Strings.isNullOrEmpty(location) && location.endsWith("#%$")) {
                objectData.set("location", null);
            }
        }
    }

    /**
     * 批量地址定位确保没有以#%$结尾的
     *
     * @param objectDataList
     */
    public static void bulkHandleLocationField(List<IObjectData> objectDataList) {
        if (!CollectionUtils.isEmpty(objectDataList)) {
            objectDataList.forEach(AccountAddrUtil::handleLocationField);
        }
    }

    /**
     * 组装GeoPoint字段
     *
     * @param objectData
     */
    public static void handleGeoPointField(IObjectData objectData) {
        if (objectData != null && ((ObjectData) objectData).containsKey("location")) {
            String location = objectData.get("location", String.class);
            if (!Strings.isNullOrEmpty(location) && !location.endsWith("#%$")) {
                String[] addressList = location.split("\\#\\%\\$");
                if (addressList != null && addressList.length == 3
                        && !Strings.isNullOrEmpty(addressList[0]) && !Strings.isNullOrEmpty(addressList[1])) {
                    objectData.set("geo_point", "(" + addressList[0] + "," + addressList[1] + ")");
                } else {
                    objectData.set("geo_point", null);
                }
            } else {
                objectData.set("geo_point", null);
            }
        }
    }

    /**
     * 批量组装GeoPoint字段
     *
     * @param objectDataList
     */
    public static void bulkHandleGeoPointField(List<IObjectData> objectDataList) {
        if (!CollectionUtils.isEmpty(objectDataList)) {
            objectDataList.forEach(AccountAddrUtil::handleGeoPointField);
        }
    }

    /**
     * 地址管理中添加客户的主地址
     *
     * @param objectData
     */
    public static IObjectData insertAccountAddr(User user, IObjectData objectData,boolean isNeedCkeckConfig,boolean isMainAddr, boolean isShipAddr) {
        if (objectData == null) {
            log.warn("insertAccountAddr param is null ");
            return null;
        }

        if(isNeedCkeckConfig && !AccountUtil.checkAddAccountMeanwhileAddAddrKeyValue(user)){
            log.warn("checkAddAccountMeanwhileAddAddrKeyValue value is 0 ");
            return null;
        }
        IObjectData objectDataResult = null;
        try {
            AccountAddrUtil.handleLocationField(objectData);
            String location = objectData.get(AccountConstants.Field.LOCATION, String.class);
            String address = objectData.get(AccountConstants.Field.ADDRESS, String.class);
            String country = objectData.get(AccountConstants.Field.COUNTRY, String.class);
            String province = objectData.get(AccountConstants.Field.PROVINCE, String.class);
            String city = objectData.get(AccountConstants.Field.CITY, String.class);
            String district = objectData.get(AccountConstants.Field.DISTRICT, String.class);
            String town = objectData.get(AccountConstants.Field.TOWN, String.class);
            List<String> dataOwnOrg = AccountUtil.getListValue(objectData, AccountConstants.Field.DATA_OWN_ORGANIZATION, Lists.newArrayList());
            List<String> dataOwnDep = AccountUtil.getListValue(objectData, AccountConstants.Field.DATA_OWN_DEPARTMENT, Lists.newArrayList());

            IObjectDescribe accountAddrDescribe = SERVICE_FACADE.findObject(user.getTenantId(), SFAPreDefineObject.AccountAddr.getApiName());
            IObjectData accountAddrData = new ObjectData();

            Object addType = null;
            IFieldDescribe fieldDescribe = accountAddrDescribe.getFieldDescribe("add_type");
            if (fieldDescribe != null) {
                SelectOneFieldDescribe addTypeField = (SelectOneFieldDescribe) fieldDescribe;
                addType = addTypeField.getDefaultValue();
                if (addType == null || StringUtils.isBlank(addType.toString())) {
                    Optional<ISelectOption> firstSelect = addTypeField.getSelectOptions().stream().filter(m -> !m.isNotUsable()).findFirst();
                    if (firstSelect.isPresent()) {
                        addType = firstSelect.get().getValue();
                    }
                }
            }

            accountAddrData.set("account_id", objectData.getId());
            accountAddrData.set("add_type", (addType != null && !Strings.isNullOrEmpty(addType.toString()) ? addType.toString() : "1"));
            accountAddrData.set("location", location);
            accountAddrData.set("address", address);
            accountAddrData.set("country", country);
            accountAddrData.set("province", province);
            accountAddrData.set("city", city);
            accountAddrData.set("district", district);
            accountAddrData.set("town", town);
            accountAddrData.set("is_default_add", isMainAddr);
            accountAddrData.set("is_ship_to_add", isShipAddr);

            accountAddrData.set("object_describe_api_name", accountAddrDescribe.getApiName());
            accountAddrData.set("object_describe_id", accountAddrDescribe.getId());
            accountAddrData.set(Tenantable.TENANT_ID, user.getTenantId());
            accountAddrData.set("owner", objectData.getOwner());
            accountAddrData.set("out_owner", objectData.getOutOwner());
            accountAddrData.set("out_tenant_id", objectData.getOutTenantId());
            accountAddrData.set("record_type", "default__c");
            accountAddrData.set("is_from_account_add", true);
            accountAddrData.set(AccountConstants.Field.DATA_OWN_DEPARTMENT, dataOwnDep);
            accountAddrData.set(AccountConstants.Field.DATA_OWN_ORGANIZATION, dataOwnOrg);

            AccountAddrUtil.handleGeoPointField(accountAddrData);

            if (GrayUtil.isGrayInsertAccountAddr(user.getTenantId())) {
                RequestContext requestContext = RequestContextManager.getContext();
                Object notValidate = requestContext.getAttribute("not_validate");
                Object triggerFlow = requestContext.getAttribute("triggerFlow");
                requestContext.setAttribute("not_validate", true);
                requestContext.setAttribute("triggerFlow", false);
                ActionContext actionContext = new ActionContext(requestContext, Utils.ACCOUNT_ADDR_API_NAME, ObjectAction.CREATE.getActionCode());
                BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
                arg.setObjectData(ObjectDataDocument.of(accountAddrData));
                BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
                optionInfo.setIsDuplicateSearch(false);
                optionInfo.setUseValidationRule(false);
                optionInfo.setSkipFuncValidate(true);
                if(AccountUtil.isGrayCreateAddrUserMainAuthCode(user.getTenantId())){
                    optionInfo.setRealTimeCalculateDetailAuth(true);
                }
                arg.setOptionInfo(optionInfo);
                String body = JSON.toJSONString(arg);
                log.debug("AccountAddrUtil>insertAccountAddr()  body{}: ", body);
                BaseObjectSaveAction.Result saveResult = SERVICE_FACADE.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
                log.debug("AccountAddrUtil>insertAccountAddr() result{}: ", JsonUtil.toJsonWithNullValues(saveResult));
                requestContext.setAttribute("not_validate", notValidate);
                requestContext.setAttribute("triggerFlow", triggerFlow);
                objectDataResult = saveResult.getObjectData().toObjectData();
            } else {
                try {
                    AUTO_NUMBER_LOGIC_SERVICE.calculateAutoNumberValue(accountAddrDescribe, Lists.newArrayList(accountAddrData));
                    IActionContext context = ActionContextExt.of(user, RequestContextManager.getContext())
                            .setNotValidate(true)
                            .getContext();
                    if(AccountUtil.isGrayCreateAddrUserMainAuthCode(user.getTenantId())){
                        context.put(ActionContextKey.MASTER_DETAIL_REAL_TIME_AUTH_CALCULATE,true);
                    }
                    objectDataResult = DATA_PROXY.create(accountAddrData, context);
                } catch (MetadataServiceException e) {
                    log.warn("insertAccountLocation error,user:{},dataList:{}", user, accountAddrData, e);
                    throw new MetaDataBusinessException(e);
                }
                //添加日志
                LogUtil.recordLogs(user, Lists.newArrayList(objectDataResult), EventType.ADD, ActionType.Add);
            }
        } catch (Exception e) {
            log.error("insertAccountLocation error,user:{},dataList:{}", user, objectData, e);
        }
        return objectDataResult;
    }

    /**
     * 批量在地址管理中添加客户的主地址
     *
     * @param objectDataList
     */
    public static void asyncBulkInsertAccountAddr(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            log.warn("asyncBulkInsertAccountAddr param is null ");
            return;
        }
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() -> bulkInsertAccountAddr(user, objectDataList));
            task.run();
        } catch (Exception e) {
            log.warn("AccountAddrUtil asyncBulkInsertAccountAddr task error,user {} {}", user, e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }

    }

    /**
     * 批量在地址管理中添加客户的主地址
     *
     * @param objectDataList
     */
    public static void bulkInsertAccountAddr(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            log.warn("bulkInsertAccountAddr param is null ");
            return;
        }
        for (IObjectData objectData : objectDataList) {
            insertAccountAddr(user, objectData,true,true,true);
        }
        AccountAddrUtil.sendLocationMq(user, SFAPreDefineObject.Account.getApiName(), objectDataList);
    }

    /**
     * 批量在地址管理中添加客户的主地址
     *
     * @param objectDataList
     */
    public static void bulkInsertAccountAddrWithLocation(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            log.warn("bulkInsertAccountAddr param is null ");
            return;
        }
        for (IObjectData objectData : objectDataList) {
            insertAccountAddr(user, objectData,true,true,true);
        }
    }

    /**
     * 更新客户时，同步更新客户地址中的主地址
     *
     * @param accountData 客户数据
     */
    public static void updateAccountAddrForMain(User user, IObjectData accountData) {
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        task.submit(() -> updateAccountAddrForMainAsync(user,accountData));
        task.run();

    }
    public static void updateAccountAddrForMainAsync(User user, IObjectData accountData) {
        if (accountData == null) {
            log.warn("updateAccountAddrForMain param is null ");
            return;
        }
        AccountAddrUtil.handleLocationField(accountData);
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        ext.addFilter(Operator.EQ, AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), accountData.getId());
        ext.addFilter(Operator.EQ, AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(), "true");
        ext.setOrders(Lists.newArrayList(new OrderBy("last_modified_time", false)));
        ext.setLimit(1000);
        QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user, SFAPreDefineObject.AccountAddr.getApiName(), (SearchTemplateQuery) ext.getQuery());
        log.info("updateAccountAddrForMainAsync");
        if (queryResult != null && !CollectionUtils.isEmpty(queryResult.getData())) {
            if(!AccountUtil.checkAccountAndAddrSyncUpdLocationKeyValue(user)){
                log.warn("checkAccountAndAddrSyncUpdLocationKeyValue value is 0 ");
                return ;
            }
            IObjectData accountAddrData = queryResult.getData().get(0);
            IObjectData dbObjectData = ObjectDataExt.of(accountAddrData).copy();
            List<String> updateFieldList = Lists.newArrayList();
            updateFieldList.add(AccountAddrConstants.Field.LOCATION.getApiName());
            updateFieldList.add(AccountAddrConstants.Field.ADDRESS.getApiName());
            updateFieldList.add(AccountAddrConstants.Field.COUNTRY.getApiName());
            updateFieldList.add(AccountAddrConstants.Field.PROVINCE.getApiName());
            updateFieldList.add(AccountAddrConstants.Field.CITY.getApiName());
            updateFieldList.add(AccountAddrConstants.Field.DISTRICT.getApiName());
            updateFieldList.add(AccountAddrConstants.Field.TOWN.getApiName());
            updateFieldList.add(AccountAddrConstants.Field.GEOPOINT.getApiName());

            accountAddrData.set(AccountAddrConstants.Field.LOCATION.getApiName(), accountData.get(AccountConstants.Field.LOCATION));
            accountAddrData.set(AccountAddrConstants.Field.ADDRESS.getApiName(), accountData.get(AccountConstants.Field.ADDRESS));
            accountAddrData.set(AccountAddrConstants.Field.COUNTRY.getApiName(), accountData.get(AccountConstants.Field.COUNTRY));
            accountAddrData.set(AccountAddrConstants.Field.PROVINCE.getApiName(), accountData.get(AccountConstants.Field.PROVINCE));
            accountAddrData.set(AccountAddrConstants.Field.CITY.getApiName(), accountData.get(AccountConstants.Field.CITY));
            accountAddrData.set(AccountAddrConstants.Field.DISTRICT.getApiName(), accountData.get(AccountConstants.Field.DISTRICT));
            accountAddrData.set(AccountAddrConstants.Field.TOWN.getApiName(), accountData.get(AccountConstants.Field.TOWN));

            AccountAddrUtil.handleGeoPointField(accountAddrData);
            List<IObjectData> objectDataList = AccountAddrUtil.batchUpdateIgnoreOther(user, Arrays.asList(accountAddrData), updateFieldList);
            //添加日志
            LogUtil.asyncRecordLogs(user, objectDataList, Lists.newArrayList(dbObjectData), EventType.MODIFY, ActionType.Modify);
        } else {
            if(AccountUtil.checkAddAccountMeanwhileAddAddrKeyValue(user)){
                log.warn("checkAddAccountMeanwhileAddAddrKeyValue is 1");
                return;
            }
            insertAccountAddr(user, accountData,true,true,true);
        }
    }


    /**
     * 批量更新客户时，同步更新客户地址中的主地址
     *
     * @param accountDataList 客户数据
     */
    public static void bulkUpdateAccountAddrForMain(User user, List<IObjectData> accountDataList) {
        if (CollectionUtils.isEmpty(accountDataList)) {
            log.warn("bulkUpdateAccountAddrForMain param is null ");
            return;
        }
        List<String> accountIds = accountDataList.stream().map(DBRecord::getId)
                .filter(id -> !Strings.isNullOrEmpty(id))
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountIds)) {
            log.warn("bulkUpdateAccountAddrForMain accountIds is null ");
            return;
        }
        if(!AccountUtil.checkAccountAndAddrSyncUpdLocationKeyValue(user)){
            log.warn("checkAccountAndAddrSyncUpdLocationKeyValue value is 0 ");
            return ;
        }
        SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        ext.addFilter(Operator.IN, AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), accountIds);
        ext.addFilter(Operator.EQ, AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(), "true");
        ext.setOrders(Lists.newArrayList(new OrderBy("last_modified_time", false)));
        ext.setLimit(1000);
        QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user, SFAPreDefineObject.AccountAddr.getApiName(), (SearchTemplateQuery) ext.getQuery());
        if (queryResult != null && !CollectionUtils.isEmpty(queryResult.getData())) {
            bulkUpdateAccountAddrForMain(user, accountDataList, queryResult.getData());
        }
    }

    /**
     * 批量更新客户时，同步更新客户地址中的主地址
     *
     * @param accountDataList     客户数据
     * @param accountAddrDataList 客户地址数据
     */
    public static void bulkUpdateAccountAddrForMain(User user, List<IObjectData> accountDataList, List<IObjectData> accountAddrDataList) {
        if (CollectionUtils.isEmpty(accountDataList) || CollectionUtils.isEmpty(accountAddrDataList)) {
            log.warn("bulkUpdateAccountAddrForMain param is null ");
            return;
        }
        AccountAddrUtil.bulkHandleLocationField(accountDataList);
        List<String> updateFieldList = Lists.newArrayList();
        updateFieldList.add(AccountAddrConstants.Field.LOCATION.getApiName());
        updateFieldList.add(AccountAddrConstants.Field.ADDRESS.getApiName());
        updateFieldList.add(AccountAddrConstants.Field.COUNTRY.getApiName());
        updateFieldList.add(AccountAddrConstants.Field.PROVINCE.getApiName());
        updateFieldList.add(AccountAddrConstants.Field.CITY.getApiName());
        updateFieldList.add(AccountAddrConstants.Field.DISTRICT.getApiName());
        updateFieldList.add(AccountAddrConstants.Field.TOWN.getApiName());
        updateFieldList.add(AccountAddrConstants.Field.GEOPOINT.getApiName());

        List<IObjectData> dbObjectDataList = ObjectDataExt.copyList(accountAddrDataList);
        List<IObjectData> dataList = Lists.newArrayList();
        for (IObjectData accountAddrData : accountAddrDataList) {
            Optional<IObjectData> optionaResult = accountDataList.stream().filter(m -> accountAddrData.get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), String.class).equals(m.getId())).findFirst();
            if (optionaResult.isPresent()) {
                IObjectData accountData = optionaResult.get();
                accountAddrData.set(AccountAddrConstants.Field.LOCATION.getApiName(), accountData.get(AccountConstants.Field.LOCATION));
                accountAddrData.set(AccountAddrConstants.Field.ADDRESS.getApiName(), accountData.get(AccountConstants.Field.ADDRESS));
                accountAddrData.set(AccountAddrConstants.Field.COUNTRY.getApiName(), accountData.get(AccountConstants.Field.COUNTRY));
                accountAddrData.set(AccountAddrConstants.Field.PROVINCE.getApiName(), accountData.get(AccountConstants.Field.PROVINCE));
                accountAddrData.set(AccountAddrConstants.Field.CITY.getApiName(), accountData.get(AccountConstants.Field.CITY));
                accountAddrData.set(AccountAddrConstants.Field.DISTRICT.getApiName(), accountData.get(AccountConstants.Field.DISTRICT));
                accountAddrData.set(AccountAddrConstants.Field.TOWN.getApiName(), accountData.get(AccountConstants.Field.TOWN));

                dataList.add(accountAddrData);
            }
        }
        if (!CollectionUtils.isEmpty(dataList)) {
            AccountAddrUtil.bulkHandleGeoPointField(dataList);
            List<IObjectData> objectDataList = AccountAddrUtil.batchUpdateIgnoreOther(user, dataList, updateFieldList);
            //添加日志
            LogUtil.asyncRecordLogs(user, objectDataList, dbObjectDataList, EventType.MODIFY, ActionType.Modify);
        }
    }

    /**
     * 批量更新主地址时，更新biz_account表中的地址相关信息[兼容低版本客户端]
     *
     * @param accountAddrDataList 地址数据
     */
    public static void bulkUpdateAccountLocation(User user, List<IObjectData> accountAddrDataList) {
        if (CollectionUtils.isEmpty(accountAddrDataList)) {
            log.warn("bulkUpdateAccountLocation param is null ");
            return;
        }
        List<IObjectData> mainAddrList = filterAccountIdAndMainIsNotNull(accountAddrDataList);
        if(CollectionUtils.isEmpty(mainAddrList)){
            log.info("accountAddrDataList not have main addr ");
            return;
        }
        List<String> accountIds = mainAddrList.stream().map(n -> n.get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), String.class)).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountIds)) {
            log.warn("bulkUpdateAccountLocation accountIds is null ");
            return;
        }
        List<IObjectData> accountList = SERVICE_FACADE.findObjectDataByIds(user.getTenantId(), accountIds, SFAPreDefineObject.Account.getApiName());
        if (accountList != null && !CollectionUtils.isEmpty(accountList)) {
            bulkUpdateAccountLocation(user, accountList, mainAddrList,true);
        }
    }

    /**
     * 批量更新主地址时，更新biz_account表中的地址相关信息[兼容低版本客户端]
     *
     * @param accountDataList     客户数据
     * @param accountAddrDataList 地址数据
     */
    public static void bulkUpdateAccountLocation(User user, List<IObjectData> accountDataList, List<IObjectData> accountAddrDataList,boolean isNeedCkeckConfig) {
        if (CollectionUtils.isEmpty(accountDataList) || CollectionUtils.isEmpty(accountAddrDataList)) {
            log.warn("bulkUpdateAccountLocation param is null ");
            return;
        }
        if(isNeedCkeckConfig && !AccountUtil.checkAccountAndAddrSyncUpdLocationKeyValue(user)){
            log.warn("checkAccountAndAddrSyncUpdLocationKeyValue value is 0 ");
            return ;
        }
        List<IObjectData> dbObjectDataList = ObjectDataExt.copyList(accountDataList);
        AccountAddrUtil.bulkHandleLocationField(accountAddrDataList);
        List<String> updateFieldList = Lists.newArrayList();
        updateFieldList.add(AccountConstants.Field.LOCATION);
        updateFieldList.add(AccountConstants.Field.ADDRESS);
        updateFieldList.add(AccountConstants.Field.COUNTRY);
        updateFieldList.add(AccountConstants.Field.PROVINCE);
        updateFieldList.add(AccountConstants.Field.CITY);
        updateFieldList.add(AccountConstants.Field.DISTRICT);
        updateFieldList.add(AccountConstants.Field.TOWN);
        List<IObjectData> dataList = Lists.newArrayList();
        for (IObjectData accountData : accountDataList) {
            Optional<IObjectData> optionaResult = accountAddrDataList.stream().filter(m -> accountData.getId().equals(m.get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), String.class))).findFirst();
            if (optionaResult.isPresent()) {
                IObjectData accountAddrData = optionaResult.get();
                accountData.set(AccountConstants.Field.LOCATION, accountAddrData.get(AccountAddrConstants.Field.LOCATION.getApiName()));
                accountData.set(AccountConstants.Field.ADDRESS, accountAddrData.get(AccountAddrConstants.Field.ADDRESS.getApiName()));
                accountData.set(AccountConstants.Field.COUNTRY, accountAddrData.get(AccountAddrConstants.Field.COUNTRY.getApiName()));
                accountData.set(AccountConstants.Field.PROVINCE, accountAddrData.get(AccountAddrConstants.Field.PROVINCE.getApiName()));
                accountData.set(AccountConstants.Field.CITY, accountAddrData.get(AccountAddrConstants.Field.CITY.getApiName()));
                accountData.set(AccountConstants.Field.DISTRICT, accountAddrData.get(AccountAddrConstants.Field.DISTRICT.getApiName()));
                accountData.set(AccountConstants.Field.TOWN, accountAddrData.get(AccountAddrConstants.Field.TOWN.getApiName()));
                dataList.add(accountData);
            }
        }
        if (!CollectionUtils.isEmpty(dataList)) {
            List<IObjectData> objectDataList = AccountAddrUtil.batchUpdateIgnoreOther(user, dataList, updateFieldList);
            //添加日志
            LogUtil.asyncRecordLogs(user, objectDataList, dbObjectDataList, EventType.MODIFY, ActionType.Modify);
        }
    }

    /**
     * 更新导入/把位置发送MQ给任务处理
     */
    public static void sendLocationMq(User user, String apiName, List<IObjectData> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            log.warn("sendLocationMq param is null ");
            return;
        }
        Map<String, String> locationInfo = Maps.newHashMap();
        for (IObjectData objectData : objectDataList) {
            String location = objectData.get("location", String.class);
            if (!Strings.isNullOrEmpty(location)) {
                String[] tempLocation = location.split("\\#\\%\\$");
                if (tempLocation.length > 1) {
                    location = tempLocation[tempLocation.length - 1];
                }
                if (tempLocation.length == 3 && tempLocation[0].startsWith("0")) {
                    locationInfo.put(objectData.getId(), location);
                } else if (tempLocation.length != 3) {
                    locationInfo.put(objectData.getId(), location);
                }
            }
        }
        if (!CollectionUtils.isEmpty(locationInfo)) {
            LocationCalculationMessage locationCalculationMq = LocationCalculationMessage
                    .builder()
                    .tenantId(user.getTenantId())
                    .apiName(apiName)
                    .locationInfo(locationInfo)
                    .build();
            try {
                LOCATION_CALCULATION_MESSAGE_SENDER.sendMessage(JSON.toJSONBytes(locationCalculationMq));
            } catch (Exception e) {
                log.error("消息发送失败！tenantId: {},apiName: {},",user.getTenantId(),apiName, e);
            }
        }
    }

    /**
     * 地址导入去掉字段
     *
     * @return
     */
    public static List<String> getImportTemplateRemoveFields(User user) {
        List<String> removeFieldList = Lists.newArrayList("is_default_add", "is_ship_to_add", "lock_status", "life_status", "owner_department",
                "geo_point", "account_record_type", "account_name");
        if(AccountUtil.isOpenAccountAddrConfig(user)){
            removeFieldList.removeAll(Lists.newArrayList("is_default_add", "is_ship_to_add"));
        }
        return removeFieldList;
    }

    public static List<IObjectData> batchUpdateIgnoreOther(User user, List<IObjectData> dataList, List<String> updateFieldList) {
        return batchUpdateIgnoreOther(buildContext(user, false), dataList, updateFieldList);
    }

    public static List<IObjectData> batchUpdateIgnoreOther(User user, List<IObjectData> dataList, List<String> updateFieldList, boolean allowUpdateInvalid,
                                                           boolean allowUpdateTime) {
        IActionContext actionContext = buildContext(user, allowUpdateInvalid);
        if (!allowUpdateTime) {
            actionContext.put("specify_time", true);
        }
        return batchUpdateIgnoreOther(actionContext, dataList, updateFieldList);
    }

    public static IActionContext buildContext(User user, boolean allowUpdateInvalid) {
        return ActionContextExt.of(user, RequestContextManager.getContext()).allowUpdateInvalid(allowUpdateInvalid).setNotValidate(true).getContext();
    }

    private static List<IObjectData> batchUpdateIgnoreOther(IActionContext context, List<IObjectData> dataList, List<String> updateFieldList) {
        if (CollectionUtils.isEmpty(dataList) || CollectionUtils.isEmpty(updateFieldList)) {
            return dataList;
        }
        try {
            List<IObjectData> updateList = Lists.newArrayList();
            dataList.forEach(x -> {
                IObjectData data = new ObjectData();
                if (ObjectDataExt.of(x).containsExtendObjDataId()) {
                    ObjectDataExt.of(data).setExtendObjDataId(ObjectDataExt.of(x).getExtendObjDataId());
                }
                data.setTenantId(x.getTenantId());
                data.setDescribeApiName(x.getDescribeApiName());
                data.setId(x.getId());
                updateFieldList.forEach(y -> data.set(y, x.get(y)));
                updateList.add(data);
            });

            return SERVICE_FACADE.batchUpdateByFields(context, updateList, updateFieldList);
        } catch (Exception e) {
            log.warn("AccountAddrUtil.batchUpdateIgnoreOther error,context:{},dataList:{},updateFieldList:{}",
                    context, dataList, updateFieldList, e);
            throw e;
        }
    }
    public static boolean setButtonsOfDataCheckArg(StandardRelatedListController.Arg arg){
        if(ObjectUtils.isNotEmpty(arg.getObjectApiName()) && SFAPreDefineObject.Account.getApiName().equals(arg.getTargetObjectApiName())){
            return true;
        }
        return false;
    }
    public static void setButtonsOfData(User user,BaseListController.Result result,String objectApiName,String type){
        if(CollectionUtils.isEmpty(result.getDataList()) || ObjectUtils.isEmpty(result.getButtonInfo()) || ObjectUtils.isEmpty(result.getButtonInfo().getButtonMap())){
            return;
        }
        /**
         * 1、添加按钮判断权限==1.客户的编辑权限、2。客户地址的编辑权限、3.客户地址字段的权限、4.按钮的权限
         * 2、给每一条数据去掉按钮
         */
        Map funPrivilegeMap = new HashMap();
        boolean authorityFlag = isHaveAuthority(user,funPrivilegeMap,objectApiName);
        Map<String, List<String>> buttonMap = result.getButtonInfo().getButtonMap();

        IObjectDescribe describe = SERVICE_FACADE.findObject(user.getTenantId(),objectApiName);
        Map<String, Permissions> privMap = SERVICE_FACADE.checkPrivilege(user, ObjectDataDocument.ofDataList(result.getDataList()), describe, UPDATE.getActionCode());

        /**
         *
         */
        //添加编辑 、作废按钮
        ADD_FUNC_LIST.stream().forEach(x->{
            result.getButtonInfo().getButtons().add(ButtonDocument.fromButton(PreDefLayoutUtil.createButton(x)));
        });

        Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = FUNCTION_PRIVILEGE_SERVICE.batchFunPrivilegeCheck(user,
                Lists.newArrayList(objectApiName),
                Lists.newArrayList(UPDATE.getActionCode(), INVALID.getActionCode()));


        result.getDataList().forEach(m -> {
            //数据权限
            Permissions permissions = privMap.get(m.getId());
            List<String> actionList = new ArrayList<>();
            if(buttonMap.containsKey(m.getId()) && !CollectionUtils.isEmpty( buttonMap.get(m.getId()))){
                actionList = buttonMap.get(m.getId());

                // authorityFlag代表的是  是否具有 1.客户的编辑权限、2。客户地址的编辑权限、3.客户地址字段的权限、4.按钮的权限
                // permissions 数据权限
                if(authorityFlag && ObjectUtils.isNotEmpty(permissions) && permissions.getValue().equals(Permissions.READ_WRITE.getValue())){
                    IObjectData accountAddrObjectData = m.toObjectData();
                    Boolean isMain = accountAddrObjectData.get(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(), Boolean.class);

                    if(!funPrivilegeMap.containsKey(ObjectAction.SET_MAIN.getButtonApiName()) || Boolean.TRUE.equals(isMain)){
                        actionList.remove(ObjectAction.SET_MAIN.getButtonApiName());
                    }

                    Boolean isShip = accountAddrObjectData.get(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName(), Boolean.class);
                    Boolean isDefault = accountAddrObjectData.get(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName(), Boolean.class);

                    if(!funPrivilegeMap.containsKey(ObjectAction.SET_DEFAULT.getButtonApiName()) || Boolean.TRUE.equals(isShip) || Boolean.TRUE.equals(isDefault)){
                        actionList.remove(ObjectAction.SET_DEFAULT.getButtonApiName());
                    }
                }else{
                    actionList.remove(ObjectAction.SET_MAIN.getButtonApiName());
                    actionList.remove(ObjectAction.SET_DEFAULT.getButtonApiName());
                }
            }
            /**
             * ObjectUtils.isEmpty(type) || !LIST_TYPE.equals(type)  list页面不下发编辑、作废按钮
             */
            if(ObjectUtils.isNotEmpty(permissions) && permissions.getValue().equals(Permissions.READ_WRITE.getValue()) && (ObjectUtils.isEmpty(type) || !LIST_TYPE.equals(type))){
                List<IButton> buttonList = getButtons(objApiNameAndActionCodePrivilegeMapping,user,objectApiName,m);
               if(!CollectionUtils.isEmpty(buttonList)){
                   buttonList=buttonList.stream().filter(button->!button.getAction().equals(CREATE.getActionCode())).collect(Collectors.toList());
               }
                if(!CollectionUtils.isEmpty(buttonList)){
                    for(IButton button :buttonList){
                        actionList.add(button.getName());
                    }
                }
            }
            buttonMap.put(m.getId(),actionList);
        });
        result.getButtonInfo().setButtonMap(buttonMap);
    }
    /**
     * 1、添加按钮判断权限==1.客户的编辑权限、2。客户地址的编辑权限、3.客户地址字段的权限、4.按钮的权限
     */
    public static boolean isHaveAuthority(User user,Map map,String objectApiName){
        if(SFAPreDefineObject.AccountAddr.getApiName().equals(objectApiName)){
            List<String> funPrivilegeList = SERVICE_FACADE.funPrivilegeCheck(user, Lists.newArrayList(SFAPreDefineObject.Account.getApiName(),objectApiName),ObjectAction.UPDATE.getActionCode());
            if(!funPrivilegeList.contains(SFAPreDefineObject.Account.getApiName()) || !funPrivilegeList.contains(objectApiName)){
                return false;
            }
        }

        Map<String, Boolean> funPrivilegeMap = SERVICE_FACADE.funPrivilegeCheck(user,objectApiName, Lists.newArrayList(ObjectAction.SET_MAIN.getActionCode(),ObjectAction.SET_DEFAULT.getActionCode()));
        if(ObjectUtils.isEmpty(funPrivilegeMap)){
            return false;
        }

        Set<String> fieldInvisible = SERVICE_FACADE.getUnauthorizedFields(user, objectApiName);
        Set<String> fieldReadonly = SERVICE_FACADE.getReadonlyFields(user, objectApiName);
        fieldInvisible.addAll(fieldReadonly);
        if(SFAPreDefineObject.AccountAddr.getApiName().equals(objectApiName)){
            if(funPrivilegeMap.get(ObjectAction.SET_MAIN.getActionCode()) && (CollectionUtils.isEmpty(fieldInvisible) || !fieldInvisible.contains(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName()))){
                map.put(ObjectAction.SET_MAIN.getButtonApiName(),true);
            }
            if(funPrivilegeMap.get(ObjectAction.SET_DEFAULT.getActionCode()) && (CollectionUtils.isEmpty(fieldInvisible) || !fieldInvisible.contains(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName()))){
                map.put(ObjectAction.SET_DEFAULT.getButtonApiName(),true);
            }
        }else if(SFAPreDefineObject.AccountFinInfo.getApiName().equals(objectApiName)){
            if(funPrivilegeMap.get(ObjectAction.SET_DEFAULT.getActionCode()) && (CollectionUtils.isEmpty(fieldInvisible) || !fieldInvisible.contains(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName()))){
                map.put(ObjectAction.SET_DEFAULT.getButtonApiName(),true);
            }
        }

        if(ObjectUtils.isEmpty(map)){
            return false;
        }
        return true;
    }

    /**
     * 判断是否有权限更新主、默认地址
     * @param user
     * @param type
     * @return
     */
    public static boolean dealIsHaveAuthorityToUpdateMainOrDefault(User user,String type,String objectApiName,String dataId){
        IObjectDescribe describe = SERVICE_FACADE.findObject(user.getTenantId(),objectApiName);
        Map<String, Permissions> privMap = SERVICE_FACADE.checkDataPrivilege(user, Lists.newArrayList(dataId), describe);
        if(!privMap.get(dataId).getValue().equals(Permissions.READ_WRITE.getValue())){
            return false;
        }
        Map funPrivilegeMap = new HashMap();
        boolean authorityFlag = isHaveAuthority(user,funPrivilegeMap,objectApiName);
        if(authorityFlag){
            if(ObjectUtils.isNotEmpty(funPrivilegeMap) && funPrivilegeMap.containsKey(type)){
                return true;
            }
        }
        return false;
    }

    public static Integer getAccountAddrCountByAaccountId(String tenantId,String accountId){
        Count countFieldDescribe = getCountField();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), accountId);
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        query.setFilters(filters);
        Object objResult = SERVICE_FACADE.getCountValue(tenantId, countFieldDescribe, query);
        if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
           return Integer.parseInt(objResult.toString());
        }
        return 0;
    }

    public static Count getCountField() {
        Count countFieldDescribe = new CountFieldDescribe();
        countFieldDescribe.setApiName(Utils.ACCOUNT_ADDR_API_NAME);
        countFieldDescribe.setFieldApiName("totalcount");
        countFieldDescribe.setSubObjectDescribeApiName(Utils.ACCOUNT_ADDR_API_NAME);
        countFieldDescribe.setCountFieldApiName("id");
        countFieldDescribe.setCountType(Count.TYPE_COUNT);
        countFieldDescribe.setReturnType("number");
        countFieldDescribe.setDecimalPlaces(0);
        return countFieldDescribe;
    }

    public static QueryResult<IObjectData> getQueryResultBySearchQuery(IActionContext context, String apiName, SearchTemplateQuery searchTemplateQuery, List<String> fieldList) {
        return SERVICE_FACADE.findBySearchTemplateQueryWithFields(context, apiName, searchTemplateQuery, fieldList);
    }


    public static void setNullOfMainAndDefaultField(String type,String objectApiName,List<BaseImportDataAction.ImportData> dataList){
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }

        dataList.stream().forEach(data->{
            if(!data.getData().containsField(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName()) && !data.getData().containsField(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName())
                    && !data.getData().containsField(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName())){
                return;
            }
            ObjectDataDocument dataDocument = ObjectDataDocument.of(data.getData());
            if(ObjectAction.CREATE.getActionCode().equals(type)){
                if(SFAPreDefineObject.AccountAddr.getApiName().equals(objectApiName)){
                    dataDocument.put(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(),false);
                    dataDocument.put(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName(),false);
                }else if(SFAPreDefineObject.AccountFinInfo.getApiName().equals(objectApiName)){
                    dataDocument.put(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName(),false);
                }
            }else if(UPDATE.getActionCode().equals(type)){
                if(SFAPreDefineObject.AccountAddr.getApiName().equals(objectApiName)){
                    dataDocument.remove(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName());
                    dataDocument.remove(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName());
                }else if(SFAPreDefineObject.AccountFinInfo.getApiName().equals(objectApiName)){
                    dataDocument.remove(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName());
                }
            }
            data.setData(dataDocument.toObjectData());
        });
    }

    /**
     * 将主地址设置为非主地址
     * @param user
     * @param accountId
     * @param notNeedHandleId
     * @param describe
     * @param operateApiName 操作的对象
     * @param actionLabelKey   操作动作  {0}{1},新建；编辑；上传新建；上传更新；设置主地址
     */
    public static void handleMainAddrSetNonMain(User user,String accountId,String notNeedHandleId,IObjectDescribe describe,String operateApiName,String actionLabelKey){
        int index = 0;
        int PAGE_SIZE = 200;
        String buttonApiName = I18N.text("sfa.tack_back.to.pool.log",
                I18N.text(DescribeI18NUtils.getObjectNameKey(operateApiName)),I18N.text(actionLabelKey));
        InternationalItem peerDisplayName = new InternationalItem();
        peerDisplayName.setDefaultInternationalValue(buttonApiName);
        peerDisplayName.setInternationalKey("sfa.tack_back.to.pool.log");
        peerDisplayName.setInternationalParameters(Lists.newArrayList(DescribeI18NUtils.getObjectNameKey(operateApiName),actionLabelKey));
        while(index<1000){
            index++; //循环次数加1
            if (index == 1000) { //达到循环次数限制
                log.warn("handleMainAddrSetNonMain loop limit, limit:{}", index); //日志打印
                //上报audit_log
                SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder().action(ObjectAction.SET_MAIN.getButtonApiName()).message("SetMainAction").objectApiNames(SFAPreDefineObject.AccountAddr.getApiName()).build(), user);
                break;
            }
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(PAGE_SIZE);
            searchTemplateQuery.setFindExplicitTotalNum(false);
            searchTemplateQuery.setNeedReturnCountNum(false);
            searchTemplateQuery.setPermissionType(0);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(), true);
            SearchUtil.fillFilterNotEq(filters, "_id", notNeedHandleId);
            SearchUtil.fillFilterEq(filters, AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), accountId);
            searchTemplateQuery.setFilters(filters);
            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user, SFAPreDefineObject.AccountAddr.getApiName(), searchTemplateQuery);
            if(ObjectUtils.isEmpty(queryResult) || com.facishare.paas.appframework.common.util.CollectionUtils.empty(queryResult.getData())){
                break;
            }
            List<IObjectData> accountAddrList =  queryResult.getData();
            List<IObjectData> tempDataList = ObjectDataExt.copyList(accountAddrList);
            for (IObjectData objectData : accountAddrList) {
                objectData.set(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(), false);
            }
            SERVICE_FACADE.batchUpdateByFields(user, accountAddrList, Arrays.asList(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName()));
            Map<String, Object> diffMap = Maps.newHashMap();
            diffMap.put(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(),false);
            Map<String,IObjectData> dbMap = tempDataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity(),(v1,v2)->v2));
            accountAddrList.stream().forEach(m->{
                SERVICE_FACADE.log(user, EventType.MODIFY, ActionType.Modify, describe, m, diffMap, dbMap.get(m.getId()), buttonApiName, buttonApiName, peerDisplayName,null,Maps.newHashMap());
            });
            if(accountAddrList.size()< PAGE_SIZE){
                break;
            }
        }
    }    /**
     * 将主地址设置为非主地址
     * @param user
     * @param accountId
     * @param notNeedHandleId
     * @param describe
     * @param operateApiName 操作的对象
     * @param actionLabelKey   操作动作  {0}{1},新建；编辑；上传新建；上传更新；设置主地址
     */
    public static void handleShipAddrSetNonMain(User user,String accountId,String notNeedHandleId,IObjectDescribe describe,String operateApiName,String actionLabelKey){

        int index = 0;
        int PAGE_SIZE = 200;
        String buttonApiName = I18N.text("sfa.tack_back.to.pool.log",
                I18N.text(DescribeI18NUtils.getObjectNameKey(operateApiName)),I18N.text(actionLabelKey));
        InternationalItem peerDisplayName = new InternationalItem();
        peerDisplayName.setDefaultInternationalValue(buttonApiName);
        peerDisplayName.setInternationalKey("sfa.tack_back.to.pool.log");
        peerDisplayName.setInternationalParameters(Lists.newArrayList(DescribeI18NUtils.getObjectNameKey(operateApiName),actionLabelKey));
        while(index<1000){
            index++; //循环次数加1
            if (index == 1000) { //达到循环次数限制
                log.warn("AccountAddrSetDefaultAction doAct loop limit, limit:{}", index); //日志打印
                //上报audit_log
                SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder().action(ObjectAction.SET_DEFAULT.getButtonApiName()).message("SetDefaultAction").objectApiNames(SFAPreDefineObject.AccountAddr.getApiName()).build(), user);
                break;
            }
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(PAGE_SIZE);
            searchTemplateQuery.setFindExplicitTotalNum(false);
            searchTemplateQuery.setNeedReturnCountNum(false);
            searchTemplateQuery.setPermissionType(0);
            List<IFilter> filters = Lists.newArrayList();
            com.facishare.crm.sfa.lto.utils.SearchUtil.fillFilterEq(filters, AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName(), true);
            com.facishare.crm.sfa.lto.utils.SearchUtil.fillFilterNotEq(filters, "_id", notNeedHandleId);
            com.facishare.crm.sfa.lto.utils.SearchUtil.fillFilterEq(filters, AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), accountId);
            searchTemplateQuery.setFilters(filters);
            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user, SFAPreDefineObject.AccountAddr.getApiName(), searchTemplateQuery);
            if(ObjectUtils.isEmpty(queryResult) || CollectionUtils.isEmpty(queryResult.getData())){
                break;
            }
            List<IObjectData> accountAddrList =  queryResult.getData();
            List<IObjectData> tempDataList = ObjectDataExt.copyList(accountAddrList);
            for (IObjectData objectData : accountAddrList) {
                objectData.set(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName(), false);
            }
            SERVICE_FACADE.batchUpdateByFields(user, accountAddrList, Arrays.asList(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName()));
            Map<String, Object> diffMap = Maps.newHashMap();
            diffMap.put(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName(),false);
            Map<String,IObjectData> dbMap = tempDataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity(),(v1,v2)->v2));
            accountAddrList.stream().forEach(m->{
                SERVICE_FACADE.log(user, EventType.MODIFY, ActionType.Modify, describe, m, diffMap, dbMap.get(m.getId()), buttonApiName, buttonApiName, peerDisplayName,null,Maps.newHashMap());
            });
            if(accountAddrList.size() < PAGE_SIZE){
                break;
            }
        }
    }

    /**
     * 将主地址设置为非主地址
     * @param user
     * @param describe
     * @param operateApiName 操作的对象
     * @param actionLabelKey   操作动作  {0}{1},新建；编辑；上传新建；上传更新；设置主地址
     */
    public static void handleMainAddrSetNonMainCheckAccountId(User user,ObjectDataDocument objectDataDocument,IObjectDescribe describe,String operateApiName,String actionLabelKey){
        String accountId = AccountUtil.getStringValue(objectDataDocument,AccountAddrConstants.Field.ACCOUNT_ID.getApiName(),"");
        if(ObjectUtils.isNotEmpty(accountId)){
            AccountAddrUtil.handleMainAddrSetNonMain(user,accountId,objectDataDocument.getId(),describe,operateApiName, actionLabelKey);
        }else{
            log.error("handleMainAddrSetNonMainCheckAccountId accountId is null id:{}",objectDataDocument.getId());
        }
    }
    public static void handleShipAddrSetNonMainCheckAccountId(User user,ObjectDataDocument objectDataDocument,IObjectDescribe describe,String operateApiName,String actionLabelKey){
        String accountId = AccountUtil.getStringValue(objectDataDocument,AccountAddrConstants.Field.ACCOUNT_ID.getApiName(),"");
        if(ObjectUtils.isNotEmpty(accountId)){
            AccountAddrUtil.handleShipAddrSetNonMain(user,accountId,objectDataDocument.getId(),describe,operateApiName, actionLabelKey);
        }else{
            log.error("handleMainAddrSetNonMainCheckAccountId accountId is null id:{}",objectDataDocument.getId());
        }
    }

    /**
     * 将主地址设置为非主地址
     * 在代码里判断主从同时新建的时候，用户有没有选择主地址 或者收货地址，如果选择了，在自动生成地址的时候不在生成主地址和收货地址
     * @param user
     *
     */
    public static void handleCheckManyMainAddr(User user,List<IObjectData> detailDataList, Map<String,Boolean> isMainOrShipMap){
        long num = detailDataList.stream()
                .filter(data -> AccountUtil.getBooleanValue(data, AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(), false))
                .count();

        if (num > 1) {
            throw new ValidateException(I18N.text(SFA_NOT_ALLOW_MANY_MAIN_ADDR));
        }
        if(num==1){
            isMainOrShipMap.put(isMainAddr,false);
        }
        num = detailDataList.stream()
                .filter(data -> AccountUtil.getBooleanValue(data, AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName(), false))
                .count();
        if(num>1){
            throw new ValidateException(I18N.text(SFA_NOT_ALLOW_MANY_SHIP_ADDR));
        }

        if(num==1){
            isMainOrShipMap.put(isShipAddr,false);
        }
    }
    /**
     * 导入检验是否有更多的主地址
     * @param user
     */
    public static List<BaseImportAction.ImportError> handleCheckManyMainAddrByImport(User user, List<BaseImportDataAction.ImportData> dataList){
        if(!AccountUtil.isOpenAccountAddrConfig(user)){
            return  new ArrayList<>();
        }
        //按客户id分类
        Map<String,List<BaseImportDataAction.ImportData>> map = new HashMap<>();
        dataList.stream().forEach(x->{
            IObjectData data = x.getData();
            if(ObjectUtils.isNotEmpty(data.get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName()))){
                String accountId = AccountUtil.getStringValue(data,AccountAddrConstants.Field.ACCOUNT_ID.getApiName(),"");
                map.computeIfAbsent(accountId, k -> new ArrayList<>()).add(x);
            }
        });
        if(CollectionUtils.isEmpty(map.values())){
            return new ArrayList<>();
        }
        List<BaseImportAction.ImportError> importErrors = new ArrayList<>();
        for(List<BaseImportDataAction.ImportData> detailDataList:map.values()){
            int num = 0;
            int shipNum = 0;
            for(int i = 0;i<detailDataList.size();i++){
                if(AccountUtil.getBooleanValue(detailDataList.get(i).getData(),AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(), false)){
                    num++;
                }
                if(AccountUtil.getBooleanValue(detailDataList.get(i).getData(),AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName(), false)){
                    shipNum++;
                }
            }
            if(num>1){
                detailDataList.stream().forEach(x->{
                    importErrors.add(new BaseImportAction.ImportError(x.getRowNo(),I18N.text(SFA_NOT_ALLOW_MANY_MAIN_ADDR)));
                });
            }
            if(shipNum>1){
                detailDataList.stream().forEach(x->{
                    importErrors.add(new BaseImportAction.ImportError(x.getRowNo(),I18N.text(SFA_NOT_ALLOW_MANY_SHIP_ADDR)));
                });
            }
        }
        return importErrors;
    }

    public static void bulkHandleMainAddrSetNonMain(User user, List<IObjectData> accountAddrDataList,IObjectDescribe describe,String actionLabelKey){
        if (CollectionUtils.isEmpty(accountAddrDataList)) {
            return;
        }
        if(!AccountUtil.isOpenAccountAddrConfig(user)){
            return ;
        }
        //开通之后处理，将其他默认收货地址 改为 非收货地址
        bulkHandleShipAddrSetNonShip(user,accountAddrDataList,describe,actionLabelKey);
        //开通之后处理，将其他主地址改为 非主地址
        List<IObjectData> mainAddrList = filterAccountIdAndMainIsNotNull(accountAddrDataList);
        if(CollectionUtils.isEmpty(mainAddrList)){
            log.info("accountAddrDataList not have main addr ");
            return;
        }
        log.warn("bulkHandleMainAddrSetNonMain isOpenAccountAddrConfig is true");
        mainAddrList.stream().forEach(x->{
            String accountId = AccountUtil.getStringValue(x,AccountAddrConstants.Field.ACCOUNT_ID.getApiName(),"");
            handleMainAddrSetNonMain(user,accountId,x.getId(),describe,describe.getApiName(),actionLabelKey);
        });
    }

    public static List<IObjectData> filterAccountIdAndMainIsNotNull(List<IObjectData> accountAddrDataList){
        return accountAddrDataList.stream().filter(m -> !Strings.isNullOrEmpty(m.get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), String.class))
                && AccountUtil.getBooleanValue(m,AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(),false)).collect(Collectors.toList());
    }


    public static void bulkHandleShipAddrSetNonShip(User user, List<IObjectData> accountAddrDataList,IObjectDescribe describe,String actionLabelKey){
        if (CollectionUtils.isEmpty(accountAddrDataList)) {
            return;
        }
        if(!AccountUtil.isOpenAccountAddrConfig(user)){
            return ;
        }
        log.warn("bulkHandleShipAddrSetNonShip isOpenAccountAddrConfig is true");
        List<IObjectData> mainAddrList = accountAddrDataList.stream().filter(m -> !Strings.isNullOrEmpty(m.get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), String.class))
                && AccountUtil.getBooleanValue(m,AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName(),false)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(mainAddrList)){
            log.info("bulkHandleShipAddrSetNonShip accountAddrDataList not have main addr ");
            return;
        }
        mainAddrList.stream().forEach(x->{
            String accountId = AccountUtil.getStringValue(x,AccountAddrConstants.Field.ACCOUNT_ID.getApiName(),"");
            handleShipAddrSetNonMain(user,accountId,x.getId(),describe,describe.getApiName(),actionLabelKey);
        });
    }
}
