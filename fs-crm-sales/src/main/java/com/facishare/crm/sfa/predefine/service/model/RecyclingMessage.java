package com.facishare.crm.sfa.predefine.service.model;

import lombok.Builder;
import lombok.Data;

/**
 * @Description 客户/线索 收回任务消息体
 * <AUTHOR>
 * @Date 2025-01-07
 */
@Data
@Builder
public class RecyclingMessage {

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 对象ID
     */
    private String objectId;

    /**
     * 对象API名称
     * 客户:AccountObj, 线索:LeadsObj
     */
    private String objectApiName;

    /**
     * 转移目标ID
     * 客户为：公海id
     * 线索为：线索池id
     */
    private String targetId;

    /**
     * 功能API名称
     */
    private String functionApiName;

    /**
     * 回收原因类型
     */
    private Integer recyclingReasonType;

    /**
     * 回收设置的天数
     */
    private Integer recyclingDays;
}
