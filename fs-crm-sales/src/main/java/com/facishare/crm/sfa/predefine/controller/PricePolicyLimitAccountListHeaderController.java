package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ListHeaderUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Lists;


public class PricePolicyLimitAccountListHeaderController extends StandardListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        ListHeaderUtils.removeButton(result, Lists.newArrayList(ObjectAction.INTELLIGENTFORM.getActionCode()));
        return result;
    }
}