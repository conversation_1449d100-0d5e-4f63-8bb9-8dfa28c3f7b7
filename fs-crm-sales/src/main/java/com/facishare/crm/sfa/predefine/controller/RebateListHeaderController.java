package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Lists;

public class RebateListHeaderController extends StandardListHeaderController {


    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        removeButton(result);
        return result;
    }

    private void removeButton(Result result) {
        CouponUtils.removeButton(result, Lists.newArrayList(ObjectAction.ENTER_ACCOUNT.getActionCode(), ObjectAction.BULK_RELATE.getActionCode(), ObjectAction.RELATE.getActionCode(), ObjectAction.BULK_DISRELATE.getActionCode()));
    }


}
