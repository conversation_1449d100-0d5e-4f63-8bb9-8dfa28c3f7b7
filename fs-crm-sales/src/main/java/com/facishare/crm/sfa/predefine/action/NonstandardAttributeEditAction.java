package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Created by luxin on 2018/11/12.
 */
@Slf4j
public class NonstandardAttributeEditAction extends StandardEditAction {
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String type = objectData.get("type", String.class);
        String defaultValue = objectData.get("default_value", String.class);
        if (StringUtils.isNotBlank(defaultValue) && Objects.equals(type, "1")) {
            boolean bool = AttributeUtils.checkNumber(defaultValue);
            if(!bool){
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NON_ATTR_DEFAULT_VALUE_WARN));
            }
        }
    }
}
