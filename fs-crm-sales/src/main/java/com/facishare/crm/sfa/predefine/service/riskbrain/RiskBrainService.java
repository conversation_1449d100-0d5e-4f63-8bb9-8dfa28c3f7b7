package com.facishare.crm.sfa.predefine.service.riskbrain;

import cn.com.antcloud.api.AntFinTechApiClient;
import cn.com.antcloud.api.riskplus.v1_0.request.GetLoginTokenRequest;
import cn.com.antcloud.api.riskplus.v1_0.response.GetLoginTokenResponse;
import cn.com.antcloud.api.riskplus.v1_0.response.QueryGeneralResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.riskbrain.model.RiskBrainModel;
import com.facishare.crm.sfa.lto.riskbrain.service.RiskBrainCommonService;
import com.facishare.crm.sfa.model.AccountRiskBrainModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.SFARiskBrainModel;
import com.facishare.crm.sfa.predefine.service.riskbrain.model.RiskBrain;
import com.facishare.crm.sfa.utilities.constant.RiskBrainConstants;
import com.facishare.crm.sfa.utilities.util.DateUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.I18NUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@ServiceModule("risk_brain_service")
@Service
@Slf4j
public class RiskBrainService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private RiskBrainCommonService riskBrainCommonService;

    public Map<String, JSONObject> getEventInfoMappingMap() {
        Map<String, JSONObject> map = new HashMap<>();
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.risk.event.config", new JSONObject());
        if (response.isSuccess()) {
            JSONObject data = JSON.parseObject(response.getData());
            if (data == null) {
                return map;
            }
            JSONObject queryResult = data.getJSONObject("queryResult");
            if (queryResult == null) {
                return map;
            }
            JSONArray configInfoArr = queryResult.getJSONArray("configInfo");
            if (configInfoArr == null) {
                return map;
            }
            for (int i = 0; i < configInfoArr.size(); i++) {
                JSONObject configInfo = configInfoArr.getJSONObject(i);
                map.put(configInfo.getString("eventId"), configInfo);
            }
        }
        return map;
    }

    @ServiceMethod("getToken")
    public RiskBrainModel.Result getToken(ServiceContext context, RiskBrain.CompanyParam param) {
        String tenantId = context.getTenantId();
        RiskBrainModel.Result result = new RiskBrainModel.Result();
        RiskBrainModel.Arg paramArg = riskBrainCommonService.getRiskBrainUserInfo(tenantId);
        GetLoginTokenRequest request = new GetLoginTokenRequest();
        request.setUsername(paramArg.getUserName());
        request.setPassword(paramArg.getPassword());
        // 发送请求，并获取结果
        try {
            GetLoginTokenResponse response = riskBrainCommonService.queryGeneral(request);
            if (!response.isSuccess()) {
                log.error("RiskBrainService getToken is null. response:{}", JSON.toJSONString(response));
                result.setMsg(response.getResultMsg());
                return result;
            }
            result.setToken(response.getLogintoken());
            if (StringUtils.isEmpty(param.getKeyword())) {
                param.setKeyword(param.getName());
            }
            fill(result, param.getKeyword());
        } catch (InterruptedException e) {
            log.error("蚂蚁风险获取token异常", e);
            Thread.currentThread().interrupt();
        }
        return result;
    }

    private void fill(RiskBrainModel.Result result, String name) {
        if (ObjectUtils.isEmpty(name)) {
            result.setMsg(String.format(I18N.text(SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL), I18NUtil.getFieldName(SFAPreDefineObject.Account.getApiName(), "uniform_social_credit_code")));
            return;
        }
        JSONObject request = new JSONObject();
        request.put("keyword", name);
        QueryGeneralResponse responseCompany = riskBrainCommonService.queryGeneral("irap.iframe.company.id.transform", request);
        if (ObjectUtils.isEmpty(responseCompany.getData())) {
            log.warn("RiskBrainService login responseCompany:{}", JSONObject.toJSONString(responseCompany));
            result.setMsg(responseCompany.getResultMsg());
            return;
        }
        JSONObject json = JSONObject.parseObject(responseCompany.getData());
        if (json.containsKey("queryResult")) {
            JSONObject itemMap = json.getJSONObject("queryResult");
            String companyId = itemMap.getString("mct_one_id");
            result.setCompanyld(companyId);
        }
    }

    @ServiceMethod("initUser")
    public RiskBrainModel.Result initUser(ServiceContext context, RiskBrainModel.Arg arg) {
        RiskBrainModel.Result result = new RiskBrainModel.Result();
        try {
            if (ObjectUtils.isEmpty(arg.getTenantId())) {
                result.setMsg("参数错误");// ignoreI18n
                return result;
            }
            User user = new User(arg.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            serviceFacade.upsertTenantConfig(user, RiskBrainConstants.KEY, JSONObject.toJSONString(arg), ConfigValueType.JSON);
        } catch (Exception e) {
            log.error("RiskBrainService initUser error e:", e);
        }
        result.setMsg("初始化成功");// ignoreI18n
        return result;
    }


    @ServiceMethod("pushAccountInfo")
    public RiskBrainModel.Result pushAccountInfo(ServiceContext context, RiskBrainModel.PushArg pushArg) {
        RiskBrainModel.Result result = new RiskBrainModel.Result();
        if (CollectionUtils.isEmpty(pushArg.getUniformSocialCreditCode())) {
            result.setMsg("参数错误!");// ignoreI18n
            return result;
        }
        RiskBrainModel.Arg userArg = riskBrainCommonService.getRiskBrainUserInfo(context.getTenantId());
        // 初始化Client
        AntFinTechApiClient antFinTechApiClient = riskBrainCommonService.getAntFinTechApiClient();

        List<List<String>> codeList = Lists.partition(pushArg.getUniformSocialCreditCode(), 100);
        for (List<String> codes : codeList) {
            JSONObject request = new JSONObject();
            request.put("unMonitorRetainOrg", 1);
            if (RiskBrainConstants.PushType.ADD.getCode().equals(pushArg.getPushType())) {
                request.put("monitorUcCodeList", codes);
                request.put("unMonitorUcCodeList", new ArrayList<>());
            } else if (RiskBrainConstants.PushType.DELETE.getCode().equals(pushArg.getPushType())) {
                request.put("unMonitorUcCodeList", codes);
                request.put("monitorUcCodeList", new ArrayList<>());
            } else {
                log.error("RiskBrainService pushAccountInfo pushType 错误 tenantId:{}, pushArg：{}", context.getTenantId(), JSONObject.toJSONString(pushArg));
                return result;
            }
            request.put("tenantCode", userArg.getOriginalTenantId());
            QueryGeneralResponse responseCompany = riskBrainCommonService.queryGeneral(antFinTechApiClient, "irap.company.update.monitor.info", request);
            if (!responseCompany.isSuccess()) {
                log.error("RiskBrainAsyncService getInvolvedAccount error msg:{}", responseCompany.getResultMsg());
                break;
            }
        }
        return result;
    }

    @ServiceMethod("getEventListByAccount")
    public SFARiskBrainModel.ResultInfo getEventListByAccount(ServiceContext context, SFARiskBrainModel.Arg arg) {
        if (ObjectUtils.isEmpty(arg) || ObjectUtils.isEmpty(arg.getObjectId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONFIG_PARAMETER_ERROR));
        }
        SFARiskBrainModel.ResultInfo resultInfo = new SFARiskBrainModel.ResultInfo();
        IObjectData data = serviceFacade.findObjectData(context.getUser(), arg.getObjectId(), SFAPreDefineObject.Account.getApiName());
        if (ObjectUtils.isEmpty(data)) {
            log.error("RiskBrainService getEventListByAccount data is null dataId:{}", arg.getObjectId());
            return resultInfo;
        }
        AntFinTechApiClient antFinTechApiClient = riskBrainCommonService.getAntFinTechApiClient();
        int index = 0;
        int size = 100;
        resultInfo.setId(data.getId());
        resultInfo.setName(data.getName());
        List<SFARiskBrainModel.EventInfo> eventInfos = new ArrayList<>();
        Map<String, JSONObject> eventInfoMappingMap = getEventInfoMappingMap();
        for (int loopTimes = 0; true; loopTimes++) {
            if (loopTimes >= 1000) {
                log.error("蚂蚁风险查询事件循环超长");
                break;
            }
            JSONObject request = new JSONObject();
            request.put("keyword", data.get("uniform_social_credit_code"));
            request.put("offset", index);
            request.put("limit", size);
            String dateStr = DateUtils.getTimeByTimeStamp(arg.getSendTime());
            request.put("begin_time", DateUtils.getTimeByTimeStamp(dateStr, riskBrainCommonService.getQuerySecond()));
            request.put("end_time", dateStr);
            QueryGeneralResponse responseCompany = riskBrainCommonService.queryGeneral(antFinTechApiClient, "rbb.company.risk.monitor", request);
            if (!responseCompany.isSuccess()) {
                log.error("RiskBrainAsyncService getInvolvedAccount error msg:{}", responseCompany.getResultMsg());
                break;
            }
            if (ObjectUtils.isEmpty(responseCompany) || ObjectUtils.isEmpty(responseCompany.getData())) {
                break;
            }
            JSONObject object = JSONObject.parseObject(responseCompany.getData());
            if (!object.containsKey("queryResult") || ObjectUtils.isEmpty(object.get("queryResult"))) {
                break;
            }
            JSONObject queryResult = object.getJSONObject("queryResult");
            if (!queryResult.containsKey("hits") || ObjectUtils.isEmpty(queryResult.get("hits"))) {
                break;
            }
            JSONObject hits = queryResult.getJSONObject("hits");
            if (!hits.containsKey("hits") || ObjectUtils.isEmpty(hits.get("hits"))) {
                break;
            }
            JSONArray hitsArr = hits.getJSONArray("hits");
            if (hitsArr != null) {
                for (int i = 0; i < hitsArr.size(); i++) {
                    JSONObject hit = hitsArr.getJSONObject(i);
                    JSONObject source = hit.getJSONObject("_source");
                    SFARiskBrainModel.EventInfo eventInfo = new SFARiskBrainModel.EventInfo();
                    eventInfo.setEventName(source.get("event_name").toString());
                    eventInfo.setEventDetailTime(source.get("event_detail_time").toString());
                    eventInfo.setContent(splitContent(source, eventInfoMappingMap));
                    eventInfos.add(eventInfo);
                }
            }
            if (size > hits.getIntValue("total")) {
                break;
            }
            index++;
        }
        resultInfo.setObjectNameApi(SFAPreDefineObject.Account.getApiName());
        resultInfo.setItems(eventInfos);
        return resultInfo;
    }

    public String splitContent(JSONObject eventInfo, Map<String, JSONObject> eventInfoMappingMap) {
        if (ObjectUtils.isEmpty(eventInfo) || ObjectUtils.isEmpty(eventInfo.get("event_detail_info"))) {
            return "";
        }
        JSONObject eventInfoMapping = eventInfoMappingMap.get(eventInfo.getString("event_id"));
        if (ObjectUtils.isEmpty(eventInfoMapping.get(RiskBrainConstants.EventInfoMapping.ATTR_DEFINE))) {
            return "";
        }
        JSONArray attrDefineList = eventInfoMapping.getJSONArray(RiskBrainConstants.EventInfoMapping.ATTR_DEFINE);
        Map<String, String> attrDefineMap = new HashMap<>();
        for (int i = 0; i < attrDefineList.size(); i++) {
            JSONObject attrDefine = attrDefineList.getJSONObject(i);
            attrDefineMap.put(attrDefine.getString(RiskBrainConstants.EventInfoAttrDefine.COLUMN), attrDefine.getString(RiskBrainConstants.EventInfoAttrDefine.TITLE));
        }
        StringBuilder sb = new StringBuilder();
        JSONObject eventDetailInfoMap = eventInfo.getJSONObject("event_detail_info");
        eventDetailInfoMap.keySet().forEach(key -> {
            if (!attrDefineMap.containsKey(key) && ObjectUtils.isEmpty(attrDefineMap.get(key))) {
                log.warn("RiskBrainService splitContent attrDefineMap is null event_id:{},key:{}", eventInfo.get("event_id"), key);
                return;
            }
            sb.append(attrDefineMap.get(key));
            sb.append(":").append(eventDetailInfoMap.get(key)).append("; ");
        });
        return sb.toString();
    }

    /**
     * 获取风险画像余额
     *
     * @param context
     * @return
     */
    @ServiceMethod("riskAmount")
    public AccountRiskBrainModel.Amount getRiskAmount(ServiceContext context) {
        String tenantId = context.getTenantId();
        AccountRiskBrainModel.Amount res = new AccountRiskBrainModel.Amount();
        res.setTotalAmount(0);
        res.setUsedAmount(0);
        JSONObject param = new JSONObject();
        param.put("clientName", tenantId);
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.create.quota.query", param);
        if (response.isSuccess()) {
            JSONObject data = JSON.parseObject(response.getData());
            JSONObject result = data.getJSONObject("queryResult");
            if (result != null) {
                res.setTotalAmount(result.getIntValue("queryCompanyQuota"));
                res.setUsedAmount(result.getIntValue("usedQueryCompanyQuota"));
                String expiryTime = result.getString("expiryTime");
                if (!StringUtils.isEmpty(expiryTime)) {
                    res.setExpirationTime(expiryTime.split(" ")[0]);
                }
            }
        }
        return res;
    }

    /**
     * 开启风险画像
     *
     * @param tenantId
     * @param ucCode
     * @return
     */
    public AccountRiskBrainModel.EnablePorTraitResult enableRiskPortrait(String tenantId, String ucCode) {
        if (StringUtils.isEmpty(ucCode)) {
            throw new ValidateException(String.format(I18N.text(SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL), I18N.text("AccountObj.field.uniform_social_credit_code.label")));
        }
        AccountRiskBrainModel.EnablePorTraitResult res = new AccountRiskBrainModel.EnablePorTraitResult();
        res.setDeductResult(false);
        JSONObject param = new JSONObject();
        param.put("clientName", tenantId);
        param.put("ucCode", ucCode);
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.deduct.quota.query", param);
        if (response.isSuccess()) {
            JSONObject data = JSON.parseObject(response.getData());
            JSONObject result = data.getJSONObject("queryResult");
            res.setDeductResult(result.getBoolean("deductResult"));
            res.setResultMsg(result.getString("resultMsg"));
            res.setOrderId(result.getString("orderId"));
            try {
                String expiryTime = result.getString("expiryTime");
                if (!StringUtils.isEmpty(expiryTime)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date = sdf.parse(expiryTime);
                    res.setExpiryTime(date.getTime());
                }
            } catch (Exception e) {
                log.error("风险大脑时间解析异常", e);
            }
        } else {
            res.setResultMsg(response.getResultMsg());
        }
        if (!Boolean.TRUE.equals(res.getDeductResult())) {
            log.warn("开启风险画像[{}]", res.getResultMsg());
            throw new ValidateException(res.getResultMsg());
        }
        return res;
    }

    /**
     * 开启/关闭 风险监控
     *
     * @param tenantId
     * @param monitorUcCodeList
     * @param unMonitorUcCodeList
     */
    public void monitor(String tenantId, List<String> monitorUcCodeList, List<String> unMonitorUcCodeList) {
        if (!CollectionUtils.isEmpty(monitorUcCodeList)) {
            monitorUcCodeList = monitorUcCodeList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(unMonitorUcCodeList)) {
            unMonitorUcCodeList = unMonitorUcCodeList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(monitorUcCodeList) && CollectionUtils.isEmpty(unMonitorUcCodeList)) {
            throw new ValidateException(String.format(I18N.text(SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL), I18N.text("AccountObj.field.uniform_social_credit_code.label")));
        }
        RiskBrainModel.Arg paramArg = riskBrainCommonService.getRiskBrainUserInfo(tenantId);
        JSONObject param = new JSONObject();
        param.put("unMonitorRetainOrg", 1);
        param.put("rbbCode", paramArg.getTenantCode());
        param.put("monitorUcCodeList", monitorUcCodeList);
        param.put("unMonitorUcCodeList", unMonitorUcCodeList);
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.company.update.monitor.info", param);
        if (!response.isSuccess()) {
            log.warn("新增/取消监控企业失败[{}]", response.getResultMsg());
            throw new ValidateException(response.getResultMsg());
        }
    }

    /**
     * 获取风险默认评分
     *
     * @param tenantId
     * @param ucCode
     * @return
     */
    public Double getCredits(String tenantId, String ucCode) {
        if (StringUtils.isEmpty(ucCode)) {
            throw new ValidateException(String.format(I18N.text(SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL), I18N.text("AccountObj.field.uniform_social_credit_code.label")));
        }
        AccountRiskBrainModel.EnablePorTraitResult res = new AccountRiskBrainModel.EnablePorTraitResult();
        res.setDeductResult(false);
        JSONObject param = new JSONObject();
        param.put("keyword", ucCode);
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("rbb.company.evaluate.model.general.v2", param);
        if (response.isSuccess()) {
            JSONObject data = JSON.parseObject(response.getData());
            JSONObject result = data.getJSONObject("queryResult");
            return result.getDouble("credits");
        }
        return null;
    }

    /**
     * 添加客商接口 使用授信功能必须添加到客商
     *
     * @param tenantId
     * @param customerUcCodeList
     * @param unCustomerUcCodeList
     * @return
     */
    public void addCompany(String tenantId, List<String> customerUcCodeList, List<String> unCustomerUcCodeList) {
        if (!CollectionUtils.isEmpty(customerUcCodeList)) {
            customerUcCodeList = customerUcCodeList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(unCustomerUcCodeList)) {
            unCustomerUcCodeList = unCustomerUcCodeList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(customerUcCodeList) && CollectionUtils.isEmpty(unCustomerUcCodeList)) {
            throw new ValidateException(String.format(I18N.text(SFAI18NKeyUtil.SFA_OBJECT_DISPLAY_NAME_NOTNULL), I18N.text("AccountObj.field.uniform_social_credit_code.label")));
        }
        RiskBrainModel.Arg paramArg = riskBrainCommonService.getRiskBrainUserInfo(tenantId);
        JSONObject param = new JSONObject();
        param.put("rbbCode", paramArg.getTenantCode());
        param.put("customerUcCodeList", customerUcCodeList);
        param.put("unCustomerUcCodeList", unCustomerUcCodeList);
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.company.update.customer.info", param);
        if (!response.isSuccess()) {
            log.warn("添加客商失败[{}]", response.getResultMsg());
            throw new ValidateException(response.getResultMsg());
        }
    }

    @ServiceMethod("credit")
    public AccountRiskBrainModel.Result credit(ServiceContext context, AccountRiskBrainModel.Arg arg) {
        AccountRiskBrainModel.Result result = new AccountRiskBrainModel.Result();
        if (StringUtils.isEmpty(arg.getUcCode()) || StringUtils.isEmpty(arg.getCreditRuleId())) {
            result.setSuccess(false);
            result.setMessage(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
            return result;
        }
        String tenantId = context.getTenantId();
        JSONObject param = new JSONObject();
        param.put("clientName", tenantId);
        param.put("creditRuleId", arg.getCreditRuleId());
        param.put("keyword", arg.getUcCode());
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.account.company.credit", param);
        if (!response.isSuccess()) {
            result.setMessage(response.getResultMsg());
            result.setSuccess(false);
            return result;
        }
        JSONObject queryResult = JSON.parseObject(response.getData()).getJSONObject("queryResult");
        JSONArray dataArray = queryResult.getJSONArray("data");
        if (dataArray.isEmpty()) {
            result.setSuccess(false);
            result.setMessage(I18N.text(SFAI18NKeyUtil.DATA_IS_NULL));
            return result;
        }
        JSONObject data = dataArray.getJSONObject(0);
        if (!Boolean.TRUE.equals(data.getBoolean("success"))) {
            result.setSuccess(false);
        }
        result.setSuccess(true);
        return result;
    }
}
