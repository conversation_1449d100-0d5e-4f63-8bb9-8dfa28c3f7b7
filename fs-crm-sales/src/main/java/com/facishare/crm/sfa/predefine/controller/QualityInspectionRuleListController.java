package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class QualityInspectionRuleListController extends StandardListController {

    private static final QualityInspectionRuleService qualityInspectionRuleService = SpringUtil.getContext().getBean(QualityInspectionRuleService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        List<ObjectDataDocument> objectDataDocumentList = result.getDataList();
        qualityInspectionRuleService.rewriteListDoc(controllerContext, objectDataDocumentList);
        return result;
    }
}