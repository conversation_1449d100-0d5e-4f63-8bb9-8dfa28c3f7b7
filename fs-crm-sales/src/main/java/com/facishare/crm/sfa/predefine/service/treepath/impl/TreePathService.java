package com.facishare.crm.sfa.predefine.service.treepath.impl;

import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.CommonSearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.constant.MarketingEventConstants.LAST_MODIFIED_TIME;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.PARENT_DATA_NOT_EXISTS;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.PARENT_DATA_PATH_EMPTY;

/**
 * <AUTHOR>
 * @date 2021/11/30 16:52
 */
@Service
@Slf4j
public class TreePathService implements ITree {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;

    @Override
    public int currentTreeDepth(User user, String id, String apiName, String pathFieldName) {
        if (StringUtils.isBlank(id)) {
            return 0;
        }
        IObjectData objectData = metaDataFindServiceExt.findObjectData(user, id, apiName);
        if (objectData == null) {
            throw new ValidateException(I18N.text(PARENT_DATA_NOT_EXISTS));
        }
        if (StringUtils.isBlank(objectData.get(pathFieldName, String.class))) {
            return 0;
        }
        int length = objectData.get(pathFieldName, String.class).split("\\.").length;
        return length - 1;
    }

    public String joinPath(User user, String id, String pid, String apiName, String pathFieldName, String parentFieldApiName) {
        if (StringUtils.isBlank(pid)) {
            return id;
        }
        IObjectData parentData = metaDataFindServiceExt.findObjectData(user, pid, apiName);
        if (parentData == null) {
            throw new ValidateException(I18N.text(PARENT_DATA_NOT_EXISTS));
        }
        String parentPath = ObjectDataUtils.getValueOrDefault(parentData, pathFieldName, "");
        if (StringUtils.isBlank(parentPath)) {
            if (StringUtils.isBlank(ObjectDataUtils.getValueOrDefault(parentData, parentFieldApiName, ""))) {
                parentData.set(pathFieldName, parentData.getId());
                metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(parentData), Lists.newArrayList(pathFieldName));
                parentPath = id;
                log.warn("数据 path 为空, apiName:{}, tenant{}", apiName, user.getTenantId());
            } else {
                throw new ValidateException(I18N.text(PARENT_DATA_PATH_EMPTY));
            }
        }
        return parentPath + "." + id;
    }

    @Override
    public boolean isLoop(User user, String apiName, String id, String pid, String pathFieldApiName) {
        if (StringUtils.isBlank(pid)) {
            return false;
        }
        if (pid.equals(id)) {
            return true;
        }
        String matchValue = String.format("*.%s.*.%s.*", id, pid);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterMatch(filters, pathFieldApiName, matchValue);
        SearchTemplateQuery searchTemplateQuery = CommonSearchUtil.getSearchTemplateQuery(filters);
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy(LAST_MODIFIED_TIME, false)));
        searchTemplateQuery.setLimit(1);
        List<IObjectData> data = serviceFacade.findBySearchQuery(user, apiName, searchTemplateQuery).getData();
        return CollectionUtils.isNotEmpty(data);
    }

    public SearchTemplateQuery getTreePathMatchTemplate(List<String> ids, String fieldApiName) {
        String matchValue = String.format("*.%s.*", Joiner.on("|").join(ids));
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterMatch(filters, fieldApiName, matchValue);
        SearchTemplateQuery searchTemplateQuery = CommonSearchUtil.getSearchTemplateQuery(filters);
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy(LAST_MODIFIED_TIME, false)));
        return searchTemplateQuery;
    }

    /**
     * <id, path>
     *
     * @param user             用户
     * @param ids              数据ids
     * @param objectApiName    对象 apiName
     * @param pathFieldApiName path 字段
     * @return
     */
    public Map<String, String> getCurrentPathByIds(User user, List<String> ids, String objectApiName, String pathFieldApiName) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, DBRecord.ID, ids);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setLimit(0);
        List<IObjectData> objectDataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, objectApiName, searchTemplateQuery).getData();
        return objectDataList.stream().collect(Collectors.toMap(DBRecord::getId, d -> ObjectDataUtils.getValueOrDefault(d, pathFieldApiName, "")));
    }

    /**
     * 仅适用于没有建立上级节点的数据 添加上级节点后path路径的初始化
     *
     * @param user               用户
     * @param objectData         待变更的数据
     * @param objectApiName      对象apiName
     * @param pathFieldApiName   path字段ApiName
     * @param parentFieldApiName 上级节点字段 apiName
     */
    public void initDataPath(User user, IObjectData objectData, String objectApiName, String pathFieldApiName, String parentFieldApiName) {
        String pid = ObjectDataUtils.getValueOrDefault(objectData, parentFieldApiName, "");
        String path = joinPath(user, objectData.getId(), pid, objectApiName, pathFieldApiName, parentFieldApiName);
        objectData.set(pathFieldApiName, path);
    }

    public void changePathByParentId(User user, String objectId, String parentId, String objectApiName, String pathFieldApiName) {
        SearchTemplateQuery searchTemplateQuery = getTreePathMatchTemplate(Lists.newArrayList(objectId), pathFieldApiName);
        metaDataFindServiceExt.dealDataByTemplate(user, objectApiName, searchTemplateQuery, dataList -> changePath(user, dataList, objectId, parentId, objectApiName, pathFieldApiName));
    }

    private void changePath(User user, List<IObjectData> dataList, String objectId, String pid, String objectApiName, String pathFieldApiName) {
        if (StringUtils.isBlank(pid)) {
            for (IObjectData data : dataList) {
                String original = ObjectDataUtils.getValueOrDefault(data, pathFieldApiName, "");
                int index = original.indexOf(objectId);
                String newPath = original.substring(index);
                data.set(pathFieldApiName, newPath);
            }
        } else {
            IObjectData objectData = metaDataFindServiceExt.findObjectData(user, pid, objectApiName);
            String path = ObjectDataUtils.getValueOrDefault(objectData, pathFieldApiName, "");
            for (IObjectData data : dataList) {
                String original = ObjectDataUtils.getValueOrDefault(data, pathFieldApiName, "");
                int index = original.indexOf(objectId);
                String substring = original.substring(index);
                String newPath = path + "." + substring;
                data.set(pathFieldApiName, newPath);
            }
        }
        metaDataFindServiceExt.bulkUpdateByFields(user, dataList, Lists.newArrayList(pathFieldApiName));
    }


    /**
     * 根据 objectDataList 组成树，更新path字段
     *
     * @param user               用户
     * @param objectDataList     数据列表
     * @param pathFieldApiName   path 字段 apiName
     * @param parentFieldApiName parent 字段 apiName
     */
    @NotNull
    public List<IObjectData> fillPathWithTheseDataListNoUpdate(User user, List<IObjectData> objectDataList, String pathFieldApiName, String parentFieldApiName) {
        Map<String, IObjectData> dataMap = objectDataList.stream().collect(Collectors.toMap(DBRecord::getId, d -> d));
        for (IObjectData data : objectDataList) {
            String originalPath = ObjectDataUtils.getValueOrDefault(data, pathFieldApiName, "");
            if (StringUtils.isNotBlank(originalPath)) {
                continue;
            }
            String path = findPath(user, data, dataMap, parentFieldApiName, pathFieldApiName);
            if (StringUtils.isNotEmpty(path)) {
                data.set(pathFieldApiName, path);
            }
        }
        return objectDataList;
    }

    private String findPath(User user, IObjectData data, Map<String, IObjectData> dataMap, String parentFieldApiName, String pathFieldApiName) {
        String pid = Optional.ofNullable(data.get(parentFieldApiName)).orElse("").toString();
        StringBuilder path = new StringBuilder(data.getId());
        while (StringUtils.isNotBlank(pid)) {
            IObjectData parentData = dataMap.get(pid);
            if (parentData == null) {
                log.warn("父分类路径存在多个上级节点, tenant:{}, pid:{}, dataPath:{}", user.getTenantId(), pid, path);
                break;
            }
            String parentDataPath = ObjectDataUtils.getValueOrDefault(parentData, pathFieldApiName, "");
            if (StringUtils.isNotBlank(parentDataPath)) {
                String tempPath = parentDataPath + ".";
                path.insert(0, tempPath);
                break;
            }
            String stitchPath = pid + ".";
            path.insert(0, stitchPath);
            pid = Optional.ofNullable(parentData.get(parentFieldApiName)).orElse("").toString();
            if (pid.equals(parentData.getId())) {
                // 成环了
                log.error("data parentId equals id, id:{}, path ring, tenant:{}", parentData.getId(), user.getTenantId());
                break;
            }
        }
        return path.toString();
    }

    public List<IObjectData> getChildren(User user, @NotNull String pathFieldApiName, @NotNull List<String> parentIdList) {
        SearchTemplateQuery searchTemplateQuery = getTreePathMatchTemplate(parentIdList, pathFieldApiName);
        List<IObjectData> categoryList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, ProductCategoryModel.Metadata.API_NAME, searchTemplateQuery).getData();
        if (CollectionUtils.isEmpty(categoryList)) {
            return Lists.newArrayList();
        }
        Set<String> childCategoryIdList = Sets.newHashSet();
        for (IObjectData category : categoryList) {
            String path = ObjectDataUtils.getValueOrDefault(category, ProductCategoryModel.Filed.PRODUCT_CATEGORY_PATH, "");
            for (String parentId : parentIdList) {
                int index = path.indexOf(parentId);
                if (index == -1) {
                    continue;
                }
                String substring = path.substring(index);
                String[] childCategoryArray = substring.split("\\.");
                childCategoryIdList.addAll(Lists.newArrayList(childCategoryArray));
            }
        }
        parentIdList.forEach(childCategoryIdList::remove);
        return categoryList.stream().filter(c -> childCategoryIdList.contains(c.getId())).collect(Collectors.toList());
    }
}
