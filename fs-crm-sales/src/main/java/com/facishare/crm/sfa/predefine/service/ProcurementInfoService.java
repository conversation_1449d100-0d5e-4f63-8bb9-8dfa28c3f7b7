package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.procurement.qianlima.QlmProcurementService;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.action.model.ProcurementAllocate;
import com.facishare.crm.sfa.predefine.action.model.ProcurementDetail;
import com.facishare.crm.sfa.predefine.action.model.ProcurementListResult;
import com.facishare.crm.sfa.predefine.service.Procurement.ProcurementConsumptionRecordsService;
import com.facishare.crm.sfa.predefine.service.Procurement.module.Procurement;
import com.facishare.crm.sfa.predefine.service.Procurement.module.ProcurementConsumptionRecords;
import com.facishare.crm.sfa.predefine.service.Procurement.module.ProcurementUtils;
import com.facishare.crm.sfa.predefine.service.model.Duplicate.SFADuplicateSearch;
import com.facishare.crm.sfa.predefine.service.model.procurement.*;
import com.facishare.crm.sfa.task.RecalculateProducer;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.proxy.model.CountryAreaResult;
import com.facishare.crm.sfa.utilities.util.DateUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.util.CRMRemindRecordUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.dao.pg.entity.metadata.RelevantTeam;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.bi.industry.args.QueryBidDetailArg;
import com.fxiaoke.bi.industry.args.QueryBidListPageArg;
import com.fxiaoke.bi.industry.client.IndustryClient;
import com.fxiaoke.bi.industry.entity.BidDetailInfo;
import com.fxiaoke.bi.industry.entity.BidListPageEntity;
import com.fxiaoke.bi.industry.entity.BidListPageInfo;
import com.fxiaoke.bi.industry.entity.HttpResponseResult;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.action.model.ProcurementDetail.*;
import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants.*;
import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants.ProcurementCommonConstants.*;
import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants.ProcurementContact.*;
import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants.ProcurementEnterprise.CREDIT_CODE;
import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants.ProcurementInfo.NAME;
import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants.ProcurementInfo.*;
import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants.ProcurementInfoLines.COMPETITIVE_PRODUCTS;
import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants.ProcurementInfoLines.PROCUREMENT_INFO_ID;
import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementRule.PROVINCE;
import static com.facishare.crm.sfa.utilities.constant.objectdata.DbSystemRecord.LIFE_STATUS;
import static com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants.BUY_DATA;
import static com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants.QLM_BUY_DATA;
import static com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants.QLM_BUY_DATA_STATUS;

@Slf4j
@Component
public class ProcurementInfoService {

    @Autowired
    protected ServiceFacade serviceFacade;

    @Autowired
    protected ICommonSqlService commonSqlService;

    @Autowired
    private IndustryClient industryClient;

    @Autowired
    MetaDataFindServiceExt metaDataFindServiceExt;

    @Autowired
    IObjectDataService objectDataService;

    @Autowired
    SpecialTableMapper specialTableMapper;

    @Autowired
    RecalculateProducer recalculateProducer;

    @Autowired
    SFADuplicateSearchService duplicateSearchService;

    @Autowired
    QlmProcurementService qlmProcurementService;

    @Autowired
    SfaRateLimiterService sfaRateLimiterService;

    @Autowired
    CountryAreaService countryAreaService;

    @Autowired
    ProcurementConsumptionRecordsService procurementConsumptionRecordsService;

    @Autowired
    private RedissonServiceImpl redissonService;

    @Autowired
    private ProcurementRuleService procurementRuleService;
    @Autowired
    private CRMNotificationService crmNotificationService;
    @Autowired
    private BiddingSubscriptionRuleService biddingSubscriptionRuleService;
    @Autowired
    private RedissonClient redissonClient;

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config", config -> {
            buildMap(CALLER_TYPE_MAP, String.class, config.get("qlm_caller_type_map", "[]"));
            buildMap(BID_TYPE_MAP, String.class, config.get("qlm_bid_type_map", "[]"));
            buildMap(BID_SUB_TYPE_MAP, String.class, config.get("qlm_bid_sub_type_map", "[]"));
            buildMap(PROGRESS_STAGE_MAP, String.class, config.get("qlm_progress_stage_map", "[]"));
            buildMap(CALLER_SUB_TYPE_MAP, String.class, config.get("qlm_caller_sub_type_map", "[]"));
        });
    }

    public static <T> void buildMap(Map<String, T> map, Class<T> clazz, String json) {
        JSONArray options = JSON.parseArray(json);
        for (int i = 0; i < options.size(); i++) {
            JSONObject option = options.getJSONObject(i);
            map.put(option.getString("thirdParty"), option.getObject("value", clazz));
        }
    }

    public void syncQlmConsumptionRecords(String tenantId, LocalDate localDate) {
        List<ProcurementConsumptionRecords.ConsumptionRecords> dataList = new ArrayList<>();
        QlmProcurementService.Account account = qlmProcurementService.getAccount(tenantId);
        JSONObject queryParam = new JSONObject();
        queryParam.put("accountKey", account.getAccountKey());
        queryParam.put("chargingStatus", 1);
        queryParam.put("startTime", localDate + " 00:00:00");
        queryParam.put("endTime", localDate + " 23:59:59");
        queryParam.put("pageSize", 100);
        for (int i = 1; i < 1000; i++) {
            queryParam.put("pageIndex", i);
            QlmProcurementService.Result response = qlmProcurementService.commonPost("/open/detail/browsing", queryParam);
            if (!response.isSuccess()) {
                log.error("千里马获取详情失败" + response.getMsg());
                return;
            }
            JSONObject data = response.getData(JSONObject.class);
            List<JSONObject> currentPageDataList = data.getJSONArray("list").toJavaList(JSONObject.class);
            for (JSONObject consumptionRecord : currentPageDataList) {
                dataList.add(new ProcurementConsumptionRecords.ConsumptionRecords(
                        consumptionRecord.getString("dataId"),
                        consumptionRecord.getLong("createTime"),
                        User.SUPPER_ADMIN_USER_ID,
                        ProcurementConsumptionRecords.ConsumptionRecordsAction.SYNC,
                        null,
                        consumptionRecord.getIntValue("chargingStatus") == 0 ? 0 : 1
                ));
            }
            if (CollectionUtils.isEmpty(currentPageDataList) || dataList.size() >= data.getLongValue("totalSize")) {
                break;
            }
        }
        procurementConsumptionRecordsService.deductAndDeduplication(tenantId, localDate, dataList);
    }

    public Procurement.PreviewPage queryBySubscriberForQlm(String tenantId, Procurement.PreviewParam param) {
        Procurement.PreviewPage page = new Procurement.PreviewPage();
        JSONObject queryParam = ProcurementUtils.toQueryParam(param.getParam().toObjectData());
        queryParam.put("pageIndex", param.getPageNo());
        queryParam.put("pageSize", param.getPageSize());
        queryParam.put("timeType", "2");//固定查询近一周
        QlmProcurementService.Account account = qlmProcurementService.getAccount(tenantId);
        if (account == null) {
            throw new ValidateException(I18N.text("sfa.no.qianlima.account.found"));
        }
        queryParam.put("accountKey", account.getAccountKey());
        QlmProcurementService.Result response = qlmProcurementService.commonPost("/open/subscriber/search/v2", queryParam);
        if (!response.isSuccess()) {
            throw new ValidateException(I18N.text("sfa.qianlima.error") + response.getMsg());
        }
        JSONObject responsePage = response.getData(JSONObject.class);
        page.setTotalSize(responsePage.getInteger("totalSize"));
        page.setTotalPage(responsePage.getInteger("totalPage"));
        page.setPageNo(responsePage.getInteger("pageNo"));
        page.setPageSize(responsePage.getInteger("pageSize"));
        page.setDataList(new ArrayList<>());
        for (JSONObject responseData : responsePage.getJSONArray("list").toJavaList(JSONObject.class)) {
            Procurement.PreviewData data = new Procurement.PreviewData();
            data.setTitle(responseData.getString("title"));
            data.setBidSubType(responseData.getString("noticeSegmentName"));
            data.setArea(responseData.getString("area"));
            data.setBidId(responseData.getLong("dataId"));
            if (responseData.containsKey("publishTime") && responseData.getLong("publishTime") != null) {
                data.setPublishTime(DateUtils.getTimeByTimeStamp(responseData.getLong("publishTime")));
            }
            page.getDataList().add(data);
        }
        return page;
    }

    public Procurement.PreviewPage queryBySubscriberForZl(String tenantId, QueryBidListPageArg requestParam, Procurement.PreviewParam param) {
        Procurement.PreviewPage page = new Procurement.PreviewPage();
        try {
            HttpResponseResult<BidListPageInfo> result = industryClient.queryBidListPageByKeywords(requestParam);
            if ("401".equals(result.getErrorCode()) || (200 != result.getStatus() && StringUtils.isNotBlank(result.getErrorMessage()))) {
                throw new ValidateException(result.getErrorMessage());
            }
            BidListPageInfo bidListPageArg = result.getResult();
            page.setTotalSize(bidListPageArg.getTotal());
            int totalPage = bidListPageArg.getTotal() / param.getPageSize();
            totalPage += bidListPageArg.getTotal() % param.getPageSize() == 0 ? 0 : 1;
            page.setTotalPage(totalPage);
            page.setPageNo(param.getPageNo());
            page.setPageSize(param.getPageSize());
            page.setDataList(new ArrayList<>());
            IObjectDescribe describe = serviceFacade.findObject(tenantId, SFAPreDefineObject.ProcurementSearch.getApiName());
            List<ISelectOption> options = ((SelectOneFieldDescribe) describe.getFieldDescribe("bid_sub_type")).getSelectOptions();
            Map<String, String> bidSubTypeMap = new HashMap<>();
            for (ISelectOption selectOption : options) {
                bidSubTypeMap.put(selectOption.getValue(), selectOption.getLabel());
            }
            for (BidListPageEntity responseData : bidListPageArg.getBidListPageEntities()) {
                Procurement.PreviewData data = new Procurement.PreviewData();
                data.setTitle(responseData.getTitle());
                data.setBidSubType(bidSubTypeMap.get(String.valueOf(responseData.getBidSubType())));
                String area = "";
                if (StringUtils.isNotEmpty(responseData.getProvince())) {
                    area = responseData.getProvince();
                }
                if (StringUtils.isNotEmpty(responseData.getCity())) {
                    if (StringUtils.isNotEmpty(area)) {
                        area += "-";
                    }
                    area += responseData.getCity();
                }
                data.setArea(area);
                data.setPublishTime(responseData.getPublishTime());
                page.getDataList().add(data);
            }
        } catch (Exception e) {
            log.error("查询知了失败", e);
        }
        return page;
    }

    public ObjectDataDocument viewQlmProcurementInfo(String tenantId, String userId, Long id) {
        QlmProcurementService.Result response = getProcurementDetailByQlm(tenantId, id);
        JSONObject data = response.getData(JSONObject.class);
        procurementConsumptionRecordsService.deduct(tenantId,
                new ProcurementConsumptionRecords.ConsumptionRecords(
                        data.getString("dataId"),
                        System.currentTimeMillis(),
                        userId,
                        ProcurementConsumptionRecords.ConsumptionRecordsAction.VIEW,
                        RequestUtil.getClientInfo(),
                        data.getBooleanValue("charging") ? 1 : 0
                )
        );
        return ProcurementUtils.toProcurementSearch(data, getSourceUrl(tenantId, data.getString("dataId")));
    }

    public ProcurementAllocate.Result receiveQlmProcurementInfo(ActionContext context, Long id) {
        String tenantId = context.getTenantId();
        QlmProcurementService.Result response = getProcurementDetailByQlm(tenantId, id);
        JSONObject data = response.getData(JSONObject.class);
        procurementConsumptionRecordsService.deduct(tenantId,
                new ProcurementConsumptionRecords.ConsumptionRecords(
                        data.getString("dataId"),
                        System.currentTimeMillis(),
                        context.getUser().getUserIdOrOutUserIdIfOutUser(),
                        ProcurementConsumptionRecords.ConsumptionRecordsAction.RECEIVE,
                        RequestUtil.getClientInfo(),
                        data.getBooleanValue("charging") ? 1 : 0
                )
        );
        ProcurementAllocate.Result result = new ProcurementAllocate.Result();
        result.setMessage(response.getMsg());
        result.setSuccess(true);
        result.setCost(data.getBooleanValue("charging") ? 1 : 0);
        ProcurementDetail detailInfo = ProcurementUtils.toProcurementDetail(data);
        lockProcurementInfoHandle(context, detailInfo);
        return result;
    }

    public QlmProcurementService.Result getProcurementDetailByQlm(String tenantId, Long id) {
        QlmProcurementService.Account account = qlmProcurementService.getAccount(tenantId);
        JSONObject queryParam = new JSONObject();
        queryParam.put("accountKey", account.getAccountKey());
        queryParam.put("dataId", id);
        QlmProcurementService.Result response = qlmProcurementService.commonPost("/open/detail/info", queryParam);
        if (!response.isSuccess()) {
            throw new ValidateException(I18N.text("sfa.qianlima.error") + response.getMsg());
        }
        return response;
    }

    public ProcurementListResult queryProcurementByQlm(String tenantId, JSONObject queryParam) {
        ProcurementListResult result = new ProcurementListResult();
        List<ObjectDataDocument> objectDataList = new ArrayList<>();
        QlmProcurementService.Account account = qlmProcurementService.getAccount(tenantId);
        if (account == null){
            throw new ValidateException(I18N.text("sfa.no.qianlima.account.found"));
        }
        queryParam.put("accountKey", account.getAccountKey());
        QlmProcurementService.Result response = qlmProcurementService.commonPost("/open/subscriber/search/v2", queryParam);
        if (!response.isSuccess()) {
            throw new ValidateException(I18N.text("sfa.qianlima.error") + response.getMsg());
        }
        JSONObject content = JSON.parseObject(response.getData());
        List<JSONObject> dataList = content.getJSONArray("list").toJavaList(JSONObject.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (JSONObject data : dataList) {
            ObjectDataDocument dataDocument = new ObjectDataDocument();
            dataDocument.put(IObjectData.ID, data.getString("dataId"));
            dataDocument.put(BID_ID, data.getString("dataId"));
            dataDocument.put("title", data.getString("title"));
            dataDocument.put("name", data.getString("title"));
            dataDocument.put("bid_type", BID_TYPE_MAP.get(data.getString("messageType")));
            dataDocument.put("bid_sub_type", BID_SUB_TYPE_MAP.get(data.getString("noticeSegmentName")));
            dataDocument.put("area", data.getString("area"));
            dataDocument.put("caller_names", data.getString("zhaoBiaoUnit"));
            dataDocument.put("caller_budget", data.getString("budgetAmountNumber"));
            dataDocument.put("winner_names", data.getString("zhongBiaoUnit"));
            dataDocument.put("winner_amount", data.getString("zhongBiaoAmount"));
            if (data.containsKey("publishTime") && data.getLong("publishTime") != null) {
                dataDocument.put("publish_time", sdf.format(new Date(data.getLong("publishTime"))));
            }
            JSONArray jsonArray = data.getJSONArray("bdKeywords");
            if (CollectionUtils.isNotEmpty(jsonArray)) {
                dataDocument.put("product_names", StringUtils.join(",", jsonArray));
            }
            dataDocument.put("object_describe_api_name", SFAPreDefineObject.ProcurementSearch.getApiName());
            if (data.containsKey("registrationEndTime") && data.getLong("registrationEndTime") != null) {
                dataDocument.put("tender_days", ProcurementUtils.getRemainder(data.getLong("registrationEndTime")));
            }
            dataDocument.put("biding_days", null);
            dataDocument.put("caller_bid_status", null);
            dataDocument.put("record_type", ProcurementUtils.QLM);
            objectDataList.add(dataDocument);
        }
        result.setDataList(objectDataList);
        result.setTotal(content.getIntValue("totalSize"));
        return result;
    }

    public String getSourceUrl(String tenantId, String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        String qlmToken = getQlmToken(tenantId, false);
        if (StringUtils.isEmpty(qlmToken)){
            return null;
        }
        if(RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest() || RequestUtil.isWXMiniProgram()){
            return QlmProcurementService.getWEB_HOST() + "/open-h5/#/detailTender/" + id + "?token=" + qlmToken;
        }
        return QlmProcurementService.getWEB_HOST() + "/open-pc/#/affiche-detail/" + id + "?token=" + qlmToken;
    }

    public String getQlmToken(String tenantId,Boolean refreshToken) {
        String redisKey = String.format("sfa_bidding_qlm_token_%s", tenantId);
        Object o = redissonClient.getBucket(redisKey).get();
        if (ObjectUtils.isNotEmpty(o) && !Boolean.TRUE.equals(refreshToken)) {
            return o.toString();
        }
        QlmProcurementService.Account account = qlmProcurementService.getAccount(tenantId);
        if (account == null) {
            return "";
        }
        JSONObject json = new JSONObject();
        json.put("accountKey", account.getAccountKey());
        json.put("accountSecret", account.getAccountSecret());
        try {
            QlmProcurementService.Result result = qlmProcurementService.commonPost("/open/account/login", json);
            if (!result.isSuccess() || ObjectUtils.isEmpty(result.getData())) {
                return "";
            }
            String token = result.getData();
            redissonClient.getBucket(redisKey).set(token, 2, TimeUnit.HOURS);
            return token;
        } catch (Exception e) {
            log.error("get qlm token error!", e);
        }
        return "";
    }

    public boolean validateProcurementInfoExists(User user, ProcurementAllocate.Arg arg) {
        List<String> list = getProcurementInfoExistIds(user, Lists.newArrayList(arg.getId()), arg.getRecordType());
        return CollectionUtils.isNotEmpty(list);
    }

    public List<String> getProcurementInfoExistIds(User user, List<String> ids) {
        return getProcurementInfoExistIds(user, ids, "default__c");
    }

    public List<String> getProcurementInfoExistIds(User user, List<String> ids, String recordType) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(ids.size());
        searchQuery.setOffset(0);
        if (ProcurementUtils.QLM.equals(recordType)) {
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, DATA_SOURCE, DATA_SOURCE_QLM);
        } else {
            SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.NEQ, DATA_SOURCE, DATA_SOURCE_QLM);
        }
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, BID_ID, ids);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, ProcurementConstants.ProcurementCommonConstants.TENANT_ID, user.getTenantId());
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, ProcurementConstants.ProcurementCommonConstants.IS_DELETED, "0");
        List<IObjectData> dataList = getObjectDataIgnoreAll(user, SFAPreDefineObject.ProcurementInfo.getApiName(), searchQuery);
        List<String> res = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            res = dataList.stream().map(e -> ObjectDataExt.of(e).get(BID_ID).toString()).collect(Collectors.toList());
        }
        return res;
    }

    // 同一时间同一个租户只会有一个任务运行，所以不用考虑并发问题
    public void syncQlmBuyData(String tenantId, String dataId) {
        ActionContext actionContext = new ActionContext(RequestContext.builder().tenantId(tenantId).user(User.systemUser(tenantId)).build(), SFAPreDefineObject.ProcurementInfo.getApiName(), null);
        IObjectData subscriberData = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, "BiddingSubscriptionRulesObj");
        if (ObjectUtils.isEmpty(subscriberData) || ObjectUtils.isEmpty(subscriberData.get(QLM_BUY_DATA))) {
            log.warn("bidding qlm buy data,qlm_buy_data is null,tenantId:{},bidding id:{}", tenantId, dataId);
            return;
        }
        JSONObject qlmBuyData = JSONObject.parseObject(subscriberData.get(QLM_BUY_DATA, String.class));
        QlmProcurementService.Account account = qlmProcurementService.getAccount(tenantId);
        int allCount = biddingSubscriptionRuleService.selectModuleParaMaxCount(tenantId, BUY_DATA);
        int balanceCount = allCount - biddingSubscriptionRuleService.getBuyHistoricalDataUsingCount(tenantId);
        // 拉数据
        int pageIndex = 0;
        int successCount = 0;
        log.info("获取千里马历史标讯数据开始,tenantId:{},订阅器id:{}", tenantId, dataId); // ignoreI18n
        external:
        while (balanceCount - successCount > 0) {
            pageIndex++;
            JSONObject pageJsonObj = new JSONObject();
            pageJsonObj.put("accountKey", account.getAccountKey());
            pageJsonObj.put("recordId", qlmBuyData.getLong("qlm_buy_data_id"));
            pageJsonObj.put("pageIndex", pageIndex);
            pageJsonObj.put("pageSize", 20);
            QlmProcurementService.Result pageResult;
            try {
                pageResult = qlmProcurementService.commonPost("/open/subscriber/export/data", pageJsonObj);
                if (!pageResult.isSuccess()) {
                    break;
                }
            } catch (ValidateException e) {
                log.warn("千里马查询历史数据失败。", e); // ignoreI18n
                break;
            }
            JSONObject pageInfo = pageResult.getData(JSONObject.class);
            JSONArray dataArray = pageInfo.getJSONArray("list");
            int pageSuccessCount = 0;
            for (int i = 0; i < dataArray.size(); i++) {
                if (balanceCount - successCount <= 0) {
                    log.warn("千里马购买历史标讯资源耗尽，结束本次获取"); // ignoreI18n
                    break external;
                }
                sfaRateLimiterService.limiter(SfaRateLimiterService.PROCUREMENT_INSERT_QLM).acquire();
                JSONObject data = dataArray.getJSONObject(i);
                // 插入数据并判断是否成功
                try {
                    ProcurementDetail procurementDetail = qlmDataToProcurementInfo(actionContext, data);
                    if (lockProcurementInfoHandle(actionContext, procurementDetail)) {
                        successCount++;
                        pageSuccessCount++;
                    }else{
                        log.warn("千里马购买历史数据重复未落库，procurementDetail:{}", procurementDetail); // ignoreI18n
                    }
                } catch (Exception e) {
                    log.error("数据落库异常", e); // ignoreI18n
                }
            }
            // 扣费
            saveQlmBuyHistoricalRemoteBill(account, qlmBuyData, successCount);
            saveQlmBuyHistoricalLocalBill(tenantId, qlmBuyData, pageIndex, dataArray.size(), pageSuccessCount);
            if (dataArray.size() < 20) {
                break;
            }
        }
        log.info("获取千里马历史标讯数据完成,tenantId:{},订阅器id:{},本次消费:{}", tenantId, dataId, successCount); // ignoreI18n
        try{
            subscriberData.set(QLM_BUY_DATA, "");
            subscriberData.set(QLM_BUY_DATA_STATUS, "102");
            serviceFacade.batchUpdateByFields(User.systemUser(tenantId), Lists.newArrayList(subscriberData), Lists.newArrayList(QLM_BUY_DATA,QLM_BUY_DATA_STATUS));
        }catch (Exception e){
            log.error("千里马购买历史数据任务状态修改失败", e); // ignoreI18n
        }
        // 发送crm通知
        String startTime = dateFormat.format(qlmBuyData.getDate("startTime"));
        String endTime = dateFormat.format(qlmBuyData.getDate("endTime"));
        List<String> memberList = procurementRuleService.getProcurementRuleMemberUserList(tenantId, subscriberData.getId());
        List<Integer> receiverIds = memberList.stream().map(Integer::parseInt).collect(Collectors.toList());
        String title = "【订阅规则】获取数据成功"; // ignoreI18n
        String msg = String.format("订阅规则 %s 获取 %s 到 %s 的数据成功，共消耗 %s 条，已从资源包内扣除。", subscriberData.getName(), startTime, endTime, successCount); // ignoreI18n
        CRMRemindRecordUtil.sendNewCRMRecordRemindSender(crmNotificationService, User.systemUser(tenantId), 13,
                receiverIds, User.SUPPER_ADMIN_USER_ID,
                title, msg,
                SFAI18NKeyUtil.SFA_BIDDING_SUBSCRIPTION_QLM_BUY_DATA_TITLE,
                Lists.newArrayList(),
                SFAI18NKeyUtil.SFA_BIDDING_SUBSCRIPTION_QLM_BUY_DATA_MSG,
                Lists.newArrayList(subscriberData.getName(), startTime, endTime, String.valueOf(successCount)),
                null, null
        );
    }

    // 本地扣费
    public void saveQlmBuyHistoricalLocalBill(String tenantId, JSONObject qlmBuyData, int pageIndex, int pageSize, int pageSuccessCount) {
        User user = User.systemUser(tenantId);
        int useCount = biddingSubscriptionRuleService.getBuyHistoricalDataUsingCount(tenantId);
        String startTime = dateFormat.format(qlmBuyData.getDate("startTime"));
        String endTime = dateFormat.format(qlmBuyData.getDate("endTime"));
        ObjectDataDocument insObjectData = new ObjectDataDocument();
        insObjectData.put("name", String.format("%s-%s", startTime, endTime));
        insObjectData.put("operation_time", qlmBuyData.getDate("task_time").getTime());
        insObjectData.put("request_count", pageSize);
        insObjectData.put("deductions", pageSuccessCount);
        insObjectData.put("cumulative_number", useCount + pageSuccessCount);
        insObjectData.put(IObjectData.DESCRIBE_API_NAME, "ProcurementQlmBuyDataBillObj");
        insObjectData.put(IObjectData.ID, String.format("%s-%s", qlmBuyData.getString("qlm_buy_data_id"), pageIndex));
        insObjectData.put(IObjectData.TENANT_ID, tenantId);
        insObjectData.put(IObjectData.RECORD_TYPE, IObjectData.RECORD_TYPE_DEFAULT);
        try {
            serviceFacade.saveObjectData(user, insObjectData.toObjectData());
        } catch (Exception e) {
            log.error("本地扣费失败，本次消耗：{}", insObjectData, e); // ignoreI18n
        }
    }

    // 远程扣费
    public void saveQlmBuyHistoricalRemoteBill(QlmProcurementService.Account account, JSONObject qlmBuyData, int successCount) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("accountKey", account.getAccountKey());
        jsonObject.put("recordId", qlmBuyData.getLong("qlm_buy_data_id"));
        jsonObject.put("dataNum", successCount);
        try {
            qlmProcurementService.commonPost("/open/subscriber/export/down", jsonObject);
        } catch (ValidateException e) {
            log.error("千里马购买历史数据同步消费记录失败。累积消费：{}条。", successCount, e); // ignoreI18n
        }
    }

    public void syncQlmProcurementInfo(ActionContext context) {
        // 更换traceId，确保每次同步，日志中打印的只有当前租户的同步信息，再分开打印出旧traceId
        String oldTraceId = TraceContext.get().getTraceId();
        TraceContext.get().setTraceId(UUID.randomUUID().toString().replace("-", ""));
        log.info("同步千里马标讯数据,tenantId:{}", context.getTenantId()); // ignoreI18n
        log.info("old traceId:{}", oldTraceId);
        String tenantId = context.getTenantId();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = serviceFacade.findTenantConfig(User.systemUser(tenantId), "biz_qlm_procurement_offset_key");
        if (startTime == null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.HOUR_OF_DAY, 0);
            calendar.add(Calendar.MINUTE, 0);
            calendar.add(Calendar.SECOND, 0);
            startTime = sdf.format(calendar.getTime());
        }
        String endTime = sdf.format(new Date());
        QlmProcurementService.Account account = qlmProcurementService.getAccount(context.getTenantId());
        // 拉取包
        syncQlmProcurementPackage(context, account, startTime, endTime);
        // 按包拉取
        syncQlmProcurementInfoByPackage(context, account);
        serviceFacade.upsertTenantConfig(User.systemUser(tenantId), "biz_qlm_procurement_offset_key", endTime, ConfigValueType.STRING);
    }

    public void syncQlmProcurementPackage(ActionContext context, QlmProcurementService.Account account, String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 更新已有的包数据
        List<IObjectData> packageDataList = selectQlmNotPushPackage(context.getTenantId());
        List<IObjectData> updatePackageList = Lists.newArrayList();
        log.info("need updated packageDataList size:{}", packageDataList.size());
        if (ObjectUtils.isNotEmpty(packageDataList)) {
            Set<Long> packageIds = new HashSet<>();
            packageDataList.forEach(item -> packageIds.add(Long.parseLong(item.getName())));
            // 把packageIds拆分为20个一组的groups
            List<List<Long>> groups = new ArrayList<>();
            List<Long> idList = new ArrayList<>(packageIds);
            for (int i = 0; i < idList.size(); i += 20) {
                groups.add(idList.subList(i, Math.min(i + 20, idList.size())));
            }
            // 分批拉取包的状态
            for (List<Long> ids : groups) {
                JSONObject queryPackage = new JSONObject();
                queryPackage.put("accountKey", account.getAccountKey());
                queryPackage.put("pushIds", ids);
                QlmProcurementService.Result packageResult = qlmProcurementService.commonPost("/open/push/get/pushPackge/status", queryPackage);
                if (packageResult.isSuccess()) {
                    packageResult.getData(JSONArray.class).forEach(item -> {
                        JSONObject itemData = (JSONObject) item;
                        String pushId = itemData.getString("pushId");
                        IObjectData objectData = null;
                        for (IObjectData o : packageDataList) {
                            if (o.getName().equals(pushId)) {
                                objectData = o;
                                break;
                            }
                        }
                        if (objectData == null) {
                            return;
                        }
                        packageDataList.remove(objectData);
                        objectData.set("ai_state", itemData.get("filter").toString());
                        updatePackageList.add(objectData);
                    });
                } else {
                    log.warn("qlm get package error.tenantId:{},msg:{}", context.getTenantId(), packageResult.getMsg());
                }
            }
        }
        if (ObjectUtils.isNotEmpty(updatePackageList)) {
            serviceFacade.batchUpdate(updatePackageList, User.systemUser(context.getTenantId()));
        }
        // 本次新增的包
        JSONObject query = new JSONObject();
        query.put("startTime", startTime);
        query.put("endTime", endTime);
        query.put("accountKey", account.getAccountKey());
        query.put("sourceName", "dingYue");
        QlmProcurementService.Result result = qlmProcurementService.commonPost("/open/push/get/pushPackge", query);
        if (!result.isSuccess()) {
            log.error("qlm get package error.tenantId:{},msg:{}", context.getTenantId(), result.getMsg());
            throw new ValidateException(result.getMsg());
        }
        List<IObjectData> packageList = new ArrayList<>();
        JSONArray resultData = result.getData(JSONArray.class);
        resultData.forEach(item -> {
            JSONObject itemData = (JSONObject) item;
            IObjectData objectData = new ObjectData();
            objectData.setName(itemData.getString("pushId"));
            objectData.set("package_type", itemData.getString("type"));
            objectData.set("package_count", itemData.getInteger("num"));
            objectData.set("ai_state", itemData.get("filter").toString());
            try {
                objectData.set("package_start_time", sdf.parse(startTime).getTime());
                objectData.set("package_end_time", sdf.parse(endTime).getTime());
            }catch (ParseException e){
                log.error("parse time error.startTime:{},endTime:{}", startTime, endTime);
            }
            objectData.set("is_push", "0");
            objectData.setDeleted(false);
            objectData.setTenantId(context.getTenantId());
            objectData.setDescribeApiName("BiddingSubscriptionPackageObj");
            packageList.add(objectData);
        });
        if (!packageList.isEmpty()){
            serviceFacade.bulkSaveObjectData(packageList, User.systemUser(context.getTenantId()));
        }
    }

    private List<IObjectData> selectQlmNotPushPackage(String tenantId){
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, "is_push", "0");
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "0");
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setOffset(0);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), "BiddingSubscriptionPackageObj", searchTemplateQuery);
        if (ObjectUtils.isEmpty(queryResult) || ObjectUtils.isEmpty(queryResult.getData())){
            log.warn("未查询到需要同步的包数据，tenantId:{}", tenantId); // ignoreI18n
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    public void syncQlmProcurementInfoByPackage(ActionContext context, QlmProcurementService.Account account) {
        // 统计各个订阅器更新和新增的数量
        Map<String, List<JSONObject>> insNumMap = new HashMap<>();
        Map<String, List<JSONObject>> updateNumMap = new HashMap<>();
        // 拉取数据详情
        selectQlmNotPushPackage(context.getTenantId()).forEach(packageItem -> {
            String type = packageItem.get("package_type", String.class);
            String qlmAiState = packageItem.get("ai_state", String.class);
            boolean pushBegin = false;
            boolean isBigModel = false;
            try {
                JSONArray jsonArray = JSONArray.parseArray(qlmAiState);
                if ("ordinary".equals(type)) {
                    // 普通数据包判断去重后直接拉取
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if ("distinct".equals(jsonObject.getString("filterName")) && 1 == jsonObject.getInteger("status")){
                            pushBegin = true;
                        }
                    }
                } else if ("bigModel".equals(type)) {
                    // 大模型数据包拉取 先判断是否已经完成，再拉取
                    isBigModel = true;
                    for (int i = 0; i < jsonArray.size(); i++) {
                        if (jsonArray.getJSONObject(i).getInteger("status") != 1) {
                            return;
                        }
                    }
                    pushBegin = true;
                }else{
                    log.error("qlm package type is error:{}", packageItem);
                    return;
                }
            }catch (Exception e){
                log.error("syncQlmProcurementInfoByPackage json erro!", e);
            }

            if (!pushBegin) {
                log.warn("package is not finish.tenantId:{},package:{},qlmAiState:{}", context.getTenantId(), packageItem.getName(), qlmAiState);
                return;
            }
            syncQlmProcurementInfo(context,packageItem.getName(), insNumMap, updateNumMap, isBigModel);
            // 修改包的拉取状态
            packageItem.set("is_push", "1");
            serviceFacade.updateObjectData(User.systemUser(context.getTenantId()), packageItem);
        });
        log.info("同步千里马标讯数据成功,tenantId:{}", context.getTenantId());
        // 给订阅器相关负责人发送crm通知
        List<String> qlmIdList = new ArrayList<>(insNumMap.keySet());
        syncQlmProcurementInfoSendCRMNotice(context, qlmIdList, insNumMap, updateNumMap);
    }

    public void syncQlmProcurementInfo(ActionContext context, String packageId, Map<String, List<JSONObject>> insNumMap,Map<String, List<JSONObject>> updateNumMap,boolean isBigModel) {
        log.info("sync Qlm package:{},tenantId:{}",packageId, context.getTenantId());
        List<JSONObject> insertDataList = new ArrayList<>();
        List<JSONObject> updateDataList = new ArrayList<>();
        QlmProcurementService.Account account = qlmProcurementService.getAccount(context.getTenantId());
        int pageIndex = 1, totalPage = 100;
        while (pageIndex <= totalPage) {
            JSONObject query = new JSONObject();
            query.put("pushId", Long.parseLong(packageId));
            query.put("endTime", pageIndex);
            query.put("accountKey", account.getAccountKey());
            query.put("pageIndex", pageIndex++);
            QlmProcurementService.Result result = qlmProcurementService.commonPost("/open/push/get/pushPackge/data", query);
            if (!result.isSuccess()) {
                return;
            }
            JSONObject pageInfo = result.getData(JSONObject.class);
            totalPage = pageInfo.getInteger("totalPage");
            JSONArray dataArray = pageInfo.getJSONArray("list");
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject data = dataArray.getJSONObject(i);
                if (isBigModel && ObjectUtils.isNotEmpty(data.get("related"))) {
                    data.put("isQlmAiFiltration", "1".equals(data.getString("related")) ? "yes" : "no");
                }
                String sourceId = data.getString("sourceId");
                if (!insNumMap.containsKey(sourceId)) {
                    insNumMap.put(sourceId, new ArrayList<>());
                }
                if (!updateNumMap.containsKey(sourceId)) {
                    updateNumMap.put(sourceId, new ArrayList<>());
                }
                if (Objects.equals(0, data.getInteger("flag"))) {//新增
                    insertDataList.add(data);
                    insNumMap.get(sourceId).add(data);
                } else if (Objects.equals(1, data.getInteger("flag"))) {//更新
                    updateDataList.add(data);
                    updateNumMap.get(sourceId).add(data);
                }
            }
        }
        log.info("千里马公告新增{}条,更新{}条,tenantId:{},packageId:{}", insertDataList.size(), updateDataList.size(), context.getTenantId(), packageId);
        for (JSONObject insertData : insertDataList) {
            try {
                if (ObjectUtils.isEmpty(insertData.getString("sourceId"))) {
                    log.warn("sourceId is null.insertData:{}", insertData.toJSONString());
                    continue;
                }
                sfaRateLimiterService.limiter(SfaRateLimiterService.PROCUREMENT_INSERT_QLM).acquire();
                lockProcurementInfoHandle(context, qlmDataToProcurementInfo(context, insertData));
            } catch (Exception e) {
                log.error("新增千里马标讯公告失败", e);
            }
        }
        for (JSONObject updateData : updateDataList) {
            try {
                sfaRateLimiterService.limiter(SfaRateLimiterService.PROCUREMENT_INSERT_QLM).acquire();
                updateQlmProcurementInfo(context, updateData);
//                updateQlmProcurementInfoByGroupId(context, updateData); 需要根据groupId查询所有的标讯，全量更新！
            } catch (Exception e) {
                log.error("更新千里马标讯公告失败", e);
            }
        }
    }

    public void syncQlmProcurementInfoSendCRMNotice(ActionContext context,List<String> qlmIdList,Map<String, List<JSONObject>> insNumMap,Map<String, List<JSONObject>> updateNumMap){
        if (ObjectUtils.isEmpty(qlmIdList)) {
            return;
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setOffset(0);
        Filter filter = new Filter();
        filter.setFieldName("qlm_id");
        filter.setFieldValues(qlmIdList);
        filter.setOperator(Operator.HASANYOF);
        searchTemplateQuery.setFilters(Lists.newArrayList(filter));
        User systemUser = User.systemUser(context.getTenantId());
        QueryResult<IObjectData> qlmRulesResult = serviceFacade.findBySearchQuery(systemUser, "BiddingSubscriptionRulesObj", searchTemplateQuery);
        qlmRulesResult.getData().forEach(objectData -> {
            // 判断是否开启了通知
            if (!objectData.get("is_push", Boolean.class)) {
                return;
            }
            // 判断本次插入是否有数据
            if (ObjectUtils.isEmpty(insNumMap.get(objectData.get("qlm_id", String.class)))) {
                return;
            }
            // 查询通知接收人
            List<String> memberList = procurementRuleService.getProcurementRuleMemberUserList(context.getTenantId(), objectData.getId());
            List<Integer> receiverIds = memberList.stream().map(Integer::parseInt).collect(Collectors.toList());
            String insNumString = String.valueOf(insNumMap.get(objectData.get("qlm_id", String.class)).size());
            // 添加外部成员
            List<Integer> downstreamSelectRole = selectProcurementInfoExternalPersonnel(objectData.get("downstream_select_role", String.class), context.getTenantId());
            receiverIds.addAll(downstreamSelectRole);
            // 当i18n解析失败时的提示内容
            String title = "【订阅规则】千里马数据有更新"; // ignoreI18n
            String msg = String.format("订阅规则 %s 推送了%s条新数据，请查看。", objectData.getName(), insNumString); // ignoreI18n
            CRMRemindRecordUtil.sendNewCRMRecordRemindSender(
                    crmNotificationService,
                    context.getUser(),
                    217,
                    receiverIds,
                    User.SUPPER_ADMIN_USER_ID,
                    title,
                    msg,
                    SFAI18NKeyUtil.SFA_BIDDING_SUBSCRIPTION_QLM_PUSH_TITLE,
                    Lists.newArrayList(),
                    SFAI18NKeyUtil.SFA_BIDDING_SUBSCRIPTION_QLM_PUSH_MSG,
                    Lists.newArrayList(objectData.getName(), insNumString),
                    com.fxiaoke.functions.utils.Maps.of("objectId", objectData.getId()),
                    context.getAppId());
        });
    }

    public List<Integer> selectProcurementInfoExternalPersonnel(String downstreamSelectRole, String tenantId) {
        Set<Integer> ret = new HashSet<>();
        if (ObjectUtils.isEmpty(downstreamSelectRole)) {
            return new ArrayList<>(ret);
        }
        Set<String> outUserIds = new HashSet<>();
        Set<String> outTenantIds = new HashSet<>();
        Set<String> outTgroupIds = new HashSet<>();
        Set<String> outRoleIds = new HashSet<>();
        try {
            JSONObject jsonObject = JSONObject.parseObject(downstreamSelectRole);
            if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(jsonObject.getJSONArray("outerUids"))) {
                jsonObject.getJSONArray("outerUids").forEach(node -> outUserIds.add(node.toString()));
            }
            if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(jsonObject.getJSONArray("outerTenantIds"))) {
                jsonObject.getJSONArray("outerTenantIds").forEach(node -> outTenantIds.add(node.toString()));
            }
            if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(jsonObject.getJSONArray("outerTgroupIds"))) {
                jsonObject.getJSONArray("outerTgroupIds").forEach(node -> outTgroupIds.add(node.toString()));
            }
            if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(jsonObject.getJSONArray("outerRoleIds"))) {
                jsonObject.getJSONArray("outerRoleIds").forEach(node -> outRoleIds.add(node.toString()));
            }
        } catch (Exception e) {
            log.error("select json error!", e);
            return new ArrayList<>(ret);
        }

        // 用户
        outUserIds.forEach(id -> ret.add(Integer.parseInt(id)));
        // 角色
        if (ObjectUtils.isNotEmpty(outRoleIds)) {
            List<IFilter> roleFilters = Lists.newArrayList();
            SearchUtil.fillFilterEq(roleFilters, IObjectData.IS_DELETED, "0");
            SearchUtil.fillFilterIn(roleFilters, "outer_role_ids", outRoleIds);
            SearchUtil.fillFilterEq(roleFilters, "active_state", "1"); // 激活状态 0未激活 1已激活
            SearchUtil.fillFilterEq(roleFilters, "type", "1"); // 互联状态 1正常 0停用
            SearchTemplateQuery roleSearchTemplateQuery = new SearchTemplateQuery();
            roleSearchTemplateQuery.setFilters(roleFilters);
            roleSearchTemplateQuery.setNeedReturnQuote(false);
            roleSearchTemplateQuery.setLimit(1000);
            roleSearchTemplateQuery.setOffset(0);
            QueryResult<IObjectData> roleObjectDataQueryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(User.systemUser(tenantId), "PublicEmployeeObj", roleSearchTemplateQuery, Lists.newArrayList("outer_uid"));
            if (ObjectUtils.isNotEmpty(roleObjectDataQueryResult) && ObjectUtils.isNotEmpty(roleObjectDataQueryResult.getData())) {
                roleObjectDataQueryResult.getData().forEach(o -> ret.add(o.get("outer_uid", Integer.class)));
            }
        }
        // 企业组
        if (ObjectUtils.isNotEmpty(outTgroupIds)) {
            String sql = String.format("select id,out_tenant_id from org_tenant_group_data where tenant_id='%s' and tenant_group_id in ('%s')",
                    tenantId, String.join("','", outTgroupIds));
            try {
                List<Map> sqlData = specialTableMapper.setTenantId(tenantId).findBySql(sql);
                if (ObjectUtils.isNotEmpty(sqlData) && ObjectUtils.isNotEmpty(sqlData.get(0))){
                    sqlData.forEach(map -> outTenantIds.add(String.valueOf(map.get("out_tenant_id"))));
                }
            } catch (Exception e) {
                log.error("select outTgroup error!sql:{}",sql,e);
            }
        }
        // 企业
        if (ObjectUtils.isNotEmpty(outTenantIds)) {
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "0");
            SearchUtil.fillFilterIn(filters, "outer_tenant_id", outTenantIds);
            SearchUtil.fillFilterEq(filters, "active_state", "1"); // 激活状态 0未激活 1已激活
            SearchUtil.fillFilterEq(filters, "type", "1"); // 互联状态 1正常 0停用
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setFilters(filters);
            searchTemplateQuery.setNeedReturnQuote(false);
            searchTemplateQuery.setLimit(1000);
            searchTemplateQuery.setOffset(0);
            QueryResult<IObjectData> objectDataQueryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(User.systemUser(tenantId), "PublicEmployeeObj", searchTemplateQuery, Lists.newArrayList("outer_uid"));
            if (ObjectUtils.isNotEmpty(objectDataQueryResult) && ObjectUtils.isNotEmpty(objectDataQueryResult.getData())) {
                objectDataQueryResult.getData().forEach(o -> ret.add(o.get("outer_uid", Integer.class)));
            }
        }

        return new ArrayList<>(ret);
    }

    public ProcurementAllocate.Result allocateProcurement2DB(ActionContext context, ProcurementAllocate.Arg arg) {
        String objectDataId = arg.getId();
        Integer bidType = arg.getBidType();
        BidDetailInfo bidDetailInfo = getRemoteSourceData(context, objectDataId, bidType, arg.getClientInfo(), arg.getBatch());
        ProcurementAllocate.Result result = ProcurementAllocate.Result.builder().success(true).cost(0).build();
        try {
            result.setCost(bidDetailInfo.getIsCost());
            ProcurementDetail detailInfo = ProcurementUtils.toProcurementDetail(bidDetailInfo, arg.getDataName());
            lockProcurementInfoHandle(context, detailInfo);
        } catch (Exception e) {
            result.setSuccess(false);
            log.error("标讯同步数据出错", e);
        }
        return result;
    }

    public ProcurementDetail qlmDataToProcurementInfo(ActionContext context, JSONObject json){
        String tenantId = context.getTenantId();

        ProcurementDetail detailInfo = new ProcurementDetail();
        detailInfo.setTitle(json.getString("title"));
        String callerNameTypeStr = json.getString("zhaobiaoUnitFirstNature");
        if (!StringUtils.isEmpty(callerNameTypeStr)) {
            detailInfo.setCallerNameType(Arrays.stream(callerNameTypeStr.split(",")).map(CALLER_TYPE_MAP::get).collect(Collectors.toList()));
        }
        detailInfo.setBidType(BID_TYPE_MAP.get(json.getString("messageType")));
        detailInfo.setBidSubType(BID_SUB_TYPE_MAP.get(json.getString("noticeSegmentName")));

        String[] area = json.getString("area").split("-");
        if (area.length > 0) {
            detailInfo.setProvince(area[0]);
        }
        if (area.length > 1) {
            detailInfo.setCity(area[1]);
        }
        detailInfo.put("qlm_source_area__c", json.getString("area"));
        detailInfo.setProjectNumber(json.getString("projectNo"));

        detailInfo.setPublishTime(json.getLong("publishTime"));
        detailInfo.setBidingEndTime(json.getLong("tenderEndTime"));
        detailInfo.setTenderEndTime(json.getLong("registrationEndTime"));
        detailInfo.setBidId(json.getString("dataId"));
        //以下是查找关联类型
        JSONArray products = json.getJSONArray("bdKeywords");
        if (products != null && !products.isEmpty()) {
            detailInfo.setProducts(products.toJavaList(String.class));
        }
        //招标单位
        UnitEntity caller = buildUnitEntity(json.getString("zhaoBiaoUnit"), json.getString("budgetAmountNumber"), json.getString("zhaoRelationName"), json.getString("zhaoRelationWay"));
        if (caller != null) {
            detailInfo.setCaller(caller);
        }
        //代理单位
        UnitEntity agent = buildUnitEntity(json.getString("agent"), null, null, null);
        if (agent != null) {
            detailInfo.setAgents(Lists.newArrayList(agent));
        }
        //中标候选人
        String progress_stage = PROGRESS_STAGE_MAP.get(json.getString("progressStage"));
        if (Objects.equals(progress_stage, "3")) {//候选人公示
            UnitEntity tender = buildUnitEntity(json.getString("zhongBiaoUnit"), json.getString("zhongBiaoAmount"), json.getString("zhongRelationName"), json.getString("zhongRelationWay"));
            if (tender != null) {
                detailInfo.setTenders(Lists.newArrayList(tender));
            }
        } else if (Objects.equals(progress_stage, "4") || Objects.equals(progress_stage, "5")) {
            //中标单位
            UnitEntity winner = buildUnitEntity(json.getString("zhongBiaoUnit"), json.getString("zhongBiaoAmount"), json.getString("zhongRelationName"), json.getString("zhongRelationWay"));
            if (winner != null) {
                detailInfo.setWinners(Lists.newArrayList(winner));
                detailInfo.setWinnerAmount(winner.getAmount());
                detailInfo.setWinnerAmountPct(winner.getAmountPct());
            }
        }
        detailInfo.put(DATA_SOURCE, DATA_SOURCE_QLM);
        detailInfo.put(SUBSCRIBER, getSubscriberId(tenantId, json.getString("sourceId")));
        //进展阶段
        detailInfo.put(PROGRESS_STAGE, progress_stage);
        //开标时间
        String openBidingTime = json.getString("openBidingTime");
        try {
            if (!StringUtils.isEmpty(openBidingTime)) {
                long obt = Long.parseLong(openBidingTime);
                if (obt > 0L && obt < 17040384000000L) {
                    detailInfo.put(OPEN_BIDING_TIME, openBidingTime);
                }
            }
        } catch (Exception e) {
            log.warn("千里马标讯[openBidingTime][{}]解析异常", openBidingTime);
        }
        //招标方式
        detailInfo.put(CALLER_METHOD, json.getString("zhaoBiaoType"));
        detailInfo.put(GROUP_ID, json.getString("groupId"));
        String callerSubTypeStr = json.getString("zhaobiaoUnitSecondNature");
        if (!StringUtils.isEmpty(callerSubTypeStr)) {
            detailInfo.put(CALLER_SUB_TYPE, Arrays.stream(json.getString("zhaobiaoUnitSecondNature").split(",")).map(CALLER_SUB_TYPE_MAP::get).collect(Collectors.toList()));
        }
        // ai相关数据
        detailInfo.setQlmAiExpandCorpore(json.getString("bigModelExpandCorpore"));
        detailInfo.setQlmAiExpandHospital(json.getString("bigModelExpandHospital"));
        if (ObjectUtils.isNotEmpty(json.get("isQlmAiFiltration"))) {
            detailInfo.setQlmAiFiltration(json.getString("isQlmAiFiltration"));
        }
        return detailInfo;
    }


    /**
     * 千里马 标讯公告更新主体
     */
    public void updateQlmProcurementInfo(ActionContext context, JSONObject json) {
        String tenantId = context.getTenantId();
        String progress_stage = PROGRESS_STAGE_MAP.get(json.getString("progressStage"));
        if (!Lists.newArrayList("3", "4", "5").contains(progress_stage)) {
            return;
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterIn(filters, DATA_SOURCE, DATA_SOURCE_QLM);
        SearchUtil.fillFilterIn(filters, BID_ID, json.getString("dataId"));
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), SFAPreDefineObject.ProcurementInfo.getApiName(), searchTemplateQuery);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            log.info("未查询到公告数据");
            return;
        }
        //公告id，使用数据唯一id查询
        IObjectData procurementInfo = queryResult.getData().get(0);

        UnitEntity unit = buildUnitEntity(json.getString("zhongBiaoUnit"), json.getString("zhongBiaoAmount"), json.getString("zhongRelationName"), json.getString("zhongRelationWay"));
        if (unit == null) {
            log.warn("千里马数据更新,未解析出中标单位{}", json);
            return;
        }
        List<UnitEntity> unitEntityList = Lists.newArrayList(unit);
        List<IObjectData> enterprises = getEnterprises(context, unitEntityList);

        if (Objects.equals(progress_stage, "3")) {//候选人公示
            List<IObjectData> dataList = saveEnterpriseReturnObjectData(context, unitEntityList, enterprises, ProcurementEnterpriseBidTypeEnum.TENDER);
            List<String> idListExists = procurementInfo.get(TENDER_ENTERPRISE, List.class);
            if (CollectionUtils.isEmpty(idListExists)) {
                idListExists = new ArrayList<>();
            }
            Set<String> set = new LinkedHashSet<>(idListExists);
            set.addAll(getIds(dataList));
            procurementInfo.set(TENDER_ENTERPRISE, new ArrayList<>(set));
        } else if (Objects.equals(progress_stage, "4") || Objects.equals(progress_stage, "5")) {
            List<IObjectData> dataList = saveEnterpriseReturnObjectData(context, unitEntityList, enterprises, ProcurementEnterpriseBidTypeEnum.WINNER);
            List<String> idListExists = procurementInfo.get(WINNER_ENTERPRISE, List.class);
            if (CollectionUtils.isEmpty(idListExists)) {
                idListExists = new ArrayList<>();
            }
            Set<String> set = new LinkedHashSet<>(idListExists);
            set.addAll(getIds(dataList));
            procurementInfo.set(WINNER_ENTERPRISE, new ArrayList<>(set));
            procurementInfo.set(WINNER_CONTACT, getUnitEntityContact(procurementInfo.get(WINNER_CONTACT, String.class), unitEntityList));

            for (IObjectData unitData : dataList) {
                if (idListExists.contains(unitData.getId())) {
                    continue;
                }
                Map<String, Object> infoLineExt = Maps.newHashMap();
                infoLineExt.put(WINNER_ENTERPRISE, unitData.getId());
                infoLineExt.put(WINNER_AMOUNT, unit.getAmount());
                infoLineExt.put(WINNER_AMOUNT_PCT, unit.getAmountPct());
                Map<String, Object> contactMap = saveAllContactObjectData(context.getUser(), null, unitEntityList, null, null);
                IObjectData lastInfoLine = findLastInfoLineByProcurementInfo(tenantId, procurementInfo.getId());
                if (lastInfoLine != null) {
                    contactMap.put(AGENT_CONTACT, lastInfoLine.get(AGENT_CONTACT));
                    contactMap.put(CALLER_CONTACT, lastInfoLine.get(ProcurementConstants.ProcurementInfoLines.CALLER_PROCUREMENT_CONTACT));
                }
                IObjectData infoLine = getInfoLinesData(context, procurementInfo, contactMap, infoLineExt);
                serviceFacade.saveObjectData(User.systemUser(tenantId), infoLine);
            }
        }
        procurementInfo.set(PROGRESS_STAGE, progress_stage);
        procurementInfo.set(WINNER_AMOUNT, unit.getAmount());
        procurementInfo.set(WINNER_AMOUNT_PCT, unit.getAmountPct());
        serviceFacade.updateObjectData(User.systemUser(tenantId), procurementInfo);
    }

    private IObjectData findLastInfoLineByProcurementInfo(String tenantId, String infoId) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, PROCUREMENT_INFO_ID, infoId);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setLimit(1);
        List<OrderBy> orders = new ArrayList<>();
        orders.add(new OrderBy(IObjectData.CREATE_TIME, Boolean.FALSE));
        searchTemplateQuery.setOrders(orders);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), SFAPreDefineObject.ProcurementInfoLines.getApiName(), searchTemplateQuery);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return null;
        }
        return queryResult.getData().get(0);
    }

    private String getSubscriberId(String tenantId, String qlmId) {
        if (StringUtils.isEmpty(qlmId)) {
            return null;
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, BiddingSubscriptionRulesConstants.QLM_ID, qlmId);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), SFAPreDefineObject.BiddingSubscriptionRules.getApiName(), searchTemplateQuery);
        if (queryResult.getData() == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return null;
        }
        return queryResult.getData().get(0).getId();
    }

    private boolean lockProcurementInfoHandle(ActionContext context, ProcurementDetail detailInfo) {
        String tenantId = context.getTenantId();
        String dataSource = null;
        if (detailInfo.getMapEx() != null) {
            dataSource = (String) detailInfo.getMapEx().get(DATA_SOURCE);
        }
        RLock lock = redissonService.tryLock(1, 1, TimeUnit.MINUTES, String.format("procurement_info_insert_%s_%s_%s", tenantId, dataSource, detailInfo.getBidId()));
        try {
            if (null == lock) {
                log.warn("千里马插入相同数据,获取锁失败");
                return false;
            }
            SearchTemplateQuery query = new SearchTemplateQuery();
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);
            SearchUtil.fillFilterEq(filters, BID_ID, detailInfo.getBidId());
            SearchUtil.fillFilterEq(filters, DATA_SOURCE, dataSource);
            query.setFilters(filters);
            Integer count = serviceFacade.countObjectDataFromDB(tenantId, SFAPreDefineObject.ProcurementInfo.getApiName(), query);
            if (count > 0) {
                log.warn("千里马存在相同数据,不进行插入");
                return false;
            }
            procurementInfoHandle(context, detailInfo);
            return true;
        } finally {
            redissonService.unlock(lock);
        }
    }

    private void procurementInfoHandle(ActionContext context, ProcurementDetail detailInfo) {
        List<UnitEntity> lists = Lists.newArrayList();
        if (detailInfo.getWinners() != null) {
            lists.addAll(detailInfo.getWinners());
        }
        List<UnitEntity> callerEntity = Lists.newArrayList();
        if (detailInfo.getCaller() != null) {
            callerEntity.add(detailInfo.getCaller());
        }
        if (CollectionUtils.isNotEmpty(detailInfo.getAgents())) {
            lists.addAll(detailInfo.getAgents());
        }
        if (CollectionUtils.isNotEmpty(detailInfo.getTenders())) {
            lists.addAll(detailInfo.getTenders());
        }
        if (CollectionUtils.isNotEmpty(callerEntity)) {
            lists.addAll(callerEntity);
        }

        List<IObjectData> enterprises = getEnterprises(context, lists);

        List<IObjectData> savedWinners = saveEnterpriseReturnObjectData(context, detailInfo.getWinners(), enterprises, ProcurementEnterpriseBidTypeEnum.WINNER);
        List<String> tenders = saveEnterpriseObjectData(context, detailInfo.getTenders(), enterprises, ProcurementEnterpriseBidTypeEnum.TENDER);
        List<String> agents = saveEnterpriseObjectData(context, detailInfo.getAgents(), enterprises, ProcurementEnterpriseBidTypeEnum.AGENT);
        List<String> callers = saveEnterpriseObjectData(context, callerEntity, enterprises, ProcurementEnterpriseBidTypeEnum.CALLER);
        List<String> products = saveProducts(context, detailInfo);

        Map<String, Object> contactMap = saveAllContactObjectData(context.getUser(), detailInfo.getCaller(), detailInfo.getWinners(), detailInfo.getAgents(), detailInfo.getJuries());

        Map<String, Object> map = Maps.newHashMap();
        map.putAll(detailInfo.getMapEx());
        map.put(CALLER_ENTERPRISE, callers);
        map.put(WINNER_ENTERPRISE, getIds(savedWinners));
        map.put(TENDER_ENTERPRISE, tenders);
        map.put(AGENT_ENTERPRISE, agents);
        map.put(PRODUCTS, products);
        IObjectData infoData = saveInfoData(context, detailInfo, map);
        triggerAction(context, SFAPreDefineObject.ProcurementInfo.getApiName(), ObjectDataDocument.of(infoData));
        List<IObjectData> infoLines = getInfoLineObjectDatas(context, detailInfo, savedWinners, infoData, contactMap);
        serviceFacade.bulkSaveObjectData(infoLines, context.getUser());
        JSONObject jsonObject = recalculateProducer.getJsonObject(context.getTenantId(), SFAPreDefineObject.ProcurementInfo.getApiName(), Lists.newArrayList(infoData.getId()), 1, context.getUser());
        recalculateProducer.sendMQ(RecalculateProducer.PROCUREMENT_COMPARISON_TAG, jsonObject, context.getTenantId());
    }

    @NotNull
    private List<IObjectData> getInfoLineObjectDatas(ActionContext context, ProcurementDetail detailInfo, List<IObjectData> winners, IObjectData infoData, Map<String, Object> contactMap) {
        List<IObjectData> infoLines = Lists.newArrayList();
        if (CollectionUtils.isEmpty(detailInfo.getWinners())) {
            infoLines.add(getInfoLinesData(context, infoData, contactMap, Maps.newHashMap()));
        } else {
            for (UnitEntity winner : detailInfo.getWinners()) {
                String winnerId = "";
                Optional<IObjectData> objectData = winners.stream().filter(x -> (winner.getCreditCode() != null && winner.getCreditCode().equals(x.get(CREDIT_CODE)) || (winner.getName() != null && winner.getName().equals(x.get(NAME))))).findFirst();
                if (objectData.isPresent()) {
                    winnerId = objectData.get().getId();
                }
                Map<String, Object> infoLine = Maps.newHashMap();
                infoLine.put(WINNER_ENTERPRISE, winnerId);
                infoLine.put(WINNER_AMOUNT, winner.getAmount());
                infoLine.put(WINNER_AMOUNT_PCT, winner.getAmountPct());
                infoLines.add(getInfoLinesData(context, infoData, contactMap, infoLine));
            }
        }
        return infoLines;
    }

    private List<ContactEntity> findAllContactEntity(List<UnitEntity> unitEntityList) {
        if (CollectionUtils.isEmpty(unitEntityList)) {
            return new ArrayList<>();
        }
        List<UnitEntity> hasContactEntity = unitEntityList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getContactPersons())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasContactEntity)) {
            return new ArrayList<>();
        }
        return hasContactEntity.stream().map(UnitEntity::getContactPersons).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private Map<String, Object> saveAllContactObjectData(User user, UnitEntity caller, List<UnitEntity> winners, List<UnitEntity> agents, List<JuryEntity> juries) {
        Map<String, Object> map = Maps.newHashMap();
        List<UnitEntity> callers = new ArrayList<>();
        if (caller != null) {
            callers.add(caller);
        }
        List<ContactEntity> callerContacts = findAllContactEntity(callers);
        List<ContactEntity> winContacts = findAllContactEntity(winners);
        List<ContactEntity> agentContacts = findAllContactEntity(agents);

        List<IObjectData> objectDataList2Save = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(winContacts)) {
            List<String> ids = fuckObjectDataBuilder(user, winContacts, ProcurementContactTypeEnum.WINNER, objectDataList2Save);
            map.put(WINNER_CONTACT, ids);
        }
        if (CollectionUtils.isNotEmpty(agentContacts)) {
            List<String> ids = fuckObjectDataBuilder(user, agentContacts, ProcurementContactTypeEnum.AGENT, objectDataList2Save);
            map.put(AGENT_CONTACT, ids);
        }
        if (CollectionUtils.isNotEmpty(callerContacts)) {
            List<String> ids = fuckObjectDataBuilder(user, callerContacts, ProcurementContactTypeEnum.CALLER, objectDataList2Save);
            map.put(CALLER_CONTACT, ids);
        }
        if (CollectionUtils.isNotEmpty(juries)) {
            List<String> ids = getObjectDataIdExists(user, juries, objectDataList2Save);
            map.put(JURY, ids);
        }
        if (CollectionUtils.isNotEmpty(objectDataList2Save)) {
            serviceFacade.bulkSaveObjectData(objectDataList2Save, user);
        }
        return map;
    }

    private List<String> fuckObjectDataBuilder(User user, List<ContactEntity> contactEntities, ProcurementContactTypeEnum type, List<IObjectData> objectDataList2Save) {
        List<String> ids = new ArrayList<>();
        ArrayList<IObjectData> objectData = getContactObjectData(user, contactEntities, type);
        objectDataList2Save.addAll(objectData);
        if (CollectionUtils.isNotEmpty(objectData)) {
            ids.addAll(objectData.stream().map(DBRecord::getId).collect(Collectors.toList()));
        }
        return ids;
    }

    private List<String> getObjectDataIdExists(User user, List<JuryEntity> contactEntities, List<IObjectData> objectDataList2Save) {
        List<String> ids = new ArrayList<>();
        ArrayList<IObjectData> objectData = getJuryObjectData(user, contactEntities);
        objectDataList2Save.addAll(objectData);
        return ids;
    }

    private IObjectData getInfoLinesData(ActionContext context, IObjectData infoData, Map<String, Object> contactMap, Map<String, Object> iterm) {
        iterm.put(PROCUREMENT_INFO_ID, infoData.getId());
        iterm.put(WINNER_CONTACT, contactMap.get(WINNER_CONTACT));

        iterm.put(CALLER_ENTERPRISE, infoData.get(CALLER_ENTERPRISE));
        iterm.put(AGENT_ENTERPRISE, infoData.get(AGENT_ENTERPRISE));
        iterm.put(COMPETITIVE_PRODUCTS, infoData.get(PRODUCTS));
        iterm.put(AGENT_CONTACT, contactMap.get(AGENT_CONTACT));
        iterm.put(TENDER_ENTERPRISE, infoData.get(TENDER_ENTERPRISE));
        iterm.put(ProcurementConstants.ProcurementInfoLines.CALLER_PROCUREMENT_CONTACT, contactMap.get(CALLER_CONTACT));

        iterm.put(ID, serviceFacade.generateId());
        iterm.put(CALLER_ENTERPRISE_NAME, infoData.get(CALLER_ENTERPRISE_NAME));
        iterm.put(OWNER, Lists.newArrayList(context.getUser().getUpstreamOwnerIdOrUserId()));
        if (context.getUser().isOutUser()){
            iterm.put(OUR_OWNER, Lists.newArrayList(context.getUser().getOutUserId()));
            iterm.put(OUR_TENANT_ID, context.getUser().getOutTenantId());
        }
        iterm.put(RELEVANT_TEAM, transitionRelevantTeam(context.getUser()));
        iterm.put(IS_DELETED, false);
        iterm.put(TENANT_ID, context.getTenantId());
        iterm.put(RECORD_TYPE, "default__c");
        iterm.put(OBJECT_DESCRIBE_API_NAME, SFAPreDefineObject.ProcurementInfoLines.getApiName());
        return ObjectDataExt.of(iterm).getObjectData();
    }

    public BidDetailInfo getRemoteSourceData(ActionContext context, String objectDataId, Integer bidType, String clientInfo, Integer isBatch) {
        QueryBidDetailArg queryBidDetailArg = QueryBidDetailArg.builder()
                .tenantId(context.getTenantId())
                .employeeId(context.getUser().getUpstreamOwnerIdOrUserId())
                .source("ZhiLiao").isBatch(isBatch == null ? 0 : isBatch)
                .opType(ProcurementSearchOpTypeEnum.CHOOSE.getType())
                .terminalType(clientInfo)
                .bidId(objectDataId)
                .bidType(bidType)
                .build();
        HttpResponseResult<BidDetailInfo> responseResult;
        try {
            responseResult = industryClient.queryBidDetailById(queryBidDetailArg);
        } catch (FRestClientException e) {
            log.error("queryBidDetailById response:{}", queryBidDetailArg, e);
            throw new RuntimeException(e);
        }
        if ("401".equals(responseResult.getErrorCode()) || (200 != responseResult.getStatus() && StringUtils.isNotBlank(responseResult.getErrorMessage()))) {
            throw new ValidateException(responseResult.getErrorMessage());
        }
        return responseResult.getResult();
    }

    private List<String> getIds(List<IObjectData> dataList) {
        List<String> ids = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            ids = dataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        }
        return ids;
    }

    private List<String> saveEnterpriseObjectData(ActionContext context, List<UnitEntity> entities, List<IObjectData> enterprises, ProcurementEnterpriseBidTypeEnum type) {
        return getIds(saveEnterpriseReturnObjectData(context, entities, enterprises, type));
    }

    private List<IObjectData> saveEnterpriseReturnObjectData(ActionContext context, List<UnitEntity> entities, List<IObjectData> enterprises, ProcurementEnterpriseBidTypeEnum type) {
        List<IObjectData> dataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(entities)) {
            return dataList;
        }
        List<UnitEntity> newEntities = Lists.newArrayList(entities);
        Set<String> newCreditCodes = entities.stream().filter(x -> StringUtils.isNotBlank(x.getCreditCode())).map(UnitEntity::getCreditCode).collect(Collectors.toSet());
        Set<String> newNames = entities.stream().filter(x -> StringUtils.isNotBlank(x.getName())).map(UnitEntity::getName).collect(Collectors.toSet());
        List<IObjectData> es = new ArrayList<>();
        // 如果没有企业信用代码，获取企业名称
        List<String> creditCodesExists = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(newCreditCodes)) {
            List<IObjectData> tmp = enterprises.stream().filter(x -> newCreditCodes.contains(String.valueOf(x.get(CREDIT_CODE)))).collect(Collectors.toList());
            es.addAll(tmp);
            creditCodesExists = tmp.stream().filter(x -> x.get(CREDIT_CODE) != null).map(x -> String.valueOf(x.get(CREDIT_CODE))).collect(Collectors.toList());
        }
        List<String> nameExists = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(newNames)) {
            List<IObjectData> tmp = enterprises.stream().filter(x -> newNames.contains(String.valueOf(x.get(NAME))) && ObjectUtils.isEmpty(x.get(CREDIT_CODE))).collect(Collectors.toList());
            es.addAll(tmp);
            nameExists = tmp.stream().filter(x -> x.get(NAME) != null).map(x -> String.valueOf(x.get(NAME))).collect(Collectors.toList());
        }

        List<IObjectData> updateDataList = new ArrayList<>();
        List<IObjectData> existDataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(es)) {
            for (IObjectData enterprise : es) {
                existDataList.add(enterprise);
                List listType = Lists.newArrayList();
                if (enterprise.get(BID_TYPE) != null) {
                    listType = (List) enterprise.get(BID_TYPE);
                    if (listType.contains(type.name())) {
                        continue;
                    }
                }
                if (enterprise.get(BID_TYPE) == null || !listType.contains(type.getValue())) {
                    listType.add(type.getValue());
                    IObjectData objectData = new ObjectData();
                    objectData.setId(enterprise.getId());
                    objectData.set(BID_TYPE, listType);
                    objectData.setTenantId(context.getTenantId());
                    objectData.setDescribeApiName(SFAPreDefineObject.ProcurementEnterprise.getApiName());
                    updateDataList.add(objectData);
                }
            }
            List<String> finalCreditCodesExists = creditCodesExists;
            newEntities.removeIf(x -> finalCreditCodesExists.contains(x.getCreditCode()));

            List<String> finalNameExists = nameExists;
            newEntities.removeIf(x -> finalNameExists.contains(x.getName()));
        }

        if (CollectionUtils.isNotEmpty(updateDataList)) {
            IActionContext actionContext = ActionContextExt.of(context.getUser()).skipRelevantTeam().getContext();
            try {
                objectDataService.batchUpdateIgnoreOther(updateDataList, Lists.newArrayList(BID_TYPE), actionContext);
            } catch (MetadataServiceException e) {
                log.error("saveEnterpriseObjectData batchUpdateIgnoreOther:{}", updateDataList, e);
                throw new RuntimeException(e);
            }
        }
        if (CollectionUtils.isNotEmpty(newEntities)) {
            List<IObjectData> objectDataList = getObjectData(context.getUser(), newEntities, type);
            List<IObjectData> winnerDataList = serviceFacade.bulkSaveObjectData(objectDataList, context.getUser());
            if (CollectionUtils.isNotEmpty(winnerDataList)) {
                dataList.addAll(winnerDataList);
            }
        }
        dataList.addAll(existDataList);
        return dataList;
    }


    public List<IObjectData> getEnterprises(ActionContext context, List<UnitEntity> entities) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        List<Wheres> wheresList = Lists.newArrayList();

        List<String> creditCodes = entities.stream().map(UnitEntity::getCreditCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(creditCodes)) {
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterIn(filters, CREDIT_CODE, creditCodes);
            SearchUtil.fillFilterEq(filters, ProcurementConstants.ProcurementCommonConstants.TENANT_ID, context.getTenantId());
            SearchUtil.fillFilterEq(filters, ProcurementConstants.ProcurementCommonConstants.IS_DELETED, 0);
            Wheres wheres = new Wheres();
            wheres.setFilters(filters);
            wheresList.add(wheres);
        }

        List<String> names = entities.stream().filter(x -> StringUtils.isBlank(x.getCreditCode())).map(UnitEntity::getName).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(names)) {
            Wheres wheres2 = new Wheres();
            List<IFilter> filters2 = Lists.newArrayList();
            SearchUtil.fillFilterIn(filters2, NAME, names);
            SearchUtil.fillFilterEq(filters2, ProcurementConstants.ProcurementCommonConstants.TENANT_ID, context.getTenantId());
            SearchUtil.fillFilterEq(filters2, ProcurementConstants.ProcurementCommonConstants.IS_DELETED, 0);
            wheres2.setFilters(filters2);
            wheresList.add(wheres2);
        }
        searchQuery.setWheres(wheresList);
        searchQuery.setSearchSource("db");
        searchQuery.setLimit(500);
        return getObjectDataIgnoreAll(context.getUser(), SFAPreDefineObject.ProcurementEnterprise.getApiName(), searchQuery);
    }

    public List<IObjectData> getProducts(ActionContext context, List<String> names) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        if (CollectionUtils.isEmpty(names)) {
            return Lists.newArrayList();
        }
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, NAME, names);
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, ProcurementConstants.ProcurementCommonConstants.TENANT_ID, context.getTenantId());
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.EQ, ProcurementConstants.ProcurementCommonConstants.IS_DELETED, "0");
        return getObjectDataIgnoreAll(context.getUser(), SFAPreDefineObject.CompetitiveProducts.getApiName(), searchQuery);
    }

    private List<String> saveProducts(ActionContext context, ProcurementDetail procurement) {
        List<String> ids = Lists.newArrayList();
        if (procurement == null) {
            return ids;
        }

        List<String> allNames = procurement.getProducts();
        List<IObjectData> products = getProductObjectData(context, allNames);
        List<String> nameExists = Lists.newArrayList();
        for (IObjectData product : products) {
            ObjectDataDocument document = productsDuplicateSearch(context, ObjectDataDocument.of(product));
            if (document != null) {
                ids.add(document.getId());
                nameExists.add(product.getName());
            }
        }
        products.removeIf(x -> nameExists.contains(x.getName()));
        //for (IObjectData product : products) {
        //    BaseObjectSaveAction.Result result = triggerAction(context, SFAPreDefineObject.CompetitiveProducts.getApiName(), ObjectDataDocument.of(product));
        //    if (result != null && result.getObjectData() != null) {
        //        ids.add(result.getObjectData().getId());
        //    }
        //}
        List<IObjectData> dataList = serviceFacade.bulkSaveObjectData(products, context.getUser(), false, false, x -> ActionContextExt.of(context.getUser(), RequestContextManager.getContext()).setNotValidate(true).getContext());
        if (CollectionUtils.isNotEmpty(dataList)) {
            ids.addAll(dataList.stream().map(DBRecord::getId).collect(Collectors.toList()));
        }
        return ids;
    }


    private ObjectDataDocument productsDuplicateSearch(ActionContext context, ObjectDataDocument checkData) {
        SFADuplicateSearch.Arg arg = new SFADuplicateSearch.Arg();
        arg.setData(ObjectDataDocument.of(checkData));
        arg.setMasterApiName(SFAPreDefineObject.CompetitiveProducts.getApiName());
        arg.setSlaveApiNames(Lists.newArrayList());
        ServiceContext serviceContext = new ServiceContext(context.getRequestContext(), "sfa_duplicate_search", "query");
        SFADuplicateSearch.Result searchResult = duplicateSearchService.query(serviceContext, arg);
        if (searchResult != null && CollectionUtils.isNotEmpty(searchResult.getData())) {
            return searchResult.getData().get(0);
        }
        return null;
    }

    private List<IObjectData> getObjectData(User user, List<UnitEntity> unitEntities, ProcurementEnterpriseBidTypeEnum type) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        for (UnitEntity entity : unitEntities) {
            String id = serviceFacade.generateId();
            Map<String, Object> iterm = Maps.newHashMap();
            iterm.put(NAME, entity.getName());
            iterm.put(CREDIT_CODE, entity.getCreditCode());
            iterm.put(BID_TYPE, Lists.newArrayList(type.getValue()));
            IObjectData newObjectData = getCommonObjectData(user, iterm, id, SFAPreDefineObject.ProcurementEnterprise);
            objectDataList.add(newObjectData);
        }
        return objectDataList;
    }

    private List<IObjectData> getProductObjectData(ActionContext context, List<String> products) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(products)) {
            return objectDataList;
        }
        for (String product : products) {
            String id = serviceFacade.generateId();
            Map<String, Object> iterm = Maps.newHashMap();
            iterm.put(NAME, product);
            IObjectData newObjectData = getCommonObjectData(context.getUser(), iterm, id, SFAPreDefineObject.CompetitiveProducts);
            objectDataList.add(newObjectData);
        }
        return objectDataList;
    }

    private List<Integer> getCallerType(List<String> types) {
        return types.stream().map(ProcurementCallerTypeEnum::toIntegerValue).collect(Collectors.toList());
    }

    private static IObjectData getCommonObjectData(User user, Map<String, Object> iterm, String id, SFAPreDefineObject procurementEnterprise) {
        iterm.put(_ID, id);
        iterm.put(OWNER, Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
        if (user.isOutUser()){
            iterm.put(OUR_OWNER, Lists.newArrayList(user.getOutUserId()));
            iterm.put(OUR_TENANT_ID, user.getOutTenantId());
        }
        iterm.put(RELEVANT_TEAM, transitionRelevantTeam(user));
        iterm.put(TENANT_ID, user.getTenantId());
        iterm.put(OBJECT_DESCRIBE_API_NAME, procurementEnterprise.getApiName());
        iterm.put(ProcurementConstants.ProcurementCommonConstants.IS_DELETED, false);
        iterm.put(ProcurementConstants.ProcurementCommonConstants.RECORD_TYPE, "default__c");
        iterm.put(LIFE_STATUS, "normal");
        return ObjectDataExt.of(iterm).getObjectData();
    }

    private ArrayList<IObjectData> getContactObjectData(User user, List<ContactEntity> contactEntities, ProcurementContactTypeEnum type) {
        ArrayList<IObjectData> objectDataList = Lists.newArrayList();
        for (ContactEntity entity : contactEntities) {
            String id = serviceFacade.generateId();
            Map<String, Object> iterm = Maps.newHashMap();
            iterm.put(NAME, entity.getName());
            iterm.put(CONTACT_TYPE, type.getValue());
            iterm.put(MOBILE1, entity.getPhone());
            IObjectData newObjectData = getCommonObjectData(user, iterm, id, SFAPreDefineObject.ProcurementContact);
            objectDataList.add(newObjectData);
        }
        return objectDataList;
    }

    private ArrayList<IObjectData> getJuryObjectData(User user, List<JuryEntity> juryEntities) {
        ArrayList<IObjectData> objectDataList = Lists.newArrayList();
        for (JuryEntity entity : juryEntities) {
            String id = serviceFacade.generateId();
            Map<String, Object> iterm = Maps.newHashMap();
            iterm.put(NAME, entity.getJuryName());
            iterm.put(JURY_POSITION, entity.getJuryPosition());
            IObjectData newObjectData = getCommonObjectData(user, iterm, id, SFAPreDefineObject.ProcurementContact);
            objectDataList.add(newObjectData);
        }
        return objectDataList;
    }

    public IObjectData saveInfoData(ActionContext context, ProcurementDetail detailInfo, Map<String, Object> iterm) {
        UnitEntity caller = detailInfo.getCaller();
        if (caller != null) {
            iterm.put(CALLER_ENTERPRISE_NAME, caller.getName());
            iterm.put(CALLER_BUDGET, caller.getAmount());
            iterm.put(CALLER_CONTACT, getContact(caller.getContactPersons()));
        }
        iterm.put(TITLE, detailInfo.getTitle());
        iterm.put(NAME, detailInfo.getTitle());
        iterm.put(BID_TYPE, detailInfo.getBidType());
        iterm.put(BID_ID, detailInfo.getBidId());
        iterm.put(BID_SUB_TYPE, detailInfo.getBidSubType());
        iterm.put(PUBLISH_TIME, detailInfo.getPublishTime());
        iterm.put(CITY, detailInfo.getCity());
        iterm.put(PROVINCE, detailInfo.getProvince());
        Result area = getProvinceCityCode(detailInfo.getProvince(), detailInfo.getCity());
        if (area != null) {
            iterm.put("area_country", area.getCountry());
            iterm.put("area_province", area.getProvince());
            iterm.put("area_city", area.getCity());
        }
        iterm.put(PROJECT_NAME, detailInfo.getProjectName());
        iterm.put(PROJECT_NUMBER, detailInfo.getProjectNumber());
        iterm.put(BIDING_END_TIME, detailInfo.getBidingEndTime());
        iterm.put(TENDER_END_TIME, detailInfo.getTenderEndTime());
        iterm.put(WINNER_AMOUNT, detailInfo.getWinnerAmount());
        iterm.put(WINNER_AMOUNT_PCT, detailInfo.getWinnerAmountPct());
        iterm.put(CONTENT, detailInfo.getContent());
        iterm.put(SOURCE_URL, ProcurementUtils.encodeURL(detailInfo.getBidUrl()));
        iterm.put(PARTNER_URL, ProcurementUtils.encodeURL(detailInfo.getBidUrl()));
        iterm.put(CALLER_METHOD, detailInfo.getBidMethod());
        if (!CollectionUtils.isEmpty(detailInfo.getCallerNameType())) {
            iterm.put(CALLER_TYPE, detailInfo.getCallerNameType());
        }
        iterm.put(WINNER_CONTACT, getUnitEntityContact(detailInfo.getWinners()));
        iterm.put(AGENT_CONTACT, getUnitEntityContact(detailInfo.getAgents()));
        iterm.put(JURY, getJury(detailInfo.getJuries()));
        iterm.put(ID, serviceFacade.generateId());
        iterm.put(OWNER, Lists.newArrayList(context.getUser().getUpstreamOwnerIdOrUserId()));
        iterm.put(IS_DELETED, false);
        iterm.put(TENANT_ID, context.getTenantId());
        iterm.put(RECORD_TYPE, "default__c");
        iterm.put(OBJECT_DESCRIBE_API_NAME, SFAPreDefineObject.ProcurementInfo.getApiName());
        iterm.put("is_qlm_ai_filtration", detailInfo.getQlmAiFiltration());
        iterm.put("qlm_ai_expand_corpore",detailInfo.getQlmAiExpandCorpore());
        iterm.put("qlm_ai_expand_hospital",detailInfo.getQlmAiExpandHospital());
        IObjectData newObjectData = ObjectDataExt.of(iterm).getObjectData();
        if (newObjectData.getOwner() != null) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(newObjectData);
            List<TeamMember> teamMembers = new ArrayList<>();
            teamMembers.add(new TeamMember(newObjectData.getOwner().get(0), TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, TeamMember.MemberType.EMPLOYEE));
            objectDataExt.addTeamMembers(teamMembers);
        }
        return newObjectData;
    }


    public BaseObjectSaveAction.Result triggerAction(ActionContext context, String apiName, ObjectDataDocument objectData) {
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        ActionContext actionContext = new ActionContext(context.getRequestContext(), apiName, ObjectAction.CREATE.getActionCode());
        arg.setObjectData(objectData);
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setIsDuplicateSearch(false);
        optionInfo.setUseValidationRule(false);
        optionInfo.setSkipFuncValidate(false);
        optionInfo.setFromMapping(false);
        arg.setOptionInfo(optionInfo);
        String body = JSON.toJSONString(arg);
        log.debug("ProcurementInfo > triggerAction()  body{}: ", body);
        BaseObjectSaveAction.Result saveResult = serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
        log.debug("ProcurementInfo > triggerAction()  result{}: ", saveResult);
        return saveResult;
    }

    private List<IObjectData> getObjectDataIgnoreAll(User user, String apiName, SearchTemplateQuery searchQuery) {
        QueryResult<IObjectData> rst = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, apiName, searchQuery);
        return rst == null ? null : rst.getData();
    }


    private String getContact(List<ContactEntity> contactEntities) {
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isEmpty(contactEntities)) {
            return sb.toString();
        }
        for (ContactEntity contactEntity : contactEntities) {
            if (StringUtils.isNotBlank(contactEntity.getName())) {
                sb.append(contactEntity.getName());
                sb.append("：");
            }
            sb.append(contactEntity.getPhone());
            sb.append("；");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    public String getJury(List<JuryEntity> juryEntities) {
        List<String> jurys = new ArrayList<>();
        if (CollectionUtils.isEmpty(juryEntities)) {
            return "";
        }
        for (JuryEntity jury : juryEntities) {
            StringBuilder sb = new StringBuilder();
            sb.append(jury.getJuryName());
            if (StringUtils.isNotEmpty(jury.getJuryPosition())) {
                sb.append(":");
                sb.append(jury.getJuryPosition());
            }
            jurys.add(sb.toString());
        }
        return StringUtils.join(jurys, ";");
    }

    private static final String SeparatorByNameAndPhone = "，";
    private static final String SeparatorByUnitAndInfo = "：";
    private static final String SeparatorByUnit = "；";

    private String getUnitEntityContactInfo(UnitEntity entity) {
        StringBuilder contacts = new StringBuilder();
        for (ContactEntity contactPerson : entity.getContactPersons()) {
            if (StringUtils.isNotBlank(contactPerson.getName())) {
                contacts.append(contactPerson.getName());
                contacts.append(SeparatorByNameAndPhone);
            }
            contacts.append(contactPerson.getPhone());
            contacts.append(SeparatorByUnit);
        }
        return contacts.toString();
    }

    private String getUnitEntityContact(String original, List<UnitEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return original;
        }
        Map<String, String> map = new LinkedHashMap<>();
        if (!StringUtils.isEmpty(original)) {
            String[] originals = original.split(SeparatorByUnitAndInfo + "|" + SeparatorByUnit);
            for (int i = 0; i < originals.length; i += 2) {
                map.put(originals[i], originals[i + 1]);
            }
        }
        for (UnitEntity entity : entities) {
            if (CollectionUtils.isNotEmpty(entity.getContactPersons())) {
                map.put(entity.getName(), getUnitEntityContactInfo(entity));
            }
        }
        StringBuilder contacts = new StringBuilder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            contacts.append(entry.getKey()).append(SeparatorByUnitAndInfo).append(entry.getValue()).append(SeparatorByUnit);
        }
        if (contacts.length() > 0) {
            contacts.deleteCharAt(contacts.length() - 1);
        }
        return contacts.toString();
    }

    private String getUnitEntityContact(List<UnitEntity> entities) {
        return getUnitEntityContact(null, entities);
    }

    public Result getProvinceCityCode(String province, String city) {
        String countryCascadeJsonString = countryAreaService.getCountryCascadeJsonString();
        JSONObject area = JSON.parseObject(countryCascadeJsonString);
        Result result = new Result();
        result.setCountry("248");
        if (StringUtils.isNotEmpty(province)) {
            JSONArray jsonArrayPro = JSON.parseArray(area.getJSONObject("province").getString("options"));
            for (CountryAreaResult areaResult : jsonArrayPro.toJavaList(CountryAreaResult.class)) {
                if (areaResult.getLabel().contains(province)) {
                    result.setProvince(areaResult.getValue());
                    break;
                }
            }
        }
        if (StringUtils.isNotEmpty(city)) {
            JSONArray jsonArrayCity = JSON.parseArray(JSON.toJSONString(area.getJSONObject("city").get("options")));
            for (CountryAreaResult areaResult : jsonArrayCity.toJavaList(CountryAreaResult.class)) {
                if (areaResult.getLabel().contains(city)) {
                    result.setCity(areaResult.getValue());
                    break;
                }
            }
        }
        return result;
    }

    public static List<Map<String,Object>> transitionRelevantTeam(User user) {
        List<Map<String,Object>> relevantTeam = Lists.newArrayList();
        String userId = user.getUserId();
        if (ObjectUtils.isEmpty(userId)) {
            userId = "-10000";
        }
        TeamMember teamMember = new TeamMember(userId, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE);
        relevantTeam.add(teamMember.toMap());
        if (user.isOutUser()) {
            TeamMember outTeamMember = new TeamMember(user.getOutUserId(), TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, user.getOutTenantId());
            relevantTeam.add(outTeamMember.toMap());
        }
        return relevantTeam;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String country;
        private String province;
        private String city;
    }

}
