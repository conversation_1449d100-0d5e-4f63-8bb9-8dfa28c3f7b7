package com.facishare.crm.sfa.predefine.enums;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.Getter;

import static com.facishare.crm.sfa.prm.core.constants.PrmI18NConstants.PRM_ENUM_PARAM_TYPE_ERROR;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-21
 * ============================================================
 */
public enum ChannelEventTag {
    REGISTER("register_event"),
    SIGN("sign_event"),
    RENEW("renew_event");

    @Getter
    private final String tag;

    ChannelEventTag(String tag) {
        this.tag = tag;
    }

    public static ChannelEventTag from(String tag) {
        for (ChannelEventTag e : values()) {
            if (e.tag.equals(tag)) {
                return e;
            }
        }
        throw new ValidateException(I18N.text(PRM_ENUM_PARAM_TYPE_ERROR, tag));
    }

    public static ChannelEventTag find(String tag) {
        return find(tag, null);
    }

    public static ChannelEventTag find(String tag, ChannelEventTag defaultValue) {
        for (ChannelEventTag e : values()) {
            if (e.tag.equals(tag)) {
                return e;
            }
        }
        return defaultValue;
    }
}
