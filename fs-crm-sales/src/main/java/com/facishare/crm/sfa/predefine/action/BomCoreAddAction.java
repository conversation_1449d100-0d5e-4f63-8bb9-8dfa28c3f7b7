package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreV3Service;
import com.facishare.crm.sfa.predefine.service.task.BomConstraintTaskService;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.enums.EnumUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class BomCoreAddAction extends StandardAddAction {
    private final BomCoreV3Service bomCoreV3Service = SpringUtil.getContext().getBean(BomCoreV3Service.class);
    private final BomConstraintTaskService bomConstraintTaskService = SpringUtil.getContext().getBean(BomConstraintTaskService.class);
    private String rootId;
    private List<ObjectDataDocument> groupList = Lists.newArrayList();
    private List<String> fieldType = Lists.newArrayList("count", "formula", "number", "percentile", "currency");
    private List<IObjectData> tmpBomList = Lists.newArrayList();

    @Override
    protected void before(Arg arg) {
        rootId = serviceFacade.generateId();
        if (SFAConfigUtil.isSimpleBom(actionContext.getTenantId())) {
            arg.getObjectData().put(BomCoreConstants.FIELD_CATEGORY, BomCoreConstants.category.standard.getValue());
        }
        List<ObjectDataDocument> bomList = arg.getDetails().get(Utils.BOM_API_NAME);
        Set<String> noActiveNode = Sets.newHashSet();
        Set<String> noActiveGroup = Sets.newHashSet();
        if (CollectionUtils.notEmpty(bomList)) {
            if (MapUtils.getBooleanValue(arg.getObjectData(), "enableSpanTree", false)) {
                ObjectDataDocument bomObj = bomList.get(0);
                rootId = MapUtils.getString(bomObj, BomConstants.FIELD_ROOT_ID);
            } else {
                Map<String, List<ObjectDataDocument>> dataMap = bomCoreV3Service.analyzeBOMV3Tree(actionContext.getUser(), bomList, rootId, 0, Lists.newArrayList(), MapUtils.getString(arg.getObjectData(), BomConstants.FIELD_PRODUCT_ID));
                tmpBomList.addAll(ObjectDataExt.copyList(ObjectDataDocument.ofDataList(dataMap.getOrDefault(Utils.BOM_API_NAME, Lists.newArrayList()))));
                arg.getDetails().put(Utils.BOM_API_NAME, dataMap.getOrDefault(Utils.BOM_API_NAME, Lists.newArrayList()));
                dataMap.getOrDefault(Utils.PRODUCT_GROUP_API_NAME, Lists.newArrayList()).forEach(g -> {
                    if (BooleanUtils.isFalse(MapUtils.getBoolean(g, BomConstants.FIELD_ENABLED_STATUS))) {
                        noActiveGroup.add(g.getId());
                    }
                });
                groupList.addAll(dataMap.getOrDefault(Utils.PRODUCT_GROUP_API_NAME, Lists.newArrayList()));
            }

        }
        super.before(arg);
        if (Objects.nonNull(objectData) && Objects.equals(objectData.get(BomCoreConstants.FIELD_PURPOSE, String.class), BomCoreConstants.purpose.service.getValue())
                && !Objects.equals(objectData.get(BomCoreConstants.FIELD_CATEGORY, String.class), BomCoreConstants.category.configure.getValue())) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_SERVICE_TYPE_BOM_CAN_NOT_STANDARD));
        }
        List<IObjectData> nodeList = Lists.newArrayList();
        List<IObjectData> dataList = detailObjectData.getOrDefault(Utils.BOM_API_NAME, Lists.newArrayList());
        Map<String, IObjectData> dataMap = dataList.stream().collect(Collectors.toMap(DBRecord::getId, o -> o, (o1, o2) -> o1));
        dataList.forEach(x -> {
            String bomPath = x.get(BomConstants.FIELD_BOM_PATH, String.class);
            String productGroupId = x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class);
            if (StringUtils.isNotBlank(bomPath) && StringUtils.isNotBlank(productGroupId)&& noActiveGroup.contains(productGroupId)) {
                x.set(BomConstants.FIELD_ENABLED_STATUS, false);
                noActiveNode.add(x.getId()+".");
            }
            dataMap.put(x.getId(), x);
            if (StringUtils.isNotBlank(x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class))) {
                nodeList.add(x);
            }
            if (Objects.nonNull(arg.getObjectData()) && Objects.equals(arg.getObjectData().toObjectData().get(BomCoreConstants.FIELD_CATEGORY, String.class), BomCoreConstants.category.standard.getValue())) {
                x.set(BomConstants.FIELD_IS_REQUIRED, true);
                x.set(BomConstants.FIELD_SELECTED_BY_DEFAULT, true);
                x.set(BomConstants.FIELD_PRICE_EDITABLE, false);
                x.set(BomConstants.FIELD_AMOUNT_EDITABLE, false);
                x.set(BomConstants.FIELD_INCREMENT, "");
            }
            if (StringUtils.equals(objectData.get(BomConstants.FIELD_PRODUCT_ID, String.class), x.get(BomConstants.FIELD_PRODUCT_ID, String.class))) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_RELATED_BOM_CYCLE, ""));
            }
            IObjectData parentNode = dataMap.get(x.get(BomConstants.FIELD_PARENT_BOM_ID, String.class));
            if (parentNode != null && (StringUtils.isNotBlank(parentNode.get(BomConstants.FIELD_RELATED_CORE_ID, String.class)) || BooleanUtils.isTrue(parentNode.get(BomConstants.FIELD_IS_PACKAGE, Boolean.class, false)))) {
                throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_RELATED_BOM_CAN_NOT_CHILD_NODE));
            }
        });
        if (CollectionUtils.notEmpty(noActiveNode)) {
            dataList.forEach(x->{
                String bomPath = x.get(BomConstants.FIELD_BOM_PATH, String.class);
                if (StringUtils.isNotBlank(bomPath)) {
                    noActiveNode.stream().filter(bomPath::contains).findFirst().ifPresent(n -> x.set(BomConstants.FIELD_ENABLED_STATUS, false));
                }
            });
        }
        bomCoreV3Service.checkNonStandardProduct(objectData, actionContext.getUser());
        bomCoreV3Service.checkQuantity(actionContext.getTenantId(), dataList.size());
        bomCoreV3Service.checkBomCycle(actionContext.getUser(), objectData, nodeList, 1);
        bomCoreV3Service.checkType(detailObjectData.get(Utils.BOM_API_NAME), this.objectData);
    }

    @Override
    protected void validateDetail(String detailApiName, List<IObjectData> detailDataList) {
        if (Objects.equals(detailApiName, Utils.BOM_API_NAME)) {
            IObjectDescribe describe = this.objectDescribes.get(Utils.BOM_API_NAME);
            if (describe != null) {
                MasterDetailFieldDescribe masterDetailField = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().orElse(null);
                if (masterDetailField != null && masterDetailField.getIsRequiredWhenMasterCreate()) {
                    if (CollectionUtils.notEmpty(groupList) && CollectionUtils.empty(detailDataList)) {
                        masterDetailField.setIsRequiredWhenMasterCreate(false);
                    }
                }
            }
        }
        super.validateDetail(detailApiName, detailDataList);
    }

    @Override
    protected void doSaveData() {
        if (CollectionUtils.notEmpty(groupList)) {
            serviceFacade.bulkSaveObjectData(ObjectDataDocument.ofDataList(groupList), actionContext.getUser());
        }
        createRootNode(actionContext.getUser());
        super.doSaveData();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        User user = actionContext.getUser();
        bomCoreV3Service.updateProductIsPackage(user, Lists.newArrayList(objectData.get(BomConstants.FIELD_PRODUCT_ID, String.class)), true);
        OptionInfo optionInfo = arg.getOptionInfo();
        if (optionInfo != null && BooleanUtils.isTrue(optionInfo.isFromClone()) && Objects.equals(optionInfo.getFromApiName(), Utils.BOM_CORE_API_NAME)) {
            String masterId = optionInfo.getFromId();
            if (StringUtils.isNotBlank(masterId) && CollectionUtils.notEmpty(tmpBomList)) {
                IObjectData tmpObjectData = ObjectDataExt.of(objectData).copy();
                tmpObjectData.set("originId", masterId);
                tmpObjectData.set(BomConstants.FIELD_BOM_ID, rootId);
                bomConstraintTaskService.sendMessage(actionContext.getUser(), EnumUtil.actionType.create.getValue(), tmpObjectData, tmpBomList);
            }
        }
        return super.after(arg, result);
    }

    private void createRootNode(User user) {
        if (MapUtils.getBooleanValue(arg.getObjectData(), "enableSpanTree", false)) {
            return;
        }
        List<IObjectData> details = detailObjectData.getOrDefault(Utils.BOM_API_NAME, Lists.newArrayList());
        IObjectDescribe describe = objectDescribes.get(Utils.BOM_API_NAME);
        IObjectData bomData;
        if (CollectionUtils.notEmpty(details)) {
            bomData = ObjectDataExt.of(details.get(0)).copy();
        } else {
            bomData = new ObjectData();
        }
        if (Objects.nonNull(describe)) {
            describe.getFieldDescribes().stream().filter(x -> !Objects.equals(x.getDefineType(), "system") && fieldType.contains(x.getType())).forEach(x -> bomData.set(x.getApiName(), "0"));
            log.warn("bom describe is null");
        }
        bomData.set(BomConstants.FIELD_PRODUCT_ID, objectData.get(BomConstants.FIELD_PRODUCT_ID));
        bomData.set(BomConstants.FIELD_PARENT_PRODUCT_ID, objectData.get(BomConstants.FIELD_PRODUCT_ID));
        bomData.setDescribeApiName(Utils.BOM_API_NAME);
        bomData.setTenantId(user.getTenantId());
        bomData.setLastModifiedTime(System.currentTimeMillis());
        bomData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
        bomData.setCreateTime(System.currentTimeMillis());
        bomData.setCreatedBy(user.getUpstreamOwnerIdOrUserId());
        bomData.set(BomConstants.FIELD_ORDER_FIELD, 0);
        bomData.setId(rootId);
        bomData.set(BomConstants.FIELD_RELATED_CORE_ID, null);
        bomData.set(BomConstants.FIELD_NODE_BOM_CORE_TYPE, null);
        bomData.set(BomConstants.FIELD_NODE_BOM_CORE_VERSION, null);
        bomData.set(BomConstants.FIELD_CORE_ID, objectData.getId());
        bomData.set(BomConstants.FIELD_ROOT_ID, rootId);
        bomData.set(BomConstants.FIELD_BOM_PATH, rootId);
        bomData.set(BomConstants.FIELD_PARENT_BOM_ID, null);
        bomData.set(BomConstants.FIELD_PRODUCT_GROUP_ID, null);
        bomData.set(BomConstants.FIELD_UNIT_ID, null);
        bomData.set(BomConstants.SHARE_RATE, null);
        bomData.set(BomConstants.FIELD_ENABLED_STATUS, true);
        bomData.set(BomConstants.FIELD_AMOUNT, "1");
        details.add(bomData);
    }
}
