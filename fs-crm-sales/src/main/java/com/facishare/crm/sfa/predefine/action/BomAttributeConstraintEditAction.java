package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.cpq.BomConstraintService;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @instruction
 */
public class BomAttributeConstraintEditAction extends StandardEditAction {
    private final BomConstraintService bomConstraintService = SpringUtil.getContext().getBean(BomConstraintService.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        checkRule();
        stopWatch.lap("checkRule");
    }

    //校验约束关系
    private void checkRule(){
        List<IObjectData> dataList = Lists.newArrayList(detailsToUpdate);
        dataList.addAll(detailsToAdd);
        bomConstraintService.checkRule(dataList, actionContext.getUser(), objectData);
    }
}
