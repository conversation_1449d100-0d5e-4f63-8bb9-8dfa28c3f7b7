package com.facishare.crm.sfa.predefine.service.email;

import com.facishare.crm.constants.CRMFeedConstants;
import com.facishare.crm.sfa.model.EmailAccountModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.proxy.FeedsProxy;
import com.facishare.crm.sfa.utilities.proxy.model.FeedsModel;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectLockStatus;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.DataRightsParameter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@ServiceModule("email_account")
@AllArgsConstructor
public class EmailAccountService implements InitializingBean {
    static final String PERSONNEL_API_NAME = "PersonnelObj";
    static final String EMAIL_ACCOUNT_API_NAME = SFAPreDefineObject.EmailAccount.getApiName();
    static final String ACCOUNT_ID = "account_id";
    static final String EMAIL = "email";
    private final Set<String> personalEmailDomain = new HashSet<>();
    private ServiceFacade serviceFacade;
    private FeedsProxy feedsProxy;

    @ServiceMethod("init_by_email")
    public EmailAccountModel.Result<Map<String, Object>> initByEmail(ServiceContext context, EmailAccountModel.InitArg arg) {
        List<String> emails = getEmails(arg);
        User user = context.getUser();
        IObjectData currentUser = findUserByUserId(user);
        String currentUserEmail = currentUser.get(EMAIL, String.class);
        ExternalScorer externalScorer = new ExternalScorer(currentUser, personalEmailDomain, serviceFacade);
        List<String> internal = new ArrayList<>();
        List<String> external = new ArrayList<>();
        boolean accessIfExist = "write".equals(arg.getAction());
        for (String email : emails) {
            if (Boolean.TRUE.equals(externalScorer.matcher.apply(email))) {
                external.add(email);
            } else {
                internal.add(email);
            }
            if (email.equals(currentUserEmail)) {
                accessIfExist = true;
            }
        }
        if (external.isEmpty()) {
            HashMap<String, Object> data = new HashMap<>();
            data.put("is_internal", true);
            data.put("no_permission", true);
            return new EmailAccountModel.Result<>("200", "success", data);
        }
        HashMap<String, Object> data = initByEmail0(user, accessIfExist, external, internal);
        return new EmailAccountModel.Result<>("200", "success", data);
    }

    /*
     * 展示逻辑：（大原则：外部人员＞内部人员，收/发件人＞抄送人）
     *  有外部人员：
     *  我发给别人：展示收件人中的第一个外部人员
     *  别人发给我：展示发件人中的第一个外部人员
     *  收件人和发件人都是内部人员：展示抄送人中的第一个外部人员
     *  内部邮件：
     *  我发给别人：展示收件人中的第一个人员
     *  别人发给我：展示发件人中的第一个人员
     */
    @ServiceMethod("query_by_email")
    public QueryResult<ObjectDataDocument> queryByEmail(ServiceContext context, EmailAccountModel.ByEmailArg arg) {
        // 需要获取当前登录用户的email
        List<String> emails = getEmails(arg);
        User user = context.getUser();
        QueryResult<IObjectData> queryResult = findByEmailWithDataRights(user, emails);
        List<IObjectData> emailAccountList = queryResult.getData();
        if (!emailAccountList.isEmpty()) {
            IObjectData currentUser = findUserByUserId(user);
            fillRelated(user, emailAccountList);
            if (emailAccountList.size() > 1) {
                ExternalScorer externalScorer = new ExternalScorer(currentUser, personalEmailDomain, serviceFacade);
                MailArgScorer mailArgScorer = new MailArgScorer(currentUser, arg, externalScorer.matcher);
                Comparator<IObjectData> comparator = Comparator.comparingInt(data -> {
                    String email = data.get(EMAIL, String.class);
                    int score = externalScorer.score(email) + mailArgScorer.score(email);
                    data.set("email_score__c", score);
                    return score;
                });
                emailAccountList.sort(comparator.reversed());
            }
        }
        return convert(queryResult);
    }

    @ServiceMethod("find_related_approximately")
    public List<ObjectDataDocument> findRelatedApproximately(ServiceContext context, EmailAccountModel.FindRelatedArg arg) {
        List<IObjectData> emailAccountList = serviceFacade.findObjectDataByIds(context.getTenantId(), Collections.singletonList(arg.getId()), EMAIL_ACCOUNT_API_NAME);
        if (emailAccountList.isEmpty()) {
            return Collections.emptyList();
        }
        IObjectData emailAccount = emailAccountList.get(0);
        if (personalEmailDomain.contains(emailAccount.getName())) {
            return Collections.emptyList();
        }
        arg.setEmailAccount(emailAccount);
        String relatedApiName = arg.getRelatedApiName();
        if (SFAPreDefineObject.Account.getApiName().equals(relatedApiName)) {
            return findAccountApproximately(context.getUser(), arg);
        }
        if (SFAPreDefineObject.Contact.getApiName().equals(relatedApiName)) {
            return findContactApproximately(context.getUser(), arg);
        }
        // 邮箱匹配的线索
        // 邮箱匹配的客户关联的线索
        if (SFAPreDefineObject.Leads.getApiName().equals(relatedApiName)) {
            return findLeadsApproximately(context.getUser(), arg);
        }
        return Collections.emptyList();
    }

    @ServiceMethod("query_related_by_email")
    public Map<String, List<ObjectDataDocument>> findRelatedByEmail(ServiceContext context, EmailAccountModel.ByEmailArg arg) {
        List<String> emails = getEmails(arg);
        Map<String, List<ObjectDataDocument>> res = new HashMap<>();
        User user = context.getUser();
        List<IObjectData> direct = findByEmailWithDataRights(user, emails).getData();
        if (direct.isEmpty()) {
            res.put("direct", Collections.emptyList());
            return res;
        }
        List<String> ids = direct.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<String> relatedAccountIds = new ArrayList<>();
        List<String> relatedContactIds = new ArrayList<>();
        for (IObjectData data : direct) {
            ids.add(data.getId());
            if (data.get(ACCOUNT_ID) != null) {
                relatedAccountIds.add(data.get(ACCOUNT_ID).toString());
            }
            if (data.get("contact_id") != null) {
                relatedContactIds.add(data.get("contact_id").toString());
            }
        }
        List<IObjectData> relatedContact = !relatedContactIds.isEmpty() ? serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), relatedContactIds, SFAPreDefineObject.Contact.getApiName()) : new ArrayList<>();
        List<IObjectData> relatedAccount = !relatedAccountIds.isEmpty() ? serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), relatedAccountIds, SFAPreDefineObject.Account.getApiName()) : new ArrayList<>();
        List<IObjectData> relatedLeads = findRelatedData0(user, SFAPreDefineObject.Leads.getApiName(), ids);
        relatedContact.addAll(relatedAccount);
        relatedContact.addAll(relatedLeads);
        res.put("direct", convert(relatedContact));
        return res;
    }

    @ServiceMethod("sync_email")
    public FeedsModel.FeedsSaveActionResult syncEmail(ServiceContext context, EmailAccountModel.SyncEmailArg arg) {
        User user = context.getUser();
        Map<String, String> headers = convert(context, user);
        ObjectDataDocument document = new ObjectDataDocument();
        document.put(CRMFeedConstants.Field.RELATED_OBJECT, arg.getRelatedObject());
        document.put(CRMFeedConstants.Field.ACTIVE_RECORD_CONTENT, arg.getContent());
        FeedsModel.FeedsSaveActionArg actionArg = FeedsModel.FeedsSaveActionArg.builder().object_data(document).source(501).build();
        return feedsProxy.publishFeed(headers, actionArg);
    }

    @ServiceMethod("associate_to")
    public EmailAccountModel.Result<Object> associateTo(ServiceContext context, EmailAccountModel.AssociateToArg arg) {
        List<IObjectData> emailAccountList = serviceFacade.findObjectDataByIds(context.getTenantId(), Collections.singletonList(arg.getId()), EMAIL_ACCOUNT_API_NAME);
        if (emailAccountList.isEmpty()) {
            return new EmailAccountModel.Result<>("500", "empty EmailAccountObj", null);
        }
        IObjectData emailAccount = emailAccountList.get(0);
        String actionCode = null;
        String relateField = "";
        switch (arg.getAssociateToApiName()) {
            case "AccountObj":
                actionCode = "AssociateToAccount";
                relateField = ACCOUNT_ID;
                break;
            case "ContactObj":
                actionCode = "AssociateToContact";
                relateField = "contact_id";
                break;
            case "LeadsObj":
                actionCode = "AssociateToLeads";
                break;
            default:
                break;
        }
        User user = context.getUser();
        if (actionCode == null || !serviceFacade.funPrivilegeCheck(user, EMAIL_ACCOUNT_API_NAME, actionCode)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_UPLOAD_NOT_HAVE_PERMISSION));
        }
        if (arg.getAssociateToApiName().equals(SFAPreDefineObject.Leads.getApiName())) {
            List<IObjectData> leadsList = serviceFacade.findObjectDataByIds(context.getTenantId(), Collections.singletonList(arg.getAssociateToId()), SFAPreDefineObject.Leads.getApiName());
            if (!leadsList.isEmpty()) {
                IObjectData leads = leadsList.get(0);
                leads.set("email_account_id", emailAccount.getId());
                serviceFacade.updateObjectData(user, leads);
            }
        } else {
            emailAccount.set(relateField, arg.getAssociateToId());
            serviceFacade.updateObjectData(user, emailAccount);
        }
        return new EmailAccountModel.Result<>("200", "success", null);
    }

    static List<String> getEmails(EmailAccountModel.ByEmailArg arg) {
        List<String> emails = new ArrayList<>();
        if (arg.getFrom() != null) {
            emails.add(arg.getFrom());
        }
        if (arg.getTo() != null) {
            emails.addAll(arg.getTo());
        }
        if (arg.getCc() != null) {
            emails.addAll(arg.getCc());
        }
        if (emails.size() > 200) {
            emails = emails.subList(0, 200);
        }
        return emails;
    }

    private IObjectData findUserByUserId(User user) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, "user_id", user.getUserId());
        query.setPermissionType(0);
        query.setLimit(1);
        List<IObjectData> userList = serviceFacade.findBySearchQueryIgnoreAll(user, PERSONNEL_API_NAME, query).getData();
        return userList.get(0);
    }

    private HashMap<String, Object> initByEmail0(User user, boolean accessIfExist, List<String> external, List<String> internal) {
        Map<String, IObjectData> exist = findByEmailIgnoreAll(user, external).stream().collect(Collectors.toMap(data -> data.get(EMAIL, String.class, ""), Function.identity(), (v1, v2) -> v1));
        Map<String, Permissions> permissionsMap = serviceFacade.checkDataPrivilege(user, exist.values().stream().map(DBRecord::getId).collect(Collectors.toList()), serviceFacade.findObject(user.getTenantId(), EMAIL_ACCOUNT_API_NAME));
        List<IObjectData> updateEmails = new ArrayList<>();
        boolean atLeastOnePermission = false;
        for (Map.Entry<String, IObjectData> entry : exist.entrySet()) {
            IObjectData data = entry.getValue();
            Permissions permissions = permissionsMap.get(data.getId());
            if (Permissions.READ_ONLY == permissions || Permissions.READ_WRITE == permissions) {
                atLeastOnePermission = true;
            }
            if (accessIfExist && Permissions.NO_PERMISSION == permissions) {
                updateEmails.add(data);
            }
        }
        external.removeIf(exist::containsKey);
        saveEmailAccount(user, internal, external);
        updateEmailAccount(user, updateEmails);
        HashMap<String, Object> data = new HashMap<>();
        data.put("is_internal", false);
        data.put("no_permission", external.isEmpty() && !accessIfExist && !atLeastOnePermission);
        return data;
    }

    private void saveEmailAccount(User user, List<String> internal, List<String> external) {
        if (external.isEmpty()) {
            return;
        }
        List<TeamMember> teamMemberList = new ArrayList<>();
        if (!internal.isEmpty()) {
            Set<String> memberIds = findByEmail0(User.systemUser(user.getTenantId()), internal).stream().map(IObjectData::getId).collect(Collectors.toSet());
            memberIds.remove(user.getUserId());
            teamMemberList.addAll(memberIds.stream().map(id -> new TeamMember(id, TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READONLY)).collect(Collectors.toList()));
        }
        List<IObjectData> insertList = new ArrayList<>(external.size());
        for (String email : external) {
            IObjectData insert = newEmailAccount(user, email);
            ObjectDataExt.of(insert).addTeamMembers(teamMemberList);
            insertList.add(insert);
        }
        serviceFacade.bulkSaveObjectData(insertList, user);
    }

    private void updateEmailAccount(User user, List<IObjectData> updateEmails) {
        if (updateEmails.isEmpty()) {
            return;
        }
        for (IObjectData data : updateEmails) {
            TeamMember teamMember = new TeamMember(user.getUserId(), TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READONLY);
            ObjectDataExt.of(data).addTeamMembers(Collections.singletonList(teamMember));
        }
        serviceFacade.batchUpdateRelevantTeam(user, updateEmails, true);
    }

    private QueryResult<IObjectData> findByEmailWithDataRights(User user, List<String> emails) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterHasAnyOf(filters, EMAIL, emails);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setLimit(200);
        withDataRights(user, query);
        return serviceFacade.findBySearchQuery(user, EMAIL_ACCOUNT_API_NAME, query);
    }

    private void withDataRights(User user, SearchTemplateQuery query) {
        if (!user.isSupperAdmin() && !serviceFacade.isAdmin(user)) {
            IDataRightsParameter parameter = new DataRightsParameter();
            parameter.setSceneType("all");
            query.setPermissionType(1);
            query.setDataRightsParameter(parameter);
        }
    }

    private void fillRelated(User user, List<IObjectData> dataList) {
        List<String> ids = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        Map<String, List<IObjectData>> relatedLeads = findRelatedData(user, SFAPreDefineObject.Leads.getApiName(), ids);
        for (IObjectData data : dataList) {
            List<IObjectData> relatedLeadsList = relatedLeads.get(data.getId());
            if (relatedLeadsList != null) {
                data.set("related_leads_list", convert(relatedLeadsList));
            }
        }
    }

    private Map<String, List<IObjectData>> findRelatedData(User user, String objectApiName, List<String> emailAccountIds) {
        List<IObjectData> data = findRelatedData0(user, objectApiName, emailAccountIds);
        return data.stream().collect(Collectors.groupingBy(d -> String.valueOf(d.get("email_account_id"))));
    }

    private List<IObjectData> findRelatedData0(User user, String objectApiName, List<String> emailAccountIds) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterHasAnyOf(filters, "email_account_id", emailAccountIds);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setLimit(10);
        return serviceFacade.findBySearchQuery(user, objectApiName, query).getData();
    }

    private List<IObjectData> findByEmail0(User user, List<String> emails) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterHasAnyOf(filters, EMAIL, emails);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setLimit(emails.size());
        return serviceFacade.findBySearchQuery(user, EmailAccountService.PERSONNEL_API_NAME, query).getData();
    }

    private List<IObjectData> findByEmailIgnoreAll(User user, List<String> emails) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterHasAnyOf(filters, EMAIL, emails);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setLimit(emails.size());
        query.setPermissionType(0);
        return serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, EmailAccountService.EMAIL_ACCOUNT_API_NAME, query, Lists.newArrayList(DBRecord.ID, EMAIL, Tenantable.TENANT_ID, IObjectData.DESCRIBE_API_NAME)).getData();
    }

    // 邮箱匹配的客户
    // 邮箱匹配的联系人关联的客户
    private List<ObjectDataDocument> findAccountApproximately(User user, EmailAccountModel.FindRelatedArg arg) {
        List<IFilter> filters = new ArrayList<>();
        SearchTemplateQuery query = buildEndWithDomainQuery(user, filters, arg.getEmailAccount());
        query.setFilters(filters);
        List<IObjectData> accountList = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.Account.getApiName(), query).getData();
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setGroupBy(Collections.singletonList(ACCOUNT_ID));
        SearchUtil.fillFilterIsNotNull(filters, ACCOUNT_ID);
        query.resetFilters(filters);
        query.setGroupByParameter(groupByParameter);
        List<IObjectData> aggDataList = serviceFacade.aggregateFindBySearchQuery(user, query, SFAPreDefineObject.Contact.getApiName());
        if (!aggDataList.isEmpty()) {
            Set<String> existIds = accountList.stream().map(IObjectData::getId).collect(Collectors.toSet());
            List<String> accountIds = aggDataList.stream().map(data -> data.get(ACCOUNT_ID, String.class)).collect(Collectors.toList());
            accountIds.removeIf(existIds::contains);
            accountList.addAll(serviceFacade.findObjectDataByIds(user.getTenantId(), accountIds, SFAPreDefineObject.Account.getApiName()));
        }
        serviceFacade.fillExtendFieldInfo(serviceFacade.findObject(user.getTenantId(), SFAPreDefineObject.Account.getApiName()), accountList, user);
        return convert(accountList);
    }

    // 邮箱匹配的联系人
    // 邮箱匹配的客户的联系人
    // 邮箱匹配的联系人的所属客户关联的联系人
    private List<ObjectDataDocument> findContactApproximately(User user, EmailAccountModel.FindRelatedArg arg) {
        List<IFilter> filters = new ArrayList<>();
        SearchTemplateQuery query = buildEndWithDomainQuery(user, filters, arg.getEmailAccount());
        query.setFilters(filters);
        List<IObjectData> contactList = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.Contact.getApiName(), query).getData();
        query.resetFilters(filters);
        List<IObjectData> accountList = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.Account.getApiName(), query).getData();
        List<String> accountIds /*邮箱匹配的客户ID*/ = accountList.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<String> accountInContactIds /*邮箱匹配的联系人的所属客户ID*/ = contactList.stream().filter(data -> data.get(ACCOUNT_ID) != null).map(data -> data.get(ACCOUNT_ID, String.class)).collect(Collectors.toList());
        accountIds.addAll(accountInContactIds);
        Optional.ofNullable(arg.getEmailAccount().get(ACCOUNT_ID, String.class)).ifPresent(accountIds::add);
        if (!accountIds.isEmpty()) {
            filters.clear();
            SearchUtil.fillFilterHasAnyOf(filters, ACCOUNT_ID, accountIds);
            query.resetFilters(filters);
            query.setLimit(accountIds.size());
            // 邮箱匹配的客户的联系人 + 邮箱匹配的联系人的所属客户关联的联系人
            List<IObjectData> relatedContactList = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.Contact.getApiName(), query).getData();
            contactList.addAll(relatedContactList);
        }
        ArrayList<IObjectData> distinct = distinct(contactList);
        serviceFacade.fillExtendFieldInfo(serviceFacade.findObject(user.getTenantId(), SFAPreDefineObject.Contact.getApiName()), distinct, user);
        return convert(distinct);
    }

    // 邮箱匹配的线索
    // 邮箱匹配的客户关联的线索转换纪录(的线索)
    private List<ObjectDataDocument> findLeadsApproximately(User user, EmailAccountModel.FindRelatedArg arg) {
        List<IFilter> filters = new ArrayList<>();
        SearchTemplateQuery query = buildEndWithDomainQuery(user, filters, arg.getEmailAccount());
        query.setFilters(filters);
        List<IObjectData> leadsList = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.Leads.getApiName(), query).getData();
        query.resetFilters(filters);
        List<IObjectData> accountList = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.Account.getApiName(), query).getData();
        if (!accountList.isEmpty()) {
            List<String> accountIds = accountList.stream().map(IObjectData::getId).collect(Collectors.toList());
            filters.clear();
            SearchUtil.fillFilterHasAnyOf(filters, ACCOUNT_ID, accountIds);
            query.setDataRightsParameter(null);
            query.setPermissionType(0);
            query.resetFilters(filters);
            List<IObjectData> leadsTransferLogList = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.LeadsTransferLog.getApiName(), query).getData();
            if (!leadsTransferLogList.isEmpty()) {
                List<String> leadsId = leadsTransferLogList.stream().map(data -> data.get("leads_id", String.class)).collect(Collectors.toList());
                leadsList.addAll(serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), leadsId, SFAPreDefineObject.Leads.getApiName()));
            }
        }
        ArrayList<IObjectData> distinct = distinct(leadsList);
        serviceFacade.fillExtendFieldInfo(serviceFacade.findObject(user.getTenantId(), SFAPreDefineObject.Leads.getApiName()), distinct, user);
        return convert(distinct);
    }

    private SearchTemplateQuery buildEndWithDomainQuery(User user, List<IFilter> filters, IObjectData emailAccount) {
        String domain = emailAccount.get(EMAIL, String.class, "@").split("@")[1];
        Filter filter = new Filter();
        filter.setFieldName(EMAIL);
        filter.setFieldValues(Collections.singletonList(domain));
        filter.setOperator(Operator.ENDWITH);
        filters.add(filter);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(10);
        withDataRights(user, query);
        return query;
    }

    private static IObjectData newEmailAccount(User user, String email) {
        IObjectData emailAccount = new ObjectData();
        emailAccount.setName(email.substring(0, email.indexOf("@")));
        emailAccount.set(EMAIL, email);
        emailAccount.setOwner(Collections.singletonList(user.getUserId()));
        emailAccount.setTenantId(user.getTenantId());
        emailAccount.setDescribeApiName(EMAIL_ACCOUNT_API_NAME);
        emailAccount.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        emailAccount.setPackage("CRM");
        emailAccount.setDeleted(Boolean.FALSE);
        emailAccount.set("life_status", ObjectLifeStatus.NORMAL.getCode());
        emailAccount.set("lock_status", ObjectLockStatus.UNLOCK.getStatus());
        return emailAccount;
    }

    private static ArrayList<IObjectData> distinct(List<IObjectData> dataList) {
        LinkedHashMap<String, IObjectData> map = dataList.stream().collect(Collectors.toMap(DBRecord::getId, Function.identity(), (v1, v2) -> v1, LinkedHashMap::new));
        return new ArrayList<>(map.values());
    }

    private static QueryResult<ObjectDataDocument> convert(QueryResult<IObjectData> queryResult) {
        QueryResult<ObjectDataDocument> result = new QueryResult<>();
        result.setTotalNumber(queryResult.getTotalNumber());
        result.setData(convert(queryResult.getData()));
        return result;
    }

    private static List<ObjectDataDocument> convert(List<IObjectData> data) {
        return data.stream().map(ObjectDataDocument::of).collect(Collectors.toCollection(() -> new ArrayList<>(data.size())));
    }

    private static Map<String, String> convert(ServiceContext context, User user) {
        Map<String, String> headers = new HashMap<>();
        headers.put("x-user-id", user.getUserId());
        headers.put("x-tenant-id", user.getTenantId());
        headers.put("x-fs-userInfo", user.getUserId());
        headers.put("x-fs-ei", user.getTenantId());
        headers.put("x-fs-locale", Optional.ofNullable(context.getLang()).map(Lang::getValue).orElse(Lang.defaultLang().getValue()));
        return headers;
    }

    @Override
    public void afterPropertiesSet() {
        ConfigFactory.getConfig("fs-crm-sales-config", config -> {
            String suffix = config.get("personal_email_domain", "");
            if (suffix != null && !suffix.isEmpty()) {
                personalEmailDomain.clear();
                personalEmailDomain.addAll(Arrays.asList(suffix.split(";")));
            }
        });
    }
}
