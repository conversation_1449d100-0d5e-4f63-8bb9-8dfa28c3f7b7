package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.platform.utils.RequestSourceResolver;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.enums.AgreementStatus;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.ListButtonManager;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel.AGREEMENT_STATUS;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-06-30
 * ============================================================
 */
public class PartnerAgreementDetailRelatedListController extends StandardRelatedListController {
    private final ChannelService channelService = SpringUtil.getContext().getBean("channelServiceProvider", ChannelService.class);
    private final ListButtonManager listButtonManager = SpringUtil.getContext().getBean("listButtonManager", ListButtonManager.class);
    protected final RequestSourceResolver requestSourceResolver = SpringUtil.getContext().getBean("requestSourceResolver", RequestSourceResolver.class);


    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        initiateRenewalButtonHandle(after);
        renewButtonHandle(after);
        return after;
    }

    /**
     * 「去续约」按钮处理
     *
     * @param after
     */
    private void renewButtonHandle(Result after) {
        if (RequestUtil.isMobileOrH5Request() || !requestSourceResolver.isErAppRequest()) {
            // 不支持上游请求或者移动端请求
            listButtonManager.removeListButton(after, Sets.newHashSet(ObjectAction.RENEW.getButtonApiName()));
        } else {
            // 下游应用请求
            listButtonManager.removeListButtonIf(after,
                    data -> !DataUtils.getValue(data, PartnerAgreementDetailModel.ABLE_RENEWAL, Boolean.class, Boolean.FALSE) || RequestUtil.isMobileOrH5Request()
                    , Sets.newHashSet(ObjectAction.RENEW.getButtonApiName()));
        }
    }

    /**
     * 「发起签约」按钮处理
     *
     * @param after
     */
    private void initiateRenewalButtonHandle(Result after) {
        if (RequestUtil.isMobileOrH5Request() || requestSourceResolver.isErAppRequest()) {
            listButtonManager.removeListButton(after, Sets.newHashSet(ObjectAction.INITIATE_RENEWAL.getButtonApiName()));
        } else {
            // 上游 Web 请求
            if (CollectionUtils.isNotEmpty(after.getDataList())) {
                removeInitiateRenewalButton(after);
            }
        }
    }

    private void removeInitiateRenewalButton(Result after) {
        Set<String> admissionDataIds = after.getDataList().stream().map(d -> getAdmissionDataId(d.toObjectData())).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        String admissionObject = channelService.fetchChannelAdmissionObject(controllerContext.getUser());
        Set<String> allowInitiateRenewalDataIds = channelService.allowInitiateRenewal(controllerContext.getUser(), admissionDataIds, admissionObject);
        listButtonManager.removeListButtonIf(after, (data) -> {
            boolean nonActiveStatus = AgreementStatus.find(DataUtils.getValue(data, AGREEMENT_STATUS, String.class, null)) != AgreementStatus.ACTIVE;
            if (nonActiveStatus) {
                return true;
            }
            String admissionDataId = getAdmissionDataId(data);
            return StringUtils.isBlank(admissionDataId) || !allowInitiateRenewalDataIds.contains(admissionDataId);
        }, Sets.newHashSet(ObjectAction.INITIATE_RENEWAL.getButtonApiName()));
    }

    private String getAdmissionDataId(IObjectData objectData) {
        return channelService.fetchAdmissionDataId(controllerContext.getUser(), objectData);
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery query = super.customSearchTemplate(searchQuery);
        if (requestSourceResolver.isErAppRequest()) {
            SearchUtil.fillFilterEq(query.getFilters(), "sign_status", SignStatus.PENDING_RENEWAL.getStatus());
        }
        return query;
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        if (requestSourceResolver.isErAppRequest()) {
            SearchUtil.fillFilterEq(query.getFilters(), "sign_status", SignStatus.PENDING_RENEWAL.getStatus());
        }
        return query;
    }

}
