package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.model.QuoterModel;
import com.facishare.crm.sfa.predefine.service.AdvancedFormulaService;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;

public class AdvancedFormulaRelatedListController extends StandardRelatedListController {
    private final AdvancedFormulaService advancedFormulaService = SpringUtil.getContext().getBean(AdvancedFormulaService.class);

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = super.findData(query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return queryResult;
        }
        List<IObjectData> dataList = queryResult.getData();
        Set<String> apiNames = new HashSet<>();
        for (IObjectData iObjectData : dataList) {
            String apiName = iObjectData.get(QuoterModel.AdvancedFormulaModel.OBJECT_NAME, String.class);
            if (StringUtils.isNotBlank(apiName)) {
                apiNames.add(apiName);
            }
        }
        if (CollectionUtils.isEmpty(apiNames)) {
            return queryResult;
        }
        Map<String, IObjectData> productMap = Maps.newHashMap();
        Map<String, IObjectDescribe> descMap = serviceFacade.findObjects(controllerContext.getTenantId(), apiNames);
        Pattern pattern = Pattern.compile("\\$(.*?)\\$");
        dataList.forEach(data -> {
            String apiName = data.get(QuoterModel.AdvancedFormulaModel.OBJECT_NAME, String.class, "");
            String fieldName = data.get(QuoterModel.AdvancedFormulaModel.FIELD_NAME, String.class, "");
            IObjectDescribe describe = descMap.get(apiName);
            if (Objects.nonNull(describe)) {
                data.set(QuoterModel.AdvancedFormulaModel.OBJECT_NAME, apiName + "(" + describe.getDisplayName() + ")");
                Optional<String> fieldOpt = Optional.of(describe).map(x -> x.getFieldDescribe(fieldName)).map(IFieldDescribe::getLabel);
                fieldOpt.ifPresent(s -> data.set(QuoterModel.AdvancedFormulaModel.FIELD_NAME, fieldName + "(" + s + ")"));
                String expression = advancedFormulaService.translateFormula(describe, pattern, productMap, data, controllerContext.getTenantId());
                data.set(QuoterModel.AdvancedFormulaModel.FORMULA, StringUtils.replace(expression, "$", ""));
            }
        });
        return queryResult;
    }
}
