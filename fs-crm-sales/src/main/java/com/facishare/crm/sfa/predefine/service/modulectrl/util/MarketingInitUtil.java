package com.facishare.crm.sfa.predefine.service.modulectrl.util;


import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.describebuilder.*;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IFieldService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringContextUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.BooleanSupplier;
import java.util.function.UnaryOperator;

/**
 * 营销类初始化工具类
 * 目前营销类，包括三种类型
 * 价格政策
 * 返利
 * 优惠券
 *
 * <AUTHOR>
 * @date 2022/01/11
 */
@Slf4j
public class MarketingInitUtil {
    private static final EnterpriseInitService enterpriseInitService = SpringContextUtil.getContext().getBean(EnterpriseInitService.class);
    private static final ServiceFacade serviceFacade = SpringContextUtil.getContext().getBean(ServiceFacade.class);
    private static final IObjectDescribeService objectDescribeService = SpringContextUtil.getContext().getBean(IObjectDescribeService.class);
    private static final IFieldService fieldService = SpringContextUtil.getContext().getBean(IFieldService.class);
    private static final ConfigService configService = SpringContextUtil.getContext().getBean(ConfigService.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringContextUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    /**
     * 添加或修改货币领域
     *
     * @param fieldInfos        字段信息
     * @param toAddFieldList    添加字段列表
     * @param toUpdateFieldList 更新字段列表
     * @param fieldLayouts      现场布局
     * @param objectDescribe    对象描述
     * @param modifyFunction    修改function
     */
    public static void addOrModifyCurrencyFields(String[] fieldInfos,
                                                 List<IFieldDescribe> toAddFieldList,
                                                 List<IFieldDescribe> toUpdateFieldList,
                                                 List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayouts,
                                                 IObjectDescribe objectDescribe, UnaryOperator<String> modifyFunction) {
        //如果已经有值了，则重新生成函数
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldInfos[0]);
        if (null != fieldDescribe) {
            calFunction(modifyFunction, fieldDescribe);
            toUpdateFieldList.add(fieldDescribe);
            //没值则是新生成的，需要新建
        } else {
            setCurrencyFieldAndLayout(fieldInfos, toAddFieldList, fieldLayouts);
        }
    }

    /**
     * 添加或修改货币领域
     *
     * @param fieldInfos        字段信息
     * @param toAddFieldList    添加字段列表
     * @param toUpdateFieldList 更新字段列表
     * @param fieldLayouts      现场布局
     * @param objectDescribe    对象描述
     * @param modifyFunction    修改function
     */
    public static void addOrModifyFormulaFields(String[] fieldInfos,
                                                List<IFieldDescribe> toAddFieldList,
                                                List<IFieldDescribe> toUpdateFieldList,
                                                List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayouts,
                                                IObjectDescribe objectDescribe, UnaryOperator<String> modifyFunction) {
        //如果已经有值了，则重新生成函数
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldInfos[0]);
        if (null != fieldDescribe) {
            calFunctionExpression(modifyFunction, fieldDescribe);
            toUpdateFieldList.add(fieldDescribe);
            //没值则是新生成的，需要新建
        } else {
            //生成描述
            IFieldDescribe formulaFieldDescribe = getFormulaField(fieldInfos[0], fieldInfos[1], fieldInfos[2]);
            //生成布局
            FieldLayoutPojo fieldLayout = enterpriseInitService.getFieldLayoutPojo(SystemConstants.RenderType.Formula.renderType, true, false);
            toAddFieldList.add(formulaFieldDescribe);
            fieldLayouts.add(Tuple.of(formulaFieldDescribe, fieldLayout));
        }
    }

    /**
     * 修改货币领域
     *
     * @param fieldInfos        字段信息
     * @param toUpdateFieldList 更新字段列表
     * @param objectDescribe    对象描述
     * @param modifyFunction    修改函数
     * @param isChanged         已经发生了改变，则会在原公式基础上增加，否则使用第一次开启的公式
     */
    public static void modifyCurrencyFields(boolean isChanged, String[] fieldInfos, List<IFieldDescribe> toUpdateFieldList,
                                            IObjectDescribe objectDescribe, UnaryOperator<String> modifyFunction) {
        //如果已经有值了，则重新生成函数
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldInfos[0]);
        if (null != fieldDescribe) {
            if (!isChanged) {
                fieldDescribe.setDefaultIsExpression(true);
                fieldDescribe.setDefaultValue(fieldInfos[2]);
            } else {
                calFunction(modifyFunction, fieldDescribe);
            }
            toUpdateFieldList.add(fieldDescribe);
        }
    }

    /**
     * 修改公式字段
     *
     * @param fieldInfos        字段信息
     * @param toUpdateFieldList 更新字段列表
     * @param objectDescribe    对象描述
     * @param modifyFunction    修改函数
     */
    public static void modifyFormulaFields(String[] fieldInfos, List<IFieldDescribe> toUpdateFieldList,
                                           IObjectDescribe objectDescribe, UnaryOperator<String> modifyFunction) {
        //如果已经有值了，则重新生成函数
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldInfos[0]);
        if (null != fieldDescribe) {
            Optional.ofNullable(fieldDescribe.getExpression())
                    .ifPresent(expression -> {
                        fieldDescribe.setExpression(modifyFunction.apply(expression));
                    });
            toUpdateFieldList.add(fieldDescribe);
        }
    }


    public static void doUpdateDescribeAndLayout(User user, IObjectDescribe iObjectDescribe, List<IFieldDescribe> toAddFieldList, List<IFieldDescribe> toUpdateFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList) {
        //增加新增描述
        addFieldDescribe(iObjectDescribe, toAddFieldList);
        //更新字段描述
        updateFieldDescribe(iObjectDescribe, toUpdateFieldList);
        //新加layout
        insertFieldsLayout(user, iObjectDescribe, fieldLayoutTupleList);
    }

    public static void updateFieldDescribe(IObjectDescribe iObjectDescribe, List<IFieldDescribe> toUpdateFieldList) {
        if (CollectionUtils.notEmpty(toUpdateFieldList)) {
            try {
                objectDescribeService.updateFieldDescribe(iObjectDescribe, toUpdateFieldList, new ActionContext());
            } catch (MetadataServiceException e) {
                log.error("updateFieldDescribe error,tenantId {} ", iObjectDescribe.getTenantId(), e);
            }
        }
    }

    public static void addFieldDescribe(IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribeList) {
        //为空直接返回
        if (CollectionUtils.empty(fieldDescribeList)) {
            return;
        }
        try {
            objectDescribeService.addCustomFieldDescribe(objectDescribe, fieldDescribeList);
        } catch (MetadataServiceException e) {
            log.error("addFieldDescribe error,tenantId {} ", objectDescribe.getTenantId(), e);
        }
    }

    private static void calFunction(UnaryOperator<String> modifyFunction, IFieldDescribe fieldDescribe) {
        Optional.ofNullable(fieldDescribe.getDefaultValue())
                .map(Object::toString)
                .ifPresent(defaultValue -> {
                    fieldDescribe.setDefaultIsExpression(true);
                    fieldDescribe.setDefaultValue(modifyFunction.apply(defaultValue));
                });
    }

    private static void calFunctionExpression(UnaryOperator<String> modifyFunction, IFieldDescribe fieldDescribe) {
        Optional.ofNullable(fieldDescribe.getExpression())
                .map(Object::toString)
                .ifPresent(defaultValue -> {
                    fieldDescribe.setDefaultIsExpression(true);
                    fieldDescribe.setExpression(modifyFunction.apply(defaultValue));
                });
    }


    public static void addCurrencyFieldOnlyOnce(ImmutableList<String[]> fieldInfos, List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayouts,
                                                IObjectDescribe objectDescribe) {
        for (String[] fieldInfo : fieldInfos) {
            //如果已经添加，则不再添加
            if (null != objectDescribe.getFieldDescribe(fieldInfo[0])) {
                continue;
            }
            setCurrencyFieldAndLayout(fieldInfo, toAddFieldList, fieldLayouts);
        }

    }


    public static void addCurrencyFieldAndLayout(ImmutableList<String[]> fieldInfos, List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayouts) {
        for (String[] fieldInfo : fieldInfos) {
            setCurrencyFieldAndLayout(fieldInfo, toAddFieldList, fieldLayouts);
        }
    }

    public static void setCurrencyFieldAndLayout(String[] infos, List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayouts) {
        setCurrencyFieldAndLayout(infos[0], infos[1], infos[2], toAddFieldList, fieldLayouts);
    }

    public static void setCurrencyFieldAndLayout(String apiName, String label, String defaultValue, List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayouts) {
        //生成描述
        IFieldDescribe fieldDescribe = getCurrencyField(apiName, label, defaultValue);
        //生成布局
        FieldLayoutPojo fieldLayout = getCurrencyLayout();
        toAddFieldList.add(fieldDescribe);
        fieldLayouts.add(Tuple.of(fieldDescribe, fieldLayout));
    }

    /**
     * 得到货币布局
     *
     * @return {@code FieldLayoutPojo}
     */
    public static FieldLayoutPojo getCurrencyLayout() {
        return enterpriseInitService.getFieldLayoutPojo(SystemConstants.RenderType.Currency.renderType, true, false);
    }

    /**
     * 货币类型
     *
     * @param apiName      api名称
     * @param label        标签
     * @param defaultValue 默认值
     * @return {@code IFieldDescribe}
     */
    public static IFieldDescribe getCurrencyField(String apiName, String label, String defaultValue) {
        IFieldDescribe currencyFieldDescribe = CurrencyFieldDescribeBuilder.builder()
                .apiName(apiName)
                .label(label)
                .maxLength(14)
                .length(12)
                .decimalPlaces(2)
                .currencyUnit("￥")
                .roundMode(4)
                .required(false)
                .build();
        if (null != defaultValue) {
            currencyFieldDescribe.setDefaultIsExpression(true);
            currencyFieldDescribe.setDefaultValue(defaultValue);
        }
        return currencyFieldDescribe;
    }

    /**
     * 货币类型
     *
     * @param apiName      api名称
     * @param label        标签
     * @param defaultValue 默认值
     * @return {@code IFieldDescribe}
     */
    public static IFieldDescribe getFormulaField(String apiName, String label, String defaultValue) {
        FormulaFieldDescribe formulaFieldDescribe = FormulaFieldDescribeBuilder.builder()
                .apiName(apiName)
                .label(label)
                .decimalPlaces(2)
                .returnType("currency")
                .decimalPlaces(2)
                .defaultToZero(true)
                .expression(defaultValue)
                .required(false)
                .build();
        formulaFieldDescribe.set("max_length", 14);
        formulaFieldDescribe.set("length", 14);
        formulaFieldDescribe.set("currency_unit", "￥");
        formulaFieldDescribe.set("round_mode", "4");
        return formulaFieldDescribe;
    }


    public static IFieldDescribe getLongTextField(String apiName, String label) {
        LongTextFieldDescribe longTextFieldDescribe = LongTextFieldDescribeBuilder.builder()
                .apiName(apiName)
                .label(label)
                .maxLength(10000)
                .required(false)
                .build();
        longTextFieldDescribe.setExpressionType("json");
        return longTextFieldDescribe;
    }

    /**
     * 添加长文本字段只有一次
     *
     * @param onlyOnceLongTextFieldList 只有一次长文本字段列表
     * @param toAddFieldList            添加字段列表
     * @param iObjectDescribe           我对象描述
     */
    public static void addLongTextFieldOnlyOnce(ImmutableList<String[]> onlyOnceLongTextFieldList, List<IFieldDescribe> toAddFieldList, IObjectDescribe iObjectDescribe) {
        for (String[] fieldInfo : onlyOnceLongTextFieldList) {
            //如果已经添加，则不再添加
            if (null != iObjectDescribe.getFieldDescribe(fieldInfo[0])) {
                continue;
            }
            IFieldDescribe longTextField = getLongTextField(fieldInfo[0], fieldInfo[1]);
            toAddFieldList.add(longTextField);
        }
    }

    public static void insertFieldsLayout(User user, IObjectDescribe objectDescribe, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList) {
        //为空直接返回
        if (CollectionUtils.empty(fieldLayoutTupleList)) {
            return;
        }
        List<ILayout> layoutList = serviceFacade.getLayoutLogicService().getDetailLayouts(user.getTenantId(), objectDescribe);
        layoutList.forEach(m -> {
            fieldLayoutTupleList.forEach(tuple -> LayoutExt.of(m).addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.getLayoutLogicService().updateLayout(user, m);
        });
    }

    /**
     * 在公式中添加优惠券优惠额
     * ($price_book_price$*$quantity$+$dynamic_amount$+$coupon_dynamic_amount$)/$quantity$
     *
     * @param defaultValue 默认值
     * @return {@code String}
     */
    @NotNull
    public static String addMacro(String defaultValue, String macro) {
        //如果已经包含了优惠券优惠额，则不处理
        if (defaultValue.contains("+" + macro)) {
            return defaultValue;
        }
        String[] split = defaultValue.split("/");
        if (ArrayUtils.isEmpty(split) || split.length < 2) {
            log.error("add macro {} ,length less than 2 after split ", macro);
            return defaultValue;
        }
        StringBuilder newFormula = new StringBuilder(split[0]);
        //删掉最后一个括号
        newFormula.deleteCharAt(newFormula.length() - 1);
        //增加
        newFormula.append("+").append(macro).append(")").append("/").append(split[1]);
        return newFormula.toString();
    }

    /**
     * 添加价目表合计
     *
     * @param toAddFieldList 添加字段列表
     * @param fieldLayouts   现场布局
     * @param objectDescribe 对象描述
     */
    public static void addPriceBookAmount(List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayouts, IObjectDescribe objectDescribe) {
        //如果已经添加，则不再添加
        if (Optional.ofNullable(objectDescribe.getFieldDescribe("price_book_amount")).isPresent()) {
            return;
        }
        IFieldDescribe priceBookAmountField = CountFieldDescribeBuilder.builder()
                .apiName("price_book_amount")
                .label("价目表合计")
                .returnType("currency")
                .countType("sum")
                .subObjectDescribeApiName(SFAPreDefineObject.SalesOrderProduct.getApiName())
                .countFieldApiName("price_book_subtotal")
                .countFieldType("currency")
                .wheres(getPriceBookAmountWheres())
                .decimalPlaces(2)
                .required(false)
                .build();
        priceBookAmountField.setIndex(true);
        toAddFieldList.add(priceBookAmountField);
        FieldLayoutPojo priceBookAmountLayout = enterpriseInitService.getFieldLayoutPojo(SystemConstants.RenderType.Count.renderType, true, false);
        fieldLayouts.add(Tuple.of(priceBookAmountField, priceBookAmountLayout));
    }

    /**
     * 添加价目表合计
     *
     * @param toAddFieldList    添加字段列表
     * @param fieldLayouts      现场布局
     * @param objectDescribe    对象描述
     * @param toUpdateFieldList 更新字段列表
     */
    public static void addOrUpdateRebatePriceBookAmount(List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayouts, IObjectDescribe objectDescribe, List<IFieldDescribe> toUpdateFieldList) {
        //如果已经添加，更新where条件字段
        if (Optional.ofNullable(objectDescribe.getFieldDescribe("price_book_amount")).isPresent()) {
            CountFieldDescribe priceBookAmountFieldDescribe = (CountFieldDescribe) objectDescribe.getFieldDescribe("price_book_amount");
            priceBookAmountFieldDescribe.setWheres(getRebatePriceBookAmountWheres());
            toUpdateFieldList.add(priceBookAmountFieldDescribe);
            return;
        }
        IFieldDescribe priceBookAmountField = CountFieldDescribeBuilder.builder()
                .apiName("price_book_amount")
                .label("价目表合计")
                .returnType("currency")
                .countType("sum")
                .subObjectDescribeApiName(SFAPreDefineObject.SalesOrderProduct.getApiName())
                .countFieldApiName("price_book_subtotal")
                .countFieldType("currency")
                .wheres(getRebatePriceBookAmountWheres())
                .decimalPlaces(2)
                .required(false)
                .build();
        priceBookAmountField.setIndex(true);
        toAddFieldList.add(priceBookAmountField);
        FieldLayoutPojo priceBookAmountLayout = enterpriseInitService.getFieldLayoutPojo(SystemConstants.RenderType.Count.renderType, true, false);
        fieldLayouts.add(Tuple.of(priceBookAmountField, priceBookAmountLayout));
    }

    public static void addPolicyAmountTotal(List<IFieldDescribe> toAddFieldList, IObjectDescribe objectDescribe, BooleanSupplier needAddPolicyField) {
        //如果已经添加，则不再添加
        if (Optional.ofNullable(objectDescribe.getFieldDescribe("policy_amount_total")).isPresent()) {
            return;
        }
        if (!needAddPolicyField.getAsBoolean()) {
            return;
        }
        IFieldDescribe policyAmountTotalField = CountFieldDescribeBuilder.builder()
                .apiName("policy_amount_total")
                .label("订单产品促销优惠额合计")
                .returnType("currency")
                .countType("sum")
                .subObjectDescribeApiName(SFAPreDefineObject.SalesOrderProduct.getApiName())
                .wheres(getRebatePriceBookAmountWheres())
                .fieldApiName("order_id")
                .countFieldApiName("policy_dynamic_amount")
                .countFieldType("currency")
                .decimalPlaces(2)
                .required(false)
                .build();
        policyAmountTotalField.setIndex(true);
        toAddFieldList.add(policyAmountTotalField);
        //价目表合计 + 订单产品促销优惠额合计 + 促销优惠额
        IFieldDescribe currencyField = getCurrencyField("policy_after_total", "促销后合计", "$price_book_amount$+$policy_amount_total$+$policy_dynamic_amount$");// ignoreI18n
        toAddFieldList.add(currencyField);
    }


    /**
     * 创建或更新赠品
     *
     * @param objectDescribe    对象描述
     * @param toAddFieldList    添加字段列表
     * @param toEnableFieldList 启用字段列表
     */
    public static void createOrUpdateGiveaway(IObjectDescribe objectDescribe, List<IFieldDescribe> toAddFieldList, List<IFieldDescribe> toEnableFieldList) {
        IFieldDescribe existFieldDescribe = objectDescribe.getFieldDescribe("is_giveaway");
        if (null == existFieldDescribe) {
            List<ISelectOption> optionList = Lists.newArrayListWithExpectedSize(4);
            ISelectOption optionTrue = new SelectOption();
            optionTrue.setValue("1");
            optionTrue.setLabel("是");// ignoreI18n
            optionList.add(optionTrue);
            ISelectOption optionFalse = new SelectOption();
            optionFalse.setValue("0");
            optionFalse.setLabel("否");// ignoreI18n
            optionList.add(optionFalse);
            IFieldDescribe isGiveawayFieldDescribe = SelectOneFieldDescribeBuilder.builder()
                    .apiName("is_giveaway")
                    .label("是否赠品")// ignoreI18n
                    .required(false)
                    .selectOptions(optionList)
                    .build();
            toAddFieldList.add(isGiveawayFieldDescribe);
        } else {
            if (!Boolean.TRUE.equals(existFieldDescribe.isActive())) {
                toEnableFieldList.add(existFieldDescribe);
            }
        }
    }

    public static void rebateCreateOrUpdateGiveaway(IObjectDescribe objectDescribe, List<IFieldDescribe> toAddFieldList, List<IFieldDescribe> toEnableFieldList, List<IFieldDescribe> toUpdateFieldList) {
        IFieldDescribe existFieldDescribe = objectDescribe.getFieldDescribe("is_giveaway");
        if (null == existFieldDescribe) {
            buildNewGiveaway(toAddFieldList);
        } else {
            //existFieldDescribe 是否是 selectOneFieldDescribe类型
            if (existFieldDescribe instanceof SelectOneFieldDescribe) {
                SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) existFieldDescribe;
                List<ISelectOption> optionList = selectOneFieldDescribe.getSelectOptions();
                if (optionList != null) {
                    //optionList 是否包含返利品
                    boolean hasRebate = optionList.stream().anyMatch(option -> "2".equals(option.getValue()));
                    if (!hasRebate) {
                        ISelectOption optionRebate = new SelectOption();
                        optionRebate.setValue("2");
                        optionRebate.setLabel("返");// ignoreI18n
                        optionList.add(optionRebate);
                        selectOneFieldDescribe.setSelectOptions(optionList);
                        toUpdateFieldList.add(existFieldDescribe);
                    }
                }
            }
            if (!Boolean.TRUE.equals(existFieldDescribe.isActive())) {
                toEnableFieldList.add(existFieldDescribe);
            }
        }
    }

    private static void buildNewGiveaway(List<IFieldDescribe> toAddFieldList) {
        List<ISelectOption> optionList = Lists.newArrayListWithExpectedSize(4);
        ISelectOption optionTrue = new SelectOption();
        optionTrue.setValue("1");
        optionTrue.setLabel("是");// ignoreI18n
        optionList.add(optionTrue);
        ISelectOption optionFalse = new SelectOption();
        optionFalse.setValue("0");
        optionFalse.setLabel("否");// ignoreI18n
        optionList.add(optionFalse);
        ISelectOption optionRebate = new SelectOption();
        optionRebate.setValue("2");
        optionRebate.setLabel("返");// ignoreI18n
        optionList.add(optionRebate);
        IFieldDescribe isGiveawayFieldDescribe = SelectOneFieldDescribeBuilder.builder()
                .apiName("is_giveaway")
                .label("是否赠品")// ignoreI18n
                .required(false)
                .selectOptions(optionList)
                .build();
        toAddFieldList.add(isGiveawayFieldDescribe);
    }


    public static List<LinkedHashMap> getPriceBookAmountWheres() {
        LinkedHashMap<String, Object> whereMap = new LinkedHashMap<>();
        whereMap.put("connector", Where.CONN.OR.toString());
        LinkedHashMap<String, Object> filters = new LinkedHashMap<>();
        filters.put("value_type", 0);
        filters.put("operator", Operator.N.toString());
        filters.put("field_name", "is_giveaway");
        filters.put("field_values", Lists.newArrayList("1"));
        whereMap.put("filters", Lists.newArrayList(filters));
        return Lists.newArrayList(whereMap);
    }


    private static List<LinkedHashMap> getRebatePriceBookAmountWheres() {
        LinkedHashMap<String, Object> whereMap = new LinkedHashMap<>();
        whereMap.put("connector", Where.CONN.OR.toString());
        LinkedHashMap<String, Object> filters = new LinkedHashMap<>();
        filters.put("value_type", 0);
        filters.put("operator", Operator.NHASANYOF.toString());
        filters.put("field_name", "is_giveaway");
        filters.put("field_values", Lists.newArrayList("1", "2"));
        whereMap.put("filters", Lists.newArrayList(filters));
        return Lists.newArrayList(whereMap);
    }

    public static ConfigCtrlModule.Result getFailResult(String i18nKey) {
        return ConfigCtrlModule.Result.builder()
                .errCode(ConfigCtrlModule.ResultInfo.Fail.getErrCode())
                .errMessage(I18N.text(i18nKey))
                .value(
                        ConfigCtrlModule.Value.builder()
                                .openStatus(ConfigCtrlModule.OpenStatus.FAIL.toString()).build()
                ).build();
    }

    public static ConfigCtrlModule.Result getOkResult() {
        return ConfigCtrlModule.Result.builder()
                .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                .value(ConfigCtrlModule.Value.builder().openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                ).build();
    }

    public static void enableField(IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribeList) {
        //为空直接返回
        if (CollectionUtils.empty(fieldDescribeList)) {
            return;
        }
        try {
            fieldService.enableField(objectDescribe, fieldDescribeList);
        } catch (MetadataServiceException e) {
            log.error("enableField error,tenantId {} ", objectDescribe.getTenantId(), e);
        }
    }

    /**
     *总方案是：不开价格政策时，开临时赠品就把【促销优惠额】字段刷上
     *
     * 如果先启返利优惠券，则开临时赠品时，把【促销优惠额】字段刷上
     * 如果先开临时赠品，后启返利优惠券，把【促销优惠额】字段刷上；后续如果再开价格政策，不再重复刷【促销优惠额】字段
     * https://www.tapd.cn/21309261/bugtrace/bugs/view/1121309261001351572
     * @param toAddFieldList 新加字段
     * @param fieldLayouts 布局
     * @param objectDescribe 描述
     */
    public static void addPolicyDynamicAmount(List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayouts, IObjectDescribe objectDescribe) {
        IFieldDescribe existFieldDescribe = objectDescribe.getFieldDescribe("policy_dynamic_amount");
        if (null == existFieldDescribe) {
            IFieldDescribe policyDynamicAmountField = MarketingInitUtil.getCurrencyField("policy_dynamic_amount", "促销优惠额", "0");// ignoreI18n
            toAddFieldList.add(policyDynamicAmountField);
            FieldLayoutPojo policyDynamicAmountLayout = enterpriseInitService.getFieldLayoutPojo(SystemConstants.RenderType.Currency.renderType, true, false);
            fieldLayouts.add(Tuple.of(policyDynamicAmountField, policyDynamicAmountLayout));
        }
    }

    public static void closeDynamicAmortize(User user) {
        if (bizConfigThreadLocalCacheService.allowDynamicAmortize(user.getTenantId())) {
            configService.updateTenantConfig(user, ConfigType.DYNAMIC_ALLOW_AMORTIZE.getKey(), "0", ConfigValueType.STRING);
        }
    }

    public static void deleteDynamicAmortizeField(User user) {
        if (bizConfigThreadLocalCacheService.allowDynamicAmortize(user.getTenantId())) {
            List<String> deleteFieldApiNameList = Lists.newArrayList("dynamic_amortize_amount", "amortize_price", "amortize_subtotal");
            IObjectDescribe salesOrderProductObjDescribe = serviceFacade.findObject(user.getTenantId(), SFAPreDefineObject.SalesOrderProduct.getApiName());
            List<IFieldDescribe> deleteFieldDescribeList = Lists.newArrayList();
            for (String deleteApiName : deleteFieldApiNameList) {
                IFieldDescribe fieldDescribe = salesOrderProductObjDescribe.getFieldDescribe(deleteApiName);
                if (fieldDescribe != null) {
                    deleteFieldDescribeList.add(fieldDescribe);
                }
            }
            if (!deleteFieldDescribeList.isEmpty()) {
                try {
                    objectDescribeService.deleteCustomFieldDescribe(salesOrderProductObjDescribe, deleteFieldDescribeList);
                } catch (MetadataServiceException e) {
                    log.error("delete field error msg is {},tenantId is {}", e.getMessage(), user.getTenantId());
                }
            }
        }
    }

    public static void setPeriodicQuantityRule(boolean openPeriodic, List<IFieldDescribe> fieldList) {
        if (!openPeriodic || CollectionUtils.empty(fieldList)) {
            return;
        }
        for (IFieldDescribe describe : fieldList) {
            String defaultValue = describe.getDefaultValue() == null ? "" : describe.getDefaultValue().toString();
            if (StringUtils.isNotBlank(defaultValue) && defaultValue.contains("$quantity$") && !defaultValue.contains("$pricing_period$")) {
                defaultValue = StringUtils.replace(defaultValue, "$quantity$", "($quantity$*$pricing_period$)");
                describe.setDefaultValue(defaultValue);
            }
        }
    }

}
