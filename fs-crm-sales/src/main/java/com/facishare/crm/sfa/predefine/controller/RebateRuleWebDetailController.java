package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.rebatecoupon.translate.RebateRuleServiceImpl;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.Optional;

public class RebateRuleWebDetailController extends SFAWebDetailController {
    private static final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    private final RebateRuleServiceImpl saveRebateRuleService = SpringUtil.getContext().getBean(RebateRuleServiceImpl.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        Optional.ofNullable(result).map(Result::getLayout)
                .ifPresent(x -> {
                    ILayout layout = new Layout(x);
                    WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.PRINT.getActionCode()));
                });
        CouponUtils.issueConditionTab(result);
        render(result);
        return result;
    }


    private void render(Result result) {
        Optional.ofNullable(result)
                .map(Result::getData)
                .map(ObjectDataDocument::toObjectData)
                .ifPresent(data ->
                        saveRebateRuleService.render(data, controllerContext.getUser())
                );
    }


    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (RequestUtil.isMobileRequest() ||RequestUtil.isMobileDeviceRequest()|| RequestUtil.isH5Request()) {
            availableRangeUtils.removeMobileButton(layout);
        }
        return layout;
    }


}
