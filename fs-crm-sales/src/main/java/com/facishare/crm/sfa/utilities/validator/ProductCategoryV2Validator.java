package com.facishare.crm.sfa.utilities.validator;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.platform.async.executor.AsyncBootstrap;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.DescribeServiceExt;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject;
import com.facishare.crm.sfa.predefine.service.objectbo.ProductCategoryBO;
import com.facishare.crm.sfa.predefine.service.treepath.impl.TreePathService;
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer;
import com.facishare.crm.sfa.prm.platform.utils.I18NUtils;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.ConfigConstant;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.*;
import static com.facishare.crm.sfa.utilities.util.ProductCategoryUtils.CATEGORY_LIMIT;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.*;

/**
 * <AUTHOR>
 * @date 2021/11/30 15:29
 */
@Slf4j
@Service
public class ProductCategoryV2Validator {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private TreePathService treePathService;
    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    public static final String OBJECT_DISPLAY_NAME_FORMAT = "sfa.%s.object.display.name";
    @Resource
    private DescribeServiceExt describeServiceExt;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Resource
    private AsyncBootstrap asyncBootstrap;
    @Resource
    private DescribeEnhancer describeEnhancer;
    @Resource
    private ProductCategoryUtils productCategoryUtils;

    /**
     * 分类同级不同名
     *
     * @param categoryName 分类名称
     * @param pid          上级分类id
     * @param excludeId    排除的数据id，编辑时传入
     */
    public void checkNameUniqueInSameLevel(User user, String categoryName, String pid, String excludeId) {
        if (StringUtils.isBlank(categoryName)) {
            throw new ValidateException(I18N.text(SO_PRODUCT_CATEGORY_NAME_NOT_EMPTY));
        }
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterEq(filters, ProductCategoryModel.Filed.CATEGORY_NAME, categoryName);
        if (StringUtils.isBlank(pid)) {
            SearchUtil.fillFilterIsNull(filters, ProductCategoryModel.Filed.PID);
        } else {
            SearchUtil.fillFilterEq(filters, ProductCategoryModel.Filed.PID, pid);
        }
        if (StringUtils.isNotBlank(excludeId)) {
            SearchUtil.fillFilterNotEq(filters, DBRecord.ID, excludeId);
        }
        searchQuery.setLimit(1);
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, ProductCategoryModel.Metadata.API_NAME, searchQuery).getData();
        if (CollectionUtils.isNotEmpty(dataList)) {
            throw new ValidateException(I18N.text(SO_CHECK_CATEGORY_SAME_NAME));
        }
    }

    /**
     * 检查分类深度是否超出限制 DEPTH_LIMIT
     *
     * @param user 用户
     * @param pid  上级产品分类id
     */
    public void checkCategoryDepth(User user, String pid) {
        if (StringUtils.isBlank(pid)) {
            return;
        }
        int depth = treePathService.currentTreeDepth(user, pid, ProductCategoryModel.Metadata.API_NAME, ProductCategoryModel.Filed.PRODUCT_CATEGORY_PATH);
        if (depth + 1 >= ConfigConstant.getLevelDepthLimit()) {
            throw new ValidateException(String.format(I18N.text(SO_CHECK_LEVEL_LIMIT), ConfigConstant.getLevelDepthLimit()));
        }
    }

    public void checkCategoryTotalLimit(User user) {
        long categoryLimit = productCategoryUtils.getTenantCategoryLimit(user);
        checkCategoryTotalLimit(user, categoryLimit);
    }

    public void checkCategoryTotalLimit(User user, long limitCount) {
        Long countNumber = metaDataFindServiceExt.getObjectCountNumber(user, ProductCategoryModel.Metadata.API_NAME);
        if (countNumber == null) {
            return;
        }
        if (countNumber + 1L > limitCount) {
            throw new ValidateException(String.format(I18N.text(SO_CATEGORY_TOTAL_LIMIT), limitCount));
        }
    }

    /**
     * 检查上级分类是否存在
     *
     * @param user 用户
     * @param pid  上级分类id
     */
    public void checkPartnerCategoryId(User user, String pid) {
        if (StringUtils.isBlank(pid)) {
            return;
        }
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterEq(filters, DBRecord.ID, pid);
        searchQuery.setLimit(1);
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, ProductCategoryModel.Metadata.API_NAME, searchQuery).getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new ValidateException(I18N.text(PID_DONT_EXISTS));
        }
    }

    public void checkCategoryCodeUnique(User user, String categoryCode, String excludeId) {
        if (StringUtils.isBlank(categoryCode)) {
            throw new ValidateException(I18N.text(SO_CATEGORY_CODE_NOT_EMPTY));
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        List<IFilter> filters = Lists.newArrayList();
        if (StringUtils.isNotBlank(excludeId)) {
            IFilter f1 = new Filter();
            f1.setFieldName(DBRecord.ID);
            f1.setOperator(Operator.NEQ);
            f1.setFieldValues(Lists.newArrayList(excludeId));
            filters.add(f1);
        }
        IFilter f2 = new Filter();
        f2.setFieldName(ProductCategoryModel.Filed.CATEGORY_CODE);
        f2.setOperator(Operator.EQ);
        f2.setFieldValues(Lists.newArrayList(categoryCode));
        filters.add(f2);
        searchTemplateQuery.setFilters(filters);
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, ProductCategoryModel.Metadata.API_NAME, searchTemplateQuery).getData();
        if (CollectionUtils.isNotEmpty(dataList)) {
            throw new ValidateException(I18N.text(SO_CATEGORY_CODE_EXIST));
        }
    }

    public void checkCodeChange(User user, String newCode, String objectDataId) {
        IObjectData objectData = metaDataFindServiceExt.findObjectData(user, objectDataId, ProductCategoryModel.Metadata.API_NAME);
        String originalCode = ObjectDataUtils.getValueOrDefault(objectData, ProductCategoryBO.CODE, "");
        if (!newCode.equals(originalCode)) {
            throw new ValidateException(I18N.text(SO_CODE_CHANGE));
        }
    }

    public void checkCategoryCount(User user) {
        if (productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            return;
        }
        IObjectDescribe productDescribe = serviceFacade.findObject(user.getTenantId(), "ProductObj");
        if (productDescribe.getFieldDescribe("category") != null) {
            SelectOneFieldDescribe category = (SelectOneFieldDescribe) productDescribe.getFieldDescribe("category");
            List<ISelectOption> selectOptions = category.getSelectOptions();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(selectOptions) && selectOptions.size() + 1 >= CATEGORY_LIMIT) {
                throw new ValidateException(I18N.text(PRODUCT_CATEGORY_SIZE_LIMIT, CATEGORY_LIMIT));
            }
        }
    }

    public void doCategoryParamCheckByFuture(User user, CompletableFuture<?>... futures) {
        doCategoryParamCheckByFuture(user, 10L, futures);
    }

    public void doCategoryParamCheckByFuture(User user, long timeout, CompletableFuture<?>... futures) {
        if (futures == null || futures.length == 0) {
            return;
        }
        AtomicReference<String> error = new AtomicReference<>("");
        AtomicReference<Throwable> finalThrowable = new AtomicReference<>();
        try {
            asyncBootstrap.composed(futures).exceptionally(throwable -> {
                if (throwable != null) {
                    finalThrowable.set(throwable);
                    error.set(throwable.getMessage());
                }
                return null;
            }).get(timeout, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            // Restore the interrupted status
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("addCategoryValidate method maybe timeout, tenant:{}", user.getTenantId(), e);
            throw new ValidateException(I18N.text(SO_CATEGORY_ASYNC_TIMEOUT));
        }
        String errorMsg = error.get();
        if (StringUtils.isBlank(errorMsg)) {
            return;
        }
        String newErrorMsg = productCategoryUtils.formattingErrorMsg(errorMsg);
        if (newErrorMsg == null) {
            log.error("doCategoryParamCheckByFuture#校验失败, tenant:{}", user.getTenantId(), finalThrowable.get());
            throw new ValidateException(I18N.text(ASYNC_CHECK_FAILED));
        }
        log.info("doCategoryParamCheckByFuture#ValidateException, tenant:{}", user.getTenantId(), finalThrowable.get());
        throw new ValidateException(newErrorMsg);
    }

    public void productCategoryDeletedValidator(User user, List<IObjectData> categoryDataList) {
        if (CollectionUtils.isEmpty(categoryDataList)) {
            return;
        }
        // 划分出商城分类与产品分类
        Set<String> shopCategoryIds = Sets.newHashSet();
        Set<String> productCategoryIds = Sets.newHashSet();
        categoryDataList.forEach(data -> {
            if (SHOP_CATEGORY_RECORD_TYPE.equals(data.getRecordType())) {
                shopCategoryIds.add(data.getId());
            } else {
                productCategoryIds.add(data.getId());
            }
        });
        checkProductCategoryUsed(user, productCategoryIds);
        checkShopCategoryUsed(user, shopCategoryIds);
    }

    private void checkShopCategoryUsed(User user, Set<String> shopCategoryIds) {
        if (CollectionUtils.isEmpty(shopCategoryIds)) {
            return;
        }
        List<IObjectData> dataList = SFAConfigUtil.isSpuOpen(user.getTenantId()) ?
                checkDataExistByShopCategory(user, shopCategoryIds, Utils.SPU_API_NAME) :
                checkDataExistByShopCategory(user, shopCategoryIds, Utils.PRODUCT_API_NAME);
        if (CollectionUtils.isNotEmpty(dataList)) {
            throw new ValidateException(I18N.text(SO_CHECK_CATEGORY_DELETE));
        }
    }

    private List<IObjectData> checkDataExistByShopCategory(User user, Set<String> shopCategoryIds, String apiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), "is_deleted", Lists.newArrayList("0", "1"));
        SearchUtil.fillFilterHasAnyOf(query.getFilters(), SHOP_CATEGORY_ID, Lists.newArrayList(shopCategoryIds));
        query.setLimit(1);
        return serviceFacade.findBySearchQuery(user, apiName, query).getData();
    }

    private void checkProductCategoryUsed(User user, Set<String> productCategoryIds) {
        if (CollectionUtils.isEmpty(productCategoryIds)) {
            return;
        }
        List<IObjectData> dataList = SFAConfigUtil.isSpuOpen(user.getTenantId()) ?
                checkDataExistByProductCategory(user, productCategoryIds, Utils.SPU_API_NAME) :
                checkDataExistByProductCategory(user, productCategoryIds, Utils.PRODUCT_API_NAME);
        if (CollectionUtils.isNotEmpty(dataList)) {
            throw new ValidateException(I18N.text(SO_CHECK_CATEGORY_DELETE));
        }
    }

    private List<IObjectData> checkDataExistByProductCategory(User user, Set<String> categoryId, String apiName) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        IFilter f1 = new Filter();
        f1.setFieldName("is_deleted");
        f1.setOperator(Operator.IN);
        f1.setFieldValues(Lists.newArrayList("0", "1"));
        IFilter f2 = new Filter();
        f2.setFieldName(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID);
        f2.setOperator(Operator.IN);
        f2.setFieldValues(Lists.newArrayList(categoryId));
        searchTemplateQuery.setFilters(Lists.newArrayList(f1, f2));
        searchTemplateQuery.setLimit(1);
        return serviceFacade.findBySearchQuery(user, apiName, searchTemplateQuery).getData();
    }

    public boolean checkCategoryFieldChanged(IObjectData originalData, IObjectData newData) {
        String newCategory = ObjectDataUtils.getValueOrDefault(newData, "category", "");
        String newCategoryId = ObjectDataUtils.getValueOrDefault(newData, "product_category_id", "");
        String originalCategory = ObjectDataUtils.getValueOrDefault(originalData, "category", "");
        String originalCategoryId = ObjectDataUtils.getValueOrDefault(originalData, "product_category_id", "");
        return !newCategory.equals(originalCategory) || !newCategoryId.equals(originalCategoryId);
    }

    public void checkCategoryIsLoop(User user, String objectId, String parentId) {
        boolean loop = treePathService.isLoop(user, ProductCategoryModel.Metadata.API_NAME, objectId, parentId, ProductCategoryModel.Filed.PRODUCT_CATEGORY_PATH);
        if (loop) {
            throw new ValidateException(I18N.text(SO_DATA_LOOP));
        }
    }

    public void checkCategoryParamOfProduct(String tenantId, IObjectDescribe objectDescribe, ObjectDataDocument spuSkuData) {
        if (!productCategoryUtils.isCloseOldProductCategory(tenantId)) {
            return;
        }
        if (spuSkuData == null || spuSkuData.toObjectData() == null) {
            log.warn("checkCategoryParam# objectData is null, tenant:{}", tenantId);
            return;
        }
        User systemUser = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        IObjectData objectData = spuSkuData.toObjectData();
        objectData.getName();
        if (!openShopCategory(objectDescribe)) {
            return;
        }
        // 检查产品分类的业务类型是否正确。
        checkProductCategoryParam(objectData, systemUser, objectDescribe);
        // 检查商城分类的业务类型是否正确。
        checkShopCategoryParam(objectData, systemUser, objectDescribe);
    }

    private void checkShopCategoryParam(IObjectData objectData, User systemUser, IObjectDescribe objectDescribe) {
        List<String> shopCategoryIdParam = Lists.newArrayList();
        try {
            shopCategoryIdParam = objectData.get(SHOP_CATEGORY_ID, List.class, Lists.newArrayList());
        } catch (Exception e) {
            log.warn("checkShopCategoryParam#parse error, tenant:{}, shopCategory:{}", systemUser.getTenantId(), objectData.get(SHOP_CATEGORY_ID), e);
            shopCategoryIdParam = Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(shopCategoryIdParam)) {
            return;
        }
        IFieldDescribe shopCategoryFieldDescribe = objectDescribe.getFieldDescribe(SHOP_CATEGORY_ID);
        String shopCategoryRecordTypeLabel = getShopCategoryRecordTypeLabel(systemUser, shopCategoryFieldDescribe.getLabel());
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        SearchUtil.fillFilterIn(query.getFilters(), DBRecord.ID, shopCategoryIdParam);
        List<IObjectData> shopCategoryData = metaDataFindServiceExt.findBySearchQueryIgnoreAll(systemUser, SFAPreDefineObject.ProductCategory.getApiName(), query).getData();
        shopCategoryData.stream().filter(d -> !SHOP_CATEGORY_RECORD_TYPE.equals(d.getRecordType())).findAny().ifPresent(data -> {
            throw new ValidateException(I18N.text(SFA_SHOP_CATEGORY_SELECT_ERROR, objectData.getName(), shopCategoryFieldDescribe.getLabel(), shopCategoryRecordTypeLabel, data.getName()));
        });
    }

    private void checkProductCategoryParam(IObjectData objectData, User systemUser, IObjectDescribe objectDescribe) {
        String productCategoryIdParam = ObjectDataUtils.getValueOrDefault(objectData, PRODUCT_CATEGORY_ID, "");
        if (StringUtils.isBlank(productCategoryIdParam)) {
            return;
        }
        IObjectData productCategoryData = metaDataFindServiceExt.findObjectByIdIgnoreAll(systemUser, productCategoryIdParam, SFAPreDefineObject.ProductCategory.getApiName());
        if (productCategoryData == null) {
            return;
        }
        IFieldDescribe productCategoryFieldDescribe = objectDescribe.getFieldDescribe(PRODUCT_CATEGORY_ID);
        String productCategoryRecordTypeLabel = getShopCategoryRecordTypeLabel(systemUser, productCategoryFieldDescribe.getLabel());
        if (SHOP_CATEGORY_RECORD_TYPE.equals(productCategoryData.getRecordType())) {
            throw new ValidateException(I18N.text(SFA_PRODUCT_CATEGORY_SELECT_ERROR,
                    objectData.getName(),
                    productCategoryFieldDescribe.getLabel(),
                    productCategoryRecordTypeLabel, productCategoryData.getName()));
        }
    }

    public String getShopCategoryRecordTypeLabel(IObjectDescribe objectDescribe, String categoryFieldLabel) {
        Optional<IRecordTypeOption> shopCategoryRecordType = ObjectDescribeExt.of(objectDescribe).getRecordTypeOption(SHOP_CATEGORY_RECORD_TYPE);
        if (shopCategoryRecordType.isPresent()) {
            return shopCategoryRecordType.get().getLabel();
        }
        return categoryFieldLabel;
    }

    public String getShopCategoryRecordTypeLabel(User user, String categoryFieldLabel) {
        IObjectDescribe categoryDescribe = serviceFacade.findObject(user.getTenantId(), SFAPreDefineObject.ProductCategory.getApiName());
        return getShopCategoryRecordTypeLabel(categoryDescribe, categoryFieldLabel);
    }

    private boolean openShopCategory(IObjectDescribe objectDescribe) {
        return objectDescribe.getFieldDescribe(SHOP_CATEGORY_ID) != null;
    }

    public void checkCategoryParam(User user, IObjectData categoryData) {
        String pid = ObjectDataUtils.getValueOrDefault(categoryData, ProductCategoryModel.Filed.PID, "");
        if (StringUtils.isBlank(pid)) {
            return;
        }
        User systemUser = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        IObjectData parentCategoryData = metaDataFindServiceExt.findObjectByIdIgnoreAll(systemUser, pid, SFAPreDefineObject.ProductCategory.getApiName());
        if (parentCategoryData == null) {
            return;
        }
        String recordTypeParam = categoryData.getRecordType();
        String recordTypeParent = parentCategoryData.getRecordType();
        if (recordTypeNotConformRule(recordTypeParent, recordTypeParam)) {
            String shopCategoryRecordTypeLabel = getShopCategoryRecordTypeLabel(user, categoryData.getName());
            throw new ValidateException(I18N.text(SFA_PARENT_PRODUCT_CATEGORY_RECORD_TYPE_SELECT_ERROR, shopCategoryRecordTypeLabel));
        }
    }


    private boolean recordTypeNotConformRule(String recordTypeParent, String recordTypeParam) {
        return SHOP_CATEGORY_RECORD_TYPE.equals(recordTypeParent) && !SHOP_CATEGORY_RECORD_TYPE.equals(recordTypeParam) ||
                !SHOP_CATEGORY_RECORD_TYPE.equals(recordTypeParent) && SHOP_CATEGORY_RECORD_TYPE.equals(recordTypeParam);
    }

    public void checkCategoryPathChanged(IObjectData oldData, IObjectData newData) {
        Object oldPath = oldData.get(PRODUCT_CATEGORY_PATH);
        Object newPath = newData.get(PRODUCT_CATEGORY_PATH);
        if (newPath == null) {
            return;
        }
        if (!newPath.equals(oldPath)) {
            throw new ValidateException(I18N.text(SFA_PARAM_NON_COMPLIANT, PRODUCT_CATEGORY_PATH));
        }
    }

    public boolean passParamCheck(User user, IObjectData paramData) {
        String pid = ObjectDataUtils.getValueOrDefault(paramData, ProductCategoryModel.Filed.PID, "");
        String categoryName = paramData.getName();
        String id = paramData.getId();
        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> checkCategoryDepth(user, pid));
        CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> checkPartnerCategoryId(user, pid));
        CompletableFuture<Void> f3 = CompletableFuture.runAsync(() -> checkNameUniqueInSameLevel(user, categoryName, pid, id));
        CompletableFuture<Void> f4 = CompletableFuture.runAsync(() -> checkCategoryParam(user, paramData));
        doCategoryParamCheckByFuture(user, f1, f2, f3, f4);
        return true;
    }

    /**
     * 检查分类是否是末级节点
     */
    public void checkCategoryIsLeafNode(User user, IObjectData categoryData, String categoryDisplayName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), PID, categoryData.getId());
        query.setLimit(1);
        List<IObjectData> dataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user,
                        ProductCategoryModel.Metadata.API_NAME,
                        query,
                        Lists.newArrayList(IObjectData.ID, IObjectData.TENANT_ID, IObjectData.NAME))
                .getData();
        if (CollectionUtils.isNotEmpty(dataList)) {
            String i18nName = I18NUtils.getDataI18nFieldValue(categoryData, IObjectData.NAME, String.class);
            throw new ValidateException(I18N.text(SFA_RELATED_CATEGORY_NOT_END_NODE, i18nName, categoryDisplayName));
        }
    }

    public void checkCategoryIsLeafNode(User user, String categoryId) {
        if (StringUtils.isBlank(categoryId)) {
            return;
        }
        IObjectDescribe categoryDescribe = describeEnhancer.fetchObject(user, SFAPreDefineObject.ProductCategory.getApiName());
        String categoryDisplayName = I18N.text(SFA_CATEGORY_OBJECT_DISPLAY_NAME);
        if (categoryDescribe != null && StringUtils.isNotBlank(categoryDescribe.getDisplayName())) {
            categoryDisplayName = categoryDescribe.getDisplayName();
        }
        IObjectData categoryData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, categoryId, ProductCategoryModel.Metadata.API_NAME);
        if (categoryData == null) {
            return;
        }
        checkCategoryIsLeafNode(user, categoryData, categoryDisplayName);
    }

    public void checkCategoryIsRelated(User user, String categoryId, IObjectDescribe categoryDescribe, String relatedObjectDescribeApiName) {
        if (StringUtils.isAnyBlank(categoryId, relatedObjectDescribeApiName)) {
            return;
        }
        IObjectData categoryData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, categoryId, ProductCategoryModel.Metadata.API_NAME);
        boolean isRelated = categoryIsRelated(user, categoryData, relatedObjectDescribeApiName);
        if (!isRelated) {
            return;
        }
        String categoryDisplayName = categoryDescribe.getDisplayName();
        String i18nName = ProductCategoryBO.of(categoryData).getI18nName();

        IObjectDescribe relatedDescribe = describeServiceExt.findObjectDescribe(user, relatedObjectDescribeApiName);
        relatedObjectDescribeApiName = I18N.text(String.format(OBJECT_DISPLAY_NAME_FORMAT, relatedObjectDescribeApiName.toLowerCase()));
        if (relatedDescribe != null) {
            relatedObjectDescribeApiName = relatedDescribe.getDisplayName();
        }
        throw new ValidateException(I18N.text(SFA_USED_CATEGORY_FORBIDDEN_TO_ADD_CHILD_NODE, i18nName, categoryDisplayName, relatedObjectDescribeApiName));
    }

    public boolean categoryIsRelated(@NotNull User user, @Nullable IObjectData categoryData, String objectDescribeApiName) {
        if (categoryData == null || StringUtils.isBlank(objectDescribeApiName)) {
            return false;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        if (productCategoryUtils.isCloseOldProductCategory(user.getTenantId())) {
            SearchUtil.fillFilterEq(query.getFilters(), PRODUCT_CATEGORY_ID, categoryData.getId());
        } else {
            SearchUtil.fillFilterEq(query.getFilters(), CATEGORY, ProductCategoryBO.of(categoryData).getCode());
        }
        SearchUtil.fillFilterGTE(query.getFilters(), DBRecord.IS_DELETED, "0");
        query.setLimit(1);
        List<IObjectData> dataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user,
                        objectDescribeApiName,
                        query,
                        Lists.newArrayList(DBRecord.ID, IObjectData.TENANT_ID, IObjectData.NAME))
                .getData();
        return CollectionUtils.isNotEmpty(dataList);
    }

    public void checkCategoryIsRelated(User user, String categoryId, IObjectDescribe objectDescribe) {
        if (GrayUtil.skipCategoryLeafNodeCheck(user.getTenantId())) {
            return;
        }
        if (StringUtils.isBlank(categoryId)) {
            return;
        }
        if (objectDescribe == null) {
            objectDescribe = describeEnhancer.fetchObject(user, SFAPreDefineObject.ProductCategory.getApiName());
        }
        checkCategoryIsRelated(user, categoryId, objectDescribe, SFAPreDefineObject.Product.getApiName());
        if (bizConfigThreadLocalCacheService.openSpu(user.getTenantId())) {
            checkCategoryIsRelated(user, categoryId, objectDescribe, SFAPreDefineObject.SPU.getApiName());
        }
    }

    public void checkCategoryIsLeafNode(User user, ObjectDataDocument objectData) {
        if (GrayUtil.skipCategoryLeafNodeCheck(user.getTenantId())) {
            return;
        }
        if (objectData == null || objectData.toObjectData() == null) {
            return;
        }
        IObjectData data = objectData.toObjectData();
        String productCategoryId = ObjectDataUtils.getValue(data, PRODUCT_CATEGORY_ID, String.class, null);
        if (StringUtils.isBlank(productCategoryId)) {
            return;
        }
        checkCategoryIsLeafNode(user, productCategoryId);
    }

    public void checkCategoryIsLeafNode(User user, ObjectDataDocument argObjectData, IObjectData dbMasterData) {
        if (GrayUtil.skipCategoryLeafNodeCheck(user.getTenantId())) {
            return;
        }
        if (argObjectData == null || argObjectData.toObjectData() == null || dbMasterData == null) {
            return;
        }
        String argProductCategoryId = ObjectDataUtils.getValue(argObjectData.toObjectData(), PRODUCT_CATEGORY_ID, String.class, "");
        String originalProductCategoryId = ObjectDataUtils.getValue(dbMasterData, PRODUCT_CATEGORY_ID, String.class, "");
        if (originalProductCategoryId.equals(argProductCategoryId)) {
            return;
        }
        checkCategoryIsLeafNode(user, argObjectData);
    }

    public List<ProductCategoryObject.NodeInfo> checkCategoriesIsLeafNode(User user, Set<String> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Lists.newArrayList();
        }
        Set<String> nonLeafNodeIds = getNonLeafNodeIds(user, categoryIds);
        return getNonLeafNodeInfo(user, nonLeafNodeIds);
    }

    private List<ProductCategoryObject.NodeInfo> getNonLeafNodeInfo(User user, Set<String> nonLeafNodeIds) {
        if (CollectionUtils.isEmpty(nonLeafNodeIds)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), IObjectData.ID, nonLeafNodeIds);
        List<IObjectData> nonLeafCategories = metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, ProductCategoryBO.PRODUCT_CATEGORY_OBJ, query, Lists.newArrayList(IObjectData.ID, IObjectData.NAME));
        List<ProductCategoryObject.NodeInfo> nodeInfos = Lists.newArrayList();
        for (IObjectData nonLeafCategory : nonLeafCategories) {
            String i18nName = I18NUtils.getDataI18nName(nonLeafCategory);
            ProductCategoryObject.NodeInfo item = ProductCategoryObject.NodeInfo
                    .builder()
                    .id(nonLeafCategory.getId())
                    .name(i18nName)
                    .isLeaf(false)
                    .build();
            nodeInfos.add(item);
        }
        return nodeInfos;
    }

    @NotNull
    private Set<String> getNonLeafNodeIds(User user, Set<String> categoryIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), ProductCategoryBO.PID, categoryIds);
        return metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, ProductCategoryBO.PRODUCT_CATEGORY_OBJ, query, Lists.newArrayList(ProductCategoryBO.PID)).stream().map(d -> ProductCategoryBO.of(d).getPid()).collect(Collectors.toSet());
    }

    public Set<String> getIsRelatedCategories(User user, Set<String> categoryIds) {
        boolean objectify = productCategoryUtils.isCloseOldProductCategory(user.getTenantId());
        String bizProductSql = getIsRelatedCategorySql(categoryIds, "biz_product", objectify);
        Set<String> isRelatedCategoriesSet = getIsRelatedCategoriesBySql(user, bizProductSql);
        if (bizConfigThreadLocalCacheService.openSpu(user.getTenantId())) {
            String bizSpuSql = getIsRelatedCategorySql(categoryIds, "stand_prod_unit", objectify);
            Set<String> isRelatedCategoriesFromSPU = getIsRelatedCategoriesBySql(user, bizSpuSql);
            isRelatedCategoriesSet.addAll(isRelatedCategoriesFromSPU);
        }
        return isRelatedCategoriesSet;
    }

    private Set<String> getIsRelatedCategoriesBySql(User user, String sql) {
        try {
            List<Map> bySql = objectDataService.findBySql(user.getTenantId(), sql);
            if (CollectionUtils.isEmpty(bySql)) {
                return Sets.newHashSet();
            }
            return bySql.stream()
                    .filter(x -> x.get("category_id") != null)
                    .map(x -> String.valueOf(x.get("category_id")))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("getIsRelatedCategoriesBySql error, sql:{}", sql, e);
        }
        return Sets.newHashSet();
    }

    private String getIsRelatedCategorySql(Set<String> categoryIds, String tableName, boolean objectify) {
        if (CollectionUtils.isEmpty(categoryIds) || StringUtils.isBlank(tableName)) {
            return null;
        }
        String where = "p.product_category_id = c.id";
        if (!objectify) {
            where = "p.category = c.code";
        }
        String sql = "SELECT c.id as category_id\n" +
                "FROM product_category c\n" +
                "WHERE is_deleted in ('0','1')\n" +
                "  AND c.id IN %s\n" +
                "  AND EXISTS (\n" +
                "      SELECT 1\n" +
                "      FROM %s p\n" +
                "      WHERE %s\n" +
                "        AND p.is_deleted in ('0','1')\n" +
                "  );";
        String formatSql = String.format(sql, SqlEscaper.in_clause(categoryIds), tableName, where);
        log.warn("getIsRelatedCategorySql:{}", formatSql);
        return formatSql;
    }
}
