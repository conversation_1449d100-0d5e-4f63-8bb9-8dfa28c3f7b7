package com.facishare.crm.sfa.predefine.service.modulectrl;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.ObjectValueMappingService;
import com.facishare.paas.appframework.metadata.ObjectConvertRuleService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectMappingRuleDetailInfo;
import com.facishare.paas.metadata.api.IObjectMappingRuleEnumInfo;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ILayoutRuleService;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectMappingRuleDetailInfo;
import com.facishare.paas.metadata.impl.ObjectMappingRuleEnumInfo;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 非标产品初始化
 *
 * @IgnoreI18nFile
 */

@Component
@Slf4j
public class NonStandardProductInitService extends AbstractModuleInitService {

    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    ILayoutService layoutService;
    @Autowired
    ILayoutRuleService layoutRuleService;
    @Autowired
    ObjectValueMappingService objectValueMappingService;
    @Autowired
    ConfigService configService;
    @Autowired
    BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    private DescribeWithSimplifiedChineseService describeWithSimplifiedChineseService;
    @Autowired
    private SFABizObjMappingRuleWrapperService mappingRuleWrapperService;
    @Autowired
    private ObjectConvertRuleService objectConvertRuleService;


    private static final List<String> apiNames = Lists.newArrayList(Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.SALE_CONTRACT_LINE_API_NAME, Utils.QUOTE_LINES_API_NAME, Utils.NEW_OPPORTUNITY_LINES_API_NAME);


    private static final String PRODUCT_TYPE = "{\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"普通品\",\"value\":\"standard\"},{\"label\":\"非标品\",\"value\":\"non_standard\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"standard\",\"label\":\"产品类型\",\"is_need_convert\":false,\"api_name\":\"product_type\",\"is_index_field\":false,\"status\":\"released\"}";
    private static final String FIELD_NON_STANDARD_PRO_DESCRIPTION = "non_standard_pro_description";
    private static final String FIELD_DETAIL_TYPE = "detail_type";
    private static final String NON_STANDARD_PRO_DESCRIPTION = "{\"default_is_expression\":false,\"auto_adapt_places\":false,\"pattern\":\"\",\"is_unique\":false,\"type\":\"long_text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"max_length\":2000,\"is_index\":false,\"is_active\":true,\"is_encrypted\":false,\"min_length\":0,\"label\":\"非标产品描述\",\"is_need_convert\":false,\"api_name\":\"non_standard_pro_description\",\"is_index_field\":false,\"status\":\"released\"}";
    private static final String DETAIL_TYPE = "{\"default_is_expression\":false,\"auto_adapt_places\":false,\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"普通品\",\"value\":\"standard\"},{\"label\":\"非标品\",\"value\":\"non_standard\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"standard\",\"label\":\"明细行类型\",\"is_need_convert\":false,\"api_name\":\"detail_type\",\"is_index_field\":false,\"status\":\"released\"}";
    private static final String DETAIL_TYPE_CHANGED = "{\"is_index\":false,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":false,\"label\":\"非标品切换普通品\",\"type\":\"true_or_false\",\"is_required\":false,\"api_name\":\"detail_type_changed\",\"options\":[{\"label\":\"是\",\"value\":true},{\"label\":\"否\",\"value\":false}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}";


    @Override
    public String getModuleCode() {
        return IModuleInitService.NON_STANDARD_PRODUCT;
    }

    @Override
    public ConfigCtrlModule.Result initModule(String tenantId, String userId) {
        TraceContext traceContext = TraceContext.get();
        String traceId = traceContext.getTraceId();
        User user = new User(tenantId, userId);
        List<String> apiNameList = Lists.newArrayList(apiNames);
        apiNameList.add(Utils.PRODUCT_API_NAME);
        try {
            Map<String, IObjectDescribe> describeMap = describeWithSimplifiedChineseService.findByDescribeApiNameList(user, apiNameList);
            handFieldList(describeMap);
            refreshMapping(tenantId);
            refreshRuleMapping(user);
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                    .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                    .value(ConfigCtrlModule.Value.builder()
                            .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString())
                            .build())
                    .build();

        } catch (Exception e) {
            log.error("initNonStandardProduct failed:", e);
            sendAuditLog(tenantId, userId, traceId + ":" + e.getMessage(), "initNonStandardProductFailed");
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Fail.getErrCode())
                    .errMessage(e.getMessage())
                    .value(ConfigCtrlModule.Value.builder()
                            .openStatus(ConfigCtrlModule.OpenStatus.CLOSE.toString())
                            .build())
                    .build();
        }
    }

    private void handFieldList(Map<String, IObjectDescribe> describeMap) {
        describeMap.forEach((apiName, describe) -> {
            List<IFieldDescribe> addFieldList = Lists.newArrayList();
            if (Objects.equals(apiName, Utils.PRODUCT_API_NAME)) {
                addFieldIfNotExists(describe, PRODUCT_TYPE, addFieldList);
            } else {
                addFieldIfNotExists(describe, NON_STANDARD_PRO_DESCRIPTION, addFieldList);
                addFieldIfNotExists(describe, DETAIL_TYPE, addFieldList);
                addFieldIfNotExists(describe, DETAIL_TYPE_CHANGED, addFieldList);
            }
            if (CollectionUtils.isNotEmpty(addFieldList)) {
                try {
                    objectDescribeService.addCustomFieldDescribe(describe, addFieldList);
                } catch (MetadataServiceException e) {
                    log.error("NonStandardProductInitService error", e);
                    throw new MetaDataBusinessException(e.getMessage());
                }
            }
        });
    }

    private void addFieldIfNotExists(IObjectDescribe describe, String productType, List<IFieldDescribe> addFieldList) {
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(productType);
        if (describe.getFieldDescribe(fieldDescribe.getApiName()) == null) {
            addFieldList.add(fieldDescribe);
        }
    }


    private void refreshMapping(String tenantId) {
        Map<String, String> fieldMapping = Maps.newHashMap();
        fieldMapping.put(FIELD_NON_STANDARD_PRO_DESCRIPTION, FIELD_NON_STANDARD_PRO_DESCRIPTION);
        fieldMapping.put(FIELD_DETAIL_TYPE, FIELD_DETAIL_TYPE);
        Map<String,String> fieldOption = Maps.newHashMap();
        fieldOption.put(FIELD_DETAIL_TYPE,"[{\"source_option\":\"standard\",\"target_option\":\"standard\"},{\"source_option\":\"non_standard\",\"target_option\":\"non_standard\"}]");
        try {
            mappingRuleWrapperService.addFieldMapping(tenantId, "rule_quotelinesobj2salesorderproduct__c", fieldMapping, fieldOption);
            mappingRuleWrapperService.addFieldMapping(tenantId, "rule_salesorderprodobj2quotelinesobj__c", fieldMapping, fieldOption);
            if (SFAConfigUtil.isSaleContractOpen(tenantId)) {
                mappingRuleWrapperService.addFieldMapping(tenantId, "rule_quotelinesobj2salecontractlineobj__c", fieldMapping, fieldOption);
                mappingRuleWrapperService.addFieldMapping(tenantId, "rule_salecontractlineobj2salesorderproductobj__c", fieldMapping, fieldOption);
            }
            mappingRuleWrapperService.addFieldMapping(tenantId, "rule_newopportunitylinesobj2quotelinesobj__c", fieldMapping, fieldOption);
        } catch (MetadataServiceException e) {
            log.error("MetadataServiceException:", e);
        }
    }

    private void refreshRuleMapping(User user) {
        if (!objectConvertRuleService.supportConvertRule(user.getTenantId())) {
            return;
        }
        List<String> ruleApiNameList = Lists.newArrayList("rule_convert_quote_to_order");
        if (SFAConfigUtil.isSaleContractOpen(user.getTenantId())) {
            ruleApiNameList.add("rule_convert_quote_to_salecontract");
            ruleApiNameList.add("rule_convert_salecontract_to_order");
        }
        List<String> detailRuleApiNameList = Lists.newArrayList("rule_convert_quoteline_to_orderline", "rule_convert_quoteline_to_salecontractline", "rule_convert_salecontractline_to_orderline");
        Map<String, String> fieldOption = Maps.newHashMap();
        fieldOption.put(FIELD_DETAIL_TYPE, "[{\"source_option\":\"standard\",\"target_option\":\"standard\"},{\"source_option\":\"non_standard\",\"target_option\":\"non_standard\"}]");
        ruleApiNameList.forEach(ruleApiName -> {
            List<IObjectMappingRuleInfo> ruleInfos = objectConvertRuleService.findConvertRuleByApiName(user, ruleApiName);
            if (CollectionUtils.isEmpty(ruleInfos)) {
                return;
            }
            ruleInfos.forEach(iObjectMappingRuleInfo -> {
                if (detailRuleApiNameList.contains(iObjectMappingRuleInfo.getRuleApiName())) {
                    List<IObjectMappingRuleDetailInfo> fieldMappingList = iObjectMappingRuleInfo.getFieldMapping();
                    fieldMappingList.add(getFieldMapping(FIELD_NON_STANDARD_PRO_DESCRIPTION, FIELD_NON_STANDARD_PRO_DESCRIPTION, null));
                    fieldMappingList.add(getFieldMapping(FIELD_DETAIL_TYPE, FIELD_DETAIL_TYPE, fieldOption));
                    iObjectMappingRuleInfo.setFieldMapping(fieldMappingList);
                }
                iObjectMappingRuleInfo.setTenantId(user.getTenantId());
            });
            objectConvertRuleService.update(user, ruleInfos);
        });
    }

    private IObjectMappingRuleDetailInfo getFieldMapping(String sourceFieldApiName, String targetFieldApiName, Map<String, String> optionMapping) {
        IObjectMappingRuleDetailInfo mappingRuleDetailInfo = new ObjectMappingRuleDetailInfo();
        mappingRuleDetailInfo.setSourceFieldName(sourceFieldApiName);
        mappingRuleDetailInfo.setTargetFieldName(targetFieldApiName);
        if (MapUtils.isNotEmpty(optionMapping)) {
            String option = optionMapping.get(sourceFieldApiName);
            if (StringUtils.isNotBlank(option)) {
                List<Map> objectMappingRuleEnumInfos = JSON.parseArray(option, Map.class);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(objectMappingRuleEnumInfos)) {
                    List<IObjectMappingRuleEnumInfo> mappings = objectMappingRuleEnumInfos.stream()
                            .map(ObjectMappingRuleEnumInfo::new).collect(Collectors.toList());
                    mappingRuleDetailInfo.setOptionMapping(mappings);
                }
            }
        }
        return mappingRuleDetailInfo;
    }


}
