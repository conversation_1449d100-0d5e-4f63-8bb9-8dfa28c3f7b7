package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.platform.converter.ObjectConverter;
import com.facishare.crm.platform.utils.RequestSourceResolver;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.enums.AgreementStatus;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import static com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel.AGREEMENT_STATUS;

/**
 * Created by Sundy on 2024/10/21 16:03
 */
public class PartnerAgreementDetailWebDetailController extends StandardWebDetailController {
    protected final ChannelService channelService = SpringUtil.getContext().getBean("channelServiceProvider", ChannelService.class);
    protected final MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean("metaDataFindServiceExt", MetaDataFindServiceExt.class);
    protected final ObjectConverter objectConverter = SpringUtil.getContext().getBean("objectConverter", ObjectConverter.class);
    protected final RequestSourceResolver requestSourceResolver = SpringUtil.getContext().getBean("requestSourceResolver", RequestSourceResolver.class);

    protected String admissionObject;
    protected String belongDataId;
    protected IObjectData admissionData;
    protected AgreementStatus agreementStatus;

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        admissionObject = channelService.fetchChannelAdmissionObject(controllerContext.getUser());
        belongDataId = channelService.getAdmissionObjectDataId(result.getData().toObjectData(), admissionObject);
        admissionData = metaDataFindServiceExt.findObjectByIdIgnoreAll(controllerContext.getUser(), belongDataId, admissionObject);
        initiateRenewalButtonHandle(arg.getIncludeLayout(), after);
        renewButtonHandle(arg.getIncludeLayout(), after);
        commonButtonHandle(arg.getIncludeLayout(), after);
        return after;
    }

    private void commonButtonHandle(Boolean includeLayout, Result after) {
        if (!Boolean.TRUE.equals(includeLayout) || after.getLayout() == null) {
            return;
        }
        WebDetailLayout.of(after.getLayout().toLayout()).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.INVALID.getActionCode()));
        WebDetailLayout.of(after.getLayout().toLayout()).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.CLONE.getActionCode()));
    }

    /**
     * 「去续约」按钮处理
     *
     * @param includeLayout 是否包含布局
     * @param after         返回结果
     */
    private void renewButtonHandle(Boolean includeLayout, Result after) {
        if (!Boolean.TRUE.equals(includeLayout) || after.getLayout() == null) {
            return;
        }
        if (RequestUtil.isMobileOrH5Request() || !requestSourceResolver.isErAppRequest()) {
            WebDetailLayout.of(after.getLayout().toLayout()).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.RENEW.getActionCode()));
        }
        boolean removeButton = !DataUtils.getValue(data, PartnerAgreementDetailModel.ABLE_RENEWAL, Boolean.class, Boolean.FALSE);
        if (removeButton) {
            WebDetailLayout.of(after.getLayout().toLayout()).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.RENEW.getActionCode()));
        }
    }

    /**
     * 「发起签约」按钮处理
     *
     * @param includeLayout 是否包含布局
     * @param after         返回结果
     */
    private void initiateRenewalButtonHandle(Boolean includeLayout, Result after) {
        if (!Boolean.TRUE.equals(includeLayout) || after.getLayout() == null) {
            return;
        }
        if (RequestUtil.isMobileOrH5Request() || requestSourceResolver.isErAppRequest()) {
            WebDetailLayout.of(after.getLayout().toLayout()).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.INITIATE_RENEWAL.getActionCode()));
        }
        agreementStatus = AgreementStatus.find(DataUtils.getValue(data, AGREEMENT_STATUS, String.class, null));
        boolean removeButton = !channelService.allowInitiateRenewal(controllerContext.getUser(), admissionObject, belongDataId) ||
                agreementStatus != AgreementStatus.ACTIVE;
        if (removeButton) {
            WebDetailLayout.of(after.getLayout().toLayout()).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.INITIATE_RENEWAL.getActionCode()));
        }
    }
}
