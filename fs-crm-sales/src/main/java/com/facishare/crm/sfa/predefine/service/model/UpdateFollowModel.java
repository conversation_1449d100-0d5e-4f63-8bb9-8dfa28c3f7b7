package com.facishare.crm.sfa.predefine.service.model;


import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface UpdateFollowModel {

    @Data
    class Arg {
        private String apiName;
        private List<String> objectIds;
    }

    @Data
    class RecyclingArg {
        private String objectId;
        /**
         * 客户:AccountObj, 线索:LeadsObj
         */
        private String objectApiName;
        private String targetId;  // 转移目标：公海id或线索池id
        private String functionApiName;
        private Integer recyclingReasonType;  // 回收原因类型
        private Integer recyclingDays;  // 回收设置的天数
        private Long expireTime;  // 到期时间
    }


    @Data
    @Builder
    class Result {
        private boolean success;
        private String msg;
    }

}
