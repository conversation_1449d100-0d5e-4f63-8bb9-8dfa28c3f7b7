package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.QuoterModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.expression.CalculateDataInfo;
import com.facishare.paas.appframework.metadata.expression.Expression;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.ExpressionFactory;
import com.facishare.paas.appframework.metadata.expression.ExpressionVariableFactory;
import com.facishare.paas.appframework.metadata.expression.ExtVariableType;
import com.facishare.paas.appframework.metadata.expression.SimpleExpression;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.Where;
import com.google.common.base.MoreObjects;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdvancedFormulaCalculateService {
    private static final String AD_EXT_ID = "ad_ext_id";
    private static final String NONSTANDARD_ATTRIBUTE_JSON = "nonstandard_attribute_json";
    private static final String ATTRIBUTE_JSON = "attribute_json";
    private static final String DATA_INDEX = "data_index";
    private static final String ADVANCED_FORMULA_ID = "advanced_formula_id";
    @Autowired
    private ServiceFacade serviceFacade;
    @Resource
    private ExpressionCalculateLogicService expressionCalculateLogicService;


    public List<ObjectDataDocument> formulaCalculate(QuoterModel.CalculateDataParam arg, ServiceContext context) {
        List<CalculateDataInfo> calculateDataInfoList = Lists.newArrayList();
        String detailObjectApiName = arg.getDetailObjectApiName();
        ObjectDataDocument masterData = arg.getMasterData();
        IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), detailObjectApiName);
        List<String> productIdList = Lists.newArrayList();
        List<String> bomIdList = Lists.newArrayList();
        Map<String, ObjectDataDocument> detailDataMap = Optional.ofNullable(arg.getDetailDataMap()).orElse(Maps.newHashMap());
        arg.getDetailCalculateFieldApiNames().getOrDefault(detailObjectApiName, Maps.newHashMap()).forEach((index, data) -> {
            ObjectDataDocument objectDataDocument = detailDataMap.get(index);
            if (objectDataDocument != null) {
                IObjectData objectData = objectDataDocument.toObjectData();
                objectData.set(DATA_INDEX, index);
                String bomId = arg.getFieldsMap().getOrDefault(BomConstants.FIELD_BOM_ID, BomConstants.FIELD_BOM_ID);
                if (StringUtils.isNotBlank(bomId) && StringUtils.isNotBlank(objectData.get(bomId, String.class))) {
                    bomIdList.add(objectData.get(bomId, String.class));
                    objectDataDocument.put(AD_EXT_ID, objectData.get(bomId, String.class));
                } else {
                    String productId = arg.getFieldsMap().getOrDefault(BomConstants.FIELD_PRODUCT_ID, BomConstants.FIELD_PRODUCT_ID);
                    if (StringUtils.isNotBlank(productId) && StringUtils.isNotBlank(objectData.get(productId, String.class))) {
                        productIdList.add(objectData.get(productId, String.class));
                        objectDataDocument.put(AD_EXT_ID, objectData.get(productId, String.class));
                    }
                }
            }
        });
        List<IObjectData> dataList = queryAdvanceFormula(context, detailObjectApiName, productIdList, bomIdList);
        Set<String> ids = dataList.stream().map(x -> x.get(ADVANCED_FORMULA_ID, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIdsIgnoreAll(context.getTenantId(), Lists.newArrayList(ids), Utils.ADVANCED_FORMULA_API_NAME);
        Map<String, String> map = objectDataByIds.stream().collect(Collectors.toMap(DBRecord::getId, x -> x.get(QuoterModel.AdvancedFormulaModel.FORMULA, String.class), (x1, x2) -> x1));
        dataList.forEach(x -> x.set(QuoterModel.AdvancedFormulaModel.FORMULA, map.get(x.get(ADVANCED_FORMULA_ID, String.class))));
        Table<String, String, String> dataTable = HashBasedTable.create();
        if (CollectionUtils.notEmpty(dataList)) {
            dataList.forEach(x -> {
                String dataId = MoreObjects.firstNonNull(x.get(QuoterModel.AdvancedFormulaModel.BOM_ID, String.class), x.get(QuoterModel.AdvancedFormulaModel.PRODUCT_ID, String.class));
                dataTable.put(dataId, x.get(QuoterModel.AdvancedFormulaModel.FIELD_NAME, String.class), x.get(QuoterModel.AdvancedFormulaModel.FORMULA, String.class));
            });
        }
        arg.getDetailCalculateFieldApiNames().getOrDefault(detailObjectApiName, Maps.newHashMap()).forEach((index, fields) -> {
            ObjectDataDocument data = detailDataMap.get(index);
            if (CollectionUtils.notEmpty(fields) && data != null) {
                CalculateDataInfo calculateDataInfo = getCalculateDataInfo(arg, detailObjectApiName, masterData, objectDescribe, dataTable, index, data);
                calculateDataInfoList.add(calculateDataInfo);
            }
        });
        List<ObjectDataDocument> resultList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(calculateDataInfoList)) {
            expressionCalculateLogicService.bulkCalculateWithCalculateDataInfo(calculateDataInfoList);
            Map<String, List<String>> detailMap = arg.getDetailCalculateFieldApiNames().getOrDefault(detailObjectApiName, Maps.newHashMap());
            calculateDataInfoList.forEach(x -> {
                List<IObjectData> calculateDataList = x.getDataList();
                if (CollectionUtils.notEmpty(calculateDataList)) {
                    calculateDataList.forEach(k -> {
                        String dataIndex = k.get(DATA_INDEX, String.class);
                        List<String> filterField = detailMap.getOrDefault(dataIndex, Lists.newArrayList());
                        if (CollectionUtils.notEmpty(filterField)) {
                            ObjectDataDocument objectDataDocument = new ObjectDataDocument();
                            filterField.forEach(g -> objectDataDocument.put(g, k.get(g)));
                            objectDataDocument.put(DATA_INDEX, k.get(DATA_INDEX));
                            resultList.add(objectDataDocument);
                        }
                    });
                }
            });
        }

        return resultList;
    }

    @NotNull
    private CalculateDataInfo getCalculateDataInfo(QuoterModel.CalculateDataParam arg, String detailObjectApiName, ObjectDataDocument masterData, IObjectDescribe objectDescribe, Table<String, String, String> dataTable, String index, ObjectDataDocument data) {
        IObjectData objectData = data.toObjectData();
        CalculateDataInfo calculateDataInfo = new CalculateDataInfo();
        calculateDataInfo.setDataList(Lists.newArrayList(objectData));
        calculateDataInfo.setDescribe(objectDescribe);
        calculateDataInfo.setMasterData(masterData.toObjectData());
        return handleExpression(calculateDataInfo, objectData, arg.getDetailCalculateFieldApiNames().get(detailObjectApiName).get(index), objectDescribe, dataTable, arg.getFieldsMap());
    }

    private CalculateDataInfo handleExpression(CalculateDataInfo calculateDataInfo, IObjectData objectData, List<String> fields, IObjectDescribe objectDescribe, Table<String, String, String> dataTable, Map<String, String> fieldsMap) {
        List<SimpleExpression> expressionList = Lists.newArrayList();
        Map<String, Object> extData = Maps.newHashMap();
        fields.forEach(field -> {
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(field);
            SimpleExpression simpleExpression = null;
            String customId = objectData.get(AD_EXT_ID, String.class);
            if (StringUtils.isNotBlank(customId) && StringUtils.isNotBlank(dataTable.get(customId, field))) {
                String expr = dataTable.get(customId, field);
                JSONObject exprObject = JSON.parseObject(expr);
                simpleExpression = SimpleExpression.builder()
                        .id(field)
                        .expression(exprObject.getString("expression"))
                        .returnType(fieldDescribe == null ? exprObject.getString("return_type") : fieldDescribe.getType())
                        .nullAsZero(exprObject.getBooleanValue("default_to_zero"))
                        .decimalPlaces(exprObject.getIntValue("decimal_places"))
                        .build();
                //4、构造Expression并解析自定义变量
                Expression expression = ExpressionFactory.createExpression(objectDescribe, simpleExpression);
                List<ExpressionVariableFactory.ExtVariable> attrVariables = expression.getExtVariablesByType(ExtVariableType.ATTRIBUTE.getCode());
                List<ExpressionVariableFactory.ExtVariable> nonAttrVariables = expression.getExtVariablesByType(ExtVariableType.NON_ATTRIBUTE.getCode());
                if (CollectionUtils.notEmpty(attrVariables)) {
                    List<SimpleExpression.VariableInfo> extVariableInfos = attrVariables.stream().map(d -> SimpleExpression.VariableInfo.of(d.getName(), IFieldType.TEXT)).collect(Collectors.toList());
                    simpleExpression.setExtVariables(extVariableInfos);

                    Map<String, Object> attrMap = objectData.get(fieldsMap.getOrDefault(ATTRIBUTE_JSON, ATTRIBUTE_JSON), Map.class, Maps.newHashMap());
                    //6、构造绑定自定义变量的map，key是"EXT#AGGR#rule1"，value是rule1的值
                    attrVariables.forEach(variable -> extData.put(variable.getName(), attrMap.get(variable.getVariableBizId())));
                }
                if (CollectionUtils.notEmpty(nonAttrVariables)) {
                    List<SimpleExpression.VariableInfo> extVariableInfos = nonAttrVariables.stream().map(d -> SimpleExpression.VariableInfo.of(d.getName(), exprObject.getString("return_type"))).collect(Collectors.toList());
                    simpleExpression.setExtVariables(extVariableInfos);

                    //5、查询自定义变量的值，此时aggrIds是从"EXT#AGGR#rule1"里解析出来的rule1，aggrValues的key是rule1，value是根据rule1查到的值
                    Map<String, Object> nonAttrMap = objectData.get(fieldsMap.getOrDefault(NONSTANDARD_ATTRIBUTE_JSON, NONSTANDARD_ATTRIBUTE_JSON), Map.class, Maps.newHashMap());
                    //6、构造绑定自定义变量的map，key是"EXT#AGGR#rule1"，value是rule1的值
                    nonAttrVariables.forEach(variable -> extData.put(variable.getName(), nonAttrMap.get(variable.getVariableBizId())));
                }
            } else {
                if (Objects.nonNull(fieldDescribe)) {
                    simpleExpression = SimpleExpression.builder()
                            .id(fieldDescribe.getApiName())
                            .expression(fieldDescribe.getExpression())
                            .returnType(fieldDescribe.get("return_type", String.class, fieldDescribe.getType()))
                            .nullAsZero(fieldDescribe.getDefaultToZero())
                            .decimalPlaces(fieldDescribe.get("decimal_places", Integer.class, 2))
                            .build();
                } else {
                    simpleExpression = SimpleExpression.builder()
                            .id(field)
                            .expression(null)
                            .returnType(IFieldType.NUMBER)
                            .nullAsZero(false)
                            .decimalPlaces(0)
                            .build();
                }
            }
            if (simpleExpression != null) {
                expressionList.add(simpleExpression);
            } else {
                log.warn("字段{}的公式为空", field);
            }
        });
        calculateDataInfo.setExpressionList(expressionList);
        calculateDataInfo.setExtData(extData);
        return calculateDataInfo;
    }


    private List<IObjectData> queryAdvanceFormula(ServiceContext serviceContext, String apiName, List<String> productIdList, List<String> bomIdList) {
        List<IObjectData> dataList = Lists.newArrayList();
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(2000);
        query.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "is_deleted", "0");
        SearchUtil.fillFilterEq(filters, QuoterModel.AdvancedFormulaModel.OBJECT_NAME, apiName);
        query.setFilters(filters);
        List<Wheres> wheres = Lists.newArrayList();
        if (CollectionUtils.notEmpty(productIdList)) {
            Wheres where = new Wheres();
            List<IFilter> proFilters = Lists.newArrayList();
            SearchUtil.fillFilterIn(proFilters, QuoterModel.AdvancedFormulaModel.PRODUCT_ID, productIdList);
            SearchUtil.fillFilterIsNull(proFilters, QuoterModel.AdvancedFormulaModel.BOM_ID);
            where.setFilters(proFilters);
            where.setConnector(Where.CONN.OR.toString());
            wheres.add(where);
        }
        if (CollectionUtils.notEmpty(bomIdList)) {
            Wheres where = new Wheres();
            List<IFilter> bomFilters = Lists.newArrayList();
            SearchUtil.fillFilterIn(bomFilters, QuoterModel.AdvancedFormulaModel.BOM_ID, bomIdList);
            where.setFilters(bomFilters);
            where.setConnector(Where.CONN.OR.toString());
            wheres.add(where);
        }
        query.setWheres(wheres);

        if (CollectionUtils.notEmpty(productIdList) || CollectionUtils.notEmpty(bomIdList)) {
            int offset = 0;
            int loopCnt = 0;
            QueryResult<IObjectData> queryResult;
            while (loopCnt < 10) {
                query.setOffset(offset);
                queryResult = serviceFacade.findBySearchQueryIgnoreAll(serviceContext.getUser(), SFAPreDefineObject.AdvancedFormulaLine.getApiName(), query);
                List resultData = queryResult == null ? Lists.newArrayList() : queryResult.getData();
                if (CollectionUtils.notEmpty(resultData)) {
                    dataList.addAll(resultData);
                }
                if (CollectionUtils.empty(resultData) || resultData.size() < 2000) {
                    break;
                }
                offset += 2000;
                loopCnt++;
            }
        }
        return dataList;
    }
}
