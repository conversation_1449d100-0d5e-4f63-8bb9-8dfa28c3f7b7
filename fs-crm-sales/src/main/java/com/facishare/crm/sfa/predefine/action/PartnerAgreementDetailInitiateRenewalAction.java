package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.enums.ReminderTrigger;
import com.facishare.crm.model.PartnerChannelManage;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.ChannelAgreementService;
import com.facishare.crm.sfa.predefine.service.ChannelTaskService;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.enums.AgreementStatus;
import com.facishare.crm.sfa.prm.core.service.TimeComputeService;
import com.facishare.crm.sfa.prm.platform.enums.TimeUnit;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.prm.platform.utils.I18NUtils;
import com.facishare.crm.sfa.service.ChannelService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.Year;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.constants.PrmI18NConstants.*;
import static com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel.AGREEMENT_STATUS;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PARAMET_ERERROR;

/**
 * Created by Sundy on 2024/10/21 16:26
 */
public class PartnerAgreementDetailInitiateRenewalAction extends AbstractStandardAction<PartnerAgreementDetailInitiateRenewalAction.Arg, PartnerAgreementDetailInitiateRenewalAction.Result> {
    private static final ChannelTaskService channelTaskService = SpringUtil.getContext().getBean(ChannelTaskService.class);
    private final ChannelService channelService = SpringUtil.getContext().getBean("channelServiceProvider", ChannelService.class);
    private final MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean(MetaDataFindServiceExt.class);
    private final ChannelAgreementService channelAgreementService = SpringUtil.getContext().getBean(ChannelAgreementService.class);
    private final TimeComputeService timeComputeService = SpringUtil.getContext().getBean(TimeComputeService.class);
    private String signSchemeId;
    private String belongDataId;
    private String admissionObject;
    private IObjectData belongData;


    private IObjectData objectData;

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.INITIATE_RENEWAL.getButtonApiName();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.INITIATE_RENEWAL.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Override
    protected Map<String, Object> getArgs() {
        return arg.getArgs();
    }

    @Override
    protected void before(PartnerAgreementDetailInitiateRenewalAction.Arg arg) {
        super.before(arg);
        if (StringUtils.isBlank(objectData.getOutTenantId())) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_OUTSIDE_COMPANY_EMPTY));
        }
        this.admissionObject = channelService.fetchChannelAdmissionObject(actionContext.getUser());
        this.belongDataId = channelService.getAdmissionObjectDataId(objectData, this.admissionObject);
        AgreementStatus originalAgreementStatus = AgreementStatus.find(DataUtils.getValue(objectData, AGREEMENT_STATUS, String.class, null), null);
        if (originalAgreementStatus != AgreementStatus.ACTIVE) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_AGREEMENT_STATUS_NOT_EQ_ACTIVATE));
        }
        this.belongData = metaDataFindServiceExt.findObjectByIdIgnoreAll(actionContext.getUser(), this.belongDataId, this.admissionObject);
        if (this.belongData == null) {
            String value = PartnerAgreementDetailModel.BELONG_ACCOUNT_ID;
            if (SFAPreDefineObject.Partner.getApiName().equals(admissionObject)) {
                value = PartnerAgreementDetailModel.BELONG_PARTNER_ID;
            }
            throw new ValidateException(I18N.text(PRM_CHANNEL_DATA_NOT_EXISTS, value));
        }
        PartnerChannelManage.SignScheme signScheme = channelService.matchSignSchemeDate(actionContext.getUser(), this.admissionObject, this.belongData);
        if (signScheme == null) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_SCHEME_NOT_MATCH, I18NUtils.getDataI18nName(this.belongData)));
        }
        if (ReminderTrigger.find(signScheme.getReminderTrigger()) != ReminderTrigger.MANUAL) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_UN_ALLOW_BY_NON_MANUAL));
        }
        this.signSchemeId = signScheme.getSignSchemeId();
        // 计算签约时间是否在时间窗口内
        Long endDate = DataUtils.getValue(objectData, "end_date", Long.class, null);
        if (endDate == null) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_UN_ALLOW_BY_END_DATE_IS_EMPTY));
        }
        Integer renewalWindowStart = signScheme.getRenewalWindowStart();
        Integer renewalWindowEnd = signScheme.getRenewalWindowEnd();
        String renewalWindowUnit = signScheme.getRenewalWindowUnit();
        long startWindowTime = timeComputeService.calculateTimestampByOffset(endDate, -renewalWindowStart, TimeUnit.find(renewalWindowUnit));
        long endWindowTime = timeComputeService.calculateTimestampByOffset(endDate, renewalWindowEnd, TimeUnit.find(renewalWindowUnit));
        int comparedTimeRange = timeComputeService.compareTimeRange(System.currentTimeMillis(), startWindowTime, endWindowTime);
        if (comparedTimeRange == -1) {
            int[] yearMonthDay = timeComputeService.getYearMonthDay(startWindowTime);
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_UN_ALLOW_BY_START_TIME, yearMonthDay[1], yearMonthDay[2]));
        } else if (comparedTimeRange == 1) {
            int[] yearMonthDay = timeComputeService.getYearMonthDay(endWindowTime);
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_UN_ALLOW_BY_END_TIME, yearMonthDay[1], yearMonthDay[2]));
        }
        // 计算当前签约是否已达到最大签约时长
        Integer planDuration = signScheme.getPlanDuration();
        String planDurationUnit = signScheme.getPlanDurationUnit();

        if (planDuration == null || StringUtils.isBlank(planDurationUnit)) {
            return;
        }
        String startDate = signScheme.getStartDate();
//        查第一条数据的开始年份
        int firstSignYear = findFirstSignYear(actionContext.getUser(), admissionObject, belongDataId);
        long firstSignTime = timeComputeService.getMidnightTimestamp(firstSignYear, startDate);
        long maxTimestamp = timeComputeService.calculateTimestampByOffset(firstSignTime, planDuration, TimeUnit.find(planDurationUnit));

        String renewalCycleUnit = signScheme.getRenewalCycleUnit();
        Integer renewalCycleTime = signScheme.getRenewalCycleTime();

        int nextSignYear = timeComputeService.getYearMonthDay(timeComputeService.calculateTimestampByOffset(endDate, 1, TimeUnit.DAY))[0];
        long nextTimestamp = timeComputeService.getMidnightTimestamp(nextSignYear, startDate);
        nextTimestamp = timeComputeService.calculateTimestampByOffset(nextTimestamp, renewalCycleTime, TimeUnit.find(renewalCycleUnit));
        if (nextTimestamp > maxTimestamp) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_SIGN_UN_ALLOW_BY_OVER_SIGN_PERIOD));
        }
    }

    private int findFirstSignYear(User user, String objectApiName, String dataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        if (SFAPreDefineObject.Account.getApiName().equals(objectApiName)) {
            SearchUtil.fillFilterEq(query.getFilters(), "belong_account_id", dataId);
        } else if (SFAPreDefineObject.Partner.getApiName().equals(objectApiName)) {
            SearchUtil.fillFilterEq(query.getFilters(), "belong_partner_id", dataId);
        } else {
            throw new ValidateException(I18N.text(SFA_PARAMET_ERERROR));
        }
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.CREATE_TIME, true)));
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, SFAPreDefineObject.PartnerAgreementDetail.getApiName(),
                query, Lists.newArrayList("_id", "start_date"));
        if (CollectionUtils.empty(dataList)) {
            log.warn("can not find first sign year, return current year, tenantId:{}", user.getTenantId());
            return Year.now().getValue();
        }
        Long startDate = DataUtils.getValue(dataList.get(0), "start_date", Long.class, null);
        if (startDate == null) {
            log.warn("can not find first sign year, return current year, tenantId:{}", user.getTenantId());
            return Year.now().getValue();
        }
        int[] yearMonthDay = timeComputeService.getYearMonthDay(startDate);
        return yearMonthDay[0];
    }


    @Override
    protected PartnerAgreementDetailInitiateRenewalAction.Result doAct(PartnerAgreementDetailInitiateRenewalAction.Arg arg) {
        IObjectData oldAgreementDetailData = metaDataFindServiceExt.findObjectData(actionContext.getUser(), arg.getObjectDataId(), SFAPreDefineObject.PartnerAgreementDetail.getApiName());
        IObjectData newAgreementDetailData = channelAgreementService.createAgreementDetailWhenInitiateRenewal(actionContext.getUser(), admissionObject, this.belongData, oldAgreementDetailData);
        channelTaskService.manualInitiateRenewal(actionContext.getUser(), this.belongDataId, this.signSchemeId, oldAgreementDetailData.getOutTenantId(), this.admissionObject);
        return PartnerAgreementDetailInitiateRenewalAction.Result.of(newAgreementDetailData);
    }

    @Data
    public static class Arg {
        private String objectDataId;
        private Map<String, Object> args;
    }

    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static PartnerAgreementDetailInitiateRenewalAction.Result of(IObjectData objectData) {
            PartnerAgreementDetailInitiateRenewalAction.Result result = new PartnerAgreementDetailInitiateRenewalAction.Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
