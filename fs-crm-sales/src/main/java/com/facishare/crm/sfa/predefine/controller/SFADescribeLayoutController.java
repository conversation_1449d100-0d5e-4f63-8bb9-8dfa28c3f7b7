package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.constant.PartnerConstants;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.DescribeDetailResult;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

/**
 * Created by zhaopx on 2017/11/14.
 */
@Slf4j
public class SFADescribeLayoutController extends StandardDescribeLayoutController {
    protected static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext()
            .getBean(BizConfigThreadLocalCacheService.class);

    protected FormComponent formComponent;
    protected static List<String> phoneNumberAttributionFields = Lists.newArrayList("phone_number_attribution", "phone_number_attribution_country",
            "phone_number_attribution_city", "phone_number_attribution_province", "phone_number_attribution_district",
            "phone_number_attribution_location", "phone_number_attribution_address");

    @Override
    protected Result after(Arg arg, Result result) {

        promptUpgrade(arg, result);
        handelDescribe(arg, result);

        if (arg.getInclude_layout()) {
            handleLayout(arg, result);
        }

        return super.after(arg, result);
    }

    protected void promptUpgrade(Arg arg, Result result) {
    }

    protected void handelDescribe(Arg arg, Result result) {
    }

    protected void handleLayout(Arg arg, Result result) {
        if (arg.getLayout_type() == null) {
            return;
        }

        User user = getControllerContext().getUser();
        if (CollectionUtils.empty(result.getLayout())) {
            return;
        }
        ILayout layout = new Layout(result.getLayout());

        try {
            if (CollectionUtils.empty(layout.getComponents())) {
                return;
            }
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        IObjectDescribe describe = result.getObjectDescribe().toObjectDescribe();
        LayoutExt layoutExt = LayoutExt.of(layout);
        layoutExt.getFormComponent().ifPresent(x->formComponent = (FormComponent) x.getFormComponent());
        switch (arg.getLayout_type()) {
            case LAYOUT_TYPE_EDIT:
                PreDefLayoutUtil.specialDealAccountObjAccountName(describe.getApiName(), this.serviceFacade, formComponent,
                        user.getTenantId());
                PreDefLayoutUtil.removeSpecialFieldNameFromFormComponent(formComponent, describe,
                        LAYOUT_TYPE_EDIT);
                PreDefLayoutUtil.removeSpecialFieldsFromDetailObjectList(result.getDetailObjectList(),
                        LAYOUT_TYPE_EDIT);
                //合作伙伴和外部负责人编辑布局中只读
                PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, Lists.newArrayList(PartnerConstants.PARTNER_ID));
                //移除外部来源、外部企业字段
                PreDefLayoutUtil.removeSomeFields(formComponent, Sets.newHashSet(PartnerConstants.OUT_RESOURCES, DBRecord.OUT_TENANT_ID, DBRecord.OUT_OWNER));
                if (Lists.newArrayList(SFAPreDefineObject.Account.getApiName(), SFAPreDefineObject.Leads.getApiName(),
                        SFAPreDefineObject.Contact.getApiName()).contains(describe.getApiName())) {
                    //电话归属地信息编辑布局中只读
                    PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, phoneNumberAttributionFields);
                }
                break;
            case LAYOUT_TYPE_ADD:
                PreDefLayoutUtil.removeSpecialFieldNameFromFormComponent(formComponent, describe,
                        LAYOUT_TYPE_ADD);
                PreDefLayoutUtil.removeSpecialFieldsFromDetailObjectList(result.getDetailObjectList(),
                        LAYOUT_TYPE_ADD);
                //移除外部来源、外部企业字段
                HashSet<String> removeField = Sets.newHashSet(PartnerConstants.OUT_RESOURCES, DBRecord.OUT_TENANT_ID);
                if ("OpportunityObj".equals(describe.getApiName())) {
                    removeField.add(DBRecord.OUT_OWNER);
                }
                PreDefLayoutUtil.removeSomeFields(formComponent, removeField);
                if (Lists.newArrayList(SFAPreDefineObject.Account.getApiName(), SFAPreDefineObject.Leads.getApiName(),
                        SFAPreDefineObject.Contact.getApiName()).contains(describe.getApiName())) {
                    //电话归属地信息编辑布局中只读
                    PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, phoneNumberAttributionFields);
                }
                break;
            default:
                break;
        }
        PreDefLayoutUtil.removeEmptySection(formComponent);
    }

    protected void handleReadOnlyFieldsForDetailLayout(Result result, String fieldName) {
        if (result == null) {
            return;
        }
        List<DetailObjectListResult> detailObjectListResultList = result.getDetailObjectList();
        if (CollectionUtils.empty(detailObjectListResultList)) {
            return;
        }
        Optional<DetailObjectListResult> detailObjectListResultOptional = detailObjectListResultList.stream()
                .filter(it -> fieldName.equals(it.getFieldApiName()))
                .findFirst();
        if (!detailObjectListResultOptional.isPresent()) {
            return;
        }
        Map detailDescribeMap = detailObjectListResultOptional.get().getObjectDescribe();
        if (CollectionUtils.empty(detailDescribeMap)) {
            return;
        }
        IObjectDescribe detailDescribe = ObjectDescribeDocument.of(detailDescribeMap).toObjectDescribe();
        if (detailDescribe == null) {
            return;
        }
        List<String> readOnlyFields = getReadOnlyFields(detailDescribe);
        if (CollectionUtils.empty(readOnlyFields)) {
            return;
        }
        List<RecordTypeLayoutStructure> detailLayoutList = detailObjectListResultOptional.get().getLayoutList();
        if (CollectionUtils.notEmpty(detailLayoutList)) {
            ILayout detailLayout;
            LayoutExt layoutExt;
            for (RecordTypeLayoutStructure recordTypeLayoutStructure : detailLayoutList) {
                detailLayout = new Layout(recordTypeLayoutStructure.getDetail_layout());
                layoutExt = LayoutExt.of(detailLayout);
                if (Objects.nonNull(layoutExt)) {
                    layoutExt.getFormComponent().ifPresent(x->PreDefLayoutUtil.setFormComponentFieldReadOnly((FormComponent) x.getFormComponent(), readOnlyFields));
                }
            }
        }
    }

    protected void handleReadOnlyFields(Result result) {
        IObjectDescribe describe = result.getObjectDescribe().toObjectDescribe();
        List<String> readOnlyFields = getReadOnlyFields(describe);
        if (CollectionUtils.notEmpty(readOnlyFields)) {
            PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, readOnlyFields);
        }
    }

    protected List<String> getReadOnlyFields(IObjectDescribe detailDescribe) {
        List<String> readOnlyFields = Lists.newArrayList();
        //强制执行价目表优先级时价目表、价目表明细只读
        if (bizConfigThreadLocalCacheService.isEnforcePriority(controllerContext.getTenantId())) {
            readOnlyFields.add("price_book_id");
            readOnlyFields.add("price_book_product_id");
        }
        return readOnlyFields;
    }

    protected List<String> getReadOnlyFieldsForQuote(IObjectDescribe objectDescribe) {
        List<String> readOnlyFields = Lists.newArrayList();
        IFieldDescribe extraField = objectDescribe.getFieldDescribe(QuoteConstants.QuoteLinesField.EXTRA_DISCOUNT.getApiName());
        if (extraField != null) {
            //readOnlyFields.add(QuoteConstants.QuoteLinesField.PRICE.getApiName());
            readOnlyFields.add(QuoteConstants.QuoteLinesField.DISCOUNT.getApiName());
            readOnlyFields.add(QuoteConstants.QuoteLinesField.TOTAL_AMOUNT.getApiName());
            readOnlyFields.add(QuoteConstants.QuoteLinesField.SYSTEM_DISCOUNT_AMOUNT.getApiName());
            readOnlyFields.add(QuoteConstants.QuoteLinesField.TOTAL_DISCOUNT_AMOUNT.getApiName());
            if (Integer.parseInt(controllerContext.getTenantId()) > 683320) {
                readOnlyFields.add(QuoteConstants.QuoteLinesField.SALES_PRICE.getApiName());
            }
        }
        if (bizConfigThreadLocalCacheService.isMultipleUnit(controllerContext.getTenantId())) {
            readOnlyFields.add(QuoteConstants.QuoteLinesField.BASE_UNIT_COUNT.getApiName());
            readOnlyFields.add(QuoteConstants.QuoteLinesField.STAT_UNIT_COUNT.getApiName());
            readOnlyFields.add(QuoteConstants.QuoteLinesField.CONVERSION_RATIO.getApiName());
        }
        return readOnlyFields;
    }

    protected boolean isFlowEditLayout() {
        return !Strings.isNullOrEmpty(arg.getLayoutApiName()) && LAYOUT_TYPE_EDIT.equals(arg.getLayout_type())
                && GrayUtil.supportFlowLayout(controllerContext.getTenantId());
    }

    protected void removeDetailButtons(Result result, String describeName) {
        if (null == result || CollectionUtils.empty(result.getDetailObjectList())) {
            return;
        }
        result.getDetailObjectList().forEach(r -> {
            if (describeName.equals(r.getObjectApiName()) && CollectionUtils.notEmpty(r.getLayoutList())) {
                r.getLayoutList().forEach(x -> {
                    ILayout detailLayout = new Layout(x.getDetail_layout());
                    detailLayout.setButtons(Lists.newArrayList());
                    x.setSingleButtons(Lists.newArrayList());
                    x.setBatchButtons(Lists.newArrayList());
                });
            }
        });
    }

    protected List<String> getAllPresetFields(IObjectDescribe objectDescribe) {
        List<String> fields = Lists.newArrayList();
        objectDescribe.getFieldDescribes().forEach(f -> {
            if (!f.getApiName().endsWith("__c")) {
                fields.add(f.getApiName());
            }
        });
        return fields;
    }

    @Override
    protected IObjectData calculateExpression(DescribeDetailResult describeDetailResult) {
        IObjectData data = super.calculateExpression(describeDetailResult);
        if (LayoutExt.Add_LAYOUT_TYPE.equals(arg.getLayout_type()) && Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.LEADS_API_NAME).contains(describe.getApiName())) {
            Optional<IFieldDescribe> ownerField = ObjectDescribeExt.of(describeDetailResult.getObjectDescribe()).getOwnerField();
            ownerField.ifPresent(m -> {
                if (m.get("wheres") == null || CollectionUtils.empty((List) m.get("wheres"))) {
                    ObjectDataExt.of(data).setDataOwner(controllerContext.getUser());
                }
            });
        }
        return data;
    }

    protected void dealOrderSettlement(Result result,List<String> settleFilterFields,String apiName) {
        if (Objects.isNull(result.getLayout())) {
            return;
        }
        Layout layout = new Layout(result.getLayout());
        try {
            List<IComponent> components = layout.getComponents();
            if (CollectionUtils.empty(components)) {
                return;
            }
            for (IComponent component : components) {
                if (!Objects.equals(component.getName(), "order_settlement")) {
                    continue;
                }
                Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), apiName);
                List<String> listHeader = (List<String>) component.get("list_header");
                if (CollectionUtils.notEmpty(unauthorizedFields)) {
                    listHeader.removeIf(unauthorizedFields::contains);
                }
                IObjectDescribe describe = result.getObjectDescribe().toObjectDescribe();
                Map<String, IFieldDescribe> fieldDescribeMap = describe.getFieldDescribeMap();
                listHeader.removeIf(x->{
                    if (settleFilterFields.contains(x)) {
                        return false;
                    }
                    IFieldDescribe iFieldDescribe = fieldDescribeMap.get(x);
                    return iFieldDescribe == null || !iFieldDescribe.isActive();
                });
            }
        } catch (Exception e) {
            log.warn("order_settlement ", e);
        }
    }

    protected void addNonProductButton(Result result,String apiName) {
        if (CollectionUtils.empty(result.getDetailObjectList()) || null == result.getLayout() || !bizConfigThreadLocalCacheService.isOpenNonStandardProduct(controllerContext.getTenantId())) {
            return;
        }
        ILayout ilayout = new Layout(result.getLayout());
        List<IComponent> components = null;
        try {
            components = ilayout.getComponents();
        } catch (MetadataServiceException e) {
            log.warn("addNonProductButton", e);
        }
        if (CollectionUtils.empty(components)) {
            return;
        }

        for (IComponent component : components) {
            if ((apiName+"_md_group_component").equals(component.getName())) {
                List<Map> buttonInfos = (List<Map>) component.get("button_info");
                for (Map buttonInfo : buttonInfos) {
                    if (("list_normal").equals(String.valueOf((buttonInfo).get("render_type")))) {
                        if (((List) (buttonInfo).get("hidden")).contains(ObjectAction.ADD_NON_STANDARD_PRODUCT.getButtonApiName())) {
                            // 布局隐藏了按钮
                            return;
                        }
                    }
                }
                break;
            }
        }

        for (DetailObjectListResult detailObject : result.getDetailObjectList()) {
            if (Objects.equals(apiName,detailObject.getObjectApiName())) {
                List<RecordTypeLayoutStructure> layouts = detailObject.getLayoutList();
                if (!org.springframework.util.CollectionUtils.isEmpty(layouts)) {
                    for (RecordTypeLayoutStructure layout : layouts) {
                        List buttons = (List) layout.getDetail_layout().get("buttons");
                        if (!org.springframework.util.CollectionUtils.isEmpty(buttons)) {
                            IButton button = ButtonUtils.buildButton(ObjectAction.ADD_NON_STANDARD_PRODUCT);
                            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(button);
                            buttons.add(jsonObject.get("containerDocument"));
                        }
                    }
                }
                break;
            }

        }
    }
}
