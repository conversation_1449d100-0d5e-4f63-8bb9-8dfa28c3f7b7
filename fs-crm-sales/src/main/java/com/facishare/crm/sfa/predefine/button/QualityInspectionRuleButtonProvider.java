package com.facishare.crm.sfa.predefine.button;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QualityInspectionRuleButtonProvider extends AbstractSfaSpecialButtonProvider {

    @Override
    public String getApiName() {
        return SFAPreDefineObject.QualityInspectionRule.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.UPDATE));
        buttons.add(ButtonUtils.buildButton(ObjectAction.DELETE));
        buttons.add(ButtonUtils.buildButton(ObjectAction.SYNC_QW));
        return buttons;
    }
}
