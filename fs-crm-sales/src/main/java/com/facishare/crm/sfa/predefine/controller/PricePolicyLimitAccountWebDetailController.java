package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;

public class PricePolicyLimitAccountWebDetailController extends SFAWebDetailController {
    private final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest()) {
            availableRangeUtils.removeMobileButton(layout);
        }
        return layout;
    }
}
