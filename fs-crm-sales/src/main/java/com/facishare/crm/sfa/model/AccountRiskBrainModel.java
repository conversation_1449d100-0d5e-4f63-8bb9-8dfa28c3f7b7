package com.facishare.crm.sfa.model;

import lombok.Data;

public interface AccountRiskBrainModel {

    @Data
    class Amount {
        /**
         * 总额度
         */
        private Integer totalAmount;
        /**
         * 已用额度
         */
        private Integer usedAmount;
        /**
         * 到期时间
         */
        private String expirationTime;
    }


    @Data
    class EnablePorTraitResult {
        /**
         * 开启画像是否成功
         */
        private Boolean deductResult;
        /**
         * 成功/失败 描述
         */
        private String resultMsg;
        /**
         * 开启成功使用的订单id
         */
        private String orderId;
        /**
         * 订单过期时间
         */
        private Long expiryTime;
    }


    @Data
    class Arg {
        private String ucCode;
        private Long creditRuleId;
    }

    @Data
    class Result {
        private Boolean success;
        private String message;

        public Result() {
            success = true;
        }
    }
}
