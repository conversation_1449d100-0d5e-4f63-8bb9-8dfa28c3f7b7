package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.Set;

public class QualityInspectionRuleWebDetailController extends StandardWebDetailController {

    private static final QualityInspectionRuleService qualityInspectionRuleService = SpringUtil.getContext().getBean(QualityInspectionRuleService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ObjectDataDocument dataDocument = result.getData();
        qualityInspectionRuleService.rewriteDoc(controllerContext, Lists.newArrayList(dataDocument.getId()), dataDocument);
        removeComponentField(result);
        return result;
    }

    private final Set<String> specialFieldNameToRemoveSet = Sets.newHashSet("monitors", "msg_user");

    private void removeComponentField(Result result) {
        LayoutExt layoutExt = LayoutExt.of(result.getLayout());
        layoutExt.removeFields(specialFieldNameToRemoveSet);
    }
}