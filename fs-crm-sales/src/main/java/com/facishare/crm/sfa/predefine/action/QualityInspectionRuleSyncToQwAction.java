package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.util.SpringUtil;

public class QualityInspectionRuleSyncToQwAction extends AbstractSimpleStandardAction {

    private static final QualityInspectionRuleService qualityInspectionRuleService = SpringUtil.getContext().getBean(QualityInspectionRuleService.class);

    @Override
    protected ObjectAction getButton() {
        return ObjectAction.SYNC_QW;
    }

    @Override
    protected Result doAct(Arg arg) {
        qualityInspectionRuleService.syncRule(actionContext.getUser(), objectData);
        return new Result();
    }
}
