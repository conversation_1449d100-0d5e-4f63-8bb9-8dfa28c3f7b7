package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.config.CouponProductConditionConfig;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.translate.TranslateManager;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

public class CouponPlanWebDetailController extends SFAWebDetailController {
    private final CouponProductConditionConfig couponProductConditionConfig = SpringUtil.getContext().getBean(CouponProductConditionConfig.class);
    private static final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    private final TranslateManager translateManager = SpringUtil.getContext().getBean(TranslateManager.class);
    private static final BizConfigThreadLocalCacheService config = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        setSelectOption(result);
        Optional.ofNullable(result).map(Result::getLayout)
                .ifPresent(x -> {
                    ILayout layout = new Layout(x);
                    WebDetailLayout of = WebDetailLayout.of(layout);
                    //移除打印按钮
                    of.removeButtonsByActionCode(Lists.newArrayList(ObjectAction.PRINT.getActionCode()));
                });
        render(result);
        CouponUtils.issueConditionTab(result);
        defaultCouponType(result);
        return result;
    }


    private void defaultCouponType(Result result) {
        if (!config.isOpenPaperCoupon(controllerContext.getTenantId())) {
            return;
        }
        Optional.ofNullable(result)
                .map(Result::getData)
                .map(ObjectDataDocument::toObjectData)
                .ifPresent(objectData ->{
                    String couponType = objectData.get(CouponConstants.CouponPlanField.COUPON_TYPE.getApiName(), String.class);
                    if(StringUtils.isBlank(couponType)){
                        objectData.set(CouponConstants.CouponPlanField.COUPON_TYPE.getApiName(),CouponConstants.CouponType.ELECTRONIC.getValue());
                    }
                });
    }
    private void render(Result result) {
        Optional.ofNullable(result)
                .map(Result::getData)
                .map(ObjectDataDocument::toObjectData)
                .ifPresent(data -> {
                    translateManager.getTranslateService(data.get(CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName(), String.class))
                            .render(data, controllerContext.getUser());
                });
    }

    private void setSelectOption(Result result) {
        Optional<List<SelectOption>> selectOptionOpt = couponProductConditionConfig.getSelectOption(controllerContext.getTenantId());
        if (!selectOptionOpt.isPresent()) {
            return;
        }
        SelectOneFieldDescribe typeSelectOneFieldDescribe = (SelectOneFieldDescribe) result.copyFieldToDescribeExt(CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName());
        if(typeSelectOneFieldDescribe != null){
            for (SelectOption selectOption : selectOptionOpt.get()) {
                typeSelectOneFieldDescribe.addSelectOption(selectOption);
            }
        }
    }

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (RequestUtil.isMobileRequest() ||RequestUtil.isMobileDeviceRequest()|| RequestUtil.isH5Request()) {
            availableRangeUtils.removeMobileButton(layout);
        }
        return layout;
    }


}
