package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.platform.utils.RequestSourceResolver;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 去续约
 */
public class PartnerAgreementDetailRenewAction extends AbstractStandardAction<PartnerAgreementDetailRenewAction.Arg, PartnerAgreementDetailRenewAction.Result> {
    private final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private final RequestSourceResolver requestSourceResolver = SpringUtil.getContext().getBean("requestSourceResolver", RequestSourceResolver.class);


    protected IObjectData objectData;

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.RENEW.getButtonApiName();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.RENEW.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Override
    protected Map<String, Object> getArgs() {
        return arg.getArgs();
    }

    @Override
    protected Result doAct(Arg arg) {
        if (!requestSourceResolver.isErAppRequest()) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NOT_SUPPORT_ACTION));
        }
        objectData.set("sign_status", SignStatus.PENDING_RENEWAL.getStatus());
        Optional.ofNullable(arg)
                .map(Arg::getSubmitAgreementItem)
                .map(SubmitAgreementItem::getValueMapping)
                .orElse(new HashMap<>())
                .forEach((k, v) -> objectData.set(k, v));
        BaseObjectSaveAction.Arg tempArg = new BaseObjectSaveAction.Arg();
        tempArg.setSkipApprovalFlow(false);
        tempArg.setObjectData(ObjectDataDocument.of(objectData));

        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setIsDuplicateSearch(true);
        optionInfo.setUseValidationRule(true);
        optionInfo.setSkipFuncValidate(true);
        tempArg.setOptionInfo(optionInfo);
        ActionContext tempAction = new ActionContext(RequestContextManager.getContext(), SFAPreDefineObject.PartnerAgreementDetail.getApiName(),
                ObjectAction.UPDATE.getActionCode());
        BaseObjectSaveAction.Result result = serviceFacade.triggerAction(tempAction, tempArg, BaseObjectSaveAction.Result.class);
        return PartnerAgreementDetailRenewAction.Result.of(result.getObjectData().toObjectData());
    }

    @Data
    public static class Arg {
        private String objectDataId;
        private SubmitAgreementItem submitAgreementItem;
        private Map<String, Object> args;
    }

    @Data
    public static class SubmitAgreementItem {
        private String agreementId;
        private String signSchemeId;
        private Map<String, Object> valueMapping;
    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static PartnerAgreementDetailRenewAction.Result of(IObjectData objectData) {
            PartnerAgreementDetailRenewAction.Result result = new PartnerAgreementDetailRenewAction.Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
