package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.platform.utils.RequestSourceResolver;
import com.facishare.crm.sfa.predefine.service.LayoutButtonService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * Created by Sundy on 2024/11/13 16:38
 */
public class PartnerAgreementDetailListHeaderController extends StandardListHeaderController {
    private final LayoutButtonService layoutButtonService = SpringUtil.getContext().getBean(LayoutButtonService.class);
    private final RequestSourceResolver requestSourceResolver = SpringUtil.getContext().getBean("requestSourceResolver", RequestSourceResolver.class);


    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        if (requestSourceResolver.isErAppRequest()) {
            layoutButtonService.removeListPageButton(result, ObjectAction.INVALID.getButtonApiName());
        }
        layoutButtonService.removeListHeaderButton(result, ObjectAction.CREATE);
        layoutButtonService.removeListHeaderButton(result, ObjectAction.BATCH_IMPORT);
        return after;
    }
}
