package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportVerifyAction;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/7 3:44 下午
 * @illustration
 */
public class BomUnionInsertImportVerifyAction extends StandardUnionInsertImportVerifyAction {

    private final List<String> filterFields = Lists.newArrayList("parent_bom_id","bom_path","root_id","record_type");


    @Override
    protected void doFunPrivilegeCheck() {
        return;
    }

    @Override
    protected void doDataPrivilegeCheck() {
        return;
    }

    @Override
    protected List<IFieldDescribe> customHandleFields(List<IFieldDescribe> fieldDescribeList) {
        List<IFieldDescribe> fieldDescribes = super.customHandleFields(fieldDescribeList);
        fieldDescribes.removeIf(field-> filterFields.contains(field.getApiName()));
        return fieldDescribes;
    }

    @Override
    protected void supportRelatedMark(List<IFieldDescribe> fieldDescribeList) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("label", I18N.text(BomI18NKeyUtil.SFA_BOM_PATH));
        map.put("api_name", BomConstants.FIELD_SFA_RELATED_PATH_MARK);
        IFieldDescribe field = ImportExportExt.createField(map, "RELATED_MARK", true);
        fieldDescribeList.add(0, field);
    }

    @Override
    protected void supportUniqueID(List<IFieldDescribe> fieldDescribeList) {
        return;
    }

    @Override
    protected void customDetailRemoveField(List<IFieldDescribe> fieldDescribeList) {
        super.customDetailRemoveField(fieldDescribeList);
        fieldDescribeList.removeIf(field-> filterFields.contains(field.getApiName()));
    }
}
