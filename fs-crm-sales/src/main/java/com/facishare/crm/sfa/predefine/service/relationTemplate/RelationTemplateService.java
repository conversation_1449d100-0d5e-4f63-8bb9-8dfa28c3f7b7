package com.facishare.crm.sfa.predefine.service.relationTemplate;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.RelationTemplateModels;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.ObjectUtils;
import com.facishare.crm.sfa.utilities.util.RelationTemplateUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lik
 * @date : 2024/7/8 14:46
 */
@ServiceModule("relationTemplateService")
@Component
@Slf4j
public class RelationTemplateService {

    @Autowired
    private ServiceFacade serviceFacade;

    @ServiceMethod("check_priority")
    public RelationTemplateModels.Result checkPriority(ServiceContext context, RelationTemplateModels.Arg arg){
        RelationTemplateModels.Result result = new RelationTemplateModels.Result();
        result.setRepeatFlag(false);
        try {
            RelationTemplateUtil.handleIsHaveRepeatPriority(context.getUser(), AccountUtil.getIntegerValue(arg.getData(),"priority",null),arg.getData().getId(),arg.getData().get(IObjectData.RECORD_TYPE).toString());
        }catch (Exception e){
            log.info("RelationTemplateService checkPriority e:",e);
            result.setRepeatFlag(true);
            result.setMsg(e.getMessage());
        }
        result.setSuccess(true);
        return result;
    }

    @ServiceMethod("get_tree_module_config")
    public RelationTemplateModels.Result getTreeModuleConfig(ServiceContext context, RelationTemplateModels.Arg arg){
        RelationTemplateModels.Result result = new RelationTemplateModels.Result();
        ILayout layout = serviceFacade.getLayoutLogicService().findLayoutByApiName(context.getUser(),"layout_detail_department__c",SFAPreDefineObject.RelationTemplate.getApiName());
        if(ObjectUtils.allIsEmpty(layout)){
            log.warn("getTreeModuleConfig layout  is null");
            return result;
        }
        LayoutExt layoutExt = LayoutExt.of(layout);
        try {
            if(ObjectUtils.allIsEmpty(layoutExt) || CollectionUtils.empty(layoutExt.getComponents())){
                return result;
            }
            layoutExt.getComponents().stream().forEach(x->{
                if("relationship_tree_structure".equals(x.getType()) && ObjectUtils.allNotEmpty(x.get("fieldsConfig"))){
                    result.setFieldsConfig((List<Map>) x.get("fieldsConfig"));
                }
            });
        }catch (Exception e){
            log.warn("getTreeModuleConfig error e:",e);
        }
        return result;
    }



}
