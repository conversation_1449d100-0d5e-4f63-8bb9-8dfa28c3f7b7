package com.facishare.crm.sfa.predefine.service.manager;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.enums.CollectionTypeEnum;
import com.facishare.crm.sfa.utilities.constant.ArI18NKey;
import com.facishare.crm.sfa.utilities.constant.CommonOrderPaymentConstants;
import com.facishare.crm.sfa.utilities.constant.PaymentObjConstants;
import com.facishare.crm.sfa.utilities.util.SFABizLogUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ArPaymentManagerNew {

    @Resource
    private CommonDescribeManagerNew commonDescribeManagerNew;

    @Resource
    private CommonLayoutManagerNew commonLayoutManagerNew;

    @Resource
    private CommonDetailLayOutManagerNew commonDetailLayoutManagerNew;

    @Resource
    private ArFieldDescribeGenerateManagerNew arFieldDescribeGenerateManagerNew;

    @Resource
    private ArFormFieldManagerNew arFormFieldManagerNew;

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    protected IObjectDescribeService objectDescribeService;

    /**
     * 开启应收，回款加字段：销售订单、已核销金额、待核销金额、核销状态
     * 回款明细加退货单字段，修改订单非必填
     */
    public void addFieldForOpenAccountsReceivable(User user, Map<String, IObjectDescribe> describeMap) {
        //1、查询信息
        //查"回款"的describe
        IObjectDescribe objectDescribe = describeMap.get(Utils.CUSTOMER_PAYMENT_API_NAME);
        IObjectDescribe detailDescribe = describeMap.get(Utils.ORDER_PAYMENT_API_NAME);

        //2、describe+layout：添加字段（添加之前，判断是否已存在对应的字段）
        ILayout defaultlLayout = serviceFacade.getLayoutLogicService().findDefaultLayout(user, SystemConstants.LayoutType.Detail.layoutType, Utils.CUSTOMER_PAYMENT_API_NAME);
        ILayout detailLayout = serviceFacade.getLayoutLogicService().findDefaultLayout(user, SystemConstants.LayoutType.Detail.layoutType, Utils.ORDER_PAYMENT_API_NAME);

        if (defaultlLayout == null) {
            log.warn("defaultLayout:{}", defaultlLayout);
            throw new ValidateException(I18N.text(ArI18NKey.PAYMENT_OBJECT_DEFAULT_LAYOUT_NOT_EXIST));
        }

        List<String> addFieldApiNames = Lists.newArrayList(PaymentObjConstants.Field.SalesOrderId.apiName
                , PaymentObjConstants.Field.MatchAmount.apiName, PaymentObjConstants.Field.NoMatchAmount.apiName
                , PaymentObjConstants.Field.MatchStatus.apiName, PaymentObjConstants.Field.OpeningBalance.apiName
                , PaymentObjConstants.Field.ContactObject.apiName, PaymentObjConstants.Field.CollectionType.apiName);

        List<String> addDetailFieldApiNames = Lists.newArrayList(PaymentObjConstants.DetailField.ReturnedGoodsInvoiceId.apiName);

        //2.1、"回款"describe 添加"销售订单、已核销金额、待核销金额、核销状态"字段
        Map<String, IFieldDescribe> fieldApiName2FieldDescribe = new HashMap<>();
        fieldApiName2FieldDescribe.put(PaymentObjConstants.Field.SalesOrderId.apiName, arFieldDescribeGenerateManagerNew.getPaymentObjFieldDescribe(PaymentObjConstants.Field.SalesOrderId.apiName));
        fieldApiName2FieldDescribe.put(PaymentObjConstants.Field.MatchAmount.apiName, arFieldDescribeGenerateManagerNew.getPaymentObjFieldDescribe(PaymentObjConstants.Field.MatchAmount.apiName));
        fieldApiName2FieldDescribe.put(PaymentObjConstants.Field.NoMatchAmount.apiName, arFieldDescribeGenerateManagerNew.getPaymentObjFieldDescribe(PaymentObjConstants.Field.NoMatchAmount.apiName));
        fieldApiName2FieldDescribe.put(PaymentObjConstants.Field.MatchStatus.apiName, arFieldDescribeGenerateManagerNew.getPaymentObjFieldDescribe(PaymentObjConstants.Field.MatchStatus.apiName));
        fieldApiName2FieldDescribe.put(PaymentObjConstants.Field.OpeningBalance.apiName, arFieldDescribeGenerateManagerNew.getPaymentObjFieldDescribe(PaymentObjConstants.Field.OpeningBalance.apiName));
        fieldApiName2FieldDescribe.put(PaymentObjConstants.Field.ContactObject.apiName, arFieldDescribeGenerateManagerNew.getPaymentObjFieldDescribe(PaymentObjConstants.Field.ContactObject.apiName));
        fieldApiName2FieldDescribe.put(PaymentObjConstants.Field.CollectionType.apiName, arFieldDescribeGenerateManagerNew.getPaymentObjFieldDescribe(PaymentObjConstants.Field.CollectionType.apiName));
        commonDescribeManagerNew.addFieldDescribes(user.getTenantId(), Utils.CUSTOMER_PAYMENT_API_NAME, objectDescribe, addFieldApiNames, fieldApiName2FieldDescribe);

        Map<String, IFieldDescribe> detailFieldApiName2FieldDescribe = new HashMap<>();
        detailFieldApiName2FieldDescribe.put(PaymentObjConstants.DetailField.ReturnedGoodsInvoiceId.apiName, arFieldDescribeGenerateManagerNew.getOrderPaymentObjFieldDescribe(PaymentObjConstants.DetailField.ReturnedGoodsInvoiceId.apiName));
        commonDescribeManagerNew.addFieldDescribes(user.getTenantId(), Utils.ORDER_PAYMENT_API_NAME, detailDescribe, addDetailFieldApiNames, detailFieldApiName2FieldDescribe);

        //2.2、"回款"detailLayout 添加"销售订单、已核销金额、待核销金额、核销状态"字段
        Map<String, IFormField> fieldApiName2IFormField = new HashMap<>();
        fieldApiName2IFormField.put(PaymentObjConstants.Field.SalesOrderId.apiName, arFormFieldManagerNew.getPaymentFormField(PaymentObjConstants.Field.SalesOrderId.apiName));
        fieldApiName2IFormField.put(PaymentObjConstants.Field.MatchAmount.apiName, arFormFieldManagerNew.getPaymentFormField(PaymentObjConstants.Field.MatchAmount.apiName));
        fieldApiName2IFormField.put(PaymentObjConstants.Field.NoMatchAmount.apiName, arFormFieldManagerNew.getPaymentFormField(PaymentObjConstants.Field.NoMatchAmount.apiName));
        fieldApiName2IFormField.put(PaymentObjConstants.Field.MatchStatus.apiName, arFormFieldManagerNew.getPaymentFormField(PaymentObjConstants.Field.MatchStatus.apiName));
        fieldApiName2IFormField.put(PaymentObjConstants.Field.OpeningBalance.apiName, arFormFieldManagerNew.getPaymentFormField(PaymentObjConstants.Field.OpeningBalance.apiName));
        fieldApiName2IFormField.put(PaymentObjConstants.Field.ContactObject.apiName, arFormFieldManagerNew.getPaymentFormField(PaymentObjConstants.Field.ContactObject.apiName));
        fieldApiName2IFormField.put(PaymentObjConstants.Field.CollectionType.apiName, arFormFieldManagerNew.getPaymentFormField(PaymentObjConstants.Field.CollectionType.apiName));
        commonDetailLayoutManagerNew.addFields(user, defaultlLayout, addFieldApiNames, fieldApiName2IFormField);

        Map<String, IFormField> detailFieldApiName2IFormField = new HashMap<>();
        fieldApiName2IFormField.put(PaymentObjConstants.DetailField.ReturnedGoodsInvoiceId.apiName, arFormFieldManagerNew.getOrderPaymentFormField(PaymentObjConstants.DetailField.ReturnedGoodsInvoiceId.apiName));
        commonDetailLayoutManagerNew.addFields(user, detailLayout, addDetailFieldApiNames, detailFieldApiName2IFormField);
    }

    /**
     * 新建回款，回款明细不用新建了
     * .isCreateWhenMasterCreate(false)
     * .isRequiredWhenMasterCreate(false)
     */
    public void updateOrderPaymentDescribe(User user, Map<String, IObjectDescribe> describeMap) {
        IObjectDescribe orderPaymentDescribe = describeMap.get(Utils.ORDER_PAYMENT_API_NAME);
        ObjectDescribeExt orderPaymentDescribeExt = ObjectDescribeExt.of(orderPaymentDescribe);

        MasterDetailFieldDescribe paymentIdFieldDescribe = (MasterDetailFieldDescribe) orderPaymentDescribeExt.getFieldDescribe(CommonOrderPaymentConstants.Field.PaymentId.apiName);
        ObjectReferenceFieldDescribe orderIdFieldDescribe = (ObjectReferenceFieldDescribe) orderPaymentDescribeExt.getFieldDescribe(CommonOrderPaymentConstants.Field.OrderId.apiName);

        paymentIdFieldDescribe.setIsRequiredWhenMasterCreate(false);
        orderIdFieldDescribe.setRequired(false);

        try {
            objectDescribeService.updateFieldDescribe(orderPaymentDescribe, Lists.newArrayList(paymentIdFieldDescribe, orderIdFieldDescribe));
            commonLayoutManagerNew.updateLayoutFieldRequired(Utils.ORDER_PAYMENT_API_NAME
                    , user.getTenantId(), Lists.newArrayList(CommonOrderPaymentConstants.Field.OrderId.apiName), false);
        } catch (MetadataServiceException e) {
            log.error(e.getMessage());
        }
    }

    public void refreshCollectionType(String tenantId) {
        User user = User.systemUser(tenantId);
        SearchTemplateQuery query = getTemplateQuery();
        List<String> apiNames = Lists.newArrayList(Utils.CUSTOMER_PAYMENT_API_NAME);
        apiNames.forEach(apiName -> {
            int loopTimes = 0;
            while (true) {
                loopTimes++;
                if (loopTimes > 1000) {
                    log.warn("refreshCollectionType loop limit, limit:{}, tenantId:{}", loopTimes, tenantId);
                    SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                            .action("sfa_loop_limit")
                            .objectApiNames(Utils.CUSTOMER_PAYMENT_API_NAME)
                            .message("refreshCollectionType")
                            .build(), User.systemUser(tenantId));
                    break;
                }
                Integer number = updateDatas(apiName, user, query);
                if (number == 0) {
                    break;
                }
            }
        });
    }


    private Integer updateDatas(String apiName, User user, SearchTemplateQuery query) {
        QueryResult<IObjectData> result = serviceFacade.findBySearchQueryIgnoreAll(user, apiName, query);
        List<IObjectData> datas = result.getData();
        serviceFacade.bulkUpdateObjectDataOneField(PaymentObjConstants.Field.CollectionType.apiName
                , datas, CollectionTypeEnum.Blue.getValue(), user);
        if (datas.size() < 1000) {
            return 0;
        }
        return 1;
    }

    private SearchTemplateQuery getTemplateQuery() {
        List<IFilter> filters = Lists.newArrayList();

        IFilter filter = new Filter();
        filter.setFieldName(PaymentObjConstants.Field.CollectionType.apiName);
        filter.setOperator(Operator.IS);
        filter.setFieldValues(null);
        filters.add(filter);

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);
        //0不走权限  1走权限
        searchTemplateQuery.setPermissionType(0);

        return searchTemplateQuery;
    }
}