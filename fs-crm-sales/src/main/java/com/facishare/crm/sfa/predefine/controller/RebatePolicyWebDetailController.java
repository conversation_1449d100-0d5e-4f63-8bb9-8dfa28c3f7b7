package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.RebatePolicyConstants;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.DescribeUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.RebateUtils;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.WebDetailUtils;
import com.facishare.crm.sfa.utilities.common.RebatePolicyUseRangeFieldRender;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.UseRangeFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public class RebatePolicyWebDetailController extends SFAWebDetailController {
    private static final RebatePolicyUseRangeFieldRender useRangeFieldDataRender = SpringUtil.getContext().getBean(RebatePolicyUseRangeFieldRender.class);
    private static final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        Optional.ofNullable(result)
                .map(Result::getData)
                .ifPresent(data ->
                        {
                            describe.getFieldDescribes().stream().filter(k -> IFieldType.UseRange.equals(k.getType()))
                                    .forEach(k ->
                                            data.put(k.getApiName(), useRangeFieldDataRender.render(data.get(k.getApiName()), ((UseRangeFieldDescribe) k).getTargetApiName()))
                                    );
                            RebateUtils.setRebateDimensionName(data.toObjectData(), controllerContext.getUser());
                        }
                );
        if (RequestUtil.isWebRequest()) {
            Optional.ofNullable(result)
                    .map(Result::getLayout)
                    .ifPresent(x -> {
                        ILayout layout = new Layout(x);
                        processButton(layout);
                    });
        }

        Map<String, String> sectionNameMap = Maps.newHashMapWithExpectedSize(2);
        sectionNameMap.put("account_section__c", "paas.metadata.detail.layout.rebate.policy.account_section");
        sectionNameMap.put("execute_section__c", "paas.metadata.detail.layout.rebate.policy.execute_section");
        WebDetailUtils.dealFormI18N(result, sectionNameMap);

        return result;
    }

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest() || RequestUtil.isH5Request()) {
            availableRangeUtils.removeMobileButton(layout);
        }
        return layout;
    }

    private void processButton(ILayout layout) {
        Optional.ofNullable(layout)
                .map(LayoutExt::of)
                .map(LayoutExt::getHeadInfoComponent)
                .map(Optional::get)
                .map(IComponent::getButtons)
                .filter(CollectionUtils::notEmpty)
                .ifPresent(buttons -> {
                    enableButton(layout, buttons);
                    issueManualButton(layout, buttons);
                });
    }

    /**
     * 下发手动产生按钮
     *
     * @param layout  布局
     * @param buttons 按钮
     */
    private void issueManualButton(ILayout layout, List<IButton> buttons) {
        String executeMode = data.get(RebatePolicyConstants.RebatePolicyField.EXECUTE_MODE, String.class);
        if (RebatePolicyConstants.ExecuteMode.MANUAL.getValue().equals(executeMode)) {
            IButton manualButton = new Button();
            manualButton.setAction("Manual");
            manualButton.setActionType("default");
            manualButton.setLabel(I18N.text("paas.udobj.action.manual"));
            manualButton.setName("RebatePolicyObj_Manual_button_default");
            buttons.add(manualButton);
            WebDetailLayout.of(layout).setButtons(buttons);
        }
    }

    private void enableButton(ILayout layout, List<IButton> buttons) {
        Map<String, Boolean> privilegeMap = serviceFacade.funPrivilegeCheck(controllerContext.getUser(),
                SFAPreDefineObject.RebatePolicy.getApiName(),
                Lists.newArrayList(ObjectAction.AttributeEnable.getActionCode(), ObjectAction.AttributeDisEnable.getActionCode()));
        boolean removeEnableButton = !Boolean.TRUE.equals(privilegeMap.getOrDefault(ObjectAction.AttributeEnable.getActionCode(), Boolean.FALSE));
        boolean removeDisableButton = !Boolean.TRUE.equals(privilegeMap.getOrDefault(ObjectAction.AttributeDisEnable.getActionCode(), Boolean.FALSE));
        if (RebatePolicyConstants.ActiveStatus.ENABLE.getValue().equals(data.get(RebatePolicyConstants.RebatePolicyField.ACTIVE_STATUS, String.class))) {
            removeEnableButton = true;
        } else {
            removeDisableButton = true;
        }
        if (removeEnableButton) {
            buttons.removeIf(b -> ObjectAction.AttributeEnable.getActionCode().equals(b.getAction()));
        }
        if (removeDisableButton) {
            buttons.removeIf(b -> ObjectAction.AttributeDisEnable.getActionCode().equals(b.getAction()));
        }
        WebDetailLayout.of(layout).setButtons(buttons);
    }
}