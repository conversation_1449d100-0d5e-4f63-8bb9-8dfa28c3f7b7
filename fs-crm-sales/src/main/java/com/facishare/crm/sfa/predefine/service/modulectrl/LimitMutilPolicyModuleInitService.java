package com.facishare.crm.sfa.predefine.service.modulectrl;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.modulectrl.util.MarketingInitUtil;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 多政策共同限量初始化服务
 *
 * <AUTHOR>
 * @date 2023/2/13
 */
@Component
@Slf4j
public class LimitMutilPolicyModuleInitService extends AbstractModuleInitService {
    private final static String PRICE_POLICY_IDS = "{\"type\": \"object_reference_many\",\"define_type\": \"package\",\"is_index\": false,\"is_need_convert\": false,\"is_required\": false,\"is_unique\": false,\"max_length\": 600,\"pattern\": \"\",\"api_name\": \"price_policy_ids\",\"is_extend\": false,\"target_api_name\": \"PricePolicyObj\",\"target_related_list_name\": \"target_related_list_price_policy_ids\",\"target_related_list_label\": \"共同限量的价格政策\",\"label\": \"共同限量的价格政策\",\"is_index_field\": false,\"status\": \"released\"}";// ignoreI18n

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private EnterpriseInitService enterpriseInitService;
    @Resource
    private DescribeWithSimplifiedChineseService describeChineseService;

    @Override
    public String getModuleCode() {
        return "limit_multi_policy";
    }

    @Override
    public ConfigCtrlModule.Result initModule(String tenantId, String userId) {
        User user = new User(tenantId, userId);
        addFieldToLimit(user);
        return MarketingInitUtil.getOkResult();
    }

    private void addFieldToLimit(User user) {
        IObjectDescribe limitDescribe = describeChineseService.findByDescribeApiName(user, Utils.PRICE_POLICY_LIMIT_ACCOUNT_API_NAME);
        updateRange(limitDescribe, user);

        IFieldDescribe pricePolicyIds = FieldDescribeFactory.newInstance(PRICE_POLICY_IDS);
        if (limitDescribe.getFieldDescribe(pricePolicyIds.getApiName()) == null) {
            List<IFieldDescribe> toAddFieldList = Lists.newArrayList();
            toAddFieldList.add(pricePolicyIds);
            List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList = Lists.newArrayList();
            FieldLayoutPojo fieldLayout = enterpriseInitService.getFieldLayoutPojo(SystemConstants.RenderType.ObjectReferenceMany.renderType, false, false);
            fieldLayoutTupleList.add(Tuple.of(pricePolicyIds, fieldLayout));

            MarketingInitUtil.addFieldDescribe(limitDescribe, toAddFieldList);
            MarketingInitUtil.insertFieldsLayout(user, limitDescribe, fieldLayoutTupleList);
        }

    }

    public void updateRange(IObjectDescribe objectDescribe, User user) {
        List<IFieldDescribe> toUpdateFieldList = Lists.newArrayList();
        IFieldDescribe rangeDesc = objectDescribe.getFieldDescribe("range");

        if (rangeDesc instanceof SelectOneFieldDescribe) {
            SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) rangeDesc;
            List<ISelectOption> optionList = selectOneFieldDescribe.getSelectOptions();
            if (optionList != null) {
                //optionList 是否包含多个价格政策
                boolean hasMultiPolicy = optionList.stream().anyMatch(option -> "MULTI_POLICY".equals(option.getValue()));
                if (!hasMultiPolicy) {
                    ISelectOption optionRebate = new SelectOption();
                    optionRebate.setValue("MULTI_POLICY");
                    optionRebate.setLabel("多个价格政策");// ignoreI18n
                    optionList.add(optionRebate);
                    selectOneFieldDescribe.setSelectOptions(optionList);
                    toUpdateFieldList.add(rangeDesc);
                }
            }
        }

        IFieldDescribe pricePolicyIdDesc = objectDescribe.getFieldDescribe("price_policy_id");
        pricePolicyIdDesc.setRequired(Boolean.FALSE);
        toUpdateFieldList.add(pricePolicyIdDesc);

        MarketingInitUtil.updateFieldDescribe(objectDescribe, toUpdateFieldList);

        List<ILayout> layoutList = serviceFacade.getLayoutLogicService().getDetailLayouts(user.getTenantId(), objectDescribe);
        layoutList.forEach(m -> {
            try {
                ModuleInitLayoutUtil.updateField(m, Lists.newArrayList(), Lists.newArrayList("price_policy_id"));
            } catch (MetadataServiceException e) {
                throw new RuntimeException(e);
            }
            serviceFacade.getLayoutLogicService().updateLayout(user, m);
        });
    }
}
