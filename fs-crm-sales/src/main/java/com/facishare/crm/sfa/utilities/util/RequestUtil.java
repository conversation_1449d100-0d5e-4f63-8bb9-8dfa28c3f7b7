package com.facishare.crm.sfa.utilities.util;

import com.facishare.paas.appframework.core.model.RequestContext;
import org.apache.commons.lang3.StringUtils;


public class RequestUtil {

    public static boolean isErpSyncData(RequestContext requestContext) {
        String peerName = requestContext.getPeerName();
        return StringUtils.isNotEmpty(peerName) && peerName.toLowerCase().contains("erp-sync-data");
    }

    public static boolean fromErLink(RequestContext requestContext) {
        if (requestContext == null) {
            return false;
        }
        return StringUtils.isNotBlank(requestContext.getAppId());
    }
}
