package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.crm.util.Safes;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.DynamicDescribe;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.invisibleReferenceMap;
import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;

public class LeadsWebDetailController extends SFAWebDetailController {
    private static final String APINAME= "api_name";

    @Override
    protected void before(Arg arg) {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_720)) {
            throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);
        }
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        handleObjectDescribe(result);
        handleObjectData(result);
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        IObjectData objectData = newResult.getData().toObjectData();
        AccountUtil.handleRemainingTime(Lists.newArrayList(objectData));
        LeadsUtils.handleIsRemindRecycling(Lists.newArrayList(objectData));
        handleCard(newResult);
        if (newResult.getLayout() != null) {
            ILayout layout = new Layout(newResult.getLayout());
            specialLogicForLayout(layout, newResult);
            LayoutUtils.removeProfileComponent(controllerContext.getTenantId(), layout);
        }
        return newResult;
    }

    private void handleObjectDescribe(Result result) {
        IObjectDescribe objectDescribe = null;
        if (result.getObjectDescribeExt() == null) {
            DynamicDescribe dynamicDescribe = new DynamicDescribe(arg.getObjectDescribeApiName(), Maps.newHashMap(), Lists.newArrayList());
            ObjectDescribeDocument objectDescribeDocument = ObjectDescribeDocument.of(dynamicDescribe, Lists.newArrayList(), Lists.newArrayList());
            result.setObjectDescribeExt(objectDescribeDocument);
        }
        objectDescribe = ObjectDescribeExt.of(result.getObjectDescribeExt().toObjectDescribe()).copyOnWrite();
        List<IFieldDescribe> fieldDescribes = Lists.newArrayList();
        String description = I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.ACCOUNT_API_NAME));
        if (Strings.isBlank(description)) {
            description = "客户名称"; // ignoreI18n
        }
        IFieldDescribe fieldDescribe = constructFieldDescribe(description, Utils.ACCOUNT_API_NAME, "account_id");
        fieldDescribes.add(fieldDescribe);

        description =  I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.PARTNER_API_NAME));
        if (Strings.isBlank(description)) {
            description = "合作伙伴"; // ignoreI18n
        }
        fieldDescribe = constructFieldDescribe(description, Utils.PARTNER_API_NAME, "transfer_partner_id");
        fieldDescribes.add(fieldDescribe);

        description =  I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.OPPORTUNITY_API_NAME));
        if (Strings.isBlank(description)) {
            description = "商机名称"; // ignoreI18n
        }
        fieldDescribe = constructFieldDescribe(description, Utils.OPPORTUNITY_API_NAME, "opportunity_id");
        fieldDescribes.add(fieldDescribe);

        description =  I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.CONTACT_API_NAME));
        if (Strings.isBlank(description)) {
            description = "联系人名称"; // ignoreI18n
        }
        fieldDescribe = constructFieldDescribe(description, Utils.CONTACT_API_NAME, "contact_id");
        fieldDescribes.add(fieldDescribe);

        description =  I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.NEW_OPPORTUNITY_API_NAME));
        if (Strings.isBlank(description)) {
            description = "商机2.0名称"; // ignoreI18n
        }
        fieldDescribe = constructFieldDescribe(description, Utils.NEW_OPPORTUNITY_API_NAME, "new_opportunity_id__c");
        fieldDescribes.add(fieldDescribe);

        description = I18N.text("OpportunityObj.field.biz_status.label");
        if (Strings.isBlank(description)) {
            description = "商机状态"; // ignoreI18n
        }
        fieldDescribe = constructQuoteFieldDescribe(description, Utils.OPPORTUNITY_API_NAME, "opportunity_status", "biz_status");
        fieldDescribes.add(fieldDescribe);

        description = I18N.text("NewOpportunityObj.field.sales_status.label");
        if (Strings.isBlank(description)) {
            description = "商机2.0状态"; // ignoreI18n
        }
        fieldDescribe = constructQuoteFieldDescribe(description, Utils.NEW_OPPORTUNITY_API_NAME, "new_opportunity_status", "sales_status");
        fieldDescribes.add(fieldDescribe);

//        description = I18N.text("NewOpportunityObj.field.sales_stage.label");
        description = I18N.text("sfa.leads.details.sales_stage.label");
        if (Strings.isBlank(description)) {
            description = "商机阶段名称"; // ignoreI18n
        }
        fieldDescribe = constructQuoteFieldDescribe(description, Utils.NEW_OPPORTUNITY_API_NAME, "new_opportunity_sales_stage", "sales_stage");
        fieldDescribes.add(fieldDescribe);

        if (objectDescribe != null) {
            if (CollectionUtils.isEmpty(objectDescribe.getFieldDescribes())) {
                objectDescribe.setFieldDescribes(fieldDescribes);
            } else {
                List<IFieldDescribe> objectFields = objectDescribe.getFieldDescribes();
                objectFields.addAll(fieldDescribes);
                objectDescribe.setFieldDescribes(objectFields);
            }
            result.setObjectDescribeExt(ObjectDescribeDocument.of(objectDescribe));
        }
        if (result.getDescribe() != null) {
            IObjectDescribe resultObjectDescribe = ObjectDescribeExt.of(result.getDescribe().toObjectDescribe()).copyOnWrite();
            resultObjectDescribe.getFieldDescribes().addAll(fieldDescribes);
            result.setDescribe(ObjectDescribeDocument.of(resultObjectDescribe));
        }
    }

    private void handleObjectData(Result result) {
        IObjectData objectData = result.getData().toObjectData();
        if (!LeadsBizStatusEnum.TRANSFORMED.getCode().equals(objectData.get(LeadsConstants.Field.BIZ_STATUS.getApiName(), String.class))) {
            return;
        }
        List<IObjectData> accountDataList = getRelatedObjectDataList(arg.getObjectDataId(), Utils.ACCOUNT_API_NAME);
        setRelatedObject(objectData, accountDataList, Utils.ACCOUNT_API_NAME);
        List<IObjectData> contactDataList = getRelatedObjectDataList(arg.getObjectDataId(), Utils.CONTACT_API_NAME);
        setRelatedObject(objectData, contactDataList, Utils.CONTACT_API_NAME);
        List<IObjectData> opportunityDataList = getOpportunityDataList(arg.getObjectDataId(), Utils.OPPORTUNITY_API_NAME);
        setRelatedObject(objectData, opportunityDataList, Utils.OPPORTUNITY_API_NAME);
        List<IObjectData> newOpportunityDataList = getOpportunityDataList(arg.getObjectDataId(), Utils.NEW_OPPORTUNITY_API_NAME);
        setRelatedObject(objectData, newOpportunityDataList, Utils.NEW_OPPORTUNITY_API_NAME);
        List<IObjectData> partnerDataList = getRelatedObjectDataList(arg.getObjectDataId(), Utils.PARTNER_API_NAME);
        setRelatedObject(objectData, partnerDataList, Utils.PARTNER_API_NAME);
    }

    private void setRelatedObject(IObjectData leadsData, List<IObjectData> dataList, String apiName) {
        String id = "";
        String name = "";
        String status = "";
        if (dataList.size() > 0) {
            IObjectData data = dataList.get(0);
            if (data != null) {
                id = data.getId();
                name = data.getName();
                if (apiName.equals(SFAPreDefineObject.Opportunity.getApiName())) {
                    status = AccountUtil.getStringValue(data, "biz_status", "invalid");
                }
            }
        }
        if (apiName.equals(SFAPreDefineObject.Account.getApiName())) {
            leadsData.set("account_id", id);
            leadsData.set("account_id__r", name);
        }
        if (apiName.equals(SFAPreDefineObject.Contact.getApiName())) {
            leadsData.set("contact_id", id);
            leadsData.set("contact_id__r", name);
        }
        if (apiName.equals(SFAPreDefineObject.Opportunity.getApiName())) {
            leadsData.set("opportunity_id", id);
            leadsData.set("opportunity_id__r", name);
            leadsData.set("opportunity_status", status);
        }
        if (apiName.equals(SFAPreDefineObject.NewOpportunity.getApiName())) {
            List<IObjectData> list = Safes.of(dataList);
            List<String> idList = list.stream().map(IObjectData::getId).collect(Collectors.toList());
            List<String> nameList = list.stream().map(IObjectData::getName).collect(Collectors.toList());
            List<String> statusList = list.stream().map(o -> AccountUtil.getStringValue(o, "sales_status", Strings.EMPTY)).collect(Collectors.toList());
            List<String> salesStage = list.stream().map(o -> AccountUtil.getStringValue(o, "sales_stage", Strings.EMPTY)).collect(Collectors.toList());
            leadsData.set("new_opportunity_id__c", idList);
            leadsData.set("new_opportunity_id__c__r", nameList);
            leadsData.set("new_opportunity_status", statusList);
            leadsData.set("new_opportunity_sales_stage", salesStage);
        }
        if (apiName.equals(SFAPreDefineObject.Partner.getApiName())) {
            leadsData.set("transfer_partner_id", id);
            leadsData.set("transfer_partner_id__r", name);
        }
    }

    private List<IObjectData> getOpportunityDataList(String leadsId, String apiName) {
        List<IObjectData> result = Lists.newArrayList();
        List<Map> list = LeadsUtils.getOpportunityDataById(controllerContext.getTenantId(), apiName, leadsId);
        for (Map m : list) {
            result.add(ObjectDataDocument.of(m).toObjectData());
        }
        return result;
    }

    private List<IObjectData> getRelatedObjectDataList(String leadsId, String apiName) {
        List<IObjectData> result = Lists.newArrayList();
        List<Map> list = LeadsUtils.getRelatedObjectDataById(controllerContext.getTenantId(), apiName, leadsId);
        for (Map m : list) {
            result.add(ObjectDataDocument.of(m).toObjectData());
        }
        return result;
    }

    private IFieldDescribe constructFieldDescribe(String description, String targetApiName, String apiName) {

        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("describe_api_name", "LeadsObj");
        fieldMap.put("is_index", true);
        fieldMap.put("is_active", true);
        fieldMap.put("create_time", System.currentTimeMillis());
        fieldMap.put("description", description);
        fieldMap.put("is_unique", false);
        fieldMap.put("label", description);
        fieldMap.put("target_api_name", targetApiName);
        fieldMap.put("type", "object_reference");
        fieldMap.put("target_related_list_name", "object_leads_list");
        fieldMap.put("field_num", null);
        fieldMap.put("target_related_list_label", I18N.text("LeadsObj.attribute.self.display_name")/*销售线索*/);
        fieldMap.put("action_on_target_delete", "cascade_delete");
        fieldMap.put("is_required", false);
        fieldMap.put(APINAME, apiName);
        fieldMap.put("define_type", "package");
        fieldMap.put("is_index_field", true);
        fieldMap.put("is_single", false);
        fieldMap.put("index_name", "index");
        fieldMap.put("status", "released");
        return FieldDescribeExt.of(fieldMap).getFieldDescribe();
    }

    private IFieldDescribe constructQuoteFieldDescribe(String description, String targetApiName, String apiName, String field) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), targetApiName);

        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("describe_api_name", "LeadsObj");
        fieldMap.put("is_index", true);
        fieldMap.put("is_active", true);
        fieldMap.put("create_time", System.currentTimeMillis());
        fieldMap.put("description", description);
        fieldMap.put("is_unique", false);
        fieldMap.put("label", description);
        fieldMap.put("type", "select_one");
        fieldMap.put("options", JSONObject.toJSON(objectDescribe.getFieldDescribe(field).get("options")));
        fieldMap.put("field_num", null);
        fieldMap.put("is_required", false);
        fieldMap.put(APINAME, apiName);
        fieldMap.put("define_type", "package");
        fieldMap.put("is_index_field", true);
        fieldMap.put("is_single", false);
        fieldMap.put("index_name", "index");
        fieldMap.put("status", "released");
        return FieldDescribeExt.of(fieldMap).getFieldDescribe();
    }

    private void handleCard(Result newResult) {
        String picPath = "picture_path";
        Object card = (newResult.getData()).get(picPath);
        if (card != null && !StringUtils.isEmpty(card)) {
            List cardlist = (List) card;
            if (cardlist.isEmpty()) {
                newResult.getData().put(picPath, Lists.newArrayList());
            } else {
                Object path = ((Map) (cardlist.get(0))).get("path");
                if (path == null || StringUtils.isEmpty(path.toString())) {
                    newResult.getData().put(picPath, Lists.newArrayList());
                }
            }
        }
    }

    private void specialLogicForLayout(ILayout layout, Result result) {
        String clientInfo = getControllerContext().getRequestContext().getAttribute(CLIENT_INFO);
        log.info("specialLogicForLayout-clientInfo={}", clientInfo);
        if (layout == null) {
            return;
        }
        //所有端需要做的特殊处理
        ObjectDataDocument data = result.getData();
        String leadsPoolId = LeadsUtils.getPoolId(data.toObjectData());
        String owner = CommonBizUtils.getOwner(data);

        if (!StringUtils.isEmpty(leadsPoolId) && StringUtils.isEmpty(owner)) {
            SFADetailController.CheckNeedShowRelatedObjsResult checkNeedShowRelatedObjsResult = checkNeedShowRelatedObjs();
            handleHideFieldsBySetting(layout, checkNeedShowRelatedObjsResult.getNeedHideFields());
            if (!checkNeedShowRelatedObjsResult.isAllowMemberRelation()) {
                List<String> removeComponentNameList = Lists.newArrayList("relevant_team_component", "contact_member_relationship", "rfm_analysis");
                WebDetailLayout.of(layout).removeComponents(removeComponentNameList);
            }
            if (!checkNeedShowRelatedObjsResult.isAllowMemberViewOperationLog()) {
                WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("operation_log"));
            }
            boolean isAllowMemberViewFeed = checkNeedShowRelatedObjsResult.isAllowMemberViewFeed();
            boolean isAllowMemberSendFeed = checkNeedShowRelatedObjsResult.isAllowMemberSendFeed();
            if (!isAllowMemberSendFeed) {
                List<IButton> layoutButtonsbuttons = layout.getButtons();
                layoutButtonsbuttons.removeIf(x -> x.getAction().equals(ObjectAction.ADD_EVENT.getActionCode()));
                layout.setButtons(layoutButtonsbuttons);
                IComponent component = getComponent(layout, "related_record", "sale_log");
                if (component != null) {
                    List<IButton> buttons = component.getButtons();
                    buttons.removeIf(b -> ObjectAction.ADD_EVENT.getActionCode().equals(b.getAction()));
                    component.setButtons(buttons);
                }
            }
            if (!isAllowMemberViewFeed) {
                WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("sale_log"));
            }
        }
        if (!LeadsUtils.isGrayLeadsDuplicated(controllerContext.getTenantId())) {
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("LeadsObj_collected_to_related_list"));
        }

        IComponent component = getComponent(layout, "relatedlist", "LeadsObj_collected_to_related_list");
        if (component != null) {
            List<IButton> buttons = component.getButtons();
            buttons.removeIf(b -> "Add_button_default".equals(b.get(APINAME, String.class))
                    || "BulkRelate_button_default".equals(b.get(APINAME, String.class))
                    || "IntelligentForm_button_default".equals(b.get(APINAME, String.class))
                    || "BulkDisRelate_button_default".equals(b.get(APINAME, String.class)));
            component.setButtons(buttons);
        }
        //移动端移除ViewFeedCard按钮
        List<IButton> buttons = layout.getButtons();
        if (!CollectionUtils.isEmpty(buttons)) {
            buttons.stream().filter(x -> "Transfer_button_default".equals(x.getName())).findFirst().ifPresent(x -> {
                x.setAction("TransferUI");
                x.setName("TransferUI_button_default");
                x.setLabel(I18N.text("paas.udobj.action.transfer_ui"));
            });
        }
        if (RequestUtil.isMobileOrH5Request() || RequestUtil.isMobileOrMobileDeviceRequest()) {
            buttons.removeIf(x -> x.getAction().equals(ObjectAction.VIEW_FEED_CARD.getActionCode()));
            buttons.removeIf(x -> x.getAction().equals(ObjectAction.PROCESS_LEADS.getActionCode()));
        }
        if (RequestUtil.isMobileOrH5Request() || RequestUtil.isWXMiniProgram() || AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(controllerContext.getAppId())) {
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("contact_member_relationship"));
        }

        if (RequestUtil.isH5Request() || AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(controllerContext.getAppId())) {
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("rfm_analysis"));
        }
        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("qywx_conversion"));
        if (!LeadsUtils.isGrayLeadsWorkPlatform(controllerContext.getTenantId())) {
            buttons.removeIf(x -> x.getAction().equals(ObjectAction.PROCESS_LEADS.getActionCode()));
        }
        layout.setButtons(buttons);

        List<String> invisibleReference = invisibleReferenceMap.get(arg.getObjectDescribeApiName());
        List<String> componentApiNameList = Lists.newArrayList();
        Safes.of(invisibleReference).forEach(m -> componentApiNameList.add(m + "_leads_id_related_list"));
        WebDetailLayout.of(layout).removeComponents(componentApiNameList);
        List<IComponent> components = LayoutExt.of(layout).getComponentsSilently();
        if (!CollectionUtils.isEmpty(components)) {
            components.forEach(c -> {
                List<IButton> buttonList = c.getButtons();
                if (!CollectionUtils.isEmpty(buttonList)) {
                    buttonList.stream().filter(x -> "Transfer_button_default".equals(x.getName())).findFirst().ifPresent(x -> {
                        x.setAction("TransferUI");
                        x.setName("TransferUI_button_default");
                        x.setLabel(I18N.text("paas.udobj.action.transfer_ui"));
                    });
                    if (!LeadsUtils.isGrayLeadsWorkPlatform(controllerContext.getTenantId())) {
                        buttonList.removeIf(x -> x.getAction().equals(ObjectAction.PROCESS_LEADS.getActionCode()));
                        c.setButtons(buttonList);
                    }
                }
            });
        }
    }
}
