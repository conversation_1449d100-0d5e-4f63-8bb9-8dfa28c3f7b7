package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.crm.sfa.utilities.util.VersionUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class NewOpportunityDescribeLayoutController extends SFADescribeLayoutController {

    @Override
    protected void handelDescribe(Arg arg, Result result) {
        super.handelDescribe(arg, result);
        IObjectDescribe describe = result.getObjectDescribe().toObjectDescribe();
        setCascadeParentApiName(describe);
    }

    @Override
    protected void promptUpgrade(Arg arg, Result result) {
        super.promptUpgrade(arg, result);
        if (VersionUtil.isVersionEarlierEqualThan715(controllerContext.getRequestContext())) {
            throw new ValidateException(I18N.text("sfa.CommonUtil.358.1"));
        }
    }

    @Override
    protected boolean supportSaveDraft() {
        return true;
    }

    @Override
    protected boolean supportSaveAndCreate() {
        return true;
    }

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        String layoutType = arg.getLayout_type();
        if (LayoutTypes.EDIT.equals(layoutType) || (LayoutTypes.ADD.equals(layoutType) && !GrayUtil.allowToEditNewOpportunitySalesStage(controllerContext.getTenantId()))) {
            List<FormComponent> list = LayoutExt.of(result.getLayout()).getFormComponents().stream().map(FormComponentExt::getFormComponent).filter(FormComponent.class::isInstance).map(FormComponent.class::cast).collect(Collectors.toList());
            for (FormComponent component : list) {
                PreDefLayoutUtil.setFormComponentFieldReadOnly(component, Collections.singletonList("sales_stage"));
            }
        }
        addNonProductButton(result, Utils.NEW_OPPORTUNITY_LINES_API_NAME);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (LayoutTypes.ADD.equals(arg.getLayout_type()) || LayoutTypes.EDIT.equals(arg.getLayout_type())) {
            handleReadOnlyFieldsForDetailLayout(newResult, NewOppportunityConstants.NewOpportunityLinesField.NEW_OPPORTUNITY_ID.getApiName());
        }
        return newResult;
    }

    /**
     * 将价目表ID和商机ID的cascade_parent_api_name设置成account_id
     *
     * @param describe 描述
     */
    private void setCascadeParentApiName(IObjectDescribe describe) {
        List<IFieldDescribe> fields = ObjectDescribeExt.of(describe)
                .getFieldDescribesSilently().stream().filter(field ->
                        field.getApiName().equals(NewOppportunityConstants.NewOpportunityField.PRICEBOOKID.getApiName()))
                .collect(Collectors.toList());

        for (IFieldDescribe field : fields) {
            field.set("cascade_parent_api_name", Lists.newArrayList("account_id"));
        }
    }
}

