package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.procurement.qianlima.QlmProcurementService;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.BiddingRuleService;
import com.facishare.crm.sfa.predefine.service.BiddingSubscriptionRuleService;
import com.facishare.crm.sfa.predefine.service.Procurement.ProcurementService;
import com.facishare.crm.sfa.predefine.service.SettingRuleMemberService;
import com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants;
import com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementRule;
import com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementRuleMemberModel;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.crm.util.CommonSqlUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants.*;

/**
 * BiddingSubscriptionRulesListController
 *
 * <AUTHOR>
 */
public class BiddingSubscriptionRulesListController extends StandardListController {

    private static final SettingRuleMemberService settingRuleMemberService = SpringUtil.getContext().getBean(SettingRuleMemberService.class);


    private static final BiddingRuleService BIDDING_RULE_SERVICE = SpringUtil.getContext().getBean(BiddingRuleService.class);

    private static final ProcurementService procurementService = SpringUtil.getContext().getBean(ProcurementService.class);

    private static final BiddingSubscriptionRuleService biddingSubscriptionRuleService = SpringUtil.getContext().getBean(BiddingSubscriptionRuleService.class);

    private List<String> recordType = new ArrayList<>();

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        List<IFilter> filters = searchQuery.getFilters();
        for (IFilter filter : filters) {
            if (IObjectData.RECORD_TYPE.equals(filter.getFieldName())) {
                recordType = filter.getFieldValues();
            }
        }
        return searchQuery;
    }
    @Override
    protected void doFunPrivilegeCheck() {

    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String tenantId = controllerContext.getTenantId();

        Map<String, Object> map = result.getExtendInfo();
        if (recordType.contains(QLM)) {
            if (map == null){
                map = new HashMap<>();
            }
            map.put("licensePara", new ImmutableMap.Builder<String, Integer>()
                    .put("max", biddingSubscriptionRuleService.getMaxCount(tenantId, QLM))
                    .put("using", biddingSubscriptionRuleService.getUsingCount(tenantId, QLM))
                    .build()
            );
            int allCount = biddingSubscriptionRuleService.selectModuleParaMaxCount(tenantId, BUY_DATA);
            int useCount = biddingSubscriptionRuleService.getBuyHistoricalDataUsingCount(tenantId);
            map.put("resources", Maps.of(
                    "all", (allCount - useCount),
                    "use", useCount,
                    "show", allCount > 0
            ));
            // ai资源
            map.put("aiResources",  procurementService.getAiResources(controllerContext.getUser()));
        }
        result.setExtendInfo(map);
        //千里马行政单位
        JSONArray provinceArray = JSON.parseArray(QlmProcurementService.getProvince());
        Map<String, String> provinceMap = new HashMap<>();
        for (int i = 0; i < provinceArray.size(); i++) {
            JSONObject json = provinceArray.getJSONObject(i);
            provinceMap.put(json.getString("value"), json.getString("label"));
        }
        JSONArray cityArray = JSON.parseArray(QlmProcurementService.getCity());
        Map<String, String> cityMap = new HashMap<>();
        for (int i = 0; i < cityArray.size(); i++) {
            JSONObject json = cityArray.getJSONObject(i);
            cityMap.put(json.getString("value"), json.getString("label"));
        }

        List<IObjectData> objectDataList = ObjectDataDocument.ofDataList(result.getDataList());
        List<String> disabledRuleIds = new ArrayList<>();
        List<String> initButtonRuleIds = new ArrayList<>();
        Set<String> buyDataButtonIds = new HashSet<>();
        for (IObjectData data : objectDataList) {
            List<ProcurementRuleMemberModel> ruleMember = settingRuleMemberService.getSettingRuleMember(controllerContext.getTenantId(),
                    controllerContext.getUser().getUpstreamOwnerIdOrUserId(), data.getId(), SFAPreDefineObject.BiddingSubscriptionRules.getApiName());
            Map<String, Object> conditions = BIDDING_RULE_SERVICE.getProcurementRuleConditions(controllerContext.getTenantId(), data.get(ProcurementRule.CONDITIONS));
            data.set(BID_SUB_TYPE_TEXT, conditions.get(BID_SUB_TYPE));
            data.set(CALLER_TYPE_TEXT, conditions.get(CALLER_TYPE));
            data.set(BID_METHOD_TEXT, conditions.get(BID_METHOD));

            data.set(BIDING_END_DATE_TIME, conditions.get(BIDING_END_DATE_TIME));
            data.set(TENDER_END_DATE_TIME, conditions.get(TENDER_END_DATE_TIME));
            data.set("member_list", ruleMember);
            if (!(boolean) data.get(ProcurementRule.IS_ENABLED)) {
                disabledRuleIds.add(data.getId());
            }
            List<String> names = procurementService.getObjectDataNames(controllerContext.getTenantId(), data);
            if (CollectionUtils.notEmpty(names)) {
                data.set(ENTERPRISE_NAME, Joiner.on(",").join(names));
            }
            data.set(PURCHASE_TYPE_TEXT, conditions.get(PURCHASE_TYPE));

            if (ZL.equals(data.getRecordType())) {
                data.set(AREA_PROVINCE_TEXT, conditions.get(AREA_PROVINCE));
                if (conditions.get(AREA_CITY) != null) {
                    List cityLabels = procurementService.getAreaCityLabelByCode(CommonSqlUtils.convert2ActionContext(controllerContext), new HashSet<>((ArrayList) conditions.get(AREA_CITY)));
                    data.set(AREA_CITY_TEXT, cityLabels);
                }
            } else if (QLM.equals(data.getRecordType())) {
                List<String> pList = getValue(data.get(ProcurementRule.CONDITIONS), AREA_PROVINCE);
                if (CollectionUtils.notEmpty(pList)) {
                    data.set(AREA_PROVINCE_TEXT, pList.stream().map(e -> provinceMap.getOrDefault(e, e)).collect(Collectors.joining(",")));
                }
                List<String> cList = getValue(data.get(ProcurementRule.CONDITIONS), AREA_CITY);
                if (CollectionUtils.notEmpty(cList)) {
                    data.set(AREA_CITY_TEXT, cList.stream().map(e -> cityMap.getOrDefault(e, e)).collect(Collectors.joining(",")));
                }
                buyDataButtonIds.add(data.getId());
                if (!ObjectUtils.isEmpty(data.get(BiddingSubscriptionRulesConstants.PROCUREMENT_ANALYSIS_INIT_STATUS))) {
                    if (!data.get(BiddingSubscriptionRulesConstants.PROCUREMENT_ANALYSIS_INIT_STATUS, String.class).equals("3")) {
                        initButtonRuleIds.add(data.getId());
                    }
                    if (data.get(BiddingSubscriptionRulesConstants.PROCUREMENT_ANALYSIS_INIT_STATUS, String.class).equals("2")) {
                        buyDataButtonIds.remove(data.getId());
                    }
                }
                if (!ObjectUtils.isEmpty(data.get(QLM_BUY_DATA))) {
                    buyDataButtonIds.add(data.getId());
                }
            }
        }
        ButtonInfo buttonInfo = result.getButtonInfo();
        if (!ObjectUtils.isEmpty(buttonInfo) && CollectionUtils.notEmpty(buttonInfo.getButtonMap())) {
            buttonInfo.getButtonMap().forEach((key, value) -> {
                if (!disabledRuleIds.contains(key)) {
                    value.removeIf(x -> ObjectAction.DELETE.getButtonApiName().equals(x) || ObjectAction.UPDATE.getButtonApiName().equals(x));
                }
                if (initButtonRuleIds.contains(key)) {
                    value.removeIf(QLM_INIT_BUTTON::equals);
                }
                if (buyDataButtonIds.contains(key)) {
                    value.removeIf(QLM_BUY_DATA_BUTTON::equals);
                }
            });
        }
        return result;
    }

    private List<String> getValue(Object conditionsComplete, String key) {
        UseRangeFieldDataRender.UseRangeInfo useRangeInfo = JSON.parseObject(conditionsComplete.toString(), UseRangeFieldDataRender.UseRangeInfo.class);
        if (useRangeInfo.getValue().equalsIgnoreCase(UseRangeFieldDataRender.UseRangeType.ALL.toString())) {
            return Lists.newArrayList();
        }
        List<UseRangeFieldDataRender.FilterGroup> filterGroups = JSON.parseArray(useRangeInfo.getValue(), UseRangeFieldDataRender.FilterGroup.class);
        if (CollectionUtils.empty(filterGroups)) {
            return new ArrayList<>();
        }
        for (UseRangeFieldDataRender.FilterGroup filterGroup : filterGroups) {
            JSONArray filters = filterGroup.getFilters();
            for (int i = 0; i < filters.size(); i++) {
                JSONObject filter = filters.getJSONObject(i);
                if (key.equals(filter.getString("field_name"))) {
                    return filter.getJSONArray("field_values").toJavaList(String.class);
                }
            }
        }
        return new ArrayList<>();
    }
}