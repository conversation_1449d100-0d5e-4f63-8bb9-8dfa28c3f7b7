package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.model.ProductCategoryTree;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.enums.CategoryFilterEnum;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.enums.ConfigType.CLOSE_OLD_CATEGORY;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.SHOP_CATEGORY_RECORD_TYPE;
import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.*;
import static com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils.getMaxCode;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_GET_WARN_INFO;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.*;
import static com.facishare.paas.metadata.api.describe.IFieldType.RECORD_TYPE;

/**
 * @author: sundy
 * @date: 2020/11/3 18:03
 * @description:
 */
@Slf4j
@Service
public class ProductCategoryUtils {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private ConfigService configService;


    public static final String PARENT_CODE = "parent_code";
    public static final String PARENT_NAME = "partner_name";
    public static final String PCODE = "pcode";
    public static final String CODE = "code";
    /**
     * 不要问为什么是123，迁移过来的
     */
    public static final String VIRTUAL_ID = "123";
    public static final String UPDATE = "update";
    public static final String ADD = "add";
    public static final int BULK_ADD_LEVEL_LIMIT = 20;
    public static final int BULK_ADD_DATA_LIMIT = 1000;
    public static final String PRODUCT_CATEGORY_NAME_PATH = "product_category_name_path";
    /**
     * 更新描述时分类【选项类型】上限是3500
     * 特设导入3000
     */
    public static final int CATEGORY_LIMIT = 3000;
    public static final int NEW_CATEGORY_LIMIT = 3500;
    public static final List<String> IMPORT_REMOVE_FIELDS = Lists.newArrayList(ORDER_FIELD, PID, CODE, "out_owner", ProductCategoryModel.Filed.PRODUCT_CATEGORY_PATH, "data_own_department");


    public IFieldDescribe getFieldDescribe(String fieldApiName) {
        if (PARENT_CODE.equals(fieldApiName)) {
            return constructFieldDescribe(I18N.text(SO_PARENT_CATEGORY_CODE), PARENT_CODE);
        } else if (PARENT_NAME.equals(fieldApiName)) {
            return constructFieldDescribe(I18N.text(SO_PARENT_CATEGORY_NAME), PARENT_NAME);
        } else if (PCODE.equals(fieldApiName)) {
            return constructFieldDescribe(I18N.text(SO_PARENT_CODE), PCODE);
        }
        return null;
    }

    public IFieldDescribe constructFieldDescribe(String label, String fieldApiName) {
        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("describe_api_name", "ProductCategoryObj");
        fieldMap.put("is_index", false);
        fieldMap.put("is_active", true);
        fieldMap.put("create_time", System.currentTimeMillis());
        fieldMap.put("description", label);
        fieldMap.put("is_unique", false);
        fieldMap.put("label", label);
        fieldMap.put("type", "text");
        fieldMap.put("max_length", 2000);
        fieldMap.put("field_num", null);
        fieldMap.put("is_required", false);
        fieldMap.put("api_name", fieldApiName);
        fieldMap.put("define_type", "package");
        fieldMap.put("is_index_field", false);
        fieldMap.put("is_abstract", true);
        fieldMap.put("status", "released");
        return FieldDescribeExt.of(fieldMap).getFieldDescribe();
    }

    public QueryResult<IObjectData> findRootByName(ServiceContext context, Set<String> rootNameSet) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        SearchUtil.fillFilterIsNull(searchTemplateQuery.getFilters(), PID);
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), NAME, rootNameSet);
        return serviceFacade.findBySearchQuery(context.getUser(), API_NAME, searchTemplateQuery);
    }

    public String findMaxCode(User user) {
        String sql = "";
        QueryResult<IObjectData> maxCode;
        try {
            sql = getMaxCode(user.getTenantId());
            maxCode = objectDataService.findBySql(sql, user.getTenantId(), API_NAME);
        } catch (MetadataServiceException e) {
            log.error("SpuSkuService searchUsedSpecValuesBySpuId findBySql error. sql:{} ", sql);
            throw new APPException("system error.");
        }
        if (CollectionUtils.isEmpty(maxCode.getData())) {
            return "1";
        }
        return maxCode.getData().get(0).get(CODE, String.class);
    }

    /**
     * 获取分类的子分类包括自己
     */
    public Set<String> findCategoryChildren(User user, String code) {
        Set<String> codeList = Sets.newHashSet(code);
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), Utils.PRODUCT_API_NAME);
        String fieldLabel = "";
        SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) describe.getFieldDescribe(CATEGORY);
        for (ISelectOption selectOption : fieldDescribe.getSelectOptions()) {
            if (selectOption.getValue().equals(code)) {
                fieldLabel = selectOption.getLabel() + "/";
                break;
            }
        }
        if (StringUtils.isNotBlank(fieldLabel)) {
            for (ISelectOption selectOption : fieldDescribe.getSelectOptions()) {
                if (selectOption.getLabel().startsWith(fieldLabel)) {
                    codeList.add(selectOption.getValue());
                }
            }
        }
        return codeList;
    }

    /**
     * 获取数据的父节点
     *
     * @return 父节点
     */
    public List<IObjectData> findParentFromDb(List<BaseImportDataAction.ImportData> dataList, User user) {
        List<String> fakeRootParentCode = dataList.stream().map(x -> x.getData().get(ProductCategoryUtils.PARENT_CODE).toString()).collect(Collectors.toList());
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(fakeRootParentCode.size());
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), CATEGORY_CODE, fakeRootParentCode);
        QueryResult<IObjectData> queryResult = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, API_NAME, searchTemplateQuery);
        return queryResult.getData();
    }

    public List<IObjectData> findChildFromDb(User user, List<IObjectData> fakeRootParent) {
        List<String> partnerIdList = fakeRootParent.stream().map(DBRecord::getId).collect(Collectors.toList());
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(partnerIdList.size());
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), PID, partnerIdList);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, API_NAME, searchTemplateQuery).getData();
    }

    public List<ProductCategoryTree> getImportTreeList(Collection<BaseImportDataAction.ImportData> headList, List<BaseImportDataAction.ImportData> dataList, String by) {
        List<ProductCategoryTree> treeList = Lists.newArrayList();
        List<ProductCategoryTree> list = createImportTreeData(dataList);
        List<ProductCategoryTree> rootList = createImportTreeData(Lists.newArrayList(headList));
        for (ProductCategoryTree root : rootList) {
            buildTree(root, list, by);
            treeList.add(root);
        }
        return treeList;
    }

    private List<ProductCategoryTree> createImportTreeData(List<BaseImportDataAction.ImportData> dataList) {
        return dataList.stream().map(o -> ProductCategoryTree.builder().name(o.getData().getName() == null ? "" : o.getData().getName()).partnerCode(o.getData().get(ProductCategoryUtils.PARENT_CODE, String.class)).categoryCode(o.getData().get(CATEGORY_CODE, String.class)).children(Lists.newArrayList()).build()).collect(Collectors.toList());
    }

    public List<ProductCategoryTree> getTreeList(List<IObjectData> headList, List<IObjectData> dataList, String by) {
        List<ProductCategoryTree> treeList = Lists.newArrayList();
        List<ProductCategoryTree> list = buildTreeInit(dataList);
        List<ProductCategoryTree> rootList = buildTreeInit(headList);
        for (ProductCategoryTree root : rootList) {
            buildTree(root, list, by);
            treeList.add(root);
        }
        return treeList;
    }

    private List<ProductCategoryTree> buildTreeInit(List<IObjectData> objectDataList) {
        return objectDataList.stream().map(o -> ProductCategoryTree.builder().id(o.getId()).name(o.getName()).pid(o.get(PID, String.class)).code(o.get(CODE, String.class)).categoryCode(o.get(CATEGORY_CODE, String.class)).partnerCode(o.get(ProductCategoryUtils.PARENT_CODE, String.class)).orderField(o.get(ORDER_FIELD, Integer.class)).children(Lists.newArrayList()).categoryImage(o.get("category_image", List.class)).build()).collect(Collectors.toList());
    }

    private void buildTree(ProductCategoryTree node, List<ProductCategoryTree> treeList, String by) {
        List<ProductCategoryTree> children;
        if (Objects.equals(by, PID)) {
            children = treeList.stream().filter(x -> Objects.equals(x.getPid(), node.getId())).collect(Collectors.toList());
        } else if (Objects.equals(by, PARENT_CODE)) {
            children = treeList.stream().filter(x -> Objects.equals(x.getPartnerCode(), node.getCategoryCode())).collect(Collectors.toList());
        } else {
            children = Lists.newArrayList();
        }
        if (CollectionUtils.isNotEmpty(children)) {
            treeList.removeAll(children);
            for (ProductCategoryTree child : children) {
                buildTree(child, treeList, by);
            }
            node.setChildren(children);
        }
    }

    public QueryResult<IObjectData> findAllCategory(User user) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(0);
        searchQuery.setOrders(Lists.newArrayList(SearchUtil.orderByDataId()));
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, API_NAME, searchQuery);
    }

    public QueryResult<IObjectData> findAllCategoryWithFields(User user, List<String> fields) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(0);
        searchQuery.setOrders(Lists.newArrayList(SearchUtil.orderByDataId()));
        return metaDataFindServiceExt.findBySearchQueryWithFields(user, API_NAME, searchQuery, fields, true);
    }

    public QueryResult<IObjectData> findDeletedCategory(User user) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, "-1");
        searchQuery.setFilters(filters);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, API_NAME, searchQuery);
    }

    public QueryResult<IObjectData> findAllSortedCategory(User user) {
        return findAllSortedCategory(user, null);
    }

    public QueryResult<IObjectData> findAllSortedCategory(User user, List<String> fieldList, boolean fromDb, CategoryFilterEnum categoryFilterEnum) {
        StopWatch stopWatch = StopWatch.create("findAllSortedCategory");
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), SFAPreDefineObject.ProductCategory.getApiName());
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        if (fromDb) {
            searchQuery.setSearchSource("db");
        }
        fillCategoryFilter(searchQuery, categoryFilterEnum);
        searchQuery.setLimit(0);
        //跳过数据权限
        searchQuery.setPermissionType(0);
        QueryResult<IObjectData> bySearchQuery;
        if (CollectionUtils.isNotEmpty(fieldList)) {
            bySearchQuery = metaDataFindServiceExt.findBySearchQueryWithFields(user, API_NAME, searchQuery, fieldList, true);
        } else {
            bySearchQuery = metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, API_NAME, searchQuery);
        }
        stopWatch.lap("all-category-findBySearchQuery");
        List<IObjectData> dataList = bySearchQuery.getData();
        List<IObjectData> collect = dataList.stream().sorted(Comparator.comparingInt(x -> Integer.parseInt(x.get(ORDER_FIELD).toString()))).collect(Collectors.toList());
        stopWatch.lap("all-category-sort");
        collect.forEach(x -> ObjectDataExt.of(x).handleMultiLangField(describe));
        stopWatch.lap("all-category-handleMultiLangField");
        bySearchQuery.setData(collect);
        stopWatch.log();
        return bySearchQuery;
    }

    public void fillCategoryFilter(SearchTemplateQuery searchQuery, CategoryFilterEnum categoryFilterEnum) {
        if (categoryFilterEnum == null) {
            return;
        }
        switch (categoryFilterEnum) {
            case PRODUCT:
                SearchUtil.fillFilterNotEq(searchQuery.getFilters(), RECORD_TYPE, SHOP_CATEGORY_RECORD_TYPE);
                break;
            case SHOP:
                SearchUtil.fillFilterEq(searchQuery.getFilters(), RECORD_TYPE, SHOP_CATEGORY_RECORD_TYPE);
                break;
            default:
                break;
        }
    }

    public QueryResult<IObjectData> findAllSortedCategory(User user, List<String> fieldList) {
        return findAllSortedCategory(user, fieldList, true, null);
    }


    public List<IObjectData> findObjectByCategoryCode(User user, Set<String> pCodeSet, int limit) {
        if (CollectionUtils.isEmpty(pCodeSet)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        IFilter filter = new Filter();
        query.setOffset(0);
        query.setLimit(limit);
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        filter.setFieldName(CATEGORY_CODE);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(pCodeSet));
        query.setFilters(Lists.newArrayList(filter));
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, API_NAME, query).getData();
    }

    public Map<ProductCategoryTree, Integer> getDepthMap(ProductCategoryTree root) {
        Map<ProductCategoryTree, Integer> depthMap = Maps.newHashMap();
        computeDepth(root, 1, depthMap);
        return depthMap;
    }

    private void computeDepth(ProductCategoryTree root, int depth, Map<ProductCategoryTree, Integer> depthMap) {
        List<ProductCategoryTree> children = root.getChildren();
        depthMap.put(root, depth);
        for (ProductCategoryTree child : children) {
            computeDepth(child, depth + 1, depthMap);
        }
    }
    public List<ProductCategoryTree> getDataListByRoot(ProductCategoryTree root) {
        List<ProductCategoryTree> list = Lists.newArrayList();
        bfs(root.getChildren(), list);
        return list;
    }

    private void bfs(List<ProductCategoryTree> children, List<ProductCategoryTree> list) {
        List<ProductCategoryTree> allChildren = new ArrayList<>();
        children = Optional.ofNullable(children).orElse(Lists.newArrayList());
        for (ProductCategoryTree child : children) {
            list.add(child);
            List<ProductCategoryTree> childrenList = child.getChildren();
            if (CollectionUtils.isNotEmpty(childrenList)) {
                allChildren.addAll(childrenList);
            }
        }
        if (CollectionUtils.isNotEmpty(allChildren)) {
            bfs(allChildren, list);
        }
    }

    public List<BaseImportAction.ImportError> getErrorList(List<BaseImportDataAction.ImportData> errorData, String msg) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        for (BaseImportDataAction.ImportData data : errorData) {
            errorList.add(new BaseImportAction.ImportError(data.getRowNo(), msg));
        }
        return errorList;
    }

    public int getMaxOrderUnderParent(User user, String pid) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setPermissionType(0);
        query.setNeedReturnQuote(Boolean.FALSE);
        query.setNeedReturnCountNum(Boolean.FALSE);
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(PID);
        if (StringUtils.isBlank(pid)) {
            filter.setOperator(Operator.IS);
        } else {
            filter.setOperator(Operator.EQ);
        }
        filter.setFieldValues(Lists.newArrayList(pid));
        filters.add(filter);
        query.addFilters(filters);
        query.setOrders(Lists.newArrayList(new OrderBy(ORDER_FIELD, Boolean.FALSE)));
        List<IObjectData> data = serviceFacade.findBySearchQuery(user, SFAPreDefineObject.ProductCategory.getApiName(), query).getData();
        if (CollectionUtils.isEmpty(data)) {
            return 0;
        }
        return Integer.parseInt(Optional.ofNullable(data.get(0).get(ORDER_FIELD)).orElse("0").toString());
    }

    public int getMaxOrderUnderParent(String pid, List<IObjectData> productCategoryDataFromDb) {
        IObjectData maxOrderData = productCategoryDataFromDb.stream()
                .filter(d -> pid.equals(ObjectDataUtils.getValueOrDefault(d, PID, "")))
                .max(Comparator.comparingInt(d -> d.get(ORDER_FIELD, Integer.class, 0)))
                .orElse(null);
        if (maxOrderData == null) {
            return 0;
        }
        return maxOrderData.get(ORDER_FIELD, Integer.class, 0);
    }

    public List<IObjectData> productCategoryTreeConvert(List<ProductCategoryTree> productCategoryList) {
        List<IObjectData> result = Lists.newArrayList();
        for (ProductCategoryTree tree : productCategoryList) {
            IObjectData data = new ObjectData();
            data.setId(tree.getId());
            data.setName(tree.getName());
            data.set(CODE, tree.getCode());
            data.set(CATEGORY_CODE, tree.getCategoryCode());
            data.set(PID, tree.getPid());
            result.add(data);
        }
        return result;
    }

    public List<IObjectData> getSameNameDataFromDb(User user, List<String> nameList) {
        if (CollectionUtils.isEmpty(nameList)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(0);
        SearchUtil.fillFilterIsNull(searchTemplateQuery.getFilters(), PID);
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), NAME, nameList);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, API_NAME, searchTemplateQuery).getData();
    }

    public String formattingErrorMsg(@NotNull String errorMsg) {
        boolean objectNotFind = errorMsg.contains("ObjectDataNotFoundException: ");
        boolean contains = errorMsg.contains("ValidateException: ");
        if (!contains && !objectNotFind) {
            return null;
        }
        String substring = errorMsg.substring(errorMsg.lastIndexOf(":") + 1);
        String trim = substring.trim();
        if (StringUtils.isBlank(trim)) {
            trim = I18N.text(SFA_GET_WARN_INFO);
        }
        return trim;
    }

    public IObjectData getDataByCode(User user, String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return findDataByField(user, ProductCategoryModel.Filed.CODE, code);
    }

    public String getCategoryIdByCode(User user, String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        IObjectData data = findDataByField(user, ProductCategoryModel.Filed.CODE, code);
        if (data == null) {
            return "";
        }
        return data.getId();
    }

    /**
     * <code, categoryId>
     *
     * @param user
     * @param codeList
     * @return
     */
    public Map<String, String> getCategoryIdByCodes(User user, List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return Maps.newHashMap();
        }
        List<IObjectData> dataList = findDataListByFields(user, ProductCategoryModel.Filed.CODE, codeList);
        if (CollectionUtils.isEmpty(dataList)) {
            return Maps.newHashMap();
        }
        return dataList.stream().collect(Collectors.toMap(d -> ObjectDataUtils.getValueOrDefault(d, CODE, ""), DBRecord::getId));
    }

    /**
     * <categoryId, code>
     *
     * @param user
     * @param idList
     * @return
     */
    public Map<String, String> getCodeByCategoryIds(User user, List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Maps.newHashMap();
        }
        List<IObjectData> dataList = findDataListByFields(user, DBRecord.ID, idList);
        if (CollectionUtils.isEmpty(dataList)) {
            return Maps.newHashMap();
        }
        return dataList.stream().collect(Collectors.toMap(DBRecord::getId, d -> ObjectDataUtils.getValueOrDefault(d, "code", "")));
    }

    public String getCodeById(User user, String id) {
        if (StringUtils.isBlank(id)) {
            return "";
        }
        IObjectData data = findDataByField(user, DBRecord.ID, id);
        if (data == null) {
            return "";
        }
        return ObjectDataUtils.getValueOrDefault(data, "code", "");
    }

    private IObjectData findDataByField(User user, String field, String code) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        IFilter filter = new Filter();
        filter.setFieldName(field);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(code));
        searchTemplateQuery.setFilters(Lists.newArrayList(filter));
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, ProductCategoryModel.Metadata.API_NAME, searchTemplateQuery).getData();
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0);
        }
        return null;
    }

    private List<IObjectData> findDataListByFields(User user, String field, List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(0);
        IFilter filter = new Filter();
        filter.setFieldName(field);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(codeList);
        searchTemplateQuery.setFilters(Lists.newArrayList(filter));
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        return metaDataFindServiceExt.findBySearchQueryIgnoreAll(user, ProductCategoryModel.Metadata.API_NAME, searchTemplateQuery).getData();
    }

    public long getTenantCategoryLimit(User user) {
        String limitTotal = configService.findTenantConfig(user, ConfigType.CATEGORY_TOTAL_LIMIT.getKey());
        if (StringUtils.isBlank(limitTotal)) {
            return 5000L;
        }
        return Long.parseLong(limitTotal);
    }

    /**
     * 是否关闭老的产品分类
     *
     * @param tenantId 企业id
     * @return 是否关闭老分类
     */
    public boolean isCloseOldProductCategory(String tenantId) {
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        String closeOldCategory = configService.findTenantConfig(user, CLOSE_OLD_CATEGORY.getKey());
        return "1".equals(closeOldCategory);
    }
}
