package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.describebuilder.SelectOneFieldDescribeBuilder;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ProductGroupImportViewController extends StandardImportViewController {
    private final List<String> filterFields = Lists.newArrayList("parent_bom_id", "bom_path", "root_id", BomConstants.FIELD_NODE_BOM_CORE_VERSION, BomConstants.FIELD_NODE_BOM_CORE_TYPE, BomConstants.FIELD_IS_PACKAGE);

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        if (arg.getImportType() == IMPORT_TYPE_ADD) {
            fieldDescribes.removeIf(field -> filterFields.contains(field.getApiName()));
            fieldDescribes.stream().filter(x -> x.getApiName().equals("owner")).forEach(x -> x.setRequired(true));
            Map<String, Object> map = Maps.newHashMap();
            map.put("label", I18N.text(BomI18NKeyUtil.SFA_BOM_PATH));
            map.put("api_name", BomConstants.FIELD_SFA_RELATED_PATH_MARK);
            IFieldDescribe field = ImportExportExt.createField(map, "RELATED_MARK", true);
            fieldDescribes.add(0, field);
            if (Objects.equals(describe.getApiName(), Utils.BOM_API_NAME)) {
                Map<String, Object> labelMap = Maps.newHashMap();
                labelMap.put("label", I18N.text("sfa.bom.parent.bom.name"));
                labelMap.put("api_name", "parent_bom_name");
                IFieldDescribe field1 = ImportExportExt.createField(labelMap, "text", false);
                fieldDescribes.add(field1);
                ISelectOption option1 = new SelectOption();
                option1.setValue("true");
                option1.setLabel(I18N.text("BOMObj.field.is_package.option.true"));
                ISelectOption option2 = new SelectOption();
                option2.setValue("false");
                option2.setLabel(I18N.text("BOMObj.field.is_package.option.false"));
                List<ISelectOption> selectOptions = Lists.newArrayList();
                selectOptions.add(option1);
                selectOptions.add(option2);
                SelectOneFieldDescribe selectOneField = SelectOneFieldDescribeBuilder.builder().apiName("root_node_flag").label(I18N.text("sfa.bom.root.node.flag")).selectOptions(selectOptions).defaultValud(false).build();
                fieldDescribes.add(selectOneField);
            }
        }

    }
}
