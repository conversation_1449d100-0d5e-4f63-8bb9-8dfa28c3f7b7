package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.predefine.service.attribute.dao.AttributeRedisDao;
import com.facishare.crm.sfa.utilities.constant.AccountsReceivableConstants;
import com.facishare.crm.sfa.utilities.constant.AccountsReceivableNoteObjConstants;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by luxin on 2017/12/7.
 */
@Slf4j
public class AccountsReceivableNoteCompleteAction extends PreDefineAction<AccountsReceivableNoteCompleteAction.Arg, AccountsReceivableNoteCompleteAction.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("Complete");
    }

    @Override
    protected List<String> getDataPrivilegeIds(AccountsReceivableNoteCompleteAction.Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        Optional<IObjectData> accountsReceivableNote = dataList.stream().filter(r -> r.getId().equals(arg.getObjectDataId())).findFirst();
        if (accountsReceivableNote.isPresent()) {
            if (Objects.equals((AccountsReceivableNoteObjConstants.CompletionStatus.Completed.getStatus()), accountsReceivableNote.get().get(AccountsReceivableNoteObjConstants.Field.CompletionStatus.apiName, String.class))) {
                // 对象已开启不可重复操作
                throw new ValidateException(I18N.text("sfa.accoutsreceivable.completed"));
            }
        } else {
            // 对象不存在或已被删除
            throw new ValidateException(I18N.text("sfa.attributevalue.status.deleted"));
        }
    }

    @Override
    protected AccountsReceivableNoteCompleteAction.Result doAct(AccountsReceivableNoteCompleteAction.Arg arg) {
        Optional<IObjectData> accountsReceivableNote = dataList.stream().filter(r -> r.getId().equals(arg.getObjectDataId())).findFirst();
        if (accountsReceivableNote.isPresent()) {
            accountsReceivableNote.get().set(AccountsReceivableNoteObjConstants.Field.CompletionStatus.apiName, AccountsReceivableNoteObjConstants.CompletionStatus.Completed.getStatus());
            if (Strings.isEmpty(arg.getCompletionTime())) {
                accountsReceivableNote.get().set(AccountsReceivableNoteObjConstants.Field.CompletionTime.apiName, System.currentTimeMillis());
            } else {
                accountsReceivableNote.get().set(AccountsReceivableNoteObjConstants.Field.CompletionTime.apiName, Long.parseLong(arg.getCompletionTime()));
            }

            serviceFacade.updateObjectData(actionContext.getUser(), accountsReceivableNote.get());
            return AccountsReceivableNoteCompleteAction.Result.builder().errorCode("0").value(true).build();
        } else {
            return AccountsReceivableNoteCompleteAction.Result.builder().errorCode("0").value(false).build();
        }
    }

    @Override
    protected AccountsReceivableNoteCompleteAction.Result after(AccountsReceivableNoteCompleteAction.Arg arg, AccountsReceivableNoteCompleteAction.Result result) {
        String logContent = I18N.text("sfa.accounts.receivable.note.completed.logcontent");
        serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.MODIFY, objectDescribe, dataList, logContent);
        return super.after(arg, result);
    }

    @Data
    public static class Arg {
        private String objectDataId;
        private String completionTime;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String errorDetail;
        private String errorCode;
        private String message;
        private Boolean value;

        public boolean isSuccess() {
            return "0".equals(errorCode) && value;
        }
    }
}
