package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.utilities.util.imports.ImportSoUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

public class QuoteLinesUpdateImportTemplateAction extends StandardUpdateImportTemplateAction {
    private static final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitService.class);

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        ImportSoUtil.removeFields(headerFieldList, ImportSoUtil.DETAIL_REMOVE_FIELD_BASE);
        if (multiUnitService.isOpenMultiUnit(actionContext.getTenantId())) {
            ImportSoUtil.removeFields(headerFieldList, ImportSoUtil.SALES_ORDER_PRODUCT_FILTER_FIELDS);
        }
        ImportSoUtil.removeFields(headerFieldList, ImportSoUtil.ATTRIBUTE_FILTER_FIELDS);
        ImportSoUtil.removeFields(headerFieldList, ImportSoUtil.PERIODIC_PRODUCT_FILTER_FIELDS);
        ImportSoUtil.removeFields(headerFieldList, ImportSoUtil.NON_STANDARD_PRODUCT_FIELD);
        ImportSoUtil.removeFields(headerFieldList, ImportSoUtil.NON_STANDARD_PRODUCT_FIELD);
    }
}