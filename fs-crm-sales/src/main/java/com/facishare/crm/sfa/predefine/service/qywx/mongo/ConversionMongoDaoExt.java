package com.facishare.crm.sfa.predefine.service.qywx.mongo;

import com.facishare.crm.constants.WechatSessionConstant;
import com.facishare.crm.sfa.lto.qywx.mongo.ConversionMongoDao;
import com.facishare.crm.sfa.lto.qywx.mongo.WechatConversion;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel;
import com.facishare.crm.sfa.predefine.service.model.qywx.GetSecretSessionDetailModel;
import com.facishare.crm.sfa.predefine.service.model.qywx.GetSecretSessionDetailModel.Direction;
import com.facishare.crm.sfa.predefine.service.model.qywx.GetSecretSessionDetailModel.FuzzyWordInfo;
import com.facishare.crm.sfa.predefine.service.qywx.WechatCustomSqlService;
import com.facishare.crm.util.Safes;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.query.CriteriaContainer;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class ConversionMongoDaoExt {

    private static final String DESC_PREFIX = "-";

    @Resource
    private ConversionMongoDao conversionMongoDao;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private WechatCustomSqlService wechatCustomSqlService;

    public List<WechatConversion> queryListByFromToId(String tenantId, String fromId, String toId, Long startTime,
                                                      Long endTime, String type, String fuzzyWord, Integer seq, Integer limit) {

        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        conversionMongoDao.fillFromToCriteria(fromId, toId, query);
        query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
        if (seq != null) {
            CriteriaContainer seqCriteria = query.and(query.criteria(WechatConversion.FIELD_SEQ).lessThanOrEq(seq));
            query.and(seqCriteria);
        }

        if (StringUtils.isNotEmpty(type)) {
            CriteriaContainer typeCriteria = query.and(query.criteria(WechatConversion.FIELD_MESSAGE_TYPE).equal(type));
            query.and(typeCriteria);
        }
        if (StringUtils.isNotEmpty(fuzzyWord)) {
            CriteriaContainer fuzzyCriteria = query.and(query.criteria(WechatConversion.FIELD_CONTENT)
                    .contains(fuzzyWord));
            query.and(fuzzyCriteria);
        }
        if (startTime != null && endTime != null) {
            CriteriaContainer startTimeCriteria = query.and(query.criteria(WechatConversion.FIELD_MESSAGE_TIME)
                    .greaterThanOrEq(startTime));
            CriteriaContainer endTimeCriteria = query.and(query.criteria(WechatConversion.FIELD_MESSAGE_TIME)
                    .lessThan(endTime));
            query.and(startTimeCriteria, endTimeCriteria);
        }
        query.order(DESC_PREFIX.concat(WechatConversion.FIELD_SEQ));
        if (StringUtils.isEmpty(fuzzyWord)) {
            query.limit(limit);
        }
        return query.asList();
    }


    public List<WechatConversion> queryListByFromToIdRoll(String tenantId, String fromId, String toId, Long startTime,
                                                          Long endTime, String type, Integer seq, Integer limit, Integer order) {

        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        conversionMongoDao.fillFromToCriteria(fromId, toId, query);
        query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
        if (seq != null) {
            CriteriaContainer seqCriteria = null;
            if (order == 1) {
                seqCriteria = query.and(query.criteria(WechatConversion.FIELD_SEQ).greaterThanOrEq(seq));
            } else {
                seqCriteria = query.and(query.criteria(WechatConversion.FIELD_SEQ).lessThanOrEq(seq));
            }
            query.and(seqCriteria);
        }

        if (StringUtils.isNotEmpty(type)) {
            CriteriaContainer typeCriteria = query.and(query.criteria(WechatConversion.FIELD_MESSAGE_TYPE).equal(type));
            query.and(typeCriteria);
        }

        if (startTime != null && endTime != null) {
            CriteriaContainer startTimeCriteria = query.and(query.criteria(WechatConversion.FIELD_MESSAGE_TIME)
                    .greaterThanOrEq(startTime));
            CriteriaContainer endTimeCriteria = query.and(query.criteria(WechatConversion.FIELD_MESSAGE_TIME)
                    .lessThan(endTime));
            query.and(startTimeCriteria, endTimeCriteria);
        }
        query.order(DESC_PREFIX.concat(WechatConversion.FIELD_SEQ));
        query.limit(limit);
        return query.asList();
    }

    public List<WechatConversion> queryListByFromToIdSepecial(String tenantId, String fromId, String toId, Long startTime,
                                                              Long endTime, String type, Integer fuzzyWordSeq, Integer limit, String flag) {

        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        // ((from_user_cipher = fromCipherId and to_list_cipher = toCipherId) or
        // 	(from_user_cipher = toCipherId and to_list_cipher = fromCipherId) )
        CriteriaContainer fromToCriteria = query.or(
                query.and(
                        query.criteria(WechatConversion.FIELD_FROM_USER).equal(fromId),
                        query.criteria(WechatConversion.FIELD_TO_LIST).equal(toId)
                ),
                query.and(
                        query.criteria(WechatConversion.FIELD_FROM_USER).equal(toId),
                        query.criteria(WechatConversion.FIELD_TO_LIST).equal(fromId)
                )
        );

        if (StringUtils.isNotEmpty(type)) {
            CriteriaContainer typeCriteria = query.and(query.criteria(WechatConversion.FIELD_MESSAGE_TYPE).equal(type));
            query.and(typeCriteria);
        }
        if (startTime != null && endTime != null) {
            CriteriaContainer startTimeCriteria = query.and(query.criteria(WechatConversion.FIELD_MESSAGE_TIME)
                    .greaterThanOrEq(startTime));
            CriteriaContainer endTimeCriteria = query.and(query.criteria(WechatConversion.FIELD_MESSAGE_TIME)
                    .lessThan(endTime));
            query.and(startTimeCriteria, endTimeCriteria);
        }
        query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
        if (fuzzyWordSeq != null) {
            CriteriaContainer seqCriteria = null;
            if ("before".equals(flag)) {
                seqCriteria = query.and(query.criteria(WechatConversion.FIELD_SEQ).lessThanOrEq(fuzzyWordSeq));
            } else {
                seqCriteria = query.and(query.criteria(WechatConversion.FIELD_SEQ).greaterThan(fuzzyWordSeq));
            }
            CriteriaContainer roomIdCriteria = query.and(query.criteria(WechatConversion.FIELD_ROOM_ID).equal(null));
            query.and(fromToCriteria, seqCriteria, roomIdCriteria);
        }
        if ("after".equals(flag)) {
            query.order(WechatConversion.FIELD_SEQ);
        } else {
            query.order(DESC_PREFIX.concat(WechatConversion.FIELD_SEQ));
        }
        query.limit(limit / 2);
        return query.asList();
    }


    public List<WechatConversion> queryListByFromToId(String tenantId, String fromId, String toId, Integer seq, Integer order) {
        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        CriteriaContainer fromToCriteria = query.or(
                query.and(
                        query.criteria(WechatConversion.FIELD_FROM_USER).equal(fromId),
                        query.criteria(WechatConversion.FIELD_TO_LIST).equal(toId)
                ),
                query.and(
                        query.criteria(WechatConversion.FIELD_FROM_USER).equal(toId),
                        query.criteria(WechatConversion.FIELD_TO_LIST).equal(fromId)
                )
        );
        CriteriaContainer seqCriteria;
        if (order == 1) {
            seqCriteria = query.and(query.criteria(WechatConversion.FIELD_SEQ).lessThan(seq));
        } else {
            seqCriteria = query.and(query.criteria(WechatConversion.FIELD_SEQ).greaterThanOrEq(seq));
        }

        CriteriaContainer roomIdCriteria = query.and(query.criteria(WechatConversion.FIELD_ROOM_ID).equal(null));

        query.and(fromToCriteria, seqCriteria, roomIdCriteria);
        query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();

        query.order(DESC_PREFIX.concat(WechatConversion.FIELD_SEQ));
        query.limit(10);
        return query.asList();
    }

    public List<WechatConversion> queryListByRoomId(String tenantId, String roomId, String type, Long startTime,
                                                    Long endTime, String fuzzyWord, Integer seq, Integer limit) {
        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        query.field(WechatConversion.FIELD_ROOM_ID).equal(roomId);
        if (StringUtils.isNotEmpty(type)) {
            query.field(WechatConversion.FIELD_MESSAGE_TYPE).equal(type);
        }
        if (StringUtils.isNotEmpty(fuzzyWord)) {
            query.field(WechatConversion.FIELD_CONTENT).contains(fuzzyWord);
        }
        if (startTime != null && endTime != null) {
            query.field(WechatConversion.FIELD_MESSAGE_TIME).greaterThanOrEq(startTime);
            query.field(WechatConversion.FIELD_MESSAGE_TIME).lessThan(endTime);
        }
        // 不传seq默认从最后一条开始往上查
        if (seq != null) {
            query.field(WechatConversion.FIELD_SEQ).lessThanOrEq(seq);
        }
        query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
        query.order(DESC_PREFIX.concat(WechatConversion.FIELD_SEQ));
        if (StringUtils.isEmpty(fuzzyWord)) {
            query.limit(limit);
        }
        return query.asList();
    }

    public List<WechatConversion> queryListByRoomIdRoll(String tenantId, String roomId, String type, Long startTime,
                                                        Long endTime, Integer seq, Integer limit, Integer order) {
        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        query.field(WechatConversion.FIELD_ROOM_ID).equal(roomId);
        if (StringUtils.isNotEmpty(type)) {
            query.field(WechatConversion.FIELD_MESSAGE_TYPE).equal(type);
        }
        if (startTime != null && endTime != null) {
            query.field(WechatConversion.FIELD_MESSAGE_TIME).greaterThanOrEq(startTime);
            query.field(WechatConversion.FIELD_MESSAGE_TIME).lessThan(endTime);
        }
        // 不传seq默认从最后一条开始往上查
        if (seq != null) {
            if (order == 1) {
                query.field(WechatConversion.FIELD_SEQ).greaterThanOrEq(seq);
            } else {
                query.field(WechatConversion.FIELD_SEQ).lessThanOrEq(seq);
            }
        }
        query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
        query.order(DESC_PREFIX.concat(WechatConversion.FIELD_SEQ));
        query.limit(limit);
        return query.asList();
    }

    public List<WechatConversion> queryListByRoomIdSpecial(String tenantId, String roomId, String type, Long startTime,
                                                           Long endTime, Integer fuzzyWordSeq, Integer limit, String flag) {
        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        query.field(WechatConversion.FIELD_ROOM_ID).equal(roomId);
        if (StringUtils.isNotEmpty(type)) {
            query.field(WechatConversion.FIELD_MESSAGE_TYPE).equal(type);
        }
        if (startTime != null && endTime != null) {
            query.field(WechatConversion.FIELD_MESSAGE_TIME).greaterThanOrEq(startTime);
            query.field(WechatConversion.FIELD_MESSAGE_TIME).lessThan(endTime);
        }
        // 不传seq默认从最后一条开始往上查
        if (fuzzyWordSeq != null) {
            if ("before".equals(flag)) {
                query.field(WechatConversion.FIELD_SEQ).lessThanOrEq(fuzzyWordSeq);
            } else {
                query.field(WechatConversion.FIELD_SEQ).greaterThan(fuzzyWordSeq);
            }
        }
        query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
        if ("after".equals(flag)) {
            query.order(WechatConversion.FIELD_SEQ);
        } else {
            query.order(DESC_PREFIX.concat(WechatConversion.FIELD_SEQ));
        }
        query.limit(limit / 2);
        return query.asList();
    }


    public List<WechatConversion> queryListByRoomId(String tenantId, String roomId, Integer seq, Integer order) {

        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        query.field(WechatConversion.FIELD_ROOM_ID).equal(roomId);
        // 不传seq默认从最后一条开始往上查
        //if (seq != null) {
        if (order == 1) {
            query.field(WechatConversion.FIELD_SEQ).lessThan(seq);
        } else {
            query.field(WechatConversion.FIELD_SEQ).greaterThanOrEq(seq);
        }
        query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
        query.order(DESC_PREFIX.concat(WechatConversion.FIELD_SEQ));
        query.limit(10);
        return query.asList();
    }

    public List<WechatConversion> queryListByfuzzyWord(String tenantId, String fuzzyWord, Long startTime, Long endTime) {
        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        if (StringUtils.isNotEmpty(fuzzyWord)) {
            query.field(WechatConversion.FIELD_CONTENT).contains(fuzzyWord);
        }
        if (startTime != null && endTime != null) {
            query.field(WechatConversion.FIELD_MESSAGE_TIME).greaterThanOrEq(startTime);
            query.field(WechatConversion.FIELD_MESSAGE_TIME).lessThan(endTime);
        }
        query.field(WechatConversion.FIELD_DESCRY_KEY).doesNotExist();
        List<WechatConversion> wechatConversions = query.asList();
        return wechatConversions;
    }

    public WechatConversion queryById(String tenantId, String id) {
        Query<WechatConversion> tenantQuery = conversionMongoDao.createTenantQuery(tenantId);
        return tenantQuery.field("messageId").equal(id).get();
    }

    public List<WechatConversion> querySecretList(String tenantId, GetSecretSessionDetailModel.Arg arg) {
        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        if (Safes.isNotEmpty(arg.getRoomId())) {
            query.field(WechatConversion.FIELD_ROOM_ID).equal(arg.getRoomId());
        } else {
            conversionMongoDao.fillFromToCriteria(arg.getOwnerCipherId(), arg.getOppositeCipherId(), query);
        }
        query.field(WechatConversion.FIELD_DESCRY_KEY).notEqual(null);

        FuzzyWordInfo fuzzyWordInfo = arg.getFuzzyWordInfo();
        if (fuzzyWordInfo != null && Safes.isNotEmpty(fuzzyWordInfo.getFuzzyWord())) {
            if (Safes.isEmpty(fuzzyWordInfo.getCurrentMessageId())) {
                return Collections.emptyList();
            } else {
                /*
                 * 从当前消息开始，默认向上查20条数据
                 */
                WechatConversion wechatConversion = this.queryById(tenantId, fuzzyWordInfo.getCurrentMessageId());
                if (wechatConversion == null) {
                    return Collections.emptyList();
                }
                Long messageTime = wechatConversion.getMessageTime();
                if (Direction.UP.toString().equals(arg.getDirection())) {
                    query.field(WechatConversion.FIELD_MESSAGE_TIME).lessThanOrEq(messageTime);
                } else {
                    query.field(WechatConversion.FIELD_MESSAGE_TIME).greaterThan(messageTime);
                }
            }
        } else {
            // 没有模糊词搜索时的时间范围。模糊词搜索的时间范围由企微接口决定
            if (arg.getStartTime() != null && arg.getEndTime() != null) {
                query.field(WechatConversion.FIELD_MESSAGE_TIME).greaterThanOrEq(arg.getStartTime());
                query.field(WechatConversion.FIELD_MESSAGE_TIME).lessThan(arg.getEndTime());
            }
        }
        if (Direction.UP.toString().equals(arg.getDirection())) {
            query.order(DESC_PREFIX.concat(WechatConversion.FIELD_MESSAGE_TIME));
        } else {
            query.order(WechatConversion.FIELD_MESSAGE_TIME);
        }
        query.offset(arg.getOffset());
        query.limit(arg.getLimit());
        return query.asList();
    }


    public QIGetWechatConversionModel.ConversionResult getConversionByPositioning(String tenantId, QIGetWechatConversionModel.PositionArg arg) {
        IObjectData session = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), arg.getSessionId(), SFAPreDefineObject.WechatSession.getApiName());
        long messageTime = arg.getMessageTime() - 1;
        if (arg.getMessageTime() > 10000000000L) {
            messageTime = arg.getMessageTime() / 1000 - 1;
        }
        QIGetWechatConversionModel.ConversionArg secondArg = new QIGetWechatConversionModel.ConversionArg();
        secondArg.setAsc(true);
        secondArg.setGreaterThan(true);
        secondArg.setLimit(arg.getLimit());
        secondArg.setMessageTime(messageTime);
        List<WechatConversion> dataList = findConversionByPage(tenantId, session, secondArg);
        if (!arg.isAsc()) {
            Collections.reverse(dataList);
        }
        return buildConversionResult(session, dataList);
    }

    public QIGetWechatConversionModel.ConversionResult findConversionByPage(String tenantId, QIGetWechatConversionModel.ConversionArg arg) {
        IObjectData session = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), arg.getSessionId(), SFAPreDefineObject.WechatSession.getApiName());
        List<WechatConversion> dataList = findConversionByPage(tenantId, session, arg);
        return buildConversionResult(session, dataList);
    }

    public List<WechatConversion> findConversionByPage(String tenantId, IObjectData session, QIGetWechatConversionModel.ConversionArg arg) {
        String groupId = session.get(WechatSessionConstant.OPPOSITE_PLAINTEXT_ID, String.class);
        String fromId = session.get("owner_cipher_id", String.class);
        String toId = session.get("opposite_cipher_id", String.class);
        Query<WechatConversion> query = conversionMongoDao.createTenantQuery(tenantId);
        query.field(WechatConversion.FIELD_DESCRY_KEY).notEqual(null);
        if (Safes.isNotEmpty(groupId)) {
            query.field(WechatConversion.FIELD_ROOM_ID).equal(groupId);
        } else {
            conversionMongoDao.fillFromToCriteria(fromId, toId, query);
        }
        Long messageTime = arg.getMessageTime();
        if (arg.isGreaterThan()) {
            query.field(WechatConversion.FIELD_MESSAGE_TIME).greaterThan(messageTime);
        } else {
            query.field(WechatConversion.FIELD_MESSAGE_TIME).lessThan(messageTime);
        }
        if (arg.isAsc()) {
            query.order(WechatConversion.FIELD_MESSAGE_TIME);
        } else {
            query.order(DESC_PREFIX.concat(WechatConversion.FIELD_MESSAGE_TIME));
        }
        query.offset(0);
        query.limit(arg.getLimit());
        return query.asList();
    }

    public QIGetWechatConversionModel.ConversionResult buildConversionResult(IObjectData session, List<WechatConversion> dataList) {
        String tenantId = session.getTenantId();
        String groupId = session.get(WechatSessionConstant.WECHAT_GROUP_ID, String.class);
        String owner = session.get("owner_cipher_id", String.class);
        String opposite = session.get("opposite_cipher_id", String.class);
        //会话类型：1外部单聊,2内部单聊,3内部群聊,4外部群聊
        String type = session.get(WechatSessionConstant.TYPE, String.class);
        Map<String, QIGetWechatConversionModel.WechatUser> userMap = new HashMap<>();
        if ("1".equals(type)) {
            userMap.put(owner, QIGetWechatConversionModel.WechatUser.builder().type(1).build());
            userMap.put(opposite, QIGetWechatConversionModel.WechatUser.builder().type(2).build());
        } else if ("2".equals(type)) {
            userMap.put(owner, QIGetWechatConversionModel.WechatUser.builder().type(1).build());
            userMap.put(opposite, QIGetWechatConversionModel.WechatUser.builder().type(1).build());
        } else if ("3".equals(type) || "4".equals(type)) {
            List<Map> groupUserList = wechatCustomSqlService.queryGroupUserList(tenantId, groupId);
            for (Map map : groupUserList) {
                String userId = (String) map.get("user_id");
                int wechatType = 1;
                if (Objects.equals("2", map.get("type"))) {
                    wechatType = 2;
                }
                if (!StringUtils.isEmpty(userId)) {
                    userMap.put(userId, QIGetWechatConversionModel.WechatUser.builder().type(wechatType).build());
                }
            }
        }
        return QIGetWechatConversionModel.ConversionResult.builder()
                .dataList(dataList)
                .userMap(userMap)
                .build();
    }

}
