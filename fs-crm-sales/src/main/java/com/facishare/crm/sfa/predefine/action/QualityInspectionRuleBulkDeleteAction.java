package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.QualityInspectionDBService;
import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleService;
import com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @Date 2022/9/26 17:01
 * @Version 1.0
 **/
@Slf4j
@Idempotent(serializer = Serializer.Type.java)
public class QualityInspectionRuleBulkDeleteAction extends StandardBulkDeleteAction {
    private static final QualityInspectionDBService qualityInspectionDBService = SpringUtil.getContext().getBean(QualityInspectionDBService.class);
    private static final QualityInspectionRuleService qualityInspectionRuleService = SpringUtil.getContext().getBean(QualityInspectionRuleService.class);

    @Override
    protected Result doAct(Arg arg) {
        if (!CollectionUtils.isEmpty(dataList)) {
            for (IObjectData data : dataList) {
                if (QIGetWechatConversionModel.record_dirtyword_layout.equals(data.getRecordType())) {
                    qualityInspectionRuleService.delRule(actionContext.getUser(), data);
                }
            }
        }
        arg.setDirectDelete(true);
        return super.doAct(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result ret = super.after(arg, result);
        if (ret.getSuccess() && !CollectionUtils.isEmpty(dataList)) {
            for (IObjectData data : dataList) {
                qualityInspectionDBService.deleteRule(actionContext, data);
                qualityInspectionDBService.deleteRuleMember(actionContext, data.getId());
            }
        }
        return ret;
    }
}