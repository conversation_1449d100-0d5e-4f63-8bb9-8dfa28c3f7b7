package com.facishare.crm.sfa.predefine.bizvalidator.validator;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class PriceBookProductScreeningConditionValidator {
    ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    /**
     * 验证当前传输商品的Filters
     */
    public void validateProductFilters(User user,String productId) {
        // 获取价目表明细 查看企业是否配置 可选择的数据范围
        IObjectDescribe PriceBookProductObjectDescribe = serviceFacade.findObject(user.getTenantId(), SFAPreDefineObject.PriceBookProduct.getApiName());
        IFieldDescribe fieldDescribe = PriceBookProductObjectDescribe.getFieldDescribe("product_id");
        List<Wheres> wheresList = Lists.newArrayList();
        convert2WheresList(fieldDescribe.get("wheres",String.class),wheresList);
        if(CollectionUtils.size(wheresList) > 0) {
            SearchTemplateQuery searchQuery = new SearchTemplateQuery();
            List filters = Lists.newLinkedList();
            SearchUtil.fillFiltersWithUser(user, filters);
            SearchUtil.fillFilterEq(filters, DBRecord.ID, productId);
            searchQuery.setFilters(filters);
            searchQuery.setWheres(wheresList);
            // 灰度es后存在es与pg同步不及时情况，目前这个查询强制走db
            QueryResult<IObjectData> queryResult = this.serviceFacade.findBySearchQueryIgnoreAll(user, Utils.PRODUCT_API_NAME, searchQuery);
            if (null == queryResult || CollectionUtil.isEmpty(queryResult.getData())) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PRICEBOOKPRODUCT_PRODUCT_WHERESFILTERE));
            }
        }
    }

    private static void convert2WheresList(String wheresString, List<Wheres> wheresList) {
        if (StringUtils.isEmpty(wheresString)) {
            return;
        }
        List<JSONObject> wheresJSONObjectList = JSON.parseObject(wheresString, List.class);
        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(wheresJSONObjectList)) {
            for (JSONObject jsonObject : wheresJSONObjectList) {
                Wheres wheres = JSON.parseObject(jsonObject.toJSONString(), Wheres.class);
                if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(wheres.getFilters())) {
                    for (IFilter filter : wheres.getFilters()) {
                        if (filter.getFieldValues() == null) {
                            filter.setFieldValues(Lists.newArrayList());
                        }
                    }
                }
                wheresList.add(wheres);
            }
        }
    }
}
