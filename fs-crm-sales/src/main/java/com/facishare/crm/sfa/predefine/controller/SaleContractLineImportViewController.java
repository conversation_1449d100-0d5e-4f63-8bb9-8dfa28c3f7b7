package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.SaleContractImportUtil;
import com.facishare.crm.sfa.utilities.util.imports.ImportSoUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class SaleContractLineImportViewController extends StandardImportViewController {

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        if(arg.getImportType() == IMPORT_TYPE_ADD){
            if (StringUtils.equals(describe.getApiName(), Utils.SALE_CONTRACT_LINE_API_NAME)) {
                SaleContractImportUtil.removeDetailImportFields(fieldDescribes);
            }
        }
        if (arg.getImportType() == IMPORT_TYPE_EDIT) {
            if (StringUtils.equals(describe.getApiName(), Utils.SALE_CONTRACT_LINE_API_NAME)) {
                SaleContractImportUtil.removeDetailImportFields(fieldDescribes);
                ImportSoUtil.removeFields(fieldDescribes, ImportSoUtil.NON_STANDARD_PRODUCT_FIELD);
            }
            
        }
    }
}
