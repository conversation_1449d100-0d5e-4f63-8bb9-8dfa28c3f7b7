package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.predefine.service.QualityInspectionDBService;
import com.facishare.crm.sfa.predefine.service.QualityInspectionRuleService;
import com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel;
import com.facishare.crm.sfa.predefine.service.model.quality.QIGetWechatConversionModel.QIUser;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2022/9/26 17:01
 * @Version 1.0
 **/
@Slf4j
@Idempotent(serializer = Serializer.Type.java)
public class QualityInspectionRuleEditAction extends StandardEditAction {
    private static final QualityInspectionRuleService qualityInspectionRuleService = SpringUtil.getContext().getBean(QualityInspectionRuleService.class);
    private static final QualityInspectionDBService qualityInspectionDBService = SpringUtil.getContext().getBean(QualityInspectionDBService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        IObjectData data = arg.getObjectData().toObjectData();
        String record_type = data.getRecordType();
        if (QIGetWechatConversionModel.record_dirtyword_layout.equals(record_type)) {
            data.set("type", 1);
            data.set("session_feature", "1");
            data.set("session_action", "1");
            data.set("sync_status", "edit_pre");
            //data.put("record_type", record_dirtyword_layout);
            //data.put("layout_api_name", dirtyword_layout_api);
        } else if (record_type.contains("action")) {
            data.set("type", 2);
            data.set("dirty_words", "{}");
            data.set("session_feature", "1");
            data.set("session_action", String.valueOf(data.get("session_action")));
            //data.put("record_type", record_action_layout);
            //data.put("layout_api_name", action_layout_api);
        } else if (record_type.contains("feature")) {
            data.set("type", 3);
            data.set("dirty_words", "{}");
            data.set("session_feature", String.valueOf(data.get("session_feature")));
            data.set("session_action", "1");
            //data.put("record_type", record_feature_layout);
            //data.put("layout_api_name", feature_layout_api);
        }
        data.setOwner(Lists.newArrayList(actionContext.getUser().getUpstreamOwnerIdOrUserId()));
        log.warn("QualityInspectionRuleEditAction before {}", data);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ObjectDataDocument dataDocument = arg.getObjectData();
        IObjectData data = dataDocument.toObjectData();
        qualityInspectionRuleService.syncRule(actionContext.getUser(), data);
        QIUser monitors = JSON.parseObject(String.valueOf(data.get("monitors")), QIUser.class);
        QIUser msgUser = JSON.parseObject(String.valueOf(data.get("msg_user")), QIUser.class);

        String ruleId = data.getId();
        qualityInspectionRuleService.updateRule(actionContext, ruleId, dataDocument);
        qualityInspectionDBService.deleteRuleMember(actionContext, ruleId);
        qualityInspectionRuleService.addRuleMember(actionContext, monitors, msgUser, ruleId);
        qualityInspectionRuleService.putRecordLayout((Integer) data.get("type"), dataDocument);
        return result;
    }
}