package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;


@Slf4j
public class NonstandardAttributeDescribeLayoutController extends SFADescribeLayoutController {

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        if (Objects.equals(arg.getLayout_type(), LAYOUT_TYPE_EDIT)) {
            PreDefLayoutUtil.setFormComponentFieldsReadOnly(formComponent, Lists.newArrayList("type"));
        }
    }
}