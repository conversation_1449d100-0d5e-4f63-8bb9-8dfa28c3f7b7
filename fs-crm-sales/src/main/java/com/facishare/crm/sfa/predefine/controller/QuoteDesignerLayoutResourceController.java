package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutResourceController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;


public class QuoteDesignerLayoutResourceController extends StandardDesignerLayoutResourceController {
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected List<IButton> processDetailObjectButtons(List<IButton> detailObjButtons, IObjectDescribe detailDescribe, IObjectDescribe masterDescribe) {
        super.processDetailObjectButtons(detailObjButtons,detailDescribe,masterDescribe);
        if (Utils.QUOTE_LINES_API_NAME.equals(detailDescribe.getApiName()) && bizConfigThreadLocalCacheService.isOpenNonStandardProduct(controllerContext.getTenantId())) {
            detailObjButtons.add(ButtonUtils.buildButton(ObjectAction.ADD_NON_STANDARD_PRODUCT));
        }
        return detailObjButtons;
    }

}
