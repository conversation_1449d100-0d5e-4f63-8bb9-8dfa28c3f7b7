package com.facishare.crm.sfa.utilities.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitService;
import com.facishare.crm.sfa.model.AccountMainDataModel;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.ObjectLimitRuleModel;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.*;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.controlstrategy.ControlStrategyService;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.predefine.service.push.ContactSessionSandwichService;
import com.facishare.crm.sfa.predefine.service.real.SFARecyclingService;
import com.facishare.crm.sfa.predefine.service.real.SFARecyclingServiceImpl;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ExceptionUtils;
import com.facishare.crm.sfa.task.RecalculateProducer;
import com.facishare.crm.sfa.task.RelationOperationType;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.*;
import com.facishare.crm.sfa.utilities.proxy.FeedsProxy;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.proxy.model.FeedsModel;
import com.facishare.crm.util.CommonSqlUtils;
import com.facishare.crm.util.Safes;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ObjectLockStatus;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardMergeAction;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.sfa.CustomerLimit;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.privilege.FieldPrivilegeServiceImpl;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISpecifiedTableParameter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IGroupComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.enterpriserelation2.arg.GetErVisitorArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.GetErVisitorResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseMetaDataService;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;
import static com.facishare.paas.metadata.api.action.ActionContextKey.SKIP_OBJECT_REFERENCE_EXIST_VALID;

/**
 * Created by renlb on 2018/11/19.
 */
@Slf4j
public class AccountUtil {
    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private static final AccountPoolServiceImpl ACCOUNT_POOL_SERVICE_IMPL = SpringUtil.getContext().getBean(AccountPoolServiceImpl.class);
    private static final ObjectPoolServiceManager objectPoolServiceManager = SpringUtil.getContext().getBean(ObjectPoolServiceManager.class);
    private static final TeamMemberService teamMemberService = SpringUtil.getContext().getBean(TeamMemberService.class);
    private static final IObjectDescribeService objectDescribeService = SpringUtil.getContext().getBean(ObjectDescribeServiceImpl.class);
    private static final CRMNotificationServiceImpl crmNotificationService = SpringUtil.getContext().getBean("crmNotificationService", CRMNotificationServiceImpl.class);
    private static final ContactSessionSandwichService contactSessionSandwichService = SpringUtil.getContext().getBean(ContactSessionSandwichService.class);
    private static final SFARecyclingService sfaRecyclingService = SpringUtil.getContext().getBean(SFARecyclingServiceImpl.class);
    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    private static final ICommonSqlService commonSqlService = SpringUtil.getContext().getBean(CommonSqlServiceImpl.class);
    private static final FeedsProxy feedsProxy = SpringUtil.getContext().getBean(FeedsProxy.class);
    private static final RecalculateProducer recalculateProducer = SpringUtil.getContext().getBean(RecalculateProducer.class);
    private static final ObjectLimitService objectLimitService = SpringUtil.getContext().getBean(ObjectLimitService.class);
    private static final InfraServiceFacade infraServiceFacade = SpringUtil.getContext().getBean(InfraServiceFacade.class);
    private static final AccountMainDataService accountMainDataService = SpringUtil.getContext().getBean(AccountMainDataService.class);
    private static final EnterpriseMetaDataService enterpriseMetaDataService = SpringUtil.getContext().getBean(EnterpriseMetaDataService.class);
    private static final EIEAConverter eieaConverter = SpringUtil.getContext().getBean(EIEAConverter.class);
    private static final ControlStrategyService controlStrategyService = SpringUtil.getContext().getBean(ControlStrategyService.class);
    private static final FieldPrivilegeServiceImpl FIELD_PRIVILEGE_SERVICE = SpringUtil.getContext().getBean(FieldPrivilegeServiceImpl.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private static final MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean(MetaDataFindServiceExt.class);

    private static final Long ONE_DAY_TIME_STAMP = 24 * 60 * 60 * 1000L;
    private static final int PAGE_SIZE = 1000;

    private static final String MULTI_CURRENCY_KEY = "multi_currency_config";

    public static final List<String> INHERIT_FIELD_LIST = Lists.newArrayList("fax","url","email","tel","industry_level2","industry_level1","account_type","business_scope","registration_status","registered_capital","legal_representative","enterprise_type","uniform_social_credit_code","name");

    /**即使灰度了主数据*/
    public static final List<String> INHERIT_FIELD_NOT_SHOW_DOWNLOAD_TEMPLATE_LIST = Lists.newArrayList("pin_yin","biz_reg_name", "industry_ext");

    //可售范围和价目表选客户
    private static final List<String> RELATED_LIST_NAME = Lists.newArrayList("account_available_account_list", "account_price_book_account_list");

    /**
     * 继承字段Name字段暂时不放开
     */
    public static final String NAME = "name";


    public static String getAccountIdByOutTenantId(String tenantId, String outTenantId, String outUserId) {
        if (Objects.equals(outTenantId, "**********") && Objects.equals(outUserId, "**********")) {
            return getErVisitorAccountId(Integer.parseInt(tenantId), outTenantId, outUserId);
        }
        return PartnerUtil.getCrmMapperByOuterTenantIds(tenantId, Utils.ACCOUNT_API_NAME, Long.valueOf(outTenantId));
    }

    /**
     * 游客角色，获取客户id
     */
    public static String getErVisitorAccountId(Integer tenantId, String outTenantId, String outUserId) {
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, Long.valueOf(outTenantId), Long.valueOf(outUserId));
        GetErVisitorArg getErVisitorArg = new GetErVisitorArg();
        getErVisitorArg.setUpstreamEa(eieaConverter.enterpriseIdToAccount(tenantId));
        RestResult<GetErVisitorResult> result = enterpriseMetaDataService.getErVisitor(headerObj, getErVisitorArg);
        if (result.getData() == null || result.getData().getErVisitorBindAccountId() == null) {
            log.warn("getErVisitor null, headerObj:{}, getErVisitorArg:{}, result:{}", headerObj, getErVisitorArg, result);
            return "";
        }
        return result.getData().getErVisitorBindAccountId();
    }

    public static void handleRemainingTime(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        long defaultValue = 946656000000L;
        long currentTimeMillis = System.currentTimeMillis();
        long base = 24 * 3600 * 1000L;
        long baseHour = 3600 * 1000L;

        for (IObjectData objectData : objectDataList) {
            String value = objectData.get("expire_time", String.class);
            if (Strings.isNullOrEmpty(value) || "0".equals(value)) {
                objectData.set("remaining_time", "");
            } else {
                long expireTimeLongValue = Long.parseLong(value);
                if (expireTimeLongValue > defaultValue) {
                    if (Utils.LEADS_API_NAME.equals(objectData.getDescribeApiName())) {
                        String remainTime = String.valueOf((expireTimeLongValue - currentTimeMillis) / baseHour);
                        objectData.set("remaining_time", remainTime);
                    } else {
                        String remainTime = String.valueOf((expireTimeLongValue - currentTimeMillis) / base);
                        objectData.set("remaining_time", remainTime);
                    }
                    objectData.set("detail_remaining_time", longTime2String(expireTimeLongValue - System.currentTimeMillis()));
                } else {
                    objectData.set("remaining_time", "");
                }
            }
        }
    }

    public static void handleRemainingTimeDesc(IObjectDescribe describe, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        handleRemainingTime(objectDataList);
        for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
            if ("remaining_time".equals(fieldDescribe.getApiName())) {
                fieldDescribe.set("data_help_text", objectDataList.get(0).get("detail_remaining_time", String.class));
            }
        }
    }

    private static String longTime2String(Long time) {
        String format;
        time = time / 1000;
        Object[] array;
        int days = (int) (time / (60 * 60 * 24));
        int hours = (int) (time / (60 * 60) - days * 24);
        if (days > 0) {
            format = "%1$,d"+I18N.text("sfa.day")+"%2$,d"+I18N.text("sfa.hour");
            array = new Object[]{days, hours};
        } else if (hours > 0) {
            format = "%1$,d"+I18N.text("sfa.hour");
            array = new Object[]{hours};
        } else {
            return null;
        }
        return String.format(format, array);
    }

    public static String getOutOwner(ObjectDataDocument dataDocument) {
        IObjectData objectData = dataDocument.toObjectData();
        return getOutOwner(objectData);
    }

    public static String getOutOwner(IObjectData objectData) {
        List ownerList = objectData.getOutOwner();
        String owner = "";
        if (CollectionUtils.notEmpty(ownerList)) {
            owner = String.valueOf(ownerList.get(0));
        }
        return owner;
    }

    public static String getEmployee(ObjectDataDocument dataDocument, String employeeField) {
        List<String> employeeList = (List<String>) dataDocument.getOrDefault(employeeField, Lists.newArrayList());
        String result = "";
        if (CollectionUtils.notEmpty(employeeList)) {
            result = employeeList.get(0);
        }
        return result;
    }

    public static String getEmployee(IObjectData objectData, String employeeField) {
        List<String> employeeList = (List<String>) objectData.get(employeeField);
        String result = "";
        if (CollectionUtils.notEmpty(employeeList)) {
            result = employeeList.get(0);
        }
        return result;
    }

    public static void checkFillingChecker(String tenantId, String fillingCheckerId) {
        if (StringUtils.isEmpty(fillingCheckerId)) {
            return;
        }
        String configValue = SFAConfigUtil.getConfigValue(tenantId, "3", User.SUPPER_ADMIN_USER_ID);
        if ("1".equals(configValue) && Integer.parseInt(fillingCheckerId) <= 0) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_FILLING_CHECKER_ERROR);
        }
    }

    public static void checkAccountLimit(User user, Integer employeeId, String highSeasId) {
        CustomerLimit.Result rstResult = getCustomerLimit(user, employeeId);
        if (rstResult == null) {
            return;
        }
        if (StringUtils.isNotEmpty(highSeasId) && !rstResult.getValue().getIncludeHighSeasCustomer()) {
            return;
        }
        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), employeeId.toString(), rstResult.getValue().getIncludeHighSeasCustomer(), rstResult.getValue().getIncludeDealCustomer());
        if (totalCount >= rstResult.getValue().getLimitNum()) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
        }
    }

    public static void checkAccountLimit(User user, String owner, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        List<String> poolIds = getPoolIds(objectDataList);
        if (CollectionUtils.notEmpty(poolIds)) {
            poolIds.forEach(x -> {
                List<IObjectData> tempDataList = objectDataList.stream().filter(data -> x.equals(getPoolId(data)))
                        .collect(Collectors.toList());
                if (CollectionUtils.notEmpty(tempDataList)) {
                    checkPoolAccountLimit(user, owner, x, tempDataList);
                }
            });
        }
        CustomerLimit.Result rstResult = getCustomerLimit(user, Integer.valueOf(owner));
        if (rstResult == null) {
            return;
        }
        List<IObjectData> dataList = Lists.newArrayList();
        if (rstResult.getValue().getIncludeHighSeasCustomer()) {
            dataList.addAll(objectDataList);
        } else {
            dataList.addAll(objectDataList.stream().filter(x -> StringUtils.isEmpty(getPoolId(x))).collect(Collectors.toList()));
        }

        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<IObjectData> unDealAccountList = getUnDealAccountList(dataList);

        if (!rstResult.getValue().getIncludeDealCustomer() && CollectionUtils.empty(unDealAccountList)) {
            return;
        }
        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), owner, rstResult.getValue().getIncludeHighSeasCustomer(), rstResult.getValue().getIncludeDealCustomer());
        if (totalCount > rstResult.getValue().getLimitNum()) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
        }
        if (rstResult.getValue().getIncludeDealCustomer()) {
            if (totalCount + dataList.size() > rstResult.getValue().getLimitNum()) {
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
            }
        } else {
            if (CollectionUtils.notEmpty(unDealAccountList)) {
                if (totalCount + unDealAccountList.size() > rstResult.getValue().getLimitNum()) {
                    throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
                }
            }
        }
    }

    private static List<IObjectData> getUnDealAccountList(List<IObjectData> dataList) {
        List<IObjectData> unDealAccountList = Lists.newArrayList();
        if (CollectionUtils.empty(dataList)) {
            return unDealAccountList;
        }
        unDealAccountList = dataList.stream()
                .filter(x -> AccountConstants.DealStatus.UN_DEAL.getValue().equals(getStringValue(x, AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue())))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(unDealAccountList)) {
            unDealAccountList = Lists.newArrayList();
        }
        return unDealAccountList;
    }

    @NotNull
    public static List<String> getPoolIds(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return Lists.newArrayList();
        }
        return objectDataList.stream().filter(x -> StringUtils.isNotEmpty(getStringValue(x, AccountConstants.Field.HIGH_SEAS_ID, "")))
                .map(x -> getPoolId(x)).distinct().collect(Collectors.toList());
    }

    public static String getPoolId(IObjectData objectData) {
        return getStringValue(objectData, AccountConstants.Field.HIGH_SEAS_ID, "");
    }

    public static void checkPoolAccountLimit(User user, String owner, String poolId, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        IObjectData poolData = ACCOUNT_POOL_SERVICE_IMPL.getObjectPoolById(user.getTenantId(), poolId);
        if (poolData == null || CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<IObjectData> unDealAccountList = objectDataList.stream()
                .filter(x -> getStringValue(x, AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue()).equals(AccountConstants.DealStatus.UN_DEAL.getValue()))
                .collect(Collectors.toList());

        boolean includeDealCustomer = getBooleanValue(poolData, "is_claim_limit_include_dealed_customers", false);

        if (!includeDealCustomer && CollectionUtils.empty(unDealAccountList)) {
            return;
        }
        Integer claimLimitNum = getIntegerValue(poolData, "claim_limit_num", 0);

        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), owner, poolId, includeDealCustomer);
        if (totalCount > claimLimitNum) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
        }
        if (includeDealCustomer) {
            if (totalCount + objectDataList.size() > claimLimitNum) {
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
            }
        } else {
            if (CollectionUtils.notEmpty(unDealAccountList)) {
                if (totalCount + unDealAccountList.size() > claimLimitNum) {
                    throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
                }
            }
        }
    }

    public static void checkPoolAccountLimitForImport(User user, String owner, String poolId, Map<Integer, IObjectData> importDataMap, List<BaseImportAction.ImportError> errorList) {
        if (CollectionUtils.empty(importDataMap) || StringUtils.isEmpty(owner)) {
            return;
        }
        IObjectData poolData = ACCOUNT_POOL_SERVICE_IMPL.getObjectPoolById(user.getTenantId(), poolId);
        if (poolData == null || CollectionUtils.empty(importDataMap)) {
            return;
        }
        List<Map.Entry<Integer, IObjectData>> unDealAccountList = importDataMap.entrySet().stream()
                .filter(x -> getStringValue(x.getValue(), AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue()).equals(AccountConstants.DealStatus.UN_DEAL.getValue()))
                .collect(Collectors.toList());

        boolean includeDealCustomer = getBooleanValue(poolData, "is_claim_limit_include_dealed_customers", false);

        if (!includeDealCustomer && CollectionUtils.empty(unDealAccountList)) {
            return;
        }
        Integer claimLimitNum = getIntegerValue(poolData, "claim_limit_num", 0);

        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), owner, poolId, includeDealCustomer);
        if (totalCount > claimLimitNum) {
            for (Map.Entry<Integer, IObjectData> importData : importDataMap.entrySet()) {
                errorList.add(new BaseImportAction.ImportError(importData.getKey(), I18N.text(SFAI18NKeyUtil.SFA_REACH_LIMIT_OBJ, HIGHSEAS_OBJ_KEY)));
            }
            return;
        }
        List<Map.Entry<Integer, IObjectData>> failImportList = Lists.newArrayList();
        if (includeDealCustomer) {
            if (totalCount + importDataMap.size() > claimLimitNum) {
                int remainCount = claimLimitNum - totalCount;
                failImportList = new ArrayList<>(importDataMap.entrySet()).subList(remainCount, importDataMap.size());
            }
        } else {
            if (CollectionUtils.notEmpty(unDealAccountList)) {
                if (totalCount + unDealAccountList.size() > claimLimitNum) {
                    int remainCount = claimLimitNum - totalCount;
                    failImportList = unDealAccountList.subList(remainCount, unDealAccountList.size());
                }
            }
        }
        for (Map.Entry<Integer, IObjectData> importData : failImportList) {
            errorList.add(new BaseImportAction.ImportError(importData.getKey(), String.format(I18N.text(SFAI18NKeyUtil.SFA_REACH_LIMIT_OBJ), I18N.text(HIGHSEAS_OBJ_KEY))));
        }
    }

    public static void checkAccountLimit(User user, String owner, String poolId, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        checkPoolAccountLimit(user, owner, poolId, objectDataList);
        CustomerLimit.Result rstResult = getCustomerLimit(user, Integer.valueOf(owner));
        if (rstResult != null && rstResult.getValue().getIncludeHighSeasCustomer()) {
            checkAccountLimit(user, owner, objectDataList);
        }
    }

    public static void checkAccountLimit(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<String> ownerList = objectDataList.stream().filter(AccountUtil::hasOwner)
                .map(CommonBizUtils::getOwner).distinct().collect(Collectors.toList());
        if (CollectionUtils.notEmpty(ownerList)) {
            ownerList.forEach(owner -> {
                List<IObjectData> tempDataList = objectDataList.stream().filter(x -> CommonBizUtils.getOwner(x).equals(owner)).collect(Collectors.toList());
                if (CollectionUtils.notEmpty(tempDataList)) {
                    checkAccountLimit(user, owner, tempDataList);
                }
            });
        }
    }

    public static void checkAccountLimitForRemove(User user, String owner, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        CustomerLimit.Result rstResult = getCustomerLimit(user, Integer.valueOf(owner));
        if (rstResult == null) {
            return;
        }
        List<IObjectData> allAccountList = objectDataList.stream().filter(x -> !owner.equals(CommonBizUtils.getOwner(x)))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(allAccountList)) {
            return;
        }
        List<IObjectData> unDealAccountList = getUnDealAccountList(allAccountList);

        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), owner, rstResult.getValue().getIncludeHighSeasCustomer(), rstResult.getValue().getIncludeDealCustomer());
        if (totalCount > rstResult.getValue().getLimitNum()) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
        }
        if (rstResult.getValue().getIncludeDealCustomer()) {
            if (totalCount + allAccountList.size() > rstResult.getValue().getLimitNum()) {
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
            }
        } else {
            if (CollectionUtils.notEmpty(unDealAccountList)) {
                if (totalCount + unDealAccountList.size() > rstResult.getValue().getLimitNum()) {
                    throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
                }
            }
        }
    }

    @Nullable
    public static CustomerLimit.Result getCustomerLimit(User user, Integer employeeId) {
        CustomerLimit.Result rstResult = null;
        ObjectLimitRuleModel.AccountLimitRule limitRule = ObjectLimitUtil.getAccountLimitRuleByEmployeeId(user, String.valueOf(employeeId));
        if (limitRule != null) {
            CustomerLimit.CustomerLimitResult limitResult = new CustomerLimit.CustomerLimitResult();
            limitResult.setLimitNum(limitRule.getLimitNumber());
            limitResult.setIncludeHighSeasCustomer(limitRule.getIncludeHighSeasCustomer());
            limitResult.setIncludeDealCustomer(limitRule.getIncludeDealCustomer());
            rstResult = new CustomerLimit.Result();
            rstResult.setValue(limitResult);
            rstResult.setMessage("");
            rstResult.setSuccess(true);
            rstResult.setErrorCode(0);
        }
        if (rstResult == null || rstResult.getValue().getLimitNum() <= 0) {
            return null;
        }
        return rstResult;
    }

    public static Integer getOwnerCustomerCount(String tenantId, String employeeId, Boolean includeHighSeasCustomer, Boolean includeDealCustomer) {
        int totalCount = 0;
        Count countFieldDescribe = getCountField();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, Tenantable.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.Owner.apiName, employeeId);
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        if (includeHighSeasCustomer != null && !includeHighSeasCustomer) {
            SearchUtil.fillFilterIsNull(filters, "high_seas_id");
        }
        if (includeDealCustomer != null && !includeDealCustomer) {
            SearchUtil.fillFilterEq(filters, "deal_status", "1");
        }
        query.setFilters(filters);
        Object objResult = SERVICE_FACADE.getCountValue(tenantId, countFieldDescribe, query);
        if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
            totalCount = Integer.parseInt(objResult.toString());
        }
        return totalCount;
    }

    public static Integer getOwnerCustomerCount(String tenantId, String employeeId, String poolId, Boolean includeDealCustomer) {
        int totalCount = 0;
        Count countFieldDescribe = getCountField();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.Owner.apiName, employeeId);
        SearchUtil.fillFilterEq(filters, "high_seas_id", poolId);
        if (!includeDealCustomer) {
            SearchUtil.fillFilterEq(filters, "deal_status", "1");
        }
        query.setFilters(filters);
        Object objResult = SERVICE_FACADE.getCountValue(tenantId, countFieldDescribe, query);
        if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
            totalCount = Integer.parseInt(objResult.toString());
        }
        return totalCount;
    }

    public static String buildSqlInString(List<String> ids) {
        if (CollectionUtils.empty(ids)) {
            return "''";
        }
        ids = ids.stream().distinct().collect(Collectors.toList());
        StringBuilder builder = new StringBuilder();
        for (String id : ids) {
            builder.append("'").append(SqlEscaper.pg_escape(id)).append("',");
        }
        String idString = builder.toString();
        idString = idString.substring(0, idString.length() - 1);
        return idString;
    }

    public static void updateContactOwner(User user, List<String> accountIds, String owner, String dataOwnerDpt, Boolean cleanTeamMember, Boolean followConfig) {
        if (CollectionUtils.empty(accountIds)) {
            return;
        }
        if (followConfig) {
            //是否开启客户收回分配时，同步变更本客户下联系人的负责人
            String configValue = SFAConfigUtil.getConfigValue(user.getTenantId(), ConfigType.CONTACT_OWNER_RULE_SETTING.getKey(), user.getUpstreamOwnerIdOrUserId());
            if ("0".equals(configValue)) {
                return;
            }
        }
        int offset = 0;
        int limit = 200;
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "account_id", accountIds);
        searchTemplateQuery.setFilters(filters);
        List<com.facishare.paas.metadata.impl.search.OrderBy> orderByList = Lists.newArrayList();
        com.facishare.paas.metadata.impl.search.OrderBy orderBy = new com.facishare.paas.metadata.impl.search.OrderBy();
        orderBy.setFieldName("create_time");
        orderBy.setIsAsc(true);
        orderByList.add(orderBy);
        searchTemplateQuery.setOrders(orderByList);
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setOffset(offset);
        int executeCount = 0;
        while (executeCount < 10000) {
            executeCount++; //循环次数加1
            if (executeCount == 10000) { //达到循环次数限制
                log.warn("updateContactOwner  loop limit, limit:{}", executeCount); //日志打印
                //上报audit_log
                SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder().action("sfa_loop_limit").message("AccountUtil.updateContactOwner").objectApiNames("AccountObj").build(), user);
                break;
            }
            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user,
                    SFAPreDefineObject.Contact.getApiName(), searchTemplateQuery);
            if (CollectionUtils.empty(queryResult.getData())) {
                if (!Strings.isNullOrEmpty(owner)) {
                    contactSessionSandwichService.push(user.getTenantId(), owner);
                }
                break;
            }
            List<IObjectData> objectDataList = queryResult.getData();
            List<IObjectData> oldDataList = Lists.newArrayList();
            for (IObjectData objectData : objectDataList) {
                oldDataList.add(ObjectDataExt.of(objectData).copy());
                if (StringUtils.isEmpty(owner)) {
                    objectData.set(SystemConstants.Field.Owner.apiName, Lists.newArrayList());
                } else {
                    objectData.set(SystemConstants.Field.Owner.apiName, Lists.newArrayList(owner));
                }
                objectData.set("owner_changed_time", System.currentTimeMillis());
                if(ObjectUtils.allIsEmpty(dataOwnerDpt)){
                    objectData.set("data_own_department", Lists.newArrayList());
                }else{
                    objectData.set("data_own_department", Lists.newArrayList(dataOwnerDpt));
                }
                objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
                objectData.setLastModifiedTime(System.currentTimeMillis());
            }
            List<String> updateFieldList = Lists.newArrayList(SystemConstants.Field.Owner.apiName, "owner_changed_time", "data_own_department",
                    "last_modified_by", "last_modified_time");
            try {
                SERVICE_FACADE.batchUpdateByFields(getDefaultActionContext(user), objectDataList, updateFieldList);
        //        objectDataService.batchUpdateWithField(objectDataList, updateFieldList, getDefaultActionContext(user));
                IObjectDescribe contactDescribe = SERVICE_FACADE.findObject(user.getTenantId(),
                        SFAPreDefineObject.Contact.getApiName());
                if (cleanTeamMember != null && cleanTeamMember) {
                    teamMemberService.removeObjectAllInnerTeamMember(user, objectDataList, oldDataList, true);
                }

                if (StringUtils.isEmpty(owner)) {
                    teamMemberService.removeObjectInnerOwner(user, objectDataList);
                } else {
                    teamMemberService.changeInnerOwner(user, owner, objectDataList);
                }

                ContactUtil.recordOwnerChangeHistory(user, oldDataList);

                SERVICE_FACADE.logByActionType(user, EventType.MODIFY, ActionType.ChangeOwner, oldDataList, objectDataList, contactDescribe);

                offset += limit;
                searchTemplateQuery.setOffset(offset);
            } catch (Exception e) {
                if (!Strings.isNullOrEmpty(owner)) {
                    contactSessionSandwichService.push(user.getTenantId(), owner);
                }
                log.error("updateContactOwner ", e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
            ContactUtils.sendHandleRelationshiMsgByDataList(user.getTenantId(),objectDataList, RelationOperationType.RESULT_ONE.getValue());
        }
    }

    public static ActionContext getDefaultActionContext(User user) {
        return getDefaultActionContextByApiName(user, SFAPreDefineObject.Account.getApiName());
    }

    public static ActionContext getDefaultActionContextByApiName(User user, String apiName) {
        IObjectDescribe objectDescribe = null;
        try {
            objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), apiName);
        } catch (Exception ignored) {

        }
        ActionContext actionContext = (ActionContext) ActionContextExt.of(user).getContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUpstreamOwnerIdOrUserId());
        actionContext.setDbType("pg");
        actionContext.setAllowUpdateInvalid(true);
        actionContext.put("not_validate", true);
        actionContext.setPrivilegeCheck(false);
        actionContext.setObjectDescribe(objectDescribe);
        return actionContext;
    }

    public static ActionContext getSkipReferenceActionContext(User user) {
        ActionContext actionContext = (ActionContext) ActionContextExt.of(user).getContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUpstreamOwnerIdOrUserId());
        actionContext.setDbType("pg");
        actionContext.setAllowUpdateInvalid(true);
        actionContext.put(SKIP_OBJECT_REFERENCE_EXIST_VALID, true);
        actionContext.setPrivilegeCheck(false);
        return actionContext;
    }

    public static ActionContext getDefaultActionContext(User user, String eventId) {
        ActionContext actionContext = getDefaultActionContext(user);
        if (StringUtils.isNotBlank(eventId)) {
            actionContext.put("eventId", eventId);
        }
        return actionContext;
    }

    public static void checkAccountClaimTime(User user, String ownerId, String poolId, List<String> objectIds) {
        if (CollectionUtils.empty(objectIds) || StringUtils.isEmpty(poolId) || StringUtils.isEmpty(ownerId)) {
            return;
        }
        IObjectData poolData = ACCOUNT_POOL_SERVICE_IMPL.getObjectPoolById(user.getTenantId(), poolId);
        Integer claimIntervalDays = getIntegerValue(poolData, "claim_interval_days", 0);
        if (claimIntervalDays <= 0) {
            return;
        }
        boolean canClaim = Objects.equals(true, checkCustomerClaimTime(user, ownerId, poolId, objectIds, claimIntervalDays));
        if (!canClaim) {
            throw new ValidateException(String.format(I18N.text(SFA_CANT_RECEIVE_SAME_IN_DAYS),
                    claimIntervalDays, I18N.text("AccountObj.attribute.self.display_name")));
        }
    }

    public static boolean hasOwner(IObjectData objectData) {
        List<String> ownerList = objectData.getOwner();
        if (CollectionUtils.empty(ownerList)) {
            return false;
        }
        String owner = ownerList.get(0);
        return !StringUtils.isEmpty(owner) && !"0".equals(owner);
    }

    public static boolean hasOwner(ObjectDataDocument dataDocument) {
        IObjectData objectData = dataDocument.toObjectData();
        return hasOwner(objectData);
    }

    public static String getUserMainDepartId(String tenantId, String userId) {
        if (StringUtils.isEmpty(userId)) {
            return "";
        }
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = SERVICE_FACADE.getMainDeptInfo(tenantId, userId, Lists.newArrayList(userId));
        if (mainDeptInfoMap.containsKey(userId)) {
            return mainDeptInfoMap.get(userId).getDeptId();
        }
        return "";
    }

    public static String getUserMainDepartName(String tenantId, String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = SERVICE_FACADE.getMainDeptInfo(tenantId, userId, Lists.newArrayList(userId));
        if (mainDeptInfoMap.containsKey(userId)) {
            return mainDeptInfoMap.get(userId).getDeptName();
        }
        return null;
    }

    public static Map<String, String> getUserMainDepartIdMap(String tenantId, List<String> userIds) {
        Map<String, String> result = Maps.newHashMap();
        if (CollectionUtils.empty(userIds)) {
            return result;
        }
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = SERVICE_FACADE.getMainDeptInfo(tenantId, User.SUPPER_ADMIN_USER_ID, userIds);
        if (CollectionUtils.notEmpty(mainDeptInfoMap)) {
            for (Map.Entry<String, QueryDeptInfoByUserIds.MainDeptInfo> data : mainDeptInfoMap.entrySet()) {
                result.put(data.getKey(), data.getValue().getDeptId());
            }
        }
        return result;
    }

    public static List<String> getUserDepartIds(String tenantId, String userId) {
        return SERVICE_FACADE.queryAllSuperDeptByUserId(tenantId, userId, userId);
    }

    public static Map<String, String> getUserName(String tenantId, List<String> userIds) {
        return SERVICE_FACADE.getUserNameMapByIds(tenantId, User.SUPPER_ADMIN_USER_ID, userIds);
    }

    public static String getStringValue(IObjectData objectData, String key, String defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            return tempValue.toString();
        }
        return defaultValue;
    }

    public static String getStringValue(ObjectDataDocument dataDocument, String key, String defaultValue) {
        if (dataDocument == null) {
            return defaultValue;
        }
        IObjectData objectData = dataDocument.toObjectData();
        return getStringValue(objectData, key, defaultValue);
    }

    public static Integer getIntegerValue(IObjectData objectData, String key, Integer defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                return Integer.valueOf(tempValue.toString());
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static Integer getIntegerValue(ObjectDataDocument dataDocument, String key, Integer defaultValue) {
        IObjectData objectData = dataDocument.toObjectData();
        return getIntegerValue(objectData, key, defaultValue);
    }

    public static boolean getBooleanValue(IObjectData objectData, String key, boolean defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                return Boolean.parseBoolean(tempValue.toString());
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static boolean getBooleanValue(ObjectDataDocument dataDocument, String key, boolean defaultValue) {
        IObjectData objectData = dataDocument.toObjectData();
        return getBooleanValue(objectData, key, defaultValue);
    }

    public static Long getLongValue(IObjectData objectData, String key, Long defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                return Long.valueOf(tempValue.toString());
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static List<String> getListValue(ObjectDataDocument dataDocument, String key, List<String> defaultValue) {
        IObjectData objectData = dataDocument.toObjectData();
        return getListValue(objectData, key, defaultValue);
    }

    public static List<String> getListValue(IObjectData objectData, String key, List<String> defaultValue) {
        Object value = objectData.get(key);
        if (null == value) {
            return defaultValue;
        } else {
            String str;
            if (value instanceof String) {
                str = (String) value;
            } else {
                str = JSON.toJSONString(value);
            }

            return JSON.parseObject(str, List.class);
        }
    }

    public static List<TeamMember> getTeamMember(IObjectData objectData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        return objectDataExt.getTeamMembers();
    }

    public static List<String> getTeamMemberEmployeeIds(User user, IObjectData objectData) {
        if (objectData == null) {
            return Lists.newArrayList();
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMemberList = objectDataExt.getTeamMembers();
        if (CollectionUtils.empty(teamMemberList)) {
            return Lists.newArrayList();
        }
        List<String> memberIds = Lists.newArrayList();
        //跟产品沟通暂时只给人员发送消息，但代码先保留
        //List<String> deptIds = Lists.newArrayList();
        //List<String> userGroupIds = Lists.newArrayList();
        //List<String> roleIds = Lists.newArrayList();
        //List<String> outRoleIds = Lists.newArrayList();
        //List<String> outTenantIds = Lists.newArrayList();
        //List<String> outTenantGroupIds = Lists.newArrayList();
        Set<String> allMembers = new HashSet<>();
        for (TeamMember teamMember : teamMemberList) {
            switch (teamMember.getMemberType()) {
                case EMPLOYEE:
                    memberIds.add(teamMember.getEmployee());
                    break;
                //case DEPARTMENT:
                //    deptIds.add(teamMember.getEmployee());
                //    break;
                //case GROUP:
                //    userGroupIds.add(teamMember.getEmployee());
                //    break;
                //case ROLE:
                //    if (teamMember.isOutMember()) {
                //        outRoleIds.add(teamMember.getEmployee());
                //    } else {
                //        roleIds.add(teamMember.getEmployee());
                //    }
                //    break;
                //case OUT_TENANT:
                //    outTenantIds.add(teamMember.getEmployee());
                //    break;
                //case OUT_TENANT_GROUP:
                //    outTenantGroupIds.add(teamMember.getEmployee());
                //    break;
                default:
                    break;
            }
        }

        //注意下面代码  外部的角色，企业，企业组没有处理
        //userGroupIds = CommonBizOrgUtils.filterStopUserGroup(user.getTenantId(), userGroupIds);
        //List<String> userGroupMemberIds = CommonBizOrgUtils.batchGetMembersByUserGroupIds(user.getTenantId(), userGroupIds);
        //if (CollectionUtils.notEmpty(userGroupMemberIds)) {
        //    allMembers.addAll(userGroupMemberIds);
        //}
        //
        //Set<String> roleMembers = CommonBizOrgUtils.batchGetRoleUsersByRoleIds(user.getTenantId(), roleIds);
        //if (CollectionUtils.notEmpty(roleMembers)) {
        //    allMembers.addAll(roleMembers);
        //}
        //
        //if (CollectionUtils.notEmpty(deptIds)) {
        //    List<String> deptMembers = orgService.getMembersByDeptIds(user, deptIds, 0);
        //    if (CollectionUtils.notEmpty(deptMembers)) {
        //        allMembers.addAll(deptMembers);
        //    }
        //}
        if (CollectionUtils.notEmpty(memberIds)) {
            allMembers.addAll(memberIds);
        }
        return Lists.newArrayList(allMembers);
    }

    public static void sendCRMNotification(User user, String remindContent, Integer remindRecordType, String title
            , String dataId, String content2Id, List<String> receiverIds) {
        receiverIds.removeIf(x -> user.getUpstreamOwnerIdOrUserId().equals(x));
        receiverIds = receiverIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(receiverIds)) {
            return;
        }

        List<Integer> realReceiverIds = Lists.newArrayList();
        for (String receiverId : receiverIds) {
            if (StringUtils.isBlank(receiverId)) {
                continue;
            }
            try {
                Integer realId = Integer.valueOf(receiverId);
                realReceiverIds.add(realId);
            } catch (Exception e) {
                log.error("accountutil sendCRMNotification error", e);
            }
        }
        if (StringUtils.isEmpty(content2Id)) {
            content2Id = user.getUpstreamOwnerIdOrUserId();
        } else {
            try {
                Integer.valueOf(content2Id);
            } catch (Exception e) {
                content2Id = user.getUpstreamOwnerIdOrUserId();
            }
        }
        CRMNotification crmNotification = CRMNotification.builder().sender(user.getUpstreamOwnerIdOrUserId())
                .remindRecordType(remindRecordType).title(title).content(remindContent).dataId(dataId)
                .content2Id(content2Id)
                .receiverIds(new HashSet<>(realReceiverIds))
                .objectApiName(Utils.ACCOUNT_API_NAME)
                .build();

        crmNotificationService.sendCRMNotification(user, crmNotification);
    }

    public static void sendCRMNotification(User user, String remindContent, Integer remindRecordType, String title
            , String dataId, String content2Id, List<String> receiverIds, Boolean remove, String apiName) {
        if (Boolean.TRUE.equals(remove)) {
            receiverIds.removeIf(x -> user.getUpstreamOwnerIdOrUserId().equals(x));
        }
        receiverIds = receiverIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(receiverIds)) {
            return;
        }

        List<Integer> realReceiverIds = Lists.newArrayList();
        for (String receiverId : receiverIds) {
            if (StringUtils.isBlank(receiverId)) {
                continue;
            }
            try {
                Integer realId = Integer.valueOf(receiverId);
                realReceiverIds.add(realId);
            } catch (Exception e) {
                log.error("accountutil sendCRMNotification error", e);
            }
        }
        if (StringUtils.isEmpty(content2Id)) {
            content2Id = user.getUpstreamOwnerIdOrUserId();
        } else {
            try {
                Integer.valueOf(content2Id);
            } catch (Exception e) {
                content2Id = user.getUpstreamOwnerIdOrUserId();
            }
        }
        CRMNotification crmNotification = CRMNotification.builder().sender(user.getUpstreamOwnerIdOrUserId())
                .remindRecordType(remindRecordType).title(title).content(remindContent).dataId(dataId)
                .content2Id(content2Id)
                .receiverIds(new HashSet<>(realReceiverIds))
                .objectApiName(apiName)
                .build();

        crmNotificationService.sendCRMNotification(user, crmNotification);
    }

    public static List<BaseImportAction.ImportError> importCustomValidate(List<BaseImportDataAction.ImportData> dataList,
                                                                          User user, boolean isUpdateImport, IObjectDescribe objectDescribe, Boolean isEmptyValueToUpdate, Boolean isUpdateOwner) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        if (CollectionUtils.empty(dataList)) {
            return errorList;
        }
        if (!isUserDefineDealSetting(user.getTenantId())) {
            BaseImportDataAction.ImportData data = dataList.get(0);
            if (data.containsField(AccountConstants.Field.DEAL_STATUS)) {
                dataList.forEach(x -> {
                    errorList.add(new BaseImportAction.ImportError(x.getRowNo(), I18N.text(SFA_DEAL_RULE_TEMPLATE_ERROR)));
                });
            }
        }

        List<String> highSeasNames = dataList.stream()
                .filter(x -> StringUtils.isNotEmpty(getStringValue(x.getData(), "high_seas_name", "")))
                .map(x -> getStringValue(x.getData(), "high_seas_name", ""))
                .distinct().collect(Collectors.toList());

        Map<String, String> highSeasNameMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(highSeasNames)) {
            highSeasNameMap = SERVICE_FACADE.findObjectIdByName(user, Utils.HIGHSEAS_API_NAME, highSeasNames);
        }
        List<String> notExistHighSeas = Lists.newArrayList();
        for (String highSeasName : highSeasNames) {
            if (!highSeasNameMap.containsKey(highSeasName)) {
                notExistHighSeas.add(highSeasName);
            }
        }
        if (CollectionUtils.notEmpty(notExistHighSeas)) {
            dataList.forEach(x -> {
                if (notExistHighSeas.contains(getStringValue(x.getData(), "high_seas_name", ""))) {
                    errorList.add(new BaseImportAction.ImportError(x.getRowNo(), String.format(I18N.text(SFA_NOT_EXISTS),
                            I18N.text("HighSeasObj.attribute.self.display_name"))));
                }
            });
        }

        if (!isUpdateImport) {
            List<IObjectData> validDataList = dataList.stream().map(x -> x.getData()).collect(Collectors.toList());
            AccountUtil.importCustomDefaultValue(validDataList, user.getTenantId(), objectDescribe);
            List<String> ownerList = dataList.stream()
                    .filter(x -> hasOwner(x.getData())).map(x -> CommonBizUtils.getOwner(x.getData())).distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(ownerList)) {
                ownerList.forEach(x -> {
                    List<IObjectData> tempDataList = dataList.stream()
                            .filter(d -> CommonBizUtils.getOwner(d.getData()).equals(x)).map(d -> d.getData())
                            .collect(Collectors.toList());
                    if (!objectLimitService.isGrayObjectLimit(user.getTenantId())) {
                        try {
                            checkAccountLimit(user, x, tempDataList);
                        } catch (Exception e) {
                            List<BaseImportDataAction.ImportData> errorDataList = dataList.stream()
                                    .filter(d -> CommonBizUtils.getOwner(d.getData()).equals(x))
                                    .collect(Collectors.toList());
                            if (CollectionUtils.notEmpty(errorDataList)) {
                                errorDataList.forEach(d -> errorList.add(new BaseImportAction.ImportError(d.getRowNo(),
                                        String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                                I18N.text("AccountObj.attribute.self.display_name")))));
                            }
                        }
                    } else {
                        tempDataList.forEach(data -> data.set(DBRecord.ID, SERVICE_FACADE.generateId()));
                        List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(tempDataList);
                        checkLimitDataList.forEach(data -> {
                                data.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
                            data.set(AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.ALLOCATED.getValue());
                            data.set("last_follow_time", System.currentTimeMillis());
                            data.set("owner_modified_time", System.currentTimeMillis());
                            data.set("owner_department", getUserMainDepartName(user.getTenantId(), x));
                            data.set("created_by", Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
                            data.set("create_time", System.currentTimeMillis());
                            List<String> ownDepartment = getListValue(data, SystemConstants.Field.DataOwnDepartment.apiName, Lists.newArrayList());
                            if (CollectionUtils.empty(ownDepartment)) {
                                data.set("data_own_department", Lists.newArrayList(getUserMainDepartId(user.getTenantId(), x)));
                            }
                        });
                        ObjectLimitService.CheckLimitResult checkLimitResult = objectLimitService.checkObjectLimit(user, SFAPreDefineObject.Account.getApiName(), x, checkLimitDataList, objectDescribe);
                        Map<String, String> failureIdAndRuleName = checkLimitResult.getFailureIdAndRuleName();
                        if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                            dataList.forEach(data -> {
                                if (checkLimitResult.getFailureIds().contains(data.getData().getId())) {
                                    if (!failureIdAndRuleName.isEmpty()) {
                                        errorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(I18N.text(SFA_REACH_RULE_LIMIT_OBJ), failureIdAndRuleName.get(data.getData().getId()),
                                                I18N.text("AccountObj.attribute.self.display_name"))));
                                    } else {
                                        errorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                                I18N.text("AccountObj.attribute.self.display_name"))));
                                    }
                                }
                            });
                        }
                    }
                });
            }
        } else {
            if (objectLimitService.isGrayObjectLimit(user.getTenantId())) {
                List<String> objectDataIds = dataList.stream().map(x -> x.getData().getId()).collect(Collectors.toList());
                List<IObjectData> originDataList = SERVICE_FACADE.findObjectDataByIdsIncludeDeleted(user, objectDataIds, SFAPreDefineObject.Account.getApiName());


                // 更新后有负责人的，校验保有量  mergeFunction (a, b) -> b : 线上有客户更新数据有两条相同的，这里校验默认用最后一个
                Map<String, BaseImportDataAction.ImportData> updateDataIdMap = dataList.stream()
                        .collect(Collectors.toMap(d -> d.getData().getId(), Function.identity(), (a, b) -> {
                            errorList.add(new BaseImportAction.ImportError(a.getRowNo(), I18N.text(SFA_UPDATE_IMPORT_ID_DUPLICATE)));
                            return b;
                        }, HashMap::new));
                List<IObjectData> noChangeOwnerDataList = Lists.newArrayList();
                List<IObjectData> changeOwnerDataOriginList = Lists.newArrayList();
                List<IObjectData> changeOwnerDataUpdateList = Lists.newArrayList();
                if (BooleanUtils.isTrue(isUpdateOwner)) {
                    for (IObjectData originData : originDataList) {
                        IObjectData updateData = updateDataIdMap.get(originData.getId()).getData();
                        if (updateData != null) {
                            if (CollectionUtils.isEqual(updateData.getOwner(), originData.getOwner())) {
                                noChangeOwnerDataList.add(originData);
                            } else {
                                if (Safes.isNotEmpty(updateData.getOwner())) {
                                    changeOwnerDataOriginList.add(originData);
                                    changeOwnerDataUpdateList.add(updateData);
                                }
                            }
                        }
                    }
                } else {
                    // 不用更新负责人的全是负责人无变化的
                    noChangeOwnerDataList = originDataList;
                }
                // 更新前后负责人有变化，走 通用校验保有量逻辑
                List<String> changeOwnerList = changeOwnerDataUpdateList.stream()
                        .filter(AccountUtil::hasOwner).map(CommonBizUtils::getOwner).distinct()
                        .collect(Collectors.toList());
                if (BooleanUtils.isTrue(isUpdateOwner) && CollectionUtils.notEmpty(changeOwnerList)) {
                    for (String owner : changeOwnerList) {
                        checkLimit(dataList, user, objectDescribe, isEmptyValueToUpdate, errorList,
                                changeOwnerDataOriginList, owner, false);
                    }
                }


                // 更新前后负责人无变化，走 forEdit 校验保有量（原来逻辑，不更新负责人，但要处理更新后数据有变化重新计算保有量）
                List<String> ownerList = noChangeOwnerDataList.stream()
                        .filter(AccountUtil::hasOwner).map(CommonBizUtils::getOwner).distinct()
                        .collect(Collectors.toList());
                if (CollectionUtils.notEmpty(ownerList)) {
                    for (String owner : ownerList) {
                        checkLimit(dataList, user, objectDescribe, isEmptyValueToUpdate, errorList,
                                noChangeOwnerDataList, owner, true);
                    }
                }
            }
        }

        checkOnlyAllowMemberMove(user, dataList, errorList, Utils.HIGHSEAS_API_NAME);

        return errorList;
    }

    private static void checkLimit(List<BaseImportDataAction.ImportData> dataList, User user,
                                   IObjectDescribe objectDescribe, Boolean isEmptyValueToUpdate, List<BaseImportAction.ImportError> errorList,
                                   List<IObjectData> originDataList, String owner, boolean isForEdit) {
        // 按负责人分组去 校验
        List<IObjectData> tempDataList;
        if (isForEdit) {
            tempDataList = originDataList.stream()
                    .filter(d -> Objects.equals(owner, Safes.first(d.getOwner()))).collect(Collectors.toList());
        } else {
            // 负责人有变化的，需要用更新后的负责人去校验保有量，也就是 dataList
            List<String> sameUpdateOwnerDataIdList = dataList.stream().map(BaseImportDataAction.ImportData::getData)
                    .filter(d -> Objects.equals(owner, Safes.first(d.getOwner()))).map(IObjectData::getId)
                    .collect(Collectors.toList());
            tempDataList = originDataList.stream()
                    .filter(d -> sameUpdateOwnerDataIdList.contains(d.getId()))
                    .collect(Collectors.toList());
        }
        List<IObjectData> oldDataList = ObjectDataExt.copyList(tempDataList);
        List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(tempDataList);
        checkLimitDataList.forEach(data -> {
            data.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
            data.set(AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.ALLOCATED.getValue());
            data.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
            data.setLastModifiedTime(System.currentTimeMillis());
            List<IObjectData> updateDataList = dataList.stream().map(BaseImportDataAction.ImportData::getData)
                    .filter(dData -> data.getId().equals(dData.getId())).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(updateDataList)) {
                IObjectData updateData = ObjectDataExt.of(updateDataList.get(0)).copy();
                ObjectDataDocument dataDocument = ObjectDataDocument.of(updateData);
                if (isEmptyValueToUpdate == null || !isEmptyValueToUpdate) {
                    dataDocument.entrySet().removeIf((next) -> {
                        String value = ObjectDataExt.of(updateData).getStringValueInImport(next.getKey());
                        return Strings.isNullOrEmpty(value);
                    });
                }
                // 将 所有 要更新的字段赋值到 原数据 上去进行保有量校验
                dataDocument.forEach(data::set);
            }
        });
        ObjectLimitService.CheckLimitResult checkLimitResult;
        if (isForEdit) {
            checkLimitResult = objectLimitService.checkObjectLimitForEdit(user, SFAPreDefineObject.Account.getApiName(),
                    owner, oldDataList, checkLimitDataList, objectDescribe);
        } else {
            checkLimitResult = objectLimitService.checkObjectLimit(user,
                    SFAPreDefineObject.Account.getApiName(), owner, checkLimitDataList, objectDescribe);
        }

        Map<String, String> failureIdAndRuleName = checkLimitResult.getFailureIdAndRuleName();
        if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
            dataList.forEach(data -> {
                if (checkLimitResult.getFailureIds().contains(data.getData().getId())) {
                    if (!failureIdAndRuleName.isEmpty()) {
                        errorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(I18N.text(SFA_REACH_RULE_LIMIT_OBJ), failureIdAndRuleName.get(data.getData().getId()),
                                I18N.text("AccountObj.attribute.self.display_name"))));
                    } else {
                        errorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                I18N.text("AccountObj.attribute.self.display_name"))));
                    }
                }
            });
        }
    }

    /**
     * 校验公海客户不能触发审批流
     *
     * @param isApprovalFlowEnabled
     * @param dataList
     * @return
     */
    public static List<BaseImportAction.ImportError> checkHighSeas(Boolean isApprovalFlowEnabled, List<BaseImportDataAction.ImportData> dataList) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        if (isApprovalFlowEnabled) {
            dataList.forEach(m -> {
                String highSeasId = m.getData().get(AccountConstants.Field.HIGH_SEAS_ID, String.class);
                if (!Strings.isNullOrEmpty(highSeasId)) {
                    errorList.add(new BaseImportAction.ImportError(m.getRowNo(), I18N.text(SFA_HIGH_SEAS_NOT_SUPPORT_APPROVALFLOW)));
                }
            });
        }
        return errorList;
    }

    public static void importCustomDefaultValue(List<IObjectData> validList, String tenantId, IObjectDescribe objectDescribe) {
        validList.forEach(x -> {
            x.setTenantId(tenantId);
            x.setDescribeApiName(objectDescribe.getApiName());
            x.setDescribeId(objectDescribe.getId());
            x.set("pin_yin", Chinese2PinyinUtils.getPinyinString(x.getName()));
            if(ObjectUtils.allNotEmpty(getStringValue(x,AccountConstants.Field.HIGH_SEAS_ID,null))){
                x.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
            }
            x.set(SystemConstants.Field.LockStatus.apiName, "0");
            x.set("owner_modified_time", System.currentTimeMillis());
            x.set("last_followed_time", System.currentTimeMillis());
            int completedFieldQuantity = calculateObjectHasValueCount(objectDescribe, x);
            x.set("completed_field_quantity", completedFieldQuantity);

            String owner = x.get(AccountConstants.Field.OWNER, String.class);
            if (StringUtils.isNotEmpty(owner)) {
                x.set(AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.ALLOCATED.getValue());
                x.set(AccountConstants.Field.TRANSFER_COUNT, 1);
                x.set("claimed_time", System.currentTimeMillis());
            } else {
                x.set(AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue());
                x.set(AccountConstants.Field.TRANSFER_COUNT, 0);
            }
            if (isUserDefineDealSetting(tenantId)) {
                String dealStatus = getStringValue(x, AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue());
                if (dealStatus.equals(AccountConstants.DealStatus.UN_DEAL.getValue())) {
                    x.set(AccountConstants.Field.LAST_DEAL_TIME, null);
                } else {
                    Long dealTime = getLongValue(x, AccountConstants.Field.LAST_DEAL_TIME, System.currentTimeMillis());
                    x.set(AccountConstants.Field.LAST_DEAL_TIME, dealTime);
                }
            } else {
                x.set(AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue());
                x.set(AccountConstants.Field.LAST_DEAL_TIME, null);
            }
        });
    }

    public static List<String> getImportTemplateRemoveFields(User user, IObjectDescribe objectDescribe,String type) {
        List<String> removeFields = Lists.newArrayList(
                SystemConstants.Field.LockStatus.apiName, SystemConstants.Field.LifeStatus.apiName, "owner_department", "leads_id", "remaining_time",
                "owner_modified_time", "claimed_time", "last_deal_closed_amount", "returned_time", "transfer_count",
                "account_status", "biz_status", "high_seas_name", "last_followed_time", "recycled_reason",
                "expire_time", "out_resources", "total_refund_amount", "filling_checker_id", "pin_yin", "extend_obj_data_id",
                "completed_field_quantity", "remind_days", "is_er_enterprise", "biz_reg_name", "phone_number_attribution_country",
                "phone_number_attribution_location", "phone_number_attribution_address", "phone_number_attribution_city",
                "phone_number_attribution_province", "phone_number_attribution_district", "account_path", "last_follower", "extend_days", "poi_information",
                "upstream_master_coding", "connect_coding", "enterpriserelation_id", "enterpriserelation_type", "industry_ext", "extend_reason"
        );
        if (!isUserDefineDealSetting(user.getTenantId())) {
            removeFields.add("deal_status");
            removeFields.add("last_deal_closed_time");
        }
        if(objectDescribe == null) {
            return removeFields;
        }

        //region 开启多组织逻辑
        if (isOpenManyOrganizations(user, objectDescribe)) {

            List<IFieldDescribe> fieldDescribeList = objectDescribe.getFieldDescribes();
            if (CollectionUtils.notEmpty(fieldDescribeList)) {
                Map<String,Boolean> inheritFieldsMap = new HashMap<>();
                if(AccountConstants.ImportType.UPDATE_IMPORT.getCode().equals(type) && isGrayMainDataControl(user.getTenantId())) {
                    inheritFieldsMap = getNeedUpdataInheritFieldByControlStrategy(user,objectDescribe.getApiName(),null,objectDescribe,SFAPreDefineObject.AccountMainData.getApiName());
                }
                Map<String, Boolean> finalInheritFieldsMap = inheritFieldsMap;
                fieldDescribeList.forEach(m -> {
                    if (ObjectUtils.allNotEmpty(m.getInheritType()) && ("1".equals(m.getInheritType().toString()) || "0".equals(m.getInheritType().toString()))) {
                        //如果是灰度了主数据管控策略 继承字段ye需要下发isGrayCreateAccountBeforeCreateMainData
                        if (isGrayCreateAccountBeforeCreateMainData(user.getTenantId())) {
                            if(INHERIT_FIELD_NOT_SHOW_DOWNLOAD_TEMPLATE_LIST.contains(m.getApiName())){
                                removeFields.add(m.getApiName());
                                return;
                            }
                            if(AccountConstants.ImportType.UPDATE_IMPORT.getCode().equals(type)){
                                if(isGrayMainDataControl(user.getTenantId())){
                                    if(ObjectUtils.allIsEmpty(finalInheritFieldsMap) || !finalInheritFieldsMap.containsKey(m.getApiName())){
                                        removeFields.add(m.getApiName());
                                        return;
                                    }
                                }else{
                                    removeFields.add(m.getApiName());
                                }
                            }
                        }else{
                            removeFields.add(m.getApiName());
                        }
                    }
                });
            }
        }
        //endregion

        return removeFields;
    }

    public static List<String> getAccountMainDataImportTemplateRemoveFields(User user, IObjectDescribe objectDescribe) {
        return Lists.newArrayList(
                SystemConstants.Field.LockStatus.apiName, SystemConstants.Field.LifeStatus.apiName, "owner_department", "biz_status", "pin_yin", "extend_obj_data_id",
                "biz_reg_name", "industry_ext"
        );
    }

    public static void handleIsRemindRecycling(List<IObjectData> objectDataList) {
        try {
            if (CollectionUtils.notEmpty(objectDataList)) {
                sfaRecyclingService.getRecyclingRule("AccountObj", objectDataList);
            }
        } catch (Exception e) {
            log.error("handleIsRemindRecycling error", e);
        }
    }

    public static void calculateCompletionRate(IObjectDescribe objectDescribe, List<IObjectData> objectDataList) {
        if (objectDescribe == null || CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<IFieldDescribe> activeFieldList = objectDescribe.getFieldDescribes().stream().filter(IFieldDescribe::isActive)
                .collect(Collectors.toList());
        int fieldTotalCount = activeFieldList.size();
        for (IObjectData objectData : objectDataList) {
            if(objectData == null) {
                continue;
            }
            int hasValueFieldCount = getIntegerValue(objectData, "completed_field_quantity", 0);
            double completeRate = ((double) hasValueFieldCount) / fieldTotalCount * 100;
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMaximumFractionDigits(2);
            String completionRate = nf.format(completeRate) + "%";
            objectData.set("completion_rate", completionRate);
        }
    }

    public static int calculateObjectHasValueCount(IObjectDescribe objectDescribe, IObjectData objectData) {
        int result = 0;
        for (IFieldDescribe fieldDescribe : objectDescribe.getFieldDescribes()) {
            Object fieldValue = objectData.get(fieldDescribe.getApiName());
            if (fieldValue != null) {
                if (fieldValue instanceof List) {
                    if (CollectionUtils.notEmpty((List) fieldValue)) {
                        ++result;
                    }
                } else if (fieldValue instanceof String) {
                    if (StringUtils.isNotEmpty((String) fieldValue)) {
                        ++result;
                    }
                } else {
                    ++result;
                }
            }
        }
        return result;
    }

    public static void setImportFields(String tenantId, String objectCode, List<IFieldDescribe> fieldDescribes, boolean isUpdateImport) {
        log.info("setImportFields tenantId:{},objectCode:{},fieldDescribes{},isUpdateImport:{} ", tenantId, objectCode, fieldDescribes, isUpdateImport);
        Optional<IFieldDescribe> poolDescribeOptional = fieldDescribes.stream()
                .filter(h -> AccountConstants.Field.HIGH_SEAS_ID.equals(h.getApiName())).findFirst();
        Optional<IFieldDescribe> ownerDescribeOptional = fieldDescribes.stream()
                .filter(h -> AccountConstants.Field.OWNER.equals(h.getApiName())).findFirst();
        Optional<IFieldDescribe> dealStatusDescribeOptional = fieldDescribes.stream()
                .filter(h -> AccountConstants.Field.DEAL_STATUS.equals(h.getApiName())).findFirst();

        if (Utils.HIGHSEAS_API_NAME.equals(objectCode) || "highseas".equals(objectCode)) {
            poolDescribeOptional.ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(true));
            ownerDescribeOptional.ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(false));
        } else {
            poolDescribeOptional.ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(false));
            ownerDescribeOptional.ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(true));
        }

        if (isUserDefineDealSetting(tenantId)) {
            if (isUpdateImport) {
                dealStatusDescribeOptional.ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(false));
            } else {
                dealStatusDescribeOptional.ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(true));
            }
        }
    }

    public static String getRecyclingReason(User user, ObjectAction action, String owner) {
        String recyclingReason = "";
        switch (action) {
            case ALLOCATE:
                if (StringUtils.isNotEmpty(owner)) {
                    // 管理员（上级）收回
                    recyclingReason = I18N.text("sfa.OpportunityBusiness.2562.1");
                }
                break;
            case TAKE_BACK:
                // 管理员（上级）收回
                recyclingReason = I18N.text("sfa.OpportunityBusiness.2562.1");
                break;
            case RETURN:
            case CHANGE_OWNER:
                if (user.getUpstreamOwnerIdOrUserId().equals(owner)) {
                    //销售人员退回
                    recyclingReason = I18N.text("sfa.OpportunityBusiness.2595.1");
                } else {
                    // 	sfa.OpportunityBusiness.2562.1
                    recyclingReason = I18N.text("sfa.OpportunityBusiness.2562.1");
                }
                break;
            default:
                //未成交/未跟进收回
                recyclingReason = I18N.text("sfa.OpportunityBusiness.2580.1");
                break;
        }
        return recyclingReason;
    }

    public static List<IObjectData> fillOldData(List<IObjectData> objectDataList, List<IObjectData> oldDataList) {
        return ObjectDataDocument.fillOldData(objectDataList, oldDataList);
    }

    public static boolean isAllowAfterActionChange(String tenantId) {
        String configValue = SFAConfigUtil.getConfigValue(tenantId, "allow_after_action_change", User.SUPPER_ADMIN_USER_ID);
        return "1".equals(configValue);
    }

    public static boolean isOpenAccountFillingCheck(String tenantId) {
        String configValue = SFAConfigUtil.getConfigValue(tenantId, "3", User.SUPPER_ADMIN_USER_ID);
        return "1".equals(configValue);
    }

    public static boolean isUserDefineDealSetting(String tenantId) {
        String sql = "SELECT * FROM object_follow_deal_setting"
                + String.format(" WHERE tenant_id = '%s'", SqlEscaper.pg_escape(tenantId))
                + " AND is_user_define_setting = 't' AND setting_type='2' AND is_deleted>=0 LIMIT 1";
        try {
            List<Map> queryResult = objectDataService.findBySql(tenantId, sql);
            return CollectionUtils.notEmpty(queryResult);
        } catch (MetadataServiceException e) {
            throw new APPException("getConfigValueFromPg"+I18N.text("sfa.abnormal.metadata") , e);
        }
    }

    public static void getOwnerFilter(List<IFilter> filters, User user) {
        if (user.isOutUser()) {
            SearchUtil.fillFilterEq(filters, "out_owner", Lists.newArrayList(user.getOutUserId()));
        } else {
            SearchUtil.fillFilterEq(filters, "owner", Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
        }
    }

    public static void checkAccountLimit(User user, String owner, String poolId, List<IObjectData> objectDataList,
                                         ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                         Long outerTenantId, Long outerOwnerId) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        checkPoolAccountLimit(user, owner, poolId, objectDataList, objectPoolMemberType, outerTenantId, outerOwnerId);
        CustomerLimit.Result rstResult = null;
        if (objectPoolMemberType != ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE &&
                objectPoolMemberType != ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE) {
            rstResult = getCustomerLimit(user, Integer.valueOf(owner));
        }
        if (rstResult != null && rstResult.getValue().getIncludeHighSeasCustomer()) {
            checkAccountLimit(user, owner, objectDataList);
        }
    }

    public static void checkPoolAccountLimit(User user, String owner, String poolId, List<IObjectData> objectDataList,
                                             ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                             Long outerTenantId, Long outerOwnerId) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        IObjectData poolData = ACCOUNT_POOL_SERVICE_IMPL.getObjectPoolById(user.getTenantId(), poolId);
        if (poolData == null) {
            return;
        }
        List<IObjectData> unDealAccountList = objectDataList.stream()
                .filter(x -> getStringValue(x, AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue()).equals(AccountConstants.DealStatus.UN_DEAL.getValue()))
                .collect(Collectors.toList());

        boolean includeDealCustomer = getBooleanValue(poolData, "is_claim_limit_include_dealed_customers", false);

        if (!includeDealCustomer && CollectionUtils.empty(unDealAccountList)) {
            return;
        }
        Integer claimLimitNum = getIntegerValue(poolData, "claim_limit_num", 0);
        String limitType = getStringValue(poolData, "limit_type", "");

        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), owner, poolId, includeDealCustomer, limitType,
                objectPoolMemberType, outerTenantId, outerOwnerId);
        if (totalCount > claimLimitNum) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
        }
        if (includeDealCustomer) {
            if (totalCount + objectDataList.size() > claimLimitNum) {
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
            }
        } else {
            if (CollectionUtils.notEmpty(unDealAccountList)) {
                if (totalCount + unDealAccountList.size() > claimLimitNum) {
                    throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
                }
            }
        }
    }

    public static Integer getOwnerCustomerCount(String tenantId, String employeeId, String poolId, Boolean includeDealCustomer,
                                                String limitType, ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                                Long outerTenantId, Long outerOwnerId) {
        int totalCount = 0;
        Count countFieldDescribe = getCountField();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "high_seas_id", poolId);
        if (objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE ||
                objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE) {
            SearchUtil.fillFilterEq(filters, "out_tenant_id", String.valueOf(outerTenantId));
            if (!ObjectPoolPermission.ObjectPoolLimitType.ENTERPRISE.getValue().equals(limitType)) {
                SearchUtil.fillFilterEq(filters, "out_owner", String.valueOf(outerOwnerId));
            }
        } else {
            SearchUtil.fillFilterEq(filters, "owner", employeeId);
        }
        if (!includeDealCustomer) {
            SearchUtil.fillFilterEq(filters, "deal_status", "1");
        }
        query.setFilters(filters);
        query.setSearchSource("db");

        Object objResult = SERVICE_FACADE.getCountValue(tenantId, countFieldDescribe, query);
        if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
            totalCount = Integer.parseInt(objResult.toString());
        }
        return totalCount;
    }

    public static Count getCountField() {
        Count countFieldDescribe = new CountFieldDescribe();
        countFieldDescribe.setApiName(Utils.ACCOUNT_API_NAME);
        countFieldDescribe.setFieldApiName("totalcount");
        countFieldDescribe.setSubObjectDescribeApiName(Utils.ACCOUNT_API_NAME);
        countFieldDescribe.setCountFieldApiName("id");
        countFieldDescribe.setCountType(Count.TYPE_COUNT);
        countFieldDescribe.setReturnType("number");
        countFieldDescribe.setDecimalPlaces(0);
        return countFieldDescribe;
    }

    public static void checkOuterAccountLimit(User user, String owner, String outTenantId, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        if (org.springframework.util.StringUtils.isEmpty(outTenantId)) {
            throw new ValidateException("outTenantId " + I18N.text(SFA_CONFIG_PARAMETER_ERROR1));
        }
        List<String> poolIds = getPoolIds(objectDataList);
        if (CollectionUtils.notEmpty(poolIds)) {
            poolIds.forEach(x -> {
                List<IObjectData> tempDataList = objectDataList.stream().filter(data ->
                        x.equals(data.get(AccountConstants.Field.HIGH_SEAS_ID, String.class)))
                        .collect(Collectors.toList());
                if (CollectionUtils.notEmpty(tempDataList)) {
                    long newOutTenantId = Long.parseLong(outTenantId);
                    checkPoolAccountLimit(user, owner, x, tempDataList, ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE,
                            newOutTenantId, Long.valueOf(owner));
                }
            });
        }
    }

    public static void handleSearchQuery(SearchTemplateQuery query, User user) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.UnderReview.value);
        SearchUtil.fillFilterEq(filters, AccountConstants.Field.FILLING_CHECKER_ID, user.getUpstreamOwnerIdOrUserId());
        if (!isOpenAccountFillingCheck(user.getTenantId())) {
            IFilter filter = new Filter();
            filter.setFieldName("name");
            filter.setFieldValues(Lists.newArrayList());
            filter.setOperator(Operator.IS);
            filters.add(filter);
        }
        query.setFilters(filters);
        query.setPermissionType(0);
    }

    public static List<IFilter> getToDoListFilters(User user) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.UnderReview.value);
        SearchUtil.fillFilterEq(filters, AccountConstants.Field.FILLING_CHECKER_ID, user.getUpstreamOwnerIdOrUserId());
        if (!isOpenAccountFillingCheck(user.getTenantId())) {
            SearchUtil.fillFilterIsNull(filters, "name");
        }
        return filters;
    }

    public static boolean isShowCompanyLyricalAll(String tenantId, String companyName) {
        return false;
    }

    public static boolean isShowCostComponent(String tenantId) {
        return gray.isAllow("account_cost_component_enable", tenantId);
    }

    public static void updateConfirmTime(String tenantId, String objectApiName, List<String> objectIds) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        objectIds.forEach(objectId -> {
            IObjectData objectData = new ObjectData();
            objectData.setTenantId(tenantId);
            objectData.setDescribeApiName(objectApiName);
            objectData.setId(objectData.getId());
            objectData.set("confirm_time", System.currentTimeMillis());
            objectDataList.add(objectData);
        });
        List<String> toUpdateFields = Lists.newArrayList("confirm_time");
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        SERVICE_FACADE.batchUpdateByFields(user, objectDataList, toUpdateFields);
    }

    public static boolean isGrayFeatureEnable(String tenantId) {
        return gray.isAllow("account_feature_enable", tenantId);
    }

    public static boolean isGrayCommonEditEnable(String tenantId) {
        return gray.isAllow("account_common_edit_enable", tenantId);
    }

    public static boolean isGrayLocationEnable(String tenantId) {
        return gray.isAllow("account_location_enable", tenantId);
    }

    public static boolean isGrayDisableGeoCalculate(String tenantId) {
        return gray.isAllow("account_geo_calculate_disable", tenantId);
    }

    public static boolean isGrayRedisDistributedLock(String tenantId) {
        return gray.isAllow("redis_distributed_lock_enable", tenantId);
    }

    public static boolean isGrayCreateAccountBeforeCreateMainData(String tenantId) {
        return gray.isAllow("account_create_before_create_main_data", tenantId);
    }

    public static boolean isGrayMainDataControl(String tenantId) {
        return gray.isAllow("main_data_control_gray_tenantId", tenantId);
    }

    public static boolean isGrayCreateAccountBeforeCreateMainDataHideAccountMainDataId(String tenantId) {
        return gray.isAllow("account_create_before_create_main_data_hide_account_main_data_id", tenantId);
    }
    public static boolean isGrayInheritFieldCanEditOfEditPageGrayTenantId(String tenantId) {
        return gray.isAllow("inherit_field_can_edit_of_edit_page_gray_tenantId", tenantId);
    }
    public static boolean isGrayUseNewSalesEventsInterface(String tenantId) {
        return gray.isAllow("use_new_sales_events_tenantId", tenantId);
    }
    public static boolean isGrayIncludeLayout(String tenantId) {
        return gray.isAllow("sfa_include_layout_tenantId", tenantId);
    }
    public static boolean isGrayFindAccountAddrUseDB(String tenantId) {
        return gray.isAllow("sfa_find_addr_use_db_tenantId", tenantId);
    }
    public static boolean isGrayQXCrossCustomerGroup(String tenantId) {
        return gray.isAllow("sfa_QX_Cross_Customer_Group_tenantId", tenantId);
    }
    public static boolean isGrayCheckAuthorAndTips(String tenantId) {
        return gray.isAllow("sfa_check_author_and_tips_tenantId", tenantId);
    }
    public static boolean isGrayFindAccountPathByAppointField(String tenantId) {
        return gray.isAllow("find_account_path_By_appoint_field", tenantId);
    }
    public static boolean isGrayCheckIsNeedUpdMainAddr(String tenantId) {
        return gray.isAllow("check_is_need_upd_main_addr", tenantId);
    }

    public static boolean isGrayContactListFindBelonging(String tenantId) {
        return gray.isAllow("contact_list_find_belonging", tenantId);
    }
    public static boolean isGrayCreateAddrUserMainAuthCode(String tenantId) {
        return gray.isAllow("create_addr_user_main_auth_code", tenantId);
    }


    public static Boolean checkCustomerClaimTime(User user, String employeeId, String poolId, List<String> objectIds, Integer claimIntervalDays) {
        List<Map> resultMap = selectHighSeasClaimLogBySql(user, employeeId, poolId, objectIds, claimIntervalDays);
        return CollectionUtils.empty(resultMap);
    }

    private static List<Map> selectHighSeasClaimLogBySql(User user, String employeeId, String poolId, List<String> objectIds, Integer claimIntervalDays) {
        List<Map> resultMap = Lists.newArrayList();
        if (CollectionUtils.empty(objectIds)) {
            return resultMap;
        }
        List<WhereParam> whereParamsList = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(whereParamsList, Tenantable.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(user.getTenantId()));
        CommonSqlUtils.addWhereParam(whereParamsList, "api_name", CommonSqlOperator.EQ, Lists.newArrayList("AccountObj"));
        CommonSqlUtils.addWhereParam(whereParamsList, "employee_id", CommonSqlOperator.EQ, Lists.newArrayList(employeeId));
        CommonSqlUtils.addWhereParam(whereParamsList, "pool_id", CommonSqlOperator.EQ, Lists.newArrayList(poolId));
        CommonSqlUtils.addWhereParam(whereParamsList, "operation_time", CommonSqlOperator.GT, Lists.newArrayList(System.currentTimeMillis() - (claimIntervalDays * ONE_DAY_TIME_STAMP)));
        List<Map<String, Object>> tmpColumnMap = Lists.newArrayList();
        objectIds.forEach(m -> {
            Map<String, Object> columnMap = Maps.newHashMap();
            columnMap.put("object_id", m);
            tmpColumnMap.add(columnMap);
        });
        IActionContext actionContext = CommonSqlUtil.buildContext(user);
        try {
            resultMap = commonSqlService.selectJoinOther("biz_data_claim_log", tmpColumnMap, whereParamsList, actionContext);
        } catch (MetadataServiceException e) {
            log.error("selectHighSeasClaimLogBySql error ", e);
        }
        return resultMap;
    }

    /**
     * 只允许本公海/线索池成员转移客户到该公海/线索池
     *
     * @param poolData
     * @return
     */
    public static Boolean getAllowMemberMove(IObjectData poolData) {
        return getBooleanValue(poolData, "only_allow_member_move", false);
    }

    /**
     * 只允许本公海/线索池成员退回客户到该公海/线索池
     *
     * @param poolData
     * @return
     */
    public static Boolean getAllowMemberReturn(IObjectData poolData) {
        return getBooleanValue(poolData, "only_allow_member_return", false);
    }

    /**
     * 过滤配置了 only_allow_member_move,only_allow_member_return 为true 并且 非此用户所在 的公海/线索池
     *
     * @param objectDataList 全部公海/线索池
     * @param poolIds        此用户所在的公海/线索池
     */
    public static void filterOnlyAllowMemberReturn(List<IObjectData> objectDataList, List<String> poolIds, String action) {
        if ("move".equals(action)) {
            objectDataList.removeIf(x -> !poolIds.contains(x.getId()) && getAllowMemberMove(x));
        }
        if ("return".equals(action)) {
            objectDataList.removeIf(x -> !poolIds.contains(x.getId()) && getAllowMemberReturn(x));
        }
    }

    /**
     * @param user
     * @param objectDataList
     * @param errorList
     */
    public static void checkOnlyAllowMemberMove(User user, List<BaseImportDataAction.ImportData> objectDataList,
                                                List<BaseImportAction.ImportError> errorList, String poolApiNName) {
        if (LeadsUtils.isCrmAdmin(user)) {
            return;
        }
        String apiName = "high_seas_id";
        String objApiName = SFAPreDefineObject.Account.getApiName();
        String i18Ntext = I18N.text(SFA_HIGH_SEAS_ONLY_ALLOW_MOVE);
        if (Utils.LEADS_POOL_API_NAME.equals(poolApiNName)) {
            apiName = "leads_pool_id";
            objApiName = SFAPreDefineObject.Leads.getApiName();
            i18Ntext = I18N.text(SFA_LEADS_POOL_ONLY_ALLOW_MOVE);
        }
        final String poolIdApiName = apiName;

        String userId = user.isOutUser() ? user.getOutUserId() : user.getUpstreamOwnerIdOrUserId();
        String outTenantId = user.getOutTenantId();
        List<String> highSeasIds = objectDataList.stream()
                .filter(x -> StringUtils.isNotEmpty(getStringValue(x.getData(), poolIdApiName, "")))
                .map(x -> getStringValue(x.getData(), poolIdApiName, ""))
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(highSeasIds)) {
            return;
        }
        List<IObjectData> poolList = objectPoolServiceManager.getObjectPoolService(objApiName)
                .getAllObjectPoolList(user.getTenantId(), userId, outTenantId);
        List<String> poolIds = poolList.stream().map(DBRecord::getId).collect(Collectors.toList());

        List<String> notMemberIdList = new ArrayList<>();
        for (String highSeasId : highSeasIds) {
            if (!poolIds.contains(highSeasId)) {
                notMemberIdList.addAll(highSeasIds);
            }
        }

        List<String> errorIdList = new ArrayList<>();
        List<IObjectData> results = SERVICE_FACADE.findObjectDataByIds(user.getTenantId(), notMemberIdList, poolApiNName);
        for (IObjectData result : Safes.of(results)) {
            if(result == null) {
                continue;
            }
            if (getAllowMemberMove(result)) {
                errorIdList.add(result.getId());
            }
        }
        if (CollectionUtils.empty(errorIdList)) {
            return;
        }
        for (BaseImportDataAction.ImportData data : objectDataList) {
            String highSeasId = getStringValue(data.getData(), poolIdApiName, null);
            if (errorIdList.contains(highSeasId)) {
                errorList.add(new BaseImportAction.ImportError(data.getRowNo(), i18Ntext));
            }
        }
    }

    /**
     * 补充手机归属地赋值
     */
    public static void getPhoneNumberInfo(ObjectDataDocument objectDataDocument, String fieldName,IObjectData dbMasterData) {
        if (!org.springframework.util.StringUtils.isEmpty(objectDataDocument.get(fieldName))) {
            String phoneNum = objectDataDocument.get(fieldName).toString();
            if(ObjectUtils.allNotEmpty(dbMasterData)){
                String dbPhoneNum = getStringValue(dbMasterData,fieldName,"");
                if(phoneNum.equals(dbPhoneNum)){
                    log.info("getPhoneNumberInfo phoneNum.equals(dbPhoneNum) ");
                    return;
                }
            }
            PhoneUtil.Result result = PhoneUtil.getPhoneNumberInfo(phoneNum);
            if (!Objects.isNull(result)) {
                objectDataDocument.put("phone_number_attribution_country", result.getCountry());
                objectDataDocument.put("phone_number_attribution_province", result.getProvince());
                objectDataDocument.put("phone_number_attribution_city", result.getCity());
            } else {
                objectDataDocument.put("phone_number_attribution_country", null);
                objectDataDocument.put("phone_number_attribution_province", null);
                objectDataDocument.put("phone_number_attribution_city", null);
            }
        } else if (objectDataDocument.containsKey(fieldName)) {
            objectDataDocument.put("phone_number_attribution_country", null);
            objectDataDocument.put("phone_number_attribution_province", null);
            objectDataDocument.put("phone_number_attribution_city", null);
        }
    }

    /**
     * 补充手机归属地赋值,审批流回调使用
     */
    public static void getPhoneNumberInfoForFlow(Map<String, Object> callbackData, String fieldName) {
        //判断电话是否修改
        if (CollectionUtils.empty(callbackData) || !callbackData.containsKey(fieldName)) {
            return;
        }
        if (!org.springframework.util.StringUtils.isEmpty(callbackData.get(fieldName))) {
            String phoneNum = callbackData.get(fieldName).toString();
            PhoneUtil.Result result = PhoneUtil.getPhoneNumberInfo(phoneNum);
            if (!Objects.isNull(result)) {
                callbackData.put("phone_number_attribution_country", result.getCountry());
                callbackData.put("phone_number_attribution_province", result.getProvince());
                callbackData.put("phone_number_attribution_city", result.getCity());
            } else {
                callbackData.put("phone_number_attribution_country", null);
                callbackData.put("phone_number_attribution_province", null);
                callbackData.put("phone_number_attribution_city", null);
            }
        } else {
            callbackData.put("phone_number_attribution_country", null);
            callbackData.put("phone_number_attribution_province", null);
            callbackData.put("phone_number_attribution_city", null);
        }
    }

    public static void publishFeeds(RequestContext context, String content, Integer source, List<FeedsModel.FeedRelatedCrmObject> crmObjects, ObjectDataDocument feedsData) {
        try {
            if (CollectionUtils.empty(crmObjects)) {
                return;
            }

            List<String> dataIds = crmObjects.stream().map(x -> x.getDataId()).collect(Collectors.toList());
            if (CollectionUtils.empty(dataIds)) {
                return;
            }
            String oriApiName = crmObjects.get(0).getApiName();
            List<Map<String, Object>> relatedObjectDataList = Lists.newArrayList();
            if (feedsData != null && feedsData.containsKey("related_object_data")) {
                relatedObjectDataList = (List<Map<String, Object>>) feedsData.get("related_object_data");
            }
            if (relatedObjectDataList == null) {
                relatedObjectDataList = Lists.newArrayList();
            }

            for (String dataId : dataIds) {
                FeedsModel.FeedsSaveActionArg arg = FeedsModel.FeedsSaveActionArg.builder()
                        .source(source).build();
                ObjectDataDocument dataDocument = new ObjectDataDocument();
                dataDocument.put("object_describe_api_name", "ActiveRecordObj");
                dataDocument.put("record_type", "default__c");
                dataDocument.put("created_by", Lists.newArrayList(context.getUser().getUpstreamOwnerIdOrUserId()));
                ObjectDataDocument contentDocument = new ObjectDataDocument();
                contentDocument.put("text", content);
                dataDocument.put("active_record_content", contentDocument);
                if (feedsData != null && !feedsData.isEmpty()) {
                    for (Map.Entry<String, Object> key : feedsData.entrySet()) {
                        dataDocument.put(key.getKey(), key.getValue());
                    }
                }

                List<Map<String, Object>> relatedObjectList = Lists.newArrayList();
                for (Map<String, Object> data : relatedObjectDataList) {
                    String objectIdVal = String.valueOf(data.get("id"));
                    String apiName = String.valueOf(data.get("describe_api_name"));

                    if (!dataId.equals(objectIdVal) && dataIds.contains(objectIdVal) && oriApiName.equals(apiName)) {
                        continue;
                    }
                    relatedObjectList.add(data);
                }

                dataDocument.put("related_object_data", relatedObjectList);
                dataDocument.put("_source", "server");
                arg.setObject_data(dataDocument);
                Map<String, String> headers = Maps.newHashMap();
                headers.put("Content-Type", "application/json");
                headers.put("x-app-id", context.getAppId());
                headers.put("x-out-tenant-id", context.getUser().getOutTenantId());
                headers.put("x-out-user-id", context.getUser().getOutUserId());

                headers.put("x-fs-enterprise-id", context.getTenantId());
                headers.put("x-tenant-id", context.getTenantId());
                headers.put("x-fs-ei", context.getTenantId());

                headers.put("x-fs-employee-id", context.getUser().getUpstreamOwnerIdOrUserId());
                headers.put("x-user-id", context.getUser().getUpstreamOwnerIdOrUserId());
                headers.put("x-fs-userInfo", context.getUser().getUpstreamOwnerIdOrUserId());
                headers.put("x-fs-locale", "zh-CN");
                FeedsModel.FeedsSaveActionResult result = feedsProxy.publishFeed(headers, arg);
            }
        } catch (Exception e) {
            log.error("PublishFeeds error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    public static String getNameCode() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String result = formatter.format(new Date(System.currentTimeMillis()));
        result = result.replace(":", "");
        result = result.replace(".", "");
        result = result.replace(" ", "-");
        int lastNumber = safeGetRandomInteger(100);
        DecimalFormat decimalFormat = new DecimalFormat("###");
        result = String.format("%s%s", result, decimalFormat.format(lastNumber));
        return result;
    }

    public static int safeGetRandomInteger(int bound) {
        int randomValue = bound;
        try {
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            randomValue = secureRandom.nextInt(bound);
        } catch (Exception e) {
            log.warn("safeGetRandomInteger error", e);
        }
        return  randomValue;
    }

    /**
     * 开启多币种，移除摘要卡片
     *
     * @param layout
     */
    public static void removeAccountSummaryCard(User user, ILayout layout) {
        try {
            //开启多币种，移除摘要卡片
            String configValue = SFAConfigUtil.getConfigValue(user.getTenantId(), MULTI_CURRENCY_KEY,
                    user.getUpstreamOwnerIdOrUserId());
            if (Objects.equals("1", configValue)) {
                LayoutExt layoutExt = LayoutExt.of(layout);
                List<IComponent> components = layoutExt.getComponentsSilently();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(components)) {
                    components.removeIf(x -> ComponentExt.SUMMARY_CARD_COMPONENT_NAME.equals(x.getName()));
                }
                LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(x -> {
                    try {
                        GroupComponentExt groupComponent = GroupComponentExt.of((IGroupComponent) x);
                        List<IComponent> childComponents = groupComponent.getChildComponents();
                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(childComponents)) {
                            Optional<IComponent> componentOp = childComponents.stream().filter(c -> ComponentExt.SUMMARY_CARD_COMPONENT_NAME.equals(c.getName())).findFirst();
                            if (componentOp.isPresent()) {
                                childComponents.remove(componentOp.get());
                                groupComponent.setChildComponents(childComponents);
                            }
                        }
                    } catch (MetadataServiceException e) {
                        log.error("Set AccountObj summary_card_component error", e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("remove AccountObj summary_card_component error", e);
        }
    }

    public static void processAccountCostCard(User user, ILayout layout) {
        try {
            if (!isShowCostComponent(user.getTenantId())) {
                LayoutExt layoutExt = LayoutExt.of(layout);
                List<IComponent> components = layoutExt.getComponentsSilently();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(components)) {
                    components.removeIf(x -> "AccountCostObj_related_list".equals(x.getName()));
                }
                LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(x -> {
                    try {
                        GroupComponentExt groupComponent = GroupComponentExt.of((IGroupComponent) x);
                        List<IComponent> childComponents = groupComponent.getChildComponents();
                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(childComponents)) {
                            Optional<IComponent> componentOp = childComponents.stream().filter(c -> "AccountCostObj_related_list".equals(c.getName())).findFirst();
                            if (componentOp.isPresent()) {
                                childComponents.remove(componentOp.get());
                                groupComponent.setChildComponents(childComponents);
                            }
                        }
                    } catch (MetadataServiceException e) {
                        log.error("processAccountCostCard error", e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("processAccountCostCard error", e);
        }
    }

    public static void removeRFMResultCard(ILayout layout) {
        try {
            List<String> apiNames = Lists.newArrayList("contact_member_relationship");
            apiNames.forEach(apiName -> {
                LayoutExt layoutExt = LayoutExt.of(layout);
                List<IComponent> components = layoutExt.getComponentsSilently();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(components)) {
                    components.removeIf(x -> apiName.equals(x.getName()));
                }
                LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(x -> {
                    try {
                        GroupComponentExt groupComponent = GroupComponentExt.of((IGroupComponent) x);
                        List<IComponent> childComponents = groupComponent.getChildComponents();
                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(childComponents)) {
                            Optional<IComponent> componentOp = childComponents.stream().filter(c -> apiName.equals(c.getName())).findFirst();
                            if (componentOp.isPresent()) {
                                childComponents.remove(componentOp.get());
                                groupComponent.setChildComponents(childComponents);
                            }
                        }
                    } catch (MetadataServiceException e) {
                        log.error("processRFMResultCard error", e);
                    }
                }); 
            });
        } catch (Exception e) {
            log.error("processRFMResultCard error", e);
        }
    }

    /**
     * 是否开启多组织，主客户
     *
     * @param user
     * @return
     */
    public static boolean isOpenManyOrganizations(User user, IObjectDescribe objectDescribe) {
        try {
            if (objectDescribe == null) {
                objectDescribe = SERVICE_FACADE.findObject(user.getTenantId(), Utils.ACCOUNT_API_NAME);
                if (objectDescribe == null) {
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONFIG_GETDESCRIPTIONFAILED));
                }
            }
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe("account_main_data_id");
            if (fieldDescribe != null && fieldDescribe.isActive()) {
                return true;
            }
        } catch (Exception e) {
            log.error("getDescribe error ", e);
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
        return false;
    }

    /**
     * 处理从对象
     *
     * @param masterDataList
     */
    public static void dealWithDetail(List<IObjectData> masterDataList, User user, IObjectDescribe objectDescribe) {
        if (CollectionUtils.empty(masterDataList)) {
            return;
        }

        //查从对象
        List<IObjectDescribe> detailDescribes =
                SERVICE_FACADE.findDetailDescribes(user.getTenantId(), objectDescribe.getApiName());

        if (CollectionUtils.empty(detailDescribes)) {
            return;
        }

        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        detailDescribes.forEach(detailDescribe -> task.submit(() -> dealOneDetail(detailDescribe, masterDataList, user, objectDescribe)));
        try {
            task.await(15000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.MD_INVALID_DETAIL_DATA_TIME_OUT));
        }
    }

    /**
     * 处理某个从对象
     *
     * @param detailDescribe
     * @param masterDataList
     */
    private static void dealOneDetail(IObjectDescribe detailDescribe, List<IObjectData> masterDataList, User user, IObjectDescribe objectDescribe) {
        List<String> masterDataIds = masterDataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        try {
            QueryResult<IObjectData> detailDataResult = SERVICE_FACADE.findDetailObjectDataBatchWithPage(user,
                    objectDescribe.getApiName(), masterDataIds, detailDescribe, 1, PAGE_SIZE, null);

            int totalPage = SearchTemplateQueryExt.calculateTotalPage(detailDataResult.getTotalNumber(), PAGE_SIZE);
            for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                if (pageNum > 1) {
                    detailDataResult = SERVICE_FACADE.findDetailObjectDataBatchWithPage(user,
                            objectDescribe.getApiName(), masterDataIds, detailDescribe, pageNum, PAGE_SIZE, null);
                }
                if (CollectionUtils.empty(detailDataResult.getData())) {
                    break;
                }
                SERVICE_FACADE.bulkInvalidAndDeleteWithSuperPrivilege(detailDataResult.getData(), user);
            }
        } catch (MetaDataBusinessException e) {
            log.warn("dealOneDetail error,user:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                    user, objectDescribe.getApiName(), masterDataIds, detailDescribe.getApiName(), e);
        } catch (Exception e) {
            log.error("dealOneDetail error,user:{},masterApiName:{},masterDataId:{},detailApiName:{}",
                    user, objectDescribe.getApiName(), masterDataIds, detailDescribe.getApiName(), e);
        }
    }

    /**
     * 校验客户编辑修改线索id
     *
     * @param newLeadsId
     * @param accountId
     */
    public static void checkChangeLeadsId(User user, IObjectDescribe objectDescribe, String newLeadsId, String accountId) {
        IObjectData leadsData = SERVICE_FACADE.findObjectData(user, newLeadsId, Utils.LEADS_API_NAME);
        String bizStatus = getStringValue(leadsData, "biz_status", "");

        if (!LeadsBizStatusEnum.TRANSFORMED.getCode().equals(bizStatus)) {
            throw new ValidateException(I18N.text(SFA_ACCOUNT_CHOOSETRANSFORMEDLEADS,
                    I18N.text("LeadsObj.attribute.self.display_name")));
        }

        // 灰度功能：允许灰度企业跳过线索绑定校验
        if (GrayUtil.isSkipLeadsBindingCheck(user.getTenantId())) {
            return;
        }

        List<IObjectData> objectDataList = getAccountListByIdWithDeleted(user, objectDescribe, newLeadsId);
        if (CollectionUtils.notEmpty(objectDataList)) {
            throw new ValidateException(I18N.text(SFA_ACCOUNT_CANNOTUSETHELEADS,
                    I18N.text("LeadsObj.attribute.self.display_name"), I18N.text("AccountObj.attribute.self.display_name"), I18N.text("LeadsObj.attribute.self.display_name")));
        }
    }

    /**
     * 根据id获取客户列表，包含作废删除的
     *
     * @param user
     * @param leadsId
     * @return
     */
    public static List<IObjectData> getAccountListByIdWithDeleted(User user, IObjectDescribe objectDescribe,
                                                                  String leadsId) {
        if (StringUtils.isBlank(leadsId)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountConstants.Field.FIELD_LEADS_ID, leadsId);
        return AccountPathUtil.getAccountListWithDeleted(user, objectDescribe, filters,null);
    }

    /**
     * 计算客户和联系人he线索的关系图谱
     * @param tenantId
     * @param dataIds
     * @param operationType
     */
    public static void sendHandleRelationshiMsgByIds(String tenantId, List<String> dataIds,String operationType){
        try {
            if(CollectionUtils.notEmpty(dataIds)){
                List<List<String>> dataIdsList = ListsUtils.splitList(dataIds,200);
                dataIdsList.stream().forEach(ids->{
                    recalculateProducer.setMsgToSend(tenantId, ids,operationType, SFAPreDefineObject.Account.getApiName());
                });
            }
        }catch (Exception  e){
            log.error("AccountUtil.sendHandleRelationshiMsgByIds e",e);
        }
    }
    public static void dealMemberRelationship(String tenantId,List<IObjectData> actualList,String operationType){
        List<String> ids = actualList.stream().map(DBRecord::getId).collect(Collectors.toList());
        sendHandleRelationshiMsgByIds(tenantId,ids, operationType);
    }

    /**
     * 合并时填充掩码字段
     *
     */
    public static void mergeFillMaskData(User user,String objectApiName, StandardMergeAction.Arg arg){
        if(ObjectUtils.allIsEmpty(arg.getValueMap())){
            return;
        }
        Set<String> maskField = new HashSet<>();
        Set<String> dataIds = new HashSet<>();
        arg.getValueMap().forEach((k,v)->{
            maskField.add(k);
            dataIds.add(v.toString());
        });
        if(CollectionUtils.empty(maskField) || CollectionUtils.empty(dataIds)){
         log.warn("mergeFillMaskData valueMap is null tenantId:{}, objectApiName:{},valueMap:{}",user.getTenantId(),objectApiName, JSON.toJSONString(arg.getValueMap()));
         return;
        }
        maskField.add("_id");
        List<String> findMaskField = new ArrayList<>(maskField);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "_id", dataIds);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchTemplateQueryWithFields(CommonSqlUtil.buildContext(user), objectApiName, searchTemplateQuery, findMaskField);
        if(ObjectUtils.allIsEmpty(queryResult) || CollectionUtils.empty(queryResult.getData())){
            log.error("mergeFillMaskData queryResult is null  tenantId:{}, objectApiName:{},maskField:{},dataIds:{},queryResult:[}",user.getTenantId(),objectApiName, JSON.toJSONString(maskField),dataIds, JSON.toJSONString(queryResult));
            return;
        }
        List<IObjectData> dataList = queryResult.getData();
        arg.getValueMap().forEach((k,v)->{
            Optional<IObjectData> masterData = dataList.stream().filter(x -> v.toString().equals(x.getId())).findFirst();
            if (masterData.isPresent()) {
                 arg.getObjectData().put(k,masterData.get().get(k));
            }
        });
    }
    /**
     * 合并时处理工商扩展字段
     */
    public static void mergeDealBusinessField (StandardMergeAction.Arg arg,List<IObjectData> dataList,String businessRelevanceField){
        /**
         * 1、判断工商字段是否目标数据的
         * 2、工商字段来自哪个源数据
         * 3、替换 工商注册  工商扩展字段的数据
         */
        try {
            if(!arg.getObjectData().containsKey(businessRelevanceField) || ObjectUtils.allIsEmpty(arg.getObjectData().get(businessRelevanceField))){
                arg.getObjectData().put(IndustryExtUtil.INDUSTRY_EXT,null);
                arg.getObjectData().put(IndustryExtUtil.BIZ_REG_NAME,null);
                return;
            }
            String businessRelevanceFieldValue = arg.getObjectData().get(businessRelevanceField).toString();
            Map<String,IObjectData> dataMap = dataList.stream().collect(Collectors.toMap(IObjectData::getId,Function.identity(),(v1,v2)->v2));
            IObjectData targetData = dataMap.get(arg.getTargetDataId());
            if(targetData.containsField(businessRelevanceField) && ObjectUtils.allNotEmpty(targetData.get(businessRelevanceField))
                    && businessRelevanceFieldValue.equals(targetData.get(businessRelevanceField).toString())){
                return;
            }
            IObjectData sourceData = new ObjectData();
            for(String sourceDataId:arg.getSourceDataIds()){
                sourceData = dataMap.get(sourceDataId);
                if(sourceData.containsField(businessRelevanceField) && ObjectUtils.allNotEmpty(sourceData.get(businessRelevanceField))
                        && businessRelevanceFieldValue.equals(sourceData.get(businessRelevanceField).toString())){
                    arg.getObjectData().put(IndustryExtUtil.INDUSTRY_EXT,sourceData.get(IndustryExtUtil.INDUSTRY_EXT));
                    arg.getObjectData().put(IndustryExtUtil.BIZ_REG_NAME,sourceData.get(IndustryExtUtil.BIZ_REG_NAME));
                    break;
                }
            }
        }catch (Exception e){
            log.error("mergeDealBusinessField error e:",e);
        }
    }


    public static List<BaseImportAction.ImportError> createMainData(List<Integer> rowNoErrorList,List<BaseImportDataAction.ImportData> dataList,com.facishare.paas.appframework.core.model.ActionContext serviceContext){
        if (!isGrayCreateAccountBeforeCreateMainData(serviceContext.getTenantId())) {
            return new ArrayList<>();
        }
        List<BaseImportAction.ImportError> errorHoopList = Lists.newArrayList();
        dataList.stream().forEach(data->{
            if(CollectionUtils.notEmpty(rowNoErrorList) && rowNoErrorList.contains(data.getRowNo())){
                return;
            }
            try {
                AccountUtil.createMainData(serviceContext,ObjectDataDocument.of(data.getData()));
            }catch (Exception e){
                log.error("createMainData error tenant_id:{},e:",serviceContext.getTenantId(),e);
                errorHoopList.add(new BaseImportAction.ImportError(data.getRowNo(), e.getMessage()));
            }

        });
        return errorHoopList;
    }

    /**
     * 创建客户时是否需要创建主数据
     * @param objectData
     * @param serviceContext
     */
    public static void createMainData(com.facishare.paas.appframework.core.model.ActionContext serviceContext,ObjectDataDocument objectData){
            User user = serviceContext.getUser();
            if (!isGrayCreateAccountBeforeCreateMainData(user.getTenantId()) || !AccountUtil.isOpenManyOrganizations (user,null)) {
                return;
            }
            /**
             * 1、判断是否需要新建客户主数据
             * 2、校验客户名是否有主数据
             * 3、获取映射关系
             * 4、填充映射关系对应的数据
             * 5、补充必填字段
             * 6、调用客户主数据新建接口
             * 7、得到客户主数据的id回填
             */


            //1

            if(objectData.containsKey(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID) && ObjectUtils.allNotEmpty(objectData.get(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID))){
                return;
            }
            //2
            boolean IS_SKIP_CHECK_BY_NAME = AccountUtil.getBooleanValue(objectData,AccountConstants.Field.IS_SKIP_CHECK_BY_NAME,false);
            if(!IS_SKIP_CHECK_BY_NAME){
                List<ObjectDataDocument> dataDocumentList = checkMainDataByName(objectData.get("name").toString(),serviceContext);

                if(CollectionUtils.notEmpty(dataDocumentList)){
                    objectData.put(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID,dataDocumentList.get(0).getId());
                    return;
                }
            }

            List<IObjectMappingRuleInfo> ruleList = infraServiceFacade.findByApiName(user, "rule_accountobj2accountmaindataobj__c");
            IObjectDescribe iObjectDescribe = null;
            try {
                iObjectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(serviceContext.getTenantId(), SFAPreDefineObject.AccountMainData.getApiName());
            } catch (MetadataServiceException e) {
                log.warn("findByTenantIdAndDescribeApiName error e:",e);
                throw new RuntimeException(e);
            }
        //3
            IObjectData newData =transferDataOfCreateAccountBeforeCreateMainData(ruleList,objectData.toObjectData(),iObjectDescribe);
            newData.set(Tenantable.TENANT_ID, user.getTenantId());
            if(!newData.containsField(AccountConstants.Field.RECORD_TYPE) || ObjectUtils.allIsEmpty(newData.get(AccountConstants.Field.RECORD_TYPE))){
                newData.set(AccountConstants.Field.RECORD_TYPE, "default__c");
            }
            newData.set("owner", Lists.newArrayList(user.getUpstreamOwnerIdOrUserId()));
            newData.set("life_status", "normal");
            newData.set("lock_status", "0");
            newData.set("package", "CRM");
            newData.set("object_describe_api_name", SFAPreDefineObject.AccountMainData.getApiName());
            newData.set("is_deleted", false);
            newData.set("data_own_organization", objectData.get("data_own_organization"));
            newData.set("data_own_department", objectData.get("data_own_department"));
            newData.set("owner", objectData.get("owner"));
            newData.set(AccountConstants.ACCOUNT_CREATE_MAIN_DATA_TAGS, true);

            BaseObjectSaveAction.Arg mainDataArg = new BaseObjectSaveAction.Arg();
            mainDataArg.setObjectData(ObjectDataDocument.of(newData));

            BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
            optionInfo.setIsDuplicateSearch(true);
            optionInfo.setUseValidationRule(false);
            optionInfo.setSkipFuncValidate(true);
            mainDataArg.setOptionInfo(optionInfo);
            com.facishare.paas.appframework.core.model.ActionContext actionContext1 = new com.facishare.paas.appframework.core.model.ActionContext(serviceContext.getRequestContext(),
                    SFAPreDefineObject.AccountMainData.getApiName(), SystemConstants.ActionCode.Add.getActionCode());
            actionContext1.getRequestContext().setAttribute("not_validate", true);
            actionContext1.getRequestContext().setAttribute(RequestContext.Attributes.TRIGGER_FLOW, false);
            BaseObjectSaveAction.Result saveResult =null;
            try {
                saveResult= SERVICE_FACADE.triggerAction(actionContext1, mainDataArg, BaseObjectSaveAction.Result.class);
            }catch (Exception e){
                log.warn("AccountUtil.createMainData.triggerAction catch name:{} ，e:",newData.getName(),e);
                holdException(e);
            }

            if(ObjectUtils.allIsEmpty(saveResult) || ObjectUtils.allIsEmpty(saveResult.getObjectData())){
                log.warn("AccountUtil.createMainData.triggerAction error");
                throw new ValidateException(I18N.text("sfa.create.accountMainData.fail"));
            }
            String mainDataId = saveResult.getObjectData().getId();
            objectData.put("account_main_data_id",mainDataId);
            actionContext1.getRequestContext().setAttribute("not_validate", false);
            actionContext1.getRequestContext().setAttribute(RequestContext.Attributes.TRIGGER_FLOW, true);
    }
    private static void holdException(Exception e){
        JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(e));
        if(ObjectUtils.allNotEmpty(object.get("validateResult"))){
            JSONObject object1 = object.getJSONObject("validateResult");
            if(ObjectUtils.allNotEmpty(object1.get("M5")) && (Boolean)object1.get("M5")){
                throw new ValidateException(I18N.text("sfa.main.data.duplicate.tip"));
            }
            if(ObjectUtils.allNotEmpty(object1.get("M3"))){
                BaseObjectSaveAction.ValidationMessage validationMessage = object1.getObject("M3",BaseObjectSaveAction.ValidationMessage.class);
                throw new ValidateException(validationMessage.getBlockMessages().get(0));
            }
        }
        throw new ValidateException(e.getMessage());
    }

    /**
     * 根据映射关系生产新的对象
     * @param ruleList
     * @param sourceDate
     * @return
     */
    public static IObjectData transferDataOfCreateAccountBeforeCreateMainData(List<IObjectMappingRuleInfo> ruleList, IObjectData sourceDate,IObjectDescribe iObjectDescribe){
        if(CollectionUtils.empty(ruleList)){
            log.error("AccountUtil--transferData，ruleList is null");
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        //
        IObjectData newData = new ObjectData();
        ruleList.stream().forEach(rule->{
            List<Map> fieldMappingList = (List<Map>) rule.get("field_mapping");
            fieldMappingList.stream().forEach(fieldMapping->{
                String sourceFieldApiName = fieldMapping.get("source_field_api_name").toString();
                if(sourceDate.containsField(sourceFieldApiName) && ObjectUtils.allNotEmpty(sourceDate.get(sourceFieldApiName))){

                    String targetFieldApiName = fieldMapping.get("target_field_api_name").toString();
                    if(fieldMapping.containsKey("option_mapping")){
                        List<Map> optionMappingList = (List<Map>)fieldMapping.get("option_mapping");
                        //判断是多选还是单选
                        IFieldDescribe fieldDescribe =  iObjectDescribe.getFieldDescribe(targetFieldApiName);
                        if(ObjectUtils.allIsEmpty(fieldDescribe)){
                            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MAPPING_FIELD_NOT_EXIST_OF_MAIN)+targetFieldApiName);
                        }
                        if(IFieldType.SELECT_MANY.equals(fieldDescribe.getType())){
                            List<String> fieldValueS = sourceDate.get(sourceFieldApiName,List.class);
                            List<String> newFieldValueS = new ArrayList<>();
                            optionMappingList.stream().forEach(optionMapping->{
                                if(fieldValueS.contains(optionMapping.get("source_option").toString())){
                                    newFieldValueS.add(optionMapping.get("target_option").toString());
                                }
                            });
                            newData.set(targetFieldApiName,newFieldValueS);
                        }else{
                            optionMappingList.stream().forEach(optionMapping->{
                                if(optionMapping.get("source_option").equals(sourceDate.get(sourceFieldApiName).toString())){
                                    if(AccountConstants.Field.RECORD_TYPE.equals(targetFieldApiName)){
                                        IFieldDescribe iFieldDescribe = iObjectDescribe.getFieldDescribe(AccountConstants.Field.RECORD_TYPE);
                                        List<Map> optionsList =  iFieldDescribe.get("options",List.class);
                                        if(CollectionUtils.notEmpty(optionsList)){
                                            List<String> values = optionsList.stream().map(x->x.get("api_name").toString()).collect(Collectors.toList());
                                            String mappingValue = optionMapping.get("target_option").toString();
                                            if(CollectionUtils.notEmpty(values) && values.contains(mappingValue)){
                                                newData.set(targetFieldApiName,optionMapping.get("target_option"));
                                            }
                                        }
                                    }else{
                                        newData.set(targetFieldApiName,optionMapping.get("target_option"));
                                    }
                                }
                            });
                        }
                    }else{
                        newData.set(targetFieldApiName,sourceDate.get(sourceFieldApiName));
                    }
                }
            });
        });
        log.info("transferDataOfCreateAccountBeforeCreateMainData newData:{}",JSONObject.toJSONString(newData));
        return newData;
    }
    /**
     * 给前端设置标识对于创建客户时需要创建主数据的企业
     * @param result
     * @param user
     */
    public static void settingMarkOfCreateAccountBeforeCreateMainDataByAdd_LAYOUT_TYPE(IObjectDescribe describe,StandardDescribeLayoutController.Result result, User user){
       try {
           if (!isGrayCreateAccountBeforeCreateMainData(user.getTenantId())) {
               return;
           }
           result.getLayout().put("createAccountBeforeCreateMainDataMark",true);
           ILayout layout = new Layout(result.getLayout());
           if(isGrayCreateAccountBeforeCreateMainDataHideAccountMainDataId(user.getTenantId())){
               handleInheritFieldCanEdit(describe,SFAPreDefine.AccountMainData.getApiName(),layout,user);
               return;
           }
           //判断布局中是否具有客户主数据字段
           boolean accountMainDataIdExistFlag =  LayoutExt.of(layout).containsField(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID);
           if(accountMainDataIdExistFlag){
               Set<String> inheritFields = new HashSet<>(describe.getInheritFields().get(SFAPreDefine.AccountMainData.getApiName()));
               LayoutExt.of(layout).setReadOnly(inheritFields,false);
           }else{
               Set<String> inheritFields = new HashSet<>();
               inheritFields.add(AccountConstants.Field.NAME);
               LayoutExt.of(layout).setReadOnly(inheritFields,true);
           }
       }catch (Exception e){
           log.error("AccountUtil.settingMarkOfCreateAccountBeforeCreateMainDataByAdd_LAYOUT_TYPE error user:{},e",JSON.toJSONString(user),e);
       }
    }

    public static void handleInheritFieldCanEdit(IObjectDescribe describe,String primaryApiName,ILayout layout, User user){
        log.info("AccountUtil.handleInheritFieldCanEdit  user:{}",user.getTenantId());
        Set<String> inheritFieldsList = new HashSet<>(describe.getInheritFields().get(primaryApiName));
        if(SERVICE_FACADE.isAdmin(user)){
            LayoutExt.of(layout).setReadOnly(inheritFieldsList,false);
            return;
        }
        //获取字段权限  fieldFuncMap  为空的话，所有的字段都是读写权限
        Map<String, Integer> fieldFuncMap = FIELD_PRIVILEGE_SERVICE.getUserFieldPrivilege(user.getTenantId(),user.getUpstreamOwnerIdOrUserId(),SFAPreDefine.Account.getApiName());

        inheritFieldsList.forEach(x->{
            if(ObjectUtils.allIsEmpty(fieldFuncMap) || !fieldFuncMap.containsKey(x) || Permissions.READ_WRITE.getValue().equals(String.valueOf(fieldFuncMap.get(x)))){
                LayoutExt.of(layout).setReadOnly(Sets.newHashSet(x),false);
            }
        });
    }

    /**
     * 给前端设置标识对于创建客户时需要创建主数据的企业
     * @param result
     * @param user
     */
    public static void settingMarkOfCreateAccountBeforeCreateMainDataByEdit_LAYOUT_TYPE(IObjectData objectData,IObjectDescribe describe,StandardDescribeLayoutController.Result result, User user, StandardDescribeLayoutController.Arg arg) {
        try {

            if (isGrayInheritFieldCanEditOfEditPageGrayTenantId(user.getTenantId())) {
                log.info("isGrayInheritFieldCanEditOfEditPageGrayTenantId");
                ILayout layout = new Layout(result.getLayout());
                handleInheritFieldCanEdit(describe,SFAPreDefine.AccountMainData.getApiName(),layout,user);
                return;
            }

            if (!isGrayMainDataControl(user.getTenantId())) {
                return;
            }
            /**
             * 1、判断当前数据的归属组织和当前用户的归属组织是否一致
             * 2、根据归属组织查询是否有管控策略
             * 3、过滤可以更新的字段
             */
            if(CollectionUtils.empty(objectData.getDataOwnOrganization())){
                log.warn("settingFieldIsReadonlyOfEditLayouOfCreatMainData objectData.getDataOwnOrganization is null TenantId:{},userId:{}",user.getTenantId(),user.getUpstreamOwnerIdOrUserId());
                return;
            }
            if(!LeadsUtils.isCrmAdmin(user)){
                String orgId = UserUtil.getOrgByUserId(user);
                if(ObjectUtils.allIsEmpty(orgId)){
                    log.error("settingFieldIsReadonlyOfEditLayouOfCreatMainData orgInfo is null TenantId:{},userId:{}",user.getTenantId(),user.getUpstreamOwnerIdOrUserId());
                    return;
                }
                if(!objectData.getDataOwnOrganization().contains(orgId)){
                    log.warn("settingFieldIsReadonlyOfEditLayouOfCreatMainData 归属组织不一样 TenantId:{},userId:{},DataId:{}",user.getTenantId(),user.getUpstreamOwnerIdOrUserId(),objectData.getId());
                    return;
                }
            }

            Map<String,Boolean> inheritFieldsMap = getNeedUpdataInheritFieldByControlStrategy(user,arg.getApiname(),objectData.getDataOwnOrganization().get(0),describe,SFAPreDefineObject.AccountMainData.getApiName());
            if(ObjectUtils.allIsEmpty(inheritFieldsMap)){
                return;
            }
            ILayout layout = new Layout(result.getLayout());

            Set<String> inheritFields = inheritFieldsMap.keySet();
            LayoutExt.of(layout).setReadOnly(inheritFields,false);
        }catch (Exception e){
            log.error("AccountUtil.settingMarkOfCreateAccountBeforeCreateMainDataByEdit_LAYOUT_TYPE error user:{},e",JSON.toJSONString(user),e);
        }
    }


    public static Map<String,Boolean> getNeedUpdataInheritFieldByControlStrategy(User user,String objectApiName,String deptId,IObjectDescribe describe,String mainObjectApiName){
        Map<String,Boolean>  inheritFieldsMap = new HashMap<>();
        try {
            if(ObjectUtils.allIsEmpty(deptId)){
                deptId = UserUtil.getOrgByUserId(user);
            }

            List<String> inheritFieldsList = (List<String>)describe.getInheritFields().get(mainObjectApiName);
            //过滤字段级权限
            if(CollectionUtils.notEmpty(inheritFieldsList)){
                Set<String> onlyReadFields = SERVICE_FACADE.getReadonlyFields(user,objectApiName);
                if(CollectionUtils.notEmpty(onlyReadFields)){
                    inheritFieldsList = inheritFieldsList.stream().filter(field->!onlyReadFields.contains(field)).collect(Collectors.toList());
                }
            }
            if(CollectionUtils.empty(inheritFieldsList)){
                log.info("getNeedUpdataInheritFieldByControlStrategy inheritFieldsList is null TenantId:{},apiName:{}",user.getTenantId(),objectApiName);
                return inheritFieldsMap;
            }
            List<Map> queryList = controlStrategyService.getControlStrategyByApiNameAndOrganizationId(user.getTenantId(),objectApiName,Lists.newArrayList(deptId));
            if(CollectionUtils.empty(queryList) || !queryList.get(0).containsKey(ControlStrategyConstants.Field.FIELD_SETTINGS)){
                log.info("getNeedUpdataInheritFieldByControlStrategy 管控规则为空 TenantId:{},apiName:{},DeptId:{}",user.getTenantId(),objectApiName,deptId);
                return inheritFieldsMap;
            }
            //过滤继承字段
            inheritFieldsList = inheritFieldsList.stream().filter(field->!ControlStrategyConstants.REMOVE_FIELD_LIST.contains(field)).collect(Collectors.toList());

            Map<String,Object> controlStrategyMap = queryList.get(0);
            if(ObjectUtils.allIsEmpty(controlStrategyMap.get(ControlStrategyConstants.Field.FIELD_SETTINGS))){
                log.error("getNeedUpdataInheritFieldByControlStrategy field_settings is null tenantId:{},deptId:{}",user.getTenantId(),deptId);
                return inheritFieldsMap;
            }
            List<Map> fieldSettingDetailList = JSON.parseArray(controlStrategyMap.get(ControlStrategyConstants.Field.FIELD_SETTINGS).toString(),Map.class);
            Map<String,Object> fieldSettingDetailMap = fieldSettingDetailList.stream().collect(Collectors.toMap(x->x.get(ControlStrategyConstants.Field.API_NAME).toString(),Function.identity(),(v1,v2)->v1));
            if (ControlStrategyConstants.SettingsType.FIELD_NAME.getCode().equals(controlStrategyMap.get(ControlStrategyConstants.Field.SETTINGS_TYPE))) {
                inheritFieldsList.stream().forEach(field->{
                    if(fieldSettingDetailMap.containsKey(field)){
                        Map<String,Object> map = (Map<String,Object>)fieldSettingDetailMap.get(field);
                        if((Boolean) map.get(ControlStrategyConstants.Field.IS_ALLOW_UPDATES)){
                            inheritFieldsMap.put(field,true);
                        }
                    }
                });
            }else if(ControlStrategyConstants.SettingsType.FIELD_TYPE.getCode().equals(controlStrategyMap.get(ControlStrategyConstants.Field.SETTINGS_TYPE))){
                inheritFieldsList.stream().forEach(field->{
                    SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) describe.getFieldDescribe(field);
                    if(fieldSettingDetailMap.containsKey(fieldDescribe.getType())){
                        Map<String,Object> map = (Map<String,Object>)fieldSettingDetailMap.get(fieldDescribe.getType());
                        if((Boolean) map.get(ControlStrategyConstants.Field.IS_ALLOW_UPDATES)){
                            inheritFieldsMap.put(field,true);
                        }
                    }
                });
            }else{
                log.error("getNeedUpdataInheritFieldByControlStrategy 管控策略按更新类型错误 TenantId:{},controlStrategyDetail:{}",user.getTenantId(),controlStrategyMap.get(RiskBrainConstants.Field.ID));
                return inheritFieldsMap;
            }
        }catch (Exception e){
            log.error("getNeedUpdataInheritFieldByControlStrategy error TenantId:{},e:",user.getTenantId(),e);
        }
        return inheritFieldsMap;
    }


    public static List<ObjectDataDocument> checkMainDataByName(String name,com.facishare.paas.appframework.core.model.ActionContext serviceContext){
        AccountMainDataModel.Arg arg = new AccountMainDataModel.Arg();
        arg.setAccountName(name);
        ServiceContext context = new ServiceContext(RequestContext.builder().tenantId(serviceContext.getTenantId())
                .user(serviceContext.getUser()).build(), null, null);
        AccountMainDataModel.Result result = accountMainDataService.getAccountMainDataByAccountName(arg,context);
        if(ObjectUtils.allIsEmpty(result) || CollectionUtils.empty(result.getDataList())){
            return Lists.newArrayList();
        }
        if(result.getDataListCode() == 1){
            throw new ValidateException(I18N.text("crm.objectForm.addAccount.createMainData.noUseableTip"));
        }
        return result.getDataList();
    }

    /**
     * 创建主数据通过主数据管控
     * @param actualList 更新后的数据
     * @param needUpdateInheritField  需要更新的继承字段
     * @param objectDescribe 对象描述
     */
    public static void updataMainDataByControlStrategy(IObjectDescribe objectDescribe,Map<String,Map<String,Object>> needUpdateInheritField,List<IObjectData> actualList,IObjectData oldData,com.facishare.paas.appframework.core.model.ActionContext serviceContext){

            if(ObjectUtils.allIsEmpty(needUpdateInheritField)){
                return;
            }
            if (!isGrayMainDataControl(serviceContext.getTenantId())) {
                return;
            }
            List<String> organizationIds = new ArrayList<>();

            if(LeadsUtils.isCrmAdmin(serviceContext.getUser())){
                if(ObjectUtils.allNotEmpty(oldData)){
                    organizationIds = oldData.getDataOwnOrganization();
                }else{
                    organizationIds = actualList.stream().filter(x->CollectionUtils.notEmpty(x.getDataOwnOrganization()))
                            .map(IObjectData::getDataOwnOrganization)
                            .flatMap(List::stream).collect(Collectors.toList());
                }
            }else{
                String orgId = UserUtil.getOrgByUserId(serviceContext.getUser());
                if(ObjectUtils.allNotEmpty(orgId)){
                    organizationIds.add(orgId);
                }
            }
            if(CollectionUtils.empty(organizationIds)){
                log.info("AccountUtil.updataMainDataByControlStrategy organizationIds is null TenantId:{},UseId:{}",serviceContext.getTenantId(),serviceContext.getUser().getUpstreamOwnerIdOrUserId());
                needUpdateInheritField.clear();
                return;
            }
            //查询是否有更新数据的管控策略
            List<Map> queryList = controlStrategyService.getControlStrategyByApiNameAndOrganizationId(serviceContext.getTenantId(),objectDescribe.getApiName(),organizationIds);
            if(CollectionUtils.empty(queryList)){
                needUpdateInheritField.clear();
                log.warn("AccountUtil.updataMainDataByControlStrategy queryList is null");
                return;
            }
            Map<String,Map> controlStrategyMap = queryList.stream().collect(Collectors.toMap(map -> map.get(ControlStrategyConstants.Field.ORGANIZATION_ID).toString(),  Function.identity(), (v1, v2) -> v2));
            /**
             * 1、过滤出需要修改继承字段
             * 2、获取字段的权限
             * 3、过滤有编辑权限的字段
             * 4、获取用户的归属组织
             * 5、遍历数据
             * 6、判断数据的归属组织和用户的是否一致
             * 7、判断更新策略
             */
            List<String> inheritFieldsList = filterOutInheritedFieldsWithoutPermission(serviceContext.getUser(),objectDescribe);

            List<IObjectData> needUpdateMainDataList = new ArrayList<>();

            //过滤 归属组织是空的，并且按照归属组织分组
            Map<String,List<IObjectData>> actualListMap =  new HashMap<>();
            if(ObjectUtils.allNotEmpty(oldData)){
                actualListMap.put(oldData.getDataOwnOrganization().get(0),Lists.newArrayList(oldData));
            }else{
                actualListMap = actualList.stream()
                        .filter(x->CollectionUtils.notEmpty(x.getDataOwnOrganization()) && ObjectUtils.allNotEmpty(x.get(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID)))
                        .collect(Collectors.groupingBy(x->x.getDataOwnOrganization().get(0)));
            }
            if(ObjectUtils.allIsEmpty(actualListMap)){
                log.warn("AccountUtil.updataMainDataByControlStrategy actualListMap is null");
                return;
            }
            for(Map.Entry<String,List<IObjectData>> map : actualListMap.entrySet()){
                String dataOrg = map.getKey();
                if(!organizationIds.contains(dataOrg) || !controlStrategyMap.containsKey(dataOrg)){
                    continue;
                }
                List<IObjectData> dataList = map.getValue();
                dataList.stream().forEach(actuaData->{
                    if(!needUpdateInheritField.containsKey(actuaData.getId()) || ObjectUtils.allIsEmpty(needUpdateInheritField.get(actuaData.getId()))){
                        needUpdateInheritField.remove(actuaData.getId());
                        return;
                    }
                    Map<String,Object> needUpdateFieldValueMap = needUpdateInheritField.get(actuaData.getId());
                    //获取管控策略详情
                    Map<String,Object> controlStrategyDetail = controlStrategyMap.get(dataOrg);
                    List<Map> fieldSettingDetailList = JSON.parseArray(controlStrategyDetail.get(ControlStrategyConstants.Field.FIELD_SETTINGS).toString(),Map.class);
                    Map<String,Object> fieldSettings = fieldSettingDetailList.stream().collect(Collectors.toMap(x->x.get(ControlStrategyConstants.Field.API_NAME).toString(),Function.identity(),(v1,v2)->v1));

                    IObjectData newData = new ObjectData();
                    needUpdateFieldValueMap.keySet().stream().forEach(field->{
                        if(!inheritFieldsList.contains(field)){
                            log.info("updataMainDataByControlStrategy>> needUpdateFieldValueMap.stream 字段没有权限 TenantId:{},UseId:{},dataId:{},field:{}",serviceContext.getTenantId(),serviceContext.getUser().getUpstreamOwnerIdOrUserId(),actuaData.getId(),field);
                            return;
                        }
                        //获取老数据
                        Object oldValue = null;
                        if(ObjectUtils.allNotEmpty(oldData)){
                            if(oldData.containsField(field) && ObjectUtils.allNotEmpty(oldData.get(field))){
                                oldValue = oldData.get(field);
                            }
                        }else{
                            if(actuaData.containsField(field) && ObjectUtils.allNotEmpty(actuaData.get(field))){
                                oldValue = actuaData.get(field);
                            }
                        }
                        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(field);
                        //判断更新类型
                        if (ControlStrategyConstants.SettingsType.FIELD_NAME.getCode().equals(controlStrategyDetail.get(ControlStrategyConstants.Field.SETTINGS_TYPE))) {
                            settingFieldValueByControlStrategyDetail(fieldDescribe,newData,field,field,fieldSettings,needUpdateFieldValueMap.get(field),oldValue,serviceContext.getTenantId());
                        }else if(ControlStrategyConstants.SettingsType.FIELD_TYPE.getCode().equals(controlStrategyDetail.get(ControlStrategyConstants.Field.SETTINGS_TYPE))){
                            if(ObjectUtils.allIsEmpty(fieldDescribe)){
                                log.error("updataMainDataByControlStrategy 获取字段描述失败 TenantId:{},UseId:{},field:{}",serviceContext.getTenantId(),field);
                                return;
                            }
                            settingFieldValueByControlStrategyDetail(fieldDescribe,newData,field,fieldDescribe.getType(),fieldSettings,needUpdateFieldValueMap.get(field),oldValue,serviceContext.getTenantId());
                        }else{
                            log.error("updataMainDataByControlStrategy 管控策略按更新类型错误 TenantId:{},controlStrategyDetail:{}",serviceContext.getTenantId(),JSON.toJSONString(controlStrategyDetail));
                        }
                        actuaData.set(field,newData.get(field));
                    });
                    if(ObjectUtils.allNotEmpty(newData)){
                        newData.setId(actuaData.get(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID).toString());
                        needUpdateMainDataList.add(newData);
                    }
                });

            }


            if(CollectionUtils.notEmpty(needUpdateMainDataList)){
                com.facishare.paas.appframework.core.model.ActionContext actionContext = new com.facishare.paas.appframework.core.model.ActionContext(serviceContext.getRequestContext(),
                        SFAPreDefineObject.AccountMainData.getApiName(), SystemConstants.ActionCode.Edit.getActionCode());
                actionContext.getRequestContext().setAttribute("not_validate", true);
                actionContext.getRequestContext().setAttribute(RequestContext.Attributes.TRIGGER_FLOW, false);
                actionContext.getRequestContext().setAttribute(RequestContext.Attributes.UPDATE_ORIGIN_SOURCE, true);
                BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
                BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
                optionInfo.setIsDuplicateSearch(true);
                optionInfo.setUseValidationRule(true);
                optionInfo.setSkipFuncValidate(false);
                arg.setOptionInfo(optionInfo);

                for(IObjectData newData : needUpdateMainDataList){

                    ObjectDataDocument document = ObjectDataDocument.of(newData);
                    arg.setObjectData(document);
                    SERVICE_FACADE.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
                }
                actionContext.getRequestContext().setAttribute("not_validate", false);
                actionContext.getRequestContext().setAttribute(RequestContext.Attributes.TRIGGER_FLOW, true);
            }
    }

    public static List<String> filterOutInheritedFieldsWithoutPermission(User user,IObjectDescribe objectDescribe){
        List<String> inheritFieldsList = (List<String>)objectDescribe.getInheritFields().get(SFAPreDefineObject.AccountMainData.getApiName());
        Set<String> fieldInvisible = SERVICE_FACADE.getUnauthorizedFields(user,objectDescribe.getApiName());
        Set<String> fieldReadonly = SERVICE_FACADE.getReadonlyFields(user,objectDescribe.getApiName());
        //Map<String, Integer> map = SERVICE_FACADE.getUserFieldPrivilege(user,objectApiName);
        fieldInvisible.addAll(fieldReadonly);
        return inheritFieldsList.stream().filter(field-> !fieldInvisible.contains(field)).collect(Collectors.toList());
    }

    public static void settingFieldValueByControlStrategyDetail(IFieldDescribe fieldDescribe,IObjectData newData,String field,String fieldSettingsKey,Map<String,Object> fieldSettings,Object newValue,Object oldValue ,String tenantId){
        if (!fieldSettings.containsKey(fieldSettingsKey)) {
            return;
        }

        Map<String,Object> fieldSettingsDetailMap = (Map)fieldSettings.get(fieldSettingsKey);

        if((Boolean) fieldSettingsDetailMap.get(ControlStrategyConstants.Field.IS_ALLOW_UPDATES)){
            if(ControlStrategyConstants.FieldSettingsUpdateType.COVER.getCode().equals(fieldSettingsDetailMap.get(ControlStrategyConstants.Field.UPDATE_TYPE))){
                newData.set(field,newValue);
            }else if(ControlStrategyConstants.FieldSettingsUpdateType.SPLICE.getCode().equals(fieldSettingsDetailMap.get(ControlStrategyConstants.Field.UPDATE_TYPE))){
                if (ObjectUtils.allNotEmpty(oldValue)) {
                    if(fieldDescribe.getType().equals(SystemConstants.RenderType.SelectMany.renderType)){
                        //多选
                        Set<String> tempOldValue = (Set<String>) oldValue;
                        tempOldValue.addAll((Set<String>) newValue);
                        newData.set(field,tempOldValue);
                    }else if(fieldDescribe.getType().equals(SystemConstants.RenderType.Image.renderType) || fieldDescribe.getType().equals(SystemConstants.RenderType.FileAttachment.renderType)){
                        //图片 //附件
                        List<Map> tempOldValue = (List<Map>) oldValue;
                        if(tempOldValue.size() < fieldDescribe.get("file_amount_limit",Integer.class)){
                            tempOldValue.addAll((List<Map>) newValue);
                            newData.set(field,tempOldValue);
                        }else{
                            log.warn("settingFieldValueByControlStrategyDetail 原（图片 || 附件）的数量已经超过设置 field：{}",field);
                        }
                    }else {
                        //文本
                        String tempValue = String.format("%s%s%s",oldValue,fieldSettingsDetailMap.get(ControlStrategyConstants.Field.SPLICE_SYMBOL),newValue);
                        if(ObjectUtils.allNotEmpty(fieldDescribe.get("max_length")) && tempValue.length() < (Integer)fieldDescribe.get("max_length")){
                            newData.set(field,tempValue);
                        }else{
                            log.warn("settingFieldValueByControlStrategyDetail 文本数量已经超过设置 field：{}",field);
                        }
                    }

                }else{
                    newData.set(field,newValue);
                }
            }else{
                log.error("updataMainDataByControlStrategy 管控策略更新方式错误 TenantId:{},controlStrategyDetail:{}",tenantId, JSON.toJSONString(fieldSettingsDetailMap));
            }
        }
    }


    public static Map<String,Map<String,Object>> handleNeedUpdateInheritField(IObjectDescribe objectDescribe,List<IObjectData> dataList,com.facishare.paas.appframework.core.model.ActionContext serviceContext){
        Map<String,Map<String,Object>> inheritFieldsDataMap = new HashMap<>();
        try {
            if (!isGrayMainDataControl(serviceContext.getTenantId())) {
                return inheritFieldsDataMap;
            }
            if(ObjectUtils.allIsEmpty(objectDescribe)){
                objectDescribe = SERVICE_FACADE.findObject(serviceContext.getTenantId(), serviceContext.getObjectApiName());
            }
            /**
             * 1、查出继承字段
             * 2、查询需要更新的继承字段的value
             */
            if(!objectDescribe.getInheritFields().containsKey(SFAPreDefineObject.AccountMainData.getApiName())){
               return inheritFieldsDataMap;
            }
            List<String> inheritFieldsList = (List<String>)objectDescribe.getInheritFields().get(SFAPreDefineObject.AccountMainData.getApiName());
            inheritFieldsList = inheritFieldsList.stream().filter(data->!data.equals(NAME)).collect(Collectors.toList());
            List<String> finalInheritFieldsList = inheritFieldsList;
            dataList.stream().forEach(data->{
                Map<String,Object> dataMap = new HashMap<>();
                finalInheritFieldsList.stream().forEach(field->{
                    if (data.containsField(field)) {
                        dataMap.put(field,data.get(field));
                    }
                });
                if(ObjectUtils.allNotEmpty(dataMap)){
                    inheritFieldsDataMap.put(data.getId(),dataMap);
                }
            });
        }catch (Exception e){
            log.error("handleNeedUpdateInheritField error tenantId:{},e:",serviceContext.getTenantId(),e);
        }
        return inheritFieldsDataMap;
    }
    /**
     * 客户和联系人同时新建处理电话手机总字段
     */
    public static void accountAndContactAddHandleTelTotalFields(BaseObjectSaveAction.Arg arg){
        if(CollectionUtils.empty(arg.getRelatedDataList()) || !arg.getRelatedDataList().containsKey(SFAPreDefineObject.Contact.getApiName())){
            return;
        }
        List<BaseObjectSaveAction.RelatedDataDocument> contactRelatedDataList = arg.getRelatedDataList().get(SFAPreDefineObject.Contact.getApiName());
        contactRelatedDataList.stream().forEach(relatedDate->{
            if(CollectionUtils.empty(relatedDate.getDataList())){
                return;
            }
            relatedDate.getDataList().stream().forEach(ContactUtil::handlePhoneFields);
        });
        arg.getRelatedDataList().put(SFAPreDefineObject.Contact.getApiName(),contactRelatedDataList);
    }

    public static boolean handleAuthorityOfQuickEdit(IObjectDescribe objectDescribe,User user,String fieldName,IObjectData data){
        if (!isGrayMainDataControl(user.getTenantId())) {
            return false;
        }
        if(CollectionUtils.empty(data.getDataOwnOrganization())){
            log.warn("handleAuthorityOfQuickEdit data.getDataOwnOrganization() is null tenantId:{},id:{}",user.getTenantId(),data.getId());
            return false;
        }
        List<String> inheritFieldList = (List<String>)objectDescribe.getInheritFields().get(SFAPreDefineObject.AccountMainData.getApiName());
        String orgId = UserUtil.getOrgByUserId(user);
        if(!inheritFieldList.contains(fieldName)){
            return false;
        }
        if(LeadsUtils.isCrmAdmin(user)){
            orgId = data.getDataOwnOrganization().get(0);
        }else if(!data.getDataOwnOrganization().contains(orgId)){
            return false;
        }


        //查询是否有更新数据的管控策略
        List<Map> queryList = controlStrategyService.getControlStrategyByApiNameAndOrganizationId(user.getTenantId(),objectDescribe.getApiName(),Lists.newArrayList(orgId));
        if(CollectionUtils.empty(queryList)){
           return false;
        }
        Map<String,Object> controlStrategyMap = queryList.get(0);

        List<Map> fieldSettingDetailList = JSON.parseArray(controlStrategyMap.get(ControlStrategyConstants.Field.FIELD_SETTINGS).toString(),Map.class);
        String apiName = "";
        if(ControlStrategyConstants.SettingsType.FIELD_NAME.getCode().equals(controlStrategyMap.get(ControlStrategyConstants.Field.SETTINGS_TYPE).toString())){
            apiName = fieldName;
        }else if(ControlStrategyConstants.SettingsType.FIELD_TYPE.getCode().equals(controlStrategyMap.get(ControlStrategyConstants.Field.SETTINGS_TYPE).toString())){
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribeMap().get(fieldName);
            apiName = fieldDescribe.getType();
        }
        if(ObjectUtils.allIsEmpty(apiName)){
            return false;
        }
        String finalApiName = apiName;
        fieldSettingDetailList = fieldSettingDetailList.stream().filter(x-> finalApiName.equals(x.get(ControlStrategyConstants.Field.API_NAME).toString())).collect(Collectors.toList());
        if(CollectionUtils.empty(fieldSettingDetailList)){
            return false;
        }
        if((boolean)fieldSettingDetailList.get(0).get(ControlStrategyConstants.Field.IS_ALLOW_UPDATES)){
            return true;
        }
        return false;
    }

    /**
     * 校验name字段是null的，灰度了主数据管控de企业
     */
    public static List<BaseImportAction.ImportError> checkNameIsNull(String tenantId ,List<BaseImportDataAction.ImportData> dataList) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        if (!isGrayCreateAccountBeforeCreateMainData(tenantId)) {
            return errorList;
        }
        dataList.forEach(m -> {
            String name = m.getData().get(AccountConstants.Field.NAME, String.class);
            String accountMainDataId = m.getData().get(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID, String.class);
            if (Strings.isNullOrEmpty(name) && Strings.isNullOrEmpty(accountMainDataId)) {
                errorList.add(new BaseImportAction.ImportError(m.getRowNo(), I18N.text(SFA_PRIMARY_ATTRIBUTE_FIELD_IS_NULL)));
            }
        });
        return errorList;
    }

    /**
     * 根据主数据管控移除tel字段
     */
    public static void checkIsRemoveTelField(User user,List<BaseImportDataAction.ImportData> dataList, Map<String, IObjectData> originDataMap,IObjectDescribe objectDescribe){
        if (isGrayMainDataControl(user.getTenantId())) {
            //有主数据管控
            List<String> organizationIds = new ArrayList<>();

            if(LeadsUtils.isCrmAdmin(user)){
                organizationIds = originDataMap.values().stream().filter(x->CollectionUtils.notEmpty(x.getDataOwnOrganization()))
                        .map(IObjectData::getDataOwnOrganization)
                        .flatMap(List::stream).collect(Collectors.toList());
            }else{
                String orgId = UserUtil.getOrgByUserId(user);
                if(ObjectUtils.allNotEmpty(orgId)){
                    organizationIds.add(orgId);
                }
            }
            if(CollectionUtils.empty(organizationIds)){
                log.info("AccountUtil.updataMainDataByControlStrategy organizationIds is null TenantId:{},UseId:{}",user.getTenantId(),user.getUpstreamOwnerIdOrUserId());
                setTelFieldValueOfNull(dataList);
                return;
            }
            //查询是否有更新数据的管控策略
            List<Map> queryList = controlStrategyService.getControlStrategyByApiNameAndOrganizationId(user.getTenantId(),SFAPreDefineObject.Account.getApiName(), organizationIds);
            if(CollectionUtils.empty(queryList)){
                setTelFieldValueOfNull(dataList);
                return;
            }
            Map<String,Map> controlStrategyMap = queryList.stream().collect(Collectors.toMap(map -> map.get(ControlStrategyConstants.Field.ORGANIZATION_ID).toString(),  Function.identity(), (v1, v2) -> v2));

            for (BaseImportDataAction.ImportData importData : dataList) {
                IObjectData data = importData.getData();
                if(!data.containsField(AccountConstants.Field.TEL)){
                    continue;
                }
                IObjectData originData = originDataMap.get(data.getId());
                if(!originData.containsField(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID) || ObjectUtils.allIsEmpty(originData.get(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID))
                        || !originData.containsField(AccountConstants.Field.DATA_OWN_ORGANIZATION) || ObjectUtils.allIsEmpty(originData.get(AccountConstants.Field.DATA_OWN_ORGANIZATION))){
                    log.info("checkIsRemoveTelField>> originData 没有权限 TenantId:{},UseId:{} dataId:{}",user.getTenantId(),user.getUpstreamOwnerIdOrUserId(),originData.getId());

                    ObjectDataExt.of(data).toMap().remove(AccountConstants.Field.TEL);
                    continue;
                }
                String dataOwnOrganization = originData.get(AccountConstants.Field.DATA_OWN_ORGANIZATION,List.class).get(0).toString();
                if(!organizationIds.contains(dataOwnOrganization)){
                    log.info("checkIsRemoveTelField>> 用户的归属组织和数据的不一致 TenantId:{},UseId:{} dataId:{}",user.getTenantId(),user.getUpstreamOwnerIdOrUserId(),originData.getId());

                    ObjectDataExt.of(data).toMap().remove(AccountConstants.Field.TEL);
                    continue;
                }
                //该组织是否有管控策略
                if(!controlStrategyMap.containsKey(dataOwnOrganization)){

                    ObjectDataExt.of(data).toMap().remove(AccountConstants.Field.TEL);
                    continue;
                }
                Map<String,Object> controlStrategyDetail = controlStrategyMap.get(dataOwnOrganization);
                List<Map> fieldSettingDetailList = JSON.parseArray(controlStrategyDetail.get(ControlStrategyConstants.Field.FIELD_SETTINGS).toString(),Map.class);
                Map<String,Object> fieldSettings = fieldSettingDetailList.stream().collect(Collectors.toMap(x->x.get(ControlStrategyConstants.Field.API_NAME).toString(),Function.identity(),(v1,v2)->v1));
                if(fieldSettings.containsKey(AccountConstants.Field.TEL)){
                    Map<String,Object>  fieldMap =  (Map<String,Object>)fieldSettings.get(AccountConstants.Field.TEL);
                    if(!(boolean)fieldMap.get(ControlStrategyConstants.Field.IS_ALLOW_UPDATES)){
                        ObjectDataExt.of(data).toMap().remove(AccountConstants.Field.TEL);
                    }
                }else{
                    ObjectDataExt.of(data).toMap().remove(AccountConstants.Field.TEL);
                }
            }
        }else{
            if (AccountUtil.isOpenManyOrganizations (user, objectDescribe)) {
                setTelFieldValueOfNull(dataList);
            }
        }
    }

    private static void setTelFieldValueOfNull(List<BaseImportDataAction.ImportData> dataList){
        for (BaseImportDataAction.ImportData importData : dataList) {
            IObjectData data = importData.getData();
            if(data.containsField(AccountConstants.Field.TEL)){
                 ObjectDataExt.of(data).toMap().remove(AccountConstants.Field.TEL);
            }
        }
    }

    /**
     * 当主数据的电话字段发生变化时，修改客户数据的归属地字段
     */
    public static void updateAccoutBelongingPlaceByMainData(User user,String dataId,Object tel){
        PhoneUtil.Result result = new PhoneUtil.Result();
        if(ObjectUtils.allNotEmpty(tel)){
            result = PhoneUtil.getPhoneNumberInfo(tel.toString());
        }
        Integer index= 0;
        int OFFSET = 0;
        while(index < 1000){
            OFFSET = index*200;
            index++; //循环次数加1
            if (index == 1000) { //达到循环次数限制
                log.warn("updateAccoutBelongingPlaceByMainData  loop limit, limit:{}", index); //日志打印
                //上报audit_log
                SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder().action("sfa_loop_limit").message("AccountUtil.updateAccoutBelongingPlaceByMainData").objectApiNames("AccountObj").build(), user);
                break;
            }
            String findSql = String.format("SELECT id from biz_account where tenant_id='%s' and account_main_data_id='%s' AND is_deleted = 0 OFFSET %s limit 200;",SqlEscaper.pg_escape(user.getTenantId())
                    ,SqlEscaper.pg_escape(dataId),OFFSET);
            try {
                List<Map> queryResult = objectDataService.findBySql(user.getTenantId(), findSql);
                if(CollectionUtils.empty(queryResult)){
                    return;
                }
                List<Map<String,Object>> updateList = new ArrayList<>();
                if(ObjectUtils.allNotEmpty(result)){
                    for(Map x:queryResult){
                        Map<String,Object> map = (Map<String,Object>)x;
                        map.put("phone_number_attribution_country", result.getCountry());
                        map.put("phone_number_attribution_province", result.getProvince());
                        map.put("phone_number_attribution_city", result.getCity());
                        updateList.add(map);
                    }
                }else{
                    queryResult.stream().forEach(x->{
                        Map<String,Object> map = (Map<String,Object>)x;
                        map.put("phone_number_attribution_country", null);
                        map.put("phone_number_attribution_province", null);
                        map.put("phone_number_attribution_city", null);
                        updateList.add(map);
                    });
                }
                IActionContext actionContext = CommonSqlUtil.buildContext(user);
                commonSqlService.batchUpdate("biz_account", updateList, Lists.newArrayList(Tenantable.TENANT_ID, "id"), actionContext);
                if(queryResult.size()<200){
                    return;
                }
            } catch (MetadataServiceException e) {
                log.error("updateAccoutBelongingPlaceByMainData error e:",e);
            }
        }
    }

    public static void getParamFromFunction(IObjectData objectData, ButtonExecutor.Result buttonExecutorResult){
        {
            if (buttonExecutorResult != null &&
                    buttonExecutorResult.getFunctionResult() != null) {
                Object functionResult = buttonExecutorResult.getFunctionResult();
                try {
                    Map<String, Object> result = (Map<String, Object>) functionResult;
                    Map<String, Object> resultArg  = (Map<String, Object>) result.get("data");
                    if (resultArg != null) {
                        if(resultArg.containsKey(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID)
                                && ObjectUtils.allNotEmpty(resultArg.get(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID))){
                            objectData.set(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID,resultArg.get(AccountConstants.Field.ACCOUNT_MAIN_DATA_ID).toString());
                        }

                        if(resultArg.containsKey(AccountConstants.Field.IS_SKIP_CHECK_BY_NAME)
                                && ObjectUtils.allNotEmpty(resultArg.get(AccountConstants.Field.IS_SKIP_CHECK_BY_NAME))){
                            objectData.set(AccountConstants.Field.IS_SKIP_CHECK_BY_NAME,resultArg.get(AccountConstants.Field.IS_SKIP_CHECK_BY_NAME));
                        }

                        if(resultArg.containsKey(AccountConstants.Field.IS_SKIP_CREATE_ADDR)
                                && ObjectUtils.allNotEmpty(resultArg.get(AccountConstants.Field.IS_SKIP_CREATE_ADDR))){
                            objectData.set(AccountConstants.Field.IS_SKIP_CREATE_ADDR,resultArg.get(AccountConstants.Field.IS_SKIP_CREATE_ADDR));
                        }
                    }
                } catch (Exception e) {
                    log.error("匹配客户主数据函数执行结果失败，functionResult:{}", JSON.toJSONString(functionResult), e);
                }

            }
        }
    }


    /**
     * is_er_enterprise = true , 解除绑定则 is_er_enterprise = false
     * @param dataList
     */
    public static void handleIsErEnterprise (List<IObjectData> dataList){
        if(CollectionUtils.empty(dataList)){
            return;
        }
        dataList.forEach(x->{
            if(AccountUtil.getBooleanValue(x,"is_er_enterprise",false)){
                if(x.containsField("enterpriserelation_id") && ObjectUtils.allNotEmpty(x.get("enterpriserelation_id"))){
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_IS_INTER_ER_ENTERPRISE_GIFT));
                }else{
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_IS_ER_ENTERPRISE_GIFT));
                }
            }
        });

    }

    public static boolean handleMergeIsHavePartnerId(IObjectData targetObjectData,List<IObjectData> sourceObjectDataList){
        //如果是关联了合作伙伴，如果不一样需要提示
        String targetPartnerId = getStringValue(targetObjectData, PartnerConstants.PARTNER_ID,"");
        for(IObjectData x:sourceObjectDataList){
            String sourcePartnerId = getStringValue(x, PartnerConstants.PARTNER_ID,"");
            if(ObjectUtils.allNotEmpty(sourcePartnerId) && !targetPartnerId.equals(sourcePartnerId)){
                return true;
            }
        }
        return false;
    }

    public static void buildMultiLevelOrderSearchQuery(User user, SearchTemplateQuery searchQuery, String relatedListName, ObjectDataDocument masterData) {
        if (!bizConfigThreadLocalCacheService.isDhtMultiLevelOrder(user.getTenantId())) {
            return;
        }
        if (!RELATED_LIST_NAME.contains(relatedListName)) {
            return;
        }
        IObjectData sourceData = Objects.isNull(masterData) ? null : masterData.toObjectData();
        if (sourceData == null) {
            return;
        }
        if (MultiLevelOrderConstants.RECORD_TYPE.equals(sourceData.getRecordType())) {
            //如果是渠道业务类型，取渠道交易关系中，供货类型等于组织直供的客户数据
            String partnerRange = null;
            String userRangeLabel = null;
            if (SFAPreDefine.PriceBook.getApiName().equals(sourceData.getDescribeApiName())) {
                partnerRange = sourceData.get(PriceBookConstants.Field.APPLY_PARTNER_RANGE.getApiName(), String.class);
                userRangeLabel = PriceBookConstants.UseRangeLabel.APPLY_PARTNER_RANGE_LABEL;
            } else if (SFAPreDefine.AvailableRange.getApiName().equals(sourceData.getDescribeApiName())) {
                partnerRange = sourceData.get(AvailableConstants.AvailableRangeField.PARTNER_RANGE, String.class);
                userRangeLabel =  AvailableConstants.UseRangeLabel.PARTNER_RANGE_LABEL;
            }
            if (StringUtils.isEmpty(partnerRange)) {
                partnerRange = "{\"type\":\"ALL\", \"value\":\"ALL\"}";
            }
            String finalPartnerRange = partnerRange;
            AvailableConstants.UseRangeInfo partnerRangeInfo = ExceptionUtils.trySupplier(() -> JSON.parseObject(finalPartnerRange, AvailableConstants.UseRangeInfo.class), userRangeLabel);
            String id = null;
            List<String> partnerIds = Lists.newArrayList();
            if (StringUtils.isEmpty(partnerRange) || AvailableConstants.UseRangeType.NONE.toString().equals(partnerRangeInfo.getType())) {
                id = "-99";
            } else if (AvailableConstants.UseRangeType.CONDITION.toString().equals(partnerRangeInfo.getType())) {
                SearchTemplateQuery searchTemplateQuery = SoCommonUtils.buildSearchTemplateQuery(2000);
                searchTemplateQuery.setWheres(JSON.parseArray(partnerRangeInfo.getValue(), Wheres.class));
                SearchUtil.fillFilterEq(searchTemplateQuery.getFilters(), "is_deleted", 0);
                List<IObjectData> partnerList = metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, SFAPreDefine.Partner.getApiName(), searchTemplateQuery, Lists.newArrayList(DBRecord.ID));
                if (CollectionUtils.notEmpty(partnerList)) {
                    partnerIds = partnerList.stream().map(DBRecord::getId).collect(Collectors.toList());
                } else {
                    id = "-99";
                }
            } else if (AvailableConstants.UseRangeType.FIXED.toString().equals(partnerRangeInfo.getType())) {
                partnerIds = JSON.parseArray(partnerRangeInfo.getValue(), String.class);
            }
            searchQuery.setSpecifiedTableParameter(getISpecifiedTableParameter(user.getTenantId(), "2", partnerIds, id));
        } else {
            //如果是非渠道业务类型，取渠道交易关系中，供货类型等于渠道供货的客户数据
            searchQuery.setSpecifiedTableParameter(getISpecifiedTableParameter(user.getTenantId()));
        }
    }

    private static ISpecifiedTableParameter getISpecifiedTableParameter(String tenantId) {
        return getISpecifiedTableParameter(tenantId, "1", null, null);
    }

    private static ISpecifiedTableParameter getISpecifiedTableParameter(String tenantId, String supplyType, List<String> partnerIds, String id) {
        ISpecifiedTableParameter specifiedTableParameter = new SpecifiedTableParameter();
        ISpecifiedTableParameter.JoinCondition joinCondition = ISpecifiedTableParameter.JoinCondition.builder().mainTableColumn("id").joinTableColumn("account_id").build();
        specifiedTableParameter.setJoinConditions(Lists.newArrayList(joinCondition));
        specifiedTableParameter.setJoinPattern("inner");
        StringBuilder tableName = new StringBuilder("(select account_id from channel_trans_relationship where is_deleted = 0");
        tableName.append(String.format(" and tenant_id = '%s'", SqlEscaper.pg_escape(tenantId)));
        tableName.append(" and status = 'on'");
        tableName.append(String.format(" and supply_type = '%s'", SqlEscaper.pg_escape(supplyType)));
        if (CollectionUtils.notEmpty(partnerIds)) {
            tableName.append(String.format(" and partner_id = %s", SqlEscaper.any_clause(partnerIds)));
        }
        if (StringUtils.isNotEmpty(id)) {
            tableName.append(String.format(" and id = '%s'", SqlEscaper.pg_escape(id)));
        }
        tableName.append(" group by account_id) as newTable");
        specifiedTableParameter.setTableName(tableName.toString());
        specifiedTableParameter.setTableNameAlias("newTable");
        return specifiedTableParameter;
    }

    public static Map<String,Object> targetTransferSource(List<IObjectMappingRuleInfo> ruleList, IObjectData targetDate){
        if(org.apache.commons.collections.CollectionUtils.isEmpty(ruleList)){
            return null;
        }
        Map<String,Object> map = new HashMap<>();
        ruleList.stream().forEach(rule->{
            List<Map> fieldMappingList = (List<Map>) rule.get("field_mapping");
            fieldMappingList.stream().forEach(fieldMapping->{
                String targetFieldApiName = fieldMapping.get("target_field_api_name").toString();
                if(targetDate.containsField(targetFieldApiName) && ObjectUtils.allNotEmpty(targetDate.get(targetFieldApiName))){

                    String sourceFieldApiName = fieldMapping.get("source_field_api_name").toString();
                    if(fieldMapping.containsKey("option_mapping")){
                        List<Map> optionMappingList = (List<Map>)fieldMapping.get("option_mapping");
                        optionMappingList.stream().forEach(optionMapping->{
                            if(optionMapping.get("target_option").toString().equals(targetDate.get(targetFieldApiName).toString())){
                                map.put(sourceFieldApiName,optionMapping.get("source_option"));
                            }
                        });
                    }else{
                        map.put(sourceFieldApiName,targetDate.get(targetFieldApiName));
                    }
                }
            });
        });
        return map;
    }

    public static void checkAuthorAndTips(User user,IObjectData data,ObjectAction objectAction){
        if (user.isSupperAdmin()){
            return;
        }
        if(isGrayCheckAuthorAndTips(user.getTenantId())){
            if(ObjectLockStatus.LOCK.getStatus().equals(data.get("lock_status",String.class))){
                throw new ValidateException(String.format(I18N.text(SFAI18NKeyUtil.SFA_DATA_NOT_ANY_OPERATION),I18N.text(objectAction.getI18NKey()),ObjectLockStatus.LOCK.getLabel(),data.getName()));
            }
        }

    }

    public static boolean checkAddAccountMeanwhileAddAddrKeyValue(User user){
        String value = SFAConfigUtil.getConfigValue(user.getTenantId(),ConfigType.ADD_ACCOUNT_MEANWHILE_ADD_ADDR.getKey(), user.getUpstreamOwnerIdOrUserId());
        if("1".equals(value)){
            return true;
        }
        return false;
    }
    public static boolean checkAccountAndAddrSyncUpdLocationKeyValue(User user){
        String value = SFAConfigUtil.getConfigValue(user.getTenantId(),ConfigType.ACCOUNT_AND_ADDR_SYNC_UPD_LOCATION.getKey(), user.getUpstreamOwnerIdOrUserId());
        if("1".equals(value)){
            return true;
        }
        return false;
    }
    public static boolean isOpenAccountAddrConfig(User user){
        String value = SFAConfigUtil.getConfigValue(user.getTenantId(),ConfigType.IS_OPEN_ACCOUNT_ADDR_CONFIG.getKey(), user.getUpstreamOwnerIdOrUserId());
        if("1".equals(value)){
            return true;
        }
        return false;
    }
    public static boolean checkIsNeedUpdMainAddr(User user,ObjectDataDocument objectDataDocument,IObjectData dbObjectData){
        if(!isGrayCheckIsNeedUpdMainAddr(user.getTenantId())){
            return true;
        }
        log.warn("checkIsNeedUpdMainAddr tenant_id:{}",user.getTenantId());
        boolean flag = false;
        for(String field : AccountConstants.needUpdAddrFields){
            if(!objectDataDocument.containsKey(field)){
                continue;
            }
            String dbValue = AccountUtil.getStringValue(dbObjectData,field,"");
            String newValue = AccountUtil.getStringValue(objectDataDocument,field,"");
            if(!dbValue.equals(newValue)){
                flag = true;
                break;
            }
        }
        return flag;
    }

    public static void replaceInheritFieldsValue(IObjectDescribe objectDescribe,ObjectDataDocument objectDataDocument,IObjectData dbMasterData){
        try {
            if(ObjectUtils.allIsEmpty(objectDescribe.getInheritFields()) || !objectDescribe.getInheritFields().containsKey(SFAPreDefineObject.AccountMainData.getApiName())){
                log.info("replaceInheritFieldsValue getInheritFields is null");
                return ;
            }
            if(ObjectUtils.allIsEmpty(dbMasterData)){
                log.warn("replaceInheritFieldsValue dbMasterData is null");
                return;
            }
            List<String> inheritFieldsList = (List<String>)objectDescribe.getInheritFields().get(SFAPreDefineObject.AccountMainData.getApiName());
            inheritFieldsList.stream().forEach(field->{
                if(objectDataDocument.containsKey(field) && ObjectUtils.allNotEmpty(objectDataDocument.get(field))){
                    Object newValue = objectDataDocument.get(field);
                    Object oldValue = dbMasterData.get(field);
                    if(!newValue.equals(oldValue)){
                        log.warn("replaceInheritFieldsValue field:{},newValue:{}",field,newValue);
                        objectDataDocument.put(field,oldValue);
                    }
                }
            });
        }catch (Exception e){
            log.error("replaceInheritFieldsValue error e:",e);
        }
    }
}
