package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.CommonProductConstants;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.AvailableRangeCoreService;
import com.facishare.crm.sfa.predefine.service.NonstandardAttributeService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeRangeService;
import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeRangeMode;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeValue;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.BomConstraintService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreServiceImpl;
import com.facishare.crm.sfa.predefine.service.cpq.BomService;
import com.facishare.crm.sfa.predefine.service.cpq.ProductConstraintLinesService;
import com.facishare.crm.sfa.predefine.service.cpq.ProductConstraintService;
import com.facishare.crm.sfa.predefine.service.cpq.model.QueryAllSubBomModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.BomConstraintConstants;
import com.facishare.crm.sfa.utilities.constant.BomCoreConstants;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.enums.EnumUtil;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.crm.sfa.utilities.util.DhtUtil;
import com.facishare.crm.sfa.utilities.util.FunctionUtils;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.Price.RealPriceModel;
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.crm.util.GsonUtil;
import com.facishare.crm.util.MtCurrentUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardTreeRelatedListV1Controller;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_SUB_FIELD_MODIFIED_ADJUST_PRICE;
import static com.facishare.paas.metadata.api.DBRecord.ID;

/**
 * <AUTHOR>
 * @date 2019/9/2 10:55
 */
public class BomTreeRelatedListV1Controller extends StandardTreeRelatedListV1Controller {

    private boolean needProductConstraint = false;
    private boolean filterGroupStatusFlag = false;
    private List<IObjectData> allProductDataList = Lists.newArrayList();

    private Map<String, Map<String, Object>> productNonstandardAttributeMap = Maps.newHashMap();
    private List<String> parentBomIds = Lists.newArrayList();

    private final List<ObjectDataDocument> globalRootBomList = Lists.newArrayList();

    private final BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreServiceImpl.class);
    private final AttributeRangeService attributeRangeService = SpringUtil.getContext().getBean(AttributeRangeService.class);


    private final BomService bomService = SpringUtil.getContext().getBean(BomService.class);
    private final ProductConstraintService productConstraintService = SpringUtil.getContext().getBean(ProductConstraintService.class);
    private final ProductConstraintLinesService constraintLinesService = SpringUtil.getContext().getBean(ProductConstraintLinesService.class);
    private final NonstandardAttributeService nonstandardAttributeService = SpringUtil.getContext().getBean(NonstandardAttributeService.class);
    private final AvailableRangeCoreService availableRangeCoreService = SpringUtil.getContext().getBean(AvailableRangeCoreService.class);
    private final AttributeCoreService attributeCoreService = SpringUtil.getContext().getBean(AttributeCoreService.class);
    private final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private final BomConstraintService bomConstraintService = SpringUtil.getContext().getBean(BomConstraintService.class);

    private final QuoterCommonController quoterCommonController = SpringUtil.getContext().getBean(QuoterCommonController.class);

    private final FunctionUtils functionUtils = SpringUtil.getContext().getBean(FunctionUtils.class);
    private Map<String, List<IObjectData>> nonstandardAttributeMap = Maps.newHashMap();
    private Map<String, List<Attribute>> attributeMap = Maps.newHashMap();

    private final List<String> rootProductIdList = Lists.newArrayList();
    private final Map<String, String> bomIdToPriceId = Maps.newHashMap();
    private final Map<String, String> rootProductIdToPriceId = Maps.newHashMap();
    private Integer total=0;
    private Integer offset=0;
    private Integer limit=10;


    private boolean enableBomCore = false;

    private String bomCoreId = null;
    private String newBomPath = null;

    private boolean canUseQuoter = false;

    private String oldBomId = null;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //是否灰度了报价器功能
        canUseQuoter = Boolean.TRUE.equals(GrayUtil.isGrayEnable(controllerContext.getTenantId(), GrayUtil.QUOTER_OPEN_TENANT_ID));
        if(canUseQuoter && quoterCommonController.isFromQuoter(arg.getExtraData())) {
            //过滤掉默认值选项，兼容根据属性匹配到的子件需要默认选中的场景（不进入配置页会多一个selected_by_default参数）
            quoterCommonController.removeFilterByFilterName(arg, Lists.newArrayList(BomConstants.FIELD_SELECTED_BY_DEFAULT));
        }
    }

    @Override
    protected void init() {
        if (CollectionUtils.notEmpty(arg.getRootProductList())) {
            arg.getRootProductList().forEach(x -> {
                if (StringUtils.isNotBlank(MapUtils.getString(x, BomConstants.FIELD_PRODUCT_ID))) {
                    rootProductIdToPriceId.put(MapUtils.getString(x, BomConstants.FIELD_PRODUCT_ID), MapUtils.getString(x, SalesOrderConstants.SalesOrderProductField.PRICE_BOOK_ID.getApiName(), ""));
                    rootProductIdList.add(MapUtils.getString(x, BomConstants.FIELD_PRODUCT_ID));
                }
            });
        } else {
            CollectionUtils.nullToEmpty(arg.getRootProductIds()).forEach(x -> {
                if (StringUtils.isNotBlank(x)) {
                    rootProductIdToPriceId.put(x, arg.getPriceBookId());
                    rootProductIdList.add(x);
                }
            });
        }

        super.init();
        needProductConstraint = Objects.equals(Boolean.TRUE, arg.getIncludeConstraint());
        //租户启用产品组合，优先判断rootBomCoreId是否有值，如果有值，则认为是新的Bom结构的交互方式,如果没有，根据bomId查coreId, 如果bomId没值，则通过rootProductIds取查coreId
        //根据rootProductIds去查询rootBomCoreId(兼容历史数据，因为只刷bom历史数据，没有刷订单、报价单、销售合同的明细数据)
        if(GrayUtil.bomMasterSlaveMode(controllerContext.getTenantId())) {
            //如果此属性有传值，则认为是采用了新的Bom结构的交互方式
            if (StringUtils.isNotBlank(arg.getRootBomCoreId())) {
                bomCoreId = arg.getRootBomCoreId();
                //根节点查询时，无法取到newBomPath，需要通过bomCoreId去取查询一次
                newBomPath = StringUtils.isNotBlank(arg.getNewBomPath()) ? arg.getNewBomPath() : getRootBomInfo(bomCoreId);
                enableBomCore = true;
            } else if(StringUtils.isNotBlank(arg.getBomId())) {
                List<IObjectData> tempDataList = bomService.getBomListByIds(controllerContext.getUser(), Lists.newArrayList(arg.getBomId()));
                if(CollectionUtils.notEmpty(tempDataList)) {
                    fillRequestParams(tempDataList.get(0));
                    globalRootBomList.add(ObjectDataDocument.of(tempDataList.get(0)));
                } else {
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS_SHOW_PARAM, "bom_core_id"));
                }
            } else if(CollectionUtils.notEmpty(arg.getRootProductIds())){//暂时留着，后续可以去掉（确保前端都已经传参数）
                String productId = arg.getRootProductIds().iterator().next();
                if(StringUtils.isEmpty(productId)) {
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NOT_FIND_BOM_CORE_PRODUCT_ID_ERROR));
                }
                IObjectData tempData = getBomsByProductId(productId);
                globalRootBomList.add(ObjectDataDocument.of(tempData));
                fillRequestParams(tempData);
            } else {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS_SHOW_PARAM, "bom_core_id"));
            }
            if(StringUtils.isNotBlank(bomCoreId)) {
                IObjectData rootProduct = bomService.getRootBomByCoreId(bomCoreId, controllerContext.getUser(), false);
                if(rootProduct != null) {
                    String realRootProductId = rootProduct.get(BomConstants.FIELD_PRODUCT_ID, String.class);
                    rootProductIdList.clear();
                    rootProductIdList.add(realRootProductId);//只可能有一条数据

                    rootProductIdToPriceId.clear();
                    rootProductIdToPriceId.put(realRootProductId, arg.getPriceBookId());
                    globalRootBomList.add(ObjectDataDocument.of(rootProduct));
                }
            }
        }
        //兼容历史数据，再做判断
        if (CollectionUtils.empty(rootProductIdList)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MISS_REQUIRED_PARAMETERS));
        }
        fillAccountId();
    }

    private void fillRequestParams(IObjectData tempData) {
        bomCoreId = tempData.get(BomConstants.FIELD_CORE_ID, String.class);
        //根节点查询时，无法取到newBomPath，需要通过bomCoreId去取查询一次
        String tempNewBomPath = tempData.get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class);
        newBomPath = StringUtils.isNotBlank(tempNewBomPath) ? tempNewBomPath : tempData.get(BomConstants.FIELD_BOM_PATH, String.class);
        enableBomCore = true;
    }

    private IObjectData getBomsByProductId(String productId){
        List<IObjectData> dataList = bomService.getBomsByProductId(productId, controllerContext.getUser());
        //取回的数据如果不是唯一的，则要么是数据没刷对，要么是参数有问题
        if(CollectionUtils.empty(dataList)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NOT_FIND_BOM_CORE_ERROR));
        }
        if(dataList.size() > 1){
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_MULTI_FIND_BOM_CORE_ERROR));
        }
        return dataList.get(0);
    }
    private String getRootBomInfo(String bomCoreId){
        IObjectData result = bomService.getRootBomByCoreId(bomCoreId, controllerContext.getUser(), false);
        if(result != null){
            return result.get(BomConstants.FIELD_BOM_PATH, String.class);
        }
        return null;
    }

    @Override
    protected SearchTemplateQuery buildTreeSearchTemplateQuery(ObjectDescribeExt objectDescribeExt, String searchQueryInfo) {
        controllerContext.getRequestContext().setAttribute("skipRelevantTeam", true);
        List<String> bomIds = Lists.newArrayList(getBomIds());
        SearchTemplateQuery query = serviceFacade.getSearchTemplateQuery(controllerContext.getUser(), objectDescribe,
                getSearchTemplateId(), searchQueryInfo, true);
        limit=query.getLimit();
        offset=query.getOffset();
        query.setNeedReturnCountNum(true);
        query.setPermissionType(0);
        query.setNeedReturnQuote(Boolean.TRUE);
        filterGroupStatusFlag = CollectionUtils.nullToEmpty(query.getFilters()).stream().anyMatch(f -> Objects.equals(f.getFieldName(), BomConstants.FIELD_ENABLED_STATUS));
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, "life_status", "normal");
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, "is_deleted", "0");
        if (CollectionUtils.empty(bomIds)) {
            return query;
        }
        oldBomId = bomIds.get(0);

        getFilterParentBomId(query);
        IObjectData objectData = Objects.isNull(arg.getObjectData()) ? null : arg.getObjectData().toObjectData();
        query = functionUtils.handleFiltersByFunction(controllerContext.getUser(), Utils.BOM_API_NAME, query, objectData , Maps.newHashMap());
        if (bomIds.size() == 1) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, BomConstants.FIELD_ROOT_ID, bomIds);
            if(arg.isQueryFirstLevelChild()){
                SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, BomConstants.FIELD_PARENT_BOM_ID, bomIds);
            }
            SearchTemplateQueryExt.of(query).addFilter(Operator.NEQ, ID, bomIds);
        } else {
            SearchTemplateQueryExt.of(query).addFilter(Operator.IN, BomConstants.FIELD_ROOT_ID, bomIds);
            if(arg.isQueryFirstLevelChild()){
                SearchTemplateQueryExt.of(query).addFilter(Operator.IN, BomConstants.FIELD_PARENT_BOM_ID, bomIds);
            }
            SearchTemplateQueryExt.of(query).addFilter(Operator.NIN, ID, bomIds);
        }
        log.info("treev1 query info:{}",query.toJsonString());
        return query;
    }

    private void getFilterParentBomId(SearchTemplateQuery query) {
        List<IFilter> filters = query.getFilters();
        if (CollectionUtils.empty(filters)) {
            return;
        }
        //分组过滤条件
        parentBomIds = filters.stream()
                .filter(x -> BomConstants.FIELD_PARENT_BOM_ID.equals(x.getFieldName())|| ID.equals(x.getFieldName()))
                .map(IFilter::getFieldValues)
                .flatMap(Collection::stream)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    protected ILayout getChildDetailLayout() {
        ILayout childLayout = super.getChildDetailLayout();

        if (!RequestUtil.isMobileRequest()) {
            return childLayout;
        }
        LayoutExt bomLayout = LayoutExt.of(childLayout);
        bomLayout.getFormComponent().flatMap(FormComponentExt::getFieldSection).ifPresent(fieldSection -> {
            List<IFormField> fields = fieldSection.getFields();
            fields.stream().filter(o -> !o.getFieldName().endsWith("__c") && !("amount".equals(o.getFieldName()) || "adjust_price".equals(o.getFieldName()))).forEach(o -> o.setReadOnly(true));

            IFormField modifiedAdjustPriceFormField = new FormField();
            modifiedAdjustPriceFormField.setReadOnly(false);
            modifiedAdjustPriceFormField.setRequired(false);
            modifiedAdjustPriceFormField.setRenderType("currency");
            modifiedAdjustPriceFormField.setFieldName("modified_adjust_price");
            IntStream.range(0, fields.size()).filter(i -> "adjust_price".equals(fields.get(i).getFieldName()))
                    .findFirst().ifPresent(i -> {
                fields.add(i + 1, modifiedAdjustPriceFormField);
                fieldSection.setFields(fields);
                try {
                    List<IComponent> components = bomLayout.getComponents();
                    components.removeIf(o -> "relevant_team_component".equals(o.getName()));
                    childLayout.setComponents(components);
                } catch (MetadataServiceException e) {
                    log.error("v1 layout.getComponents error:" + e.getMessage(), e);
                }
            });
        });

        return childLayout;
    }

    @Override
    protected List<ILayout> getDetailLayout() {
        return Lists.newArrayList(this.getChildDetailLayout());
    }

    @Override
    protected Result buildResult(List<ILayout> mobileLayouts, List<ILayout> detailLayouts, List<IObjectData> queryChildList) {
        Result result = super.buildResult(mobileLayouts, detailLayouts, queryChildList);
        if (!RequestUtil.isMobileRequest()) {
            return result;
        }
        if (arg.getIncludeDesc()) {
            List<ObjectDescribeDocument> tmp = Lists.newArrayList();
            List<ObjectDescribeDocument> objectDescribes = result.getObjectDescribes();
            if (CollectionUtils.notEmpty(objectDescribes)) {
                objectDescribes.stream().filter(Objects::nonNull).forEach(x -> {
                    IObjectDescribe describe = x.toObjectDescribe();
                    tmp.add(ObjectDescribeDocument.of(ObjectDescribeExt.of(describe).copyOnWrite()));
                });
            }
            tmp.stream().filter(o -> SFAPreDefineObject.Bom.getApiName().equals(o.toObjectDescribe().getApiName())).findFirst().ifPresent(objectDescribeDocument -> {
                IFieldDescribe adjustPrice = objectDescribeDocument.toObjectDescribe().getFieldDescribe("adjust_price");
                if (Objects.nonNull(adjustPrice)) {
                    IFieldDescribe modifiedAdjustPrice = FieldDescribeExt.copy(adjustPrice);
                    modifiedAdjustPrice.setApiName("modified_adjust_price");
                    modifiedAdjustPrice.setId("123");
                    modifiedAdjustPrice.setLabel(I18N.text(SO_SUB_FIELD_MODIFIED_ADJUST_PRICE));
                    objectDescribeDocument.toObjectDescribe().addFieldDescribe(modifiedAdjustPrice);
                }
            });
            result.setObjectDescribes(tmp);
        }
        return result;
    }

    /**
     * 根据产品Id 找到该产品所配置的产品约束关系
     * @param bomList
     */
    private void fillAttrInfo(Arg arg, List<ObjectDataDocument> bomList) {
        if (SFAConfigUtil.isSimpleBom(controllerContext.getTenantId())) {
            return;
        }
        List<String> products = bomList.parallelStream().map(x -> MapUtils.getString(x, BomConstants.FIELD_PRODUCT_ID)).collect(Collectors.toList());
        products.addAll(globalRootBomList.stream()
                .map(data -> data.get(BomConstants.FIELD_PRODUCT_ID).toString()).collect(Collectors.toList()));
        if (CollectionUtils.empty(products)) {
            return;
        }
        if (bizConfigThreadLocalCacheService.isOpenAttribute(controllerContext.getTenantId())) {
            attributeMap = attributeCoreService.getAttributeByProductIds(controllerContext.getUser(), null, false, allProductDataList);
            bomList.forEach(objectData -> {
                List<Attribute> attributeList = attributeMap.get(objectData.get(BomConstants.FIELD_PRODUCT_ID));
                //重置默认属性值处理
                List<Attribute> oldAttibuteList = resetDefaultAttr(attributeList, arg);
                //如果有更改过默认值，则会返回最原始的属性值，供前端计算原始默认值对应的价格
                if (CollectionUtils.notEmpty(oldAttibuteList)) {
                    objectData.put(AttributeConstants.OLD_ATTRIBUTE, oldAttibuteList);
                }
                objectData.put(AttributeConstants.ATTRIBUTE, attributeList);
            });
        }
        nonstandardAttributeMap = nonstandardAttributeService.getNonstandardAttributeDataByProductIds(controllerContext.getUser(), null, allProductDataList);
        if (MapUtils.isNotEmpty(nonstandardAttributeMap)) {
            ObjectDataDocument.ofDataList(bomList).forEach(objectData -> {
                List<IObjectData> objectDataList = nonstandardAttributeMap.get(objectData.get(BomConstants.FIELD_PRODUCT_ID, String.class));
                //回填非标属性值
                setNonAttrValue(arg, objectDataList);
                setAttr(objectData, objectDataList);
            });
        }
    }

    private void fillQuoterMatchProduct(Arg arg, List<ObjectDataDocument> bomList, Result rst) {
        if(!quoterCommonController.isFromQuoter(arg.getExtraData())) {
            return;
        }
        //根节点是否符合筛选条件
        boolean isRootMatched = isRootProductMatched(arg, rst);
        if(CollectionUtils.empty(bomList)) {
            return;
        }
        //根节点是否符合筛选条件，所有节点都需要勾选
        if(isRootMatched) {
            bomList.stream().forEach(bom -> {
                //如果是根节点匹配上那么当前Bom下的所有子节点都需要打标，供前端处理勾选使用
                bom.toObjectData().set(BomConstants.VIRTUAL_FIELD_SELECTED_BY_QUOTER, true);
            });
        } else {
            Map<String, List<String>> quoterAttrMap = quoterCommonController.getQueryInfo(arg.getExtraData(), QuoterCommonController.KEY_STANDARD_ATTRIBUTE_QUERY_INFO);
            Map<String, List<String>> quoterNonAttrMap = quoterCommonController.getQueryInfo(arg.getExtraData(), QuoterCommonController.KEY_NONSTANDARD_ATTRIBUTE_QUERY_INFO);
            List<String> quoterAttrIds = Lists.newArrayList(quoterAttrMap.keySet());
            List<String> quoterNonAttrIds = Lists.newArrayList(quoterNonAttrMap.keySet());
            bomList.stream().forEach(bom -> {
                boolean needSelected = false;
                List<Attribute> list = bom.toObjectData().get(AttributeConstants.ATTRIBUTE, List.class, Lists.newArrayList());
                List<String> attrIds = Optional.ofNullable(list).orElse(Lists.newArrayList()).stream().map(x -> "attribute" + x.getFieldNum()).collect(Collectors.toList());
                needSelected = attrIds.size() > 0 && attrIds.containsAll(quoterAttrIds);
                //如果属性不匹配，则不往下继续判断，不用选中处理
                if (!needSelected) {
                    return;
                }
                List<Map> nonList = bom.toObjectData().get(AttributeConstants.NON_ATTRIBUTE, List.class, Lists.newArrayList());
                if (CollectionUtils.notEmpty(nonList)) {
                    List<String> nonAttrIds = nonList.stream().map(x -> x.get("id").toString()).collect(Collectors.toList());
                    needSelected = nonAttrIds.size() > 0 && nonAttrIds.containsAll(quoterNonAttrIds);
                }
                if (needSelected) {
                    //当前子节点匹配了属性和非标属性，需要打标，供前端处理勾选使用
                    bom.toObjectData().set(BomConstants.VIRTUAL_FIELD_SELECTED_BY_QUOTER, true);
                }
            });
        }
    }
    @Override
    protected BaseListController.Result after(Arg arg, BaseListController.Result result) {
        Result rst = (Result) super.after(arg, result);
        Optional<ObjectData> objData = rst.getDataMapList().stream().filter(o -> Utils.BOM_API_NAME.equals(o.getDescribeApiName())).findFirst();
        List<ObjectDataDocument> bomList = objData.isPresent() ? objData.get().getDataList() : Lists.newArrayList();
        log.info("db-queryAllProductList-".concat(bomList.size()+""));
        // 给Bom数据补充相关信息
        IFieldDescribe priceModeFieldDescribe = objectDescribe.getFieldDescribe(BomConstants.FIELD_PRICE_MODE);
        Object priceMode = Optional.ofNullable(priceModeFieldDescribe).map(IFieldDescribe::getDefaultValue).orElse(EnumUtil.PriceMode.DEF.getValue());
        if(enableBomCore) {
            bomList.stream().filter(Objects::nonNull).forEach(x -> {
                x.put(BomConstants.FIELD_NODE_TYPE, EnumUtil.nodeType.stand.getValue());
                //给每个bom的newBomPath赋值，拼接完整路径
                String toReplaceBomPath = bomService.getNewBomPath(x.toObjectData(), newBomPath);
                //如果前端没传，则此字段值与bomPath一致，方便前端统一用新字段处理
                x.put(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, toReplaceBomPath);
                x.put(BomConstants.VIRTUAL_FIELD_CURRENT_ROOT_NEW_PATH, newBomPath);
                if(StringUtils.isBlank(MapUtils.getString(x,MultiRecordType.RECORD_TYPE))){
                    x.put(MultiRecordType.RECORD_TYPE,MultiRecordType.RECORD_TYPE_DEFAULT);
                }
            });
        }


        List<ObjectDataDocument> tmpBomList = Lists.newArrayList();
        CollectionUtils.nullToEmpty(arg.getBomList()).stream().filter(x -> Objects.equals(EnumUtil.nodeType.temp.getValue(), MapUtils.getString(x, BomConstants.FIELD_NODE_TYPE))).forEach(x -> {
            x.put(ID, serviceFacade.generateId());
            if (Objects.equals(String.valueOf(EnumUtil.PriceMode.PRICE_BOOK.getValue()), priceMode.toString())) {
                x.put(BomConstants.FIELD_PRICE_MODE, EnumUtil.PriceMode.PRICE_BOOK.getValue());
            }
            tmpBomList.add(x);
        });
        bomList.addAll(tmpBomList);
        queryAllProductList(bomList);
        stopWatch.lap("queryAllProductList-".concat(bomList.size()+""));
        fillAttrInfo(arg, bomList);
        stopWatch.lap("fillAttrInfo");
        // 佳能灰度控制，下发priceBookProduct__ro
        if (GrayUtil.isGrayBomPriceBookProduct(controllerContext.getTenantId())) {
            fillPriceBookProduct(bomList, arg.getPriceBookId(), rst, arg.getMcCurrency());
            stopWatch.lap("fillPriceBookProduct");
        } else {
            // 只下发根节点的价目表数据
            fillPriceBookProduct(arg.getPriceBookId(), rst, arg.getMcCurrency());
            stopWatch.lap("fillPriceBookProduct");
            if (bomList.size()>200) {
                ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
                Lists.partition(bomList,200).forEach(x-> parallelTask.submit(() -> getBomPriceByAccountIdAndProId(x)));
                try {
                    parallelTask.await(10, TimeUnit.SECONDS);
                } catch (Exception e) {
                    this.log.error("Error in fill price of bom", e);
                }
            } else {
                getBomPriceByAccountIdAndProId(bomList);
            }
            stopWatch.lap("getBomListPrice");
        }
        if(arg.isQueryChild()){
            bomCoreService.fillHasChild(controllerContext.getUser(), ObjectDataDocument.ofDataList(bomList));
            stopWatch.lap("fillHasChild");
        }
        fillRootProduct(arg, rst);
        //将符合筛选条件的子件，设置为默认勾选
        fillQuoterMatchProduct(arg, bomList, rst);

        stopWatch.lap("fillRootProduct");
        fillData(bomList, priceMode);
        stopWatch.lap("fillDataList");
        fillProductGroup(bomList, rst);
        stopWatch.lap("fillProductGroup");
        fillProductConstraint(rst);
        stopWatch.lap("loadConstraint");
        getPage(rst);
        if(enableBomCore && arg.isIncludeAllSubCoreId()) {
            stopWatch.lap("fillAllSubBomList");
            Map<String, String> path2coreIds = Maps.newHashMap();
            //兼容不指定版本时，二次配置获取不到完整BOM结构
            if (CollectionUtils.notEmpty(arg.getBomList())) {
                Predicate<ObjectDataDocument> existCoreIdsPredicate = x ->
                        StringUtils.isNotBlank(x.toObjectData().get(BomConstants.FIELD_RELATED_CORE_ID, String.class)) &&
                                StringUtils.isNotBlank(x.toObjectData().get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class));
                path2coreIds.putAll(
                        arg.getBomList().stream().filter(existCoreIdsPredicate)
                        .collect(Collectors.toMap(
                                        x -> x.toObjectData().get(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, String.class),
                                        x -> x.toObjectData().get(BomConstants.FIELD_RELATED_CORE_ID, String.class),
                                        (oldValue, newValue) -> oldValue
                                )
                        )
                );
            }
            fillAllSubBomList(controllerContext.getUser(),  bomCoreId, newBomPath, rst, path2coreIds);
        }
        return rst;
    }

    private void fillAllSubBomList( User user, String bomCoreId, String newBomPath, Result rst, Map<String, String> path2coreIds) {
        Map toAppendPath2coreIds = Maps.newHashMap();
        fillNewAllSubBomList(user, bomCoreId, newBomPath, rst, toAppendPath2coreIds, false);
        //有未指定版本的复用BOM,需要根据前端传过来的复用BOM,进行填充，构建出完整的BOM结构（二次配置需要此功能）
        if(CollectionUtils.notEmpty(path2coreIds) && !toAppendPath2coreIds.keySet().containsAll(path2coreIds.keySet())) {
            List<String> restList = path2coreIds.keySet().stream().filter(x-> !(toAppendPath2coreIds.keySet().contains(x))).collect(Collectors.toList());
            restList.stream().forEach(x -> fillNewAllSubBomList(user, path2coreIds.get(x), x, rst, toAppendPath2coreIds, true));
        }
    }

    private void fillNewAllSubBomList( User user, String bomCoreId, String newBomPath, Result rst, Map<String, String> toAppendPath2coreIds, boolean needAppendSelf) {
        List<Map<String, String>> listMap = rst.getAllSubBomList() == null ? Lists.newArrayList() : rst.getAllSubBomList();
        if(needAppendSelf) {
            Map selfMap = Maps.newHashMap();
            selfMap.put("bom_core_id", bomCoreId);
            selfMap.put("new_bom_path", newBomPath);
            selfMap.put("new_bom_id", newBomPath.substring(newBomPath.lastIndexOf(".") + 1));
            listMap.add(selfMap);
            toAppendPath2coreIds.put(newBomPath, bomCoreId);
        }
        QueryAllSubBomModel.Result result = bomService.queryAllSubBomCore(user, QueryAllSubBomModel.Arg.builder().bomCoreId(bomCoreId).newBomPath(newBomPath).build());
        if(CollectionUtils.notEmpty(result.getAllSubBomList())) {
            List<Map<String, String>> allSubBomList = result.getAllSubBomList().stream().map(x -> {
                Map<String, String> map = Maps.newHashMap();
                map.put("bom_core_id", x.getBomCoreId());
                map.put("new_bom_id", x.getNewBomId());
                map.put("new_bom_path", x.getNewBomPath());
                toAppendPath2coreIds.put(x.getNewBomPath(), x.getBomCoreId());
                return map;
            }).collect(Collectors.toList());
            listMap.addAll(allSubBomList);
        }
        rst.setAllSubBomList(listMap);
    }

    //根据母件价目表过滤子件
    private boolean filterBomAccordingPriceBook() {
        return GrayUtil.filterBomAccordingPriceBook(controllerContext.getTenantId());
    }

    private void getPage(Result rst) {
        rst.setTotal(total);
        rst.setLimit(limit);
        rst.setOffset(offset);
    }


    private void queryAllProductList(List<ObjectDataDocument> bomList) {
        List<String> productIds = bomList.parallelStream().map(x -> MapUtils.getString(x, BomConstants.FIELD_PRODUCT_ID)).collect(Collectors.toList());
        productIds.addAll(globalRootBomList.stream()
                .map(data -> data.get(BomConstants.FIELD_PRODUCT_ID).toString()).collect(Collectors.toList()));
        allProductDataList = serviceFacade.findObjectDataByIdsIgnoreRelevantTeam(controllerContext.getTenantId(), productIds, Utils.PRODUCT_API_NAME);
        if (CollectionUtils.notEmpty(allProductDataList)) {
            allProductDataList.parallelStream().filter(data->StringUtils.isNotBlank(data.get(ProductConstants.NON_ATTRIBUTE_VALUES, String.class))).forEach(product->{
                Map<String, Object> dbDefaultValues = JSON.parseObject(product.get(ProductConstants.NON_ATTRIBUTE_VALUES, String.class, "{}"), Map.class);
                productNonstandardAttributeMap.put(product.getId(), dbDefaultValues);
            });
        }
    }

    /**
     * 除了 Bis001（佳能）外的企业走该逻辑
     *
     * @param bomList
     */
    private void getBomPriceByAccountIdAndProId(List<ObjectDataDocument> bomList) {
        if (StringUtils.isBlank(arg.getAccountId())) {
            return;
        }
        List<ObjectDataDocument> newBomList = bomList.stream().filter(x -> MapUtils.getIntValue(x, BomConstants.FIELD_PRICE_MODE, 0) == EnumUtil.PriceMode.PRICE_BOOK.getValue()).collect(Collectors.toList());
        if (CollectionUtils.empty(newBomList)) {
            return;
        }
        RealPriceModel.Arg param = new RealPriceModel.Arg();
        param.setAccountId(arg.getAccountId());
        param.setPartnerId(arg.getPartnerId());
        param.setMcCurrency(arg.getMcCurrency());
        param.setObjectData(arg.getObjectData());
        param.setDetails(arg.getDetails());
        List<RealPriceModel.FullProduct> fullProductList = Lists.newArrayList();
        boolean enforcePriority = bizConfigThreadLocalCacheService.isEnforcePriority(controllerContext.getTenantId());
        boolean flag = bizConfigThreadLocalCacheService.bomAdaptationPriceListRules(controllerContext.getTenantId());
        if (CollectionUtils.empty(arg.getBomList())) {
            //首次取价
            newBomList.forEach(x -> handleParam(fullProductList, x, enforcePriority, bomIdToPriceId.get(x.get(BomConstants.FIELD_ROOT_ID)), flag));
        } else {
            //二次配置重新取价
            newBomList.forEach(x -> reHandleParam(fullProductList, x, enforcePriority, bomIdToPriceId.get(x.get(BomConstants.FIELD_ROOT_ID)), flag));
        }
        if (CollectionUtils.empty(fullProductList)) {
            return;
        }
        param.setFullProductList(fullProductList);
        param.setOnlySimpleSearch(Boolean.TRUE);
        List<IObjectData> allNode = Lists.newArrayList();
        allNode.addAll(ObjectDataDocument.ofDataList(globalRootBomList));
        allNode.addAll(ObjectDataDocument.ofDataList(bomList));
        param.setRootBomList(allNode);
        setBomPrice(bomList, param);
    }

    private void handleParam(List<RealPriceModel.FullProduct> fullProductList, ObjectDataDocument x, boolean enforcePriority, String priceBookId, boolean flag) {
        RealPriceModel.FullProduct entity = new RealPriceModel.FullProduct();
        entity.setProductId(MapUtils.getString(x, BomConstants.FIELD_PRODUCT_ID));
        setNodeDefaultAttribute(x, entity);
        if (!enforcePriority && !flag && StringUtils.isNotBlank(priceBookId)) {
            entity.setPriceBookId(priceBookId);
        }
        entity.setUnit(MapUtils.getString(x, BomConstants.FIELD_UNIT_ID, ""));
        entity.setRootBomId(MapUtils.getString(x, BomConstants.FIELD_ROOT_ID, ""));
        entity.setBomId(x.getId());
        entity.setParentBomId(MapUtils.getString(x, BomConstants.FIELD_PARENT_BOM_ID));
        fullProductList.add(entity);
    }

    private void setNodeDefaultAttribute(ObjectDataDocument x, RealPriceModel.FullProduct entity) {
        Optional.ofNullable(x.get("attribute"))
                .filter(attr -> attr instanceof List)
                .map(attr -> (List<Attribute>) attr)
                .filter(CollectionUtils::notEmpty)
                .map(attrList -> {
                    Map<String, String> attrMap = Maps.newHashMap();
                    attrList.forEach(attr -> {
                        List<AttributeValue> attributeValues = attr.getAttributeValues();
                        if (CollectionUtils.notEmpty(attributeValues)) {
                            AttributeValue attributeValue = attributeValues.stream()
                                    .filter(p -> Objects.equals("1", p.getIs_default()))
                                    .findFirst()
                                    .orElse(attributeValues.get(0));
                            attrMap.put(attr.getId(), attributeValue.getId());
                        }
                    });
                    return attrMap;
                })
                .ifPresent(entity::setAttrMap);
    }

    private void reHandleParam(List<RealPriceModel.FullProduct> fullProductList, ObjectDataDocument x, boolean enforcePriority, String priceBookId, boolean flag) {
        RealPriceModel.FullProduct entity = new RealPriceModel.FullProduct();
        entity.setProductId(MapUtils.getString(x, BomConstants.FIELD_PRODUCT_ID));
        ObjectDataDocument bom = arg.getBomList().stream().filter(k -> Objects.equals(k.get(BomConstants.FIELD_BOM_ID), x.getId())).findFirst().orElse(null);
        if (Objects.isNull(bom)) {
            setNodeDefaultAttribute(x, entity);
        } else {
            if (Objects.nonNull(bom.get("attribute_json")) && bom.get("attribute_json") instanceof Map) {
                entity.setAttrMap((Map<String, String>) bom.get("attribute_json"));
            }
        }
        //以防price_book_id取出来的是 null
        String nodePriceBookId = Optional.ofNullable(bom).map(t -> Objects.toString(t.getOrDefault("price_book_id", ""), "")).orElse("");
        if (!enforcePriority && StringUtils.isNotBlank(nodePriceBookId)) {
            entity.setPriceBookId(nodePriceBookId);
        } else {
            if (!enforcePriority && !flag && StringUtils.isNotBlank(priceBookId)) {
                entity.setPriceBookId(priceBookId);
            }
        }
        entity.setRootBomId(MapUtils.getString(x, BomConstants.FIELD_ROOT_ID, ""));
        entity.setBomId(x.getId());
        entity.setParentBomId(MapUtils.getString(x, BomConstants.FIELD_PARENT_BOM_ID));
        entity.setUnit(MapUtils.getString(x, BomConstants.FIELD_UNIT_ID, ""));
        fullProductList.add(entity);
    }

    private void setBomPrice(List<ObjectDataDocument> bomList, RealPriceModel.Arg param) {
        boolean flag = filterBomAccordingPriceBook();
        RealPriceModel.Result realPrice = availableRangeCoreService.getRealPrice(controllerContext.getUser(), param);
        List<ObjectDataDocument> newRst = realPrice.getNewRst();
        if (CollectionUtils.empty(newRst)) {
            log.warn("取价接口返回为空！");
        }
        Map<String, ObjectDataDocument> rstMap = newRst.stream().collect(Collectors.toMap(k -> String.valueOf(k.get(BomConstants.FIELD_BOM_ID)), v -> v, (k1, k2) -> k1));
        bomList.stream().filter(x -> MapUtils.getIntValue(x, BomConstants.FIELD_PRICE_MODE, 0) == EnumUtil.PriceMode.PRICE_BOOK.getValue()).forEach(x -> Optional.ofNullable(rstMap.get(x.getId())).ifPresent(r -> {
            BigDecimal sellingPrice = new BigDecimal(MapUtils.getString(r, "selling_price", "0"));
            BigDecimal discount = new BigDecimal(MapUtils.getString(r, "discount", "100")).divide(new BigDecimal(100));
            if(!(flag&&!Objects.equals(arg.getPriceBookId(),r.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName())))){
                x.put(BomConstants.FIELD_ADJUST_PRICE, sellingPrice.multiply(discount).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            x.put(QuoteConstants.QuoteField.PRICEBOOKID.getApiName(), r.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName()));
            x.put("price_book_product_id", r.getId());
            x.put("price_book_product_id__r", r.get("name"));
            x.put("attribute_price_book_id", r.get("attribute_price_book_id"));
            x.put("attribute_price_book_id__r", r.get("attribute_price_book_id__r"));
            x.put("attribute_price_book_lines_ids", r.get("attribute_price_book_lines_ids"));
            x.put(QuoteConstants.QuoteField.PRICEBOOKNAME.getApiName(), r.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName().concat(SystemConstants.AliasApiName.R.value)));
        }));
    }

    private String getOptionLabel(List<Map<String, String>> optionsList, String key) {
        if (StringUtils.isBlank(key)||CollectionUtils.empty(optionsList)) {
            return null;
        }
        return optionsList.stream()
                .filter(option -> Objects.equals(MapUtils.getString(option,"value"),key))
                .map(option -> MapUtils.getString(option,"label"))
                .findFirst()
                .orElse(null);
    }

    /**
     * 给BOM数据 补充产品信息
     * @param bomList
     * @param priceMode
     */
    private void fillData(List<ObjectDataDocument> bomList, Object priceMode) {
        if (CollectionUtils.empty(bomList)) {
            return;
        }
        IObjectDescribe productObjDesc = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRODUCT_API_NAME);
        IFieldDescribe fieldDescribe = productObjDesc.getFieldDescribe(CommonProductConstants.Field.Unit.apiName);
        List<Map<String,String>> optionsList = fieldDescribe.get("options", List.class);

        Map<String, String> bomIdToProductId = bomList.stream().filter(o -> StringUtils.isNotEmpty(o.getOrDefault(BomConstants.FIELD_PRODUCT_ID, "").toString()))
                .collect(Collectors.toMap(ObjectDataDocument::getId, v -> v.getOrDefault(BomConstants.FIELD_PRODUCT_ID, "").toString(), (v1, v2) -> v1));

        bomIdToProductId.putAll(globalRootBomList.stream().filter(o -> StringUtils.isNotEmpty(o.getOrDefault(BomConstants.FIELD_PRODUCT_ID, "").toString()))
                .collect(Collectors.toMap(ObjectDataDocument::getId, v -> v.getOrDefault(BomConstants.FIELD_PRODUCT_ID, "").toString(), (v1, v2) -> v1)));

        // 产品数据
        allProductDataList.forEach(data -> Optional.ofNullable(data.get(CommonProductConstants.Field.Unit.apiName, String.class))
                .filter(StringUtils::isNotBlank)
                .map(value -> getOptionLabel(optionsList, value))
                .ifPresent(label -> data.set(CommonProductConstants.Field.Unit.apiName + "__r", label)));

        Map<String, IObjectData> referenceDataMap = allProductDataList.stream().collect(Collectors.toMap(DBRecord::getId, o -> o, (v1, v2) -> v1));

        bomList.forEach(bom -> {
            IObjectData referenceData = referenceDataMap.get(MapUtils.getString(bom, BomConstants.FIELD_PRODUCT_ID));
            if (referenceData != null) {
                bom.put(BomConstants.FIELD_PRODUCT_ID + "__ro", ObjectDataDocument.of(referenceData));
                bom.put("bom_id__r", SoCommonUtils.getValue(bom, "name", ""));
                if (Objects.equals(EnumUtil.nodeType.temp.getValue(), MapUtils.getString(bom, BomConstants.FIELD_NODE_TYPE))) {
                    bom.put("product_life_status__v", referenceData.get(SystemConstants.Field.LifeStatus.apiName));
                    bom.put("product_status__v", referenceData.get(ProductConstants.Field.STATUS.getApiName()));
                    bom.put(BomConstants.FIELD_ENABLED_STATUS, true);
                    if (!Objects.equals(String.valueOf(EnumUtil.PriceMode.PRICE_BOOK.getValue()), priceMode.toString())) {
                        bom.put(BomConstants.FIELD_ADJUST_PRICE, referenceData.get(ProductConstants.PRODUCT_PRICE));
                    }
                }
            }
        });
    }

    /**
     * 判断根节点是否匹配了所有属性和非标属性，如果匹配则所有子节点都需要默认勾选 （目前只支持一个根节点）
     * @param arg
     * @param rst
     * @return
     */
    private boolean isRootProductMatched(Arg arg, Result rst) {
        if(!quoterCommonController.isFromQuoter(arg.getExtraData()) || arg.getExtraData() == null) {
            return false;
        }
        List<ObjectDataDocument> rootProductList = null;
        IObjectData rootProduct = null;
        if(CollectionUtils.notEmpty(rst.getDataMapList())) {
            rootProductList = rst.getDataMapList().stream().filter(x -> Objects.equals(x.getDescribeApiName(), Utils.PRODUCT_API_NAME)).findFirst().orElse(ObjectData.builder().build()).getDataList();
            if(CollectionUtils.notEmpty(rootProductList)) {
                rootProduct = rootProductList.get(0).toObjectData();
            } else {
                return false;
            }
        } else {
            return false;
        }
        Map<String, List<String>> quoterAttrMap = quoterCommonController.getQueryInfo(arg.getExtraData(), QuoterCommonController.KEY_STANDARD_ATTRIBUTE_QUERY_INFO);
        Map<String, List<String>> quoterNonAttrMap = quoterCommonController.getQueryInfo(arg.getExtraData(), QuoterCommonController.KEY_NONSTANDARD_ATTRIBUTE_QUERY_INFO);
        List<String> quoterAttrIds = Lists.newArrayList(quoterAttrMap.keySet());
        List<String> quoterNonAttrIds = Lists.newArrayList(quoterNonAttrMap.keySet());

        boolean needSelected = false;

        List<Attribute> list = rootProduct.get(AttributeConstants.ATTRIBUTE, List.class, Lists.newArrayList());
        List<String> attrIds = Optional.ofNullable(list).orElse(Lists.newArrayList()).stream().map(x->"attribute"+x.getFieldNum()).collect(Collectors.toList());
        needSelected = attrIds.size() > 0 && attrIds.containsAll(quoterAttrIds);
        //如果属性不匹配，则不往下继续判断，不用选中处理
        if(needSelected) {
            List<Map> nonList = rootProduct.get(AttributeConstants.NON_ATTRIBUTE, List.class, Lists.newArrayList());
            if (CollectionUtils.notEmpty(nonList)) {
                List<String> nonAttrIds = nonList.stream().map(x -> x.get("id").toString()).collect(Collectors.toList());
                needSelected = nonAttrIds.size() > 0 && nonAttrIds.containsAll(quoterNonAttrIds);
            }
        }
        return needSelected;
    }

    /**
     * 补充 rootBom 根节点的产品数据
     *
     * @param rst
     */
    private void fillRootProduct(Arg arg, Result rst) {
        List<String> rootProductIds = globalRootBomList.stream()
                .map(data -> data.get(BomConstants.FIELD_PRODUCT_ID).toString()).collect(Collectors.toList());

        List<IObjectData> rootProductData = allProductDataList.stream().filter(x -> rootProductIds.contains(x.getId())).distinct().collect(Collectors.toList());
        Map<Object, ObjectDataDocument> rootBomDataMap = globalRootBomList.stream().collect(Collectors.toMap(o -> o.get(BomConstants.FIELD_PRODUCT_ID), o -> o, (v1, v2) -> v1));
        boolean isRootProductMatched = isRootProductMatched(arg, rst);
        String bomCoreAttributeConstraintId = null;
        if(enableBomCore && StringUtils.isNotBlank(bomCoreId)) {
            IObjectData bomCoreData = serviceFacade.findObjectData(controllerContext.getUser(), bomCoreId, Utils.BOM_CORE_API_NAME);
            if (bomCoreData != null) {
                bomCoreAttributeConstraintId = bomCoreData.get(BomCoreConstants.FIELD_ATTRIBUTE_CONSTRAINT_ID, String.class);
            }
        }
        attributeRangeService.queryAttrRange(controllerContext.getUser(),ObjectDataDocument.ofDataList(globalRootBomList),Utils.BOM_CORE_API_NAME);

        for (IObjectData data : rootProductData) {
            ObjectDataDocument bom = rootBomDataMap.get(data.getId());
            if (Objects.isNull(bom)) {
                continue;
            }
            data.set("bom_id", bom.getId());
            if(StringUtils.isEmpty(arg.getNewBomPath())) {
                data.set("new_bom_path", bom.get(BomConstants.FIELD_BOM_PATH));
            } else {
                data.set("new_bom_path", arg.getNewBomPath());
            }
            List<IObjectData> objectDataList = nonstandardAttributeMap.get(data.getId());
            //回填非标属性值
            setNonAttrValue(arg, objectDataList);
            setAttr(data, objectDataList);
            List<Attribute> attributes = attributeMap.get(data.getId());
            //报价器过来的需要重新设置属性默认值
            List<Attribute> oldAttributes =resetDefaultAttr(attributes, arg);
            if (CollectionUtils.notEmpty(attributes)) {
                data.set(AttributeConstants.ATTRIBUTE, attributes);
                if(CollectionUtils.notEmpty(oldAttributes)) {
                    data.set(AttributeConstants.OLD_ATTRIBUTE, oldAttributes);
                }
            }
            //启用产品组合并且有bomCoreId值，往产品数据赋core_id字段
            if(enableBomCore && StringUtils.isNotBlank(bomCoreId)) {
                data.set("core_id", bomCoreId);
                //将产品组合关联的属性级联约束方案 赋值到产品对象上
                data.set(BomCoreConstants.FIELD_ATTRIBUTE_CONSTRAINT_ID, bomCoreAttributeConstraintId);
            }
            Object rangeIds = bom.get("rangeIds");
            Object rangeObj = bom.get(AttributeRangeMode.ATTR_RANGE);
            List<Attribute> attributeList = attributeMap.get(data.getId());
            List<IObjectData> nonAttributeList = nonstandardAttributeMap.get(data.getId());
            try {
                attributeRangeService.handleBomRootNodeAttrRange(data, (List<String>) rangeIds, rangeObj, attributeList, nonAttributeList);
                Map<String, List<AttributeValue>> cacheAttrVal = Maps.newHashMap();
                attributeMap.forEach((k,v)-> v.forEach(attr->{
                    cacheAttrVal.put(attr.getId(),attr.getAttributeValues());
                }));
                List<AttributeRangeMode.AttributeRange> rangeList = data.get(AttributeRangeMode.ATTR_RANGE, List.class);
                if (CollectionUtils.notEmpty(rangeList)) {
                    rangeList.forEach(range->{
                        if (Objects.equals(Utils.ATTRIBUTE_OBJ_API_NAME,range.getApiName()) && cacheAttrVal.containsKey(range.getId())) {
                            range.setAttributeValues(cacheAttrVal.get(range.getId()));
                        }
                    });
                    data.set(AttributeRangeMode.ATTR_RANGE,rangeList);
                }
            } catch (Exception e) {
                log.warn(e.getMessage(),e);
            }
            if(isRootProductMatched) {
                data.set(BomConstants.VIRTUAL_FIELD_SELECTED_BY_QUOTER, true);
            }
        }
        ObjectData rootProduct = ObjectData.builder().dataList(ObjectDataDocument.ofList(rootProductData)).describeApiName(Utils.PRODUCT_API_NAME).build();
        rst.getDataMapList().add((rootProduct));
    }

    /**
     * 从报价器过来的需要更改默认的属性值（更改为报价器选中的属性值，如果属性值多选，则保持原来的默认值，单选则做变更）
     * @param attributeList
     * @param arg
     */
    private List<Attribute> resetDefaultAttr(List<Attribute> attributeList, Arg arg) {
        if (!quoterCommonController.isFromQuoter(arg.getExtraData())) {
            return null;
        }
        Map<String, List<String>> quoterAttrMap = Maps.newHashMap();
        if(canUseQuoter) {
            quoterAttrMap.putAll(quoterCommonController.getQueryInfo(arg.getExtraData(), QuoterCommonController.KEY_STANDARD_ATTRIBUTE_QUERY_INFO));
        }
        boolean changed = false;
        List<Attribute> oldAttrList = Lists.newArrayList();
        if(CollectionUtils.notEmpty(attributeList) && CollectionUtils.notEmpty(quoterAttrMap)) {
            for(Attribute attr: attributeList) {
                oldAttrList.add(GsonUtil.GSON.fromJson(GsonUtil.GSON.toJson(attr), Attribute.class));
                List<String> selectedAttrValue = quoterAttrMap.get("attribute"+attr.getFieldNum());
                //该属性报价器选中的属性值只有一个，并且数据库查询出来的该产品关联的属性的属性值存在，则进行替换默认选择处理
                if (CollectionUtils.size(selectedAttrValue) == 1 && CollectionUtils.notEmpty(attr.getAttributeValues())) {
                    Optional<AttributeValue> oldDefaultAttr = attr.getAttributeValues().stream().filter(a->"1".equals(a.getIs_default())).findFirst();
                    //老的默认值与新的默认值不一致，则说明更改过默认值，只要有一个属性变更过默认值，就设置changed为true
                    if(!changed && oldDefaultAttr.isPresent() && oldDefaultAttr.get().getId().equals(selectedAttrValue.get(0))) {
                        changed = true;
                    }
                    attr.getAttributeValues().stream().filter(v->selectedAttrValue.get(0).equals(v.getId())).forEach(v->v.setIs_default("1"));
                    attr.getAttributeValues().stream().filter(v->!selectedAttrValue.get(0).equals(v.getId())).forEach(v->v.setIs_default("0"));
                }
            }
        }
        //有变更过则返回旧属性值，否则返回null
        return changed ? oldAttrList : null;
    }

    /**
     * 非标属性值回填处理
     * @param arg
     * @param objectDataList
     * @return
     */
    private void setNonAttrValue(Arg arg, List<IObjectData> objectDataList) {
        if (!quoterCommonController.isFromQuoter(arg.getExtraData())) {
            return;
        }
        Map<String, List<String>> quoterNonAttrMap = Maps.newHashMap();
        if(canUseQuoter) {
            quoterNonAttrMap.putAll(quoterCommonController.getQueryInfo(arg.getExtraData(), QuoterCommonController.KEY_NONSTANDARD_ATTRIBUTE_QUERY_INFO));
        }
        if(CollectionUtils.notEmpty(objectDataList) && CollectionUtils.notEmpty(quoterNonAttrMap)) {
            objectDataList.stream().forEach(attr->attr.set("value", quoterNonAttrMap.get(attr.getId())));
        }
    }

    private void setAttr(IObjectData data, List<IObjectData> objectDataList) {
        if (CollectionUtils.notEmpty(objectDataList)) {
            List<Map<String, Object>> dataMapList = Lists.newArrayList();
            objectDataList.forEach(attr -> {
                Map<String, Object> dataMap = Maps.newHashMap();
                dataMap.put("id", attr.getId());
                dataMap.put("name", AttributeUtils.getI18nName(attr));
                dataMap.put("type",attr.get("type"));
                //非标属性自身设置的默认值,可能是数字或文本
                Object defaultValue = attr.get("default_value");
                //优先取产品设置的非标属性值(母件与子件对象不一样，所以取产品ID的字段值有所区别)
                String fieldApiName = Utils.PRODUCT_API_NAME.equals(data.getDescribeApiName())? ID : BomConstants.FIELD_PRODUCT_ID;
                dataMap.put("default_value", productNonstandardAttributeMap.getOrDefault(data.get(fieldApiName, String.class), Maps.newHashMap()).getOrDefault(attr.getId(), defaultValue));
                dataMap.put(AttributeConstants.GroupField.GROUP_ID,attr.get(AttributeConstants.GroupField.GROUP_ID));
                dataMap.put(AttributeConstants.GroupField.GROUP_NAME,attr.get(AttributeConstants.GroupField.GROUP_NAME));
                dataMap.put(AttributeConstants.GroupField.GROUP_NO,attr.get(AttributeConstants.GroupField.GROUP_NO));
                if(Objects.nonNull(attr.get("value"))) {
                    dataMap.put("value", attr.get("value", List.class));
                }
                dataMapList.add(dataMap);
            });
            data.set(AttributeConstants.NON_ATTRIBUTE, dataMapList);
        }
    }

    /**
     * 补充bom 分组信息
     *
     * @param bomList
     * @param rst
     */
    private void fillProductGroup(List<ObjectDataDocument> bomList, Result rst) {
        List<String> rootIds = globalRootBomList.stream().map(ObjectDataDocument::getId).collect(Collectors.toList());
        if (CollectionUtils.empty(rootIds)) {
            return ;
        }
        Set<String> ids = Sets.newHashSet();
        Set<String> allParentIds = Sets.newHashSet();
        Map<String,  String> bomId2newBomPath = Maps.newHashMap();
        for (ObjectDataDocument dataDocument : bomList) {
            if (dataDocument != null) {
                ids.add(dataDocument.getId());
                String path = MapUtils.getString(dataDocument, BomConstants.FIELD_BOM_PATH);
                if (StringUtils.isNotBlank(path)) {
                    List<String> parentIds = Splitter.on(".").splitToList(StringUtils.substringBeforeLast(path, "."));
                    allParentIds.addAll(parentIds);
                }
                if(enableBomCore) {
                    bomId2newBomPath.put(dataDocument.getId(), MapUtils.getString(dataDocument, BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH));
                }
            }
        }
        ids.addAll(rootIds);
        allParentIds.removeAll(ids);
        ids.addAll(allParentIds);
        ids.addAll(parentBomIds);
        Map<String, String> productMap = Maps.newHashMap();
        List<IObjectData> tmpParentBomList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(allParentIds)) {
            List<IObjectData> parentBomList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(allParentIds), Utils.BOM_API_NAME);
            if (CollectionUtils.notEmpty(parentBomList)) {
                tmpParentBomList.addAll(parentBomList);
                List<String> productIds = parentBomList.stream().map(x -> x.get(BomConstants.FIELD_PRODUCT_ID, String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                if (CollectionUtils.notEmpty(productIds)) {
                    List<IObjectData> parentProductList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), productIds, Utils.PRODUCT_API_NAME);
                    if(CollectionUtils.notEmpty(parentProductList)){
                        Map<String, String> tmpMap = parentProductList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (key1, key2) -> key2));
                        productMap.putAll(tmpMap);
                    }
                }
            }
        }
        Map<String, String> referenceDataMap = allProductDataList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (key1, key2) -> key2));
        productMap.putAll(referenceDataMap);
        // 查询分组信息
        Map<String, String> groupMap = Maps.newHashMap();
        SearchTemplateQuery searchQuery = bomCoreService.getNewTemplateQuery();
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, BomConstants.FIELD_PARENT_BOM_ID, Lists.newArrayList(ids));
        if (GrayUtil.isBOMSearchDB(controllerContext.getTenantId())) {
            searchQuery.setSearchSource("db");
        }
        QueryResult<IObjectData> groupResult = serviceFacade.findBySearchQueryIgnoreAll(controllerContext.getUser(), Utils.PRODUCT_GROUP_API_NAME, searchQuery);
        List<IObjectData> groupList = groupResult.getData();
        if (CollectionUtils.notEmpty(groupList)) {
            if(filterGroupStatusFlag){
                groupList = groupList.stream().filter(x->BooleanUtils.isNotFalse(x.get(BomConstants.FIELD_ENABLED_STATUS,Boolean.class))).collect(Collectors.toList());
            }
            List<ObjectDataDocument> parentBomDataList = Lists.newArrayList();
            parentBomDataList.addAll(bomList);
            parentBomDataList.addAll(globalRootBomList);
            Map<String, String> bomIdToProductId = Maps.newHashMap();
            Map<String, String> bomIdToBomName = Maps.newHashMap();
            Map<String, List<String>> bomIdToGroupMap = Maps.newHashMap();
            parentBomDataList.forEach(data -> {
                String productId = data.get(BomConstants.FIELD_PRODUCT_ID).toString();
                bomIdToProductId.put(data.getId(), productId);
                bomIdToBomName.put(data.getId(), MapUtils.getString(data, "name", ""));
            });
            for (IObjectData group : groupList) {
                bomIdToGroupMap.putIfAbsent(group.get(BomConstants.FIELD_PARENT_BOM_ID, String.class), Lists.newArrayList());
                bomIdToGroupMap.get(group.get(BomConstants.FIELD_PARENT_BOM_ID, String.class)).add(group.getId());
                String productName = referenceDataMap.get(bomIdToProductId.get(group.get(BomConstants.FIELD_PARENT_BOM_ID, String.class)));
                group.set(BomConstants.FIELD_PARENT_PRODUCT_ID + "__r", productName);
                group.set(BomConstants.FIELD_PARENT_BOM_ID + "__r", bomIdToBomName.get(group.get(BomConstants.FIELD_PARENT_BOM_ID, String.class)));
                if (enableBomCore) {
                    //给分组设置new_bom_path，取值逻辑，根据分组的parent_bom_id 取出对应的new_bom_path ，再拼上分组的 id
                    group.set(BomConstants.VIRTUAL_FIELD_NEW_BOM_PATH, bomId2newBomPath.getOrDefault(group.get(BomConstants.FIELD_PARENT_BOM_ID, String.class), newBomPath) + "." + group.getId());
                }
                groupMap.put(group.getId(), group.getName());
            }
            groupList = filterEmptyGroup(bomList, rootIds, groupList, bomIdToGroupMap);
            ObjectData groupData = ObjectData.builder().dataList(ObjectDataDocument.ofList(groupList)).describeApiName(Utils.PRODUCT_GROUP_API_NAME).build();
            rst.getDataMapList().add((groupData));
        }
        if (CollectionUtils.empty(bomList)) {
            return ;
        }
        //处理面包蟹
        handleCrumb(bomList, productMap, tmpParentBomList, groupMap);
    }

    private List<IObjectData> filterEmptyGroup(List<ObjectDataDocument> bomList, List<String> rootIds, List<IObjectData> groupList, Map<String, List<String>> bomIdToGroupMap) {
        if(BooleanUtils.isNotTrue(arg.getFilterEmptyGroups())){
            return groupList;
        }
        if(arg.isPageFlag()){
            if (rootIds.size()==1) {
                Set<String> groupIds = groupList.stream().map(DBRecord::getId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                if(CollectionUtils.empty(groupIds)){
                    return groupList;
                }
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setOffset(0);
                query.setLimit(2000);
                query.setNeedReturnCountNum(false);
                query.setPermissionType(0);
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, BomConstants.FIELD_ROOT_ID, rootIds.get(0));
                SearchUtil.fillFilterIn(filters, BomConstants.FIELD_PRODUCT_GROUP_ID, groupIds);
                SearchUtil.fillFilterEq(filters, ObjectLifeStatus.LIFE_STATUS_API_NAME, "normal");
                SearchUtil.fillFilterEq(filters, ObjectDataExt.IS_DELETED, "0");
                SearchUtil.fillFilterEq(filters, BomConstants.FIELD_ENABLED_STATUS, true);
                query.setFilters(filters);
                QueryResult<IObjectData> nodes = serviceFacade.findBySearchQuery(controllerContext.getUser(), Utils.BOM_API_NAME, query);
                if(Objects.nonNull(nodes)&&CollectionUtils.notEmpty(nodes.getData())){
                    Set<String> dbGroupIds = nodes.getData().stream()
                            .filter(x->StringUtils.isNotBlank(x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))&&x.get(BomConstants.FIELD_PRODUCT_STATUS, Integer.class,1)==1)
                            .map(x -> x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class))
                            .collect(Collectors.toSet());
                    groupList = groupList.stream().filter(g -> dbGroupIds.contains(g.getId())).collect(Collectors.toList());
                }else{
                    groupList = Lists.newArrayList();
                }
                Map<String/*parent_bom_id*/, List<IObjectData>> dataMap = groupList.stream().filter(x-> StringUtils.isNotBlank(x.get(BomConstants.FIELD_PARENT_BOM_ID,String.class)))
                        .collect(Collectors.groupingBy(x->x.get(BomConstants.FIELD_PARENT_BOM_ID,String.class)));
                bomList.forEach(g -> {
                    if(MapUtils.getBooleanValue(g, BomConstants.FIELD_ONLY_HAS_GROUP_CHILD)
                            &&CollectionUtils.empty(dataMap.get(g.getId()))){
                        g.put(BomConstants.FIELD_HAS_CHILD, false);
                    }
                });
            }
        }else {
            Set<String> groupIds = bomList.stream()
                    .filter(d->StringUtils.isNotBlank(MapUtils.getString(d, BomConstants.FIELD_PRODUCT_GROUP_ID))
                            &&MapUtils.getBooleanValue(d,BomConstants.FIELD_ENABLED_STATUS)
                            &&MapUtils.getIntValue(d,BomConstants.FIELD_PRODUCT_STATUS__V,1)==1)
                    .map(d -> MapUtils.getString(d, BomConstants.FIELD_PRODUCT_GROUP_ID))
                    .collect(Collectors.toSet());
            groupList = groupList.stream().filter(g -> groupIds.contains(g.getId())).collect(Collectors.toList());
        }
        return groupList;
    }

    private void handleCrumb(List<ObjectDataDocument> bomList, Map<String, String> productMap, List<IObjectData> tmpParentBomList, Map<String, String> groupMap) {
        List<IObjectData> dataList = ObjectDataDocument.ofDataList(bomList);
        dataList.addAll(tmpParentBomList);
        Map<String/*root_id*/, Map<String/*parent_bom_id*/, List<IObjectData>>> dataMap = dataList.stream().filter(x->
                StringUtils.isNotBlank(x.get(BomConstants.FIELD_PARENT_BOM_ID,String.class))&&StringUtils.isNotBlank(x.get(BomConstants.FIELD_ROOT_ID,String.class)))
                .collect(Collectors.groupingBy(x->x.get(BomConstants.FIELD_ROOT_ID,String.class),Collectors.groupingBy(x -> x.get(BomConstants.FIELD_PARENT_BOM_ID, String.class))));
        for (ObjectDataDocument rootBom : globalRootBomList) {
            List<String> path;
            List<String> pathName;
            List<String> productNamePath;
            List<String> productIdPath;
            Map<String, List<IObjectData>> childMap = dataMap.getOrDefault(rootBom.getId(), Maps.newHashMap());
            List<IObjectData> childDataList = childMap.get(rootBom.getId());
            if (CollectionUtils.notEmpty(childDataList)) {
                for (IObjectData x : childDataList) {
                    path = Lists.newArrayList();
                    pathName = Lists.newArrayList();
                    productIdPath = Lists.newArrayList();
                    productNamePath = Lists.newArrayList();
                    String groupId = x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class);
                    path.add(rootBom.getId());
                    productIdPath.add(MapUtils.getString(rootBom,BomConstants.FIELD_PRODUCT_ID));
                    pathName.add(productMap.getOrDefault(MapUtils.getString(rootBom,BomConstants.FIELD_PRODUCT_ID), ""));
                    productNamePath.add(productMap.getOrDefault(MapUtils.getString(rootBom,BomConstants.FIELD_PRODUCT_ID), ""));
                    if (StringUtils.isNotBlank(groupId)) {
                        path.add(groupId);
                        pathName.add(groupMap.getOrDefault(groupId, ""));
                    }
                    path.add(x.getId());
                    productIdPath.add(x.get(BomConstants.FIELD_PRODUCT_ID,String.class));
                    pathName.add(productMap.getOrDefault(x.get(BomConstants.FIELD_PRODUCT_ID), ""));
                    productNamePath.add(productMap.getOrDefault(x.get(BomConstants.FIELD_PRODUCT_ID), ""));
                    x.set(BomConstants.FIELD_BOM_CRUMB_BREAD, path);
                    x.set(BomConstants.FIELD_BOM_CRUMB_BREAD_NAME, pathName);
                    x.set(BomConstants.FIELD_PRODUCT_NAME_PATH, productNamePath);
                    x.set(BomConstants.FIELD_PRODUCT_ID_PATH, productIdPath);
                    handleChildCrumb(x, childMap,productMap,groupMap);
                }
            }
        }
    }

    private void handleChildCrumb(IObjectData parentData, Map<String, List<IObjectData>> dataMap, Map<String, String> productMap, Map<String, String> groupMap){
        List<IObjectData> childDataList = dataMap.get(parentData.getId());
        if (CollectionUtils.empty(childDataList)) {
            return;
        }
        for (IObjectData x : childDataList) {
            List<String> path = Lists.newArrayList();
            List<String> pathName = Lists.newArrayList();
            List<String> productIdPath = Lists.newArrayList();
            List<String> productNamePath = Lists.newArrayList();
            List<String> parentPath = parentData.get(BomConstants.FIELD_BOM_CRUMB_BREAD, List.class, Lists.newArrayList());
            path.addAll(parentPath);
            List<String> parentPathName = parentData.get(BomConstants.FIELD_BOM_CRUMB_BREAD_NAME, List.class, Lists.newArrayList());
            pathName.addAll(parentPathName);
            List<String> parentProductIdPath = parentData.get(BomConstants.FIELD_PRODUCT_ID_PATH, List.class, Lists.newArrayList());
            productIdPath.addAll(parentProductIdPath);
            List<String> parentProductNamePath = parentData.get(BomConstants.FIELD_PRODUCT_NAME_PATH, List.class, Lists.newArrayList());
            productNamePath.addAll(parentProductNamePath);
            String groupId = x.get(BomConstants.FIELD_PRODUCT_GROUP_ID, String.class, "");
            if (StringUtils.isNotBlank(groupId)) {
                path.add(groupId);
                pathName.add(groupMap.getOrDefault(groupId, ""));
            }
            path.add(x.getId());
            productIdPath.add(x.get(BomConstants.FIELD_PRODUCT_ID,String.class));
            pathName.add(productMap.getOrDefault(x.get(BomConstants.FIELD_PRODUCT_ID), ""));
            productNamePath.add(productMap.getOrDefault(x.get(BomConstants.FIELD_PRODUCT_ID), ""));
            x.set(BomConstants.FIELD_BOM_CRUMB_BREAD, path);
            x.set(BomConstants.FIELD_BOM_CRUMB_BREAD_NAME, pathName);
            x.set(BomConstants.FIELD_PRODUCT_NAME_PATH, productNamePath);
            x.set(BomConstants.FIELD_PRODUCT_ID_PATH, productIdPath);
            handleChildCrumb(x, dataMap, productMap, groupMap);
        }
    }

    /**
     * 除了 Bis001（佳能）外的企业走该逻辑
     *
     * @param priceBookId
     * @param after
     */
    private void fillPriceBookProduct(String priceBookId, Result after, String mcCurrency) {
        if (StringUtils.isBlank(priceBookId)) {
            return;
        }
        List<String> productIds = globalRootBomList.stream().map(data -> data.get(BomConstants.FIELD_PRODUCT_ID).toString()).collect(Collectors.toList());
        if (CollectionUtils.empty(productIds)) {
            return;
        }
        List<IObjectData> priceBookProductList = getPriceBookProduct(priceBookId, productIds);
        if (CollectionUtils.empty(priceBookProductList)) {
            return;
        }
        handCurrency(mcCurrency, priceBookProductList);
        fillRootBomPriceBookProduct(after, priceBookProductList);
    }


    /**
     * bis001（佳能） 走该逻辑
     *
     * @param bomList
     * @param priceBookId
     * @param after
     */
    private void fillPriceBookProduct(List<ObjectDataDocument> bomList, String priceBookId, Result after, String mcCurrency) {
        if (StringUtils.isBlank(priceBookId)) {
            return;
        }

        List<ObjectDataDocument> allBomDataList = Lists.newArrayList();
        allBomDataList.addAll(bomList);
        allBomDataList.addAll(globalRootBomList);
        List<String> productIds = allBomDataList.stream().map(data -> data.get(BomConstants.FIELD_PRODUCT_ID).toString()).collect(Collectors.toList());

        List<IObjectData> priceBookProductList = getPriceBookProduct(priceBookId, productIds);
        if (CollectionUtils.empty(priceBookProductList)) {
            return;
        }
        handCurrency(mcCurrency, priceBookProductList);
        fillRootBomPriceBookProduct(after, priceBookProductList);
        fillSubBomBisOO1PriceBookProduct(bomList, priceBookProductList);
    }

    private void handCurrency(String mcCurrency, List<IObjectData> priceBookProductList) {
        if(StringUtils.isEmpty(mcCurrency)){
            return;
        }
        boolean isCurrencyEnabled = bizConfigThreadLocalCacheService.isCurrencyEnabled(controllerContext.getTenantId());
        if (!isCurrencyEnabled) {
            return;
        }
        Map<String, String> exchangeRateMap = MtCurrentUtil.getExchangeRateMap(controllerContext.getUser(), arg.getMcCurrency());
        if (CollectionUtils.empty(exchangeRateMap)) {
            return;
        }
        priceBookProductList.stream().forEach(x -> {
            String priceBookSellingPrice = x.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(), String.class);
            String dataMcCurrency = x.get("mc_currency", String.class);
            if (StringUtils.isNotBlank(priceBookSellingPrice) && StringUtils.isNotBlank(dataMcCurrency)) {
                x.set(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(), MtCurrentUtil.changePriceToCurrency(mcCurrency, new BigDecimal(priceBookSellingPrice), dataMcCurrency, exchangeRateMap));
            }
        });
    }


    private void fillSubBomBisOO1PriceBookProduct(List<ObjectDataDocument> bomList, List<IObjectData> priceBookProductList) {
        Map<String, ObjectDataDocument> priceBookProductMap = priceBookProductList.stream()
                .collect(Collectors.toMap(o -> o.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class),
                        ObjectDataDocument::of));
        bomList.forEach(oo -> oo.put("price_book_product__ro", priceBookProductMap.get(oo.get("product_id"))));
    }

    private void fillRootBomPriceBookProduct(Result after, List<IObjectData> priceBookProductList) {
        //只给根节点添加价目表明细数据
        List<IObjectData> priceBookDataList = priceBookProductList.stream()
                .filter(x -> rootProductIdList.contains(x.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class))).collect(Collectors.toList());

        ObjectData rootProd = ObjectData.builder().dataList(ObjectDataDocument.ofList(priceBookDataList)).describeApiName(Utils.PRICE_BOOK_PRODUCT_API_NAME).build();
        after.getDataMapList().add((rootProd));
    }


    private List<IObjectData> getPriceBookProduct(String priceBookId, List<String> productIds) {
        List<IObjectData> priceBookProductList = bomCoreService.getPriceBookProductList(controllerContext.getUser(), priceBookId, productIds);
        List<INameCache> recordName = serviceFacade.findRecordName(ActionContextExt.of(controllerContext.getUser()).getContext(),
                Utils.PRICE_BOOK_API_NAME, Lists.newArrayList(priceBookId));
        if (CollectionUtils.notEmpty(recordName)) {
            priceBookProductList.forEach(data -> data.set("pricebook_id__r", recordName.get(0).getName()));
        }
        return priceBookProductList;
    }

    /**
     * 根据RootProductId 查询 根 BomId 数据
     *
     * @return
     */
    private Set<String> getBomIds() {
        if (CollectionUtils.empty(globalRootBomList)) {
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(2000);
            QueryResult<IObjectData>  rootBomList = null;
            if(enableBomCore) {
                //根据BomCoreId查询对应Bom数据（BomCore 存储时会将主对象也存储到Bom表里，确保根原来的逻辑一致）
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, "life_status", "normal");
                SearchUtil.fillFilterEq(filters, "is_deleted", "0");
                SearchUtil.fillFilterEq(filters, BomConstants.FIELD_CORE_ID, bomCoreId);
                SearchUtil.fillFilterIsNotNull(filters, BomConstants.FIELD_ROOT_ID);
                SearchUtil.fillFilterIsNull(filters, BomConstants.FIELD_PARENT_BOM_ID);
                query.setFilters(filters);
                rootBomList = serviceFacade.findBySearchQueryIgnoreAll(controllerContext.getUser(), Utils.BOM_API_NAME, query);
            } else {
                rootBomList = bomCoreService.findBomByRootProductId(controllerContext.getUser(), query, rootProductIdList);
            }

            if (CollectionUtils.notEmpty(rootBomList.getData())) {
                globalRootBomList.addAll(ObjectDataDocument.ofList(rootBomList.getData()));
            }
        }

        if (CollectionUtils.empty(globalRootBomList)) {
            hasData = Boolean.FALSE;
            return Sets.newHashSet();
        }

        HashSet<String> rootBomIdList = Sets.newHashSet();

        for (ObjectDataDocument data : globalRootBomList) {
            rootBomIdList.add(data.getId());
            bomIdToPriceId.put(data.getId(), rootProductIdToPriceId.get(MapUtils.getString(data,BomConstants.FIELD_PRODUCT_ID)));
        }
        return rootBomIdList;
    }

    /**
     * 补充约束条件数据
     *
     * @param after
     */
    private void fillProductConstraint(Result after) {
        if (!needProductConstraint||SFAConfigUtil.isSimpleBom(controllerContext.getTenantId())) {
            return;
        }
        List<String> rootBomIds = globalRootBomList.stream().map(ObjectDataDocument::getId).collect(Collectors.toList());
        if (CollectionUtils.empty(rootBomIds)) {
            return;
        }
        IObjectDescribe bomAttributeConstraintDescribe = null;
        try {
            bomAttributeConstraintDescribe = serviceFacade.findObject(controllerContext.getTenantId(), SFAPreDefineObject.BomAttributeConstraint.getApiName());
        } catch (Exception e) {
            log.warn("find desc:{}", e.getMessage());
        }
        if (Objects.nonNull(bomAttributeConstraintDescribe)) {
            if(bomCoreId != null) {
                List<IObjectData> bomAplConstrainList = bomConstraintService.findBomConstrainByCoreId(controllerContext.getUser(), Lists.newArrayList(bomCoreId), false);
                if (CollectionUtils.notEmpty(bomAplConstrainList)) {
                    List<ObjectDataDocument> aplFunctionList = Lists.newArrayList();
                    List<ObjectDataDocument> assignAplFunctionList = Lists.newArrayList();
                    bomAplConstrainList.stream().forEach(x -> {
                        String aplInfo = x.get(BomConstraintConstants.APL_API_NAME, String.class);
                        if (StringUtils.isNotBlank(aplInfo)) {
                            Map<String, String> m = JSON.parseObject(aplInfo, Map.class);
                            ObjectDataDocument document = new ObjectDataDocument();
                            document.put("api_name", m.get(BomConstraintConstants.APL_API_NAME_JSON));
                            aplFunctionList.add(document);
                        }
                        String assignAplInfo = x.get(BomConstraintConstants.ASSIGN_VALUE_APL, String.class);
                        if (StringUtils.isNotBlank(assignAplInfo)) {
                            Map<String, String> m = JSON.parseObject(assignAplInfo, Map.class);
                            ObjectDataDocument document = new ObjectDataDocument();
                            document.put("api_name", m.get(BomConstraintConstants.APL_API_NAME_JSON));
                            assignAplFunctionList.add(document);
                        }
                    });
                    if(CollectionUtils.notEmpty(aplFunctionList)) {
                        ObjectData objectData = ObjectData.builder().dataList(aplFunctionList).describeApiName(BomConstraintConstants.APL_FUNCTION_LIST).build();
                        after.getDataMapList().add(objectData);
                    }
                    if(CollectionUtils.notEmpty(assignAplFunctionList)) {
                        ObjectData objectData = ObjectData.builder().dataList(assignAplFunctionList).describeApiName(BomConstraintConstants.ASSIGN_APL_FUNCTION_LIST).build();
                        after.getDataMapList().add(objectData);
                    }
                }
            }
            List<IObjectData> bomConstrainList = bomConstraintService.findBomConstrainByRootBomId(controllerContext.getUser(), rootBomIds, false);

            bomConstrainList = bomConstrainList.stream().filter(x -> !Objects.equals(x.get(BomConstraintConstants.CHECK_MODE, String.class), BomConstraintConstants.CheckMode.APL.getMode())).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(bomConstrainList)) {
                List<String> bomConstraintIds = bomConstrainList.stream().map(DBRecord::getId).collect(Collectors.toList());
                List<IObjectData> bomConstraintLinesList = bomConstraintService.findBomConstraintLinesByMasterId(controllerContext.getUser(), bomConstraintIds);
                if (CollectionUtils.notEmpty(bomConstraintLinesList)) {
                    //如果是查询复用BOM，则需要将复用定义的产品属性约束规则中，使用到母件作为约束条件、计算因子，需要将母件替换成前端传过来的newBomId
                    replaceBomId(oldBomId, arg.getNewBomId(), bomConstraintLinesList);

                    ProductConstraintUtil.handleData(bomConstraintLinesList, controllerContext.getUser());
                    ObjectData bomConstraintLinesResult = ObjectData.builder().dataList(ObjectDataDocument.ofList(bomConstraintLinesList))
                            .describeApiName("BomAttributeConstraintLinesObj").build();
                    after.getDataMapList().add(bomConstraintLinesResult);
                    try {
                        //修改子件的数量、属性、非标属性需要触发执行高级公式信息封装
                        List<ObjectDataDocument> formulaList = bomConstraintService.buidlTriggerFormulaMap(bomConstraintLinesList);
                        if(CollectionUtils.notEmpty(formulaList)) {
                            ObjectData expressionListResult = ObjectData.builder().dataList(formulaList)
                                    .describeApiName("TriggerFormulaMap").build();
                            after.getDataMapList().add(expressionListResult);
                        }
                    } catch (Exception e) {
                        log.error("TriggerFormulaMap handle error", e);
                    }
                    return;
                }
            }
        }
        List<IObjectData> constrainList = productConstraintService.findProductConstrainByRootBomId(controllerContext.getUser(), rootBomIds);
        List<String> constraintIds = constrainList.stream().map(DBRecord::getId).collect(Collectors.toList());
        if (CollectionUtils.empty(constraintIds)) {
            return;
        }
        List<IObjectData> constraintLinesList = constraintLinesService.findProductConstraintLinesByMasterId(controllerContext.getUser(), constraintIds);
        if (CollectionUtils.empty(constraintLinesList)) {
            return;
        }
        List<String> bomIds = Lists.newArrayList();
        constraintLinesList.forEach(o -> {
            String upBomId = o.get("up_bom_id", String.class);
            String downBomId = o.get("down_bom_id", String.class);

            bomIds.add(upBomId);
            bomIds.add(downBomId);
        });
        List<IObjectData> bomDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), bomIds, Utils.BOM_API_NAME);
        if (CollectionUtils.empty(bomDataList)) {
            return;
        }
        Map<String, IObjectData> bomIdToDataMap = bomDataList.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (v1, v2) -> v1));

        constraintLinesList.forEach(o -> {
            IObjectData upBomData = bomIdToDataMap.get(o.get("up_bom_id", String.class));
            if (upBomData != null) {
                o.set("up_bom_path", upBomData.get("bom_path", String.class));

            }
            IObjectData downBomData = bomIdToDataMap.get(o.get("down_bom_id", String.class));
            if (downBomData != null) {
                o.set("down_bom_path", downBomData.get("bom_path", String.class));
            }
        });

        ObjectData constrainResult = ObjectData.builder().dataList(ObjectDataDocument.ofList(constrainList))
                .describeApiName(Utils.PRODUCT_CONSTRAINT_API_NAME).build();
        ObjectData constraintLinesResult = ObjectData.builder().dataList(ObjectDataDocument.ofList(constraintLinesList))
                .describeApiName(Utils.PRODUCT_CONSTRAINT_LINES_API_NAME).build();

        after.getDataMapList().add(constrainResult);
        after.getDataMapList().add(constraintLinesResult);

    }

    /**
     * 对约束条件、被约束条件、高级公式中用到的母件id进行替换
     * @param newBomId
     * @param bomConstraintLinesList
     */
    private void replaceBomId(String oldBomId, String newBomId, List<IObjectData> bomConstraintLinesList) {
        if (StringUtils.isAnyBlank(newBomId, oldBomId) || CollectionUtils.empty(bomConstraintLinesList)) {
            return;
        }
        for (IObjectData bomConstraintLine : bomConstraintLinesList) {
            String  newConditionRange = bomConstraintLine.get(BomConstraintConstants.CONDITION_RANGE, String.class).replaceAll(oldBomId, newBomId);
            String  newResultRange = bomConstraintLine.get(BomConstraintConstants.RESULT_RANGE, String.class).replaceAll(oldBomId, newBomId);
            bomConstraintLine.set(BomConstraintConstants.CONDITION_RANGE, newConditionRange);
            bomConstraintLine.set(BomConstraintConstants.RESULT_RANGE, newResultRange);
        }
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = super.getQueryResult(query);
        if (Objects.nonNull(queryResult) && CollectionUtils.notEmpty(queryResult.getData())&& queryResult.getData().size()>=2000) {
            List<IObjectData> dataList = queryResult.getData();
            query.setOffset(offset+2000);
            QueryResult<IObjectData> newResult = super.getQueryResult(query);
            if (Objects.nonNull(newResult) && CollectionUtils.notEmpty(newResult.getData())) {
                dataList.addAll(newResult.getData());
                queryResult.setData(dataList);
            }
        }
        total = Objects.nonNull(queryResult)?queryResult.getTotalNumber():0;
        return queryResult;
    }


    private void fillAccountId() {
        if (Strings.isNullOrEmpty(arg.getAccountId())
                && DhtUtil.isDhtOrAccessoriesMallRequest(controllerContext.getPeerName(), controllerContext.getAppId())) {
            String accountId = AccountUtil.getAccountIdByOutTenantId(controllerContext.getTenantId(),
                    controllerContext.getUser().getOutTenantId(), controllerContext.getUser().getOutUserId());
            arg.setAccountId(accountId);
        }
    }

    @Override
    protected boolean needSearchRichTextExtra() {
        return BooleanUtils.isTrue(arg.getSearchRichTextExtra());
    }
}
