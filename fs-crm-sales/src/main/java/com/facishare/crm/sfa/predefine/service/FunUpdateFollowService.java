package com.facishare.crm.sfa.predefine.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.crm.sfa.predefine.service.model.UpdateFollowModel;
import com.facishare.crm.sfa.predefine.service.task.RecalculateTaskService;
import com.facishare.crm.sfa.predefine.service.task.TaskService;
import com.facishare.crm.sfa.task.RecalculateProducer;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.facishare.crm.sfa.utilities.constant.AccountConstants.Field.EXPIRE_TIME;
import static com.facishare.crm.sfa.utilities.constant.AccountConstants.Field.LAST_FOLLOWED_TIME;
import static com.facishare.crm.sfa.utilities.constant.LeadsConstants.LAST_FOLLOW_TIME;

@ServiceModule("follow")
@Component
@Slf4j
public class FunUpdateFollowService {

    @Autowired
    @Qualifier("objectDataPgService")
    private IObjectDataService objectDataService;

    @Autowired
    private RecalculateTaskService recalculateTaskService;
    
    @Autowired
    private RecalculateProducer recalculateProducer;
    
    @Autowired
    private TaskService taskService;

    @ServiceMethod("calculateExpireTime")
    public UpdateFollowModel.Result calculateExpireTime(ServiceContext context, UpdateFollowModel.Arg arg) {
        UpdateFollowModel.Result result = UpdateFollowModel.Result.builder().build();
        if (arg.getApiName() == null
                || (!Utils.ACCOUNT_API_NAME.equals(arg.getApiName()) && !Utils.LEADS_API_NAME.equals(arg.getApiName()))) {
            result.setMsg("apiName is illegal, only support AccountObj and LeadsObj");
            return result;
        }
        if (CollectionUtils.isEmpty(arg.getObjectIds())){
            result.setMsg("objectIds is empty");
            return result;
        }

        List<String> objectIds = arg.getObjectIds();
        List<IObjectData> objectDataList = Lists.newArrayList();
        ArrayList<String> updateFields = Lists.newArrayList();
        objectIds.forEach(
                objectId -> {
                    IObjectData data = new ObjectData();
                    data.setTenantId(context.getTenantId());
                    data.setDescribeApiName(arg.getApiName());
                    if (Utils.LEADS_API_NAME.equals(arg.getApiName())) {
                        data.set(LAST_FOLLOW_TIME, System.currentTimeMillis());
                        updateFields.add(LAST_FOLLOW_TIME);
                    } else {
                        data.set(LAST_FOLLOWED_TIME, System.currentTimeMillis());
                        updateFields.add(LAST_FOLLOWED_TIME);
                    }
                    data.setId(objectId);
                    objectDataList.add(data);
                }
        );

        try {
            objectDataService.batchUpdateIgnoreOther(objectDataList, updateFields, ActionContextExt.of(context.getUser()).getContext());
        } catch (MetadataServiceException e) {
            log.error("calculateExpireTime error", e);
        }
        for (String objectId : objectIds) {
            recalculateTaskService.send(context.getTenantId(), objectId, arg.getApiName(), ActionCodeEnum.FOLLOW);
        }
        return result;
    }
    
    /**
     * 更新客户、线索的到期时间并发送定时任务MQ
     * 
     * @param context 服务上下文
     * @param arg 参数信息，包含以下字段：
     *            - objectId: 对象ID（必填）
     *            - objectApiName: 对象API名称（必填）支持：AccountObj（客户）、LeadsObj（线索）
     *            - targetId: 转移目标ID（可选）公海id或线索池id
     *            - functionApiName: 功能API名称（可选）
     *            - recyclingReasonType: 回收原因类型（可选）支持以下类型：
     *              1 - {0}天未跟进
     *              2 - {0}小时未跟进  
     *              3 - {0}天{1}小时未跟进
     *              4 - {0}天未成交
     *              5 - {0}天未转换
     *              6 - {0}小时未转换
     *              7 - {0}天{1}小时未转换
     *            - recyclingDays: 回收设置的天数（可选）
     *            - expireTime: 到期时间戳（必填）Long类型，毫秒时间戳
     * @return 执行结果
     */
    @ServiceMethod("updateExpireTime")
    public UpdateFollowModel.Result updateExpireTime(ServiceContext context, UpdateFollowModel.RecyclingArg arg) {
        UpdateFollowModel.Result result = UpdateFollowModel.Result.builder().build();
        
        // 参数校验
        if (arg.getObjectApiName() == null
                || (!Utils.ACCOUNT_API_NAME.equals(arg.getObjectApiName()) && !Utils.LEADS_API_NAME.equals(arg.getObjectApiName()))) {
            result.setMsg("objectApiName is illegal, only support AccountObj and LeadsObj");
            return result;
        }
        if (arg.getObjectId() == null) {
            result.setMsg("objectId is empty");
            return result;
        }
        if (arg.getExpireTime() == null) {
            result.setMsg("expireTime is required");
            return result;
        }
        
        List<IObjectData> objectDataList = Lists.newArrayList();
        ArrayList<String> updateFields = Lists.newArrayList();
        
        // 1. 更新到期时间
        IObjectData data = new ObjectData();
        data.setTenantId(context.getTenantId());
        data.setDescribeApiName(arg.getObjectApiName());
        
        // 更新到期时间字段
        data.set(EXPIRE_TIME, arg.getExpireTime());
        updateFields.add(EXPIRE_TIME);
        
        data.setId(arg.getObjectId());
        objectDataList.add(data);
        
        try {
            // 批量更新数据
            objectDataService.batchUpdateIgnoreOther(objectDataList, updateFields, 
                    ActionContextExt.of(context.getUser()).getContext());
            
            // 2. 发送定时收回任务MQ
            recalculateTaskService.sendRecyclingTask(
                    context.getTenantId(), 
                    arg.getObjectId(), 
                    arg.getObjectApiName(), 
                    arg.getExpireTime()
            );
            
            result.setSuccess(true);
            result.setMsg("Successfully updated expire time and sent MQ for object " + arg.getObjectId());
            
        } catch (MetadataServiceException e) {
            log.error("updateExpireTimeAndSendMQ error", e);
            result.setSuccess(false);
            result.setMsg("Failed to update expire time: " + e.getMessage());
        } catch (Exception e) {
            log.error("updateExpireTimeAndSendMQ send MQ error", e);
            result.setSuccess(false);
            result.setMsg("Failed to send MQ: " + e.getMessage());
        }
        
        return result;
    }
}
