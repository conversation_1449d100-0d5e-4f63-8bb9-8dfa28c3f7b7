package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.describebuilder.ObjectReferenceFieldDescribeBuilder;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.GeneralPricePolicy.GeneralLayoutDescribeScenes;
import com.facishare.crm.sfa.predefine.service.model.AddFuncModel;
import com.facishare.crm.sfa.predefine.service.model.PredObjInitResult;
import com.facishare.crm.sfa.predefine.service.model.PriceBookInitResult;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.predefine.service.modulectrl.ModuleInitLayoutUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFABizLogUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.crm.util.GsonUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ObjectValueConvertRuleService;
import com.facishare.paas.appframework.core.predef.service.ObjectValueMappingService;
import com.facishare.paas.appframework.core.predef.service.dto.convertRule.CreateConvertRule;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.CreateRule;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypePojo;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypeRoleViewPojo;
import com.facishare.paas.appframework.metadata.dto.auth.RoleViewForWebPojo;
import com.facishare.paas.appframework.metadata.dto.auth.UpdateRoleRecordTypeModel;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.ObjectDataPermissionInfo;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ILayoutRuleService;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.ErrorCode;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.exception.MetadataValidateException;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.facishare.crm.privilege.util.Constant.SYSTEM_OPT_USER_ID;
import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;

@ServiceModule("enterprise_init")
@Component
@Slf4j
public class EnterpriseInitService {

    @Autowired
    private RecordTypeLogicServiceImpl recordTypeLogicService;
    @Autowired
    private DataPrivilegeService dataPrivilegeService;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    ILayoutService layoutService;
    @Autowired
    ILayoutRuleService layoutRuleService;
    @Autowired
    ObjectValueMappingService objectValueMappingService;
    @Autowired
    ConfigService configService;
    @Autowired
    private RecordTypeAuthProxy recordTypeAuthProxy;
    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected GeneralLayoutDescribeScenes generalLayoutDescribeScenes;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private ObjectConvertRuleService objectConvertRuleService;
    @Autowired
    private ObjectValueConvertRuleService objectValueConvertRuleService;

    private static final String CONVERT_RULE_RESOURCE_DIRECTORY = "objectconvertrulejson";

    private static int generalPricePolicyAllow;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config", config -> {
            generalPricePolicyAllow = config.getInt("general_price_policy_allow", 1);
        });
    }

    @ServiceMethod("initProductPkg")
    public PriceBookInitResult.Result initProductPkg(PriceBookInitResult.Arg arg, ServiceContext context) {
        log.info("arg:{},context{", arg, context);
        String tenantIds = arg.getTenantId();
        String[] eis = tenantIds.split(",");
        StringBuilder sb = new StringBuilder();
        for (String tenantId : eis) {
            String userId = arg.getUserId();
            List<String> describeApiNames = arg.getDescribeApiNames();
            if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(userId) || describeApiNames.isEmpty()) {
                log.error("accountId is blank,tenantId {}", context.getTenantId());
                return PriceBookInitResult.Result.builder().rtnMsg("wrong param").build();
            }
            User user = new User(tenantId, userId);
            for (String descApiName : describeApiNames) {
                // 功能权限初始化
                functionPrivilegeService.initFunctionPrivilege(user, descApiName);
                try {
                    //record type 初始化
                    recordTypeLogicService.recordTypeInit(user, null, tenantId, descApiName);
                    sb.append("||Success: Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" recordTypeInit |||");
                } catch (Exception e) {
                    log.error("recordTypeLogicService.recordTypeInit error", e);
                    sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" recordTypeInit failed|||");
                }
                IObjectDescribe describe = null;
                try {
                    describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, descApiName);
                } catch (MetadataServiceException e) {
                    log.error("Exception:", e);
                    sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" findByTenantIdAndDescribeApiName failed|||");
                }
                // 数据权限初始化
                try {
                    if ("ProductPackageObj".equals(descApiName) && null != describe) {
                        dataPrivilegeService.addCommonPrivilegeListResult(user, Lists.newArrayList(
                                new ObjectDataPermissionInfo(descApiName, describe.getDisplayName(),
                                        DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE.getValue())));
                        sb.append("||Success: Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" addCommonPrivilegeListResult |||");
                    }
                } catch (Exception e) {
                    log.error("dataPrivilegeService.addCommonPrivilegeListResult error", e);
                    sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" addCommonPrivilegeListResult failed|||");
                }
            }
        }
        log.info("initProductPkg executed:", sb.toString());
        return PriceBookInitResult.Result.builder().rtnMsg(sb.toString()).build();
    }


    @ServiceMethod("syncSkuRecordTypeToSpu")
    public PriceBookInitResult.Result syncSkuRecordTypeToSpu(PriceBookInitResult.Arg arg, ServiceContext context) {
        String[] tenantIds = arg.getTenantId().split(",");
        for (String tenantId : tenantIds) {
            RecordTypeResult skuRoleAndRecordType = recordTypeLogicService.findRoleAndRecordType(tenantId, Utils.PRODUCT_API_NAME, new User(tenantId, "-10000"));
            RecordTypeResult spuRoleAndRecordType = recordTypeLogicService.findRoleAndRecordType(tenantId, Utils.SPU_API_NAME, new User(tenantId, "-10000"));
            List<Map> skuRecordTypeList = skuRoleAndRecordType.getRecord_list();
            List<Map> spuRecordTypeList = spuRoleAndRecordType.getRecord_list();
            Set<String> recordTypeApiNames = spuRecordTypeList.stream().map(o -> o.get("api_name").toString()).collect(Collectors.toSet());
            skuRecordTypeList.removeIf(o -> recordTypeApiNames.contains(o.get("api_name").toString()));
            if (CollectionUtils.isNotEmpty(skuRecordTypeList)) {
                for (Map skuRecordType : skuRecordTypeList) {
                    RecordTypeRoleViewPojo spuNewRecordTypePojo = new RecordTypeRoleViewPojo();
                    spuNewRecordTypePojo.setLabel(skuRecordType.get("label").toString());
                    String skuRecordTypeApiName = skuRecordType.get("api_name").toString();
                    spuNewRecordTypePojo.setApi_name(skuRecordTypeApiName);
                    spuNewRecordTypePojo.setDescription(skuRecordType.get("description").toString());
                    spuNewRecordTypePojo.setIs_active((Boolean) skuRecordType.get("is_active"));
                    List<RoleViewForWebPojo> roleViewForWebPojoList = Lists.newArrayList();
                    for (Object skuRole : skuRoleAndRecordType.getRole_list()) {
                        List<String> recordList = (List<String>) ((Map) skuRole).get("records");
                        if (recordList != null && recordList.contains(skuRecordTypeApiName)) {
                            RoleViewForWebPojo spuRoleViewForWebPojo = new RoleViewForWebPojo();
                            spuRoleViewForWebPojo.setIs_default(Boolean.FALSE);
                            spuRoleViewForWebPojo.setRoleCode(((Map) skuRole).get("roleCode").toString());
                            spuRoleViewForWebPojo.setIs_used(Boolean.TRUE);
                            spuRoleViewForWebPojo.setLayout_api_name("SPUObj_layout_generate_by_UDObjectServer__c");
                            roleViewForWebPojoList.add(spuRoleViewForWebPojo);
                        }
                    }
                    spuNewRecordTypePojo.setRoles(roleViewForWebPojoList);
                    recordTypeLogicService.createRecordType(tenantId, Utils.SPU_API_NAME, spuNewRecordTypePojo, new User(tenantId, "-10000"));
                }
            }
        }
        return PriceBookInitResult.Result.builder().rtnMsg("").build();
    }

    @ServiceMethod("syncSpuRecordTypeToSku")
    public PriceBookInitResult.Result syncSpuRecordTypeToSku(PriceBookInitResult.Arg arg, ServiceContext context) {
        String[] tenantIds = arg.getTenantId().split(",");
        for (String tenantId : tenantIds) {
            RecordTypeResult spuRoleAndRecordType = recordTypeLogicService.findRoleAndRecordType(tenantId, Utils.SPU_API_NAME, new User(tenantId, "-10000"));
            RecordTypeResult skuRoleAndRecordType = recordTypeLogicService.findRoleAndRecordType(tenantId, Utils.PRODUCT_API_NAME, new User(tenantId, "-10000"));
            List<Map> spuRecordTypeList = spuRoleAndRecordType.getRecord_list();
            List<Map> skuRecordTypeList = skuRoleAndRecordType.getRecord_list();
            Set<String> skuRecordTypeApiNames = skuRecordTypeList.stream().map(o -> o.get("api_name").toString()).collect(Collectors.toSet());
            Set<String> spuRecordTypeApiNames = spuRecordTypeList.stream().map(o -> o.get("api_name").toString()).collect(Collectors.toSet());
            List<Map> skuExist = skuRecordTypeList.stream().filter(o -> spuRecordTypeApiNames.contains(o.get("api_name").toString())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuExist)) {
                List<RecordTypePojo> recordTypePojoList = Lists.newArrayList();
                for (Map map : skuExist) {
                    for (Object role : spuRoleAndRecordType.getRole_list()) {
                        RecordTypePojo recordTypePojo = new RecordTypePojo();
                        recordTypePojo.setAppId("CRM");
                        recordTypePojo.setTenantId(tenantId);
                        recordTypePojo.setRoleCode(((Map) role).get("roleCode").toString());
                        recordTypePojo.setEntityId(Utils.PRODUCT_API_NAME);
                        recordTypePojo.setRecordTypeId(map.get("api_name").toString());
                        recordTypePojo.setDefaultType(map.get("api_name").toString().equals(((Map) role).get("default_record").toString()));
                        recordTypePojoList.add(recordTypePojo);
                    }
                }


                UpdateRoleRecordTypeModel.Arg arg1 = new UpdateRoleRecordTypeModel.Arg();
                arg1.setRecordTypePojos(recordTypePojoList);
                arg1.setEntityId(Utils.PRODUCT_API_NAME);
                arg1.setAuthContext(new User(tenantId, "-10000"));
                recordTypeAuthProxy.updateRoleRecordType(arg1, PAAS_PRIVILEGE_HEADDER.buildHeader(tenantId));
            }
            spuRecordTypeList.removeIf(o -> skuRecordTypeApiNames.contains(o.get("api_name").toString()));
            if (CollectionUtils.isNotEmpty(spuRecordTypeList)) {
                for (Map spuRecordType : spuRecordTypeList) {
                    RecordTypeRoleViewPojo recordTypeRoleViewPojo = new RecordTypeRoleViewPojo();
                    recordTypeRoleViewPojo.setLabel(spuRecordType.get("label").toString());
                    recordTypeRoleViewPojo.setApi_name(spuRecordType.get("api_name").toString());
                    recordTypeRoleViewPojo.setDescription(spuRecordType.get("description").toString());
                    recordTypeRoleViewPojo.setIs_active((Boolean) spuRecordType.get("is_active"));
                    List<RoleViewForWebPojo> roleViewForWebPojoList = Lists.newArrayList();
                    for (Object role : spuRoleAndRecordType.getRole_list()) {
                        //同步到产品业务类型适合所有的角色
//                            if (((List) ((Map) role).get("records")).contains(spuRecordType.get("api_name").toString())) {
                        RoleViewForWebPojo roleViewForWebPojo = new RoleViewForWebPojo();
                        roleViewForWebPojo.setIs_default(Boolean.FALSE);
                        roleViewForWebPojo.setRoleCode(((Map) role).get("roleCode").toString());
                        roleViewForWebPojo.setIs_used(Boolean.TRUE);
                        roleViewForWebPojo.setLayout_api_name("ProductObj_layout_generate_by_UDObjectServer__c");
                        roleViewForWebPojoList.add(roleViewForWebPojo);
//                            }
                    }
                    recordTypeRoleViewPojo.setRoles(roleViewForWebPojoList);
                    recordTypeLogicService.createRecordType(tenantId, Utils.PRODUCT_API_NAME, recordTypeRoleViewPojo, new User(tenantId, "-10000"));
                }
            }
        }
        return PriceBookInitResult.Result.builder().rtnMsg("").build();
    }

    @ServiceMethod("commonInitAll")
    public PriceBookInitResult.Result commonInitAll(PriceBookInitResult.Arg arg, ServiceContext context) {
        log.info("arg:{},context{", arg, context);
        String tenantIds = arg.getTenantId();
        String[] eis = tenantIds.split(",");
        StringBuilder sb = new StringBuilder();
        List<String> opers = arg.getOperation();
        for (String tenantId : eis) {
            User user = new User(tenantId, arg.getUserId());
            String msg = this.initPrivilegeRelate(arg.getDescribeApiNames(), user, opers, arg.getDataPermissionValue(), arg.getLayoutApiName());
            sb.append(msg);
        }
        log.info("commonInitAll executed:", sb.toString());
        return PriceBookInitResult.Result.builder().rtnMsg(sb.toString()).build();
    }

    public String initPrivilegeRelate(List<String> describeApiNames, User user, List<String> opers, String dataPermissionValue, String layoutApiName) {
        StringBuilder sb = new StringBuilder();
        String tenantId = user.getTenantId();
        if (CollectionUtils.isEmpty(opers)) {
            opers = Lists.newArrayList();
            opers.add("funcPrivilege");
            opers.add("recordType");
            opers.add("dataPrivilege");
        }
        for (String descApiName : describeApiNames) {
            // 功能权限初始化
            if (opers.contains("funcPrivilege")) {
                try {
                    functionPrivilegeService.initFunctionPrivilege(user, descApiName);
                    sb.append("||Success: Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" initFunctionPrivilege |||");
                } catch (Exception e) {
                    sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" initFunctionPrivilege failed|||");
                    log.error("initFunctionPrivilege failed:", e);
                }
            }
            if (opers.contains("recordType")) {
                try {
                    //record type 初始化
                    recordTypeLogicService.recordTypeInit(user, layoutApiName, tenantId, descApiName);
                    sb.append("||Success: Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" recordTypeInit |||");
                } catch (Exception e) {
                    log.error("recordTypeLogicService.recordTypeInit error", e);
                    sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" recordTypeInit failed|||");
                }
            }
            if (opers.contains("dataPrivilege")) {
                try {
                    IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, descApiName);
                    if (StringUtils.isEmpty(dataPermissionValue)) {
                        dataPermissionValue = DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE.getValue();
                    }
                    dataPrivilegeService.addCommonPrivilegeListResult(user, Lists.newArrayList(
                            new ObjectDataPermissionInfo(descApiName, describe.getDisplayName(), dataPermissionValue)));
                    log.info("addCommonPrivilegeListResult called , dataPermissionValue is {}", dataPermissionValue);
                    sb.append("||Success: Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" addCommonPrivilegeListResult |||");
                } catch (Exception e) {
                    log.error("dataPrivilegeService.addCommonPrivilegeListResult error", e);
                    sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" addCommonPrivilegeListResult failed|||");
                }
            }
        }
        log.info("initPrivilegeRelate executed:", sb.toString());
        return sb.toString();
    }

    @ServiceMethod("pred_obj_init")
    public PredObjInitResult.Result predObjInit(PredObjInitResult.Arg arg, ServiceContext context) {
        log.info("predObjInit init arg:{},context{", arg, context);
        String tenantIds = arg.getTenantId();
        List<String> operations = arg.getOperations();
        String[] eis = tenantIds.split(",");
        StringBuilder sb = new StringBuilder();
        for (String tenantId : eis) {
            List<String> describeApiNames = arg.getDescribeApiNames();
            User user = new User(tenantId, "-10000");
            for (String descApiName : describeApiNames) {
                // 功能权限初始化
                if (operations.contains("funcPrivilegeInit")) {
                    try {
                        functionPrivilegeService.initFunctionPrivilege(user, descApiName);
                        log.info("initFunctionPrivilege executed:", sb.toString());
                        sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" initFunctionPrivilege success!!");
                    } catch (Exception e) {
                        log.error("initFunctionPrivilege failed:", e);
                        sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" initFunctionPrivilege failed|||");
                    }
                }

                // 数据权限初始化
                if (operations.contains("dataPrivilegeInit")) {
                    IObjectDescribe describe = null;
                    try {
                        describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, descApiName);
                    } catch (MetadataServiceException e) {
                        log.error("Exception:", e);
                        sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" findByTenantIdAndDescribeApiName failed|||");
                    }
                    try {
                        if (describe != null) {
                            dataPrivilegeService.addCommonPrivilegeListResult(user, Lists.newArrayList(
                                    new ObjectDataPermissionInfo(descApiName, describe.getDisplayName(),
                                            DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE.getValue())));
                        }
                        sb.append("||Success: Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" addCommonPrivilegeListResult |||");
                    } catch (Exception e) {
                        log.error("dataPrivilegeService.addCommonPrivilegeListResult error", e);
                        sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" addCommonPrivilegeListResult failed|||");
                    }
                }
            }
        }
        return PredObjInitResult.Result.builder().rtnMsg(sb.toString()).build();
    }

    @ServiceMethod("add_func")
    public boolean addFunc(ServiceContext context, AddFuncModel.Arg arg) {
        if (null == arg || Strings.isNullOrEmpty(arg.getApiName()) || CollectionUtils.isEmpty(arg.getCodeList())) {
            return false;
        }
        User user = new User(context.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        serviceFacade.batchCreateFunc(user, arg.getApiName(), arg.getCodeList());
        serviceFacade.updateUserDefinedFuncAccess(user,
                PrivilegeConstants.ADMIN_ROLE_CODE, arg.getApiName(), arg.getCodeList(), Lists.newArrayList());
        return true;
    }

    public void createDesc(String tenantId, String describeApiName) throws MetadataServiceException {
        ObjectDescribe describe = getDescribeFromLocalResource(describeApiName);
        if (Utils.MULTI_UNIT_RELATED_API_NAME.equals(describeApiName)) {
            if (SFAConfigUtil.isSpuOpen(tenantId)) {
                IFieldDescribe fieldDescribe = describe.getFieldDescribe("spu_id");
                if (fieldDescribe != null) {
                    fieldDescribe.setRequired(true);
                }
            }
        }
        describe.setTenantId(tenantId);
        describe.setCreatedBy(SYSTEM_OPT_USER_ID);
        describe.setLastModifiedBy(SYSTEM_OPT_USER_ID);
        objectDescribeService.create(describe, true, false);
    }

    public String initDescribeForTenant(String tenantId, String describeApiName) {
        try {
            ObjectDescribe describe = getDescribeFromLocalResource(describeApiName);
            describe.setTenantId(tenantId);
            describe.setCreatedBy(SYSTEM_OPT_USER_ID);
            describe.setLastModifiedBy(SYSTEM_OPT_USER_ID);
            objectDescribeService.create(describe, true, false);
            log.info("objectDescribeDraftService.create executed! tenantId:{},apiName:{}", tenantId, describeApiName);
            return " \n initDescribeForTenant success";
        } catch (MetadataServiceException e) {
            log.error("EnterpriseInitService#initDescribeForTenant failed initDescribeForTenant error,tenantId {}", tenantId, e);
            return " \n initDescribeForTenant error:" + e.getMessage();
        }
    }

    public String initDescribeForTenant(String tenantId, ObjectDescribe describe) {
        try {
            describe.setTenantId(tenantId);
            describe.setCreatedBy(SYSTEM_OPT_USER_ID);
            describe.setLastModifiedBy(SYSTEM_OPT_USER_ID);
            objectDescribeService.create(describe, true, false);
            log.info("objectDescribeDraftService.create executed! tenantId:{},apiName:{}", tenantId, describe.getApiName());
            return " \n initDescribeForTenant success";
        } catch (MetadataServiceException e) {
            log.error("partner_init open initDescribeForTenant error,tenantId {}", tenantId, e);
            return " \n initDescribeForTenant error:" + e.getMessage();
        }
    }


    public String initDescribeForTenant(String tenantId, String describeApiName, String moduleCode) {

        try {
            ObjectDescribe describe = getDescribeFromLocalResource(describeApiName);
            if (SFAPreDefineObject.AmortizeInfo.getApiName().equals(describeApiName) &&
                    (IModuleInitService.COUPON.equals(moduleCode) || IModuleInitService.REBATE.equals(moduleCode))) {
                //先开启优惠券或者返利，移除分摊信息中价格政策相关字段
                List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();
                HashSet<String> removeFields = Sets.newHashSet("price_policy_id", "price_policy_rule_id");
                fieldDescribes.removeIf(x -> removeFields.contains(x.getApiName()));
                //必须的设置回去，要不不会变更，不是一个对象
                describe.setFieldDescribes(fieldDescribes);
            }
            describe.setTenantId(tenantId);
            describe.setCreatedBy(SYSTEM_OPT_USER_ID);
            describe.setLastModifiedBy(SYSTEM_OPT_USER_ID);
            objectDescribeService.create(describe, true, false);
            log.info("objectDescribeDraftService.create executed! tenantId:{},apiName:{}", tenantId, describeApiName);
            return " \n initDescribeForTenant success";
        } catch (MetadataServiceException e) {
            log.error("partner_init open initDescribeForTenant error,tenantId {}", tenantId, e);
            return " \n initDescribeForTenant error:" + e.getMessage();
        }
    }

    public Boolean getIsEnablePriceBook(String tenantId) {
        if ("-1".equals(tenantId)) {
            return false;
        }
        String result = SFAConfigUtil.getConfigValue(tenantId, "28", User.SUPPER_ADMIN_USER_ID);
        return "1".equals(result);
    }

    private static Map configMap;
    private static Map detailedConfigMap;

    static {
        configMap = getConfigMap();
        detailedConfigMap = getDetailedConfigMap();
    }

    public static Map<String, Object> getConfigMap() {
        Map<String, Object> outMap = new HashMap<>();
        outMap.put("edit", 1);
        outMap.put("display", 1);
        outMap.put("enable", 0);
        Map<String, Object> innerMap = new HashMap<>();
        innerMap.put("default_value", 0);
        innerMap.put("wheres", 0);
        innerMap.put("is_required", 1);
        innerMap.put("help_text", 1);
        innerMap.put("label", 1);
        innerMap.put("target_related_list_label", 1);
        outMap.put("attrs", innerMap);
        return outMap;
    }

    public static Map<String, Object> getDetailedConfigMap() {
        Map<String, Object> outMap = new HashMap<>();
        outMap.put("edit", 1);
        outMap.put("display", 1);
        outMap.put("enable", 0);
        Map<String, Object> innerMap = new HashMap<>();
        innerMap.put("default_value", 0);
        innerMap.put("wheres", 0);
        innerMap.put("is_required", 1);
        innerMap.put("help_text", 1);
        innerMap.put("label", 1);
        innerMap.put("target_related_list_label", 1);
        outMap.put("attrs", innerMap);
        return outMap;

    }

    /**
     * 商机2.0增加价目表字段,商机2.0明细上增加价目表产品字段，并在布局中展示
     *
     * @param user
     * @param notNull
     */
    public void addPriceBookFieldForNewOpportunity(User user, Map<String, IObjectDescribe> describiMap,
                                                   Boolean notNull) {
        List<IFieldDescribe> insertFieldDescribes = Lists.newArrayList();
        List<IFieldDescribe> updateFieldDescribes = Lists.newArrayList();
        //region 商机2.0修改价目表
        IObjectDescribe newOpportunityDescribe = describiMap.get(Utils.NEW_OPPORTUNITY_API_NAME);
        if (newOpportunityDescribe != null) {
            IFieldDescribe priceBookIDFieldDescribe = newOpportunityDescribe.getFieldDescribe("price_book_id");
            if (priceBookIDFieldDescribe != null) {
                priceBookIDFieldDescribe.setActive(true);
                priceBookIDFieldDescribe.setConfig(configMap);
                priceBookIDFieldDescribe.setRequired(notNull);
                updateFieldDescribes.add(priceBookIDFieldDescribe);
            } else {
                priceBookIDFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder()
                        .apiName("price_book_id")
                        .label("价目表")
                        .targetApiName(Utils.PRICE_BOOK_API_NAME)
                        .targetRelatedListLabel("商机2.0")
                        .targetRelatedListName("price_book_new_opportunity_list")
                        .unique(false)
                        .required(notNull)
                        .build();
                priceBookIDFieldDescribe.setConfig(configMap);
                insertFieldDescribes.add(priceBookIDFieldDescribe);
            }
            if (insertFieldDescribes.size() > 0) {
                addFieldDescribe(newOpportunityDescribe, insertFieldDescribes);
            }
            if (updateFieldDescribes.size() > 0) {
                serviceFacade.updateFieldDescribe(newOpportunityDescribe, updateFieldDescribes);
            }

            // 把新增的字段放入到指定的layout中的FormComponent中的基本信息FieldSection的里面
            FieldLayoutPojo fieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.ObjectReference.renderType, false, notNull);
            insertFieldToLayout(user, newOpportunityDescribe, priceBookIDFieldDescribe, fieldLayoutPojo);
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), Utils.NEW_OPPORTUNITY_API_NAME);
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONFIG_GETDESCRIPTIONFAILED));
        }
        //endregion

        //region 商机2.0明细修改价目表产品
        IObjectDescribe newOpportunityLinesDescribe = describiMap.get(Utils.NEW_OPPORTUNITY_LINES_API_NAME);
        if (newOpportunityLinesDescribe != null) {
            insertFieldDescribes.clear();
            updateFieldDescribes.clear();
            IFieldDescribe priceBookProductIdFieldDescribe = newOpportunityLinesDescribe.getFieldDescribe("price_book_product_id");
            if (priceBookProductIdFieldDescribe != null) {
                priceBookProductIdFieldDescribe.setActive(true);
                priceBookProductIdFieldDescribe.setConfig(detailedConfigMap);
                priceBookProductIdFieldDescribe.setRequired(Boolean.FALSE);
                updateFieldDescribes.add(priceBookProductIdFieldDescribe);
            } else {
                priceBookProductIdFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder()
                        .apiName("price_book_product_id")
                        .label("价目表产品")
                        .targetApiName(Utils.PRICE_BOOK_PRODUCT_API_NAME)
                        .targetRelatedListLabel("商机2.0明细")
                        .targetRelatedListName("price_book_product_new_opportunity_lines_list")
                        .unique(false)
                        .required(Boolean.FALSE)
                        .build();
                priceBookProductIdFieldDescribe.setConfig(detailedConfigMap);
                insertFieldDescribes.add(priceBookProductIdFieldDescribe);
            }
            IFieldDescribe fieldDescribe = newOpportunityLinesDescribe.getFieldDescribe("price_book_id");

            if (fieldDescribe != null) {
                fieldDescribe.setActive(true);
                fieldDescribe.setConfig(detailedConfigMap);
                fieldDescribe.setRequired(Boolean.FALSE);
                updateFieldDescribes.add(fieldDescribe);
            } else {
                fieldDescribe = ObjectReferenceFieldDescribeBuilder.builder()
                        .apiName("price_book_id")
                        .label("价目表")
                        .targetApiName(Utils.PRICE_BOOK_API_NAME)
                        .targetRelatedListLabel("商机2.0明细")
                        .targetRelatedListName("price_book_new_opportunity_lines_list")
                        .unique(false)
                        .required(Boolean.FALSE)
                        .build();
                fieldDescribe.setConfig(detailedConfigMap);
                insertFieldDescribes.add(fieldDescribe);
            }

            IFieldDescribe discountField = newOpportunityLinesDescribe.getFieldDescribe("discount");
            if (discountField == null) {
                log.warn("setConfigValue>获取字段描述失败={},{},{}", user.getTenantId(), Utils.NEW_OPPORTUNITY_LINES_API_NAME, "discount");
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_FAILED_TO_GET_FIELD_DESCRIPTION));
            }
            discountField.set("default_value", "$price_book_product_id__r.discount$");
            discountField.set("default_is_expression", true);
            updateFieldDescribes.add(discountField);

            IFieldDescribe productPriceField = newOpportunityLinesDescribe.getFieldDescribe("price");
            if (productPriceField == null) {
                log.warn("setConfigValue>获取字段描述失败={},{},{}", user.getTenantId(), Utils.NEW_OPPORTUNITY_LINES_API_NAME, "price");
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_FAILED_TO_GET_FIELD_DESCRIPTION));
            }
            productPriceField.set("default_value", "$price_book_product_id__r.pricebook_sellingprice$");
            updateFieldDescribes.add(productPriceField);
            if (insertFieldDescribes.size() > 0) {
                addFieldDescribe(newOpportunityLinesDescribe, insertFieldDescribes);
            }
            if (updateFieldDescribes.size() > 0) {
                serviceFacade.updateFieldDescribe(newOpportunityLinesDescribe, updateFieldDescribes);
            }

            // 把新增的字段放入到指定的layout中的FormComponent中的基本信息FieldSection的里面
            List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList = Lists.newArrayList();
            FieldLayoutPojo fieldLayoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.ObjectReference.renderType, false, false);
            addFieldTupleList.add(Tuple.of(priceBookProductIdFieldDescribe, fieldLayoutPojo));

            FieldLayoutPojo layoutPojo = getFieldLayoutPojo(SystemConstants.RenderType.ObjectReference.renderType, false, false);
            addFieldTupleList.add(Tuple.of(fieldDescribe, layoutPojo));

            insertFieldsToLayout(user, newOpportunityLinesDescribe, addFieldTupleList);
        } else {
            log.warn("setConfigValue>获取描述失败={},{}", user.getTenantId(), Utils.NEW_OPPORTUNITY_LINES_API_NAME);
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_FAILED_TO_GET_FIELD_DESCRIPTION));
        }
        //endregion
    }

    public void addFieldDescribe(IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribeList) {
        try {
            objectDescribeService.addCustomFieldDescribe(objectDescribe, fieldDescribeList);
        } catch (MetadataServiceException e) {
            log.error("addFieldDescribe error,tenantId {} ", objectDescribe.getTenantId(), e);
            throw new RuntimeException(e);
        }
    }

    public FieldLayoutPojo getFieldLayoutPojo(String renderType, boolean readOnly, boolean required) {
        FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
        fieldLayoutPojo.setReadonly(readOnly);
        fieldLayoutPojo.setRequired(required);
        fieldLayoutPojo.setRenderType(renderType);
        return fieldLayoutPojo;
    }

    public void insertFieldsToLayout(User user, IObjectDescribe objectDescribe, List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList) {
        List<ILayout> layoutList = serviceFacade.getLayoutLogicService().getDetailLayouts(user.getTenantId(), objectDescribe);
        layoutList.forEach(m -> removeRepeatField(m, addFieldTupleList.stream().map(n -> n.getKey().getApiName()).distinct()
                .collect(Collectors.toList())));
        layoutList.forEach(m -> {
            addFieldTupleList.forEach(tuple -> LayoutExt.of(m).addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.getLayoutLogicService().updateLayout(user, m);
        });
    }

    public ILayout removeRepeatField(ILayout layout, List<String> fieldNames) {
        try {
            for (IComponent component : layout.getComponents()) {
                if (component instanceof FormComponent) {
                    FormComponent formComponent = (FormComponent) component;
                    removeField(fieldNames, formComponent.getFieldSections());
                }
            }
        } catch (MetadataServiceException e) {
            log.warn("updateFieldRenderType error occur,for exception:{}", e);
            throw new RuntimeException(e);

        }
        return layout;
    }

    private void removeField(List<String> fieldNames, List<IFieldSection> fieldSections) {
        for (IFieldSection fieldSection : fieldSections) {
            List<IFormField> removeFormFields = Lists.newArrayList();
            List<IFormField> formFields = fieldSection.getFields();
            formFields.forEach(formFiled -> {
                if (fieldNames.contains(formFiled.getFieldName())) {
                    removeFormFields.add(formFiled);
                }
            });
            formFields.removeAll(removeFormFields);
            fieldSection.setFields(formFields);
        }
    }

    public void insertFieldToLayout(User user, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, FieldLayoutPojo fieldLayoutPojo) {
        List<Tuple<IFieldDescribe, FieldLayoutPojo>> addFieldTupleList = Lists.newArrayList();
        addFieldTupleList.add(Tuple.of(fieldDescribe, fieldLayoutPojo));
        List<ILayout> layoutList = serviceFacade.getLayoutLogicService().getDetailLayouts(user.getTenantId(), objectDescribe);
        layoutList.forEach(m -> removeRepeatField(m, Lists.newArrayList(fieldDescribe.getApiName())));
        layoutList.forEach(m -> {
            addFieldTupleList.forEach(tuple -> LayoutExt.of(m).addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.getLayoutLogicService().updateLayout(user, m);
        });
    }

    public String initMultiLayoutForOneTenant(List<String> descApiNames, String tenantId) {
        StringBuilder msgForOneTenant = new StringBuilder();
        for (String apiName : descApiNames) {
            try {
                String detailLayoutStr = getLayoutJsonFromResourceByApiName(apiName, "detail");
                this.initLayoutByJson(tenantId, apiName, detailLayoutStr);
                String listLayoutStr = getLayoutJsonFromResourceByApiName(apiName, "list");
                this.initLayoutByJson(tenantId, apiName, listLayoutStr);
                msgForOneTenant.append(",init layout:'").append(apiName).append("' succeed");
            } catch (Exception e) {
                msgForOneTenant.append(",init:'").append(apiName).append("' failed:").append(e);
                log.error("init initMultiLayoutForOneTenant error:", e);
            }
        }
        return msgForOneTenant.toString();
    }

    public String initMultiLayoutForOneTenant(List<String> descApiNames, String tenantId, List<IFormField> formFieldList) {
        StringBuilder msgForOneTenant = new StringBuilder();
        for (String apiName : descApiNames) {
            try {
                String detailLayoutStr = getLayoutJsonFromResourceByApiName(apiName, "detail");
                this.initLayoutByJson(tenantId, apiName, detailLayoutStr, formFieldList);
                String listLayoutStr = getLayoutJsonFromResourceByApiName(apiName, "list");
                this.initLayoutByJson(tenantId, apiName, listLayoutStr);
                msgForOneTenant.append(",init layout:'").append(apiName).append("' succeed");
            } catch (Exception e) {
                msgForOneTenant.append(",init:'").append(apiName).append("' failed:").append(e);
                log.error("init initMultiLayoutForOneTenant error:", e);
            }
        }
        return msgForOneTenant.toString();
    }

    private void initLayoutByJson(String tenantId, String describeApiName, String layoutJson, List<IFormField> formFieldList) throws MetadataServiceException {
        Layout layout = new Layout();
        layout.fromJsonString(layoutJson);
        layout.setTenantId(tenantId);
        if (CollectionUtils.isNotEmpty(formFieldList)) {
            ModuleInitLayoutUtil.addFieldsToDetailLayoutFormComponent(layout, formFieldList);
        }
        List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(describeApiName, tenantId);

        if (CollectionUtils.isNotEmpty(layouts)) {
            Optional<ILayout> oldLayoutOpt = layouts.stream().filter(x -> layout.getLayoutType().equals(x.getLayoutType())).findFirst();
            if (oldLayoutOpt.isPresent()) {

                ILayout oldLayout = oldLayoutOpt.get();
                layout.setId(oldLayout.getId());
                layout.setVersion(oldLayout.getVersion());
                layoutService.replace(layout);
            } else {
                layoutService.create(layout);
            }
        } else {
            layoutService.create(layout);
        }
    }

    public String initMultiLayoutForOneTenant(List<String> descApiNames, String tenantId, String moduleCode) {
        StringBuilder msgForOneTenant = new StringBuilder();
        for (String apiName : descApiNames) {
            try {
                String detailLayoutStr = getLayoutJsonFromResourceByApiName(apiName, "detail");
                this.initLayoutByJson(tenantId, apiName, detailLayoutStr, layout -> {
                    //先开启返利或者优惠券，去掉detail布局中去掉价格政策相关字段
                    if (SFAPreDefineObject.AmortizeInfo.getApiName().equals(apiName) &&
                            (IModuleInitService.COUPON.equals(moduleCode) || IModuleInitService.REBATE.equals(moduleCode))) {
                        removeRepeatField(layout, Lists.newArrayList("price_policy_id", "price_policy_rule_id"));
                    }
                });
                String listLayoutStr = getLayoutJsonFromResourceByApiName(apiName, "list");
                this.initLayoutByJson(tenantId, apiName, listLayoutStr, layout -> {
                    //先开启返利或者优惠券，去掉list布局中去掉价格政策相关字段
                    if (SFAPreDefineObject.AmortizeInfo.getApiName().equals(apiName) &&
                            (IModuleInitService.COUPON.equals(moduleCode) || IModuleInitService.REBATE.equals(moduleCode))) {
                        TableComponent tableComponent = LayoutExt.of(layout).getTableComponent().get();
                        List<ITableColumn> includeFields = tableComponent.getIncludeFields();
                        HashSet<String> removeFields = Sets.newHashSet("price_policy_id", "price_policy_rule_id");
                        includeFields.removeIf(x -> removeFields.contains(x.get("api_name")));
                        tableComponent.setIncludeFields(includeFields);
                    }
                });
                msgForOneTenant.append(",init layout:'").append(apiName).append("' succeed");
            } catch (Exception e) {
                msgForOneTenant.append(",init:'").append(apiName).append("' failed:").append(e);
                log.error("init initMultiLayoutForOneTenant error:", e);
            }
        }
        return msgForOneTenant.toString();
    }


    //初始化映射规则
    public void initObjectMappingRule(ServiceContext context, String ruleApiName) {
        if (StringUtils.isBlank(ruleApiName)) {
            return;
        }
        List<IObjectMappingRuleInfo> ruleInfos = objectMappingService.findByApiName(context.getUser(), ruleApiName);
        if (CollectionUtils.isNotEmpty(ruleInfos)) {
            return;
        }
        String jsonRule = loadJsonFromResource(ruleApiName);
        CreateRule.Arg arg = GsonUtil.json2object(jsonRule, CreateRule.Arg.class);
        objectValueMappingService.createRule(arg, context);
    }

    //初始化对象转换规则
    public void initObjectConvertRule(ServiceContext context, String ruleApiName) {
        String jsonRule = loadJsonFromResource(CONVERT_RULE_RESOURCE_DIRECTORY, ruleApiName);
        CreateConvertRule.Arg arg = JSON.parseObject(jsonRule, CreateConvertRule.Arg.class);

        List<IObjectMappingRuleInfo> ruleInfos = objectConvertRuleService.findConvertRuleByApiName(context.getUser(), ruleApiName);
        if (CollectionUtils.isNotEmpty(ruleInfos)) {
            return;
        }
        objectValueConvertRuleService.create(arg, context);
    }

    private String loadJsonFromResource(String ruleApiName) {
        ClassLoader classLoader = getClass().getClassLoader();
        try {
            return IOUtils.toString(classLoader.getResource(getMappingRulePath(ruleApiName)), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("loadJsonFromResource error");
            return "";
        }
    }

    private String loadJsonFromResource(String relativeDirectory, String ruleApiName) {
        ClassLoader classLoader = getClass().getClassLoader();
        try {
            return IOUtils.toString(classLoader.getResource(getMappingRulePath(relativeDirectory, ruleApiName)), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("loadJsonFromResource error");
            return "";
        }
    }

    public void initLayoutByJson(String tenantId, String describeApiName, String layoutJson) throws MetadataServiceException {
        Layout layout = new Layout();
        layout.fromJsonString(layoutJson);
        layout.setTenantId(tenantId);

        List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(describeApiName, tenantId);

        if (CollectionUtils.isNotEmpty(layouts)) {
            Optional<ILayout> oldLayoutOpt = layouts.stream().filter(x -> layout.getLayoutType().equals(x.getLayoutType())).findFirst();
            if (oldLayoutOpt.isPresent()) {

                ILayout oldLayout = oldLayoutOpt.get();
                layout.setId(oldLayout.getId());
                layout.setVersion(oldLayout.getVersion());
                layoutService.replace(layout);
            } else {
                layoutService.create(layout);
            }
        } else {
            layoutService.create(layout);
        }
    }

    public void initLayoutByJson(String tenantId, String describeApiName, String layoutJson, Consumer<Layout> consumer) throws MetadataServiceException {
        Layout layout = new Layout();
        layout.fromJsonString(layoutJson);
        layout.setTenantId(tenantId);
        consumer.accept(layout);
        List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(describeApiName, tenantId);
        if (CollectionUtils.isEmpty(layouts)) {
            layouts = Lists.newArrayList();
        }
        if (CollectionUtils.isNotEmpty(layouts)) {
            Optional<ILayout> oldLayoutOpt = layouts.stream().filter(x -> layout.getLayoutType().equals(x.getLayoutType())).findFirst();
            if (oldLayoutOpt.isPresent()) {
                ILayout oldLayout = oldLayoutOpt.get();
                layout.setId(oldLayout.getId());
                layout.setVersion(oldLayout.getVersion());
                layoutService.replace(layout);
            } else {
                layoutService.create(layout);
            }
        } else {
            layoutService.create(layout);
        }
    }

    public ObjectDescribe getDescribeFromLocalResource(String apiName) {
        ObjectDescribe describe = new ObjectDescribe();
        //获取到此对象的json数据
        String jsonStr = getDescribeJsonFromResourceByApiName(apiName);
        describe.fromJsonString(jsonStr);
        return describe;
    }

    /**
     * 从本地resource种获取对应对象的预制json格式。
     *
     * @param apiName 对象apiName
     */
    private String getDescribeJsonFromResourceByApiName(String apiName) {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonstr;
        try {
            jsonstr = IOUtils.toString(classLoader.getResource(
                    "describejson/init_crm_" + apiName + "_describe.json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initCRMDescribeByApiName file parse error");
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initCRMDescribeByApiName file parse error", e);
        }
        return jsonstr;
    }

    private String getMappingRulePath(String ruleApiName) {
        return String.format("objectmappingrulejson/init_%s.json", ruleApiName);
    }

    private String getMappingRulePath(String relativeDirectory, String ruleApiName) {
        return String.format("%s/init_%s.json", relativeDirectory, ruleApiName);
    }

    public String getLayoutJsonFromResourceByApiName(String apiName, String type) {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonstr;
        try {
            jsonstr = IOUtils.toString(classLoader.getResource(
                    "layoutjson/init_crm_" + apiName + "_" + type + "_layout.json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initCRMDescribeByApiName file parse error");
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initCRMDescribeByApiName file parse error", e);
        }
        return jsonstr;
    }

    public String getPartnerJsonFromFieldName(String fieldName) {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonstr;
        try {
            jsonstr = IOUtils.toString(classLoader.getResource(
                    "partnerfieldjson/" + fieldName.toLowerCase() + "_field.json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initPartnerFieldJSON file parse error");
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initPartnerFieldJSON file parse error", e);
        }
        return jsonstr;
    }

    public String getPoolJsonFromFieldName(String fieldName) {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonStr;
        try {
            jsonStr = IOUtils.toString(classLoader.getResource(
                    "poolfieldjson/" + fieldName.toLowerCase() + "_field.json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initPoolFieldJSON file parse error");
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initPoolFieldJSON file parse error", e);
        }
        return jsonStr;
    }

    public String getProductJsonFromFieldName(String fieldName) {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonStr;
        try {
            jsonStr = IOUtils.toString(classLoader.getResource(
                    "productfieldjson/" + fieldName.toLowerCase() + "_field.json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initProductFieldJSON file parse error");
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initProductFieldJSON file parse error", e);
        }
        return jsonStr;
    }

    public String getAvailableJsonFromFieldName(String fieldName) {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonStr;
        try {
            jsonStr = IOUtils.toString(classLoader.getResource(
                    "availablefieldjson/" + fieldName.toLowerCase() + "_field.json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initAvailableFieldJSON file parse error");
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initAvailableFieldJSON file parse error", e);
        }
        return jsonStr;
    }

    public String getPriceBookJsonFromFieldName(String fieldName) {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonStr;
        try {
            jsonStr = IOUtils.toString(classLoader.getResource(
                    "pricebookfieldjson/" + fieldName.toLowerCase() + "_field.json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initPriceBookFieldJSON file parse error");
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initPriceBookFieldJSON file parse error", e);
        }
        return jsonStr;
    }

    public void initAvailableObject(User user, RequestContext context) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName());
        List<String> apiNames = Lists.newArrayList(SFAPreDefineObject.AvailableRange.getApiName(),
                SFAPreDefineObject.AvailableAccount.getApiName(),
                SFAPreDefineObject.AvailableProduct.getApiName(),
                SFAPreDefineObject.AvailablePriceBook.getApiName());
        if (GrayUtil.isPriceBookReform(user.getTenantId())) {
            apiNames.removeIf(v -> v.equals(SFAPreDefineObject.AvailablePriceBook.getApiName()));
        }
        stopWatch.lap("init name list");
        for (String apiName : apiNames) {
            initDescribeForTenant(user.getTenantId(), apiName);
        }
        stopWatch.lap("init describe");
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            RequestContextManager.setContext(context);
            initMultiLayoutForOneTenant(apiNames, user.getTenantId());
            initPrivilegeRelate(apiNames, user, null, null, null);
        });
        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.warn("initAvailableObject time out", e);
            SFABizLogUtil.sendAuditLog(SFABizLogUtil.Arg.builder()
                    .action("sfa_async_time_out")
                    .objectApiNames("AvailableRangeObj")
                    .message("EnterpriseInitService.initAvailableObject").build(), user);
        }
        stopWatch.lap("init layout and privilege");
        stopWatch.logSlow(2000);
    }

    public void initPriceToolFunc(User user) {
        List<String> actionCodeList = Lists.newArrayList(ObjectAction.PRICE_TOOL.getActionCode());
        try {
            serviceFacade.batchCreateFunc(user, SFAPreDefineObject.PriceBook.getApiName(), actionCodeList);
            serviceFacade.updateUserDefinedFuncAccess(user,
                    PrivilegeConstants.ADMIN_ROLE_CODE, SFAPreDefineObject.PriceBook.getApiName(), actionCodeList, Lists.newArrayList());
        } catch (Exception e) {
            log.error("initPriceToolFunc addRoleFunc error,apiName:PriceBookObj,ei:{}", user.getTenantId(), e);
        }
    }

    public void initPricePolicyObject(User user, RequestContext context, List<String> apiNames, Consumer<String> finishConsumer) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName());
        stopWatch.lap("init name list");
        for (String apiName : apiNames) {
            initDescribeForTenant(user.getTenantId(), apiName);
        }
        // OrderOccupy内置对象不需要刷权限布局
        apiNames.removeIf(v -> v.equals(SFAPreDefineObject.OrderOccupy.getApiName()));
        stopWatch.lap("init describe");
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            RequestContextManager.setContext(context);
            initMultiLayoutForOneTenant(apiNames, user.getTenantId());
            initPrivilegeRelate(apiNames, user, null, null, null);
            finishConsumer.accept("finish");
            //是否创建通用价格政策,0 创建
            if (generalPricePolicyAllow == 0) {
                try {
                    generalLayoutDescribeScenes.updateGeneralPricePolicy(user.getTenantId());
                } catch (MetadataServiceException e) {
                    log.error("开启价格政策,创建通用模式错误{},错误信息{}", user.getTenantId(), e.getMessage());
                }
            }
        }).run();
        stopWatch.lap("init layout and privilege");
//        initMultiLayoutForOneTenant(apiNames, user.getTenantId());
//        stopWatch.lap("init layout");
//        initPrivilegeRelate(apiNames, user, null, null, null);
//        stopWatch.lap("init privilege");
        stopWatch.logSlow(2000);
    }

    public String getLayRuleJsonFromFieldName(String fieldName) {
        ClassLoader classLoader = getClass().getClassLoader();
        String jsonStr;
        try {
            jsonStr = IOUtils.toString(classLoader.getResource("layoutrule/" + fieldName + ".json"), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("initProductFieldJSON file parse error");
            throw new MetadataValidateException(ErrorCode.FS_PAAS_MDS_FILE_PARSE_EXCEPTION, "initProductFieldJSON file parse error", e);
        }
        return jsonStr;
    }

    /**
     * 初始化 LayoutRule
     *
     * @param user
     * @param fieldName
     */
    public void initLayoutRule(User user, String fieldName) {
        try {
            LayoutRuleInfo ruleInfo = new LayoutRuleInfo();
            ruleInfo.fromJsonString(getLayRuleJsonFromFieldName(fieldName));
            layoutRuleService.create(user.getTenantId(), ruleInfo);
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(), e);
        }
    }


    public void initRebateOrCouponPlanObj(User user, RequestContext context, List<String> apiNames, String moduleCode) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName());
        String tenantId = user.getTenantId();
        if (CollectionUtils.isEmpty(apiNames)) {
            return;
        }
        stopWatch.lap("init name list");
        for (String apiName : apiNames) {
            initDescribeForTenant(user.getTenantId(), apiName, moduleCode);
        }
        stopWatch.lap("init describe");
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            RequestContextManager.setContext(context);
            initMultiLayoutForOneTenant(apiNames, tenantId, moduleCode);
            initPrivilegeRelate(apiNames, user, null, null, null);
            if (apiNames.contains(SFAPreDefineObject.AggregateRule.getApiName())) {
                initLayoutRule(user, "AggregateRuleObj_layout_rule_type__c");
            }
        }).run();
        stopWatch.lap("init layout and privilege");
        stopWatch.logSlow(2000);
    }

    /**
     * 优惠卷、返利、价格政策 有公用对象 剔除已经刷的企业
     *
     * @param tenantId 企业信息
     * @param apiNames 对象apiName
     */
    public void removeApiName(String tenantId, List<String> apiNames) {
        try {
            List<IObjectDescribe> describes = objectDescribeService.findDescribeListByApiNames(tenantId, apiNames);
            List<String> alreadyApiNames = describes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
            apiNames.removeAll(alreadyApiNames);
        } catch (MetadataServiceException e) {
            log.error("initRebateOrCouponPlan findDescribeListByApiNames error,tenantId {}", tenantId, e);
        }
    }
}
