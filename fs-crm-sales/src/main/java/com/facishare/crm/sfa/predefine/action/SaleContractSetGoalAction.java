package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.model.ContractProgressModel;
import com.facishare.crm.sfa.predefine.service.ContractProgressRuleService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2025-04-15
 */
@Slf4j
public class SaleContractSetGoalAction extends PreDefineAction<ContractProgressModel.RuleGoalModel, ContractProgressModel.Result> {
    private static final ContractProgressRuleService contractProgressRuleService = SpringUtil.getContext().getBean(ContractProgressRuleService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.SET_GOAL.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(ContractProgressModel.RuleGoalModel arg) {
        return Lists.newArrayList(arg.getContractId());
    }

    @Override
    protected ContractProgressModel.Result doAct(ContractProgressModel.RuleGoalModel arg) {
        if(CollectionUtils.isEmpty(dataList)) {
            return ContractProgressModel.Result.builder().msg("ok").success(true).data(Lists.newArrayList()).build();
        }
        return contractProgressRuleService.batchSaveGoalAndCheckPoint(actionContext.getUser(), arg, dataList.get(0).getName());
    }
}
