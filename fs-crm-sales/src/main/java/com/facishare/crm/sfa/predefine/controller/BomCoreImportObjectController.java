package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetImportObjectList;

/**
 *  fs-paas-metadata-dataloader-> noBatchCount=3000
 */

public class BomCoreImportObjectController extends StandardImportObjectController {

    @Override
    protected GetImportObjectList.ImportObjectDTO getImportObject(String objectCode) {
        GetImportObjectList.ImportObjectDTO importObject = super.getImportObject(objectCode);
        if (importObject != null) {
            importObject.setIsNoBatch(Boolean.TRUE);
        }
        return importObject;
    }
}
