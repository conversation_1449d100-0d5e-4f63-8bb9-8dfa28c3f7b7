package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/6/22 10:21 上午
 * @illustration
 */
public class SaleContractWebDetailController extends SFAWebDetailController {

    // TODO: 2020/6/23  do Nothing
    private List<String> removeActionList = Lists.newArrayList(
            ObjectAction.INVALID.getActionCode(),
            ObjectAction.LOCK.getActionCode(),
            ObjectAction.UNLOCK.getActionCode(),
            ObjectAction.CONFIRM.getActionCode(),
            ObjectAction.REJECT.getActionCode(),
            ObjectAction.UPDATE.getActionCode(),
            ObjectAction.CLONE.getActionCode(),
            ObjectAction.DELETE.getActionCode(),
            ObjectAction.CREATE.getActionCode(),
            ObjectAction.CHANGE_OWNER.getActionCode(),
            ObjectAction.CHANGE_PARTNER_OWNER.getActionCode(),
            ObjectAction.CHANGE_PARTNER.getActionCode(),
            ObjectAction.DELETE_PARTNER.getActionCode(),
            "button_salecontractobj2salesorderobj__c");
    private List<String> removeGrayActionList = Lists.newArrayList(
            ObjectAction.CONFIRM.getActionCode(),
            ObjectAction.REJECT.getActionCode(),
            ObjectAction.DELETE.getActionCode(),
            ObjectAction.CHANGE_PARTNER_OWNER.getActionCode(),
            ObjectAction.CHANGE_PARTNER.getActionCode(),
            ObjectAction.DELETE_PARTNER.getActionCode());
    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        SalesOrderUtil.handleRangeRebateRule(controllerContext.getTenantId(), Lists.newArrayList(newResult.getData().toObjectData()));
        ILayout layout = Optional.ofNullable(newResult).map(x->x.getLayout()).map(x->x.toLayout()).orElse(null);
        if (Objects.isNull(layout)) {
            return newResult ;
        }
        WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList("Recover"));
        if (RequestUtil.isMobileRequest() || RequestUtil.isH5Request()) {
            if (GrayUtil.isSaleContractMobileButton(controllerContext.getTenantId())) {
                WebDetailLayout.of(layout).removeButtonsByActionCode(removeGrayActionList);
            } else {
                WebDetailLayout.of(layout).removeButtonsByActionCode(removeActionList);
            }
        }
        return newResult;
    }
}
