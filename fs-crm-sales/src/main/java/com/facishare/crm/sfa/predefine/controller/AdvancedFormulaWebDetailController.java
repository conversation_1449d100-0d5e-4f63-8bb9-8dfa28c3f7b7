package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.model.QuoterModel;
import com.facishare.crm.sfa.predefine.service.AdvancedFormulaService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;

public class AdvancedFormulaWebDetailController extends SFAWebDetailController {
    private final AdvancedFormulaService advancedFormulaService = SpringUtil.getContext().getBean(AdvancedFormulaService.class);
    private static final List<String> MOBILE_FILTER_BUTTON = Lists.newArrayList(ObjectAction.CREATE.getActionCode(), ObjectAction.UPDATE.getActionCode(), ObjectAction.CLONE.getActionCode());
    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        ObjectDataDocument data = newResult.getData();
        Map<String, IObjectData> productMap = Maps.newHashMap();
        if (Objects.nonNull(data)) {
            String apiName = MapUtils.getString(data, QuoterModel.AdvancedFormulaModel.OBJECT_NAME, "");
            String fieldName = MapUtils.getString(data, QuoterModel.AdvancedFormulaModel.FIELD_NAME, "");
            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), apiName);
            Pattern pattern = Pattern.compile("\\$(.*?)\\$");
            if (Objects.nonNull(describe)) {
                data.put(QuoterModel.AdvancedFormulaModel.OBJECT_NAME.concat("__r"), describe.getDisplayName());
                Optional<String> field = Optional.of(describe).map(x -> x.getFieldDescribe(fieldName)).map(IFieldDescribe::getLabel);
                field.ifPresent(s -> data.put(QuoterModel.AdvancedFormulaModel.FIELD_NAME.concat("__r"), s));
                String expression = advancedFormulaService.translateFormula(describe, pattern, productMap, data.toObjectData(), controllerContext.getTenantId());
                data.put("expression", StringUtils.replace(expression, "$", ""));
                newResult.setData(data);
            }
        }
        if (newResult.getLayout() != null) {
            ILayout layout = new Layout(newResult.getLayout());
            LayoutExt layoutExt = LayoutExt.of(layout);
            List<IButton> buttons = layoutExt.getButtons();
            if ((RequestUtil.isMobileOrH5Request() || RequestUtil.isMobileDeviceRequest()) && CollectionUtils.notEmpty(buttons)) {
                buttons.removeIf(b -> MOBILE_FILTER_BUTTON.contains(b.getAction()));
                WebDetailLayout.of(layout).setButtons(buttons);
            }
        }
        return newResult;
    }

}
