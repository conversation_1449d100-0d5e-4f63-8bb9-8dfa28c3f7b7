package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.ContractProgressRuleService;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;

public class SaleContractBulkInvalidAction extends StandardBulkInvalidAction {

    private static final ContractProgressRuleService contractProgressRuleService = SpringUtil.getContext().getBean(ContractProgressRuleService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst =  super.after(arg, result);
        contractProgressRuleService.bulkInvalidRuleGoalByContract(actionContext.getUser(), objectDataList);
        return rst;
    }
}
