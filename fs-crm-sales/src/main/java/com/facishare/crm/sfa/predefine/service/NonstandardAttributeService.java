package com.facishare.crm.sfa.predefine.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.model.NonstandardAttributeEntity;
import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.eservice.base.utils.GsonUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NonstandardAttributeService {
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    /**
     * 入参：objectDataList 可能是产品列表、一页可能是价目表明细列表
     * @param objectDataList
     */
    public void fillNonstandardAttributeDefaultValue(String tenantId, List<ObjectDataDocument> objectDataList, boolean isPriceBookProduct) {
        //将产品上的非标默认值值重新赋值赋值到非标属性上
        if(CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        if (!bizConfigThreadLocalCacheService.isOpenNonstandardAttribute(tenantId)) {
            return;
        }
        for (ObjectDataDocument data : objectDataList){
            IObjectData objectData = data.toObjectData();
            String nonAttributeValuesStr = null;
            List<String> nonAttributeOriginalIds = null;
            if(isPriceBookProduct) {
                Map<String, Object> productData = objectData.get("product_id__ro", Map.class, Maps.newHashMap());
                ObjectDataDocument objectDataDocument = ObjectDataDocument.of(productData);
                nonAttributeValuesStr = objectDataDocument.toObjectData().get(ProductConstants.NON_ATTRIBUTE_VALUES, String.class);
                nonAttributeOriginalIds = objectDataDocument.toObjectData().get(ProductConstants.NONSTANDARD_ATTRIBUTE_IDS, List.class, Lists.newArrayList());
//                //最后移除掉product_id__o里的非标属性字段，构建product_id__ro时补充的字段
                productData.remove(ProductConstants.NON_ATTRIBUTE_VALUES);
                productData.remove(ProductConstants.NONSTANDARD_ATTRIBUTE_IDS);
            } else {
                nonAttributeValuesStr = objectData.get(ProductConstants.NON_ATTRIBUTE_VALUES, String.class);
                nonAttributeOriginalIds = objectData.get(ProductConstants.NONSTANDARD_ATTRIBUTE_IDS, List.class, Lists.newArrayList());
            }
            if (StringUtils.isBlank(nonAttributeValuesStr)) {
                continue;
            }
            //非标默认值支持数字及文本
            Map<String, Object> dbDefaultValues = Maps.newHashMap();
            //遇到错误时则做降级处理，不做非标默认值处理
            try {
                dbDefaultValues.putAll(JSON.parseObject(nonAttributeValuesStr, Map.class));
            }catch (Exception e) {
                log.error("解析非标属性值错误，格式有问题", e);
            }
            List<IObjectData> nonAttributeList = objectData.get("nonstandardAttribute", List.class, Lists.newArrayList());
            if(CollectionUtils.isNotEmpty(nonAttributeList) && MapUtils.isNotEmpty(dbDefaultValues)){
                nonAttributeList = ObjectDataExt.copyList(nonAttributeList);
                List<NonstandardAttributeEntity> nonDataList = nonAttributeList.stream().filter(Objects::nonNull)
                        .map(NonstandardAttributeEntity::newInstance)
                        .collect(Collectors.toList());
                nonDataList.stream().forEach(nonAttribute -> {
                    if(dbDefaultValues.get(nonAttribute.getId()) != null) {
                        nonAttribute.set("default_value", dbDefaultValues.get(nonAttribute.getId()));
                    }
                });
                objectData.set("nonstandardAttribute", sortNonstandardAttribute(nonAttributeOriginalIds, nonDataList));
            }
        }
    }

    /**
     * 对非标属性进行重新排序，确保返回前端的展示顺序与产品上设置的顺序一致
     * @param originalIds
     * @param nonstandardAttributeList
     * @return
     */
    private List<NonstandardAttributeEntity> sortNonstandardAttribute(List<String> originalIds, List<NonstandardAttributeEntity> nonstandardAttributeList) {
        if(CollectionUtils.isEmpty(originalIds) || CollectionUtils.isEmpty(nonstandardAttributeList)) {
            return nonstandardAttributeList;
        }
        List<NonstandardAttributeEntity> result = Lists.newArrayList();
        Map<String, NonstandardAttributeEntity> map = nonstandardAttributeList.stream().collect(Collectors.toMap(NonstandardAttributeEntity::getId, Function.identity(), (k1, k2) -> k1));
        for(String originalId : originalIds) {
            result.add(map.get(originalId));
        }
        return result;
    }
    public void fillNonstandardAttributeText(User user, List<IObjectData> objectDataList) {
        if (bizConfigThreadLocalCacheService.isOpenNonstandardAttribute(user.getTenantId())) {
            List<IObjectData> relateAttributeDataList = objectDataList.stream()
                    .filter(x -> !Objects.isNull(x.get("nonstandard_attribute_json"))
                            && StringUtils.isNotEmpty(x.get("nonstandard_attribute_json", String.class)))
                    .collect(Collectors.toList());
            Set<String> attributeIds = Sets.newHashSet();
            for (IObjectData objectData : relateAttributeDataList) {
                String value = objectData.get("nonstandard_attribute_json", String.class);
                Map<String, String> map = Strings.isEmpty(value) ? Maps.newHashMap() : GsonUtil.fromJson(value, Map.class);
                attributeIds.addAll(map.keySet());
            }
            if (CollectionUtils.isNotEmpty(attributeIds)) {
                List<INameCache> nameCaches = serviceFacade.findRecordName(ActionContextExt.of(user).getContext()
                        , Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME
                        , Lists.newArrayList(attributeIds));
                for (IObjectData objectData : relateAttributeDataList) {
                    List<String> snippetList = Lists.newArrayList();
                    String value = objectData.get("nonstandard_attribute_json", String.class);
                    Map<String, String> map = Strings.isEmpty(value) ? Maps.newHashMap() : GsonUtil.fromJson(value, Map.class);//前端传过来的非标属性值，可能是数字也可能是字符串
                    for (Map.Entry<String, String> item : map.entrySet()) {
                        Optional<INameCache> nameCache = nameCaches.stream()
                                .filter(x -> x.getId().equals(item.getKey()))
                                .findFirst();
                        if (nameCache.isPresent()) {//兼容前端传过来时，可能是 数字的情况
                            snippetList.add(nameCache.get().getName() + ":" + String.valueOf(item.getValue()));
                        }
                    }
                    objectData.set("nonstandard_attribute", Joiner.on(";").join(snippetList));
                }
            }
        }
    }

    public void attachNonstandardAttributeData(User user, List<IObjectData> objectDataList, boolean isFromPriceBookProduct) {
        if (bizConfigThreadLocalCacheService.isOpenNonstandardAttribute(user.getTenantId())) {
            List<IObjectData> productList = new ArrayList<>();
            String productFieldName = Strings.EMPTY;
            if (isFromPriceBookProduct) {
                productFieldName = PriceBookConstants.ProductField.PRODUCTID.getApiName();
                List<String> productIds = objectDataList.stream()
                        .map(x -> x.get("product_id", String.class))
                        .collect(Collectors.toList());
                productList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), productIds, Utils.PRODUCT_API_NAME);
            } else {
                productFieldName = IObjectData.ID;
                productList = objectDataList;
            }
            Map<String, List<IObjectData>> attributeMap = getNonstandardAttributeProductMap(user, productList);
            for (IObjectData objectData : objectDataList) {
                List<NonstandardAttributeEntity> dataList = attributeMap.getOrDefault(objectData.get(productFieldName, String.class),Lists.newArrayList()).stream()
                        .filter(Objects::nonNull)
                        .map(NonstandardAttributeEntity::newInstance)
                        .collect(Collectors.toList());
                objectData.set("nonstandardAttribute",dataList);
            }
        }
    }

    public Map<String, List<IObjectData>> getNonstandardAttributeDataByProductIds(User user, List<String> productIds,List<IObjectData> productList) {
        Map<String, List<IObjectData>> attributeMap = new HashMap<>();
        if (bizConfigThreadLocalCacheService.isOpenNonstandardAttribute(user.getTenantId())) {
            if (CollectionUtils.isEmpty(productList)&& CollectionUtils.isNotEmpty(productIds)) {
                productList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), productIds, Utils.PRODUCT_API_NAME);
            }
            if (CollectionUtils.isNotEmpty(productList)) {
                attributeMap = getNonstandardAttributeProductMap(user, productList);
            }
        }
        return attributeMap;
    }

    public Map<String, List<IObjectData>> getNonstandardAttributeDataByProductIds(User user, List<String> productIds) {
        return getNonstandardAttributeDataByProductIds(user,productIds,null);
    }

    public List<IObjectData> getProductListIdNonstandardAttributeIds(String tenantId, List<String> nonstandardAttributeIds) {
        List<IObjectData> result = new ArrayList<>();
        if (bizConfigThreadLocalCacheService.isOpenNonstandardAttribute(tenantId) && CollectionUtils.isNotEmpty(nonstandardAttributeIds)) {
            SearchTemplateQuery query = new SearchTemplateQuery();
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterHasAnyOf(filters, "nonstandard_attribute_ids", nonstandardAttributeIds);
            query.setFilters(filters);
            query.setOffset(0);
            query.setLimit(1);//最多50
            query.setPermissionType(0);
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            QueryResult<IObjectData> productList = serviceFacade.findBySearchQueryIgnoreAll(user, Utils.PRODUCT_API_NAME, query);
            result = productList.getData();
        }
        return result;
    }

    private Map<String,List<IObjectData>> getNonstandardAttributeProductMap(User user, List<IObjectData> objectDataList) {
        List<IObjectData> relatedObjectDataList = objectDataList.stream()
                .filter(x -> hasNonstandardAttribute(x))
                .collect(Collectors.toList());
        Map<String, List<IObjectData>> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(relatedObjectDataList)) {
            List<String> nonstandardAttributeIds = new ArrayList<>();
            for (IObjectData objectData : objectDataList) {
                if (hasNonstandardAttribute(objectData)) {
                    nonstandardAttributeIds.addAll(objectData.get("nonstandard_attribute_ids", ArrayList.class));
                }
            }
            if (CollectionUtils.isNotEmpty(nonstandardAttributeIds)) {
                List<IObjectData> nonstandardAttributeList =
                        serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId()
                                , nonstandardAttributeIds
                                , Utils.NONSTANDARD_ATTRIBUTE_OBJ_API_NAME);
                if (CollectionUtils.isNotEmpty(nonstandardAttributeList)) {
                    Set<String> attrGroupIds = nonstandardAttributeList.stream().map(x -> x.get(AttributeConstants.GroupField.ATTRIBUTE_GROUP_ID, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
                    Map<String, IObjectData> groupMap = getGroupMap(user, attrGroupIds);
                    for (IObjectData objectData : relatedObjectDataList) {
                        List<String> dataNonstandardIds = objectData.get("nonstandard_attribute_ids", ArrayList.class);
                        List<IObjectData> dataNonstandardList = new ArrayList<>();
                        for (IObjectData x : nonstandardAttributeList) {
                            x.setName(AttributeUtils.getI18nName(x));
                            if (dataNonstandardIds.contains(x.getId())) {
                                IObjectData groupObjectData = groupMap.get(x.get(AttributeConstants.GroupField.ATTRIBUTE_GROUP_ID, String.class));
                                if (groupObjectData != null) {
                                    x.set(AttributeConstants.GroupField.GROUP_ID,groupObjectData.getId());
                                    x.set(AttributeConstants.GroupField.GROUP_NAME, AttributeUtils.getI18nName(groupObjectData));
                                    x.set(AttributeConstants.GroupField.GROUP_NO,groupObjectData.get(AttributeConstants.GroupField.SERIAL_NO));
                                }else{
                                    x.set(AttributeConstants.GroupField.ATTRIBUTE_GROUP_ID,null);
                                }
                                dataNonstandardList.add(x);
                            }
                        }
                        result.put(objectData.getId(), dataNonstandardList);
                    }
                }
            }
        }
        return result;
    }

    private Map<String, IObjectData> getGroupMap(User user , Set<String> attrGroupIds) {
        Map<String, IObjectData> groupMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(attrGroupIds)) {
            List<IObjectData> dataList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(), Lists.newArrayList(attrGroupIds), Utils.ATTRIBUTE_GROUP_OBJ_API_NAME);
            if (CollectionUtils.isNotEmpty(dataList)) {
                groupMap = dataList.stream().collect(Collectors.toMap(DBRecord::getId, Function.identity(), (k1, k2) -> k1));
            }
        }
        return groupMap;
    }

    private boolean hasNonstandardAttribute(IObjectData objectData) {
        return objectData.get("nonstandard_attribute_ids") != null
                && Strings.isNotEmpty(objectData.get("nonstandard_attribute_ids", String.class));
    }
}
