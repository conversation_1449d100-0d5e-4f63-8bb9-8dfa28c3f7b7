package com.facishare.crm.sfa.utilities.proxy;

import com.facishare.crm.model.QueryOrderModeModel;
import com.facishare.crm.model.SalesOrderCloseModel;
import com.facishare.crm.model.ShopCategoryModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/9/11 16:10
 */
@RestResource(value = "FS_CRM_REST", desc = "订单拦截器", contentType = "application/json", codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec")
public interface SalesOrderBizProxy {

    @POST(value = "/SalesOrderInterceptor/service/AddBefore", desc = "订单新建，前置处理")
    SalesOrderInterceptorModel.AddBeforeResult addBefore(@Body SalesOrderAddBeforeModel.Arg arg, @HeaderMap Map<String, String> headers);


    @POST(value = "/SalesOrderInterceptor/service/AddAfter", desc = "订单新建，后置处理")
    SalesOrderInterceptorModel.AddAfterResult addAfter(@Body SalesOrderAddAfterModel.Arg arg, @HeaderMap Map<String, String> headers);



    @POST(value = "/SalesOrderInterceptor/service/AddFlowCompletedAfter", desc = "订单审批流完成回调,后置处理")
    SalesOrderInterceptorModel.AddFlowCompletedAfterResult addFlowCompletedAfter(@Body SalesOrderAddFlowCompletedAfterModel.Arg arg, @HeaderMap Map<String, String> headers);


    @POST(value = "/SalesOrderInterceptor/service/EditBefore", desc = "订单编辑，前置处理")
    SalesOrderInterceptorModel.EditBeforeResult editBefore(@Body SalesOrderEditBeforeModel.Arg arg, @HeaderMap Map<String, String> headers);


    @POST(value = "/SalesOrderInterceptor/service/EditAfter", desc = "订单编辑，后置处理")
    SalesOrderInterceptorModel.EditAfterResult editAfter(@Body SalesOrderEditAfterModel.Arg arg, @HeaderMap Map<String, String> headers);



    @POST(value = "/SalesOrderInterceptor/service/EditFlowCompletedAfter", desc = "订单编辑审批流完成，后置处理")
    SalesOrderInterceptorModel.EditFlowCompletedAfterResult editFlowCompletedAfter(@Body SalesOrderEditFlowCompletedAfterModel.Arg arg, @HeaderMap Map<String, String> headers);


    @POST(value = "/SalesOrderInterceptor/service/InvalidBefore", desc = "订单作废，前置处理")
    SalesOrderInterceptorModel.InvalidBeforeResult invalidBefore(@Body SalesOrderInvalidBeforeModel.Arg arg, @HeaderMap Map<String, String> headers);

    // TODO: 2019/10/14  从这开始待处理
    @POST(value = "/SalesOrderInterceptor/service/InvalidAfter", desc = "订单作废，后置处理")
    SalesOrderInterceptorModel.InvalidAfterResult invalidAfter(@Body SalesOrderInvalidAfterModel.Arg arg, @HeaderMap Map<String, String> headers);


    @POST(value = "/SalesOrderInterceptor/service/InvalidFlowCompletedAfter", desc = "订单作废审批流完成，后置处理")
    SalesOrderInterceptorModel.InvalidFlowCompletedAfterResult invalidFlowCompletedAfter(@Body SalesOrderInvalidFlowCompletedAfterModel.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/SalesOrderInterceptor/service/BulkInvalidBefore", desc = "订单批量作废，前置处理")
    SalesOrderInterceptorModel.BulkInvalidBeforeResult bulkInvalidBefore(@Body BulkInvalidBeforeModel.Arg arg, @HeaderMap Map<String, String> headers);


    @POST(value = "/SalesOrderInterceptor/service/BulkInvalidAfter", desc = "订单批量作废，后置处理")
    SalesOrderInterceptorModel.BulkInvalidAfterResult bulkInvalidAfter(@Body BulkInvalidAfterModel.Arg arg, @HeaderMap Map<String, String> headers);



    @POST(value = "/SalesOrderInterceptor/service/BulkRecoverBefore", desc = "订单批量恢复，前置处理")
    SalesOrderInterceptorModel.BulkRecoverBeforeResult bulkRecoverBefore(@Body BulkRecoverBeforeModel.Arg arg, @HeaderMap Map<String, String> headers);


    @POST(value = "/SalesOrderInterceptor/service/BulkRecoverAfter", desc = "订单批量恢复，后置处理")
    SalesOrderInterceptorModel.BulkRecoverAfterResult bulkRecoverAfter(@Body BulkRecoverAfterModel.Arg arg, @HeaderMap Map<String, String> headers);



    @POST(value = "/SalesOrderInterceptor/service/BulkAddBefore", desc = "订单批量新建，前置处理")
    SalesOrderInterceptorModel.BulkAddBeforeResult bulkAddBefore(@Body SalesOrderBulkAddBeforeModel.Arg arg, @HeaderMap Map<String, String> headers);



    @POST(value = "/SalesOrderInterceptor/service/BulkAddAfter", desc = "订单批量新建，后置处理")
    SalesOrderInterceptorModel.BulkAddAfterResult bulkAddAfter(@Body SalesOrderBulkAddAfterModel.Arg arg, @HeaderMap Map<String, String> headers);



    @POST(value = "/stock_order_close/service/switch_on_init", desc = "开关打开时，调用的库存业务初始化接口")
    SalesOrderCloseModel.CloseConfigResult switchOnInit(@Body SalesOrderCloseModel.CloseConfigArg arg, @HeaderMap Map<String, String> headers);



    @POST(value = "/stock_order_close/service/get_can_close_quantity", desc = "获取可发货数量")
    SalesOrderCloseModel.QueryStockQuantityResult getCanCloseQuantity(@Body SalesOrderCloseModel.QueryStockQuantityArg arg, @HeaderMap Map<String, String> headers);


    @POST(value = "/StockSalesOrderProductInterceptor/service/BulkAddProductBefore", desc = "订单产品新建导入，前置处理")
    SalesOrderProductBulkAddBeforeModel.Result bulkAddProductBefore(@Body SalesOrderProductBulkAddBeforeModel.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/StockSalesOrderProductInterceptor/service/BulkAddProductAfter", desc = "订单产品新建导入，后置处理")
    SalesOrderProductBulkAddAfterModel.Result bulkAddProductAfter(@Body SalesOrderProductBulkAddAfterModel.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/stock/service/findOrderRelatedMode", desc = "查询订货模式")
    QueryOrderModeModel.Result findOrderRelatedMode(@Body  QueryOrderModeModel.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/shop_category/service/query_sub_shop_category_list", desc = "查询子类目")
    ShopCategoryModel.SubCategoryResult querySubShopCategoryList(@Body  ShopCategoryModel.SubCategoryArg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/shop_category/service/handle_mall_category_list_filters", desc = "解析商城类目")
    ShopCategoryModel.HandleMallCategoryResult handleMallCategoryFilter(@Body  ShopCategoryModel.HandleMallCategoryArg arg, @HeaderMap Map<String, String> headers);
}
