package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

public class BomCoreListHeaderController extends StandardListHeaderController {
    private static final List<String> FILTER_BUTTON = Lists.newArrayList(ObjectAction.CREATE.getActionCode(),ObjectAction.UPDATE.getActionCode(),ObjectAction.CLONE.getActionCode(),ObjectAction.BATCH_IMPORT.getActionCode());


    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        if (!arg.includeLayout()||after.getLayout()==null) {
            return after;
        }
        ILayout layout = after.getLayout().toLayout();
        List<IButton> buttons = layout.getButtons();
        if ((RequestUtil.isMobileOrH5Request()||RequestUtil.isMobileDeviceRequest())&& CollectionUtils.notEmpty(buttons)) {
            buttons = buttons.stream().filter(o -> !FILTER_BUTTON.contains(o.getAction())).collect(Collectors.toList());
        }
        if(SFAConfigUtil.isSimpleBom(controllerContext.getTenantId())){
            buttons = buttons.stream().filter(o -> !FILTER_BUTTON.contains(o.getAction())).collect(Collectors.toList());
        }
        layout.setButtons(buttons);
        return after;
    }

}
