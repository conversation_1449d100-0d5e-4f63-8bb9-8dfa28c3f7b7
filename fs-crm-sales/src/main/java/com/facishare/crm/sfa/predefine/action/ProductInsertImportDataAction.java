package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.product.ProductStockValidator;
import com.facishare.crm.sfa.predefine.service.ProductCategoryBizService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuService;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils;
import com.facishare.crm.sfa.utilities.util.SpuProductImportUtil;
import com.facishare.crm.sfa.utilities.util.SpuSku18NKeyUtil;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryImportValidator;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.crm.sfa.utilities.validator.ProductValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.dto.ai.AIDto;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.constant.ProductConstants.MAX_SKU_LIMIT_OF_SAME_GROUP;

/**
 * <AUTHOR>
 * @date 2020/2/20 3:16 下午
 * @illustration
 */
@Slf4j
public class ProductInsertImportDataAction extends StandardInsertImportDataAction {
    private final SpuSkuService spuSkuService = SpringUtil.getContext().getBean(SpuSkuService.class);
    private BizConfigThreadLocalCacheService cacheConfigService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);
    private ProductCategoryV2Validator productCategoryV2Validator = SpringUtil.getContext().getBean(ProductCategoryV2Validator.class);
    private ProductCategoryImportValidator productCategoryImportValidator = SpringUtil.getContext().getBean(ProductCategoryImportValidator.class);
    private final ProductCategoryUtils productCategoryUtils = SpringUtil.getContext().getBean(ProductCategoryUtils.class);


    private List<IObjectData> spuDataList = Lists.newArrayList();
    private List<Map> skuDataList = Lists.newArrayList();

    private Map<String, Boolean> spuIdToIsSpec = Maps.newHashMap();
    private Map<String, String> spuIdToRecordType = Maps.newHashMap();
    private Map<String, String> spuIdToName = Maps.newHashMap();
    private Map<String, Map<String, Object>> syncSpuToSku = Maps.newHashMap();

    private Map<String, List<String>> validateSpecExist = Maps.newHashMap();
    private Map<String, Map<String, List<String>>> spuIdToSpecAndSpecValue = Maps.newHashMap();
    private Map<String, Map<String, List<String>>> spuRowNoToSpecAndSpecValue = Maps.newHashMap();
    private Map<String, String> spuIdToExistSpecValue = Maps.newHashMap();
    private Map<String, Integer> spuIdToSkuCounts = Maps.newHashMap();
    private Map<String, List<String>> spuIdSpecIdMap = Maps.newHashMap();
    private Set<String> fillCategoryData = Sets.newHashSet();

    /**
     * 是否走老逻辑
     * 默认走新逻辑 开了spu + 灰度 走新逻辑
     * 不符合条件走老逻辑
     */
    private boolean newLogic = true;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        List<IObjectData> collect = dataList.stream().map(ImportData::getData).collect(Collectors.toList());
        fillCategoryData = productCategoryBizService.handleCategoryMappingCategoryIdOfImport(actionContext.getUser(), collect, "ProductObj");
        newLogic = cacheConfigService.spuSkuNewLogic(actionContext.getTenantId());
        if (newLogic) {
            Set<String> spuIdSets = getSpuIdLis(dataList);
            spuDataList = findSpuData(spuIdSets);
            skuDataList = findSkuData(spuIdSets);
            findSpuSkuRelateData(actionContext.getTenantId(), spuIdSets);
            spuIdToSkuCounts = findSkuCountBySpuIds(spuIdSets);
            initSpuGlobalVariable();
            initSkuGlobalVariable();
            initSpecAndSpecValue();
            fillDefaultName();
        }
    }


    @Override
    protected void validateUniqueDataInExcel() {
        if (newLogic) {
            cleanName();
        }
        super.validateUniqueDataInExcel();
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        if (newLogic) {
            validateSpec(dataList);
            changeProductName(dataList);
        } else {
            doValidator(dataList);
        }
        List<ImportError> importErrors = productCategoryImportValidator.checkInsertRelatedCategoryIsLeafNode(actionContext.getUser(), dataList);
        allErrorList.addAll(importErrors);
        dataList.forEach(data -> {
            try {
                productCategoryV2Validator.checkCategoryParamOfProduct(actionContext.getTenantId(), objectDescribe, ObjectDataDocument.of(data.getData()));
            } catch (ValidateException ex) {
                allErrorList.addAll(productCategoryUtils.getErrorList(Lists.newArrayList(data), ex.getMessage()));
            }
        });
        ProductValidator.checkImportPeriodicProduct(actionContext.getUser(), dataList, allErrorList);
    }

    private void doValidator(List<ImportData> dataList) {
        ValidatorContext validatorContext = ValidatorContext.builder().action(ObjectAction.BATCH_IMPORT).user(actionContext.getUser()).describeApiName(objectDescribe.getApiName()).build();
        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            int rowNo = importData.getRowNo();
            try {
                validatorContext.setObjectData(importData.getData());
                BizValidator.build().withContext(validatorContext).with(new ProductStockValidator()).doValidate();
            } catch (ValidateException ex) {
                errorList.add(new ImportError(rowNo, ex.getMessage()));
            }
        }
        if (CollectionUtils.notEmpty(errorList)) {
            mergeErrorList(errorList);
        }
    }

    @Override
    protected void generateResult(Result result) {
        mergeErrorList();
        super.generateResult(result);
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        if (newLogic) {
            /**
             * 1. 处理同步信息
             * 2. 处理关联信息 spu_sku_spec_value_relate
             * 3. return
             */

            Map<String, Map<String, String>> nameToInfo = Maps.newHashMap();
            validList.forEach(data -> {
                String spuId = data.get(SpuProductImportUtil.FIELD_NAME_SPU_ID, String.class);
                Map<String, Object> syncInfo = syncSpuToSku.get(spuId);
                data.set(SpuProductImportUtil.FIELD_NAME_CATEGORY, syncInfo.get(SpuProductImportUtil.FIELD_NAME_CATEGORY));
                data.set(SpuProductImportUtil.PRODUCT_CATEGORY_ID, syncInfo.get(SpuProductImportUtil.PRODUCT_CATEGORY_ID));
                data.set(SpuProductImportUtil.SHOP_CATEGORY_ID, syncInfo.get(SpuProductImportUtil.SHOP_CATEGORY_ID));
                data.set(SpuProductImportUtil.FIELD_NAME_PRODUCT_LINE, syncInfo.get(SpuProductImportUtil.FIELD_NAME_PRODUCT_LINE));
                data.set(SpuProductImportUtil.FIELD_NAME_UNIT, syncInfo.get(SpuProductImportUtil.FIELD_NAME_UNIT));
                data.set(SpuProductImportUtil.FIELD_NAME_BATCH_SN, syncInfo.get(SpuProductImportUtil.FIELD_NAME_BATCH_SN));
                data.set(SpuProductImportUtil.FIELD_NAME_ON_SHELVES_TIME, System.currentTimeMillis());
                data.set(SpuProductImportUtil.FIELD_NAME_RECORD_TYPE, spuIdToRecordType.get(spuId));
                data.set(ProductConstants.STORE_ID, syncInfo.get(ProductConstants.STORE_ID));
                data.set(ProductConstants.MALL_CATEGORY_ID, syncInfo.get(ProductConstants.MALL_CATEGORY_ID));
                if (spuIdToIsSpec.get(spuId) != null && spuIdToIsSpec.get(spuId)) {
                    String specId = data.get("spec_id", String.class);
                    String specValue = data.get("spec_value_id", String.class);
                    Map<String, String> keyToValue = Maps.newHashMap();
                    keyToValue.put("spec_id", specId);
                    keyToValue.put("spec_value_id", specValue);
                    nameToInfo.put(data.getName(), keyToValue);
                } else {
                    String name = spuIdToName.get(spuId);
                    data.setName(name);
                    data.set(SpuProductImportUtil.FIELD_NAME_OWNER, syncInfo.get(SpuProductImportUtil.FIELD_NAME_OWNER));
                    ObjectDataExt dataExt = ObjectDataExt.of(data);
                    Optional<String> ownerId = dataExt.getOwnerId();
                    ownerId.ifPresent(a -> {
                        TeamMember teamMember = new TeamMember(a, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE);
                        dataExt.setTeamMembers(Lists.newArrayList(teamMember));
                    });
                }
            });

            List<IObjectData> skuObjectData = super.importData(validList);
            List<IObjectData> relateList = Lists.newArrayList();
            skuObjectData.forEach(data -> {
                String name = data.getName();
                String id = data.getId();
                String spuId = data.get("spu_id", String.class);
                if (spuIdToIsSpec.get(spuId) != null && spuIdToIsSpec.get(spuId)) {
                    Map<String, String> keyToValue = nameToInfo.get(name);
                    List<String> specIdList = Arrays.asList(keyToValue.get("spec_id").split(";"));
                    List<String> specValueIdList = Arrays.asList(keyToValue.get("spec_value_id").split(";"));
                    List<String> existSpecIdList = spuIdSpecIdMap.get(spuId);
                    if (CollectionUtils.empty(existSpecIdList)) {
                        spuIdSpecIdMap.put(spuId, specIdList);
                        existSpecIdList = specIdList;
                    }
                    insertSpuSkuValueRelate(relateList, spuId, id, actionContext.getTenantId(), specIdList, specValueIdList, existSpecIdList);
                }
            });
            if (CollectionUtils.notEmpty(relateList)) {
                serviceFacade.bulkSaveObjectData(relateList, actionContext.getUser());
            }
            return skuObjectData;
        } else {
            return super.importData(validList);
        }
    }

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        super.customAfterImport(actualList);
        productCategoryBizService.fillCategoryField(actionContext.getUser(), actualList, fillCategoryData, SFAPreDefineObject.Product.getApiName());
        spuSkuService.asynchronousCreatePriceBookProduct(actionContext.getUser(), actualList);
    }

    /* --------------- 新逻辑 ---------------------*/


    private void changeProductName(List<ImportData> dataList) {
        dataList.forEach(importData -> {
            IObjectData data = importData.getData();
            String name = data.getName();
            String spuId = data.get(SpuProductImportUtil.FIELD_NAME_SPU_ID, String.class);
            int rowNo = importData.getRowNo();

            List<String> existSpecList;
            if (spuIdToExistSpecValue.containsKey(spuId)) {
                existSpecList = SpuProductImportUtil.specAndSpecValue(spuIdToExistSpecValue.get(spuId)).getOrDefault("spec", Lists.newArrayList());
            } else {
                existSpecList = spuIdToSpecAndSpecValue.getOrDefault(spuId, Maps.newHashMap()).getOrDefault("spec", Lists.newArrayList());
            }

            Map<String, List<String>> spuRowNoToSpec = spuRowNoToSpecAndSpecValue.get(spuId + rowNo);
            if (spuRowNoToSpec != null && spuRowNoToSpec.containsKey("specValue")) {
                List<String> specValueList = spuRowNoToSpecAndSpecValue.get(spuId + rowNo).get("specValue");
                List<String> specList = spuRowNoToSpecAndSpecValue.get(spuId + rowNo).get("spec");
                if (Strings.isNullOrEmpty(name) || name.equals(spuIdToName.getOrDefault(spuId, ""))) {
                    data.setName(SpuProductImportUtil.appendSpecValueToName(specValueList, spuIdToName.get(spuId), specList, existSpecList));
                }
                resetProductSpec(data, specList, specValueList, existSpecList);
            }
        });
    }

    private void cleanName() {
        for (ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            String spuId = objectData.get(SpuProductImportUtil.FIELD_NAME_SPU_ID, String.class);
            if (!Strings.isNullOrEmpty(spuId)) {
                if (Objects.equals(spuIdToName.get(spuId), objectData.getName())) {
                    objectData.setName("");
                }
            }
        }
    }

    private void fillDefaultName() {
        for (ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            if (Strings.isNullOrEmpty(objectData.getName())) {
                String spuId = objectData.get(SpuProductImportUtil.FIELD_NAME_SPU_ID, String.class);
                if (!Strings.isNullOrEmpty(spuId)) {
                    objectData.setName(spuIdToName.get(spuId));
                }
            }
        }
    }

    private void resetProductSpec(IObjectData objectData, List<String> specList, List<String> specValueList, List<String> existSpecList) {
        StringBuilder builder = new StringBuilder();
        for (String spec : existSpecList) {
            int index = specList.indexOf(spec);
            if (index < 0) {
                continue;
            }
            builder.append(spec);
            builder.append(":");
            builder.append(specValueList.get(index));
            builder.append(";");
        }
        if (builder.length() > 0) {
            objectData.set("product_spec", builder.toString().substring(0, builder.length() - 1));
        }
    }

    private void insertSpuSkuValueRelate(List<IObjectData> relateList, String spuId, String skuId, String tenantId, List<String> specIdList, List<String> specValueIdList, List<String> existSpecIdList) {
        for (int i = 0; i < specIdList.size(); i++) {
            int index = existSpecIdList.indexOf(specIdList.get(i));
            if (index < 0) {
                continue;
            }
            Map<String, String> skuSpecMapping = SpuProductImportUtil.buildSpuSkuRelateData(spuId, specIdList.get(i), specValueIdList.get(i), String.valueOf(index), tenantId, skuId);
            relateList.add(new ObjectData(skuSpecMapping));
        }
    }

    private void validateSpec(List<ImportData> dataList) {
        dataList.forEach(importData -> {
            IObjectData data = importData.getData();
            String spuId = data.get(SpuProductImportUtil.FIELD_NAME_SPU_ID, String.class);
            String maxStock = data.get("max_stock", String.class);
            String safetyStock = data.get("safety_stock", String.class);
            String productSpec = data.get("product_spec", String.class);

            int rowNo = importData.getRowNo();
            String spuIdRowNo = spuId + rowNo;
            if (spuIdToIsSpec.get(spuId) != null && spuIdToIsSpec.get(spuId)) {
                validateSpecAndSpecValueMaxNumber(spuIdRowNo, rowNo);
                validateSpecOrderIsAgreement(spuId, spuIdRowNo, rowNo);
                validateSpecAndSpecValueData(productSpec, rowNo);
                validateProdSpecExist(spuId, productSpec, rowNo);
                validateSpecAndSpecValueNumber(spuIdRowNo, rowNo);
            } else {
                validateNoSpecValue(productSpec, rowNo);
            }
            validateSkuMaxCount(spuId, rowNo);
            ValidateSafeStock(maxStock, safetyStock, rowNo);
        });
        validateSpecExist(dataList, findSpecAndSpecValueDataList());
    }

    private void validateNoSpecValue(String productSpec, int rowNo) {
        if (StringUtils.isNotEmpty(productSpec)) {
            allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_NO_SPEC_NOT_WRITE)));
        }
    }

    private void validateSpecExist(List<ImportData> dataList, List<SpecAndSpecValueInfo> specAndSpecValueDataList) {
        dataList.forEach(importData -> {
            IObjectData data = importData.getData();
            String spuId = data.get(SpuProductImportUtil.FIELD_NAME_SPU_ID, String.class);
            String spuRowNo = spuId + importData.getRowNo();

            if (spuIdToIsSpec.get(spuId) == null || !spuIdToIsSpec.get(spuId)) {
                return;
            }

            Map<String, List<String>> specAndSpecValueMaps = spuRowNoToSpecAndSpecValue.get(spuRowNo);
            if (specAndSpecValueMaps == null) {
                return;
            }
            List<String> specList = specAndSpecValueMaps.get(SpuProductImportUtil.FIELD_NAME_SPEC_KEY);
            List<String> specValueList = specAndSpecValueMaps.get(SpuProductImportUtil.FIELD_NAME_SPEC_VALUE_KEY);

            StringBuilder specIdBuffer = new StringBuilder();
            StringBuilder specValueIdBuffer = new StringBuilder();

            if (CollectionUtils.empty(specList)) {
                allErrorList.add(new ImportError(importData.getRowNo(), I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_HAS_ERROR)));
                return;
            }

            for (int i = 0; i < specList.size(); i++) {
                AtomicBoolean specAndSpecValueExist = new AtomicBoolean(false);

                String spec = specList.get(i);
                String specValue = specValueList.get(i);
                String name = spec.concat(specValue);
                specAndSpecValueDataList.forEach(info -> {
                    if (info.getSpecNameAnfSpecValueName().equals(name)) {
                        String specId = info.getSpecId();
                        String specValueId = info.getSpecValueId();
                        specIdBuffer.append(specId).append(";");
                        specValueIdBuffer.append(specValueId).append(";");
                        specAndSpecValueExist.set(true);
                    }
                });
                if (specAndSpecValueExist.get()) {
                    data.set(SpuProductImportUtil.FIELD_NAME_SPEC_ID, specIdBuffer.toString());
                    data.set(SpuProductImportUtil.FIELD_NAME_SPEC_VALUE_ID, specValueIdBuffer.toString());
                } else {
                    allErrorList.add(new ImportError(importData.getRowNo(), spec + ":" + specValue + I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_GROUP_NOT_EXIST)));
                }
            }
        });
    }

    private void validateSpecAndSpecValueNumber(String spuIdRowNo, int rowNo) {
        Map<String, List<String>> keyToValue = spuRowNoToSpecAndSpecValue.get(spuIdRowNo);
        if (keyToValue == null || keyToValue.get("spec") == null || keyToValue.get("specValue") == null) {
            return;
        }
        int specSize = keyToValue.get("spec").size();
        int specValueSize = keyToValue.get("specValue").size();
        if (specSize != specValueSize) {
            allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_VALUE_COUNT_NOT_SAME)));
        }
    }

    private void validateProdSpecExist(String spuId, String productSpec, int rowNo) {
        if (isProductSpecExist(validateSpecExist.get(spuId), productSpec)) {
            allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_EXIST)));
        } else {
            if (Strings.isNullOrEmpty(productSpec)) {
                return;
            }
            validateSpecExist.computeIfAbsent(spuId, v -> Lists.newArrayList()).add(productSpec);
        }
    }

    private boolean isProductSpecExist(List<String> existProductSpecList, String productSpec) {
        if (Strings.isNullOrEmpty(productSpec) || CollectionUtils.empty(existProductSpecList)) {
            return false;
        }
        String[] currentSpecArray = productSpec.split(";");
        List<String> currentSpecList = Lists.newArrayList(currentSpecArray);
        int currentSize = currentSpecList.size();
        for (String existSpec : existProductSpecList) {
            String[] existSpecArray = existSpec.split(";");
            List<String> existSpecList = Lists.newArrayList(existSpecArray);
            int existSize = existSpecList.size();
            //校验规格是否相同，如：[颜色-红色；产地-北京]与[产地-北京；颜色-红色]
            //校验规格是否互为子集，如：[颜色-红色]与[产地-北京；颜色-红色]
            existSpecList.retainAll(currentSpecList);
            if (existSize == existSpecList.size() || currentSize == existSpecList.size()) {
                return true;
            }
        }
        return false;
    }

    private void validateSpecAndSpecValueData(String productSpec, int rowNo) {
        if (StringUtils.isEmpty(productSpec)) {
            allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_MUST_WRITE)));
        }
    }

    private void validateSkuMaxCount(String spuId, int rowNo) {
        Integer skuCount = spuIdToSkuCounts.get(spuId);

        if (spuIdToIsSpec.get(spuId) != null && !spuIdToIsSpec.get(spuId) && skuCount == 1) {
            allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_NO_SPEC_MUST_HAVE_ONE_LINE)));
        }

        skuCount = skuCount + 1;
        if (skuCount > MAX_SKU_LIMIT_OF_SAME_GROUP) {
            allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_NOT_GT_THREE_HUNDRED)));
        }

        spuIdToSkuCounts.put(spuId, skuCount);
    }

    private void validateSpecAndSpecValueMaxNumber(String spuIdRowNo, int rowNo) {
        Map<String, List<String>> specAndSpecValueMaps = spuRowNoToSpecAndSpecValue.get(spuIdRowNo);
        if (specAndSpecValueMaps == null) {
            return;
        }
        if (specAndSpecValueMaps.get("spec").size() > 15) {
            allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_NOT_GT_FIFTEEN)));
        }

        if (specAndSpecValueMaps.get("specValue").size() > 15) {
            allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_NOT_GT_FIFTEEN)));
        }
    }

    private void validateSpecOrderIsAgreement(String spuId, String spuIdRowNo, int rowNo) {
        Map<String, List<String>> specAndSpecValueMaps = spuIdToSpecAndSpecValue.get(spuId);
        if (specAndSpecValueMaps == null || specAndSpecValueMaps.get("spec") == null) {
            return;
        }
        Map<String, List<String>> specAndSpecValueRowNo = spuRowNoToSpecAndSpecValue.get(spuIdRowNo);
        if (specAndSpecValueRowNo == null || specAndSpecValueMaps.get("spec") == null) {
            return;
        }
        List<String> existSpecList = spuIdToSpecAndSpecValue.get(spuId).getOrDefault("spec", Lists.newArrayList());

        String existSpec = spuIdToExistSpecValue.get(spuId);
        if (StringUtils.isNotEmpty(existSpec)) {
            existSpecList = SpuProductImportUtil.specAndSpecValue(existSpec).getOrDefault("spec", Lists.newArrayList());
        }
        String firstSpec = Joiner.on(",").join(existSpecList);

        List<String> currentSpecList = spuRowNoToSpecAndSpecValue.get(spuIdRowNo).getOrDefault("spec", Lists.newArrayList());
        if (!isSpecMatched(existSpecList, currentSpecList)) {
            allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_ORDER_NOT_SAME, firstSpec)));
        }
    }

    private boolean isSpecMatched(List<String> existSpecList, List<String> currentSpecList) {
        for (String spec : currentSpecList) {
            if (!existSpecList.contains(spec)) {
                return false;
            }
        }
        return true;
    }

    private void ValidateSafeStock(String maxStock, String safetyStock, int rowNo) {
        if (StringUtils.isEmpty(maxStock)) {
            return;
        }

        try {
            BigDecimal maxStockBigDml = new BigDecimal(maxStock);
            BigDecimal safetyStockBigDml = BigDecimal.ZERO;

            if (StringUtils.isNotEmpty(safetyStock)) {
                safetyStockBigDml = new BigDecimal(safetyStock);
            }

            if (maxStockBigDml.compareTo(safetyStockBigDml) < 1) {
                allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_MAX_STOCK_MUST_GT_SAFE_STOCK)));
            }
        } catch (Exception e) {
            log.warn("Product Insert Import data tenantId {} maxStock {}, safetyStock {}", actionContext.getTenantId(), maxStock, safetyStock, e);
        }
    }

    /**
     * --------------- 初始化数据  ----------------------
     */
    private void initSpecAndSpecValue() {
        dataList.forEach((importData) -> {
            IObjectData data = importData.getData();
            String spuId = data.get(SpuProductImportUtil.FIELD_NAME_SPU_ID, String.class);
            int rowNo = importData.getRowNo();
            String spuRowNo = spuId + rowNo;


            if (spuIdToIsSpec.get(spuId) != null && spuIdToIsSpec.get(spuId)) {
                String productSpec = data.get(SpuProductImportUtil.FIELD_NAME_PRODUCT_SPEC, String.class);
                if (StringUtils.isEmpty(productSpec)) {
//                    allErrorList.add(new ImportError(rowNo, "有规格商品，产品规格属性必须填写。"));
                    allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_MUST_WRITE)));
                    return;
                }
                productSpec = SpuProductImportUtil.replaceProductSpec(productSpec);
                if (!SpuProductImportUtil.validatePatternSpec(productSpec)) {
//                    allErrorList.add(new ImportError(rowNo, "规格规格值填写不合法，请检查规格或规格值内是否包含冒号和分号。"));
                    allErrorList.add(new ImportError(rowNo, I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_VALUE_NOT_RULE)));
                    return;
                }
                data.set("product_spec", productSpec.substring(0, productSpec.length() - 1));

                Map<String, List<String>> specAndSpecValue = SpuProductImportUtil.specAndSpecValue(productSpec);
                if (!spuIdToSpecAndSpecValue.containsKey(spuId)) {
                    spuIdToSpecAndSpecValue.put(spuId, specAndSpecValue);
                }
                spuRowNoToSpecAndSpecValue.put(spuRowNo, specAndSpecValue);
            }
        });
    }

    private void initSkuGlobalVariable() {
        skuDataList.forEach(dataMap -> {
            String spuId = dataMap.get("spu_id") == null ? "" : dataMap.get("spu_id").toString();
            String productSpec = dataMap.get("product_spec") == null ? "" : dataMap.get("product_spec").toString();
            if (validateSpecExist.containsKey(spuId)) {
                List<String> oldList = validateSpecExist.get(spuId);
                oldList.add(productSpec);
                validateSpecExist.put(spuId, oldList);
            } else {
                validateSpecExist.put(spuId, Lists.newArrayList(productSpec));
            }
        });
    }

    private void initSpuGlobalVariable() {
        spuDataList.forEach((data) -> {
            String spuId = data.getId();
            Boolean isSpec = data.get(SpuProductImportUtil.FIELD_NAME_IS_SPEC, Boolean.class, Boolean.FALSE);
            String productCategoryId = data.get(SpuProductImportUtil.PRODUCT_CATEGORY_ID, String.class);
            Object shopCategoryId = data.get(SpuProductImportUtil.SHOP_CATEGORY_ID);
            String category = data.get(SpuProductImportUtil.FIELD_NAME_CATEGORY, String.class);
            String productLine = data.get(SpuProductImportUtil.FIELD_NAME_PRODUCT_LINE, String.class);
            String unit = data.get(SpuProductImportUtil.FIELD_NAME_UNIT, String.class);
            String batchSn = data.get(SpuProductImportUtil.FIELD_NAME_BATCH_SN, String.class);
            String recordType = data.get(SpuProductImportUtil.FIELD_NAME_RECORD_TYPE, String.class);
            Object storeId = data.get(ProductConstants.STORE_ID);
            Object mallCategoryId = data.get(ProductConstants.MALL_CATEGORY_ID);
            List<String> owner = data.getOwner();
            String name = data.getName();

            spuIdToIsSpec.put(spuId, isSpec);
            spuIdToRecordType.put(spuId, recordType);
            spuIdToName.put(spuId, name);

            Map<String, Object> syncInfo = Maps.newHashMap();
            syncInfo.put(SpuProductImportUtil.FIELD_NAME_CATEGORY, category);
            syncInfo.put(SpuProductImportUtil.PRODUCT_CATEGORY_ID, productCategoryId);
            syncInfo.put(SpuProductImportUtil.SHOP_CATEGORY_ID, shopCategoryId);
            syncInfo.put(SpuProductImportUtil.FIELD_NAME_PRODUCT_LINE, productLine);
            syncInfo.put(SpuProductImportUtil.FIELD_NAME_UNIT, unit);
            syncInfo.put(SpuProductImportUtil.FIELD_NAME_BATCH_SN, batchSn);
            syncInfo.put(SpuProductImportUtil.FIELD_NAME_OWNER, owner);
            syncInfo.put(ProductConstants.STORE_ID, storeId);
            syncInfo.put(ProductConstants.MALL_CATEGORY_ID, mallCategoryId);
            syncSpuToSku.put(spuId, syncInfo);
        });
    }

    /**
     * --------------- 查数据  ----------------------
     */
    private List<SpecAndSpecValueInfo> findSpecAndSpecValueDataList() {
        List<String> specList = Lists.newArrayList();
        List<String> specValueList = Lists.newArrayList();
        List<String> sqlWhereList = Lists.newArrayList();
        String tenantId = actionContext.getTenantId();

        spuRowNoToSpecAndSpecValue.forEach((spuId, specAnfSpecValue) -> {
            specList.addAll(specAnfSpecValue.get("spec"));
            specValueList.addAll(specAnfSpecValue.get("specValue"));
            sqlWhereList.addAll(specAnfSpecValue.get("sqlWhere"));
        });

        if (CollectionUtils.empty(specList) || CollectionUtils.empty(specValueList) || CollectionUtils.empty(sqlWhereList)) {
            return Lists.newArrayList();
        }

        String querySql = SpuProductImportUtil.findSpecAndSpecValueDataList(specList, specValueList, sqlWhereList, tenantId);
        try {
            List<SpecAndSpecValueInfo> result = Lists.newArrayList();
            List<Map> specAndSpecValueDataList = objectDataService.findBySql(actionContext.getTenantId(), querySql);
            if (CollectionUtils.empty(specAndSpecValueDataList)) {
                return result;
            }
            specAndSpecValueDataList.forEach(map -> {
                String specId = map.get("spec_id").toString();
                String specName = map.get("spec_name").toString();
                String specValueId = map.get("spec_value_id").toString();
                String specValueName = map.get("spec_value_name").toString();
                SpecAndSpecValueInfo info = new SpecAndSpecValueInfo(specId, specValueId, specName.concat(specValueName));
                result.add(info);
            });
            return result;
        } catch (MetadataServiceException e) {
            log.error("findSpecAndSpecValueDataList error tenantId->{},", actionContext.getTenantId(), e);
            throw new ValidateException(I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_FIND_FAIL));
        }
    }

    private Set<String> getSpuIdLis(List<ImportData> dataList) {
        return dataList.stream().map(data -> data.getData().get(SpuProductImportUtil.FIELD_NAME_SPU_ID, String.class)).collect(Collectors.toSet());
    }

    private List<IObjectData> findSpuData(Set<String> spuIdSets) {
        return serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(spuIdSets), Utils.SPU_API_NAME);
    }

    private List<Map> findSkuData(Set<String> spuIdSets) {
        String tenantId = actionContext.getTenantId();
        try {
            return objectDataService.findBySql(tenantId, SpuProductImportUtil.findProductSpecBySpuSQL(tenantId, Lists.newArrayList(spuIdSets)));
        } catch (MetadataServiceException e) {
            log.error("findSkuData error tenantId->{},", actionContext.getTenantId(), e);
            throw new ValidateException(I18N.text(SpuSku18NKeyUtil.SPU_SKU_FIND_FAIL));
        }
    }

    private void findSpuSkuRelateData(String tenantId, Set<String> spuIdSets) {
        try {
            List<Map> allInfo = objectDataService.findBySql(tenantId, SpuProductImportUtil.findSpuSkuSpecSpecValueJoinSQL(tenantId, SpuProductImportUtil.translationValue(spuIdSets)));
            if (CollectionUtils.notEmpty(allInfo)) {
                AtomicReference<String> atomicSpuId = new AtomicReference<>("");
                allInfo.forEach(map -> {
                    String spuId = map.get("spu_id").toString();
                    String specName = map.get("spec_name").toString();
                    String specValueName = map.get("spec_value_name").toString();
                    String specId = map.get("spec_id").toString();
                    String lastSpuId = atomicSpuId.get();

                    if (!Objects.equals(lastSpuId, spuId)) {
                        String appendStr = specName.concat(":").concat(specValueName).concat(";");
                        spuIdToExistSpecValue.put(spuId, appendStr);
                        spuIdSpecIdMap.put(spuId, Lists.newArrayList(specId));
                    }

                    if (atomicSpuId.get().equals(spuId)) {
                        String oldAppendStr = spuIdToExistSpecValue.get(spuId);
                        String newAppendStr = oldAppendStr.concat(specName).concat(":").concat(specValueName).concat(";");
                        spuIdToExistSpecValue.put(spuId, newAppendStr);
                        spuIdSpecIdMap.get(spuId).add(specId);
                    }
                    atomicSpuId.set(spuId);
                });
            }
        } catch (MetadataServiceException e) {
            log.error("findSpuSkuRelateData error tenantId->{},", actionContext.getTenantId(), e);
            throw new ValidateException(I18N.text(SpuSku18NKeyUtil.SPU_SKU_SPEC_RELATE_FIND_FAIL));
        }

    }

    private Map<String, Integer> findSkuCountBySpuIds(Set<String> spuIdSets) {
        Map<String, Integer> result = Maps.newHashMap();
        String tenantId = actionContext.getTenantId();
        try {
            List<Map> resultList = objectDataService.findBySql(tenantId, SpuProductImportUtil.countSkuNumberBySpuIds(tenantId, spuIdSets));
            if (CollectionUtils.notEmpty(resultList)) {
                resultList.forEach((map) -> {
                    String spuId = map.get("spu_id").toString();
                    Integer count = Integer.valueOf(map.get("count").toString());
                    result.put(spuId, count);
                });
            }

            // 可能查询不到spu，导致后续逻辑出现npe，这里直接赋值为0
            spuIdSets.forEach(spuId -> {
                if (!result.containsKey(spuId)) {
                    result.put(spuId, 0);
                }
            });
            return result;
        } catch (MetadataServiceException e) {
            log.error("findSkuCountBySpuIds error tenantId->{},", actionContext.getTenantId(), e);
            throw new ValidateException(I18N.text(SpuSku18NKeyUtil.SPU_SKU_FIND_SKU_COUNT_FAIL));
        }
    }

    private void mergeErrorList() {
        List<ImportError> errorList = SpuProductImportUtil.mergeErrorList(allErrorList);
        allErrorList = errorList;
    }


    private static class SpecAndSpecValueInfo {
        String specId;
        String specValueId;
        String specNameAnfSpecValueName;

        public SpecAndSpecValueInfo(String specId, String specValueId, String specNameAnfSpecValueName) {
            this.specId = specId;
            this.specValueId = specValueId;
            this.specNameAnfSpecValueName = specNameAnfSpecValueName;
        }

        public String getSpecId() {
            return specId;
        }

        public String getSpecValueId() {
            return specValueId;
        }

        public String getSpecNameAnfSpecValueName() {
            return specNameAnfSpecValueName;
        }
    }
}
