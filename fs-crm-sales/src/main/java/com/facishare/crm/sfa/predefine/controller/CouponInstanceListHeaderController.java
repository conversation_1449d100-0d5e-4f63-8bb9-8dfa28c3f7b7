package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;

public class CouponInstanceListHeaderController extends StandardListHeaderController {
    private static final List<String> TO_REMOVE_BUTTON_COUPON_INSTANCE = Lists.newArrayList(ObjectAction.INTELLIGENTFORM.getActionCode());

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        removeButton(result);
        return result;
    }

    public static void removeButton(StandardListHeaderController.Result result) {
        if (null == result || null == result.getLayout()) {
            return;
        }
        ILayout layout = new Layout(result.getLayout());
        List<IButton> buttonList = layout.getButtons();
        if (CollectionUtils.empty(buttonList)) {
            return;
        }
        buttonList.removeIf(button -> TO_REMOVE_BUTTON_COUPON_INSTANCE.contains(button.getAction()));
        //移动端移除新建按钮
        if (!RequestUtil.isWebRequest() || RequestUtil.isMobileDeviceRequest()) {
            buttonList.removeIf(button -> ObjectAction.CREATE.getActionCode().contains(button.getAction()));
        }
        layout.setButtons(buttonList);
    }

}
