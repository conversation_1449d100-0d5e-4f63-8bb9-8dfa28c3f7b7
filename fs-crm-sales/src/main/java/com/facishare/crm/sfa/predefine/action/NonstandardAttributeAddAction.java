package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreServiceImpl;
import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class NonstandardAttributeAddAction extends StandardAddAction {

    private static final AttributeCoreServiceImpl attributeCoreServiceImpl = SpringUtil.getContext().getBean(AttributeCoreServiceImpl.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String type = objectData.get("type", String.class);
        String defaultValue = objectData.get("default_value", String.class);
        if (StringUtils.isNotBlank(defaultValue) && Objects.equals(type, "1")) {
            boolean bool = AttributeUtils.checkNumber(defaultValue);
            if(!bool){
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NON_ATTR_DEFAULT_VALUE_WARN));
            }
        }
    }
}
