package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.utilities.util.imports.ImportSoUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

public class QuoteLinesUpdateImportVerifyAction extends StandardUpdateImportVerifyAction {
    private static final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitService.class);

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fields = super.getValidImportFields();
        ImportSoUtil.removeFields(fields, ImportSoUtil.DETAIL_REMOVE_FIELD_BASE);
        if (multiUnitService.isOpenMultiUnit(actionContext.getTenantId())) {
            ImportSoUtil.removeFields(fields, ImportSoUtil.SALES_ORDER_PRODUCT_FILTER_FIELDS);
        }
        ImportSoUtil.removeFields(fields, ImportSoUtil.NON_STANDARD_PRODUCT_FIELD);
        return fields;
    }
}
