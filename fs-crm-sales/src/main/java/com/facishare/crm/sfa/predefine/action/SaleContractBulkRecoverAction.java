package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.ContractProgressRuleService;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.metadata.util.SpringUtil;

public class SaleContractBulkRecoverAction extends StandardBulkRecoverAction {
    private static final ContractProgressRuleService contractProgressRuleService = SpringUtil.getContext().getBean(ContractProgressRuleService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        contractProgressRuleService.bulkRecoverRuleGoalByContract(actionContext.getUser(), dataList);
        return rst;
    }
}
