package com.facishare.crm.sfa.predefine.button;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SaleContractSpecialButtonProvider extends AbstractSfaSpecialButtonProvider {
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Override
    public String getApiName() {
        return Utils.SALE_CONTRACT_API_NAME;
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = super.getSpecialButtons();
        String tenantId = RequestContextManager.getContext().getTenantId();
        if(StringUtils.isNotBlank(tenantId) && bizConfigThreadLocalCacheService.isOpenContractProgress(tenantId)) {
            buttons.add(ButtonUtils.buildButton(ObjectAction.SET_GOAL));
            buttons.add(ButtonUtils.buildButton(ObjectAction.VIEW_GOAL));
        }
        return buttons;
    }



}

