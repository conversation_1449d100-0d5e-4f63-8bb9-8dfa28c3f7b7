package com.facishare.crm.sfa.utilities.util;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-09
 * ============================================================
 */
@Service
@Slf4j
public class ListButtonManager {

    public void removeListButtonIf(BaseListController.Result after, Predicate<IObjectData> filter, Set<String> removeButtons) {
        boolean verifyFailed = !verifyButtonParam(after, filter, removeButtons);
        if (verifyFailed) {
            return;
        }
        List<String> needRemoveButtonDataIds = after.getDataList().stream()
                .map(ObjectDataDocument::toObjectData)
                .filter(filter)
                .map(DBRecord::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needRemoveButtonDataIds)) {
            return;
        }
        Map<String, List<String>> buttonMap = after.getButtonInfo().getButtonMap();
        buttonMap.forEach((id, buttons) -> {
            if (needRemoveButtonDataIds.contains(id)) {
                buttons.removeIf(removeButtons::contains);
            }
        });
    }

    public void removeListButton(BaseListController.Result after, Set<String> removeButtons) {
        removeListButtonIf(after, anyData -> true, removeButtons);
    }

    private boolean verifyButtonParam(BaseListController.Result after, Predicate<IObjectData> filter, Set<String> removeButtons) {
        if (after == null || CollectionUtils.isEmpty(removeButtons) || filter == null) {
            return false;
        }
        if (CollectionUtils.isEmpty(removeButtons)) {
            return false;
        }
        if (CollectionUtils.isEmpty(after.getDataList())) {
            return false;
        }
        BaseListController.ButtonInfo buttonInfo = after.getButtonInfo();
        if (buttonInfo == null) {
            return false;
        }
        Map<String, List<String>> buttonMap = buttonInfo.getButtonMap();
        return buttonMap != null && !buttonMap.isEmpty();
    }
}
