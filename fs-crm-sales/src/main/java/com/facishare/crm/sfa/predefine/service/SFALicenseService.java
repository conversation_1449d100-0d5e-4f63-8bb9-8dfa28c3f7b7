package com.facishare.crm.sfa.predefine.service;

import com.alibaba.excel.util.CollectionUtils;
import com.facishare.crm.sfa.predefine.service.model.SFALicenseModel;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.exception.LicenseException;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.Result.ParaInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.arg.QueryModuleParaArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.exception.PaasMessage;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/*
license相关wiki page: http://wiki.firstshare.cn/pages/viewpage.action?pageId=90509404
 */
@ServiceModule("sfa_license_service")
@Component
@Slf4j
public class SFALicenseService {
    @Autowired
    private LicenseClient licenseClient;

    @ServiceMethod("check_license_exist")
    public SFALicenseModel.CheckModuleLicenseExistResult checkModuleLicenseExist(ServiceContext context, SFALicenseModel.CheckModuleLicenseExistArg arg) {
        boolean hasLicense = checkModuleLicenseExist(context.getTenantId(), arg.getPackageName());
        return SFALicenseModel.CheckModuleLicenseExistResult.builder().hasLicense(hasLicense).build();
    }

    public boolean checkModuleLicenseExist(String tenantId, String packageName) {
        QueryModuleArg queryModuleArg = new QueryModuleArg();
        LicenseContext licenseContext = getLicenseContext(tenantId);
        queryModuleArg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(queryModuleArg);
        if (result == null) {
            return false;
        }
        List<ModuleInfoPojo> modules = result.getResult();
        return (modules != null && modules.stream().anyMatch(module -> Objects.equals(module.getModuleCode(), packageName)));
    }

    @ServiceMethod("check_modules_license_exist")
    public SFALicenseModel.CheckModulesLicenseExistResult checkModuleLicenseExists(ServiceContext context, SFALicenseModel.CheckModulesLicenseExistArg arg) {
        if (CollectionUtils.isEmpty(arg.getPackageNameList())) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        QueryModuleArg queryModuleArg = new QueryModuleArg();
        LicenseContext licenseContext = getLicenseContext(context.getTenantId());
        queryModuleArg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(queryModuleArg);
        SFALicenseModel.CheckModulesLicenseExistResult checkModulesLicenseExistResult = SFALicenseModel.CheckModulesLicenseExistResult.builder().build();
        Map<String, Boolean> licenseMap = Maps.newHashMap();
        arg.getPackageNameList().forEach(x -> licenseMap.put(x, false));
        checkModulesLicenseExistResult.setLicenseResult(licenseMap);
        if (result == null) {
            return checkModulesLicenseExistResult;
        }
        List<ModuleInfoPojo> modules = result.getResult();
        if (modules != null) {
            arg.getPackageNameList().forEach(moduleCode -> {
                if (modules.stream().anyMatch(module -> Objects.equals(module.getModuleCode(), moduleCode))) {
                    licenseMap.put(moduleCode, true);
                }
            });
        }
        return checkModulesLicenseExistResult;
    }

    @ServiceMethod("get_crm_product_version")
    public LicenseVersionResult getCRMProductVersion(ServiceContext context) {
        return getCRMProductVersion(context.getTenantId());
    }

    @NotNull
    private LicenseContext getLicenseContext(String tenantId) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(tenantId);
        licenseContext.setUserId(User.SUPPER_ADMIN_USER_ID);
        return licenseContext;
    }

    private LicenseVersionResult getCRMProductVersion(String tenantId) {
        QueryProductArg queryProductArg = new QueryProductArg();
        LicenseContext licenseContext = getLicenseContext(tenantId);
        queryProductArg.setLicenseContext(licenseContext);

        return licenseClient.queryProductVersion(queryProductArg);
    }

    public int getQuotaByModule(String tenantId, String paraKey) {
        LicenseContext context = getLicenseContext(tenantId);
        QueryModuleParaArg arg = new QueryModuleParaArg();
        arg.setContext(context);
        arg.setParaKeys(Sets.newHashSet(paraKey));
        ParaInfoResult paraInfoResult = licenseClient.queryModulePara(arg);

        if (Objects.isNull(paraInfoResult)) {
            throw new APPException("license service error");
        }

        if (!Objects.equals(paraInfoResult.getErrCode(), PaasMessage.SUCCESS.getCode())) {
            log.error("queryModulePara error,arg:{},result:{}", arg, paraInfoResult);
            throw new LicenseException(paraInfoResult.getErrMessage());
        }

        return CollectionUtils.isEmpty(paraInfoResult.getResult()) ? 0 :
                Integer.parseInt(paraInfoResult.getResult().get(0).getParaValue());
    }

}
