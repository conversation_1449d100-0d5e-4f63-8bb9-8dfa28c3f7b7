package com.facishare.crm.sfa.predefine.button;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by Sundy on 2024/11/13 18:43
 */
@Component
public class PartnerAgreementDetailSpecialButtonProvider extends AbstractSfaSpecialButtonProvider {
    @Override
    public String getApiName() {
        return SFAPreDefineObject.PartnerAgreementDetail.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> specialButtons = super.getSpecialButtons();
        boolean notExistsInitiateRenewalButton = specialButtons.stream().noneMatch(button -> button.getAction().equals(ObjectAction.INITIATE_RENEWAL.getActionCode()));
        if (notExistsInitiateRenewalButton) {
            specialButtons.add(ButtonUtils.buildButton(ObjectAction.INITIATE_RENEWAL));
        }
        boolean notExistsRenewButton = specialButtons.stream().noneMatch(button -> button.getAction().equals(ObjectAction.RENEW.getActionCode()));
        if (notExistsRenewButton) {
            specialButtons.add(ButtonUtils.buildButton(ObjectAction.RENEW));
        }
        return specialButtons;
    }
}
