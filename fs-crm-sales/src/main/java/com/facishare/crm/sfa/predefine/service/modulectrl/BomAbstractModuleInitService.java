package com.facishare.crm.sfa.predefine.service.modulectrl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.modulectrl.util.InitUtil;
import com.facishare.crm.sfa.predefine.service.task.TaskService;
import com.facishare.crm.sfa.utilities.enums.EnumUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public abstract class BomAbstractModuleInitService extends AbstractModuleInitService {
    public static final String SALES_ORDER_PRODUCT_OBJ = "SalesOrderProductObj";
    public static final String QUOTE_LINES_OBJ = "QuoteLinesObj";
    public static final String RETURNED_GOODS_INVOICE_PRODUCT_OBJ = "ReturnedGoodsInvoiceProductObj";
    public static final String PRODUCT_OBJ = "ProductObj";
    public static final String PRICE_BOOK_PRODUCT_OBJ = "PriceBookProductObj";
    public static final String ORDER_PRODUCT_ID = "order_product_id";
    public static final String FIELD_JSON_PROD_IS_PACKAGE = "{\"type\":\"true_or_false\",\"define_type\":\"package\",\"is_active\":true,\"is_index\":true,\"is_need_convert\":false,\"is_required\":true,\"is_unique\":false,\"description\":\"产品组合\",\"api_name\":\"is_package\",\"label\":\"产品组合\",\"default_value\":false,\"help_text\":\"被设置为子产品的产品，暂不支持设置为产品组合\",\"status\":\"released\",\"options\":[{\"label\":\"是\",\"value\":true},{\"label\":\"否\",\"value\":false}]}";
    public static final String FIELD_JSON_PROD_IS_SALEABLE = "{\"type\":\"true_or_false\",\"define_type\":\"package\",\"is_active\":true,\"is_index\":true,\"is_need_convert\":false,\"is_required\":true,\"is_unique\":false,\"description\":\"可独立销售\",\"api_name\":\"is_saleable\",\"label\":\"可独立销售\",\"default_value\":true,\"status\":\"released\",\"options\":[{\"label\":\"是\",\"value\":true},{\"label\":\"否\",\"value\":false}]}";

    public static final String FIELD_JSON_PRICE_BOOK_PROD_IS_PACKAGE = "{\"describe_api_name\":\"PriceBookProductObj\",\"is_index\":false,\"is_active\":true,\"quote_field_type\":\"true_or_false\",\"is_unique\":false,\"label\":\"是否产品组合\",\"type\":\"quote\",\"is_abstract\":null,\"quote_field\":\"product_id__r.is_package\",\"field_num\":null,\"is_required\":false,\"api_name\":\"is_package\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"status\":\"released\"}";
    public static final String FIELD_JSON_PRICE_BOOK_PROD_IS_SALEABLE = "{\"describe_api_name\":\"PriceBookProductObj\",\"is_index\":false,\"is_active\":true,\"quote_field_type\":\"true_or_false\",\"is_unique\":false,\"label\":\"是否可独立销售\",\"type\":\"quote\",\"is_abstract\":null,\"quote_field\":\"product_id__r.is_saleable\",\"field_num\":null,\"is_required\":false,\"api_name\":\"is_saleable\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"status\":\"released\"}";

    public static final String EXTEND_INFO_ATTR_JSON = "{\"show_positive_sign\":true}";

    //在报价单明细， 订单产品中增加的字段
    public static final String FIELD_JSON_PROD_PKG_KEY = "{\"default_is_expression\":false,\"is_index\":false,\"is_active\":true,\"pattern\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"产品组合虚拟key\",\"type\":\"text\",\"is_abstract\":null,\"default_to_zero\":false,\"is_required\":false,\"api_name\":\"prod_pkg_key\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"max_length\":32,\"status\":\"released\"}";
    public static final String FIELD_JSON_PARENT_PROD_PKG_KEY = "{\"default_is_expression\":false,\"is_index\":false,\"is_active\":true,\"pattern\":\"\",\"is_unique\":false,\"default_value\":\"\",\"label\":\"产品组合虚拟parent key\",\"type\":\"text\",\"is_abstract\":null,\"default_to_zero\":false,\"is_required\":false,\"api_name\":\"parent_prod_pkg_key\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"max_length\":32,\"status\":\"released\"}";
    public static final String FIELD_JSON_ROOT_PROD_PKG_KEY = "{\"default_is_expression\":false,\"is_index\":false,\"is_active\":true,\"is_unique\":false,\"default_value\":\"\",\"label\":\"产品组合虚拟root key\",\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"api_name\":\"root_prod_pkg_key\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"max_length\":32,\"status\":\"released\"}";
    public static final String FIELD_JSON_IS_PACKAGE = "{\"is_index\":false,\"is_active\":true,\"quote_field_type\":\"true_or_false\",\"is_unique\":false,\"label\":\"是否产品组合\",\"type\":\"quote\",\"is_abstract\":null,\"quote_field\":\"product_id__r.is_package\",\"is_required\":false,\"api_name\":\"is_package\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"status\":\"released\"}";
    public static final String FIELD_JSON_IS_SALEABLE = "{\"is_index\":false,\"is_active\":true,\"quote_field_type\":\"true_or_false\",\"is_unique\":false,\"label\":\"是否可独立售卖\",\"type\":\"quote\",\"is_abstract\":null,\"quote_field\":\"product_id__r.is_saleable\",\"is_required\":false,\"api_name\":\"is_saleable\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"status\":\"released\"}";
    public static final String FIELD_JSON_PRODUCT_STATUS = "{\"is_index\":false,\"is_active\":true,\"quote_field_type\":\"select_one\",\"is_unique\":false,\"label\":\"产品上下架\",\"type\":\"quote\",\"is_abstract\":null,\"quote_field\":\"product_id__r.product_status\",\"is_required\":false,\"api_name\":\"product_status\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"status\":\"released\"}";
    public static final String FIELD_JSON_PRODUCT_LIFE_STATUS = "{\"is_index\":false,\"is_active\":true,\"quote_field_type\":\"select_one\",\"is_unique\":false,\"label\":\"产品生命状态\",\"type\":\"quote\",\"is_abstract\":null,\"quote_field\":\"product_id__r.life_status\",\"is_required\":false,\"api_name\":\"product_life_status\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"status\":\"released\"}";
    public static final String FIELD_JSON_PRODUCT_GROUP_ID = "{ \"is_index\": false, \"is_active\": true, \"quote_field_type\": \"object_reference\", \"description\": \"\", \"is_unique\": false, \"label\": \"产品分组\", \"type\": \"quote\", \"is_abstract\": null, \"quote_field\": \"bom_id__r.product_group_id\", \"field_num\": null, \"is_required\": false, \"api_name\": \"product_group_id\", \"define_type\": \"package\", \"is_index_field\": false, \"is_single\": false, \"help_text\": \"\", \"status\": \"new\" }";

    public static final String FIELD_JSON_QUOTE_LINE_BOM_CORE_ID = "{\"action_on_target_delete\":\"set_null\",\"api_name\":\"bom_core_id\",\"define_type\":\"package\",\"is_index\":false,\"label\":\"产品组合\",\"max_length\":256,\"pattern\":\"\",\"is_required\":false,\"is_unique\":false,\"target_api_name\":\"BomCoreObj\",\"target_related_list_label\":\"报价单明细\",\"target_related_list_name\":\"bome_core_list\",\"type\":\"object_reference\"}";
    public static final String FIELD_JSON_SALE_CONTRACT_LINE_BOM_CORE_ID = "{\"action_on_target_delete\":\"set_null\",\"api_name\":\"bom_core_id\",\"define_type\":\"package\",\"is_index\":false,\"label\":\"产品组合\",\"max_length\":256,\"pattern\":\"\",\"is_required\":false,\"is_unique\":false,\"target_api_name\":\"BomCoreObj\",\"target_related_list_label\":\"销售合同产品\",\"target_related_list_name\":\"bome_core_list\",\"type\":\"object_reference\"}";
    public static final String FIELD_JSON_SALES_ORDER_PRODUCT_BOM_CORE_ID = "{\"action_on_target_delete\":\"set_null\",\"api_name\":\"bom_core_id\",\"define_type\":\"package\",\"is_index\":false,\"label\":\"产品组合\",\"max_length\":256,\"pattern\":\"\",\"is_required\":false,\"is_unique\":false,\"target_api_name\":\"BomCoreObj\",\"target_related_list_label\":\"订单产品\",\"target_related_list_name\":\"bome_core_list\",\"type\":\"object_reference\"}";
    public static final String FIELD_JSON_BOM_TYPE = "{\"api_name\":\"bom_type\",\"define_type\":\"package\",\"description\":\"bom类型\",\"is_index\":false,\"is_need_convert\":false,\"is_required\":false,\"is_unique\":false,\"label\":\"bom类型\",\"quote_field\":\"bom_core_id__r.category\",\"quote_field_type\":\"select_one\",\"status\":\"released\",\"type\":\"quote\"}";
    public static final String FIELD_JSON_BOM_VERSION = "{\"api_name\":\"bom_version\",\"define_type\":\"package\",\"description\":\"bom版本\",\"is_index\":false,\"is_need_convert\":false,\"is_required\":false,\"is_unique\":false,\"label\":\"bom版本\",\"quote_field\":\"bom_core_id__r.core_version\",\"quote_field_type\":\"auto_number\",\"status\":\"released\",\"type\":\"quote\"}";
    public static final String FIELD_JSON_LINES_RELATED_CORE_ID = "{\"description\":\"复用产品组合Id\",\"is_unique\":false,\"type\":\"text\",\"is_required\":false,\"define_type\":\"package\",\"max_length\":100,\"is_index\":false,\"is_active\":true,\"default_value\":\"\",\"label\":\"复用产品组合Id\",\"is_need_convert\":false,\"api_name\":\"related_core_id\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}";

    public static final String FIELD_JSON_LINES_NEW_BOM_PATH = "{\"description\": \"Bom完整路径\",\"is_unique\": false,\"type\": \"text\",\"is_required\": false,\"define_type\": \"package\",\"max_length\": 500,\"is_index\": false,\"is_active\": true,\"default_value\": \"\",\"label\": \"Bom完整路径\",\"is_need_convert\": false,\"api_name\": \"new_bom_path\",\"is_index_field\": false,\"help_text\": \"\",\"status\": \"released\"}";

    public static final String FIELD_JSON_PRINT_HIERARCHY_FOR_ORDER_PRODUCT = "{\n" +
            "    \"default_is_expression\": false,\n" +
            "    \"pattern\": \"\",\n" +
            "    \"is_unique\": false,\n" +
            "    \"type\": \"text\",\n" +
            "    \"default_to_zero\": false,\n" +
            "    \"is_required\": false,\n" +
            "    \"define_type\": \"package\",\n" +
            "    \"input_mode\": \"\",\n" +
            "    \"is_extend\": false,\n" +
            "    \"is_single\": false,\n" +
            "    \"max_length\": 100,\n" +
            "    \"is_index\": true,\n" +
            "    \"is_active\": true,\n" +
            "    \"default_value\": \".\",\n" +
            "    \"label\": \"层级\",\n" +
            "    \"is_abstract\": null,\n" +
            "    \"api_name\": \"print_hierarchy\",\n" +
            "    \"is_index_field\": false,\n" +
            "    \"help_text\": \"此字段仅在 [ 订单 ] 、 [ 订单产品 ] ，打印模板中显示，但不可在布局中配置显示，默认值只支持 . _ - ，填写其它或者公式，默认按照 . 处理\",\n" +
            "    \"status\": \"new\"\n" +
            "}";

    public static final String FIELD_JSON_PRINT_HIERARCHY_FOR_QUOTE_LINES = "{\n" +
            "    \"default_is_expression\": false,\n" +
            "    \"pattern\": \"\",\n" +
            "    \"is_unique\": false,\n" +
            "    \"type\": \"text\",\n" +
            "    \"default_to_zero\": false,\n" +
            "    \"is_required\": false,\n" +
            "    \"define_type\": \"package\",\n" +
            "    \"input_mode\": \"\",\n" +
            "    \"is_extend\": false,\n" +
            "    \"is_single\": false,\n" +
            "    \"max_length\": 100,\n" +
            "    \"is_index\": true,\n" +
            "    \"is_active\": true,\n" +
            "    \"default_value\": \".\",\n" +
            "    \"label\": \"层级\",\n" +
            "    \"is_abstract\": null,\n" +
            "    \"api_name\": \"print_hierarchy\",\n" +
            "    \"is_index_field\": false,\n" +
            "    \"help_text\": \"此字段仅在 [ 报价单 ] 、 [ 报价单明细 ] ，打印模板中显示，但不可在布局中配置显示，默认值只支持 . _ - ，填写其它或者公式，默认按照 . 处理\",\n" +
            "    \"status\": \"new\"\n" +
            "}";

    public static final String FIELD_JSON_BOM_ATTR_CORE_ID = "{\n" +
            "      \"default_is_expression\": false,\n" +
            "      \"description\": \"\",\n" +
            "      \"is_unique\": false,\n" +
            "      \"where_type\": \"field\",\n" +
            "      \"type\": \"object_reference\",\n" +
            "      \"is_required\": true,\n" +
            "      \"wheres\": [\n" +
            "        {\n" +
            "          \"connector\": \"OR\",\n" +
            "          \"filters\": [\n" +
            "            {\n" +
            "              \"value_type\": 0,\n" +
            "              \"operator\": \"EQ\",\n" +
            "              \"field_name\": \"life_status\",\n" +
            "              \"field_values\": [\n" +
            "                \"normal\"\n" +
            "              ]\n" +
            "            }\n" +
            "          ]\n" +
            "        }\n" +
            "      ],\n" +
            "      \"define_type\": \"package\",\n" +
            "      \"is_extend\": false,\n" +
            "      \"is_single\": false,\n" +
            "      \"is_index\": true,\n" +
            "      \"is_active\": true,\n" +
            "      \"label\": \"产品组合\",\n" +
            "      \"target_api_name\": \"BomCoreObj\",\n" +
            "      \"target_related_list_name\": \"bom_core_attribute_constraint_list\",\n" +
            "      \"action_on_target_delete\": \"cascade_delete\",\n" +
            "      \"is_abstract\": null,\n" +
            "      \"field_num\": null,\n" +
            "      \"lookup_roles\": [],\n" +
            "      \"target_related_list_label\": \"产品属性约束\",\n" +
            "      \"is_need_convert\": false,\n" +
            "      \"api_name\": \"core_id\",\n" +
            "      \"is_index_field\": false,\n" +
            "      \"help_text\": \"\",\n" +
            "      \"status\": \"released\"\n" +
            "    }";
    private static final String ADF_BOM_ID = "{\"is_index\":true,\"is_active\":true,\"is_unique\":false,\"label\":\"产品选配明细编号\",\"target_api_name\":\"BOMObj\",\"type\":\"object_reference\",\"target_related_list_name\":\"advanced_formula_line_related_list_bom_id\",\"is_abstract\":null,\"target_related_list_label\":\"高级公式明细\",\"action_on_target_delete\":\"cascade_delete\",\"is_required\":false,\"wheres\":[],\"api_name\":\"bom_id\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"status\":\"released\"}";
    private static final String NODE_PRICE = "{\"description\":\"\",\"is_unique\":false,\"type\":\"currency\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"max_length\":20,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"length\":18,\"default_value\":\"\",\"label\":\"标准选配价格\",\"currency_unit\":\"￥\",\"is_need_convert\":false,\"api_name\":\"node_price\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"released\"}";
    private static final String NODE_DISCOUNT = "{\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"type\":\"percentile\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"max_length\":100,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"length\":12,\"default_value\":\"\",\"label\":\"选配折扣\",\"is_need_convert\":false,\"api_name\":\"node_discount\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}";
    protected static final String SHARE_RATE = "{\"pattern\":\"\",\"description\":\"\",\"is_unique\":false,\"type\":\"percentile\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"max_length\":12,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"length\":10,\"default_value\":\"\",\"label\":\"分摊比例\",\"is_need_convert\":false,\"api_name\":\"share_rate\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}";
    private static final String NODE_SUBTOTAL = "{\"description\":\"\",\"is_unique\":false,\"type\":\"number\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"max_length\":20,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"length\":18,\"default_value\":\"\",\"label\":\"部件小计\",\"currency_unit\":\"￥\",\"is_need_convert\":false,\"api_name\":\"node_subtotal\",\"round_mode\":4,\"help_text\":\"\",\"status\":\"released\"}";

    public static final String FIELD_JSON_BOM_ID = "{\"default_is_expression\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"label\":\"产品选配明细\",\"target_api_name\":\"BOMObj\",\"target_related_list_name\":\"target_related_list_bom\",\"target_related_list_label\":\"报价单明细\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"bom_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"released\"}";
    public static final String FIELD_JSON_ORDER_PRODUCT_BOM_ID = "{\"default_is_expression\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"label\":\"产品选配明细\",\"target_api_name\":\"BOMObj\",\"target_related_list_name\":\"target_related_list_bom\",\"target_related_list_label\":\"订单产品\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"bom_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"released\"}";
    public static final String FIELD_JSON_SALE_CONTRACT_BOM_ID = "{\"default_is_expression\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"label\":\"产品选配明细\",\"target_api_name\":\"BOMObj\",\"target_related_list_name\":\"target_related_list_bom\",\"target_related_list_label\":\"销售合同产品\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"bom_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"released\"}";

    private static final String RETURN_GOOD_INVOICE_PRODUCT_UNIT = "{\"describe_api_name\":\"ReturnedGoodsInvoiceProductObj\",\"is_index\":false,\"is_active\":true,\"quote_field_type\":\"select_one\",\"description\":\"单位\",\"is_unique\":false,\"label\":\"单位\",\"type\":\"quote\",\"is_abstract\":null,\"quote_field\":\"product_id__r.unit\",\"field_num\":null,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"unit\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"help_text\":\"\",\"status\":\"released\"}";


    public static final String FIELD_JSON_TIERDPRICEBOOKOBJ = "{\"default_is_expression\":false,\"is_index\":true,\"is_active\":true,\"is_unique\":false,\"label\":\"阶梯价目表\",\"target_api_name\":\"TieredPriceBookObj\",\"type\":\"object_reference\",\"target_related_list_name\":\"target_related_list_tired_pricebook\",\"is_abstract\":null,\"target_related_list_label\":\"报价单明细\",\"action_on_target_delete\":\"set_null\",\"is_required\":false,\"wheres\":[],\"api_name\":\"tiered_price_book_id\",\"define_type\":\"package\",\"is_index_field\":true,\"is_single\":false,\"help_text\":\"\",\"status\":\"released\"}";


    public static final String FIELD_JSON_CORE_ID = "{      \"auto_adapt_places\": false,      \"pattern\": \"\",      \"is_unique\": false,      \"description\": \"\",      \"type\": \"master_detail\",      \"is_required\": true,      \"define_type\": \"package\",      \"is_single\": false,      \"is_index\": true,      \"is_active\": true,      \"is_encrypted\": false,      \"target_api_name\": \"BomCoreObj\",      \"label\": \"产品组合\",      \"show_detail_button\": false,      \"target_related_list_name\": \"bom_id_list\",      \"target_related_list_label\": \"产品选配明细\",      \"api_name\": \"core_id\",      \"is_create_when_master_create\": true,      \"is_required_when_master_create\": true,      \"is_index_field\": true,      \"status\": \"released\",      \"help_text\": \"\"    }";
    public static final String FIELD_JSON_RELATED_CORE_ID = "{      \"default_is_expression\": false,      \"auto_adapt_places\": false,      \"pattern\": \"\",      \"description\": \"\",      \"is_unique\": false,      \"type\": \"text\",      \"default_to_zero\": false,      \"is_required\": false,      \"define_type\": \"package\",      \"input_mode\": \"\",      \"is_single\": false,      \"max_length\": 64,      \"is_index\": true,      \"is_active\": true,      \"is_encrypted\": false,      \"default_value\": \"\",      \"label\": \"关联产品组合\",      \"api_name\": \"related_core_id\",      \"is_index_field\": false,      \"help_text\": \"\",      \"status\": \"released\"    }";
    public static final String FIELD_JSON_NODE_BOM_CORE_VERSION = "{      \"default_is_expression\": false,      \"auto_adapt_places\": false,      \"pattern\": \"\",      \"description\": \"\",      \"is_unique\": false,      \"type\": \"text\",      \"default_to_zero\": false,      \"is_required\": false,      \"define_type\": \"package\",      \"input_mode\": \"\",      \"is_single\": false,      \"max_length\": 256,      \"is_index\": true,      \"is_active\": true,      \"is_encrypted\": false,      \"default_value\": \"\",      \"label\": \"子件bom版本\",      \"api_name\": \"node_bom_core_version\",      \"is_index_field\": false,      \"help_text\": \"\",      \"status\": \"released\"    }";
    public static final String FIELD_JSON_NODE_BOM_CORE_TYPE = "{\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"product\",\"label\":\"子件类型\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"node_bom_core_type\",\"options\":[{\"label\":\"选配件\",\"value\":\"configure\"},{\"label\":\"普通品\",\"value\":\"product\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}";

    //报价单订单明细添加引用字段
    private static String FIELD_JSON_EDIT_LINES = "{\n" +
            "\"price_editable\": {\"is_index\": false,\"is_active\": true,\"quote_field_type\": \"true_or_false\",\"description\": \"\",\"is_unique\": false,\"label\": \"价格可编辑\",\"type\": \"quote\",\"is_abstract\": null,\"quote_field\": \"bom_id__r.price_editable\",\"field_num\": null,\"is_required\": false,\"api_name\": \"price_editable\",\"define_type\": \"package\",\"is_index_field\": false,\"is_single\": false,\"help_text\": \"\",\"status\": \"new\"},\n" +
            "\"price_mode\": {\"is_index\": false,\"is_active\": true,\"quote_field_type\": \"select_one\",\"description\": \"\",\"is_unique\": false,\"label\": \"定价模式\",\"type\": \"quote\",\"is_abstract\": null,\"quote_field\": \"bom_id__r.price_mode\",\"field_num\": null,\"is_required\": false,\"api_name\": \"price_mode\",\"define_type\": \"package\",\"is_index_field\": false,\"is_single\": false,\"help_text\": \"\",\"status\": \"new\"},\n" +
            "\"amount_editable\": {\"is_index\": false,\"is_active\": true,\"quote_field_type\": \"true_or_false\",\"description\": \"\",\"is_unique\": false,\"label\": \"数量可编辑\",\"type\": \"quote\",\"is_abstract\": null,\"quote_field\": \"bom_id__r.amount_editable\",\"field_num\": null,\"is_required\": false,\"api_name\": \"amount_editable\",\"define_type\": \"package\",\"is_index_field\": false,\"is_single\": false,\"help_text\": \"\",\"status\": \"new\"},\n" +
            "\"max_amount\": {\"is_index\": false,\"is_active\": true,\"quote_field_type\": \"number\",\"description\": \"\",\"is_unique\": false,\"label\": \"最大数量\",\"type\": \"quote\",\"is_abstract\": null,\"quote_field\": \"bom_id__r.max_amount\",\"field_num\": null,\"is_required\": false,\"api_name\": \"max_amount\",\"define_type\": \"package\",\"is_index_field\": false,\"is_single\": false,\"help_text\": \"\",\"status\": \"new\"},\n" +
            "\"min_amount\": {\"is_index\": false,\"is_active\": true,\"quote_field_type\": \"number\",\"description\": \"\",\"is_unique\": false,\"label\": \"最小数量\",\"type\": \"quote\",\"is_abstract\": null,\"quote_field\": \"bom_id__r.min_amount\",\"field_num\": null,\"is_required\": false,\"api_name\": \"min_amount\",\"define_type\": \"package\",\"is_index_field\": false,\"is_single\": false,\"help_text\": \"\",\"status\": \"new\"},\n" +
            "\"increment\": {\"is_index\": false,\"is_active\": true,\"quote_field_type\": \"number\",\"description\": \"\",\"is_unique\": false,\"label\": \"增加数量幅度\",\"type\": \"quote\",\"is_abstract\": null,\"quote_field\": \"bom_id__r.increment\",\"field_num\": null,\"is_required\": false,\"api_name\": \"increment\",\"define_type\": \"package\",\"is_index_field\": false,\"is_single\": false,\"help_text\": \"\",\"status\": \"new\"},\n" +
            "\"amount_any\":{\"is_index\":false,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"quote_field_type\":\"true_or_false\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"label\":\"数量是否任意值\",\"type\":\"quote\",\"quote_field\":\"bom_id__r.amount_any\",\"is_required\":false,\"api_name\":\"amount_any\",\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"}" +
            "}";
    private static final String PRICING_PERIOD = "{\"default_is_expression\":false,\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"is_unique\":false,\"type\":\"number\",\"decimal_places\":0,\"default_to_zero\":true,\"is_required\":true,\"define_type\":\"package\",\"is_single\":false,\"max_length\":14,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"display_style\":\"step\",\"step_value\":20,\"length\":14,\"default_value\":\"1\",\"label\":\"期数\",\"is_need_convert\":false,\"api_name\":\"pricing_period\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"released\"}";//ignoreI18n


    @Autowired
    IObjectDescribeService objectDescribeService;
    @Autowired
    protected EnterpriseInitService enterpriseInitService;
    @Autowired
    ILayoutService layoutService;
    @Autowired
    ConfigService configService;

    @Autowired
    SFABizObjMappingRuleWrapperService mappingRuleWrapperService;

    @Autowired
    protected BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    @Autowired
    private TaskService taskService;
    @Autowired
    private DescribeWithSimplifiedChineseService describeWithSimplifiedChineseService;

    @Autowired
    ServiceFacade serviceFacade;

    @Override
    public ConfigCtrlModule.Result initModule(String tenantId, String userId) {
        long startTime = System.currentTimeMillis();
        User user = new User(tenantId, userId);
        if (!GrayUtil.bomMasterSlaveMode(tenantId)) {
            throw new ValidateException(I18N.text(BomI18NKeyUtil.SFA_BOM_CORE_WARN));
        }
        if (bizConfigThreadLocalCacheService.isOpenStratifiedPricing(tenantId)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONFIG_OPEN_STRATIFIED_PRICE_NOT_OPEN_OTHER_WARN));
        }
        before(user);
        try {
            initBomRelated(tenantId);
            initRelated(user);
            initProductConstraintRelated(tenantId);
            refreshDescribes(tenantId);
            refreshLayouts(tenantId);
            refreshMapping(tenantId, user);
            refreshAttrRange(user);
            createTaskForUpdateProductFiled(user.getTenantId());
            sendAuditLog(tenantId, userId, "success", "InitCPQSucceed", startTime);
            sendMqAfterInitModuleSuccess(tenantId);
        } catch (Exception e) {
            log.error("initCPQ failed:", e);
            sendAuditLog(tenantId, userId, e.toString(), "InitCPQFailed", startTime);
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Fail.getErrCode())
                    .errMessage(e.getMessage())
                    .value(
                            ConfigCtrlModule.Value.builder()
                                    .openStatus(ConfigCtrlModule.OpenStatus.CLOSE.toString()).build()
                    ).build();
        }
        return ConfigCtrlModule.Result.builder()
                .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                .value(
                        ConfigCtrlModule.Value.builder()
                                .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                ).build();

    }

    private void refreshAttrRange(User user) {
        if (!bizConfigThreadLocalCacheService.isOpenAttribute(user.getTenantId()) && !bizConfigThreadLocalCacheService.isOpenNonstandardAttribute(user.getTenantId())) {
            return;
        }
        serviceFacade.batchCreateFunc(user, Utils.BOM_CORE_API_NAME, Lists.newArrayList(ObjectAction.SET_ATTR_RANGE.getActionCode()));
        serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, Utils.BOM_CORE_API_NAME, Lists.newArrayList(ObjectAction.SET_ATTR_RANGE.getActionCode()), Lists.newArrayList());
    }

    private void refreshMapping(String tenantId, User user) {
        Map<String, String> fieldMapping = Maps.newHashMap();
        fieldMapping.put("bom_id", "bom_id");
        fieldMapping.put("prod_pkg_key", "prod_pkg_key");
        fieldMapping.put("parent_prod_pkg_key", "parent_prod_pkg_key");
        fieldMapping.put("root_prod_pkg_key", "root_prod_pkg_key");
        fieldMapping.put("amount_any", "amount_any");

        if (GrayUtil.bomMasterSlaveMode(tenantId)) {
            fieldMapping.put("bom_core_id", "bom_core_id");
            fieldMapping.put("bom_type", "bom_type");
            fieldMapping.put("bom_version", "bom_version");
            fieldMapping.put("related_core_id", "related_core_id");
            fieldMapping.put("new_bom_path", "new_bom_path");
        }

        Map<String, String> shareMapping = Maps.newHashMap();
        Map<String, String> contractMapping = Maps.newHashMap();
        contractMapping.putAll(fieldMapping);
        if (Objects.equals(IModuleInitService.MODULE_SIMPLE_CPQ, getModuleCode())) {
            shareMapping.put("node_price", "node_price");
            shareMapping.put("node_discount", "node_discount");
            fieldMapping.putAll(shareMapping);
        }

        try {
            mappingRuleWrapperService.addFieldMapping(user.getTenantId(), "rule_quotelinesobj2salesorderproduct__c", fieldMapping);
            if (SFAConfigUtil.isSaleContractOpen(tenantId)) {
                mappingRuleWrapperService.addFieldMapping(user.getTenantId(), "rule_quotelinesobj2salecontractlineobj__c", contractMapping);
                mappingRuleWrapperService.addFieldMapping(user.getTenantId(), "rule_salecontractlineobj2salesorderproductobj__c", contractMapping);
            }
            mappingRuleWrapperService.addFieldMapping(user.getTenantId(), "rule_salesorderprodobj2quotelinesobj__c", fieldMapping);
        } catch (MetadataServiceException e) {
            log.error("MetadataServiceException:", e);
        }
    }

    private void createTaskForUpdateProductFiled(String tenantId) {
        try {
            taskService.createOrUpdateTask("init_product_is_package_field", tenantId, tenantId, new Date(), JSON.toJSONString(ImmutableMap.<String, String>builder().put("tenantId", tenantId).build()));
        } catch (Exception e) {
            log.error("CPQModuleInitService initModule method sendMessage to mq error ", e);
        }
    }

    @Override
    public ConfigCtrlModule.Result initModuleRepair(ServiceContext context, String tenantId) {
        try {
            initCPQV2(tenantId);
        } catch (Exception e) {
            log.error("initCPQV2 initModule Fail tenantId->{}", tenantId, e);
            sendAuditLog(tenantId, context.getUser().getUpstreamOwnerIdOrUserId(), e.toString(), "initCPQV2Failed");
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Fail.getErrCode())
                    .errMessage(e.getMessage())
                    .value(ConfigCtrlModule.Value.builder().openStatus(ConfigCtrlModule.OpenStatus.CLOSE.toString()).build()
                    ).build();

        }
        return ConfigCtrlModule.Result.builder()
                .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                .value(
                        ConfigCtrlModule.Value.builder()
                                .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                ).build();
    }

    private void initCPQV2(String tenantId) throws MetadataServiceException {
        initNewObjectNoFuncPrivilege(tenantId, "TieredPriceBookObj", Lists.newArrayList("recordType"));
        initNewObjectNoFuncPrivilege(tenantId, "TieredPriceBookRuleObj", Lists.newArrayList("recordType", "dataPrivilege"));
        initNewObjectNoFuncPrivilege(tenantId, "TieredPriceBookProductObj", Lists.newArrayList("recordType", "dataPrivilege"));
        IObjectDescribe quoteLinesObjDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, QUOTE_LINES_OBJ);
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_TIERDPRICEBOOKOBJ);
        objectDescribeService.update(quoteLinesObjDescribe);
    }

    private void initBomRelated(String tenantId) {
        enterpriseInitService.initDescribeForTenant(tenantId, Utils.PRODUCT_GROUP_API_NAME);
        if (GrayUtil.bomMasterSlaveMode(tenantId)) {
            enterpriseInitService.initDescribeForTenant(tenantId, getDescribeFromLocalResource(SFAPreDefineObject.BomCore.getApiName(), tenantId));
            enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(SFAPreDefineObject.BomCore.getApiName()), tenantId);
            enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(SFAPreDefineObject.BomCore.getApiName()), new User(tenantId, "-10000"), null, "3", null);
        }
        enterpriseInitService.initDescribeForTenant(tenantId, getDescribeFromLocalResource(Utils.BOM_API_NAME, tenantId));
        enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(Utils.BOM_API_NAME), new User(tenantId, "-10000"), Lists.newArrayList("recordType"), null, null);
    }

    private void initProductConstraintRelated(String tenantId) {
        enterpriseInitService.initDescribeForTenant(tenantId, Utils.PRODUCT_CONSTRAINT_API_NAME);
        enterpriseInitService.initDescribeForTenant(tenantId, Utils.PRODUCT_CONSTRAINT_LINES_API_NAME);

        enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(Utils.PRODUCT_CONSTRAINT_API_NAME, Utils.PRODUCT_CONSTRAINT_LINES_API_NAME), new User(tenantId, "-10000"), Lists.newArrayList("recordType"), null, null);
        enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(Utils.PRODUCT_CONSTRAINT_API_NAME, Utils.PRODUCT_CONSTRAINT_LINES_API_NAME), tenantId);
    }

    private void initNewObjectNoFuncPrivilege(String tenantId, String apiName, List<String> operTypeLs) {
        enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(apiName), new User(tenantId, "-10000"), operTypeLs
                , DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PUBLIC_READONLY.getValue(), null);
        enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(apiName), tenantId);
        enterpriseInitService.initDescribeForTenant(tenantId, apiName);

    }

    public List<ILayout> getDetailLayouts(String tenantId, String apiName) {
        return findByType(tenantId, apiName, ILayout.DETAIL_LAYOUT_TYPE);
    }

    private List<ILayout> findByType(String tenantId, String describeApiName, String layoutType) {
        try {
            List<Layout> layoutList = layoutService.findByTypes(tenantId, Lists.newArrayList(layoutType), describeApiName);
            return layoutList.stream().collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
    }

    public void refreshLayouts(String tenantId) throws MetadataServiceException {
        addFieldForProductLayout(tenantId);
        addFieldForAdvancedFormulaLayout(tenantId);
    }

    private void addFieldForProductLayout(String tenantId) throws MetadataServiceException {
        List<ILayout> productAllDetailLayouts = getDetailLayouts(tenantId, PRODUCT_OBJ);
        for (ILayout layout : productAllDetailLayouts) {
            List<IFormField> formFieldList = Lists.newArrayList();
            formFieldList = addFiledForProductLayout(formFieldList);
            formFieldList.add(convertFormFieldByDescJson(FIELD_JSON_PROD_IS_PACKAGE));
            formFieldList.add(convertFormFieldByDescJson(FIELD_JSON_PROD_IS_SALEABLE));
            ModuleInitLayoutUtil.addFieldsToDetailLayoutFormComponent(layout, formFieldList);
            layoutService.replace(layout);
        }
    }

    private void addFieldForAdvancedFormulaLayout(String tenantId) throws MetadataServiceException {
        if (!SFAConfigUtil.quoterEnable(tenantId)) {
            return;
        }
        List<ILayout> detailLayouts = getDetailLayouts(tenantId, SFAPreDefineObject.AdvancedFormulaLine.getApiName());
        for (ILayout layout : detailLayouts) {
            List<IFormField> formFieldList = Lists.newArrayList();
            formFieldList.add(convertFormFieldByDescJson(ADF_BOM_ID, false, false));
            ModuleInitLayoutUtil.addFieldsToDetailLayoutFormComponent(layout, formFieldList);
            layoutService.replace(layout);
        }
    }

    protected List<IFormField> addFiledForProductLayout(List<IFormField> formFieldList) {
        return formFieldList;
    }

    private void refreshDescribes(String tenantId) throws MetadataServiceException {
        addNewFieldsForProductObj(tenantId);
        addNewFieldForPriceBookProduct(tenantId);
        addnewFieldsForQuoteLinesObj(tenantId);
        addnewFieldsForSalesOrderProductObj(tenantId);
        addFieldsForSaleContractLineObj(tenantId);
        //2019/6/21 退货单产品字段刷wheres条件
        updateFieldForReturnedGoodsInvoiceProductObj(tenantId);
        addField2AvailableProduct(tenantId);
        addField2Constraint(tenantId);
        addField2AdvancedFormula(tenantId);
        addField2periodicProduct(tenantId);
    }

    private void addField2periodicProduct(String tenantId) {
        if (!SFAConfigUtil.periodicProductEnable(tenantId)) {
            return;
        }
        IObjectDescribe describe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId),Utils.BOM_API_NAME);
        if (Objects.isNull(describe)) {
            return;
        }
        generateNewField(Utils.BOM_API_NAME, describe, PRICING_PERIOD);
        try {
            objectDescribeService.update(describe);
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(), e);
        }
    }


    private void addField2AdvancedFormula(String tenantId) {
        if (SFAConfigUtil.quoterEnable(tenantId)) {
            try {
                IObjectDescribe bDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId),SFAPreDefineObject.AdvancedFormulaLine.getApiName());
                generateNewField(SFAPreDefineObject.AdvancedFormulaLine.getApiName(), bDescribe, ADF_BOM_ID);
                objectDescribeService.update(bDescribe);
            } catch (MetadataServiceException e) {
                log.error("advancedFormula add field error:", e);
            }
        }
    }

    protected void addField2Constraint(String tenantId) {
        if (GrayUtil.bomMasterSlaveMode(tenantId)) {
            try {
                IObjectDescribe bDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId),SFAPreDefineObject.BomAttributeConstraint.getApiName());
                if (bDescribe != null) {
                    generateNewField(SFAPreDefineObject.BomAttributeConstraint.getApiName(), bDescribe, FIELD_JSON_BOM_ATTR_CORE_ID);
                    objectDescribeService.update(bDescribe);
                }
            } catch (MetadataServiceException e) {
                log.error("constraint add field error:", e);
            }
        }
    }

    private void addFieldsForSaleContractLineObj(String tenantId) throws MetadataServiceException {
        if (!SFAConfigUtil.isSaleContractOpen(tenantId)) {
            return;
        }
        IObjectDescribe saleContractLineObjDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId),Utils.SALE_CONTRACT_LINE_API_NAME);
        ObjectReferenceFieldDescribe productIdField = (ObjectReferenceFieldDescribe) saleContractLineObjDescribe.getFieldDescribe("product_id");
        productIdField.setWheres(getProductIdLookupWheres(productIdField.getWheres()));
        ObjectReferenceFieldDescribe priceBookProductIdField = (ObjectReferenceFieldDescribe) saleContractLineObjDescribe.getFieldDescribe("price_book_product_id");
        if (priceBookProductIdField != null) {
            priceBookProductIdField.setWheres(getProductIdLookupWheres(priceBookProductIdField.getWheres()));
        }
        saleContractLineObjDescribe.getFieldDescribe("product_price").setExtendInfo((Map) JSON.parse(EXTEND_INFO_ATTR_JSON));
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_IS_PACKAGE);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_IS_SALEABLE);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_PRODUCT_STATUS);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_PRODUCT_LIFE_STATUS);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_SALE_CONTRACT_BOM_ID);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_PROD_PKG_KEY);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_PARENT_PROD_PKG_KEY);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_ROOT_PROD_PKG_KEY);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_PRODUCT_GROUP_ID);
        generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_PRINT_HIERARCHY_FOR_ORDER_PRODUCT);
        if (GrayUtil.bomMasterSlaveMode(tenantId)) {
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_SALE_CONTRACT_LINE_BOM_CORE_ID);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_BOM_TYPE);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_BOM_VERSION);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_LINES_RELATED_CORE_ID);
            generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, FIELD_JSON_LINES_NEW_BOM_PATH);
        }
        JSONObject jsonFiled = JSON.parseObject(FIELD_JSON_EDIT_LINES);
        jsonFiled.keySet().stream().forEach(x -> generateNewField(Utils.SALE_CONTRACT_LINE_API_NAME, saleContractLineObjDescribe, jsonFiled.getString(x)));
        objectDescribeService.update(saleContractLineObjDescribe);
    }

    public static List<LinkedHashMap> getOrderProductLookupWheres(List<LinkedHashMap> oldWheres) {
        List<LinkedHashMap> wheres = Lists.newArrayList();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("connector", Where.CONN.AND);
        List<Map> filters = Lists.newArrayList();
        Map<String, Object> filter = Maps.newHashMap();
        filter.put("field_name", "parent_prod_pkg_key");
        filter.put("operator", Operator.IS.name());
        filter.put("value_type", 0);
        filter.put("field_values", Lists.newArrayList());
        filters.add(filter);
        if (oldWheres != null && oldWheres.stream().findFirst().isPresent()) {
            Map<String, Object> oldmap = oldWheres.get(0);
            List<Map> oldfilters = (List<Map>) oldmap.get("filters");
            if (CollectionUtils.notEmpty(oldfilters)) {
                filters.addAll(oldfilters);
            }
            if (oldfilters.contains(filter)) {

                filters.remove(filter);
            }
        }
        map.put("filters", filters);
        wheres.add(map);
        return wheres;
    }


    /**
     * 给退货单产品对象的order_product_id刷where条件
     *
     * @param tenantId
     * @throws MetadataServiceException
     */
    private void updateFieldForReturnedGoodsInvoiceProductObj(String tenantId) throws MetadataServiceException {
        IObjectDescribe returnedGoodsInvoiceProductDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId),Utils.RETURN_GOODS_INVOICE_Product_API_NAME);
        IFieldDescribe orderProductIdField = returnedGoodsInvoiceProductDescribe.getFieldDescribe(ORDER_PRODUCT_ID);
        if (orderProductIdField instanceof ObjectReferenceFieldDescribe) {
            ObjectReferenceFieldDescribe orderProdIdFieldRef = ((ObjectReferenceFieldDescribe) orderProductIdField);
            orderProdIdFieldRef.setWheres(getOrderProductLookupWheres(orderProdIdFieldRef.getWheres()));
        }
        returnedGoodsInvoiceProductDescribe.getFieldDescribe("returned_product_price").setExtendInfo((Map) JSON.parse(EXTEND_INFO_ATTR_JSON));
        //增加prod_pkg_key和parent_prod_pkg_key两个字段
        generateNewField(RETURNED_GOODS_INVOICE_PRODUCT_OBJ, returnedGoodsInvoiceProductDescribe, FIELD_JSON_PROD_PKG_KEY);
        generateNewField(RETURNED_GOODS_INVOICE_PRODUCT_OBJ, returnedGoodsInvoiceProductDescribe, FIELD_JSON_PARENT_PROD_PKG_KEY);

        // 添加退货单产品引用产品单位引用字段
        generateNewField(RETURNED_GOODS_INVOICE_PRODUCT_OBJ, returnedGoodsInvoiceProductDescribe, RETURN_GOOD_INVOICE_PRODUCT_UNIT);
        objectDescribeService.update(returnedGoodsInvoiceProductDescribe);
    }

    private void addField2AvailableProduct(String tenantId) throws MetadataServiceException {
        String moduleConfig = configService.findTenantConfig(new User(tenantId, User.SUPPER_ADMIN_USER_ID),
                IModuleInitService.AVAILABLE_RANGE);
        if ("1".equals(moduleConfig)) {
            String apiName = SFAPreDefineObject.AvailableProduct.getApiName();
            IObjectDescribe objectDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId),apiName);
            if (Objects.isNull(objectDescribe)) {
                return;
            }
            generateNewField(apiName, objectDescribe, FIELD_JSON_IS_PACKAGE);
            generateNewField(apiName, objectDescribe, FIELD_JSON_IS_SALEABLE);
            objectDescribeService.update(objectDescribe);
        }
    }

    private void addnewFieldsForSalesOrderProductObj(String tenantId) throws MetadataServiceException {
        IObjectDescribe salesOrderProdObjDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId),SALES_ORDER_PRODUCT_OBJ);
        ObjectReferenceFieldDescribe productIdField = (ObjectReferenceFieldDescribe) salesOrderProdObjDescribe.getFieldDescribe("product_id");
        productIdField.setWheres(getProductIdLookupWheres(productIdField.getWheres()));
        ObjectReferenceFieldDescribe priceBookProductIdField = (ObjectReferenceFieldDescribe) salesOrderProdObjDescribe.getFieldDescribe("price_book_product_id");
        if (priceBookProductIdField != null) {
            priceBookProductIdField.setWheres(getProductIdLookupWheres(priceBookProductIdField.getWheres()));
        }
        salesOrderProdObjDescribe.getFieldDescribe("product_price").setExtendInfo((Map) JSON.parse(EXTEND_INFO_ATTR_JSON));
        generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_IS_PACKAGE);
        generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_IS_SALEABLE);
        generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_PRODUCT_STATUS);
        generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_PRODUCT_LIFE_STATUS);
        generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_ORDER_PRODUCT_BOM_ID);
        generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_PROD_PKG_KEY);
        generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_PARENT_PROD_PKG_KEY);
        generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_ROOT_PROD_PKG_KEY);
        generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_PRODUCT_GROUP_ID);
        generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_PRINT_HIERARCHY_FOR_ORDER_PRODUCT);
        if (GrayUtil.bomMasterSlaveMode(tenantId)) {
            generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_SALES_ORDER_PRODUCT_BOM_CORE_ID);
            generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_BOM_TYPE);
            generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_BOM_VERSION);
            generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_LINES_RELATED_CORE_ID);
            generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, FIELD_JSON_LINES_NEW_BOM_PATH);
        }
        if (Objects.equals(IModuleInitService.MODULE_SIMPLE_CPQ, getModuleCode())) {
            generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, NODE_DISCOUNT);
            generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, NODE_PRICE);
            generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, NODE_SUBTOTAL);
            generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, SHARE_RATE);
            IFieldDescribe priceBookPriceField = salesOrderProdObjDescribe.getFieldDescribe("price_book_price");
            if (Objects.nonNull(priceBookPriceField)) {
                String PRICE_DEFAULT_VALUE = "$product_price$*$price_book_discount$";
                String PRICE_DEFAULT_VALUE_WITH_EXCHANGE_RATE = "$product_price$*EXCHANGERATE($price_book_product_id__r.mc_currency$,$mc_currency$,1)*$price_book_discount$";
                boolean currencyEnabled = Objects.equals("1", configService.findTenantConfig(new User(tenantId, User.SUPPER_ADMIN_USER_ID), "multi_currency_config"));
                priceBookPriceField.setDefaultValue(currencyEnabled ? PRICE_DEFAULT_VALUE_WITH_EXCHANGE_RATE : PRICE_DEFAULT_VALUE);
                priceBookPriceField.setDefaultIsExpression(true);
            }
        }
        JSONObject jsonFiled = JSON.parseObject(FIELD_JSON_EDIT_LINES);
        jsonFiled.keySet().stream().forEach(x -> generateNewField(SALES_ORDER_PRODUCT_OBJ, salesOrderProdObjDescribe, jsonFiled.getString(x)));
        objectDescribeService.update(salesOrderProdObjDescribe);
    }

    private void addnewFieldsForQuoteLinesObj(String tenantId) throws MetadataServiceException {
        IObjectDescribe quoteLinesObjDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId),QUOTE_LINES_OBJ);
        ObjectReferenceFieldDescribe productIdField = (ObjectReferenceFieldDescribe) quoteLinesObjDescribe.getFieldDescribe("product_id");
        productIdField.setWheres(getProductIdLookupWheres(productIdField.getWheres()));
        ObjectReferenceFieldDescribe priceBookProductIdField = (ObjectReferenceFieldDescribe) quoteLinesObjDescribe.getFieldDescribe("price_book_product_id");
        if (priceBookProductIdField != null) {
            priceBookProductIdField.setWheres(getProductIdLookupWheres(priceBookProductIdField.getWheres()));
        }
        quoteLinesObjDescribe.getFieldDescribe("price").setExtendInfo((Map) JSON.parse(EXTEND_INFO_ATTR_JSON));
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_IS_PACKAGE);
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_IS_SALEABLE);
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_PRODUCT_STATUS);
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_PRODUCT_LIFE_STATUS);
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_BOM_ID);
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_PROD_PKG_KEY);
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_PARENT_PROD_PKG_KEY);
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_ROOT_PROD_PKG_KEY);
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_PRODUCT_GROUP_ID);
        generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_PRINT_HIERARCHY_FOR_QUOTE_LINES);
        if (GrayUtil.bomMasterSlaveMode(tenantId)) {
            generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_QUOTE_LINE_BOM_CORE_ID);
            generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_BOM_TYPE);
            generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_BOM_VERSION);
            generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_LINES_RELATED_CORE_ID);
            generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, FIELD_JSON_LINES_NEW_BOM_PATH);
        }
        if (Objects.equals(IModuleInitService.MODULE_SIMPLE_CPQ, getModuleCode())) {
            generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, NODE_PRICE);
            generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, NODE_DISCOUNT);
        }
        JSONObject jsonFiled = JSON.parseObject(FIELD_JSON_EDIT_LINES);
        jsonFiled.keySet().stream().forEach(x -> generateNewField(QUOTE_LINES_OBJ, quoteLinesObjDescribe, jsonFiled.getString(x)));
        objectDescribeService.update(quoteLinesObjDescribe);
    }

    protected void generateNewField(String objDescribeApiName, IObjectDescribe quoteLinesObjDescribe, String fieldJson) {
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldJson);
        fieldDescribe.setDescribeApiName(objDescribeApiName);
        if (quoteLinesObjDescribe.getFieldDescribe(fieldDescribe.getApiName()) == null) {
            quoteLinesObjDescribe.addFieldDescribe(fieldDescribe);
        }
    }

    private void addNewFieldForPriceBookProduct(String tenantId) throws MetadataServiceException {
        IObjectDescribe priceBookProductObjDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId),PRICE_BOOK_PRODUCT_OBJ);
        if (Objects.isNull(priceBookProductObjDescribe)) {
            return;
        }
        IFieldDescribe isPackageField = FieldDescribeFactory.newInstance(FIELD_JSON_PRICE_BOOK_PROD_IS_PACKAGE);
        if (priceBookProductObjDescribe.getFieldDescribe(isPackageField.getApiName()) == null) {
            priceBookProductObjDescribe.addFieldDescribe(isPackageField);
        }
        IFieldDescribe isSaleableField = FieldDescribeFactory.newInstance(FIELD_JSON_PRICE_BOOK_PROD_IS_SALEABLE);
        if (priceBookProductObjDescribe.getFieldDescribe(isSaleableField.getApiName()) == null) {
            priceBookProductObjDescribe.addFieldDescribe(isSaleableField);
        }
        objectDescribeService.update(priceBookProductObjDescribe);

    }

    public static List<LinkedHashMap> getProductIdLookupWheres(List<LinkedHashMap> oldWheres) {
        List<LinkedHashMap> wheres = Lists.newArrayList();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("connector", Where.CONN.OR);
        List<Map> filters = Lists.newArrayList();
        Map<String, Object> filter = Maps.newHashMap();
        filter.put("field_name", "is_saleable");
        filter.put("operator", Operator.EQ.name());
        filter.put("field_values", Lists.newArrayList(Boolean.TRUE));
        filters.add(filter);
        if (CollectionUtils.notEmpty(oldWheres)) {
            Map<String, Object> oldmap = oldWheres.get(0);
            List<Map> oldfilters = (List<Map>) oldmap.get("filters");
            if (CollectionUtils.notEmpty(oldfilters)) {
                filters.addAll(oldfilters);
            }
            if (oldfilters.contains(filter)) {
                filters.remove(filter);
            }
        }
        map.put("filters", filters);
        wheres.add(map);
        return wheres;
    }

    private void addNewFieldsForProductObj(String tenantId) throws MetadataServiceException {
        IObjectDescribe productObjDescribe = addNewFieldsForProductObj(tenantId, PRODUCT_OBJ);
        objectDescribeService.update(productObjDescribe);
    }

    protected IObjectDescribe addNewFieldsForProductObj(String tenantId, String objectApiName) throws MetadataServiceException {
        IObjectDescribe productObjDescribe = describeWithSimplifiedChineseService.findByDescribeApiName(User.systemUser(tenantId),objectApiName);
        IFieldDescribe productIsPackageField = FieldDescribeFactory.newInstance(FIELD_JSON_PROD_IS_PACKAGE);
        if (productObjDescribe.getFieldDescribe(productIsPackageField.getApiName()) == null) {
            productObjDescribe.addFieldDescribe(productIsPackageField);
        } else {
            productObjDescribe.getFieldDescribe(productIsPackageField.getApiName()).setActive(true);
            productObjDescribe.getFieldDescribe(productIsPackageField.getApiName()).setRequired(true);
        }
        IFieldDescribe productIsSaleableField = FieldDescribeFactory.newInstance(FIELD_JSON_PROD_IS_SALEABLE);
        if (productObjDescribe.getFieldDescribe(productIsSaleableField.getApiName()) == null) {
            productObjDescribe.addFieldDescribe(productIsSaleableField);
        } else {
            productObjDescribe.getFieldDescribe(productIsSaleableField.getApiName()).setActive(true);
            productObjDescribe.getFieldDescribe(productIsSaleableField.getApiName()).setRequired(true);
        }
        productObjDescribe.getFieldDescribe("price").setExtendInfo((Map) JSON.parse(EXTEND_INFO_ATTR_JSON));
        return productObjDescribe;
    }

    protected IFormField convertFormFieldByDescJson(String descJson, boolean required, boolean readOnly) {
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(descJson);
        IFormField formField = new FormField();
        formField.setFieldName(fieldDescribe.getApiName());
        formField.setRequired(required);
        formField.setReadOnly(readOnly);
        formField.setRenderType(fieldDescribe.getType());
        return formField;
    }

    private IFormField convertFormFieldByDescJson(String descJson) {
        return convertFormFieldByDescJson(descJson, true, false);
    }

    public void addFunc(User user, List<String> productActionCodeList) {
        List<String> bomActionCodeList = Lists.newArrayList();
        bomActionCodeList.add(ObjectAction.CREATE_GROUP.getActionCode());
        bomActionCodeList.add(ObjectAction.SET_GROUP.getActionCode());
        if (!GrayUtil.bomMasterSlaveMode(user.getTenantId())) {
            bomActionCodeList.add(ObjectAction.DEL_GROUP.getActionCode());
            bomActionCodeList.add(ObjectAction.SYNC_PRODUCT_STRUCTURE.getActionCode());
            bomActionCodeList.add(ObjectAction.SYNC_STRUCTURE_TO_OTHER.getActionCode());
        }
        try {
            if (CollectionUtils.notEmpty(productActionCodeList)) {
                serviceFacade.batchCreateFunc(user, SFAPreDefineObject.Product.getApiName(), productActionCodeList);
                serviceFacade.updateUserDefinedFuncAccess(user,
                        PrivilegeConstants.ADMIN_ROLE_CODE, SFAPreDefineObject.Product.getApiName(), productActionCodeList, Lists.newArrayList());
            }
            serviceFacade.batchCreateFunc(user, SFAPreDefineObject.Bom.getApiName(), bomActionCodeList);
            serviceFacade.updateUserDefinedFuncAccess(user,
                    PrivilegeConstants.ADMIN_ROLE_CODE, SFAPreDefineObject.Bom.getApiName(), bomActionCodeList, Lists.newArrayList());
        } catch (Exception e) {
            log.error("addRoleFunc error,apiName:BOMObj,ei:{}", user.getTenantId(), e);
        }
    }


    @Override
    public ConfigCtrlModule.Result closeModule(String tenantId) {
        return null;
    }

    protected ObjectDescribe getDescribeFromLocalResource(String apiName, String tenantId) {
        ObjectDescribe describe = InitUtil.getDescribeFromLocalResource(apiName);
        if (Objects.equals(apiName, Utils.BOM_API_NAME) && GrayUtil.bomMasterSlaveMode(tenantId)) {
            IFieldDescribe coreField = FieldDescribeFactory.newInstance(FIELD_JSON_CORE_ID);
            if (describe.getFieldDescribe(coreField.getApiName()) == null) {
                describe.addFieldDescribe(coreField);
            } else {
                describe.getFieldDescribe(coreField.getApiName()).setActive(true);
            }
            IFieldDescribe relateCoreField = FieldDescribeFactory.newInstance(FIELD_JSON_RELATED_CORE_ID);
            if (describe.getFieldDescribe(relateCoreField.getApiName()) == null) {
                describe.addFieldDescribe(relateCoreField);
            } else {
                describe.getFieldDescribe(relateCoreField.getApiName()).setActive(true);
            }
            IFieldDescribe versionField = FieldDescribeFactory.newInstance(FIELD_JSON_NODE_BOM_CORE_VERSION);
            if (describe.getFieldDescribe(versionField.getApiName()) == null) {
                describe.addFieldDescribe(versionField);
            } else {
                describe.getFieldDescribe(versionField.getApiName()).setActive(true);
            }
            SelectOneFieldDescribe typeField = (SelectOneFieldDescribe) FieldDescribeFactory.newInstance(FIELD_JSON_NODE_BOM_CORE_TYPE);
            if (typeField != null) {
                if (Objects.equals(ConfigType.MODULE_SIMPLE_CPQ.getKey(), getModuleCode())) {
                    ISelectOption option = new SelectOption();
                    option.setValue("standard");
                    option.setLabel("标准件");
                    List<ISelectOption> selectOptions = typeField.getSelectOptions();
                    selectOptions.removeIf(x -> Objects.equals(x.getValue(), EnumUtil.nodeBomCoreType.configure.getValue()));
                    selectOptions.add(option);
                    typeField.setSelectOptions(selectOptions);
                }
                if (describe.getFieldDescribe(typeField.getApiName()) == null) {
                    describe.addFieldDescribe(typeField);
                } else {
                    describe.getFieldDescribe(typeField.getApiName()).setActive(true);
                }
            }
        }
        return describe;
    }

    protected abstract void initRelated(User user);

    protected abstract void before(User user);
}

