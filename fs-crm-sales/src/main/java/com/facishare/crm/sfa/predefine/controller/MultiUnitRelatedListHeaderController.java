package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

/**
 * 该对象不外漏,该方法只下发简单的布局,没有场景按钮等数据
 */
public class MultiUnitRelatedListHeaderController extends StandardListHeaderController {
    protected static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected Result doService(Arg arg) {
        ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(controllerContext.getUser(), "list", "MultiUnitRelatedObj");

        if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(controllerContext.getTenantId())) {
            List<IComponent> components = Lists.newArrayList();
            LayoutExt.of(layout).getComponentsSilently().forEach(o -> {
                List fields = ComponentExt.of(o).get("include_fields", List.class);
                fields.removeIf(x -> "is_pricing".equals(((Map) x).get("api_name")));

                o.set("include_fields", fields);
                components.add(o);
            });
            layout.setComponents(components);
        }

        Result ret = Result.builder()
                .objectDescribe(ObjectDescribeDocument.of(objectDescribeExt))
                .layout(LayoutDocument.of(layout))
                .build();
        return ret;
    }
}
