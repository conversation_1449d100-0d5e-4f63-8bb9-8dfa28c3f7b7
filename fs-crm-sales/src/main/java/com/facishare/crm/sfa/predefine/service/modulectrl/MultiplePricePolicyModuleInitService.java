package com.facishare.crm.sfa.predefine.service.modulectrl;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.describebuilder.SelectManyFieldDescribeBuilder;
import com.facishare.crm.describebuilder.SelectOneFieldDescribeBuilder;
import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.modulectrl.util.MarketingInitUtil;
import com.facishare.crm.sfa.predefine.service.pricepolicy.MarketingGrayObjectService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.model.FindMarketingGrayObj;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 跨对象促销模块初始化服务
 */
@Component
@Slf4j
public class MultiplePricePolicyModuleInitService extends AbstractModuleInitService {
    String fieldJson = "{\"default_is_expression\":false,\"is_index\":false,\"is_active\":true,\"is_unique\":false,\"label\":\"促销对象\",\"type\":\"array\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"multiple_object_api_name\",\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"status\":\"released\"}";// ignoreI18n
    private static final String BIZ = "update_multiple_price_policy";
    @Resource
    private BizConfigThreadLocalCacheService configThreadLocalCacheService;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private EnterpriseInitService enterpriseInitService;
    @Resource
    private MarketingGrayObjectService marketingGrayObjectService;
    @Resource
    private AsyncTaskProducer asyncTaskProducer;
    @Resource
    private DescribeWithSimplifiedChineseService describeChineseService;

    @Override
    public String getModuleCode() {
        return ConfigType.MULTIPLE_OBJECT_PRICE_POLICY.getKey();
    }

    @Override
    public ConfigCtrlModule.Result initModule(String tenantId, String userId) {
        if (!configThreadLocalCacheService.isOpenPricePolicy(tenantId)) {
            return MarketingInitUtil.getFailResult("sfa.config.price.policy.not.open");
        }
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> addFields(new User(tenantId, userId)));
        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error in open multiple_object_price_policy, ei:{},msg is {}", tenantId, e.getMessage());
        }
        return MarketingInitUtil.getOkResult();
    }

    private void addFields(User user) {
        IObjectDescribe policyDescribe = describeChineseService.findByDescribeApiName(user, SFAPreDefineObject.PricePolicy.getApiName());
        List<IFieldDescribe> toAddFieldList = Lists.newArrayList();
        List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList = Lists.newArrayList();
        List<ISelectOption> selectOptions = Lists.newArrayList();
        selectOptions.add(new SelectOption("单对象促销", "one", ""));// ignoreI18n
        selectOptions.add(new SelectOption("多对象促销", "multiple", ""));// ignoreI18n
        SelectOneFieldDescribe fieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName("policy_object_type").label("促销模式")// ignoreI18n
                .selectOptions(selectOptions).build();
        fieldDescribe.setActive(true);
        fieldDescribe.setIndex(true);
        fieldDescribe.setDefaultValue("one");
        toAddFieldList.add(fieldDescribe);
        FieldLayoutPojo fieldLayout = enterpriseInitService.getFieldLayoutPojo(SystemConstants.RenderType.SelectOne.renderType, false, false);
        fieldLayoutTupleList.add(Tuple.of(fieldDescribe, fieldLayout));
        FindMarketingGrayObj.Arg arg = new FindMarketingGrayObj.Arg();
        arg.setModule("price_policy");
        List<FindMarketingGrayObj.ResultInfo> mlist = marketingGrayObjectService.findMarketingGrayObj(user, arg);
        addObjectApiName(toAddFieldList, fieldLayoutTupleList, mlist);
        MarketingInitUtil.addFieldDescribe(policyDescribe, toAddFieldList);

        List<ILayout> layoutList = serviceFacade.getLayoutLogicService().getDetailLayouts(user.getTenantId(), policyDescribe);
        for (ILayout m : layoutList) {
            fieldLayoutTupleList.forEach(tuple -> LayoutExt.of(m).addField(tuple.getKey(), tuple.getValue()));
            serviceFacade.getLayoutLogicService().updateLayout(user, m);
        }

        IObjectDescribe policyRuleDescribe = describeChineseService.findByDescribeApiName(user, SFAPreDefineObject.PricePolicyRule.getApiName());
        List<IFieldDescribe> toAddFieldRuleList = Lists.newArrayList();

        IFieldDescribe mtFieldDescribe = FieldDescribeFactory.newInstance(fieldJson);
        fieldDescribe.setDescribeApiName("multiple_object_api_name");
        toAddFieldRuleList.add(mtFieldDescribe);

        IFieldDescribe longTextField = MarketingInitUtil.getLongTextField("multiple_json", "多对象促销信息");// ignoreI18n
        longTextField.setRequired(false);
        toAddFieldRuleList.add(longTextField);
        MarketingInitUtil.addFieldDescribe(policyRuleDescribe, toAddFieldRuleList);

        asyncTaskProducer.create(BIZ, user.getTenantId());
    }

    private void addObjectApiName(List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList,List<FindMarketingGrayObj.ResultInfo> mlist) {
        List<ISelectOption> selectOptions = Lists.newArrayList();
        for (FindMarketingGrayObj.ResultInfo resultInfo : mlist) {
            selectOptions.add(new SelectOption(resultInfo.getLabel(), resultInfo.getApiName(), ""));
        }
        SelectManyFieldDescribe objectDescribe = SelectManyFieldDescribeBuilder.builder().apiName("multiple_object_api_name").selectOptions(selectOptions).label("促销对象")// ignoreI18n
                .build();
        objectDescribe.setActive(true);
        objectDescribe.setIndex(true);
        objectDescribe.setRequired(false);
        toAddFieldList.add(objectDescribe);
        FieldLayoutPojo objectLayout = enterpriseInitService.getFieldLayoutPojo(SystemConstants.RenderType.SelectMany.renderType, false, false);
        fieldLayoutTupleList.add(Tuple.of(objectDescribe, objectLayout));
    }

}
