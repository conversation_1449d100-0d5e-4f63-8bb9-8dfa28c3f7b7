package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.ProductCategoryBizService;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.crm.sfa.utilities.validator.ProductValidator;
import com.facishare.crm.util.StringUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import joptsimple.internal.Strings;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/11/23 10:05
 */
public class ProductIncrementUpdateAction extends StandardIncrementUpdateAction {
    private final ProductCategoryBizService productCategoryBizService = SpringUtil.getContext().getBean(ProductCategoryBizService.class);
    private ProductCategoryV2Validator productCategoryV2Validator = SpringUtil.getContext().getBean(ProductCategoryV2Validator.class);
    private boolean isSpuOpen;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        isSpuOpen = SFAConfigUtil.isSpuOpen(actionContext.getTenantId());

        if (!isSpuOpen) {
            if (StringUtil.isNotEmpty(objectData.get("batch_sn", String.class))) {
                ProductValidator.batchSNFieldValidate(actionContext.getUser(),  objectDescribe, objectData, dbObjectData);
            }
        }
        productCategoryV2Validator.checkCategoryParamOfProduct(actionContext.getTenantId(), objectDescribe, arg.getData());
        validateFields();
        ObjectDataDocument objectDataDocument = arg.getData();
        if (objectDataDocument.containsKey(ProductConstants.Field.STATUS.getApiName())) {
            String timeApiName = Strings.EMPTY;
            if (ProductConstants.Status.ON.getStatus().equals(objectData.get(ProductConstants.Field.STATUS.getApiName()))) {
                timeApiName = "on_shelves_time";
            }
            if (ProductConstants.Status.OFF.getStatus().equals(objectData.get(ProductConstants.Field.STATUS.getApiName()))) {
                timeApiName = "off_shelves_time";
            }
            if (!Strings.isNullOrEmpty(timeApiName)) {
                SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                String str = dateformat.format(System.currentTimeMillis());
                try {
                    objectData.set(timeApiName, String.valueOf(dateformat.parse(str).getTime()));
                } catch (ParseException e) {
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NPRODUCT_VALIDATOR_DATE_PARSE_FAILED));
                }
            }
        }
        productCategoryBizService.handleCategoryMappingCategoryId(actionContext.getUser(), dbObjectData,
                objectDataDocument.toObjectData(), SFAPreDefineObject.Product.getApiName());
    }

    private void validateFields() {
        if (!SFAConfigUtil.isSpuOpen(actionContext.getTenantId())) {
            return;
        }
        List<String> relatedFields = Lists.newArrayList("category", "product_category_id", "unit", "product_line", "spu_id", "shop_category_id");
        for (String field : relatedFields) {
            if (objectData.containsField(field)) {
                String newValue = objectData.get(field, String.class);
                String oldValue = dbObjectData.get(field, String.class);
                if (!Objects.equals(newValue, oldValue)) {
                    throw new ValidateException(I18N.text("sfa.sku.increment.update.fields.warn"));
                }
            }
        }
    }
}
