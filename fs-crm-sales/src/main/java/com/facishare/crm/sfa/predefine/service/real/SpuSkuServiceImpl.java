package com.facishare.crm.sfa.predefine.service.real;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.model.MultiUnitData;
import com.facishare.crm.sfa.predefine.service.model.MultiUnitListData;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.utilities.common.convert.ConvertUtil;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.SpuSkuConstants;
import com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils;
import com.facishare.crm.sfa.utilities.util.SkuUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.RocketMQMessageSender;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ObjectLockStatus;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.paas.metadata.util.IdUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel.Filed.SHOP_CATEGORY_ID;
import static com.facishare.crm.sfa.utilities.constant.ProductConstants.MAX_SKU_LIMIT_OF_SAME_GROUP;

/**
 * Created by luxin on 2018/11/13.
 */

@Service
@Slf4j
public class SpuSkuServiceImpl implements SpuSkuService {
    @Autowired
    private MetaDataActionService metaDataActionService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private RocketMQMessageSender addProMqMessageSender;
    @Autowired
    private GDSHandler gdsHandler;
    @Autowired
    private MultiUnitService multiUnitService;
    @Autowired
    private SpecialTableMapper specialTableMapper;
    @Autowired
    private LogFacade logFacade;


    @Override
    @Transactional
    public SaveMasterAndDetailData.Result saveSpuAndSkuRelation(User user, List<IObjectData> skuList, SaveMasterAndDetailData.Arg saveArg) {
        if (skuList.size() > MAX_SKU_LIMIT_OF_SAME_GROUP) {
            throw new ValidateException(I18N.text("product.sku_data_more_then_limit"));
        }
        checkBatchSn(saveArg.getMasterObjectData());
        // 为导入增加的逻辑
        SaveMasterAndDetailData.Result saveResult;
        if (saveArg.getMasterObjectData().get("spu_exist") != null) {
            saveResult = SaveMasterAndDetailData.Result.builder().masterObjectData(saveArg.getMasterObjectData()).detailObjectData(saveArg.getDetailObjectData()).build();
        } else {
            // 方式发送 mq 过大
            saveArg.getMasterObjectData().set("sku", Lists.newArrayListWithCapacity(0));
            saveResult = metaDataActionService.saveMasterAndDetailData(user, saveArg);
        }

        IObjectData spuData = saveResult.getMasterObjectData();
        if (spuData == null || spuData.getId() == null) {
            throw new RuntimeException();
        }

        List<IObjectData> skuDataList = batchSaveSku(user, skuList, spuData);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                asynchronousCreatePriceBookProduct(user, skuDataList);
            }
        });
        return saveResult;
    }

    @Override
    @Transactional
    public SaveMasterAndDetailData.Result saveMultiUnitSpuAndSku(User user, List<IObjectData> skuDataList, List<MultiUnitData.MultiUnitItem> multiUnitItems, SaveMasterAndDetailData.Arg saveArg) {
        checkBatchSn(saveArg.getMasterObjectData());
        SaveMasterAndDetailData.Result result = saveSpuAndSkuRelation(user, skuDataList, saveArg);
        multiUnitService.batchSaveMultiUnit(user, saveArg.getMasterObjectData(), skuDataList, multiUnitItems);

        return result;
    }

    @Override
    @Transactional
    public List<IObjectData> batchSaveSku(User user, List<IObjectData> skuList, IObjectData spuData) {
        if (CollectionUtils.isEmpty(skuList)) {
            throw new SFABusinessException(SFAErrorCode.SKU_NO_HAVE_SPEC);
        }

        processProductStatusTimeField(skuList);
        List<IObjectData> skuDataList;
        skuList.forEach(o -> o.set("relevant_team", spuData.get("relevant_team")));
        // 是否有规格
        if ((Objects.equals(spuData.get("is_spec"), true))) {
            Map<String, List<Map<String, String>>> skuNameSkuSpecRelationMapping = processingSku(user, skuList, spuData);
            skuDataList = metaDataActionService.bulkSaveObjectData(skuList, user);
            if (CollectionUtils.isEmpty(skuDataList)) {
                throw new SFABusinessException(SFAErrorCode.SKU_SAVE_FAILED);
            }
            // 保存关系表
            batchSaveSkuSpecRelation(user, skuNameSkuSpecRelationMapping, skuDataList);
        } else {
            skuDataList = batchSaveNoSpecSku(user, skuList, spuData);
        }
        for (IObjectData sku : skuDataList) {
            sku.set("status_flag", ProductConstants.SkuEditStatus.ADD.getStatus());
        }

        return skuDataList;
    }


    /**
     * 处理数据的上下架时间
     *
     * @param skuList
     */
    private void processProductStatusTimeField(List<IObjectData> skuList) {
        long productStatusUpdateTime = System.currentTimeMillis();
        skuList.forEach(o -> {
            String productStatus = o.get("product_status", String.class);

            if (ProductConstants.Status.OFF.getStatus().equals(productStatus)) {
                o.set("off_shelves_time", productStatusUpdateTime);
            } else {
                o.set("on_shelves_time", productStatusUpdateTime);
            }
        });
    }


    /**
     * 将spu和sku公共部分的值填充到sku上，sku填充spuId，将产品 规格id&规则值id处理成map供后续使用.
     * 如果产品是更换规格值,还需要把对应的关联表删除
     *
     * @return 每个 产品->规格id&规则值id数组
     */
    @Override
    public Map<String, List<Map<String, String>>> processingSku(User user, List<IObjectData> skuList, IObjectData spuData) {
        Set<String> specIds = Sets.newHashSet();
        Set<String> specValueIds = Sets.newHashSet();
        skuList.forEach(o -> {
            o.set("spu_id", spuData.getId());
            o.set("category", spuData.get("category"));
            o.set(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, ObjectDataUtils.getValueOrDefault(spuData, ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, ""));
            o.set(SHOP_CATEGORY_ID, spuData.get(SHOP_CATEGORY_ID));
            o.set("product_line", spuData.get("product_line"));
            o.set("unit", spuData.get("unit"));
            if (CollectionUtils.isEmpty(o.getOwner())) {
                o.set("owner", spuData.getOwner());
            }
            o.set("batch_sn", spuData.get("batch_sn"));
            o.set("is_spec", true);
            o.set("record_type", spuData.get("record_type"));
            o.set(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
            o.set("store_id", spuData.get("store_id"));
            o.set("mall_category_id", spuData.get("mall_category_id"));
            SkuUtils.fillSpecsAndSpecValuesBySkuData(specIds, specValueIds, o);
        });

        // 获取 规格id，规格名称 映射关系
        Map<String, String> dbSpecIdSpecNameMapping = metaDataFindService.findObjectDataByIds(
                user.getTenantId(),
                Lists.newArrayList(specIds),
                Utils.SPECIFICATION_API_NAME
        ).stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));

        // 获取 规格值id，规格值数据 映射关系
        Map<String, IObjectData> specValueSpecDataMapping = metaDataFindService.findObjectDataByIds(
                user.getTenantId(),
                Lists.newArrayList(specValueIds),
                Utils.SPECIFICATION_VALUE_API_NAME
        ).stream().collect(Collectors.toMap(IObjectData::getId, o -> o));

        Map<String, List<Map<String, String>>> skuNameSkuSpecRelationMapping = Maps.newHashMap();
        List<Set<String>> usedSpecValueIdsList = Lists.newArrayListWithExpectedSize(skuList.size());

        List<String> targetSpecValueIds = Lists.newArrayList();
        List<String> changeSpecValueSkuId = Lists.newArrayList();

        for (IObjectData skuData : skuList) {
            List<Map<String, String>> specSpecValues = skuData.get("spec_and_value", List.class);
            specSpecValues.sort(Comparator.comparing(o -> o.get("order_field")));

            final StringBuilder skuNameBuilder = new StringBuilder(spuData.getName() + "[");
            final StringBuilder skuSpecSpecValueNameBuilder = new StringBuilder();
            Object statusFlag = skuData.get("status_flag");
            boolean isChangeSpecValue = Objects.equals(statusFlag, ProductConstants.SkuEditStatus.SKU_EDIT_STATUS.getStatus());
            Set<String> useSpecValueIds = Sets.newHashSet();
            specSpecValues.forEach(o -> {
                String specId = o.get("spec_id");
                String specValueId = o.get("spec_value_id");
                String dbSpecName = dbSpecIdSpecNameMapping.get(specId);
                IObjectData specValueData = specValueSpecDataMapping.get(specValueId);

                // 校验更换规格值的规格值名称是否和数据库中相符
                if (isChangeSpecValue && Objects.equals(o.get("change_spec_value"), true)) {
                    String inputSpecValueName = o.get("spec_value_name");
                    targetSpecValueIds.add(specValueId);

                    if (!Objects.equals(specValueData.getName(), inputSpecValueName)) {
                        //throw new ValidateException("规格值名称已经变更");
                        throw new ValidateException(I18N.text("sfa.guige.name.changed"));
                    }
                }

                if (StringUtils.isNotBlank(dbSpecName)
                        && specValueData != null
                        && StringUtils.isNotBlank(specValueData.getName())
                        && specId.equals(specValueData.get("specification_id"))
                ) {
                    skuNameBuilder.append(specValueData.getName()).append("-");
                    skuSpecSpecValueNameBuilder.append(dbSpecName).append(":").append(specValueData.getName()).append(";");
                }

                if (!Strings.isNullOrEmpty(specValueId)) {
                    useSpecValueIds.add(specValueId);
                }
            });

            if (skuSpecSpecValueNameBuilder.length() < 1) {
                throw new ValidateException(I18N.text("product.specification.validate.msg"));
            }

            if (CollectionUtils.isNotEmpty(useSpecValueIds)) {
                usedSpecValueIdsList.add(useSpecValueIds);
            }

            if (Strings.isNullOrEmpty(skuData.getName()) || skuData.getName().equals(spuData.getName())) {
                skuData.set("name", skuNameBuilder.toString().substring(0, skuNameBuilder.toString().length() - 1) + "]");
            }
            deleteLastChar(skuSpecSpecValueNameBuilder);
            skuData.set("product_spec", skuSpecSpecValueNameBuilder.toString());

            // 作废和编辑的数据,关联关系表无需变更
            if (!Objects.equals(ProductConstants.SkuEditStatus.INVALID.getStatus(), statusFlag)) {
                preprocessingSkuSpecRelationData(user.getTenantId(), spuData.getId(), skuNameSkuSpecRelationMapping, skuData);
                if (Objects.equals(ProductConstants.SkuEditStatus.SKU_EDIT_STATUS.getStatus(), statusFlag) ||
                        Objects.equals(ProductConstants.SkuEditStatus.ADD_SPEC.getStatus(), statusFlag) ||
                        Objects.equals(ProductConstants.SkuEditStatus.EDIT.getStatus(), statusFlag)) {
                    changeSpecValueSkuId.add(skuData.getId());
                }
            }
        }
        validateSpecValueExistSubset(usedSpecValueIdsList);
        checkChangeSpecValueUsed(user, spuData, targetSpecValueIds);
        deleteChangeSpecValueSpuSkuRelation(user, changeSpecValueSkuId);
        return skuNameSkuSpecRelationMapping;
    }

    /**
     * 删除最后一个字符
     *
     * @param skuSpecSpecValueNameBuilder sku规范规范值名称生成器
     */
    private void deleteLastChar(StringBuilder skuSpecSpecValueNameBuilder) {
        int length = skuSpecSpecValueNameBuilder.length();
        if (length > 0 && skuSpecSpecValueNameBuilder.charAt(length - 1) == ';') {
            //skuSpecSpecValueNameBuilder 去掉最后一个分号
            skuSpecSpecValueNameBuilder.deleteCharAt(length - 1);
        }
    }

    //规格值不允许出现包含关系
    private void validateSpecValueExistSubset(List<Set<String>> usedSpecValueIds) {
        if (CollectionUtils.isEmpty(usedSpecValueIds)) {
            return;
        }
        for (int i = 0; i < usedSpecValueIds.size() - 1; i++) {
            Set<String> currentSpecValueIds = Sets.newHashSet(usedSpecValueIds.get(i));
            int currentSize = currentSpecValueIds.size();
            for (int j = i + 1; j < usedSpecValueIds.size(); j++) {
                Set<String> otherSpecValueIds = Sets.newHashSet(usedSpecValueIds.get(j));
                int otherSize = otherSpecValueIds.size();
                otherSpecValueIds.retainAll(currentSpecValueIds);
                if (currentSize == otherSpecValueIds.size() || otherSize == otherSpecValueIds.size()) {
                    throw new ValidateException(I18N.text("spu.spec_value.overlap"));
                }
            }
        }
    }

    /**
     * 删除被修改规格值的 产品商品规格规格值关联表数据
     *
     * @param user
     * @param changeSpecValueSkuId
     */
    private void deleteChangeSpecValueSpuSkuRelation(User user, List<String> changeSpecValueSkuId) {
        if (!changeSpecValueSkuId.isEmpty()) {
            String tmpSql = "delete from spu_sku_spec_value_relate\n" +
                    "where tenant_id = '%s'\n" +
                    "  and sku_id in('%s');";

            String sql = String.format(tmpSql, user.getTenantId(), ConcatenateSqlUtils.COLLECTION_JOINER.join(changeSpecValueSkuId));
            try {
                objectDataService.deleteBySql(user.getTenantId(), sql);
            } catch (MetadataServiceException e) {
                throw new MetaDataBusinessException(e);
            }
        }
    }

    /**
     * 校验目标改变的规格值,是否被使用了
     *
     * @param user
     * @param spuData
     * @param targetSpecValueIds
     */
    private void checkChangeSpecValueUsed(User user, IObjectData spuData, List<String> targetSpecValueIds) {
        if (!targetSpecValueIds.isEmpty()) {
            String tmpSql = "select 1\n" +
                    "from spu_sku_spec_value_relate r\n" +
                    "       inner join biz_product p on r.tenant_id = p.tenant_id and r.spu_id = p.spu_id and r.sku_id = p.id\n" +
                    "where r.tenant_id = '%s'\n" +
                    "  and r.spu_id = '%s'\n" +
                    "  and r.is_deleted = 0\n" +
                    "  and p.is_deleted in (0, 1)\n" +
                    "  and spec_value_id in('%s');";

            String format = String.format(tmpSql, user.getTenantId(), spuData.getId(), ConcatenateSqlUtils.COLLECTION_JOINER.join(targetSpecValueIds));
            try {
                List<Map> result = objectDataService.findBySql(user.getTenantId(), format);
                if (!result.isEmpty()) {
                    //throw new ValidateException("被更换的规格值已经被使用,退出界面重新编辑");
                    throw new ValidateException(I18N.text("sfa.changed.guige.used.exit.update.oncemore"));
                }
            } catch (MetadataServiceException e) {
                throw new MetaDataBusinessException(e);
            }
        }
    }


    /**
     * 异步发送 mq 用来新建价目表产品
     *
     * @param user
     * @param skuDataList
     */
    @Override
    public void asynchronousCreatePriceBookProduct(User user, List<IObjectData> skuDataList) {
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        //新建的产品同步到标准价目表
        List<IObjectData> skuDataListCopy = ObjectDataExt.copyList(skuDataList);

        parallelTask.submit(() -> sendMessage(skuDataListCopy, user));
        parallelTask.run();
    }


    @Override
    public List<String> findNeedFilterProductId(User user) {
        List<Map> productDataList = specialTableMapper.setTenantId(user.getTenantId())
                .findBySql(String.format(SpuSkuConstants.FILTER_PRODUCT_WITH_PRODUCT_CONSTRAINT_LINES, user.getTenantId()));
        if (CollectionUtils.isEmpty(productDataList)) {
            return Lists.newArrayList();
        }

        return productDataList.stream().filter(o -> StringUtils.isNotEmpty(o.get("up_product_id").toString()))
                .map(o -> o.get("up_product_id").toString()).collect(Collectors.toList());
    }

    @Override
    public List<IObjectData> findAllSkusUnderSpusByIsDeleteStatusNotWithDataPermiss(User user, List<String> spuIds, List<String> deleteStatusList) {
        if (CollectionUtils.isEmpty(spuIds) || CollectionUtils.isEmpty(deleteStatusList)) {
            return Lists.newArrayListWithCapacity(0);
        }

        IObjectDescribe productDesc = describeLogicService.findObject(user.getTenantId(), Utils.PRODUCT_API_NAME);

        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "spu_id", spuIds);
        SearchUtil.fillFilterIn(filters, "is_deleted", deleteStatusList);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setPermissionType(0);
        query.setLimit(1000);
        query.setOffset(0);

        QueryResult<IObjectData> skuQueryResult = metaDataFindService.findBySearchQueryWithDeleted(user, productDesc, query);
        return skuQueryResult.getData();
    }

    @Override
    public Boolean isExistSkuUnderAnySpuByIsDeleteStatusNotWithDataPermiss(User user, List<String> spuIds, List<String> statusList) {
        return CollectionUtils.isNotEmpty(findAllSkusUnderSpusByIsDeleteStatusNotWithDataPermiss(user, spuIds, statusList));
    }

    @Override
    @Transactional
    public void updateSpuAndHandleSku(IObjectData objectData, User user, List<IObjectData> addSkuDataList, List<IObjectData> invalidSkuDataList, List<IObjectData> editedSkuDataList) {
        checkBatchSn(objectData);
        ObjectDataExt dataExt = processSpuLifeStatusFields(objectData);

        // spu的更新
        metaDataActionService.batchUpdate(Lists.newArrayList(dataExt.getObjectData()), user);

        if ((Objects.equals(objectData.get("is_spec"), true))) {
            Map<String, List<Map<String, String>>> changeSpecValueSkuName2SpecInfo = processingSku(user, editedSkuDataList, objectData);
            // 更换规格值的关联关系表保存
            batchSaveSkuSpecRelation(user, changeSpecValueSkuName2SpecInfo, editedSkuDataList);
        }

        //----------------------------------编辑 sku start
        Map<String, IObjectData> skuIdBeforeUpdateDataMap = Maps.newHashMap();
        List<String> skuIds = Lists.newArrayList();

        editedSkuDataList.forEach(o -> {
            String skuId = o.getId();
            if (skuId == null) {
                throw new ValidateException(I18N.text("not.have.sku.spu.edit.message"));
            }
            skuIds.add(skuId);
        });

        IObjectDescribe skuDescribe = describeLogicService.findObject(user.getTenantId(), Utils.PRODUCT_API_NAME);
        List<IObjectData> beforeUpdateSkuDataList;
        if (CollectionUtils.isNotEmpty(skuIds)) {
            beforeUpdateSkuDataList = metaDataFindService.findObjectDataByIds(user.getTenantId(), skuIds, Utils.PRODUCT_API_NAME);
            beforeUpdateSkuDataList.forEach(o -> skuIdBeforeUpdateDataMap.put(o.getId(), o));

            long productStatusUpdateTime = System.currentTimeMillis();
            editedSkuDataList.forEach(o -> {
                ConvertUtil.convertOrderFieldValue2Number(o);
                String productStatus = o.get("product_status", String.class);
                if (StringUtils.isEmpty(productStatus)) {
                    return;
                }
                IObjectData skuBeforeUpdateData = skuIdBeforeUpdateDataMap.get(o.getId());
                if (skuBeforeUpdateData == null) {
                    return;
                }
                String productBeforeUpdateStatus = skuBeforeUpdateData.get("product_status", String.class);

                if (!productStatus.equals(productBeforeUpdateStatus)) {
                    if (ProductConstants.Status.OFF.getStatus().equals(productStatus)) {
                        o.set("off_shelves_time", productStatusUpdateTime);
                    } else {
                        o.set("on_shelves_time", productStatusUpdateTime);
                    }
                }
            });

            // 更新被编辑的sku
            batchUpdateSku(user, editedSkuDataList);
            logFacade.dataModifyLog(user, skuDescribe, beforeUpdateSkuDataList, editedSkuDataList);
        }
        //----------------------------------编辑 sku end

        // 批量作废数据
        metaDataActionService.bulkInvalid(invalidSkuDataList, user);
        logFacade.batchDataInvalidLog(user, skuDescribe, invalidSkuDataList);

        // 新增sku 数据处理
        if (CollectionUtils.isNotEmpty(addSkuDataList)) {
            batchSaveSku(user, addSkuDataList, objectData);
            logFacade.batchDataAddLog(user, skuDescribe, addSkuDataList);
            asynchronousCreatePriceBookProduct(user, addSkuDataList);
        }
    }

    @Override
    @Transactional
    public void updateMultiUnitSpuAndHandleSku(IObjectData spuData, User user, List<IObjectData> addSkuDataList, List<IObjectData> invalidSkuDataList,
                                               List<IObjectData> editedSkuDataList, MultiUnitListData multiUnitListData) {
        updateSpuAndHandleSku(spuData, user, addSkuDataList, invalidSkuDataList, editedSkuDataList);

        addSkuDataList.addAll(editedSkuDataList);
        multiUnitService.batchUpdateMultiUnit(user, spuData, addSkuDataList, multiUnitListData);
    }

    @Override
    @Transactional
    public void updateMultiUnitSku(User user, IObjectData skuData, MultiUnitListData multiUnitListData) {
        metaDataActionService.batchUpdate(Lists.newArrayList(skuData), user);
        multiUnitService.batchUpdateMultiUnit(user, skuData, multiUnitListData);
    }


    @Override
    public void updateSpuAndHandleSku4UpdateInsert(IObjectData spuData, User user, List<IObjectData> editedSkuDataList) {
        checkBatchSn(spuData);
        ObjectDataExt dataExt = processSpuLifeStatusFields(spuData);

        // spu的更新
        metaDataActionService.batchUpdate(Lists.newArrayList(dataExt.getObjectData()), user);
        List<IObjectData> updateSkuDatas = Lists.newArrayList();
        editedSkuDataList.forEach(o -> {
            ObjectDataExt dataExt1 = ObjectDataExt.of(Maps.newHashMap(ObjectDataExt.of(o).toMap()));
            dataExt1.remove(DBRecord.VERSION);
            dataExt1.remove(ObjectLockStatus.LOCK_STATUS_API_NAME);
            dataExt1.remove(ObjectLifeStatus.LIFE_STATUS_API_NAME);
            dataExt1.remove(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME);

            if (spuData.get("category") != null) {
                dataExt1.set("category", spuData.get("category"));
                o.set("category", spuData.get("category"));
            }
            if (spuData.get(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID) != null) {
                dataExt1.set(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, spuData.get(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID));
                o.set(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, spuData.get(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID));
            }
            if (spuData.get(SHOP_CATEGORY_ID) != null) {
                dataExt1.set(SHOP_CATEGORY_ID, spuData.get(SHOP_CATEGORY_ID));
                o.set(SHOP_CATEGORY_ID, spuData.get(SHOP_CATEGORY_ID));
            }

            if (spuData.get("product_line") != null) {
                dataExt1.set("product_line", spuData.get("product_line"));
                o.set("product_line", spuData.get("product_line"));
            }

            if (spuData.get("unit") != null) {
                dataExt1.set("unit", spuData.get("unit"));
                o.set("unit", spuData.get("unit"));
            }
            if (spuData.get("batch_sn") != null) {
                dataExt1.set("batch_sn", spuData.get("batch_sn"));
            }
            if (spuData.get(ProductConstants.STORE_ID) != null) {
                dataExt1.set(ProductConstants.STORE_ID, spuData.get(ProductConstants.STORE_ID));
                o.set(ProductConstants.STORE_ID, spuData.get(ProductConstants.STORE_ID));
            }
            if (spuData.get(ProductConstants.MALL_CATEGORY_ID) != null) {
                dataExt1.set(ProductConstants.MALL_CATEGORY_ID, spuData.get(ProductConstants.MALL_CATEGORY_ID));
                o.set(ProductConstants.MALL_CATEGORY_ID, spuData.get(ProductConstants.MALL_CATEGORY_ID));
            }
            dataExt1.remove("owner");

            if (!(Objects.equals(spuData.get("is_spec"), true)) && spuData.get("standard_price") != null) {
                dataExt1.set("price", spuData.get("standard_price"));
            }
            updateSkuDatas.add(dataExt1.getObjectData());
        });

        // 更新被编辑的sku
        metaDataActionService.batchUpdate(updateSkuDatas, user);
    }

    @Override
    public List<Map> findHaveSpecSkuIdInfo(User user, List<String> productIds) {
        //language=PostgresPLSQL
        String tmpFindHaveSpecSkuInfoSql = "select k.id as _id\n" +
                "from biz_product as k,\n" +
                "     stand_prod_unit as p\n" +
                "where p.tenant_id = '%s'\n" +
                "  and k.tenant_id = '%s'\n" +
                "  and p.is_spec = true\n" +
                "  and k.id in('%s')\n" +
                "  and p.id = k.spu_id;";
        String findHaveSpecSkuInfoSql = String.format(tmpFindHaveSpecSkuInfoSql, user.getTenantId(), user.getTenantId(), ConcatenateSqlUtils.COLLECTION_JOINER.join(productIds));
        try {
            return objectDataService.findBySql(user.getTenantId(), findHaveSpecSkuInfoSql);
        } catch (MetadataServiceException e) {
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR, e);
        }
    }

    /**
     * 处理 spu 数据生命状态相关的字段
     *
     * @param spuData
     * @return
     */
    private ObjectDataExt processSpuLifeStatusFields(IObjectData spuData) {
        ObjectDataExt dataExt = ObjectDataExt.of(Maps.newHashMap(ObjectDataExt.of(spuData).toMap()));
        dataExt.remove(IObjectData.VERSION);
        dataExt.remove(ObjectLockStatus.LOCK_STATUS_API_NAME);
        dataExt.remove(ObjectLifeStatus.LIFE_STATUS_API_NAME);
        dataExt.remove(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME);
        dataExt.remove("owner");
        return dataExt;
    }


    private void batchUpdateSku(User user, List<IObjectData> editedSkuDataList) {
        try {
            metaDataActionService.batchUpdate(editedSkuDataList, user);
        } catch (MetaDataBusinessException e) {
            if (e.getErrorCode() == 201112013) {
                // 请先填写无权限数据的必填项
                throw new MetaDataBusinessException(e.getMessage(), e);
            } else {
                throw e;
            }
        }
    }


    /**
     * 批量保存没有规则的sku
     */
    private List<IObjectData> batchSaveNoSpecSku(User user, List<IObjectData> skuList, IObjectData spuData) {
        skuList.forEach(o -> {
            o.set("spu_id", spuData.getId());
            o.set("name", spuData.getName());
            o.set("category", spuData.get("category"));
            o.set(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID, spuData.get(ProductCategoryModel.Filed.PRODUCT_CATEGORY_ID));
            o.set(SHOP_CATEGORY_ID, spuData.get(SHOP_CATEGORY_ID));
            o.set("product_line", spuData.get("product_line"));
            o.set("unit", spuData.get("unit"));
            o.set("batch_sn", spuData.get("batch_sn"));
            o.set("price", spuData.get("standard_price"));
            o.set("owner", spuData.get("owner"));
            o.set("record_type", spuData.get("record_type"));
            o.set(ProductConstants.STORE_ID, spuData.get(ProductConstants.STORE_ID));
            o.set(ProductConstants.MALL_CATEGORY_ID, spuData.get(ProductConstants.MALL_CATEGORY_ID));
            o.set(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
        });
        List<IObjectData> skuDataList = metaDataActionService.bulkSaveObjectData(skuList, user);
        skuDataList.stream().findFirst().ifPresent(x -> {
            spuData.set("create_Product_id", x.getId());
        });
        return skuDataList;
    }


    /**
     * 批零保存 商品&产品&规格&规格值 关联关系 数据
     *
     * @param skuNameSkuSpecRelationMapping 产品名称和
     */
    private void batchSaveSkuSpecRelation(User user, Map<String, List<Map<String, String>>> skuNameSkuSpecRelationMapping, List<IObjectData> skuDataList) {
        List<IObjectData> skuSpecRelateDataList = Lists.newArrayList();
        skuDataList.stream().filter(skuData -> skuNameSkuSpecRelationMapping.size() != 0).forEach(skuData -> {
            List<Map<String, String>> skuSpecRelationList = skuNameSkuSpecRelationMapping.get(skuData.getId());
            String skuDataId = skuData.getId();
            if (skuSpecRelationList != null && skuData.getId() != null) {
                skuSpecRelationList.forEach(o -> {
                    o.put("sku_id", skuDataId);
                    skuSpecRelateDataList.add(new ObjectData(o));
                });
            }
        });
        skuSpecRelateDataList.removeIf(r -> Strings.isNullOrEmpty(r.get("spec_value_id", String.class)));
        if (CollectionUtils.isNotEmpty(skuSpecRelateDataList)) {
            metaDataActionService.bulkSaveObjectData(skuSpecRelateDataList, user);
        }
    }


    /**
     * 预处理 商品&产品&规格&规格值 关联关系数据
     *
     * @param spuId                         商品的id
     * @param skuNameSkuSpecRelationMapping 商品&产品&规格&规格值 关系集合
     * @param skuData                       产品数据
     */
    private void preprocessingSkuSpecRelationData(String tenantId, String spuId, Map<String, List<Map<String, String>>> skuNameSkuSpecRelationMapping, IObjectData skuData) {
        List<Map<String, String>> specSpecValues = skuData.get("spec_and_value", List.class);
        String skuId = skuData.getId();
        if (Strings.isNullOrEmpty(skuId)) {
            skuId = IdUtil.generateId();
            skuData.setId(skuId);
        }

        for (Map<String, String> specSpecValue : specSpecValues) {
            Map<String, String> skuSpecMapping = Maps.newHashMap();
            skuSpecMapping.put("spu_id", spuId);
            skuSpecMapping.put("spec_id", specSpecValue.get("spec_id"));
            skuSpecMapping.put("spec_value_id", specSpecValue.get("spec_value_id"));
            skuSpecMapping.put("order_field", specSpecValue.get("order_field"));
            skuSpecMapping.put("object_describe_api_name", "SpuSkuSpecValueRelateObj");
            skuSpecMapping.put("object_describe_id", "XXXX");
            skuSpecMapping.put(Tenantable.TENANT_ID, tenantId);

            List<Map<String, String>> skuSpecRelationList = skuNameSkuSpecRelationMapping.get(skuId);

            if (skuSpecRelationList == null) {
                skuSpecRelationList = Lists.newArrayList(skuSpecMapping);
                skuNameSkuSpecRelationMapping.put(skuId, skuSpecRelationList);
            } else {
                skuSpecRelationList.add(skuSpecMapping);
            }
        }
    }

    private void checkBatchSn(IObjectData spuData) {
        String batchSn = spuData.get("batch_sn", String.class);
        // 开启序列号
        if ("3".equals(batchSn)) {
            Boolean isMultipleUnit = spuData.get("is_multiple_unit", Boolean.class);
            if (Objects.equals(Boolean.TRUE, isMultipleUnit)) {
                throw new ValidateException(I18N.text("spu.sku.batch.sn.msg"));
            }
        }
    }

    private void sendMessage(List<IObjectData> objectDataList, User user) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }

        String ea = gdsHandler.getEAByEI(user.getTenantId());
        for (IObjectData objectData : objectDataList) {
            log.debug("send action message,action:{},tenantId:{},apiName:{},dataId:{}", "Add",
                    objectData.getTenantId(), objectData.getDescribeApiName(), objectData.getId());

            byte[] bytes = ObjectDataExt.of(objectData).toMessageData(ObjectAction.CREATE, ea, user.getUpstreamOwnerIdOrUserId());
            if (bytes != null) {
                addProMqMessageSender.sendMessage(bytes);
            }
        }
    }

    public List<IObjectData> findProductData(String tenantId, List<String> spuIdList) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        com.facishare.crm.sfa.utilities.common.convert.SearchUtil.fillFilterIn(filters, "spu_id", spuIdList);
        query.setFilters(filters);
        query.setLimit(2000);
        query.setOffset(0);
        query.setPermissionType(0);
        QueryResult<IObjectData> productDataResult = metaDataFindService.findBySearchQueryIgnoreAll(new User(tenantId, "-10000"), Utils.PRODUCT_API_NAME, query);
        return productDataResult.getData();
    }
}
