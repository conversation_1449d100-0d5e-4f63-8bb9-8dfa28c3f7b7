package com.facishare.crm.sfa.predefine.service.ai.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.service.ai.Util.I18NAIUtil;
import com.facishare.crm.sfa.predefine.service.ai.enums.OpTypeEnum;
import com.facishare.crm.sfa.predefine.service.ai.model.Agent;
import com.facishare.crm.sfa.predefine.service.ai.service.AbstractAgentServiceImpl;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/4/13 13:35
 * @description: 营销互动摘要
 * @IgnoreI18n or IgnoreI18nFile or @IgnoreI18nFile
 */
@Component
@Slf4j
public class LeadsMarketingSummary extends AbstractAgentServiceImpl {


    private static final Pattern SAFE_PATTERN = Pattern.compile("\\s++\\d++\\s*+(秒|分钟)\\s*+浏览至\\d++%$");


    @Override
    public String getApiName() {
        return "LeadsMarketingSummary";
    }

    @Override
    public String getPrompt() {
        return "";
    }

    @Override
    public Agent.Result getObjectData(ServiceContext context, Agent.Arg arg) {
        Agent.Result result = super.getObjectData(context, arg);
        // 从DB查
        IObjectData iObjectData = selectInsightsResults(context.getTenantId(), arg.getObjectApiName(), arg.getObjectId(), getApiName());
        if (OpTypeEnum.REFRESH.getCode().equals(arg.getOpType()) && iObjectData != null) {
            // 刷新操作，并且数据库有数据
            List<ObjectDataDocument> retList = JSON.parseArray(JSON.toJSONString(iObjectData.get("insights_result")), ObjectDataDocument.class);
            result.setUpdateTime(Long.parseLong(iObjectData.get("last_modified_time").toString()));
            result.setDataList(retList);
        } else {
            // 重新生成数据
            List<ObjectDataDocument> dataList;
            try {
                dataList = getDataList(context, arg);
            } catch (Exception e) {
                log.error("LeadsMarketingSummary get data error", e);
                dataList = JSON.parseArray(getDefaultData(), ObjectDataDocument.class);
            }
            // 写库
            if (result.getDataList() == null || OpTypeEnum.REGENERATE.getCode().equals(arg.getOpType())) {
                saveAIRetToDB(context, arg, JSON.toJSONString(dataList));
            }
            result.setDataList(dataList);
            result.setUpdateTime(new Date().getTime());
        }
        result.setSimpleDescribe(createDescribeMap());
        result.setShowFields(createShowFields());
        return result;
    }

    /**
     * {
     * "activity": "活跃时间",
     * "activityTime": "18:00 ~ 20:00 时段",
     * "repeatBrowse": "反复浏览",
     * "repeatBrowseList": [
     * {
     * "title": "共赴电子信息行业一体化协同增长",
     * "url": "",
     * "count": "5 次"
     * },
     * {
     * "title": "共促医疗器械全产业链高质量增长",
     * "url": "",
     * "count": "4 次"
     * },
     * {
     * "title": "2024年中国企业CRM软件国产替代趋势与应用研究报告",
     * "url": "",
     * "count": "3 次"
     * }
     * ]
     * }
     */
    private String getDefaultData() {
        return "[{\n" +
                "    \"activity\": \"" + I18N.text(I18NAIUtil.ACTIVE_TIME) + "\",\n" +
                "    \"activityTime\": \"" + I18N.text(I18NAIUtil.WEEKDAY) + " 00:00 ~ 00:00 " + I18N.text(I18NAIUtil.TIME_INTERVAL) + "\",\n" +
                "    \"repeatBrowse\": \"" + I18N.text(I18NAIUtil.REPEATED_BROWSING) + "\",\n" +
                "    \"repeatBrowseList\": []\n" +
                "}]";
    }

    private List<ObjectDataDocument> getDataList(ServiceContext context, Agent.Arg arg) throws ParseException {
        // 调用远程接口获取新的原始数据
        JSONArray dataArray = getUserMarketingAction(context, arg);
        Map<String, Count> dataMap = new HashMap<>();
        Map<Long, Integer> timeMap = new HashMap<>();
        dataArray.forEach(x -> count(dataMap, timeMap, (JSONObject) x));
        // 对结果排序后取前五个
        List<Count> countList = dataMap.values().stream()
                .sorted(Comparator.comparing(Count::getCount).reversed())
                .limit(5)
                .collect(Collectors.toList());
        if (countList.isEmpty()) {
            return JSON.parseArray(getDefaultData(), ObjectDataDocument.class);
        }
        for (Count c : countList) {
            c.count = c.count + " " + I18N.text(I18NAIUtil.COUNT);
        }
        String timeRes = analyzeTimeMap(timeMap);
        // 封装结果
        Map<String, Object> retMap = Maps.of(
                "activity", I18N.text(I18NAIUtil.ACTIVE_TIME),
                "activityTime", timeRes + I18N.text(I18NAIUtil.TIME_INTERVAL),
                "repeatBrowse", " " + I18N.text(I18NAIUtil.REPEATED_BROWSING),
                "repeatBrowseList", countList
        );
        String jsonString = JSONObject.toJSONString(Lists.newArrayList(retMap));
        return JSON.parseArray(jsonString, ObjectDataDocument.class);
    }

    private List<String> createShowFields() {
        return Lists.newArrayList("activity", "activityTime", "repeatBrowse", "repeatBrowseList");
    }

    private Map<String, Object> createDescribeMap() {
        Map<String, Object> describe = new HashMap<>();
        describe.put("activity", Maps.of("label", I18N.text("sfa.ai.leads.product_intention_active_time")/*活跃时间*/));
        describe.put("activityTime", Maps.of("label", I18N.text("sfa.ai.leads.product_intention_active_time_quantum")/*活跃时间时段*/));
        describe.put("repeatBrowse", Maps.of("label", I18N.text("sfa.ai.leads.product_intention_repeated_browsing")/*反复浏览*/));
        describe.put("repeatBrowseList", Maps.of("label", I18N.text("sfa.ai.leads.product_intention_repeated_browsing.content")/*反复浏览内容*/));
        return describe;
    }

    // 分析时间Map，得到活跃时间信息


    // 计数
    private void count(Map<String, Count> dataMap, Map<Long, Integer> timeMap, JSONObject data) {
        String objectName = data.get("objectName").toString();
        int countNum = Integer.parseInt(data.get("count").toString());
        Long time = Long.parseLong(data.get("createTime").toString());

        java.util.regex.Matcher matcher = SAFE_PATTERN.matcher(objectName);
        if (matcher.find()) {
            objectName = objectName.substring(0, matcher.start());
        }

        // 数据统计次数
        if (dataMap.containsKey(objectName)) {
            Count count = dataMap.get(objectName);
            int allCount = Integer.parseInt(dataMap.get(objectName).count);
            count.setCount(String.valueOf(allCount + countNum));
        } else {

            Count count = data.toJavaObject(Count.class);
            if (StringUtils.isBlank(objectName) && data.get("actionName") != null) {
                count.setTitle(data.get("actionName").toString());
            } else {
                count.setTitle(objectName);
            }
            count.setCount(countNum + "");
            dataMap.put(objectName, count);
        }
        // 时间汇总次数
        if (timeMap.containsKey(time)) {
            timeMap.put(time, timeMap.get(time) + 1);
        } else {
            timeMap.put(time, 1);
        }
    }

    /*
    {"marketingEventId":"61a471e9b8768e000134617d","objectTypeName":"活动","marketingScene":"会议营销","objectType":13,"client":"H5","id":"a75eea6a2a544ae68d905d1b681920bd","spreadChannel":"广告","objectId":"3e7b03bb6d8f4f6cbdfbbe9e9f3cf86f","count":1,"userName":"田先生","actionType":1000038,"createTime":1717576357360,"objectName":"大暑会议","userMarketingId":"8ab0f7408719450ebec898841b65d913","dataSource":"营销通","actionName":"查看活动","marketingEventName":"大暑会议"}
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    static class Count {
        private String title;
        private String url;
        private String count;
        private String objectType;
        private String id;
        private String marketingEventId;
        private String objectTypeName;
        private String marketingScene;
        private String client;
        private String spreadChannel;
        private String objectId;
        private String userName;
        private String actionType;
        private String createTime;
        private String userMarketingId;
        private String dataSource;
        private String actionName;
        private String marketingEventName;
    }

}
