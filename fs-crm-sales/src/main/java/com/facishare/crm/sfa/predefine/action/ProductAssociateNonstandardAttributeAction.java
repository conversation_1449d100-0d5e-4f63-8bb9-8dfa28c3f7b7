package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PRODUCT_RELATED_NONSTANDARD_ATTRIBUTE_LIMIT;

/**
 * Created by luxin on 2017/12/7.
 */
@Slf4j
public class ProductAssociateNonstandardAttributeAction extends PreDefineAction<ProductAssociateNonstandardAttributeAction.Arg, ProductAssociateNonstandardAttributeAction.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("AssociateNonstandardAttribute");
    }

    @Override
    protected List<String> getDataPrivilegeIds(ProductAssociateNonstandardAttributeAction.Arg arg) {
        return arg.getProductIds();
    }

    @Override
    protected ProductAssociateNonstandardAttributeAction.Result doAct(ProductAssociateNonstandardAttributeAction.Arg arg) {
        List<String> nonstandardAttributeIds = arg.getNonstandardAttributeIds();
        if(CollectionUtils.isNotEmpty(nonstandardAttributeIds)) {
            nonstandardAttributeIds = nonstandardAttributeIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(arg.productIds) && CollectionUtils.isNotEmpty(nonstandardAttributeIds)) {
            if (CollectionUtils.isNotEmpty(nonstandardAttributeIds) && nonstandardAttributeIds.size() > 50) {
                throw new ValidateException(I18N.text(SFA_PRODUCT_RELATED_NONSTANDARD_ATTRIBUTE_LIMIT));
            }
            List<String> updateFieldList = new ArrayList<>();
            for (IObjectData product : dataList) { //目前只支持单个产品更新非标属性
                //每次都更新，确保顺序正确，前端每次传的参数都是完整的
                product.set("nonstandard_attribute_ids", nonstandardAttributeIds);
                updateFieldList.add("nonstandard_attribute_ids");

                Map<String, Object> updateDaultValues = Maps.newHashMap();
                if(arg.getDefaultValues() != null && arg.getDefaultValues().get(product.getId())!=null) {
                    updateDaultValues = arg.getDefaultValues().get(product.getId());
                }
                if(updateDaultValues == null) {
                    updateDaultValues = Maps.newHashMap();
                }
                product.set("non_attribute_values", JSON.toJSONString(updateDaultValues));
                updateFieldList.add("non_attribute_values");
            }

            if (!dataList.isEmpty()) {
                serviceFacade.batchUpdateByFields(actionContext.getUser(), dataList, updateFieldList);
            }
        }
        return ProductAssociateNonstandardAttributeAction.Result.builder().errorCode("0").value(true).build();
    }
    @Override
    protected ProductAssociateNonstandardAttributeAction.Result after(ProductAssociateNonstandardAttributeAction.Arg arg, ProductAssociateNonstandardAttributeAction.Result result){
        /*String logContent = String.format("状态，原状态: 禁用 ,被更改为: 启用 状态");
        serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.MODIFY, objectDescribe, dataList, logContent);
        */return super.after(arg, result);
    }

    @Data
    public static class Arg {
        @JsonProperty("product_ids")
        private List<String> productIds;

        @JsonProperty("nonstandard_attribute_ids")
        private List<String> nonstandardAttributeIds;

        @JsonProperty("default_values")
        private Map<String, Map<String, Object>> defaultValues;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String errorDetail;
        private String errorCode;
        private String message;
        private Boolean value;

        public boolean isSuccess() {
            return "0".equals(errorCode) && value;
        }
    }
}
