package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.AdvancedFormulaService;
import com.facishare.crm.sfa.predefine.service.NonstandardAttributeService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

public class AdvancedFormulaLineWebDetailController extends SFAWebDetailController {
    private final AdvancedFormulaService advancedFormulaService = SpringUtil.getContext().getBean(AdvancedFormulaService.class);
    private final AttributeCoreService attributeCoreService = SpringUtil.getContext().getBean(AttributeCoreService.class);
    private final NonstandardAttributeService nonstandardAttributeService = SpringUtil.getContext().getBean(NonstandardAttributeService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null) {
            return newResult;
        }
        removeDetailButtons(newResult);
        return newResult;
    }

    private void removeDetailButtons(Result result) {
        List<String> removeActionList = Lists.newArrayList(
                ObjectAction.INVALID.getActionCode(),
                ObjectAction.LOCK.getActionCode(),
                ObjectAction.UNLOCK.getActionCode(),
                ObjectAction.UPDATE.getActionCode(),
                ObjectAction.CLONE.getActionCode(),
                ObjectAction.DELETE.getActionCode(),
                ObjectAction.RECOVER.getActionCode());
        WebDetailLayout.of(result.getLayout().toLayout()).removeButtonsByActionCode(removeActionList);

    }

}
