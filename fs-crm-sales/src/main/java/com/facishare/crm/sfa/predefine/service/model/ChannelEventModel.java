package com.facishare.crm.sfa.predefine.service.model;

import com.facishare.crm.sfa.prm.api.enums.SignStatus;
import com.facishare.paas.appframework.core.model.User;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-21
 * ============================================================
 */
public interface ChannelEventModel {
    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    class BaseMessage {
        protected String tenantId;
        protected String currentStatus;
        protected String language;
        protected String operator;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @SuperBuilder
    @AllArgsConstructor
    @NoArgsConstructor
    class RegisterMessage extends BaseMessage {
        private String objectApiName;
        private String dataId;
        private String approvalStatus;
        private String app;
        private String enterpriseActivationSettingId;

        public boolean isPass() {
            return "pass".equals(this.approvalStatus);
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @SuperBuilder
    @AllArgsConstructor
    @NoArgsConstructor
    class SignMessage extends BaseMessage {
        private String objectApiName;
        private String dataId;
        private String approvalStatus;
        private String app;
        private String signSchemeId;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @SuperBuilder
    @AllArgsConstructor
    @NoArgsConstructor
    class RenewMessage extends BaseMessage {
        private Long outTenantId;
        private String objectDataId;
        private String objectApiName;
        private String partnerAgreementDetailId;
        private String signSchemeId;
        private Boolean pass;
        private String app;
    }
}
