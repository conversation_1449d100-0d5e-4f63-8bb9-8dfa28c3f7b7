package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService.MODULE_MULTIPLE_UNIT;
import static com.facishare.crm.sfa.utilities.constant.SpuSkuConstants.MULTIUNITRELEATED_IS_MULTIPLE_UNIT;
import static com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils.findUnitReferenceBySkuId;

@ServiceModule("pricebook_standard")
@Service
@Slf4j
public class PriceBookStandardService {
    @Autowired
    PriceBookService priceBookService;
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Autowired
    private ObjectDataServiceImpl iObjectDataService;
    @Autowired
    private BizCommonConfigService bizCommonConfigService;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    @ServiceMethod("update_price_product")
    public boolean updateStandardPriceBookProduct(JSONObject object, ServiceContext context) {
        return doUpdateStandardPriceBookProduct(context.getTenantId(), object.getString("productId"), object.getString("actionCode"));
    }

    public boolean doUpdateStandardPriceBookProduct(String tenantId, String productId, String actionCode) {
        IObjectData standardPriceBook = priceBookService.getStandardPriceBook(new User(tenantId, ProductConstants.SUPER_ADMIN_USER_ID));

        if (standardPriceBook == null) {
            log.warn("standard priceBook is not exists,tenantI1d {},productId {},actionCode {}", tenantId, productId, actionCode);
            return false;
        }

        User user = new User(tenantId, getOwnerId(standardPriceBook.get(UdobjConstants.OWNER_API_NAME)));

        String priceBookId = standardPriceBook.getId();
        IObjectData priceBookProduct = priceBookService.getPriceBookProduct(user, priceBookId, productId);
        if (ProductConstants.ADD_ACTION.equals(actionCode)) {
            if (priceBookProduct != null) {
                log.info("standard priceBook product has exist，skip ，tenantId {},productId {},actionCode {}", user.getTenantId(), productId, actionCode);
                return true;
            }

            IObjectData productObj = null;
            try {
                productObj = serviceFacade.findObjectData(ActionContextExt.of(user).pgDbType().getContext(), productId, Utils.PRODUCT_API_NAME);
                //验证产品是否存在，不存在则返回false
            } catch (AppBusinessException appEx) {
                log.warn("MetaDataBusinessException occurs, product might be not exists，tenantId {},productId {},actionCode {}, exception info {}",
                        user.getTenantId(), productId, actionCode, appEx.toString());
                return false;
            } catch (Exception ex) {
                log.error("Unknown exception occurs", ex);
                return false;
            }
            if (productObj == null) {
                log.warn("product has not exist ，tenantId {},productId {},actionCode {}", user.getTenantId(), productId, actionCode);
                return false;
            }
            IObjectDescribe priceProductDescribe = serviceFacade.findObject(user.getTenantId(), PriceBookConstants.API_NAME_PRODUCT);
            //处理对象
            IObjectData objectData = new ObjectData();
            //标准价目表的价目表产品id=产品id+tenantId
            objectData.setId(productId + tenantId);
            objectData.set(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), priceBookId);
            objectData.set(PriceBookConstants.ProductField.PRODUCTID.getApiName(), productId);
            objectData.set(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(), productObj.get("price"));
            IObjectDescribe priceBookProductDescribe = serviceFacade.findObject(user.getTenantId(), PriceBookConstants.API_NAME_PRODUCT);
            fillDefaultObject(user, priceBookProductDescribe, objectData);
            fillDefaultIsExpression(priceProductDescribe, Lists.newArrayList(objectData));
            if (bizCommonConfigService.isOpen(MODULE_MULTIPLE_UNIT, user) && Optional.ofNullable(productObj.get(MULTIUNITRELEATED_IS_MULTIPLE_UNIT, Boolean.class)).orElse(Boolean.FALSE)) {
                fillPricing(tenantId, objectData, Double.parseDouble(productObj.get("price").toString()), productId);
            }
            try {
                if (serviceFacade.saveObjectData(user, objectData) == null) {
                    return false;
                }
            } catch (MetaDataBusinessException e) {
                log.warn("synchronized failed.", e);
                return Boolean.FALSE;
            }
            logAsync(new User(tenantId, ProductConstants.SUPER_ADMIN_USER_ID), priceProductDescribe, Lists.newArrayList(productId + tenantId));
        } else if (ProductConstants.DELETE_ACTION.equals(actionCode)) {
            if (priceBookProduct == null) {
                log.info("standard priceBook product is not exist，skip ，tenantId {},productId {},actionCode {}", user.getTenantId(), productId, actionCode);
                return true;
            }
            IObjectData productObj = null;
            try {
                productObj = serviceFacade.findObjectData(ActionContextExt.of(user).pgDbType().getContext(), productId, Utils.PRODUCT_API_NAME);
            } catch (ObjectDataNotFoundException e) {
                log.warn(e.getMessage(), e);
            }
            //如果产品还没有被删除，则返回失败
            if (productObj != null) {
                log.error("'',delete failed ，tenantId {},productId {},actionCode {}", user.getTenantId(), productId, actionCode);
                return false;
            }
            String result = serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(Arrays.asList(priceBookProduct), user);
            if (StringUtils.isNotBlank(result)) {
                log.error("delete priceBook product failed,tenantId {},productId {},failReason {}", user.getTenantId(), productId, result);
                return false;
            }
        }
        return true;
    }


    private String getOwnerId(Object ownerIdTmp) {
        String ownerId;
        if (ownerIdTmp == null) {
            ownerId = ProductConstants.SUPER_ADMIN_USER_ID;
        } else {
            List<String> ownerIds = ((List<String>) ownerIdTmp);
            if (CollectionUtils.isNotEmpty(ownerIds)) {
                ownerId = ownerIds.get(0);
            } else {
                ownerId = ProductConstants.SUPER_ADMIN_USER_ID;
            }
        }
        return ownerId;
    }

    @ServiceMethod("check_or_init_standard_pricebook")
    public Map<String, Object> checkOrInitStandardPricebook(ServiceContext context) {
        Map<String, Object> map = Maps.newHashMap();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            boolean result;
            String tenantId = context.getTenantId();
            try {
                log.info("fs-crm:updatePricebook:start,tenantId {}", tenantId);
                result = initStandardPriceProduct(tenantId, true);
                if (result) {
                    log.info("fs-crm:updatePricebook:success,tenantId {}", tenantId);
                } else {
                    log.error("fs-crm:updatePricebook:result  error,tenantId {}", tenantId);
                }
            } catch (Exception e) {
                log.error("fs-crm:updatePricebook:exception error,tenantId {}", tenantId, e);
            }
        });
        parallelTask.run();
        map.put("result", true);
        return map;
    }

    @ServiceMethod("init_pricebook")
    public Map<String, Object> initStandardPrice(JSONObject obj, ServiceContext context) {
        Map<String, Object> map = Maps.newHashMap();
        boolean initProduct = obj.getBoolean("initProduct") != null ? obj.getBoolean("initProduct") : true;
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            for (String tenantId : obj.getString("tenantIds").split(",")) {
                boolean result;
                try {
                    log.info("fs-crm:initStandardPrice:start,tenantId {}, initProduct {}", tenantId, initProduct);
                    result = initStandardPriceProduct(tenantId, initProduct);
                    if (result) {
                        log.info("fs-crm:initStandardPrice:success,tenantId {}", tenantId);
                    } else {
                        log.error("fs-crm:initStandardPrice:result  error,tenantId {}", tenantId);
                    }
                } catch (Exception e) {
                    log.error("fs-crm:initStandardPrice:exception error,tenantId {}", tenantId, e);
                }
            }
        });
        parallelTask.run();
        map.put("result", true);
        return map;
    }


    public boolean initStandardPriceProduct(String tenantId, boolean initProduct) {
        User user = new User(tenantId, ProductConstants.SUPER_ADMIN_USER_ID);
        IObjectData standardPriceBook = priceBookService.getStandardPriceBook(user);
        if (standardPriceBook == null) {
            standardPriceBook = initStandardPriceBook(tenantId);
        }
        if (!initProduct) {
            return true;
        }
        //如果发现标准价目表同步失败
        if (standardPriceBook == null) {
            log.error("standard priceBook init error,tenantId {}", tenantId);
            return false;
        }
        //获取当前标准价目表下已有的产品列表
        String priceBookId = standardPriceBook.getId();
        String salesOrderDiscount = null;
        try {
            salesOrderDiscount = getSalesOrderDisount(tenantId);
        } catch (Exception e) {
            log.warn("getSalesOrderDisount error,tenantId {}", tenantId, e);
        }
        //分页处理企业所有的产品
        int offset = 0, limit = 500, count = 0;
        IObjectDescribe priceProductDescribe = serviceFacade.findObject(user.getTenantId(), PriceBookConstants.API_NAME_PRODUCT);
        boolean flag = true;
        while (true) {
            QueryResult<IObjectData> productResult = priceBookService.findProductNotInPriceBook(user, priceBookId, offset, limit);
            if (productResult.getData().size() <= 0) {
                break;
            }
            count += productResult.getData().size();
            List<IObjectData> addPriceProductList = Lists.newLinkedList();
            for (IObjectData productObj : productResult.getData()) {
                IObjectData objectData = new ObjectData();
                //标准价目表的价目表产品id=产品id+tenantId
                objectData.setId(productObj.getId() + tenantId);
                objectData.set(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), priceBookId);
                objectData.set(PriceBookConstants.ProductField.PRODUCTID.getApiName(), productObj.getId());
                objectData.set(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName(), productObj.get("price"));
                fillDefaultObject(user, priceProductDescribe, objectData);
                if (StringUtils.isNotBlank(salesOrderDiscount)) {
                    objectData.set("discount", salesOrderDiscount);
                }
                addPriceProductList.add(objectData);
            }
            fillDefaultIsExpression(priceProductDescribe, addPriceProductList);
            List<IObjectData> dataList = serviceFacade.bulkSaveObjectData(addPriceProductList, user);
            logAsync(user, priceProductDescribe, addPriceProductList.stream().map(DBRecord::getId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(dataList) || addPriceProductList.size() != dataList.size()) {
                flag = false;
            }
            //底层的查询是有缓存的，同一线程同一查询参数返回的结果数据是一样的,所以此处在每次查询时，改变查询数据的页数据条数
            limit++;
        }
        log.info("fs-crm:initStandardPrice:count ,tenantId {},count {}", tenantId, count);
        return flag;
    }

    public IObjectData initStandardPriceBook(String tenantId) {
        User user = new User(tenantId, ProductConstants.SUPER_ADMIN_USER_ID);
        //处理对象
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), PriceBookConstants.API_NAME);
        IObjectData objectData = new ObjectData();
        objectData.set("name", "标准价目表");// ignoreI18n
        objectData.set(PriceBookConstants.Field.ACTIVESTATUS.getApiName(), PriceBookConstants.ActiveStatus.ON.getStatus());
        objectData.set("dept_range", Arrays.asList("999999"));
        objectData.set(PriceBookConstants.Field.ACCOUNTRANGE.getApiName(), "{\"type\":\"noCondition\",\"value\":\"ALL\"}");
        objectData.set("is_standard", true);
        if (GrayUtil.isPriceBookReform(tenantId)) {
            objectData.set(PriceBookConstants.Field.APPLY_ACCOUNT_RANGE.getApiName(), "{\"type\":\"ALL\"}");
            objectData.set(PriceBookConstants.Field.APPLY_ORG_RANGE.getApiName(), "{\"type\":\"ALL\"}");
            if (bizConfigThreadLocalCacheService.isPartnerEnabled(tenantId)) {
                objectData.set(PriceBookConstants.Field.APPLY_PARTNER_RANGE.getApiName(), "{\"type\":\"ALL\"}");
            }
            objectData.set(PriceBookConstants.Field.CALCULATE_STATUS.getApiName(),
                    PriceBookConstants.CalculateStatus.CALCULATING.getStatus());
        }
        fillDefaultObject(user, objectDescribe, objectData);
        return serviceFacade.saveObjectData(user, objectData);
    }

    public String getSalesOrderDisount(String tenantId) {
        //获取订单产品的描述
        IObjectDescribe salesOrderProductDescribe = serviceFacade.findObject(tenantId, Utils.SALES_ORDER_PRODUCT_API_NAME);
        IFieldDescribe discountDescribe = salesOrderProductDescribe.getFieldDescribe("discount");
        Object discountDefault = discountDescribe.getDefaultValue();
        if (discountDefault != null && StringUtils.isNumeric(discountDefault.toString())) {
            return discountDefault.toString();
        }
        return null;
    }

    private void fillDefaultObject(User user, IObjectDescribe objectDescribe, IObjectData objectData) {
        //默认信息
        objectData.setTenantId(user.getTenantId());
        objectData.setCreatedBy(user.getUpstreamOwnerIdOrUserId());
        objectData.setLastModifiedBy(user.getUpstreamOwnerIdOrUserId());
        objectData.set(UdobjConstants.LIFE_STATUS_API_NAME, UdobjConstants.LIFE_STATUS_VALUE_NORMAL);
        objectData.set(UdobjConstants.OWNER_API_NAME, Arrays.asList(user.getUpstreamOwnerIdOrUserId()));
        objectData.setDataOwnOrganization(Lists.newArrayList("999999"));
        //锁定状态设置成,为锁定
        objectData.set("lock_status", "0");
        //业务类型
        objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        objectData.set(IObjectData.DESCRIBE_ID, objectDescribe.getId());
        objectData.set(IObjectData.DESCRIBE_API_NAME, objectDescribe.getApiName());
        objectData.set(IObjectData.PACKAGE, "CRM");
        objectData.set(IObjectData.VERSION, objectDescribe.getVersion());
        //相关团队
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        TeamMember teamMember = new TeamMember(user.getUpstreamOwnerIdOrUserId(), TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE);
        objectDataExt.addTeamMembers(Lists.newArrayList(teamMember));
        //其他相关字段信息
        if (!objectDescribe.getApiName().equals(PriceBookConstants.API_NAME_PRODUCT)) {
            return;
        }
        //其他相关字段信息的默认信息
        List<String> list = Collections.singletonList("discount");
        list.forEach(k -> {
            Object kVal = objectData.get(k);
            if (kVal == null || StringUtils.isBlank(kVal.toString())) {
                IFieldDescribe field = objectDescribe.getFieldDescribe(k);
                Object defaultVal = field.getDefaultValue();
                if (defaultVal != null && StringUtils.isNotBlank(defaultVal.toString())) {
                    objectData.set(k, defaultVal);
                }
            }
        });
    }

    private void fillDefaultIsExpression(IObjectDescribe priceProductDescribe, List<IObjectData> addPriceProductList) {
        List<IFieldDescribe> formulaDefaultValueFields = priceProductDescribe.getFieldDescribes().stream()
                .filter(o -> o.getDefaultIsExpression() || IFieldType.FORMULA.equals(o.getType()))
                .collect(Collectors.toList());
        expressionCalculateLogicService.bulkCalculate(priceProductDescribe, addPriceProductList, formulaDefaultValueFields);
    }

    //开启多单位的企业特殊处理价目表售价，价目表价格
    private void fillPricing(String tenantId, IObjectData addPriceProduct, double skuPrice, String skuId) {
        String unitReferenceBySkuIdSql = findUnitReferenceBySkuId(tenantId, skuId);
        try {
            List<Map> bySql = iObjectDataService.findBySql(tenantId, unitReferenceBySkuIdSql);
            double conversionRatio = Double.parseDouble(bySql.get(0).get("conversion_ratio").toString());
            addPriceProduct.set("pricebook_sellingprice", conversionRatio * skuPrice);
            log.warn("PriceBookStandardService:tenantId:{},skuId:{},conversionRatio:{},sql:{}", tenantId, skuId, conversionRatio, unitReferenceBySkuIdSql);
        } catch (Exception e) {
            log.error("doUpdateStandardPriceBookProduct:tenantId:{},skuId:{},sql:{}", tenantId, skuId, unitReferenceBySkuIdSql, e);
        }

    }

    private void logAsync(User user, IObjectDescribe objectDescribes, List<String> dataIdList) {
        if (CollectionUtils.isEmpty(dataIdList)) return;
        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(
                user.getTenantId(), dataIdList, Utils.PRICE_BOOK_PRODUCT_API_NAME);
        if (CollectionUtils.isEmpty(dataList)) return;
        serviceFacade.log(user, EventType.ADD, ActionType.Add, objectDescribes, dataList);
    }
}
