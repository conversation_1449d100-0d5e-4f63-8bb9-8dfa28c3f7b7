{"validate_rules": {}, "triggers": {}, "actions": {}, "version": 1, "api_name": "AdvancedFormulaObj", "display_name": "高级公式", "package": "CRM", "define_type": "package", "is_active": true, "store_table_name": "biz_advanced_formula", "is_deleted": false, "fields": {"tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "api_name": "tenant_id", "description": "tenant_id", "status": "released"}, "formula": {"expression_type": "long_text", "default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "公式", "type": "long_text", "default_to_zero": false, "is_required": true, "api_name": "formula", "define_type": "package", "help_text": "", "max_length": 10000, "status": "released"}, "ref_object_api_name": {"api_name": "ref_object_api_name", "is_index": true, "define_type": "package", "index": false, "is_need_convert": false, "label": "适用对象", "is_required": true, "default_value": "", "status": "released", "type": "text", "max_length": 256, "is_unique": false}, "ref_field_name": {"api_name": "ref_field_name", "is_index": true, "define_type": "package", "index": false, "is_need_convert": false, "label": "适用字段", "is_required": true, "default_value": "", "status": "released", "type": "text", "max_length": 256, "is_unique": false}, "enabled_trial": {"is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": false, "label": "支持试算", "type": "true_or_false", "is_required": false, "api_name": "enabled_trial", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_single": false, "is_index_field": false, "help_text": "", "status": "released"}, "lock_rule": {"is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "field_num": 1, "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "is_field": false, "status": "new"}, "origin_source": {"is_index": false, "is_active": true, "is_unique": false, "label": "数据来源", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "origin_source", "options": [{"label": "数据同步", "value": "0"}], "define_type": "system", "is_extend": false, "config": {"display": 0}, "status": "released"}, "lock_user": {"is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "field_num": 4, "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "is_field": false, "status": "new"}, "is_deleted": {"is_index": false, "is_unique": false, "description": "is_deleted", "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "status": "released"}, "life_status_before_invalid": {"is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "field_num": 7, "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "is_field": false, "max_length": 256, "status": "new"}, "object_describe_api_name": {"is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "owner_department": {"default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_field": false, "is_show_mask": false, "help_text": "", "status": "new"}, "out_owner": {"is_index": true, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "config": {"display": 1}, "status": "released"}, "owner": {"default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "default_value": "", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "is_field": false, "help_text": "", "status": "new"}, "lock_status": {"is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "field_num": 2, "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "is_field": false, "config": {}, "status": "new"}, "package": {"is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "field_num": 3, "is_need_convert": false, "api_name": "life_status", "is_field": false, "config": {}, "help_text": "", "status": "new"}, "last_modified_by": {"is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "config": {"display": 0}, "max_length": 200, "status": "released"}, "version": {"is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "is_field": false, "help_text": "相关团队", "status": "new"}, "record_type": {"is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "is_field": false, "config": {}, "help_text": "", "status": "released"}, "data_own_department": {"default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "default_value": "", "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "wheres": [], "optional_type": "department", "api_name": "data_own_department", "define_type": "package", "is_single": true, "is_field": false, "help_text": "", "status": "new"}, "name": {"prefix": "{yyyy}{mm}{dd}-", "auto_adapt_places": false, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "is_required": true, "define_type": "system", "postfix": "", "is_single": false, "is_index": true, "is_active": true, "auto_number_type": "normal", "is_encrypted": false, "serial_number": 6, "default_value": "01", "label": "编号", "condition": "NONE", "is_need_convert": false, "api_name": "name", "is_index_field": false, "help_text": "", "status": "released"}, "order_by": {"is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "_id": {"is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "resource_bundle_key": "AdvancedFormulaObj._id", "max_length": 200, "status": "released"}, "extend_obj_data_id": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "default_value": "", "type": "text", "label": "extend_obj_data_id", "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}}}