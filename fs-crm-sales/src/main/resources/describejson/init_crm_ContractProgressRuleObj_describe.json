{"fields": {"created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "label": "最后修改人"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released"}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released"}, "name": {"default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "description": "", "is_unique": true, "default_value": "", "label": "规则名称", "type": "text", "default_to_zero": false, "is_required": true, "api_name": "name", "define_type": "system", "help_text": "", "max_length": 120, "status": "released", "is_extend": false}, "description": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "描述", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "description", "define_type": "system", "help_text": "", "max_length": 2000, "status": "released", "is_extend": false}, "enabled_status": {"is_index": false, "is_active": true, "description": "", "is_unique": false, "default_value": true, "label": "启用状态", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "enabled_status", "options": [{"label": "禁用", "value": false}, {"label": "启用", "value": true}], "define_type": "package", "is_index_field": false, "status": "released"}, "contract_record_type": {"default_is_expression": false, "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "合同业务类型", "type": "array", "default_to_zero": false, "is_need_convert": false, "is_required": false, "api_name": "contract_record_type", "define_type": "package", "is_single": false, "is_extend": false, "is_index_field": false, "help_text": "", "status": "released"}, "index_type": {"default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "合同产品", "value": "1"}, {"label": "产品分类", "value": "2"}, {"label": "合同数值", "value": "3"}], "define_type": "package", "is_single": false, "is_extend": false, "is_index": false, "is_active": true, "default_value": "", "label": "指标项", "api_name": "index_type", "help_text": "", "status": "released"}, "index_type_object": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "考核参照对象", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "index_type_object", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "index_type_object_field": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "考核参照字段", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "index_type_object_field", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "index_type_object_field_value": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "考核参照字段选项值", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "index_type_object_field_value", "define_type": "system", "help_text": "", "max_length": 200, "status": "released", "is_extend": false}, "index_type_category": {"default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "数值型", "value": "numberType"}], "define_type": "package", "is_single": false, "is_extend": false, "is_index": false, "is_active": true, "default_value": "numberType", "label": "指标分类", "api_name": "index_type_category", "help_text": "", "status": "released"}, "index_type_calc_type": {"default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "求和", "value": "sum"}], "define_type": "package", "is_single": false, "is_extend": false, "is_index": false, "is_active": true, "default_value": "numberType", "label": "指标计算类型", "api_name": "index_type_calc_type", "help_text": "", "status": "released"}, "index_goal_object": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "目标对象", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "index_goal_object", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "index_goal_object_field": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "目标字段", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "index_goal_object_field", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "index_goal_data_object": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "数据采集来源", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "index_goal_data_object", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "index_goal_data_direct_object": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "直接数据采集来源", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "index_goal_data_direct_object", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "index_goal_data_object_field": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "数据采集字段", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "index_goal_data_object_field", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "index_goal_data_ref_contract_field": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "绑定合同", "type": "text", "default_to_zero": false, "is_required": true, "api_name": "index_goal_data_ref_contract_field", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "index_goal_data_ref_product_field": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "产品", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "index_goal_data_ref_product_field", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "index_goal_data_ref_time_field": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "统计基准时间", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "index_goal_data_ref_time_field", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "index_goal_data_condition": {"expression_type": "json", "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "条件筛选", "type": "use_range", "default_to_zero": false, "is_required": false, "api_name": "index_goal_data_condition", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}}, "actions": {}, "validate_rules": {}, "triggers": {}, "index_version": 1, "api_name": "ContractProgressRuleObj", "display_name": "履约进度规则", "package": "CRM", "is_active": true, "define_type": "internal", "is_deleted": false, "store_table_name": "biz_contract_progress_rule"}