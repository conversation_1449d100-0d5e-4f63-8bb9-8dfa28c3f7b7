{"fields": {"name": {"is_index": true, "is_active": true, "description": "非标属性名称", "is_unique": true, "label": "非标属性名称", "type": "text", "is_abstract": null, "field_num": null, "is_required": true, "api_name": "name", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 100, "status": "released"}, "code": {"is_index": true, "is_active": true, "description": "非标属性编号", "is_unique": true, "label": "非标属性编号", "type": "text", "is_abstract": null, "field_num": null, "is_required": true, "api_name": "code", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 200, "status": "released"}, "type": {"is_index": true, "is_active": true, "description": "类型", "is_unique": false, "label": "类型", "type": "select_one", "is_abstract": null, "field_num": null, "is_need_convert": false, "is_required": true, "api_name": "type", "options": [{"label": "数值", "value": "1"}, {"label": "文本", "value": "2"}], "define_type": "package", "is_index_field": false, "is_single": false, "status": "released"}, "default_value": {"is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "max_length": 500, "is_index": true, "is_active": true, "label": "默认值", "is_abstract": null, "api_name": "default_value", "is_index_field": false, "help_text": "", "status": "released"}, "description": {"is_index": true, "is_active": true, "description": "描述", "is_unique": false, "label": "描述", "type": "long_text", "is_abstract": null, "field_num": null, "is_required": false, "api_name": "description", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 3000, "status": "released"}, "owner": {"is_index": true, "is_active": true, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_index_field": false, "is_single": true, "help_text": "", "status": "new"}, "lock_rule": {"is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_index_field": false, "is_single": false, "status": "released"}, "lock_status": {"is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_index_field": false, "is_single": false, "status": "released"}, "life_status": {"is_index": true, "is_active": true, "description": "生命状态", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "status": "released"}, "life_status_before_invalid": {"is_index": false, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 256, "status": "released"}, "owner_department": {"default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "负责人主属部门", "type": "text", "default_to_zero": false, "is_need_convert": false, "is_required": false, "api_name": "owner_department", "define_type": "package", "is_single": true, "help_text": "", "max_length": 100, "status": "released"}, "lock_user": {"is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "released"}, "record_type": {"is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": true, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "status": "released"}, "relevant_team": {"embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "相关团队", "status": "released"}, "created_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": true, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "创建人", "api_name": "created_by", "description": "created_by", "status": "released"}, "last_modified_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": true, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "最后修改人", "api_name": "last_modified_by", "description": "last_modified_by", "status": "released"}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "api_name": "package", "description": "package", "status": "released"}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "api_name": "tenant_id", "description": "tenant_id", "status": "released"}, "object_describe_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_id", "api_name": "object_describe_id", "description": "object_describe_id", "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released"}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released"}, "extend_obj_data_id": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "default_value": "", "type": "text", "label": "extend_obj_data_id", "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "help_text": "", "is_unique": false, "max_length": 100, "status": "released", "is_extend": false}}, "index_version": 1, "api_name": "NonstandardAttributeObj", "display_name": "非标属性", "package": "CRM", "define_type": "package", "is_active": true, "version": 1, "is_deleted": false, "revision": 1, "icon_index": 12, "store_table_name": "biz_nonstandard_attribute", "short_name": "nao"}