{"fields": {"created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "label": "最后修改人"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released"}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released"}, "rule_goal_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "履约进度目标", "target_api_name": "ContractProgressRuleGoalObj", "type": "object_reference", "target_related_list_name": "target_related_list_rule_goal_id", "is_abstract": null, "target_related_list_label": "履约进度目标快照", "action_on_target_delete": "cascade_delete", "is_required": true, "wheres": [], "api_name": "rule_goal_id", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "status": "released"}, "goal_value": {"default_is_expression": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "max_length": 16, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "目标值", "is_need_convert": false, "api_name": "goal_value", "round_mode": 4, "help_text": "", "status": "released"}, "current_value": {"default_is_expression": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "max_length": 16, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "当前完成值", "is_need_convert": false, "api_name": "current_value", "round_mode": 4, "help_text": "", "status": "released"}, "check_type": {"default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "过程检查", "value": "1"}, {"label": "完结检查", "value": "2"}], "define_type": "package", "is_single": false, "is_extend": false, "is_index": false, "is_active": true, "default_value": "1", "label": "检查类型", "api_name": "check_type", "help_text": "", "status": "released"}}, "actions": {}, "validate_rules": {}, "triggers": {}, "index_version": 1, "api_name": "ContractProgressRuleGoalSnapshotObj", "display_name": "履约进度目标快照", "package": "CRM", "is_active": true, "define_type": "internal", "is_deleted": false, "store_table_name": "biz_contract_progress_rule_goal_snapshot"}