{"fields": {"created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "label": "最后修改人"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released"}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released"}, "name": {"prefix": "{yyyy}{mm}{dd}-", "auto_adapt_places": false, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "is_required": true, "define_type": "system", "postfix": "", "is_single": false, "is_index": true, "is_active": true, "auto_number_type": "normal", "is_encrypted": false, "serial_number": 6, "default_value": "01", "label": "编号", "condition": "NONE", "is_need_convert": false, "api_name": "name", "is_index_field": false, "help_text": "", "status": "released"}, "rule_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "履约规则", "target_api_name": "ContractProgressRuleObj", "type": "object_reference", "target_related_list_name": "target_related_list_rule_id", "is_abstract": null, "target_related_list_label": "履约目标", "action_on_target_delete": "cascade_delete", "is_required": true, "wheres": [], "api_name": "rule_id", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "status": "released"}, "contract_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "销售合同", "target_api_name": "SaleContractObj", "type": "object_reference", "target_related_list_name": "target_related_list_contract_id", "is_abstract": null, "target_related_list_label": "履约进度", "action_on_target_delete": "cascade_delete", "is_required": true, "wheres": [], "api_name": "contract_id", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "status": "released"}, "product_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "产品", "target_api_name": "ProductObj", "type": "object_reference", "target_related_list_name": "target_related_list_product_id", "is_abstract": null, "target_related_list_label": "履约目标", "action_on_target_delete": "cascade_delete", "is_required": false, "wheres": [], "api_name": "product_id", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "status": "released"}, "index_type_name": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "指标项", "type": "text", "is_abstract": true, "default_to_zero": false, "is_required": false, "api_name": "index_type_name", "define_type": "system", "help_text": "", "max_length": 120, "status": "released", "is_extend": false}, "index_type_object_field_name": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "基准参照字段", "type": "text", "is_abstract": true, "default_to_zero": false, "is_required": false, "api_name": "index_type_object_field_name", "define_type": "system", "help_text": "", "max_length": 120, "status": "released", "is_extend": false}, "goal_value": {"default_is_expression": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "max_length": 16, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "目标值", "is_need_convert": false, "api_name": "goal_value", "round_mode": 4, "help_text": "", "status": "released"}, "current_value": {"default_is_expression": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 16, "is_index": false, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "当前完成值", "is_need_convert": false, "api_name": "current_value", "round_mode": 4, "help_text": "", "status": "released"}, "to_complete_time": {"default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "date", "default_to_zero": false, "is_required": true, "define_type": "package", "is_single": false, "is_extend": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "达成日期", "time_zone": "GMT+8", "is_need_convert": false, "api_name": "to_complete_time", "date_format": "yyyy-MM-dd", "is_index_field": false, "help_text": "", "status": "released"}}, "actions": {}, "validate_rules": {}, "triggers": {}, "index_version": 1, "api_name": "ContractProgressRuleGoalObj", "display_name": "履约进度目标", "package": "CRM", "is_active": true, "define_type": "internal", "is_deleted": false, "store_table_name": "biz_contract_progress_rule_goal"}